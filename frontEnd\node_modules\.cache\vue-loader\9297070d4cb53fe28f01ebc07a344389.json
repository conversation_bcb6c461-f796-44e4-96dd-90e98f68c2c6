{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\equipmentAlarm\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\equipmentAlarm\\index.vue", "mtime": 1755674552432}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/safetyManagement/equipmentAlarm", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 equipmentAlarm\">\r\n    <custom-layout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"submitForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        >\r\n          <template #customBtn=\"{ slotScope }\">\r\n            <el-button\r\n              v-if=\"slotScope.Handle_Status != 2\"\r\n              type=\"text\"\r\n              @click=\"handleChange(slotScope)\"\r\n            >关闭</el-button>\r\n          </template>\r\n        </CustomTable>\r\n      </template>\r\n    </custom-layout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport getGridByCode from '../mixins/index'\r\nimport {\r\n  GetWarningList,\r\n  ExportWaringManage,\r\n  WaringManageInfo,\r\n  UpdateWarningStatus,\r\n  ExportWaringList\r\n} from '@/api/business/safetyManagement'\r\nimport DialogForm from './components/dialogForm.vue'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n\r\nexport default {\r\n  components: {\r\n    CustomLayout,\r\n    CustomTable,\r\n    CustomForm\r\n  },\r\n  mixins: [getGridByCode],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        EquipmentName: '',\r\n        EquipmentDate: ['', ''],\r\n        BeginTime: '',\r\n        EndTime: '',\r\n        Status: '',\r\n        EquipmentType: '',\r\n        Handler: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'EquipmentName',\r\n            label: '设备名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentDate',\r\n            label: '触发告警时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              type: 'daterange',\r\n              rangeSeparator: '至',\r\n              startPlaceholder: '开始日期',\r\n              endPlaceholder: '结束日期',\r\n              clearable: true,\r\n              valueFormat: 'yyyy-MM-dd'\r\n            },\r\n            change: (e) => {\r\n              this.ruleForm.BeginTime = (e ?? [])[0]\r\n              this.ruleForm.EndTime = (e ?? [])[1]\r\n            }\r\n          },\r\n          // {\r\n          //   key: 'EquipmentType',\r\n          //   label: '设备类型',\r\n          //   type: 'select',\r\n          //   options: [],\r\n          //   otherOptions: {\r\n          //     clearable: true\r\n          //   },\r\n          //   change: (e) => {\r\n          //     console.log(e)\r\n          //   }\r\n          // },\r\n          {\r\n            key: 'Handle_Status',\r\n            label: '告警状态',\r\n            type: 'select',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择告警状态'\r\n            },\r\n            options: [\r\n              {\r\n                label: '告警中',\r\n                value: 1\r\n              },\r\n              {\r\n                label: '已关闭',\r\n                value: 2\r\n              }\r\n              // {\r\n              //   label: '已处理',\r\n              //   value: 3\r\n              // }\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Handler',\r\n            label: '处理人',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '批量导出',\r\n              onclick: () => {\r\n                this.handleExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [20, 40, 60, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 1000,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center'\r\n        },\r\n        tableActions: [\r\n          /* {\r\n            actionLabel: '关闭',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleChange(row)\r\n            }\r\n          }, */\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleWatch(row.Id)\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '查看详情',\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n        }\r\n      },\r\n      multipleSelection: []\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchData()\r\n    this.getGridByCode('equipmentAlarm')\r\n  },\r\n  async mounted() {\r\n    // this.customForm.formItems[2].options = await this.getDictionaryDetailListByCode()\r\n  },\r\n  methods: {\r\n    fetchData(data = { Page: 1, PageSize: 20 }) {\r\n      const Data = {\r\n        ...this.ruleForm,\r\n        ...{\r\n          Page: this.customTableConfig.currentPage,\r\n          PageSize: this.customTableConfig.pageSize\r\n        }\r\n      }\r\n      delete Data.EquipmentDate\r\n      GetWarningList(Data).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n          this.customTableConfig.tableData = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    resetForm() {\r\n      this.ruleForm = {\r\n        EquipmentName: '',\r\n        EquipmentDate: ['', ''],\r\n        BeginTime: '',\r\n        EndTime: '',\r\n        Status: '',\r\n        EquipmentType: '',\r\n        Handler: ''\r\n      }\r\n      this.fetchData()\r\n    },\r\n    submitForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData()\r\n    },\r\n    handleSizeChange(val) {\r\n      this.customTableConfig.pageSize = val\r\n      this.fetchData({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: val\r\n      })\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.customTableConfig.currentPage = val\r\n      this.fetchData({ Page: val, PageSize: this.customTableConfig.pageSize })\r\n    },\r\n    handleSelectionChange(data) {\r\n      console.log(data)\r\n      this.multipleSelection = data\r\n    },\r\n    // handleExport() {\r\n    //   let id = ''\r\n    //   if (this.multipleSelection.length == 0) {\r\n    //     this.$message.warning('请选择数据!')\r\n    //     return\r\n    //   } else {\r\n    //     id = this.multipleSelection.map(item => item.Id).join(',')\r\n    //   }\r\n    //   ExportWaringManage({\r\n    //     code: 'equipmentAlarm',\r\n    //     id\r\n    //   }).then(res => {\r\n    //     if (res.IsSucceed) {\r\n    //       this.$message.success('导出成功')\r\n    //       downloadFile(res.Data, '安防告警信息管理数据')\r\n    //     } else {\r\n    //       this.$message.error(res.Message)\r\n    //     }\r\n    //   })\r\n    // },\r\n    // v2 版本导出\r\n    handleExport() {\r\n      const Id = this.multipleSelection.map((item) => item.Id).join(',')\r\n      ExportWaringList({\r\n        ...this.ruleForm,\r\n        Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('导出成功')\r\n          downloadFile(res.Data, '安防告警信息管理数据')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleWatch(id) {\r\n      WaringManageInfo({ id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          // 数据\r\n          this.dialogVisible = true\r\n          this.componentsConfig.Data = res.Data\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleChange(row) {\r\n      console.log(row)\r\n      if (row.HandleStatusStr == '关闭') {\r\n        this.$message.warning('请勿重复操作')\r\n      } else {\r\n        this.$confirm('此操作将关闭该告警, 是否继续?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n          .then(() => {\r\n            UpdateWarningStatus({\r\n              id: row.Id,\r\n              wid: row.WId,\r\n              StatusEnum: 2\r\n            }).then((res) => {\r\n              if (res.IsSucceed) {\r\n                this.$message.success('操作成功')\r\n                this.fetchData()\r\n              } else {\r\n                this.$message.error(res.Message)\r\n              }\r\n            })\r\n          })\r\n          .catch(() => {\r\n            this.$message({\r\n              type: 'info',\r\n              message: '已取消'\r\n            })\r\n          })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang='scss'>\r\n.equipmentAlarm{\r\n  // height: calc(100vh - 90px);\r\n  // overflow: hidden;\r\n}\r\n</style>\r\n"]}]}