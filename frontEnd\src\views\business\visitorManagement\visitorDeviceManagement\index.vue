<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      destroy-on-close
    >
      <component
        :is="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import DialogForm from './dialogForm.vue'
import DeviceConnect from './deviceConnect.vue'
import { downloadFile } from '@/utils/downloadFile'
import getGridByCode from '../../safetyManagement/mixins/index'
import {
  GetVisitorEquipmentPageList,
  DeleteVisitorEquipment,
  ExportVisitorEquipment,
  GetVisitorEquipmentEntity
} from '@/api/business/visitorManagement'
import { GetParkArea } from '@/api/business/energyManagement.js'

export default {
  name: '',
  components: {
    CustomTable,
    CustomForm,
    CustomLayout
  },
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {
        Data: {},
        treeAddressoptions: [],
        dictionaryDetailoptions: []
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      tableSelection: [],
      ruleForm: {
        Name: '',
        EquipmentType: '',
        Position: '',
        Platform: '',
        Status: null
      },
      customForm: {
        formItems: [
          {
            key: 'Name',
            label: '设备名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'EquipmentType',
            label: '设备类型',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Position',
            label: '安装位置',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Platform',
            label: '平台名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Status',
            label: '状态',
            type: 'select',
            otherOptions: {
              clearable: true
            },
            options: [
              { label: '全部', value: null },
              { label: '在线', value: true },
              { label: '离线', value: false }
            ],
            change: (e) => {
              console.log(e)
            }
          }
        ],
        rules: {
        },
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: '新增',
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: false, // 是否禁用
              icon: '', //  图标
              autofocus: false, // 是否聚焦
              type: 'primary', // primary / success / warning / danger / info / text
              size: 'small', // medium / small / mini
              onclick: (item) => {
                console.log(item)
                this.handleCreate()
              }
            },
            {
              text: '批量导出',
              onclick: (item) => {
                console.log(item)
                this.handleExport()
              }
            }
          ]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [],
        tableData: [],
        operateOptions: {
          width: '240px',
          align: 'center',
        },
        tableActionsWidth: 140,
        tableActions: [
          {
            actionLabel: '查看',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'view')
            }
          },
          {
            actionLabel: '编辑',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'edit')
            }
          },
          {
            actionLabel: '删除',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleDelete(index, row)
            }
          },
          /* {
            actionLabel: '设备连接',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleConent(row)
            }
          } */
        ]
      },
      Park_Area: '',
    }
  },
  async created() {
    this.init()
    this.customForm.formItems[1].options = await this.getDictionaryDetailListByCode('VisitorEqtType')
  },
  mixins: [getGridByCode],
  methods: {
    searchForm(data) {
      console.log(data)
      this.customTableConfig.currentPage = 1
      this.onFresh()
    },
    resetForm() {
      this.onFresh()
    },
    onFresh() {
      this.getEquipmentList()
    },

    init() {
      this.getGridByCode('VisitorDeviceManagement')
      GetParkArea().then(res => {
        this.Park_Area = res.Data
      })
      this.getEquipmentList()
    },
    async getEquipmentList() {
      const res = await GetVisitorEquipmentPageList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data
        this.customTableConfig.total = res.Data.TotalCount
      } else {
        this.$message.error(res.Message)
      }
    },
    async handleCreate() {
      this.dialogTitle = '新增'
      this.dialogVisible = true
      this.currentComponent = DialogForm
      this.componentsConfig.Data = {
        EId: '',
        Name: '',
        EquipmentType: '',
        Park_area: [],
        Address: '',
        Position: '',
        PlatformName: '',
        PlatformLink: '',
        PlatformEngineer: '',
        PlatformEngineerLink: '',
        Park_Area: this.Park_Area,
      }
      this.componentsConfig.treeAddressoptions = await this.getTreeAddress()
      this.componentsConfig.dictionaryDetailoptions = await this.getDictionaryDetailListByCode('VisitorEqtType')
    },
    handleDelete(index, row) {
      this.$confirm('确认删除？', {
        type: 'warning'
      })
        .then(async (_) => {
          const res = await DeleteVisitorEquipment({
            IDs: [row.Id]
          })
          if (res.IsSucceed) {
            this.$message.success('操作成功')
            this.getEquipmentList()
          } else {
            this.$message.error(res.Message)
          }
        })
        .catch((_) => { })
    },
    async fetchData(Id) {
      if ((Id ?? '') != '') {
      return   await GetVisitorEquipmentEntity({ ID: Id }).then(res => {
          if (res.IsSucceed) {
            let Park_area = (res.Data.Scene ?? '') == '' ? [res.Data.PurposeCatetory] : (res.Data.Site ?? '') == '' ? [res.Data.PurposeCatetory, res.Data.Scene] : [res.Data.PurposeCatetory, res.Data.Scene, res.Data.Site]
            return { ...res.Data, Park_area }
          } else {
            this.$message.error(res.Message)
          }
        })
      }
    },
    async handleEdit(index, row, type) {
      console.log(index, row, type)
      this.currentComponent = DialogForm
      let isWatch = true
      this.componentsConfig.treeAddressoptions = await this.getTreeAddress()
      this.componentsConfig.dictionaryDetailoptions = await this.getDictionaryDetailListByCode('VisitorEqtType')
      if (type === 'view') {
        this.dialogTitle = '查看'
        isWatch = true
      } else if (type === 'edit') {
        this.dialogTitle = '编辑'
        isWatch = false
      }
      this.dialogVisible = true
      let data = await this.fetchData(row.Id)
      this.componentsConfig.Data = { ...data, Park_Area: this.Park_Area, isWatch }
      console.log(this.componentsConfig)
    },
    async handleExport() {
      const res = await ExportVisitorEquipment({
        IDs: this.tableSelection.map((item) => item.Id)
      })
      if (res.IsSucceed) {
        downloadFile(res.Data, '访客设备数据')
      } else {
        this.$message(res.Message)
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.getEquipmentList()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.getEquipmentList()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
    },
    handleConent(row) {
      this.dialogVisible = true
      this.dialogTitle = '确认设备连接'
      this.currentComponent = DeviceConnect
      this.componentsConfig.Data = { ...row }
    }
  }
}
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
</style>
