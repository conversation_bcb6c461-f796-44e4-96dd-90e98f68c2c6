{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJsafetyManagement\\equipmentManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJsafetyManagement\\equipmentManagement\\index.vue", "mtime": 1755674552406}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/SZCJsafetyManagement/equipmentManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 equipmentManagement\">\r\n    <custom-layout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"submitForm\"\r\n          @resetForm=\"fetchData\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </custom-layout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport getGridByCode from '../mixins/index'\r\nimport { GetEquipmentListSZCJ, MonitoreImportTemplate, ExportMonitoreEquipment, LookVideo, DelEquipment, MonitoreEquipmentInfo } from '@/api/business/safetyManagement'\r\nimport DialogForm from './components/dialogForm.vue'\r\nimport WatchVideoDialog from './components/watchVideoDialog.vue'\r\nimport DeviceInfoDialog from './components/deviceInfoDialog.vue'\r\nimport ImportFile from './components/importFile.vue'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport { GetDictionaryTreeDetailListByCode } from '@/api/sys'\r\nimport { GetParkArea, GetTreeAddress } from '@/api/business/energyManagement.js'\r\n\r\nexport default {\r\n  components: {\r\n    CustomLayout,\r\n    CustomTable,\r\n    CustomForm\r\n  },\r\n  mixins: [getGridByCode],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        EquipmentName: '',\r\n        EquipmentNumber: '',\r\n        InstallSite: '',\r\n        EquipmentType: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'EquipmentName',\r\n            label: '设备名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentNumber',\r\n            label: '设备编码',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'InstallSite',\r\n            label: '设备部署位置',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentType',\r\n            label: '设备类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '下载模板',\r\n              onclick: () => {\r\n                this.handleDownTemplate()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导入',\r\n              onclick: () => {\r\n                this.handleImport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导出',\r\n              onclick: () => {\r\n                this.handleExport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量删除',\r\n              onclick: () => {\r\n                this.handleDelete('batch')\r\n              }\r\n            },\r\n            {\r\n              text: '新增',\r\n              type: 'primary',\r\n              onclick: () => {\r\n                this.handleEdit()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [20, 40, 60, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 1000,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center'\r\n        },\r\n        tableActionsWidth: 260,\r\n        tableActions: [\r\n          {\r\n            actionLabel: '监控链接',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDeviceInfo(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete('single', row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '查看视频',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleLookVideo(row.Id)\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '新增',\r\n      currentComponent: null,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n        },\r\n        fetchData: () => {\r\n          this.fetchData()\r\n        }\r\n      },\r\n      multipleSelection: [],\r\n      Park_Area: ''\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchData()\r\n    this.getGridByCode('equipmentManagement')\r\n    GetParkArea().then(res => {\r\n      this.Park_Area = res.Data\r\n    })\r\n  },\r\n  async mounted() {\r\n    this.customForm.formItems[3].options = await this.getDictionaryDetailListByCode()\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      GetEquipmentListSZCJ({\r\n        ...this.ruleForm, Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n          this.customTableConfig.tableData = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    handleDelete(type, id) {\r\n      if (type == 'batch') {\r\n        if (this.multipleSelection.length == 0) {\r\n          this.$message.warning('请选择数据!')\r\n          return\r\n        } else {\r\n          id = this.multipleSelection.map(item => item.Id).join(',')\r\n        }\r\n      }\r\n      this.$confirm('确认删除？', {\r\n        type: 'warning'\r\n      })\r\n        .then(async(_) => {\r\n          await DelEquipment({ id }).then(res => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success('删除成功!')\r\n              this.fetchData()\r\n            } else {\r\n              this.$message.error(res.Message)\r\n            }\r\n          })\r\n        })\r\n        .catch((_) => { })\r\n    },\r\n    handleEdit(index, row, type = 'add') {\r\n      this.currentComponent = DialogForm\r\n      if (type === 'add') {\r\n        this.dialogTitle = '新增'\r\n        this.componentsConfig.Data = {\r\n          Monitore_Equipment_Number: '',\r\n          Monitore_Equipment_Name: '',\r\n          Monitore_Equipment_SN_Number: '',\r\n          Monitore_Equipment_Type: '',\r\n          Pisition: '',\r\n          Monitore_Equipment_Name: '',\r\n          Park_Area: this.Park_Area,\r\n          Site: '',\r\n          Address: '',\r\n          Brand: '',\r\n          Version: '',\r\n          Equipment_Purpose_Catetory: ''\r\n        }\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        row.Park_area = [row.Purpose_Catetory, row.Scene, row.Site]\r\n        this.componentsConfig.Data = { ...row, Park_Area: this.Park_Area }\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    submitForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData()\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.fetchData()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.customTableConfig.currentPage = val\r\n      this.fetchData()\r\n    },\r\n    handleSelectionChange(data) {\r\n      console.log(data)\r\n      this.multipleSelection = data\r\n    },\r\n    handleLookVideo(id) {\r\n      this.currentComponent = WatchVideoDialog\r\n      this.dialogTitle = '查看视频'\r\n      this.dialogVisible = true\r\n      this.componentsConfig.Data = id\r\n    },\r\n    handleDeviceInfo(id) {\r\n      this.currentComponent = DeviceInfoDialog\r\n      this.dialogTitle = '监控链接'\r\n      this.dialogVisible = true\r\n      MonitoreEquipmentInfo({ id }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.componentsConfig.Data = {\r\n            Mainstream_Code: res.Data.Mainstream_Code,\r\n            Substream_Code: res.Data.Substream_Code,\r\n            Url: res.Data.Url\r\n          }\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleDownTemplate() {\r\n      MonitoreImportTemplate({ code: 'equipmentManagement' }).then(res => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data, '安防监控设备管理导入模板')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleExport() {\r\n      let id = ''\r\n      if (this.multipleSelection.length == 0) {\r\n        this.$message.warning('请选择数据!')\r\n        return\r\n      } else {\r\n        id = this.multipleSelection.map(item => item.Id).join(',')\r\n      }\r\n      ExportMonitoreEquipment({\r\n        code: 'equipmentManagement',\r\n        id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('导出成功')\r\n          downloadFile(res.Data, '安防监控设备管理数据')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleImport() {\r\n      this.currentComponent = ImportFile\r\n      this.dialogTitle = '批量导入'\r\n      this.dialogVisible = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang='scss'>\r\n.equipmentManagement {\r\n  // height: calc(100vh - 90px);\r\n  // overflow: hidden;\r\n}\r\n</style>\r\n"]}]}