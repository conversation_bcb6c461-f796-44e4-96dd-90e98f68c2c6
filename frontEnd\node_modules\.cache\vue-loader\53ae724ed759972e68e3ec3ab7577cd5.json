{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\alarmLinkageSettings\\broadcastTimeSettings.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\alarmLinkageSettings\\broadcastTimeSettings.vue", "mtime": 1755674552413}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["broadcastTimeSettings.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "broadcastTimeSettings.vue", "sourceRoot": "src/views/business/behaviorAnalysis/alarmLinkageSettings", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <el-card class=\"box-card\">\r\n      <div\r\n        v-loading=\"loading\"\r\n        class=\"main-wapper\"\r\n        :style=\"{\r\n          maxHeight: !IsEdit\r\n            ? 'calc(100% - 30px - 14px)'\r\n            : 'calc(100% - 14px - 30px - 30px)',\r\n        }\"\r\n      >\r\n        <div v-for=\"(item, idx) in timeData\" :key=\"idx\">\r\n          <!-- <el-form v-if=\"!item.IsDeleted\" ref=\"formRef\" :model=\"item\" inline label-width=\"120px\" :rules=\"idx !== timeData.findIndex(item => !item.IsDeleted) ? formRules : {}\" :hide-required-asterisk=\"true\"> -->\r\n          <el-form\r\n            v-if=\"!item.IsDeleted\"\r\n            ref=\"formRef\"\r\n            :model=\"item\"\r\n            inline\r\n            label-width=\"120px\"\r\n            :rules=\"formRules\"\r\n            :hide-required-asterisk=\"true\"\r\n          >\r\n            <el-form-item\r\n              :label=\"\r\n                idx == timeData.findIndex((item) => !item.IsDeleted)\r\n                  ? '提交广播时间'\r\n                  : ' '\r\n              \"\r\n              prop=\"time\"\r\n            >\r\n              <el-time-picker\r\n                v-model=\"item.time\"\r\n                is-range\r\n                :disabled=\"!IsEdit\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始时间\"\r\n                end-placeholder=\"结束时间\"\r\n                placeholder=\"选择时间范围\"\r\n                value-format=\"HH:mm:ss\"\r\n                format=\"HH:mm:ss\"\r\n                @change=\"changeTime(idx, $event)\"\r\n              />\r\n            </el-form-item>\r\n            <el-button\r\n              v-if=\"IsEdit\"\r\n              type=\"text\"\r\n              style=\"color: #f56c6c\"\r\n              @click=\"handeldelete(item, idx)\"\r\n              >删除</el-button\r\n            >\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div v-if=\"IsEdit\">\r\n        <el-button\r\n          type=\"text\"\r\n          style=\"font-size: 12px; margin-left: 120px\"\r\n          @click=\"handeladd()\"\r\n          >+新增</el-button\r\n        >\r\n      </div>\r\n      <div class=\"prompt\">\r\n        系统将于以上设置时间段进行行为分析告警信息广播播放，其他时间识别行为分析告警但不进行广播播放\r\n      </div>\r\n      <div class=\"cs-btn\">\r\n        <el-button v-if=\"!IsEdit\" type=\"primary\" @click=\"IsEdit = !IsEdit\"\r\n          >编辑</el-button\r\n        >\r\n        <el-button\r\n          v-if=\"IsEdit\"\r\n          type=\"primary\"\r\n          :loading=\"btnloading\"\r\n          @click=\"handelSubmit\"\r\n          >确认</el-button\r\n        >\r\n        <el-button v-if=\"IsEdit\" @click=\"handelCancel\">取消</el-button>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetBroTimeRangeList, BatchEditBroTimeRange } from '@/api/business/behaviorAnalysis'\r\nimport dayjs from 'dayjs'\r\nexport default {\r\n  data() {\r\n    return {\r\n      timeData: [],\r\n      IsEdit: false,\r\n      formRules: { time: [{ required: true, message: '请填写时间', trigger: 'change' }] },\r\n      loading: false,\r\n      btnloading: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    // 新增\r\n    handeladd() {\r\n      this.timeData.push({\r\n        Id: '',\r\n        StartTime: '',\r\n        EndTime: '',\r\n        time: null,\r\n        IsDeleted: false\r\n      })\r\n    },\r\n    // 删除\r\n    handeldelete(item, idx) {\r\n      if (item.Id) {\r\n        this.timeData[idx].IsDeleted = true\r\n      } else {\r\n        this.timeData.splice(idx, 1)\r\n      }\r\n    },\r\n    // 时间\r\n    changeTime(index, e) {\r\n      console.log(index, e)\r\n      const day = dayjs().format('YYYY-MM-DD')\r\n      this.timeData[index].StartTime = day + ' ' + e[0]\r\n      this.timeData[index].EndTime = day + ' ' + e[1]\r\n      console.log(this.timeData[index])\r\n    },\r\n    // 获取数据\r\n    fetchData() {\r\n      // this.loading = true\r\n      GetBroTimeRangeList({ page: 1, PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          if (res.Data.Data.length > 0) {\r\n            this.timeData = res.Data.Data.map((item) => {\r\n              item.time = [item.StartTime, item.EndTime]\r\n              return item\r\n            })\r\n          } else {\r\n            this.timeData = []\r\n            this.handeladd()\r\n          }\r\n          console.log(this.timeData, ' this.timeData')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 提交\r\n    handelSubmit() {\r\n      const validatePromises = this.$refs.formRef.map((item, index) => {\r\n        return new Promise((resolve, reject) => {\r\n          item.validate((valid) => {\r\n            if (valid) {\r\n              resolve()\r\n            } else {\r\n              reject()\r\n            }\r\n          })\r\n        })\r\n      })\r\n\r\n      Promise.all(validatePromises)\r\n        .then(() => {\r\n          this.saveInfo()\r\n        })\r\n        .catch(() => {\r\n          return false\r\n        })\r\n    },\r\n\r\n    saveInfo() {\r\n      this.btnloading = true\r\n      console.log('111')\r\n      BatchEditBroTimeRange(this.timeData).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '保存成功'\r\n          })\r\n          this.fetchData()\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.btnloading = false\r\n      })\r\n    },\r\n    // 取消\r\n    handelCancel() {\r\n      this.timeData = []\r\n      this.IsEdit = !this.IsEdit\r\n      this.fetchData()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.box-card div:not(:first-child) {\r\n  .el-form {\r\n    .el-form-item {\r\n      ::v-deep.el-form-item__label {\r\n        // color: transparent !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.layout {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .box-card {\r\n    height: calc(100vh - 190px);\r\n    overflow: auto;\r\n    max-height: 100%;\r\n    ::v-deep .el-card__body {\r\n      max-height: 100%;\r\n    }\r\n    .main-wapper {\r\n      // max-height: calc(100% - 34px - 30px);\r\n      overflow: auto;\r\n    }\r\n    .prompt {\r\n      color: #a0a0a0;\r\n      font-size: 12px;\r\n      margin: 10px 0;\r\n      margin-left: 120px;\r\n    }\r\n\r\n    .cs-btn {\r\n      margin-left: 120px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}