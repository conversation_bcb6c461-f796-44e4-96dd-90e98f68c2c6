{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\behavioralAnalysisDashboard\\index.vue?vue&type=style&index=0&id=b541a33c&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\behavioralAnalysisDashboard\\index.vue", "mtime": 1755674552414}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hbmFseXNpc0JveCB7DQogIC8vIHBhZGRpbmc6IDEwcHggMTVweDsNCiAgLy8gaGVpZ2h0OiBjYWxjKDEwMHZoIC0gOTBweCk7DQogIG92ZXJmbG93LXk6IGF1dG87DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQoNCiAgLnRvcCwNCiAgLmJvdHRvbSB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4OiAxOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/behaviorAnalysis/behavioralAnalysisDashboard", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 analysisBox\">\r\n    <top class=\"top\" :params-obj=\"warningTotal\" />\r\n    <bottom class=\"bottom\" :params-arr=\"paramsArr\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Top from './components/top'\r\nimport Bottom from './components/bottom'\r\nimport { GetJumpUrl } from '@/api/business/PJbehaviorAnalysis'\r\nexport default {\r\n  components: {\r\n    Top,\r\n    Bottom\r\n  },\r\n  data() {\r\n    return {\r\n      warningTotal: {},\r\n      paramsArr: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getJumpUrl()\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n    async getJumpUrl() {\r\n      const res = await GetJumpUrl()\r\n      if (res.IsSucceed) {\r\n        res.Data.map(i => {\r\n          if (i.ViewMore == '行为告警总数') {\r\n            this.warningTotal = i\r\n          } else {\r\n            this.paramsArr.push(i)\r\n          }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang='scss'>\r\n.analysisBox {\r\n  // padding: 10px 15px;\r\n  // height: calc(100vh - 90px);\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .top,\r\n  .bottom {\r\n    display: flex;\r\n    flex: 1;\r\n  }\r\n}\r\n</style>\r\n"]}]}