import request from '@/utils/request'
// 车间看板模块

/** ************  生产看板配置 ****************/
// 获取导入模板
export function GetTemplate(data) {
  return request({
    method: 'post',
    url: '/DF/BoardProduce/GetTemplate',
    data
  })
}
// 获取看板数据列表
export function GetPageList(data) {
  return request({
    method: 'post',
    url: '/DF/BoardProduce/GetPageList',
    data
  })
}
// 获取看板配置详情
export function GetDetailEntity(data) {
  return request({
    method: 'post',
    url: '/DF/BoardProduce/GetDetailEntity',
    data
  })
}
// 保存看板单体
export function SaveEntity(data) {
  return request({
    method: 'post',
    url: '/DF/BoardProduce/SaveEntity',
    data
  })
}
// 删除看板
export function DeleteEntity(data) {
  return request({
    method: 'post',
    url: '/DF/BoardProduce/DeleteEntity',
    data
  })
}
// 导入文件
export function ImportFile(data) {
  return request({
    method: 'post',
    url: '/DF/BoardProduce/ImportFile',
    data
  })
}
// 推送大屏数据
export function PushToScreen(data) {
  return request({
    method: 'post',
    url: '/DF/BoardProduce/PushToScreen',
    data
  })
}
// 获取设备数据列表
export function GetEquipAssetForProBoardPageList(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetEquipAssetForProBoardPageList',
    data
  })
}
// 通过id获取设备数据列表
export function GetEquipmentAssetDetail(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetEquipmentAssetDetail',
    data
  })
}
// 班组
export function GetWorkingTeams(data) {
  return request({
    method: 'post',
    url: '/df/ShopSign/GetTeams',
    data
  })
}
// 单点登录
export function DataGrandSsoJumpUrl(data) {
  return request({
    method: 'post',
    url: '/DF/SsoJump/DataGrandSsoJumpUrl',
    data
  })
}
// 信发单点登陆
export function XinFaSsoJumpUrl(data) {
  return request({
    method: 'post',
    url: '/DF/SsoJump/XinFaSsoJumpUrl',
    data
  })
}
