{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\environmentalManagement\\alarmConfiguration\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\environmentalManagement\\alarmConfiguration\\index.vue", "mtime": 1755506574262}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/environmentalManagement/alarmConfiguration", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport DialogForm from './dialogForm.vue'\r\nimport DialogFormLook from './dialogFormLook.vue'\r\n\r\n// import { downloadFile } from '@/utils/downloadFile'\r\nimport deviceTypeMixins from '../../mixins/index.js'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport {\r\n  GetQuotaList,\r\n  DeleteQuota,\r\n  DeleteAllQuota,\r\n  GetEnviromentDTCList\r\n} from '@/api/business/environmentalManagement'\r\nimport dayjs from 'dayjs'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  mixins: [deviceTypeMixins],\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        EquipmentTypeId: '',\r\n        AlarmType: '',\r\n        TriggerItem: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'EquipmentTypeId', // 字段ID\r\n            label: '设备类型', // Form的label\r\n            type: 'select', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            options: [],\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请输入设备类�?\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              this.customForm.formItems.find((item) => item.key === 'TriggerItem').otherOptions.disabled = !e\r\n              this.ruleForm.TriggerItem = ''\r\n              this.getEnviromentDTCList(GetEnviromentDTCList, e)\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'TriggerItem',\r\n            label: '配置�?,\r\n            type: 'select',\r\n\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n              disabled: true,\r\n              placeholder: '请选择...'\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'AlarmType',\r\n            label: '告警类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n              placeholder: '请选择...'\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '新增',\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载�?\n              disabled: false, // 是否禁用\r\n              icon: '', //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleCreate()\r\n              }\r\n            },\r\n            {\r\n              text: '全部删除',\r\n              type: 'danger',\r\n              disabled: false,\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleAllDelete(item)\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          // {\r\n          //   width: 50,\r\n          //   otherOptions: {\r\n          //     type: 'selection',\r\n          //     align: 'center'\r\n          //   }\r\n          // },\r\n          {\r\n            width: 60,\r\n            label: '序号',\r\n            otherOptions: {\r\n              type: 'index',\r\n              align: 'center'\r\n            } // key\r\n            // otherOptions: {\r\n            //   width: 180, // 宽度\r\n            //   fixed: 'left', // left, right\r\n            //   align: 'center' //\tleft/center/right\r\n            // }\r\n          },\r\n          {\r\n            label: '设备类型',\r\n            key: 'EqtType',\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: '告警类型',\r\n            key: 'AlarmType'\r\n          },\r\n          {\r\n            label: '配置�?,\r\n            key: 'TriggerItem'\r\n          },\r\n          {\r\n            label: '对比方式',\r\n            key: 'ContrastModeStr'\r\n          },\r\n          {\r\n            label: '阈�?,\r\n            key: 'LimitValue'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 200\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  async mounted() {\r\n    this.customForm.formItems.find((v) => v.key === 'EquipmentTypeId').options = await this.getDictionaryDetailListByCode('EnvironmentEqtType', 'Id')\r\n    this.customForm.formItems.find((v) => v.key === 'AlarmType').options = await this.getDictionaryDetailListByCode('EnvironmentAlarmType', 'Value')\r\n    this.customForm.formItems.find((v) => v.key === 'EquipmentTypeId').options.unshift({ label: '全部', value: '' })\r\n    this.customForm.formItems.find((v) => v.key === 'AlarmType').options.unshift({ label: '全部', value: '' })\r\n    this.getEnviromentDTCList(GetEnviromentDTCList, '')\r\n    this.init()\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.customForm.formItems.find((item) => item.key === 'TriggerItem').otherOptions.disabled = !this.ruleForm.EquipmentTypeId\r\n      this.getEnviromentDTCList(GetEnviromentDTCList, '')\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.GetQuotaList()\r\n    },\r\n    init() {\r\n      this.GetQuotaList()\r\n    },\r\n    async GetQuotaList() {\r\n      this.customTableConfig.loading = true\r\n      const res = await GetQuotaList({\r\n        ParameterJson: [\r\n          {\r\n            Key: '',\r\n            Value: [null],\r\n            Type: '',\r\n            Filter_Type: ''\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n\r\n        SortName: '',\r\n        SortOrder: '',\r\n        Search: '',\r\n        Content: '',\r\n        ...this.ruleForm\r\n      })\r\n      this.customTableConfig.loading = false\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n          ...item,\r\n          Date: dayjs(item.Date).format('YYYY-MM-DD HH:mm:ss')\r\n        }))\r\n        console.log(res)\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n        this.customTableConfig.buttonConfig.buttonList.find(\r\n          (item) => item.text == '全部删除'\r\n        ).disabled = this.customTableConfig.total == 0\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.componentsConfig = {\r\n        disabled: false,\r\n        title: '新增'\r\n      }\r\n      this.dialogVisible = true\r\n      this.currentComponent = DialogForm\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row)\r\n      console.log(this)\r\n      this.$confirm('该操作将删除当前配置，是否确认删除？', '删除', {\r\n        type: 'error'\r\n      })\r\n        .then(async(_) => {\r\n          const res = await DeleteQuota({\r\n            IDs: [row.ID]\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.init()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n        .catch((_) => { })\r\n    },\r\n    handleAllDelete(index, row) {\r\n      console.log(index, row)\r\n      console.log(this)\r\n      this.$confirm('该操作将删除全部配置，是否确认删除？', '删除', {\r\n        type: 'error'\r\n      })\r\n        .then(async(_) => {\r\n          const res = await DeleteAllQuota({\r\n            // IDs: [row.ID]\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.init()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n        .catch((_) => { })\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      this.dialogVisible = true\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.currentComponent = DialogFormLook\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: true,\r\n          title: '查看'\r\n        }\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        this.currentComponent = DialogForm\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: false,\r\n          title: '编辑'\r\n        }\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: 100% !important;\r\n  width: 100%;\r\n  position: absolute;\r\n  ::v-deep {\r\n    .CustomLayout {\r\n      .layoutTable {\r\n        height: 0;\r\n        .CustomTable {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          .table {\r\n            flex: 1;\r\n            height: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            .el-table {\r\n              flex: 1;\r\n              height: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}