{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\workshopBulletinBoard\\ProductionConfiguration\\index.vue?vue&type=style&index=0&id=38ee0794&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\workshopBulletinBoard\\ProductionConfiguration\\index.vue", "mtime": 1755506574564}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLm10MjAgewogIG1hcmdpbi10b3A6IDEwcHg7Cn0KOjp2LWRlZXAgLmVsLXRhYmxlX19maXhlZC1ib2R5LXdyYXBwZXIgewogIHRvcDogNDBweCAhaW1wb3J0YW50Owp9Ci5sYXlvdXQgewogIGhlaWdodDogY2FsYygxMDB2aCAtIDkwcHgpOwogIG92ZXJmbG93OiBhdXRvOwp9Cjo6di1kZWVwIC5jcy1lbC1kaWFsb2cgewogIG1pbi1oZWlnaHQ6IDMwMHB4ICFpbXBvcnRhbnQ7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAogBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/workshopBulletinBoard/ProductionConfiguration", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        />\n      </template>\n    </CustomLayout>\n    <el-dialog\n      v-dialogDrag\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogVisible\"\n      custom-class=\"cs-el-dialog\"\n      top=\"10vh\"\n    >\n      <component\n        :is=\"currentComponent\"\n        ref=\"currentComponent\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n      />\n    </el-dialog>\n    <!-- 导入弹窗 -->\n    <!-- <dialogImport ref=\"dialogImport\" /> -->\n  </div>\n</template>\n\n<script>\nimport { parseTime } from \"@/utils\";\n// import { baseUrl } from '@/utils/baseurl'\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\nimport DialogForm from \"./components/dialogTable.vue\";\nimport dialogImport from \"./components/dialogImport.vue\";\n// import { downloadFile } from '@/utils/downloadFile'\nimport { GetPreferenceSettingValue } from \"@/api/sys/system-setting\";\nimport { GetGridByCode } from \"@/api/sys\";\nimport { RoleAuthorization } from \"@/api/user\";\nimport addRouterPage from \"@/mixins/add-router-page\";\nimport {\n  GetPageList,\n  DeleteEntity,\n} from \"@/api/business/productionConfiguration\";\n// import { divide } from 'xe-utils'\nexport default {\n  name: \"ProductionConfiguration\",\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout,\n  },\n  mixins: [addRouterPage],\n  data() {\n    return {\n      addPageArray: [\n        {\n          path: this.$route.path + \"/add\",\n          hidden: true,\n          component: () => import(\"./components/add.vue\"),\n          name: \"ProductionConfigurationAdd\",\n          meta: { title: `新增` },\n        },\n        {\n          path: this.$route.path + \"/edit\",\n          hidden: true,\n          component: () => import(\"./components/add.vue\"),\n          name: \"ProductionConfigurationEdit\",\n          meta: { title: `编辑` },\n        },\n        {\n          path: this.$route.path + \"/view\",\n          hidden: true,\n          component: () => import(\"./components/add.vue\"),\n          name: \"ProductionConfigurationView\",\n          meta: { title: `查看` },\n        },\n      ],\n      currentComponent: DialogForm,\n      componentsConfig: {},\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true;\n        },\n        close: () => {\n          this.dialogVisible = false;\n          this.onFresh();\n        },\n      },\n      dialogVisible: false,\n      dialogTitle: \"\",\n      tableSelection: [],\n\n      ruleForm: {\n        Board_Name: \"\",\n      },\n      customForm: {\n        formItems: [\n          {\n            key: \"Board_Name\", // 字段ID\n            label: \"看板名称\", // Form的label\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?            placeholder: \"请输入看�?,\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n            },\n            change: (e) => {\n              // change事件\n              console.log(e);\n            },\n          },\n        ],\n        rules: {\n          // 请参照elementForm rules\n        },\n        customFormButtons: {\n          submitName: \"查询\",\n          resetName: \"重置\",\n        },\n      },\n      customTableConfig: {\n        buttonConfig: {\n          buttonList: [\n            {\n              text: \"新增\",\n              round: false, // 是否圆角\n              plain: false, // 是否朴素\n              circle: false, // 是否圆形\n              loading: false, // 是否加载�?              disabled: false, // 是否禁用\n              icon: \"\", //  图标\n              autofocus: false, // 是否聚焦\n              type: \"primary\", // primary / success / warning / danger / info / text\n              size: \"small\", // medium / small / mini\n              onclick: (item) => {\n                console.log(item);\n                this.handleCreate();\n              },\n            },\n          ],\n        },\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        tableColumns: [\n          {\n            width: 60,\n            label: \"序号\",\n            otherOptions: {\n              type: \"index\",\n              align: \"center\",\n            },\n          },\n          //   {\n          //     label: '报表编码',\n          //     key: 'Code'\n          //   },\n          //   {\n          //     label: '报表名称',\n          //     key: 'Name'\n          //   },\n          //   {\n          //     label: '报表创建时间',\n          //     key: 'Create_Date'\n          //   },\n          //   {\n          //     label: '报表创建�?,\n          //     key: 'Create_UserName'\n          //   }\n        ],\n        tableData: [],\n        tableActionsWidth: 240,\n        tableActions: [\n          {\n            actionLabel: \"查看\",\n            otherOptions: {\n              type: \"text\",\n              disabled: false,\n            },\n            onclick: (index, row) => {\n              this.handleView2(index, row);\n            },\n          },\n          {\n            actionLabel: \"编辑\",\n            otherOptions: {\n              type: \"text\",\n              disabled: false,\n            },\n            onclick: (index, row) => {\n              this.handleEdit(index, row);\n            },\n          },\n          {\n            actionLabel: \"删除\",\n            otherOptions: {\n              type: \"text\",\n              disabled: false,\n            },\n            onclick: (index, row) => {\n              this.handleDelete(index, row);\n            },\n          },\n          {\n            actionLabel: \"复制链接\",\n            otherOptions: {\n              type: \"text\",\n              disabled: false,\n            },\n            onclick: (index, row) => {\n              this.handleCopy(index, row);\n            },\n          },\n          {\n            actionLabel: \"导入数据\",\n            otherOptions: {\n              type: \"text\",\n              disabled: false,\n            },\n            onclick: (index, row) => {\n              this.handleImport(index, row);\n            },\n          },\n        ],\n        otherOptions: {},\n      },\n      roleAuthorizationList: [],\n      Big_Screen_Url: \"\",\n    };\n  },\n  computed: {},\n  created() {\n    this.getBaseData();\n    this.init();\n  },\n  activated() {\n    this.init();\n  },\n  methods: {\n    getBaseData() {\n      // 获取表格配置\n      GetGridByCode({ code: \"production_configuration_list\" }).then((res) => {\n        if (res.IsSucceed) {\n          const data = res.Data.ColumnList.map((item) => {\n            const temp = {\n              label: item.Display_Name,\n              key: item.Code,\n            };\n            if (item.Code === \"Ids\") {\n              temp.render = (row) => {\n                return this.$createElement(\n                  \"el-button\",\n                  {\n                    attrs: {\n                      type: \"text\",\n                    },\n                    on: {\n                      click: (val) => {\n                        this.handleView(val, row);\n                      },\n                    },\n                  },\n                  \"查看\"\n                );\n              };\n            }\n            return temp;\n          });\n          this.customTableConfig.tableColumns.push(...data);\n        } else {\n          this.$message({\n            type: \"error\",\n            message: res.Message,\n          });\n        }\n      });\n      /**\n     *  menuType: 2, //1PC 2app\n        roleType: 3, //1菜单权限�?列权�?�?按钮权限\n     */\n      RoleAuthorization({\n        workObjId: localStorage.getItem(\"Last_Working_Object_Id\"),\n        menuId: this.$route.meta.Id,\n        menuType: 1,\n        roleType: 3,\n        sign: 10,\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.roleAuthorizationList = res.Data;\n          this.customTableConfig.tableActions[1].otherOptions.disabled =\n            !this.roleAuthorizationList.find(\n              (item) => item.display_name === \"编辑\"\n            ).is_enabled;\n          this.customTableConfig.tableActions[2].otherOptions.disabled =\n            !this.roleAuthorizationList.find(\n              (item) => item.display_name === \"删除\"\n            ).is_enabled;\n          this.customTableConfig.tableActions[4].otherOptions.disabled =\n            !this.roleAuthorizationList.find(\n              (item) => item.display_name === \"导入数据\"\n            ).is_enabled;\n        } else {\n          this.$message({\n            type: \"error\",\n            message: res.Message,\n          });\n        }\n      });\n\n      GetPreferenceSettingValue({\n        Code: \"Big_screen\",\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.Big_Screen_Url = res.Data;\n        }\n      });\n    },\n    searchForm(data) {\n      console.log(data);\n      this.onFresh();\n    },\n    resetForm() {\n      this.onFresh();\n    },\n    onFresh() {\n      this.getPageList();\n    },\n    init() {\n      this.getPageList();\n    },\n    async getPageList() {\n      const res = await GetPageList({\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n        ...this.ruleForm,\n      });\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data.map((item) => {\n          item.Modify_Date = item.Modify_Date\n            ? parseTime(new Date(item.Modify_Date), \"{y}-{m}-{d} {h}:{i}:{s}\")\n            : \"\";\n          item.Create_Date = item.Create_Date\n            ? parseTime(new Date(item.Create_Date), \"{y}-{m}-{d} {h}:{i}:{s}\")\n            : \"\";\n          // item.Is_Push = item.Is_Push ? '�? : '�?\n          return item;\n        });\n        this.customTableConfig.total = res.Data.TotalCount;\n      } else {\n        this.$message({\n          type: \"error\",\n          message: res.Message,\n        });\n      }\n    },\n    // 新增\n    handleCreate() {\n      //   this.dialogTitle = '新增'\n      //   this.dialogVisible = true\n      this.$router.push({\n        name: \"ProductionConfigurationAdd\",\n        query: { pg_redirect: this.$route.name, type: 1 },\n      });\n      // this.$qiankun.switchMicroAppFn(\n      //   \"project\",\n      //   \"szdn\",\n      //   \"1cf6f8ac-d9d0-4b18-9959-5e4ef37886f4\",\n      //   `/business/workshop/productionConfiguration/add?pg_redirect=${this.$route.name}&type=1`\n      // );\n    },\n    // 查看绑定设备明细\n    handleView(index, row) {\n      console.log(index, row);\n      this.currentComponent = DialogForm;\n      this.dialogTitle = \"查看绑定设备明细\";\n      this.dialogVisible = true;\n      this.$nextTick(() => {\n        this.$refs.currentComponent.initView(row.Equipment_Ids);\n      });\n    },\n    // 查看看板\n    handleView2(index, row) {\n      window.open(\n        `${this.Big_Screen_Url}/productionBoard/index?id=${\n          row.Id\n        }&tenant=${localStorage.getItem(\"tenant\")}`,\n        \"_blank\"\n      );\n      // window.open(\n      //   `${process.env.VUE_APP_SCREEN_URL}/productionBoard/index?id=${\n      //     row.Id\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`,\n      //   \"_blank\"\n      // );\n      // if (process.env.NODE_ENV === \"development\") {\n      //   window.open(\n      //     `http://localhost:5173/productionBoard/index?id=${\n      //       row.Id\n      //     }&tenant=${localStorage.getItem(\"tenant\")}`,\n      //     \"_blank\"\n      //   );\n      // } else {\n      //   window.open(\n      //     `http://wnpzgc-test.bimtk.com/productionBoard/index?id=${\n      //       row.Id\n      //     }&tenant=${localStorage.getItem(\"tenant\")}`,\n      //     \"_blank\"\n      //   );\n      // }\n    },\n    // 编辑\n    handleEdit(index, row) {\n      console.log(index, row);\n      this.$router.push({\n        name: \"ProductionConfigurationAdd\",\n        query: { pg_redirect: this.$route.name, type: 2, id: row.Id },\n      });\n    },\n    // 复制链接\n    handleCopy(index, row) {\n      // console.log(\n      //   process.env.VUE_APP_SCREEN_URL,\n      //   \"process.env.VUE_APP_SCREEN_URL\"\n      // );\n      const textareaEle = document.createElement(\"textarea\");\n      // if (process.env.NODE_ENV === \"development\") {\n      //   textareaEle.value = `http://localhost:5173/productionBoard/index?id=${\n      //     row.Id\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`;\n      // } else {\n      //   textareaEle.value = `http://wnpzgc-test.bimtk.com/productionBoard/index?id=${\n      //     row.Id\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`;\n      // }\n      textareaEle.value = `${this.Big_Screen_Url}/productionBoard/index?id=${\n        row.Id\n      }&tenant=${localStorage.getItem(\"tenant\")}`;\n\n      document.body.appendChild(textareaEle);\n      textareaEle.select();\n      document.execCommand(\"copy\");\n      document.body.removeChild(textareaEle);\n      this.$message({\n        type: \"success\",\n        message: \"已成功复制到截切�?,\n      });\n    },\n    // 导入数据\n    handleImport(index, row) {\n      console.log(index, row);\n      this.currentComponent = dialogImport;\n      this.dialogTitle = \"导入数据\";\n      this.dialogVisible = true;\n      this.$nextTick(() => {\n        this.$refs.currentComponent.init(row);\n      });\n    },\n    // 删除\n    handleDelete(index, row) {\n      console.log(index, row);\n      this.$confirm(\"确认删除�?, {\n        type: \"warning\",\n      })\n        .then(async (_) => {\n          const res = await DeleteEntity({\n            id: row.Id,\n          });\n          if (res.IsSucceed) {\n            this.$message({\n              type: \"success\",\n              message: \"删除成功\",\n            });\n            this.init();\n          } else {\n            this.$message({\n              type: \"error\",\n              message: res.Message,\n            });\n          }\n        })\n        .catch((_) => {});\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`);\n      this.customTableConfig.pageSize = val;\n      this.init();\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`);\n      this.customTableConfig.currentPage = val;\n      this.init();\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection;\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.mt20 {\n  margin-top: 10px;\n}\n::v-deep .el-table__fixed-body-wrapper {\n  top: 40px !important;\n}\n.layout {\n  height: calc(100vh - 90px);\n  overflow: auto;\n}\n::v-deep .cs-el-dialog {\n  min-height: 300px !important;\n}\n</style>\n"]}]}