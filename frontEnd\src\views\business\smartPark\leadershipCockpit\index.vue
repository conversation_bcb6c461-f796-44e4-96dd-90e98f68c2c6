<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        >

          <template #customBtn="{slotScope}">
            <el-button v-if="slotScope.Status !== 2" type="text" @click="handleCreate(slotScope)">导入表单</el-button>
            <el-button v-if="slotScope.Status == 1" type="text" @click="withDrawOrSubmit(slotScope,1)">提交</el-button>
            <el-button v-if="slotScope.Status !== 0" type="text" @click="handleTemplateDown(slotScope)">下载</el-button>
            <el-button type="text" @click="handelHistory(slotScope)">历史记录</el-button>
            <el-button v-if="slotScope.Status == 2" type="text" @click="withDrawOrSubmit(slotScope,2)">撤回</el-button>
          </template></CustomTable>
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible" top="6vh">
      <component
        :is="currentComponent"
        ref="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      />
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from '@/utils'
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import DialogForm from './dialogForm.vue'
import history from './history.vue'
import { combineURL } from '@/utils'
import {
  GetPageList,
  OperateData,
  DownloadDataAsync
} from '@/api/business/smartPark'
export default {
  name: 'LeadershipCockpit',
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
    history,
    DialogForm
  },
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {},
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        },

      },
      dialogVisible: false,
      dialogTitle: '',
      tableSelection: [],

      ruleForm: {
        Status: null,
        StartDate: null,
        EndDate: null,
        data: []
      },
      customForm: {
        formItems: [
          {
            key: 'Status', // 字段ID
            label: '状态', // Form的label
            type: 'select', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            placeholder: '请选择',
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true
            },
            options: [
              {
                value: 0,
                label: '未导入'
              },
              {
                value: 1,
                label: '草稿'
              },
              {
                value: 2,
                label: '正式'
              }
            ],
            width: '240px',
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'data',
            label: '时间',
            type: 'datePicker',
            otherOptions: {
              type: 'monthrange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              clearable: true,
              valueFormat: 'yyyy-MM'
            },
            change: (e) => {
              this.ruleForm.StartDate = e[0]
              this.ruleForm.EndDate = e[1]
            }
          }
        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList:[]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            width: 60,
            label: '序号',
            otherOptions: {
              type: 'index',
              align: 'center'
            }
          },
          {
            label: '报表统计月份',
            key: 'Title'
          },
          {
            label: '状态',
            key: 'StatusName'
          },
          {
            label: '操作人',
            key: 'Operator'
          },
          {
            label: '操作时间',
            key: 'OperateTime'
          }
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: '',
            otherOptions: {
              type: 'text',
              disabled: false
            },
            onclick: (index, row) => {
              this.handleView(index, row)
            }
          }

        ]
      }
    }
  },
  computed: {},
  created() {
    // this.getBaseData()
    this.init()
  },
  methods: {
    searchForm(data) {
      console.log(data)
      this.onFresh()
    },
    resetForm() {
      this.onFresh()
    },
    onFresh() {
      this.getPageList()
    },
    init() {
      this.getPageList()
    },
    async getPageList() {
      if (this.ruleForm.data.length == 0) {
        this.ruleForm.StartDate = null
        this.ruleForm.EndDate = null
      }
      const res = await GetPageList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        console.log(res)
        this.customTableConfig.tableData = res.Data.Data
        this.customTableConfig.total = res.Data.Total
      } else {
        this.$message({
          type: 'error',
          message: res.Message
        })
      }
    },
    handleCreate(slotScope) {
      this.dialogTitle = '导入报表'
      this.dialogVisible = true
      this.currentComponent = 'DialogForm'
      this.$nextTick(() => {
        this.$refs.currentComponent.init(slotScope)
      })
    },
    // handleView(index, row) {
    //   // console.log(index, row)
    //   // 环境判断
    //   if (process.env.NODE_ENV === 'development') {
    //     console.log('开发环境')
    //     window.open('http://wnpzgc-dev.bimtk.com/cockpit', '_blank')
    //   } else {
    //     console.log('生产环境')
    //     window.open('http://wnpzgc-test.bimtk.com/cockpit', '_blank')
    //   }
    // },
    async handleTemplateDown(row) {
      if (row.Url) {
        DownloadDataAsync({ url: row.Url }).then((res) => {
          const url = window.URL.createObjectURL(
            new Blob([res], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
          )
          const link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          // 文件名
          link.setAttribute('download', '驾驶舱报表.xlsx')
          document.body.appendChild(link)
          link.click()
        })
      } else {
        this.$message({
          type: 'error',
          message: '暂无数据'
        })
      }
    },

    // 撤回/提交
    withDrawOrSubmit(row, type) {
      this.$confirm(`${type === 2 ? '数据撤回会，当月数据将不会在驾驶舱大屏上展示，请确认，是否继续?' : '提交选中的数据，是否继续？'} `, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        OperateData({
          Id: row.Id,
          Type: type
        }).then((res) => {
          console.log(res)
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: `${type === 2 ? '撤回成功!' : '提交成功!'}`
            })
            this.onFresh()
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    // 历史记录
    handelHistory(row) {
      this.dialogTitle = '历史记录'
      this.dialogVisible = true
      this.currentComponent = 'history'
      this.$nextTick(() => {
        this.$refs.currentComponent.init(row)
      })
    },

    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.init()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.init()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
    }
  }
}
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
.layout{
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>

