<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        >
          <template #customBtn="{ slotScope }">
            <el-button
              v-if="slotScope.Handle_Status != 2"
              type="text"
              @click="handleChange(slotScope)"
            >关闭</el-button>
          </template>
        </CustomTable>
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import getGridByCode from '../../safetyManagement/mixins/index'
import DialogForm from './dialogForm.vue'

import { downloadFile } from '@/utils/downloadFile'
import dayjs from 'dayjs'
import {
  entranceWarningGetWarningList,
  ExportEntranceWarning,
  UpdateEntranceWarningStatus
} from '@/api/business/hazardousChemicals'
export default {
  name: '',
  components: {
    CustomTable,
    CustomForm,
    CustomLayout
  },
  mixins: [getGridByCode],
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {
        Data: {}
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '查看',
      tableSelection: [],
      ruleForm: {
        Warning_Name: '',
        Warning_Type: '',
        Warning_Information: ''
      },
      customForm: {
        formItems: [
          {
            key: 'Warning_Name',
            label: '告警事件名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'Warning_Type',
            label: '告警事件类型',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Warning_Information',
            label: '告警内容',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'Handle_Status',
            label: '告警状态',
            type: 'select',
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: '请选择告警状态'
            },
            options: [
              {
                label: '告警中',
                value: 1
              },
              {
                label: '已关闭',
                value: 2
              },
              {
                label: '已处理',
                value: 3
              }
            ],
            change: (e) => {
              console.log(e)
            }
          }
        ],
        rules: {
        },
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: '导出',
              onclick: (item) => {
                console.log(item)
                this.handleExport()
              }
            }
          ]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [],
        tableData: [],
        operateOptions: {
          align: 'center'
        },
        tableActionsWidth: 120,
        tableActions: [
          // {
          //   actionLabel: '关闭',
          //   otherOptions: {
          //     type: 'text'
          //   },
          //   onclick: (index, row) => {
          //     this.handleChange(row)
          //   }
          // },
          {
            actionLabel: '查看',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(row)
            }
          }
        ]
      }
    }
  },
  computed: {},
  created() {
    this.init()
  },
  methods: {
    searchForm(data) {
      console.log(data)
      this.customTableConfig.currentPage = 1
      this.onFresh()
    },
    resetForm() {
      this.onFresh()
    },
    onFresh() {
      this.getEquipmentList()
    },
    init() {
      this.getGridByCode('AccessControlAlarmDetails1')
      this.getEquipmentList()
    },
    async getEquipmentList() {
      const res = await entranceWarningGetWarningList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data
        this.customTableConfig.total = res.Data.TotalCount
        if (this.customTableConfig.tableData.length > 0) {
          this.customTableConfig.tableData.map(v => {
            v.Warning_First_Time = (v.Warning_First_Time ?? '') != '' ? dayjs(v.Warning_First_Time).format('YYYY-MM-DD HH:mm:ss') : ''
            v.Warning_Last_Time = (v.Warning_Last_Time ?? '') != '' ? dayjs(v.Warning_Last_Time).format('YYYY-MM-DD HH:mm:ss') : ''
          })
        }
      } else {
        this.$message.error(res.Message)
      }
    },
    async handleExport() {
      if (this.tableSelection.length == 0) {
        this.$message.warning('请选择数据在导出')
        return
      }
      const res = await ExportEntranceWarning({
        id: this.tableSelection.map((item) => item.Id).toString(),
        code: 'AccessControlAlarmDetails1'
      })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '告警明细数据')
      } else {
        this.$message.error(res.Message)
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.getEquipmentList()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.getEquipmentList()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
    },
    handleEdit(row) {
      this.dialogVisible = true
      this.componentsConfig.Data = row
    },
    handleChange(row) {
      if (row.HandleStatusStr == '关闭') {
        this.$message.warning('请勿重复操作')
      } else {
        this.$confirm('此操作将关闭该告警, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          UpdateEntranceWarningStatus({ id: row.Id, wid: row.WId, StatusEnum: 2 }).then(res => {
            if (res.IsSucceed) {
              this.$message.success('操作成功')
              this.init()
            } else {
              this.$message.error(res.Message)
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
.layout{
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
