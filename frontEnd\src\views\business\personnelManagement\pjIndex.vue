<template>
  <div class="app-container abs100 personnelManagement">
    <custom-layout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="submitForm"
          @resetForm="fetchData"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </custom-layout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      />
    </el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import getGridByCode from '../safetyManagement/mixins/index'
import {
  querypersonnel,
  DeletePersonnel,
  DownloadPersonnelsTemplate,
  DownloadPersonnelsToExcel,
  ExportPersonnelList
} from '@/api/business/personnelManagementV2.js'
import { GetDepartment, GetCompany, GetMesTeams } from '@/api/business/accessControl'
import { downloadFile } from '@/utils/downloadFile'
import addDialog from './components/pjAddDialog'
import DialogFormImport from './components/pjImportFile'
import DialogPhotoFormImport from './components/pjPhotoImportFile'
import addRouterPage from '@/mixins/add-router-page'
export default {
  components: {
    CustomLayout,
    CustomTable,
    CustomForm
  },
  mixins: [getGridByCode, addRouterPage],
  data() {
    return {
      ruleForm: {
        name: '',
        mobile: '',
        personnelType: null,
        companyId: '',
        departmentId: '',
        MesTeamId: '',
        personnelStatus: null
      },
      customForm: {
        formItems: [
          {
            key: 'name',
            label: '姓名',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'mobile',
            label: '联系方式',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'personnelType',
            label: '人员类型',
            type: 'select',
            options: [
              {
                label: '系统人员',
                value: '1'
              },
              {
                label: '普通人员',
                value: '2'
              }
            ],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'companyId',
            label: '所属公司',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'departmentId',
            label: '所属部门',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'personnelStatus',
            label: '状态',
            type: 'select',
            options: [
              {
                label: '在职',
                value: '1'
              },
              {
                label: '离职',
                value: '2'
              }
            ],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'MesTeamId',
            label: 'MES班组',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true,
              filterable: true
            },
            change: (e) => {
              console.log(e)
            }
          }
        ],
        rules: {},
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: '导入模板下载',
              icon: 'el-icon-download',
              onclick: () => {
                this.handleDownTemplate()
              }
            },
            {
              text: '批量导入',
              icon: 'el-icon-upload2',
              onclick: () => {
                this.handleImport()
              }
            },
            {
              text: '照片导入',
              icon: 'el-icon-upload2',
              onclick: () => {
                this.handlePhotoImport()
              }
            },
            {
              text: '批量导出',
              icon: 'el-icon-download',
              onclick: () => {
                this.handleExport()
              }
            },
            {
              text: '新增',
              icon: 'el-icon-plus',
              type: 'primary',
              onclick: () => {
                this.handleClick('add')
              }
            }
          ]
        },
        pageSizeOptions: [20, 40, 60, 80, 100],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [],
        tableData: [],
        operateOptions: {
          align: 'center',
          width: '130px'
        },
        tableActions: [
          {
            actionLabel: '查看',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleWatch(row.Id)
            }
          },
          {
            actionLabel: '编辑',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleClick('edit', row)
            }
          },
          {
            actionLabel: '删除',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleDele(row.Id)
            }
          }
        ]
      },
      multipleSelection: [],
      dialogVisible: false,
      currentComponent: null,
      dialogTitle: '新增',
      componentsFuns: {
        close: () => {
          this.dialogVisible = false
          this.fetchData()
        }
      },
      componentsConfig: {},
      addPageArray: [
        {
          path: this.$route.path + '/pjInfo',
          hidden: true,
          component: () => import('./pjInfo.vue'),
          meta: { title: '人员详情' },
          name: 'PersonnelManagementPJInfo'
        }
      ]
    }
  },
  async created() {
    this.fetchData()
    this.getMesTeams()
    this.customForm.formItems.find(
      (item) => item.key === 'departmentId'
    ).options = await this.initGetDepartment()
    // 所属单位
    this.customForm.formItems.find((item) => item.key === 'companyId').options =
      await this.initGetCompany()
    this.getGridByCodeRender('PJPersonnelManagement', [
      { key: 'Gender', Tag: 'span', condition: 1, val1: '男', val2: '女' },
      {
        key: 'PersonnelStatus',
        Tag: 'span',
        condition: 1,
        val1: '在职',
        val2: '离职'
      },
      {
        key: 'PersonnelType',
        Tag: 'span',
        condition: 1,
        val1: '系统人员',
        val2: '普通人员'
      }
    ])
  },
  async mounted() {
    this.customForm.formItems[1].options =
      await this.getDictionaryDetailListByCode('PatrolResult')
  },
  methods: {
    fetchData() {
      querypersonnel({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm
      }).then((res) => {
        if (res.IsSucceed) {
          this.customTableConfig.tableData = res.Data.Data
          this.customTableConfig.total = res.Data.TotalCount
        } else {
          this.$message.error(res.Message)
        }
      })
    },

    submitForm(data) {
      this.customTableConfig.currentPage = 1
      this.fetchData()
    },
    async initGetDepartment() {
      const res = await GetDepartment({})
      const options = res.Data.map((item, index) => ({
        label: item.Display_Name,
        value: item.Value
      }))
      return options
    },
    async initGetCompany() {
      const res = await GetCompany({})
      const options = res.Data.map((item, index) => ({
        label: item.Display_Name,
        value: item.Value
      }))
      return options
    },
    handleSizeChange(val) {
      this.customTableConfig.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.customTableConfig.currentPage = val
      this.fetchData()
    },
    handleSelectionChange(data) {
      this.multipleSelection = data
    },
    // async handleExport() {
    //   const res = await DownloadPersonnelsToExcel({
    //     id: this.multipleSelection.map(v => v.Id)
    //   })
    //   if (res.IsSucceed) {
    //     downloadFile(res.Data, '人员管理')
    //   } else {
    //     this.$message({
    //       type: 'error',
    //       message: res.Message
    //     })
    //   }
    // },
    // v2 版本导出
    async handleExport() {
      const res = await ExportPersonnelList({
        Id: this.multipleSelection.map((v) => v.Id).toString(),
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        downloadFile(res.Data, '人员管理')
      } else {
        this.$message({
          type: 'error',
          message: res.Message
        })
      }
    },
    handleClick(type, data) {
      this.dialogVisible = true
      this.currentComponent = addDialog
      if (type == 'add') {
        this.dialogTitle = '新增'
        this.componentsConfig = {
          name: '',
          mobile: '',
          personnelType: null,
          companyId: '',
          departmentId: '',
          personnelStatus: null
        }
      } else {
        this.dialogTitle = '编辑'
        this.componentsConfig = data
      }
    },
    handleWatch(Id) {
      this.$router.push({
        name: 'PersonnelManagementPJInfo',
        query: { pg_redirect: this.$route.name, Id }
      })
    },
    handleDele(id) {
      this.$confirm('是否确定删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          DeletePersonnel({ id }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.fetchData()
            } else {
              this.$message({
                type: 'error',
                message: res.Message
              })
            }
          })
        })
        .catch(() => { })
    },
    handleDownTemplate() {
      DownloadPersonnelsTemplate({}).then((res) => {
        if (res.IsSucceed) {
          downloadFile(res.Data, '授权名单导入模板')
        } else {
          this.$message.error(res.Message)
        }
      })
    },
    async handleImport() {
      this.dialogTitle = '批量导入'
      this.currentComponent = DialogFormImport
      this.componentsConfig = {
        disabled: true,
        title: '批量导入'
      }
      this.dialogVisible = true
    },
    async handlePhotoImport() {
      this.dialogTitle = '数据批量导入'
      this.currentComponent = DialogPhotoFormImport
      this.componentsConfig = {
        disabled: true,
        title: '数据批量导入'
      }
      this.dialogVisible = true
    },
    async getMesTeams() {
      const res = await GetMesTeams()
      if (res.IsSucceed) {
        this.customForm.formItems.find((item) => item.key === 'MesTeamId').options =
          res.Data.map(v => {
            v.value = v.MesTeamId
            v.label = v.TeamName
            return v
          })
      }
    }
  }
}
</script>
  <style scoped lang='scss'>
  .personnelManagement{
    // height: calc(100vh - 90px);
    // overflow: hidden;
  }
</style>
