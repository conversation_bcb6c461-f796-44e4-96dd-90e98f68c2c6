{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\environmentalManagement\\monitorData\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\environmentalManagement\\monitorData\\index.vue", "mtime": 1755506574270}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk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file": "index.vue", "sourceRoot": "src/views/business/environmentalManagement/monitorData", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport DialogFormLook from './dialogFormLook.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport {\r\n  GetDataList,\r\n  ExportDataList,\r\n  GetEnviromentDTCList,\r\n  ExportEnvironmentDataList\r\n} from '@/api/business/environmentalManagement'\r\nimport { GetDictionaryDetailListByCode } from '@/api/sys'\r\nimport { deviceTypeMixins } from '../../mixins/deviceType.js'\r\nimport otherMixin from '../../mixins/index.js'\r\n\r\n// import * as moment from 'moment'\r\nimport dayjs from 'dayjs'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  mixins: [deviceTypeMixins, otherMixin],\r\n  data() {\r\n    return {\r\n      currentComponent: DialogFormLook,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        DataType: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Content', // 字段ID\r\n            label: '', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '输入设备编号或名称进行搜�?\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EqtType',\r\n            label: '设备类型',\r\n            type: 'select',\r\n\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择设备类型'\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              this.ruleForm.DataType = ''\r\n              const Id = this.deceiveTypeList.find((item) => item.value === e).Id\r\n              this.getDTCList(GetEnviromentDTCList, Id, 'DataType', 'DataId')\r\n            }\r\n          },\r\n          {\r\n            key: 'DataType',\r\n            label: '数据类型',\r\n            type: 'select',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择设备类型'\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position', // 字段ID\r\n            label: '安装位置', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请输入安装位�?\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            // {\r\n            //   text: '新增',\r\n            //   round: false, // 是否圆角\r\n            //   plain: false, // 是否朴素\r\n            //   circle: false, // 是否圆形\r\n            //   loading: false, // 是否加载�?\n            //   disabled: false, // 是否禁用\r\n            //   icon: '', //  图标\r\n            //   autofocus: false, // 是否聚焦\r\n            //   type: 'primary', // primary / success / warning / danger / info / text\r\n            //   size: 'small', // medium / small / mini\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleCreate()\r\n            //   }\r\n            // },\r\n            // {\r\n            //   text: '导出',\r\n            //   key: 'batch',\r\n            //   disabled: true,\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleExport()\r\n            //   }\r\n            // },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleAllExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          // {\r\n          //   otherOptions: {\r\n          //     width: 20,\r\n          //     type: \"selection\",\r\n          //     align: \"center\",\r\n          //   },\r\n          // },\r\n          // {\r\n          //   width: 60,\r\n          //   label: '序号',\r\n          //   otherOptions: {\r\n          //     type: 'index',\r\n          //     align: 'center'\r\n          //   }\r\n          // }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 200\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          }\r\n          // {\r\n          //   actionLabel: '编辑',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleEdit(index, row, 'edit')\r\n          //   }\r\n          // },\r\n          // {\r\n          //   actionLabel: '删除',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleDelete(index, row)\r\n          //   }\r\n          // }\r\n        ]\r\n      },\r\n      deceiveTypeList: []\r\n    }\r\n  },\r\n  computed: {},\r\n  async mounted() {\r\n    this.init()\r\n    this.deceiveTypeList = this.customForm.formItems.find((item) => item.key === 'EqtType').options = await this.getDictionaryDetailListByCode('EnvironmentEqtType', 'Value')\r\n    // this.initDeviceType(\"EqtType\", \"EnvironmentEqtType\");\r\n    this.getDTCList(GetEnviromentDTCList, '', 'DataType', 'DataId')\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.getDTCList(GetEnviromentDTCList, '', 'DataType', 'DataId')\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.GetDataList()\r\n    },\r\n    init() {\r\n      this.GetDataList()\r\n    },\r\n    async GetDataList() {\r\n      this.customTableConfig.loading = true\r\n      await GetDataList({\r\n        ParameterJson: [\r\n          {\r\n            Key: '',\r\n            Value: [null],\r\n            Type: '',\r\n            Filter_Type: ''\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        SortName: '',\r\n        SortOrder: '',\r\n        Search: '',\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        ...this.ruleForm\r\n      }).then((res) => {\r\n        this.customTableConfig.loading = false\r\n        if (res.IsSucceed) {\r\n          if (res.Data.Data && res.Data.Data.length > 0) {\r\n            this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n              ...item,\r\n              Time: dayjs(item.Time).format('YYYY-MM-DD HH:mm:ss')\r\n            }))\r\n          } else {\r\n            this.customTableConfig.tableData = []\r\n          }\r\n          /* this.customTableConfig.tableColumns = [].concat(\r\n            [\r\n              // {\r\n              //   width: 50,\r\n              //   otherOptions: {\r\n              //     type: 'selection',\r\n              //     align: 'center'\r\n              //   }\r\n              // },\r\n              // {\r\n              //   width: 60,\r\n              //   label: '序号',\r\n              //   otherOptions: {\r\n              //     type: 'index',\r\n              //     align: 'center'\r\n              //   }\r\n              // }\r\n            ],\r\n            res.Data.Data.Headers\r\n          )\r\n          console.log(res.Data.Data.Headers, 'tableColumns')\r\n          console.log(this.customTableConfig.tableColumns, 'tableColumns')\r\n          */\r\n          /** 2023-09-18 erwin modify */\r\n          this.customTableConfig.tableColumns = [\r\n            {\r\n              otherOptions: {\r\n                width: 20,\r\n                type: 'selection',\r\n                align: 'center'\r\n              }\r\n            },\r\n            {\r\n              width: 60,\r\n              label: '序号',\r\n              otherOptions: {\r\n                type: 'index',\r\n                align: 'center'\r\n              }\r\n            },\r\n            {\r\n              label: '设备编号',\r\n              key: 'EId',\r\n              otherOptions: {\r\n                fixed: 'left'\r\n              },\r\n            },\r\n            {\r\n              label: '设备名称',\r\n              key: 'Name',\r\n              otherOptions: {\r\n                fixed: 'left'\r\n              },\r\n            },\r\n            {\r\n              label: '设备类型',\r\n              key: 'EqtType'\r\n            },\r\n            {\r\n              label: '安装位置',\r\n              key: 'Position'\r\n            },\r\n            {\r\n              label: '数据更新时间',\r\n              key: 'Time'\r\n            },\r\n            {\r\n              label: '数据类型',\r\n              key: 'TypeDes'\r\n              // render: (row) => {\r\n              //   return `${row.TypeDes}(${row.Unit})`\r\n              // }\r\n            },\r\n            {\r\n              label: '数据参数',\r\n              key: 'Value'\r\n            },\r\n            {\r\n              label: '单位',\r\n              key: 'Unit'\r\n            }\r\n          ]\r\n          /** end  */\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    async getDictionaryDetailListByCode(dictionaryCode = 'deviceType', Value) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode\r\n      })\r\n      if (res.IsSucceed) {\r\n        const options = [{ label: '全部', value: '' }]\r\n        res.Data.map((item) => {\r\n          options.push({\r\n            label: item.Display_Name,\r\n            value: item[Value],\r\n            ...item\r\n          })\r\n        })\r\n        return options\r\n      }\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      this.dialogVisible = true\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.currentComponent = DialogFormLook\r\n        this.componentsConfig = {\r\n          ID: row.Id,\r\n          disabled: true,\r\n          title: '查看',\r\n          ...row\r\n        }\r\n      }\r\n      // else if (type === 'edit') {\r\n      //   this.dialogTitle = '编辑'\r\n      //   this.componentsConfig = {\r\n      //     ID: row.ID,\r\n      //     disabled: false,\r\n      //     title: '编辑'\r\n      //   }\r\n      // }\r\n    },\r\n    // async handleExport() {\r\n    //   console.log(this.ruleForm)\r\n    //   const res = await ExportDataList({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: false,\r\n    //     Ids: this.tableSelection.map((item) => item.Id),\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n    // async handleAllExport() {\r\n    //   const res = await ExportDataList({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: true,\r\n    //     Ids: [],\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n    // v2 版本导出\r\n    async handleAllExport() {\r\n      const res = await ExportEnvironmentDataList({\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        Id: this.tableSelection.map((item) => item.Id).toString(),\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n      // if (this.tableSelection.length > 0) {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find(\r\n      //     (v) => v.key == 'batch'\r\n      //   ).disabled = false\r\n      // } else {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find(\r\n      //     (v) => v.key == 'batch'\r\n      //   ).disabled = true\r\n      // }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}