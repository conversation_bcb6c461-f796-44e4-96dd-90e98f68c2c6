{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\environmentalManagement\\monitoringArchives\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\environmentalManagement\\monitoringArchives\\index.vue", "mtime": 1755674552418}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "DialogForm", "DialogType", "DialogFormLook", "downloadFile", "GetEquipmentList", "DeleteEquipment", "ExportEnvironmentEquipment", "EnvironmentImportTemplate", "EnvironmentEquipmentImport", "ExportEquipmentList", "deviceTypeMixins", "dayjs", "importDialog", "exportInfo", "getGridByCode", "name", "components", "mixins", "data", "_this", "options", "props", "label", "children", "value", "checkStrictly", "positionArr", "currentComponent", "componentsConfig", "interfaceName", "componentsFuns", "open", "dialogVisible", "close", "onFresh", "dialogTitle", "tableSelection", "ruleForm", "Content", "EqtType", "Position", "customForm", "formItems", "key", "type", "otherOptions", "clearable", "placeholder", "width", "change", "e", "console", "log", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "text", "size", "onclick", "item", "handleClick", "handleCreate", "disabled", "handleDownTemplate", "handleAllExport", "loading", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "align", "fixed", "tableData", "operateOptions", "tableActions", "actionLabel", "index", "row", "handleEdit", "handleDelete", "computed", "created", "init", "mounted", "initDeviceType", "methods", "searchForm", "resetForm", "getEquipmentList", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "get<PERSON>reeA<PERSON>ress", "sent", "stop", "_this3", "_callee2", "res", "_callee2$", "_context2", "length", "join", "_objectSpread", "Parameter<PERSON>son", "Key", "Value", "Type", "Filter_Type", "Page", "PageSize", "SortName", "SortOrder", "Search", "IsAll", "IsSucceed", "Data", "map", "Date", "format", "TotalCount", "$message", "error", "Message", "title", "_this4", "$confirm", "then", "_ref", "_callee3", "_", "_callee3$", "_context3", "IDs", "ID", "_x", "apply", "arguments", "catch", "_this5", "_callee4", "_callee4$", "_context4", "Id", "toString", "_this6", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "selection"], "sources": ["src/views/business/environmentalManagement/monitoringArchives/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        >\r\n          <template #Position>\r\n            <el-cascader\r\n              v-model=\"positionArr\"\r\n              :options=\"options\"\r\n              :placeholder=\"`请选择`\"\r\n              :props=\"props\"\r\n              style=\"margin-bottom: 18px; width: 100%\"\r\n            />\r\n          </template>\r\n        </CustomForm>\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport DialogForm from './dialogForm.vue'\r\nimport DialogType from './dialogType.vue'\r\nimport DialogFormLook from './dialogFormLook.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport {\r\n  GetEquipmentList,\r\n  DeleteEquipment,\r\n  ExportEnvironmentEquipment,\r\n  EnvironmentImportTemplate,\r\n  EnvironmentEquipmentImport,\r\n  ExportEquipmentList,\r\n} from '@/api/business/environmentalManagement'\r\nimport { deviceTypeMixins } from '../../mixins/deviceType.js'\r\n// import * as moment from 'moment'\r\nimport dayjs from 'dayjs'\r\nimport importDialog from '@/views/business/energyManagement/components/import.vue'\r\nimport exportInfo from '@/views/business/energyManagement/mixins/export.js'\r\nimport getGridByCode from '@/views/business/safetyManagement/mixins/index'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout,\r\n    importDialog\r\n  },\r\n  mixins: [deviceTypeMixins, exportInfo, getGridByCode],\r\n  data() {\r\n    return {\r\n      options: [],\r\n      props: {\r\n        label: 'Label',\r\n        children: 'Children',\r\n        value: 'Label',\r\n        checkStrictly: true\r\n      },\r\n      positionArr: [],\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        interfaceName: EnvironmentEquipmentImport\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Content', // 字段ID\r\n            label: '', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '输入设备编号或名称进行搜索'\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EqtType',\r\n            label: '设备类型',\r\n            type: 'select',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择设备类型'\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position', // 字段ID\r\n            label: '安装位置', // Form的label\r\n            type: 'slot', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              // placeholder: '请输入安装位置'\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '设备类型配置',\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleClick()\r\n              }\r\n            },\r\n            {\r\n              text: '新增',\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleCreate()\r\n              }\r\n            },\r\n            {\r\n              text: '下载模板',\r\n              disabled: false, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleDownTemplate()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导入',\r\n              disabled: false, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.currentComponent = 'importDialog'\r\n                this.dialogVisible = true\r\n                this.dialogTitle = '批量导入'\r\n              }\r\n            },\r\n            // {\r\n            //   text: '导出',\r\n            //   key: 'batch',\r\n            //   disabled: true,\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleExport()\r\n            //   }\r\n            // },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleAllExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            width: 60,\r\n            label: '序号',\r\n            otherOptions: {\r\n              type: 'index',\r\n              align: 'center'\r\n            } // key\r\n            // otherOptions: {\r\n            //   width: 180, // 宽度\r\n            //   fixed: 'left', // left, right\r\n            //   align: 'center' //\tleft/center/right\r\n            // }\r\n          },\r\n          {\r\n            label: '设备编号',\r\n            key: 'EId',\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n            // render: row => {\r\n            //   return (\r\n            //     <span>\r\n            //       {row.EId}\r\n            //     </span>\r\n            //   )\r\n            // }\r\n          },\r\n          {\r\n            label: '设备名称',\r\n            key: 'Name',\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: '品牌',\r\n            key: 'Brand'\r\n          },\r\n          {\r\n            label: '型号',\r\n            key: 'Type'\r\n          },\r\n          {\r\n            label: '设备类型',\r\n            key: 'EqtType'\r\n          },\r\n          {\r\n            label: '安装位置',\r\n            key: 'Position'\r\n          },\r\n          {\r\n            label: '安装日期',\r\n            key: 'Date'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 200\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init()\r\n  },\r\n  mounted() {\r\n    this.initDeviceType('EqtType', 'EnvironmentEqtType')\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.positionArr = []\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getEquipmentList()\r\n    },\r\n    async init() {\r\n      this.getEquipmentList()\r\n      this.options = await this.getTreeAddress()\r\n      console.log(this.options)\r\n    },\r\n    async getEquipmentList() {\r\n      this.customTableConfig.loading = true\r\n      if (this.positionArr.length > 0) {\r\n        this.ruleForm.Position = this.positionArr.join('/')\r\n      }\r\n      const res = await GetEquipmentList({\r\n        ParameterJson: [\r\n          {\r\n            Key: '',\r\n            Value: [null],\r\n            Type: '',\r\n            Filter_Type: ''\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n\r\n        SortName: '',\r\n        SortOrder: '',\r\n        Search: '',\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        ...this.ruleForm\r\n      })\r\n      this.customTableConfig.loading = false\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n          ...item,\r\n          Date: dayjs(item.Date).format('YYYY-MM-DD')\r\n        }))\r\n        console.log(res)\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.currentComponent = DialogForm\r\n      this.componentsConfig = {\r\n        disabled: false,\r\n        title: '新增'\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row)\r\n      console.log(this)\r\n      this.$confirm(\r\n        '该操作将在监测设备档案中删除该设备信息，请确认是否删除？',\r\n        '删除',\r\n        {\r\n          type: 'error'\r\n        }\r\n      )\r\n        .then(async (_) => {\r\n          const res = await DeleteEquipment({\r\n            IDs: [row.ID]\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.init()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n        .catch((_) => { })\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      if (type === 'view') {\r\n        this.currentComponent = DialogFormLook\r\n        this.dialogTitle = '查看'\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: true,\r\n          title: '查看'\r\n        }\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        this.currentComponent = DialogForm\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: false,\r\n          title: '编辑'\r\n        }\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    // async handleExport() {\r\n    //   const res = await ExportEnvironmentEquipment({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: false,\r\n    //     Ids: this.tableSelection.map((item) => item.ID),\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n\r\n    // v2 版本导出\r\n    async handleAllExport() {\r\n      const res = await ExportEquipmentList({\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        Id: this.tableSelection.map((item) => item.ID).toString(),\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    // 下载模板\r\n    handleDownTemplate() {\r\n      EnvironmentImportTemplate({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data, '环境监测设备档案导出')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n      // if (this.tableSelection.length > 0) {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = false\r\n      // } else {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = true\r\n      // }\r\n    },\r\n    handleClick() {\r\n      this.currentComponent = DialogType\r\n      this.componentsConfig = {\r\n        disabled: false,\r\n        title: '新增'\r\n      }\r\n      this.dialogVisible = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: calc(100vh - 100px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AAEA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,cAAA;AAEA,SAAAC,YAAA;AACA;AACA;;AAEA,SACAC,gBAAA,EACAC,eAAA,EACAC,0BAAA,EACAC,yBAAA,EACAC,0BAAA,EACAC,mBAAA,QACA;AACA,SAAAC,gBAAA;AACA;AACA,OAAAC,KAAA;AACA,OAAAC,YAAA;AACA,OAAAC,UAAA;AACA,OAAAC,aAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAlB,WAAA,EAAAA,WAAA;IACA;IACA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA,YAAA;IACAe,YAAA,EAAAA;EACA;EACAK,MAAA,GAAAP,gBAAA,EAAAG,UAAA,EAAAC,aAAA;EACAI,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,KAAA;QACAC,aAAA;MACA;MACAC,WAAA;MACAC,gBAAA,EAAA3B,UAAA;MACA4B,gBAAA;QACAC,aAAA,EAAArB;MACA;MACAsB,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAZ,KAAA,CAAAa,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAd,KAAA,CAAAa,aAAA;UACAb,KAAA,CAAAe,OAAA;QACA;MACA;MACAF,aAAA;MACAG,WAAA;MACAC,cAAA;MAEAC,QAAA;QACAC,OAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UAAA;UACArB,KAAA;UAAA;UACAsB,IAAA;UAAA;UACAC,YAAA;YACA;YACAC,SAAA;YACAC,WAAA;UACA;UACAC,KAAA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UACArB,KAAA;UACAsB,IAAA;UACAC,YAAA;YACA;YACAC,SAAA;YACAC,WAAA;UACA;UACA3B,OAAA;UACA6B,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UAAA;UACArB,KAAA;UAAA;UACAsB,IAAA;UAAA;UACAC,YAAA;YACA;YACAC,SAAA;YACA;UACA;UACAG,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,EACA;QACAG,KAAA;UACA;QAAA,CACA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAC,IAAA;YACAhB,IAAA;YAAA;YACAiB,IAAA;YAAA;YACAC,OAAA,WAAAA,QAAAC,IAAA;cACAZ,OAAA,CAAAC,GAAA,CAAAW,IAAA;cACA5C,KAAA,CAAA6C,WAAA;YACA;UACA,GACA;YACAJ,IAAA;YACAhB,IAAA;YAAA;YACAiB,IAAA;YAAA;YACAC,OAAA,WAAAA,QAAAC,IAAA;cACAZ,OAAA,CAAAC,GAAA,CAAAW,IAAA;cACA5C,KAAA,CAAA8C,YAAA;YACA;UACA,GACA;YACAL,IAAA;YACAM,QAAA;YAAA;YACAJ,OAAA,WAAAA,QAAAC,IAAA;cACAZ,OAAA,CAAAC,GAAA,CAAAW,IAAA;cACA5C,KAAA,CAAAgD,kBAAA;YACA;UACA,GACA;YACAP,IAAA;YACAM,QAAA;YAAA;YACAJ,OAAA,WAAAA,QAAAC,IAAA;cACAZ,OAAA,CAAAC,GAAA,CAAAW,IAAA;cACA5C,KAAA,CAAAQ,gBAAA;cACAR,KAAA,CAAAa,aAAA;cACAb,KAAA,CAAAgB,WAAA;YACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;YACAyB,IAAA;YACAE,OAAA,WAAAA,QAAAC,IAAA;cACAZ,OAAA,CAAAC,GAAA,CAAAW,IAAA;cACA5C,KAAA,CAAAiD,eAAA;YACA;UACA;QAEA;QACA;QACAC,OAAA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA,GACA;UACA1B,KAAA;UACAH,YAAA;YACAD,IAAA;YACA+B,KAAA;UACA;QACA,GACA;UACA3B,KAAA;UACA1B,KAAA;UACAuB,YAAA;YACAD,IAAA;YACA+B,KAAA;UACA;UACA;UACA;UACA;UACA;UACA;QACA,GACA;UACArD,KAAA;UACAqB,GAAA;UACAE,YAAA;YACA+B,KAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA,GACA;UACAtD,KAAA;UACAqB,GAAA;UACAE,YAAA;YACA+B,KAAA;UACA;QACA,GACA;UACAtD,KAAA;UACAqB,GAAA;QACA,GACA;UACArB,KAAA;UACAqB,GAAA;QACA,GACA;UACArB,KAAA;UACAqB,GAAA;QACA,GACA;UACArB,KAAA;UACAqB,GAAA;QACA,GACA;UACArB,KAAA;UACAqB,GAAA;QACA,EACA;QACAkC,SAAA;QACAC,cAAA;UACA9B,KAAA;QACA;QACA+B,YAAA,GACA;UACAC,WAAA;UACAnC,YAAA;YACAD,IAAA;UACA;UACAkB,OAAA,WAAAA,QAAAmB,KAAA,EAAAC,GAAA;YACA/D,KAAA,CAAAgE,UAAA,CAAAF,KAAA,EAAAC,GAAA;UACA;QACA,GACA;UACAF,WAAA;UACAnC,YAAA;YACAD,IAAA;UACA;UACAkB,OAAA,WAAAA,QAAAmB,KAAA,EAAAC,GAAA;YACA/D,KAAA,CAAAgE,UAAA,CAAAF,KAAA,EAAAC,GAAA;UACA;QACA,GACA;UACAF,WAAA;UACAnC,YAAA;YACAD,IAAA;UACA;UACAkB,OAAA,WAAAA,QAAAmB,KAAA,EAAAC,GAAA;YACA/D,KAAA,CAAAiE,YAAA,CAAAH,KAAA,EAAAC,GAAA;UACA;QACA;MAEA;IACA;EACA;EACAG,QAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAAzE,IAAA;MACAiC,OAAA,CAAAC,GAAA,CAAAlC,IAAA;MACA,KAAAuC,iBAAA,CAAAc,WAAA;MACA,KAAArC,OAAA;IACA;IACA0D,SAAA,WAAAA,UAAA;MACA,KAAAlE,WAAA;MACA,KAAAQ,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAA2D,gBAAA;IACA;IACAN,IAAA,WAAAA,KAAA;MAAA,IAAAO,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAT,MAAA,CAAAD,gBAAA;cAAAQ,QAAA,CAAAE,IAAA;cAAA,OACAT,MAAA,CAAAU,cAAA;YAAA;cAAAV,MAAA,CAAA1E,OAAA,GAAAiF,QAAA,CAAAI,IAAA;cACAtD,OAAA,CAAAC,GAAA,CAAA0C,MAAA,CAAA1E,OAAA;YAAA;YAAA;cAAA,OAAAiF,QAAA,CAAAK,IAAA;UAAA;QAAA,GAAAR,OAAA;MAAA;IACA;IACAL,gBAAA,WAAAA,iBAAA;MAAA,IAAAc,MAAA;MAAA,OAAAZ,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAW,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAAb,mBAAA,GAAAG,IAAA,UAAAW,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAT,IAAA,GAAAS,SAAA,CAAAR,IAAA;YAAA;cACAI,MAAA,CAAAlD,iBAAA,CAAAY,OAAA;cACA,IAAAsC,MAAA,CAAAjF,WAAA,CAAAsF,MAAA;gBACAL,MAAA,CAAAtE,QAAA,CAAAG,QAAA,GAAAmE,MAAA,CAAAjF,WAAA,CAAAuF,IAAA;cACA;cAAAF,SAAA,CAAAR,IAAA;cAAA,OACAnG,gBAAA,CAAA8G,aAAA;gBACAC,aAAA,GACA;kBACAC,GAAA;kBACAC,KAAA;kBACAC,IAAA;kBACAC,WAAA;gBACA,EACA;gBACAC,IAAA,EAAAb,MAAA,CAAAlD,iBAAA,CAAAc,WAAA;gBACAkD,QAAA,EAAAd,MAAA,CAAAlD,iBAAA,CAAAe,QAAA;gBAEAkD,QAAA;gBACAC,SAAA;gBACAC,MAAA;gBACAtF,OAAA;gBACAC,OAAA;gBACAC,QAAA;gBACAqF,KAAA;cAAA,GACAlB,MAAA,CAAAtE,QAAA,CACA;YAAA;cApBAwE,GAAA,GAAAE,SAAA,CAAAN,IAAA;cAqBAE,MAAA,CAAAlD,iBAAA,CAAAY,OAAA;cACA,IAAAwC,GAAA,CAAAiB,SAAA;gBACAnB,MAAA,CAAAlD,iBAAA,CAAAoB,SAAA,GAAAgC,GAAA,CAAAkB,IAAA,CAAAA,IAAA,CAAAC,GAAA,WAAAjE,IAAA;kBAAA,OAAAmD,aAAA,CAAAA,aAAA,KACAnD,IAAA;oBACAkE,IAAA,EAAAtH,KAAA,CAAAoD,IAAA,CAAAkE,IAAA,EAAAC,MAAA;kBAAA;gBAAA,CACA;gBACA/E,OAAA,CAAAC,GAAA,CAAAyD,GAAA;gBACAF,MAAA,CAAAlD,iBAAA,CAAAgB,KAAA,GAAAoC,GAAA,CAAAkB,IAAA,CAAAI,UAAA;cACA;gBACAxB,MAAA,CAAAyB,QAAA,CAAAC,KAAA,CAAAxB,GAAA,CAAAyB,OAAA;cACA;YAAA;YAAA;cAAA,OAAAvB,SAAA,CAAAL,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IACA3C,YAAA,WAAAA,aAAA;MACA,KAAA9B,WAAA;MACA,KAAAR,gBAAA,GAAA3B,UAAA;MACA,KAAA4B,gBAAA;QACAsC,QAAA;QACAqE,KAAA;MACA;MACA,KAAAvG,aAAA;IACA;IACAoD,YAAA,WAAAA,aAAAH,KAAA,EAAAC,GAAA;MAAA,IAAAsD,MAAA;MACArF,OAAA,CAAAC,GAAA,CAAA6B,KAAA,EAAAC,GAAA;MACA/B,OAAA,CAAAC,GAAA;MACA,KAAAqF,QAAA,CACA,gCACA,MACA;QACA7F,IAAA;MACA,CACA,EACA8F,IAAA;QAAA,IAAAC,IAAA,GAAA5C,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA2C,SAAAC,CAAA;UAAA,IAAAhC,GAAA;UAAA,OAAAb,mBAAA,GAAAG,IAAA,UAAA2C,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAzC,IAAA,GAAAyC,SAAA,CAAAxC,IAAA;cAAA;gBAAAwC,SAAA,CAAAxC,IAAA;gBAAA,OACAlG,eAAA;kBACA2I,GAAA,GAAA9D,GAAA,CAAA+D,EAAA;gBACA;cAAA;gBAFApC,GAAA,GAAAkC,SAAA,CAAAtC,IAAA;gBAGA,IAAAI,GAAA,CAAAiB,SAAA;kBACAU,MAAA,CAAAjD,IAAA;gBACA;kBACAiD,MAAA,CAAAJ,QAAA,CAAAC,KAAA,CAAAxB,GAAA,CAAAyB,OAAA;gBACA;cAAA;cAAA;gBAAA,OAAAS,SAAA,CAAArC,IAAA;YAAA;UAAA,GAAAkC,QAAA;QAAA,CACA;QAAA,iBAAAM,EAAA;UAAA,OAAAP,IAAA,CAAAQ,KAAA,OAAAC,SAAA;QAAA;MAAA,KACAC,KAAA,WAAAR,CAAA;IACA;IACA1D,UAAA,WAAAA,WAAAF,KAAA,EAAAC,GAAA,EAAAtC,IAAA;MACAO,OAAA,CAAAC,GAAA,CAAA6B,KAAA,EAAAC,GAAA,EAAAtC,IAAA;MACA,IAAAA,IAAA;QACA,KAAAjB,gBAAA,GAAAzB,cAAA;QACA,KAAAiC,WAAA;QACA,KAAAP,gBAAA;UACAqH,EAAA,EAAA/D,GAAA,CAAA+D,EAAA;UACA/E,QAAA;UACAqE,KAAA;QACA;MACA,WAAA3F,IAAA;QACA,KAAAT,WAAA;QACA,KAAAR,gBAAA,GAAA3B,UAAA;QACA,KAAA4B,gBAAA;UACAqH,EAAA,EAAA/D,GAAA,CAAA+D,EAAA;UACA/E,QAAA;UACAqE,KAAA;QACA;MACA;MACA,KAAAvG,aAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACAoC,eAAA,WAAAA,gBAAA;MAAA,IAAAkF,MAAA;MAAA,OAAAvD,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAsD,SAAA;QAAA,IAAA1C,GAAA;QAAA,OAAAb,mBAAA,GAAAG,IAAA,UAAAqD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnD,IAAA,GAAAmD,SAAA,CAAAlD,IAAA;YAAA;cAAAkD,SAAA,CAAAlD,IAAA;cAAA,OACA9F,mBAAA,CAAAyG,aAAA;gBACA5E,OAAA;gBACAC,OAAA;gBACAC,QAAA;gBACAqF,KAAA;gBACA6B,EAAA,EAAAJ,MAAA,CAAAlH,cAAA,CAAA4F,GAAA,WAAAjE,IAAA;kBAAA,OAAAA,IAAA,CAAAkF,EAAA;gBAAA,GAAAU,QAAA;cAAA,GACAL,MAAA,CAAAjH,QAAA,CACA;YAAA;cAPAwE,GAAA,GAAA4C,SAAA,CAAAhD,IAAA;cAQA,IAAAI,GAAA,CAAAiB,SAAA;gBACA3E,OAAA,CAAAC,GAAA,CAAAyD,GAAA;gBACA1G,YAAA,CAAA0G,GAAA,CAAAkB,IAAA;cACA;gBACAuB,MAAA,CAAAlB,QAAA,CAAAC,KAAA,CAAAxB,GAAA,CAAAyB,OAAA;cACA;YAAA;YAAA;cAAA,OAAAmB,SAAA,CAAA/C,IAAA;UAAA;QAAA,GAAA6C,QAAA;MAAA;IACA;IACA;IACApF,kBAAA,WAAAA,mBAAA;MAAA,IAAAyF,MAAA;MACArJ,yBAAA,KAAAmI,IAAA,WAAA7B,GAAA;QACA,IAAAA,GAAA,CAAAiB,SAAA;UACA3H,YAAA,CAAA0G,GAAA,CAAAkB,IAAA;QACA;UACA6B,MAAA,CAAAxB,QAAA,CAAAC,KAAA,CAAAxB,GAAA,CAAAyB,OAAA;QACA;MACA;IACA;IACAuB,gBAAA,WAAAA,iBAAAC,GAAA;MACA3G,OAAA,CAAAC,GAAA,iBAAA2G,MAAA,CAAAD,GAAA;MACA,KAAArG,iBAAA,CAAAe,QAAA,GAAAsF,GAAA;MACA,KAAAvE,IAAA;IACA;IACAyE,mBAAA,WAAAA,oBAAAF,GAAA;MACA3G,OAAA,CAAAC,GAAA,wBAAA2G,MAAA,CAAAD,GAAA;MACA,KAAArG,iBAAA,CAAAc,WAAA,GAAAuF,GAAA;MACA,KAAAvE,IAAA;IACA;IACA0E,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA9H,cAAA,GAAA8H,SAAA;MACA;MACA;MACA;MACA;MACA;IACA;IACAlG,WAAA,WAAAA,YAAA;MACA,KAAArC,gBAAA,GAAA1B,UAAA;MACA,KAAA2B,gBAAA;QACAsC,QAAA;QACAqE,KAAA;MACA;MACA,KAAAvG,aAAA;IACA;EACA;AACA", "ignoreList": []}]}