<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
// import getGridByCode from "../../safetyManagement/mixins/index";
// import DialogForm from "./dialogForm.vue";

import { downloadFile } from '@/utils/downloadFile'
import dayjs from 'dayjs'
import {
  GetWarningPageList,
  SetWarningStatus,
  GetTypesByModule,
  GetModule
} from '@/api/business/eventManagement'
export default {
  name: '',
  components: {
    CustomTable,
    CustomForm,
    CustomLayout
  },
  data() {
    return {
      currentComponent: null,
      componentsConfig: {
        Data: {}
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '编辑',
      tableSelection: [],
      ruleForm: {
        Module: '',
        WarningType: '',
        WarningName: '',
        Status: '0',
        Date: [],
        WarningBeg: null,
        WarningEnd: null
      },
      customForm: {
        formItems: [
          {
            key: 'Module',
            label: '业务模块',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
              this.GetTypesByModule()
            }
          },
          {
            key: 'WarningType',
            label: '告警类型',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'WarningName',
            label: '告警名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'Status',
            label: '状态',
            type: 'select',
            options: [
              {
                label: '全部',
                value: '0'
              },
              {
                label: '告警中',
                value: '1'
              },
              {
                label: '已关闭',
                value: '2'
              }
              // {
              //   label: "已处理",
              //   value: "3",
              // },
            ],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'Date', // 字段ID
            label: '告警时间', // Form的label
            type: 'datePicker', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              type: 'daterange',
              disabled: false,
              placeholder: '请输入...'
            },
            change: (e) => {
              // change事件
              console.log(e)
              this.ruleForm.WarningBeg = dayjs(e[0]).format('YYYY-MM-DD')
              this.ruleForm.WarningEnd = dayjs(e[1]).format('YYYY-MM-DD')
            }
          }
        ],
        rules: {},
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        loading: false,
        buttonConfig: {
          buttonList: [
            {
              text: '批量关闭',
              onclick: (item) => {
                console.log(item)
                this.handleClose()
              }
            }
          ]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        height: '100%',
        tableColumns: [
          {
            otherOptions: {
              type: 'selection',
              align: 'center',
              fixed: 'left'
            }
          },
          {
            label: '告警时间',
            key: 'WarningTime',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '告警事件名称',
            key: 'Name',
            width: 140,
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '告警编号',
            key: 'BusinessNo',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '告警类型',
            key: 'WarningType',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '来源',
            key: 'SourceName',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '业务模块',
            key: 'Module',
            otherOptions: {
              align: 'center'
            }
          },

          {
            label: '状态',
            key: 'StatusDisplay',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '通知人员',
            key: 'ActualReceiveUsersNames',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '通知方式',
            key: 'MessageNoticeModeDisplay',
            width: 180,
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '操作人员',
            key: 'ModifyUserName',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '操作时间',
            key: 'ModifyDate',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '处理内容',
            key: 'DoneContent',
            otherOptions: {
              align: 'center'
            }
          }
        ],
        tableData: [],
        operateOptions: {
          align: 'center',
          width: '180'
        },
        tableActions: [
          {
            actionLabel: '查看详情',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              console.log(row, 'row')
              const platform = 'digitalfactory'
              const code = 'szgc'
              const id = '97b119f9-e634-4d95-87b0-df2433dc7893'
              let url = ''
              if (row.Module == '能耗管理') {
                url = '/business/energy/alarmDetail'
              } else if (row.Module == '车辆道闸') {
                url = '/bussiness/vehicle/alarm-info'
              } else if (row.Module == '门禁管理') {
                url = '/business/AccessControlAlarmDetails'
              } else if (row.Module == '安防管理') {
                url = '/business/equipmentAlarm'
              } else if (row.Module == '危化品管理') {
                url = '/business/hazchem/alarmInformation'
              } else if (row.Module == '环境管理') {
                url = '/business/environment/alarmInformation'
              } else if (row.Module == '访客管理') {
                // url = "/business/energy/alarmDetail";
                console.log('访客管理')
              }
              this.$qiankun.switchMicroAppFn(platform, code, id, url)
            }
          }
          // {
          //   actionLabel: "编辑",
          //   otherOptions: {
          //     type: "text",
          //   },
          //   onclick: (index, row) => {
          //     this.handleEdit(row);
          //   },
          // },
        ]
      }
    }
  },
  computed: {},
  created() {
    this.init()

    this.GetModule()
  },
  // mixins: [getGridByCode],
  methods: {
    async handleClose() {
      const res = await SetWarningStatus({
        Status: '2',
        Ids: this.tableSelection.map((item) => item.Id)
      })
      if (res.IsSucceed) {
        this.$message.success('操作成功')
        this.onFresh()
      }
    },
    async GetTypesByModule() {
      const res = await GetTypesByModule({
        Type: '2',
        Module: this.ruleForm.Module
      })
      console.log(res, 'res')
      if (res.IsSucceed) {
        const result = res.Data || []
        const typeList = result.map((item) => ({
          value: item,
          label: item
        }))
        this.customForm.formItems.find(
          (item) => item.key == 'WarningType'
        ).options = typeList
      }
    },
    async GetModule() {
      const res = await GetModule({})
      console.log(res, 'res')
      if (res.IsSucceed) {
        const result = res.Data || []
        const moduleList = result.map((item) => ({
          value: item,
          label: item
        }))
        console.log(moduleList, 'moduleList')
        this.customForm.formItems.find((item) => item.key == 'Module').options =
          [
            {
              value: '',
              label: '全部'
            },
            ...moduleList
          ]
      }
    },

    searchForm(data) {
      console.log(data)
      this.customTableConfig.currentPage = 1
      this.onFresh()
    },
    resetForm() {
      this.ruleForm.WarningBeg = null
      this.ruleForm.WarningEnd = null
      this.ruleForm.Date = null

      this.onFresh()
    },
    onFresh() {
      this.GetWarningPageList()
    },

    init() {
      // this.getGridByCode("AccessControlAlarmDetails1");
      this.GetWarningPageList()
    },
    async GetWarningPageList() {
      this.customTableConfig.loading = true
      // delete this.ruleForm.Date;
      const res = await GetWarningPageList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm
      }).finally(() => {
        this.customTableConfig.loading = false
      })
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data
        this.customTableConfig.total = res.Data.TotalCount
      } else {
        this.$message.error(res.Message)
      }
    },
    async handleExport() {
      const res = await ExportEntranceWarning({
        id: this.tableSelection.map((item) => item.Id).toString(),
        code: 'AccessControlAlarmDetails1'
      })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '告警明细数据')
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.GetWarningPageList()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.GetWarningPageList()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
    },
    handleEdit(row) {
      this.dialogVisible = true
      this.componentsConfig.Data = row
    }
  }
}
</script>

<style lang="scss" scoped>

.layout {
  height: 100%;
  width: 100%;
  position: absolute;
  ::v-deep {
    .CustomLayout {
      .layoutTable {
        height: 0;
        .CustomTable {
          height: 100%;
          display: flex;
          flex-direction: column;
          .table {
            flex: 1;
            height: 0;
            display: flex;
            flex-direction: column;
            .el-table {
              flex: 1;
              height: 0;
            }
          }
        }
      }
    }
  }
}
</style>
