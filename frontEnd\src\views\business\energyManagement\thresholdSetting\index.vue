<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      top="6vh"
      :destroy-on-close="true"
    >
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        ref="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import DialogForm from './dialogForm.vue'
import {
  DelThresholdSetting,
  GetPageList,
  GetDictionaryDetailListByCode
} from '@/api/business/energyManagement'
export default {
  name: 'MonitorData',
  components: {
    CustomTable,
    // CustomButton,
    // CustomTitle,
    CustomForm,
    CustomLayout
  },
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {},
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.init()
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      tableSelection: [],

      ruleForm: {
        NodeId: '',
        DeviceTypeId: ''
      },
      customForm: {
        formItems: [
          {
            key: 'NodeId', // 字段ID
            label: '统计节点', // Form的label
            type: 'select',
            options: [], // 类型数据列表
            otherOptions: {
              clearable: true
            },
            width: '240px',
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'DeviceTypeId',
            label: '点表类型',
            type: 'select',
            placeholder: '请选择点表类型',
            options: [], // 类型数据列表
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          }
        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: '新增',
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: false, // 是否禁用
              icon: '', //  图标
              autofocus: false, // 是否聚焦
              type: 'primary', // primary / success / warning / danger / info / text
              size: 'small', // medium / small / mini
              onclick: (item) => {
                console.log(item)
                this.handleCreate()
              }
            }
          ]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            label: '统计节点',
            key: 'NodeName'
          },
          {
            label: '点表类型',
            key: 'DeviceTypeName'
          },
          {
            label: '告警提示',
            key: 'Prompt'
          },
          {
            label: '告警类型',
            key: 'WarningTypeName'
          },
          {
            label: '条件',
            key: 'ConditionName'
          },
          {
            label: '阈值',
            key: 'Threshold'
          },
          {
            label: '比对项',
            key: 'ContrastName'
          }
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: '编辑',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'edit')
            }
          },
          {
            actionLabel: '删除',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleDelete(index, row)
            }
          }
        ]
      }
    }
  },
  computed: {},
  created() {
    this.getBaseData()
    this.init()
  },
  methods: {
    getBaseData() {
      // 获取点表类型
      GetDictionaryDetailListByCode({ dictionaryCode: 'PointTableType' }).then(
        (res) => {
          if (res.IsSucceed) {
            const data = res.Data.map((item) => {
              return {
                label: item.Display_Name,
                value: item.Id
              }
            })
            this.customForm.formItems[1].options = data
          } else {
            this.$message({
              type: 'error',
              data: res.Message
            })
          }
        }
      )
      // 获取统计节点
      GetDictionaryDetailListByCode({ dictionaryCode: 'EnergyNode' }).then(
        (res) => {
          if (res.IsSucceed) {
            const data = res.Data.map((item) => {
              return {
                label: item.Display_Name,
                value: item.Id
              }
            })
            this.customForm.formItems[0].options = data
          } else {
            this.$message({
              type: 'error',
              data: res.Message
            })
          }
        }
      )
    },
    searchForm(data) {
      this.customTableConfig.currentPage = 1
      this.init()
    },
    resetForm() {
      this.init()
    },
    init() {
      this.getDataList()
    },
    async getDataList() {
      const res = await GetPageList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data
        this.customTableConfig.total = res.Data.Total
      } else {
        this.$message({
          type: 'error',
          message: res.Message
        })
      }
    },
    handleCreate() {
      this.dialogTitle = '新增'
      this.componentsConfig = {
        type: 'add'
      }
      this.dialogVisible = true
    },
    handleEdit(index, row, type) {
      console.log(index, row, type)
      if (type === 'edit') {
        this.dialogTitle = '编辑'
        this.componentsConfig = { ...row, type }
      }
      this.dialogVisible = true
    },
    async handleDelete(index, row) {
      const res = await DelThresholdSetting({
        Id: row.Id
      })
      if (res.IsSucceed) {
        this.init()
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      }
    },

    handleSizeChange(val) {
      this.customTableConfig.pageSize = val
      this.init()
    },
    handleCurrentChange(val) {
      this.customTableConfig.currentPage = val
      this.init()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
    }
  }
}
</script>
<style lang="scss" scoped>
.thresholdSetting {
  overflow: hidden;
}
</style>

