{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\szcjPJEquipmentAssetList\\index.vue?vue&type=style&index=0&id=695d9b4e&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\szcjPJEquipmentAssetList\\index.vue", "mtime": 1755674552420}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouc3pjalBKRXF1aXBtZW50QXNzZXRMaXN0IHsNCiAgLy8gcGFkZGluZzogMTBweCAxNXB4Ow0KICAvLyBoZWlnaHQ6IGNhbGMoMTAwdmggLSA5MHB4KTsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCg0KICAudHJlZV9jYXJkIHsNCiAgICA6OnYtZGVlcCAuZWwtY2FyZF9fYm9keSB7DQogICAgICBwYWRkaW5nOiAxMHB4IDBweCAxMHB4IDBweCAhaW1wb3J0YW50Ow0KICAgIH0NCiAgICA6OnYtZGVlcCAuZWwtdHJlZS1ub2RlX19sYWJlbCB7DQogICAgICB0ZXh0LXdyYXA6IHdyYXAgIWltcG9ydGFudDsNCiAgICAgIHBhZGRpbmc6IDZweCAhaW1wb3J0YW50Ow0KICAgIH0NCiAgICA6OnYtZGVlcCAuZWwtdHJlZS1ub2RlX19jb250ZW50IHsNCiAgICAgIG1pbi1oZWlnaHQ6IDMycHggIWltcG9ydGFudDsNCiAgICAgIGhlaWdodDogYXV0byAhaW1wb3J0YW50Ow0KICAgIH0NCiAgfQ0KDQogIC5wb3NpdGlvblRyZWVDbGFzczo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICAgIHdpZHRoOiA1cHg7DQogICAgaGVpZ2h0OiAxcHg7DQogIH0NCiAgLnBvc2l0aW9uVHJlZUNsYXNzOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7DQogICAgLyrmu5rliqjmnaHph4zpnaLlsI/mlrnlnZcqLw0KICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7DQogICAgLy8gLXdlYmtpdC1ib3gtc2hhZG93OiBpbnNldCAwIDAgNXB4IHJnYmEoNzksIDEwNCwgMTQ1LCAwLjM1KTsNCiAgICBiYWNrZ3JvdW5kOiAjZjBmMmY3Ow0KICB9DQogIC5wb3NpdGlvblRyZWVDbGFzczo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgew0KICAgIC8q5rua5Yqo5p2h6YeM6Z2i6L2o6YGTKi8NCiAgICAvLyAtd2Via2l0LWJveC1zaGFkb3c6IGluc2V0IDAgMCA1cHggcmdiYSg3OSwgMTA0LCAxNDUsIDAuMzUpOw0KICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7DQogICAgLy8gYmFja2dyb3VuZDogcmdiYSg3OSwgMTA0LCAxNDUsIDAuMzUpOw0KICB9DQoNCiAgLmxpc3RfY2xhc3M6Oi13ZWJraXQtc2Nyb2xsYmFyIHsNCiAgICB3aWR0aDogNXB4Ow0KICAgIGhlaWdodDogMXB4Ow0KICB9DQogIC5saXN0X2NsYXNzOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7DQogICAgLyrmu5rliqjmnaHph4zpnaLlsI/mlrnlnZcqLw0KICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7DQogICAgLy8gLXdlYmtpdC1ib3gtc2hhZG93OiBpbnNldCAwIDAgNXB4IHJnYmEoNzksIDEwNCwgMTQ1LCAwLjM1KTsNCiAgICAvLyBiYWNrZ3JvdW5kOiAjZjBmMmY3Ow0KICB9DQogIC5saXN0X2NsYXNzOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7DQogICAgLyrmu5rliqjmnaHph4zpnaLovajpgZMqLw0KICAgIC8vIC13ZWJraXQtYm94LXNoYWRvdzogaW5zZXQgMCAwIDVweCByZ2JhKDc5LCAxMDQsIDE0NSwgMC4zNSk7DQogICAgYm9yZGVyLXJhZGl1czogMTBweDsNCiAgICAvLyBiYWNrZ3JvdW5kOiByZ2JhKDc5LCAxMDQsIDE0NSwgMC4zNSk7DQogIH0NCg0KICAuY2FyZF9jb250ZW50IHsNCiAgICBoZWlnaHQ6IDEzMHB4Ow0KICAgIHBhZGRpbmc6IDBweCAyNHB4Ow0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgLmxlZnQgew0KICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nIFNDLCBQaW5nRmFuZyBTQzsNCiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICBmb250LXNpemU6IDIwcHg7DQogICAgICBjb2xvcjogIzdmOGNhMjsNCiAgICAgIGZvbnQtc3R5bGU6IG5vcm1hbDsNCiAgICAgIHRleHQtdHJhbnNmb3JtOiBub25lOw0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAubnVtIHsNCiAgICAgICAgZm9udC1mYW1pbHk6IEhlbHZldGljYSwgSGVsdmV0aWNhOw0KICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgZm9udC1zaXplOiA0MHB4Ow0KICAgICAgICBjb2xvcjogIzI5OGRmZjsNCiAgICAgICAgZm9udC1zdHlsZTogbm9ybWFsOw0KICAgICAgICB0ZXh0LXRyYW5zZm9ybTogbm9uZTsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgICAucmlnaHQgew0KICAgICAgaW1nIHsNCiAgICAgICAgd2lkdGg6IDY0cHg7DQogICAgICAgIGhlaWdodDogNjRweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLmxpc3Rfbm9fYm94IHsNCiAgICBwYWRkaW5nOiA1MHB4IDEwcHg7DQogICAgaGVpZ2h0OiAxMDAlOw0KICAgIC5ub19jb250ZW50IHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIGltZyB7DQogICAgICAgIHdpZHRoOiAzMDBweDsNCiAgICAgICAgaGVpZ2h0OiBhdXRvOw0KICAgICAgfQ0KICAgICAgc3BhbiB7DQogICAgICAgIG1hcmdpbi10b3A6IDEwcHg7DQogICAgICAgIGZvbnQtZmFtaWx5OiBNaWNyb3NvZnQgWWFIZWksIE1pY3Jvc29mdCBZYUhlaTsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBjb2xvcjogI2MyY2JlMjsNCiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgICBmb250LXN0eWxlOiBub3JtYWw7DQogICAgICAgIHRleHQtdHJhbnNmb3JtOiBub25lOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5saXN0X2JveCB7DQogICAgd2lkdGg6IDEwMCU7DQogICAgLy8gaGVpZ2h0OiAxMDAlOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC13cmFwOiB3cmFwOw0KICAgIGRpc3BsYXk6IGdyaWQ7DQogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoNSwgY2FsYygyMCUgLSAxMHB4KSk7DQogICAgZ3JpZC1jb2x1bW4tZ2FwOiAxMHB4Ow0KICAgIGdyaWQtcm93LWdhcDogMTBweDsNCg0KICAgIC5saXN0X2l0ZW0gew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgICAgIC8vIG1hcmdpbi1yaWdodDogMTBweDsNCiAgICAgIC8vIG1hcmdpbi1ib3R0b206IDEwcHg7DQogICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICAubGlzdF9sb2dvIHsNCiAgICAgICAgaGVpZ2h0OiAxNDBweDsNCiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjJmNzsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICAgIC5lbC1pbWFnZSB7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgICB9DQogICAgICB9DQogICAgICAubGlzdF9pbmZvIHsNCiAgICAgICAgbWFyZ2luLXRvcDogMTBweDsNCiAgICAgICAgLy8gY3Vyc29yOiBwb2ludGVyOw0KICAgICAgfQ0KICAgICAgLnRpdGxlIHsNCiAgICAgICAgcGFkZGluZzogNnB4IDEwcHg7DQogICAgICAgIGZvbnQtZmFtaWx5OiBNaWNyb3NvZnQgWWFIZWksIE1pY3Jvc29mdCBZYUhlaTsNCiAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgY29sb3I6ICM2NjY2NjY7DQogICAgICAgIGZvbnQtc3R5bGU6IG5vcm1hbDsNCiAgICAgICAgdGV4dC10cmFuc2Zvcm06IG5vbmU7DQogICAgICB9DQogICAgICAuaW5mbyB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIHBhZGRpbmc6IDBweCAxMHB4Ow0KICAgICAgICBtYXJnaW4tdG9wOiA2cHg7DQogICAgICAgIC5sYWJlbCB7DQogICAgICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nIFNDLCBQaW5nRmFuZyBTQzsNCiAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICBjb2xvcjogIzk5OTk5OTsNCiAgICAgICAgICBmb250LXN0eWxlOiBub3JtYWw7DQogICAgICAgICAgdGV4dC10cmFuc2Zvcm06IG5vbmU7DQogICAgICAgICAgbWFyZ2luLXJpZ2h0OiA2cHg7DQogICAgICAgIH0NCiAgICAgICAgLnZhbHVlIHsNCiAgICAgICAgICBmb250LWZhbWlseTogSGVsdmV0aWNhLCBIZWx2ZXRpY2E7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgY29sb3I6ICM2NjY2NjY7DQogICAgICAgICAgZm9udC1zdHlsZTogbm9ybWFsOw0KICAgICAgICAgIHRleHQtdHJhbnNmb3JtOiBub25lOw0KICAgICAgICB9DQogICAgICB9DQogICAgICAuYWN0aW9uIHsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgICBwYWRkaW5nOiAwcHggMTBweDsNCiAgICAgICAgbWFyZ2luLXRvcDogNnB4Ow0KICAgICAgICBtYXJnaW4tYm90dG9tOiA4cHg7DQogICAgICAgIC50YWdzIHsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7DQogICAgICAgICAgLnRhZ3NfaXRlbSB7DQogICAgICAgICAgICBmb250LWZhbWlseTogUGluZ0ZhbmcgU0MsIFBpbmdGYW5nIFNDOw0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTFweDsNCiAgICAgICAgICAgIHBhZGRpbmc6IDFweCAycHg7DQogICAgICAgICAgICBmb250LXN0eWxlOiBub3JtYWw7DQogICAgICAgICAgICB0ZXh0LXRyYW5zZm9ybTogbm9uZTsNCiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogNnB4Ow0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMnB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAucmlnaHQgew0KICAgICAgICAgIGNvbG9yOiAjMjk4ZGZmOw0KICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDsNCiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAuY2FyZF9zZWNvbmRfY29udGVudCB7DQogICAgaGVpZ2h0OiAzOXB4Ow0KICAgIHBhZGRpbmc6IDBweCAzMHB4Ow0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgLmxlZnQgew0KICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nIFNDLCBQaW5nRmFuZyBTQzsNCiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICBmb250LXN0eWxlOiBub3JtYWw7DQogICAgICB0ZXh0LXRyYW5zZm9ybTogbm9uZTsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgLnRleHRJbmZvIHsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgICAgLnRleHRTdHlsZSB7DQogICAgICAgICAgbWFyZ2luLXRvcDogOHB4Ow0KICAgICAgICAgIGNvbG9yOiAjOTk5OTk5Ow0KICAgICAgICB9DQogICAgICB9DQogICAgICAucG9waW5mbyB7DQogICAgICAgIHdpZHRoOiAxNXB4Ow0KICAgICAgICBoZWlnaHQ6IDE1cHg7DQogICAgICAgIG1hcmdpbi1sZWZ0OiAyMHB4Ow0KICAgICAgfQ0KICAgICAgLm51bSB7DQogICAgICAgIGZvbnQtZmFtaWx5OiBIZWx2ZXRpY2EsIEhlbHZldGljYTsNCiAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgIGZvbnQtc2l6ZTogMzJweDsNCiAgICAgICAgZm9udC1zdHlsZTogbm9ybWFsOw0KICAgICAgICB0ZXh0LXRyYW5zZm9ybTogbm9uZTsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICAgICAgICBtaW4td2lkdGg6IDMwcHg7DQogICAgICB9DQogICAgfQ0KICAgIC5yaWdodCB7DQogICAgICBpbWcgew0KICAgICAgICB3aWR0aDogMzZweDsNCiAgICAgICAgaGVpZ2h0OiAzNnB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KICA6OnYtZGVlcCAuZWwtY2FyZF9fYm9keSB7DQogICAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7DQogIH0NCiAgOjp2LWRlZXAgLmVsLWNhcmRfX2hlYWRlciB7DQogICAgYm9yZGVyLWJvdHRvbTogbm9uZSAhaW1wb3J0YW50Ow0KICB9DQogIDo6di1kZWVwIC5lbC1wcm9ncmVzc19fdGV4dCB7DQogICAgZm9udC1zaXplOiAxOHB4ICFpbXBvcnRhbnQ7DQogICAgY29sb3I6ICM2NjY2NjYgIWltcG9ydGFudDsNCiAgfQ0KICA6OnYtZGVlcC5lbC10YWJsZSAucm93LW9uZSB7DQogICAgYmFja2dyb3VuZDogcmdiYSg0MSwgMTQxLCAyNTUsIDAuMDMpICFpbXBvcnRhbnQ7DQogIH0NCg0KICA6OnYtZGVlcCAuZWwtdGFibGUgLnJvdy10d28gew0KICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMSkgIWltcG9ydGFudDsNCiAgfQ0KDQogIDo6di1kZWVwIC5lbC1yYWRpby1idXR0b25fX2lubmVyIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmOw0KICAgIGhlaWdodDogMzJweDsNCiAgICB3aWR0aDogODBweDsNCiAgICBmb250LXNpemU6IDE0cHg7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAitBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/equipmentManagement/szcjPJEquipmentAssetList", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 szcjPJEquipmentAssetList\">\r\n    <el-row :gutter=\"12\">\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"never\">\r\n          <div class=\"card_content\">\r\n            <div class=\"left\">\r\n              <span class=\"num\">{{ getDeviceValue(\"设备总数\") }}</span>\r\n              <span>设备总数 </span>\r\n            </div>\r\n            <div class=\"right\">\r\n              <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #4ebf8b\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"在线\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <span>在线 </span>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"在线\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #1bb5e0\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"正常\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <span>正常 </span>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"正常\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #68748a\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"离线\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <span>离线 </span>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"离线\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #ff5f7a\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"故障\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <div>\r\n                      <span>故障 </span>\r\n                      <el-popover\r\n                        placement=\"top-start\"\r\n                        title=\"\"\r\n                        width=\"\"\r\n                        trigger=\"hover\"\r\n                        content=\"对设备发起报修单记为设备故障\"\r\n                      >\r\n                        <img\r\n                          slot=\"reference\"\r\n                          class=\"popinfo\"\r\n                          src=\"@/assets/<EMAIL>\"\r\n                          alt=\"\"\r\n                        >\r\n                      </el-popover>\r\n                    </div>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"故障\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #ff902c\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"异常\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <div>\r\n                      <span>异常 </span>\r\n                      <el-popover\r\n                        placement=\"top-start\"\r\n                        title=\"\"\r\n                        width=\"\"\r\n                        trigger=\"hover\"\r\n                        content=\"设备所传的一切非正常状态均记为异常\"\r\n                      >\r\n                        <img\r\n                          slot=\"reference\"\r\n                          class=\"popinfo\"\r\n                          src=\"@/assets/<EMAIL>\"\r\n                          alt=\"\"\r\n                        >\r\n                      </el-popover>\r\n                    </div>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"异常\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #6754d2\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"维修中\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <span>维修中 </span>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"维修中\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px; flex: 1; height: 0;\">\r\n      <el-col :span=\"3\" style=\"height: 100%\">\r\n        <el-card\r\n          shadow=\"never\"\r\n          class=\"tree_card\"\r\n          style=\"height: 100%; padding: 0\"\r\n        >\r\n          <el-tree\r\n            ref=\"positionTreeRef\"\r\n            v-loading=\"positionTreeLoading\"\r\n            class=\"positionTreeClass\"\r\n            style=\"height: calc(100vh - 340px); overflow-y: auto\"\r\n            :data=\"positionTree\"\r\n            :props=\"defaultProps\"\r\n            node-key=\"Id\"\r\n            :expand-on-click-node=\"false\"\r\n            :default-expanded-keys=\"defaultExpandedKeys\"\r\n            @node-click=\"handleNodeClick\"\r\n          />\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"21\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <CustomForm\r\n                :custom-form-items=\"customForm.formItems\"\r\n                :custom-form-buttons=\"customForm.customFormButtons\"\r\n                :value=\"ruleForm\"\r\n                :inline=\"true\"\r\n                :rules=\"customForm.rules\"\r\n                @submitForm=\"submitForm\"\r\n                @resetForm=\"resetForm\"\r\n              />\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row\r\n          :gutter=\"12\"\r\n          style=\"\r\n            margin-top: 10px;\r\n            height: calc(100vh - 430px);\r\n            overflow-y: auto;\r\n          \"\r\n          class=\"list_class\"\r\n        >\r\n          <el-col :span=\"24\" style=\"height: 100%\">\r\n            <div v-loading=\"deviceListLoading\" style=\"height: 100%\">\r\n              <div v-if=\"deviceList.length > 0\" class=\"list_box\">\r\n                <div\r\n                  v-for=\"(item, index) in deviceList\"\r\n                  :key=\"index\"\r\n                  class=\"list_item\"\r\n                  @click=\"nextRouteDetail(item)\"\r\n                >\r\n                  <div class=\"list_logo\">\r\n                    <el-image\r\n                      v-if=\"item.isHaveUrl\"\r\n                      style=\"width: auto; height: 100%\"\r\n                      :src=\"item.Url\"\r\n                      fit=\"cover\"\r\n                    >\r\n                      <!-- :preview-src-list=\"[item.Url]\" -->\r\n                      <div slot=\"error\" class=\"image-slot\">\r\n                        <i class=\"el-icon-picture-outline\" />\r\n                      </div>\r\n                    </el-image>\r\n\r\n                    <el-image\r\n                      v-if=\"!item.isHaveUrl\"\r\n                      style=\"width: auto; height: 100%\"\r\n                      :src=\"item.Url\"\r\n                      fit=\"cover\"\r\n                    >\r\n                      <div slot=\"error\" class=\"image-slot\">\r\n                        <i class=\"el-icon-picture-outline\" />\r\n                      </div>\r\n                    </el-image>\r\n                  </div>\r\n                  <div class=\"list_info\">\r\n                    <span class=\"title\">{{ item.Name || \"-\" }}</span>\r\n                    <div class=\"info\">\r\n                      <span class=\"label\">位置</span>\r\n                      <span class=\"value\">{{ item.Postion || \"-\" }}</span>\r\n                    </div>\r\n                    <div class=\"info\">\r\n                      <span class=\"label\">品牌</span>\r\n                      <span class=\"value\">{{ item.Brand || \"-\" }}</span>\r\n                    </div>\r\n                    <div class=\"action\">\r\n                      <div class=\"tags\">\r\n                        <div\r\n                          v-for=\"(statusItem, statusIndex) in item.Status\"\r\n                          :key=\"statusIndex\"\r\n                          class=\"tags_item\"\r\n                          :style=\"{\r\n                            background: getTagsStyle(statusItem).background,\r\n                          }\"\r\n                        >\r\n                          <span\r\n                            v-if=\"statusItem == '在线'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '正常'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '离线'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '故障'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '异常'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '维修中'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <!-- <span>{{ statusItem }}</span>\r\n                        <span>{{ statusItem }}</span> -->\r\n                        </div>\r\n                      </div>\r\n                      <div class=\"right\">\r\n                        <span>查看</span>\r\n                        <i class=\"el-icon-arrow-right\" />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div v-else class=\"list_no_box\">\r\n                <div class=\"no_content\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                  <span>无相关设备信息</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport nopictures from '@/assets/<EMAIL>'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport editDialog from '@/views/business/maintenanceAndUpkeep/workOrderManagement/editDialog.vue'\r\nimport {\r\n  GetDeviceStatus,\r\n  GetDeviceList,\r\n  GetPostionTreeList,\r\n  GetDictionaryDetailListByParentId\r\n} from '@/api/business/eqptAsset'\r\nimport { GetOssUrl, GetDictionaryDetailListByCode } from '@/api/sys/index'\r\nimport { getDictionary } from '@/utils/common'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\n\r\nexport default {\r\n  name: 'SzcjPJEquipmentAssetList',\r\n  components: {\r\n    editDialog,\r\n    CustomForm\r\n  },\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n      tagsStyle: [\r\n        {\r\n          text: '在线',\r\n          color: 'rgba(78, 191, 139, 1)',\r\n          background: 'rgba(78, 191, 139, 0.1)'\r\n        },\r\n        {\r\n          text: '正常',\r\n          color: 'rgba(27, 181, 224, 1)',\r\n          background: 'rgba(27, 181, 224, .1)'\r\n        },\r\n        {\r\n          text: '离线',\r\n          color: 'rgba(104, 116, 138, 1)',\r\n          background: 'rgba(104, 116, 138, .1)'\r\n        },\r\n        {\r\n          text: '故障',\r\n          color: 'rgba(255, 95, 122, 1)',\r\n          background: 'rgba(255, 95, 122, .1)'\r\n        },\r\n        {\r\n          text: '异常',\r\n          color: 'rgba(255, 144, 44, 1)',\r\n          background: 'rgba(255, 144, 44, .1)'\r\n        },\r\n        {\r\n          text: '维修中',\r\n          color: 'rgba(103, 84, 210, 1)',\r\n          background: 'rgba(103, 84, 210, .1)'\r\n        }\r\n      ],\r\n      ruleForm: {\r\n        Display_Name: '',\r\n        Brand: '',\r\n        Device_Type_Id: '',\r\n        Device_Type_Detail_Id: '',\r\n        Postion: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Display_Name',\r\n            label: '设备名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Brand',\r\n            label: '设备品牌',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Device_Type_Id',\r\n            label: '设备类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              this.customForm.formItems.find(\r\n                (v) => v.key === 'Device_Type_Detail_Id'\r\n              ).options = []\r\n              this.ruleForm.Device_Type_Detail_Id = ''\r\n              GetDictionaryDetailListByParentId(e).then((res) => {\r\n                this.customForm.formItems.find(\r\n                  (v) => v.key === 'Device_Type_Detail_Id'\r\n                ).options = res.Data.map((v) => {\r\n                  return {\r\n                    label: v.Display_Name,\r\n                    value: v.Id\r\n                  }\r\n                })\r\n              })\r\n            }\r\n          },\r\n          {\r\n            key: 'Device_Type_Detail_Id',\r\n            label: '设备子类',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Status',\r\n            label: '设备状态',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'WhereOrderStatus',\r\n            label: '维修状态',\r\n            type: 'select',\r\n            options: [\r\n              {\r\n                label: '正常',\r\n                value: '3'\r\n              },\r\n              {\r\n                label: '维修中',\r\n                value: '1'\r\n              },\r\n              {\r\n                label: '故障',\r\n                value: '0'\r\n              }\r\n            ],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          marginLeft: '50px',\r\n          submitName: '搜索',\r\n          submitShow: true,\r\n          resetShow: true,\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      // 设备状态\r\n      deviceStatusList: [],\r\n      // 位置树\r\n      positionTree: [],\r\n      // 设备列表\r\n      deviceList: [],\r\n      deviceListLoading: false,\r\n      positionTreeLoading: false,\r\n      defaultProps: {\r\n        children: 'Children',\r\n        label: 'Name',\r\n        value: 'Id'\r\n      },\r\n      defaultExpandedKeys: [],\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/equipmentData',\r\n          hidden: true,\r\n          component: () => import('./equipmentData.vue'),\r\n          name: 'PJEquipmentData',\r\n          meta: { title: `设备数采` }\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  activated() {},\r\n  beforeDestroy() {},\r\n  created() {\r\n    this.getDeviceStatus()\r\n    this.getPostionTreeList()\r\n    this.getDeviceList()\r\n\r\n    getDictionary('deviceType').then((res) => {\r\n      const item = this.customForm.formItems.find(\r\n        (v) => v.key === 'Device_Type_Id'\r\n      )\r\n      item.options = res.map((v) => {\r\n        return {\r\n          label: v.Display_Name,\r\n          value: v.Id\r\n        }\r\n      })\r\n    })\r\n\r\n    GetDictionaryDetailListByCode({\r\n      dictionaryCode: 'MonitorAudioStatus'\r\n    }).then((res) => {\r\n      const item = this.customForm.formItems.find((v) => v.key === 'Status')\r\n      item.options = res.Data.map((v) => {\r\n        return {\r\n          label: v.Display_Name,\r\n          value: v.Value\r\n        }\r\n      })\r\n    })\r\n  },\r\n  mounted() {},\r\n  methods: {\r\n    getTagsStyle(name) {\r\n      return this.tagsStyle.find((item) => item.text == name)\r\n    },\r\n    nextRouteDetail(data) {\r\n      data.num = 1\r\n      data.historyRouter = this.$route.name\r\n      this.$router.push({\r\n        name: 'PJEquipmentData',\r\n        query: { pg_redirect: this.$route.name, Id: data.Id }\r\n      })\r\n      this.$store.dispatch('eqpt/changeEqptData', data)\r\n    },\r\n\r\n    async getDeviceStatus() {\r\n      const res = await GetDeviceStatus({})\r\n      if (res.IsSucceed) {\r\n        console.log(res, '1221')\r\n        this.deviceStatusList = res.Data.map((item) => ({\r\n          ...item,\r\n          percent: ((item.Value / res.Data[0].Value) * 100).toFixed(0) + '%'\r\n        }))\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    getDeviceValue(name) {\r\n      if (this.deviceStatusList.length > 0) {\r\n        return this.deviceStatusList.find((item) => item.Label == name).Value\r\n      }\r\n      return ''\r\n    },\r\n    getDevicePrecentValue(name) {\r\n      if (this.deviceStatusList.length > 0) {\r\n        return this.deviceStatusList.find((item) => item.Label == name).percent\r\n      }\r\n      return ''\r\n    },\r\n\r\n    async getPostionTreeList() {\r\n      this.positionTreeLoading = true\r\n      const res = await GetPostionTreeList({})\r\n      if (res.IsSucceed) {\r\n        this.positionTreeLoading = false\r\n        this.defaultExpandedKeys = res.Data.map((v) => v.Id)\r\n        this.positionTree = res.Data\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n\r\n    async getDeviceList() {\r\n      this.deviceList = []\r\n      // this.deviceListLoading = true\r\n      const res = await GetDeviceList({\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        // this.userTableData.map(async item => {\r\n        //   item.ImgUrl = await GetOssUrl({ url: item.ImgUrl }).then(res => {\r\n        //     return res.Data\r\n        //   })\r\n        // })\r\n        this.deviceList = res.Data.map((item) => {\r\n          if (item.Url) {\r\n            return {\r\n              ...item,\r\n              isHaveUrl: true\r\n            }\r\n          } else {\r\n            return {\r\n              ...item,\r\n              isHaveUrl: false,\r\n              Url: nopictures\r\n            }\r\n          }\r\n        })\r\n        this.deviceListLoading = false\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n        this.deviceListLoading = false\r\n      }\r\n    },\r\n    submitForm() {\r\n      this.getDeviceList()\r\n    },\r\n    resetForm() {\r\n      this.getDeviceList()\r\n    },\r\n\r\n    async handleNodeClick(data, node) {\r\n      const parents = await this.findParentIds(this.positionTree, data.Id)\r\n      const newNode = [\r\n        ...parents,\r\n        {\r\n          Id: data.Id,\r\n          Name: data.Name\r\n        }\r\n      ]\r\n      this.ruleForm.Postion = newNode.map((v) => v.Name).join('/')\r\n      this.getDeviceList()\r\n    },\r\n    findParentIds(tree, targetId) {\r\n      const parentNodes = [] // 存储唯一的父节点ID和Name\r\n\r\n      // 辅助函数，用于检查父节点是否已存在于数组中\r\n      function parentNodeExists(id, name, array) {\r\n        return array.some((node) => node.Id === id && node.Name === name)\r\n      }\r\n\r\n      function traverse(nodes, parentId, parentName) {\r\n        if (!nodes) return false\r\n        for (const node of nodes) {\r\n          if (node.Id === targetId) {\r\n            // 如果当前节点是目标节点，并且它有父节点，则添加父节点信息（避免重复）\r\n            if (\r\n              parentId !== '' &&\r\n              !parentNodeExists(parentId, parentName, parentNodes)\r\n            ) {\r\n              parentNodes.push({ Id: parentId, Name: parentName })\r\n            }\r\n            return true // 已找到目标节点，停止遍历\r\n          }\r\n\r\n          // 递归遍历子节点\r\n          if (node.Children && traverse(node.Children, node.Id, node.Name)) {\r\n            // 如果在子节点中找到了目标节点，并且当前节点信息未收集，则添加它\r\n            if (!parentNodeExists(node.Id, node.Name, parentNodes)) {\r\n              parentNodes.push({ Id: node.Id, Name: node.Name })\r\n            }\r\n            return true // 继续向上遍历\r\n          }\r\n        }\r\n        return false // 在当前层级未找到目标节点\r\n      }\r\n      // 从树的根节点开始遍历\r\n      traverse(tree, '', '') // 根节点没有父节点，所以parentId和parentName为空字符串\r\n      // 如果需要，可以根据实际需求决定是否反转数组\r\n      parentNodes.reverse().push()\r\n      return parentNodes // 返回包含唯一父节点ID和名称的数组\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.szcjPJEquipmentAssetList {\r\n  // padding: 10px 15px;\r\n  // height: calc(100vh - 90px);\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .tree_card {\r\n    ::v-deep .el-card__body {\r\n      padding: 10px 0px 10px 0px !important;\r\n    }\r\n    ::v-deep .el-tree-node__label {\r\n      text-wrap: wrap !important;\r\n      padding: 6px !important;\r\n    }\r\n    ::v-deep .el-tree-node__content {\r\n      min-height: 32px !important;\r\n      height: auto !important;\r\n    }\r\n  }\r\n\r\n  .positionTreeClass::-webkit-scrollbar {\r\n    width: 5px;\r\n    height: 1px;\r\n  }\r\n  .positionTreeClass::-webkit-scrollbar-thumb {\r\n    /*滚动条里面小方块*/\r\n    border-radius: 10px;\r\n    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);\r\n    background: #f0f2f7;\r\n  }\r\n  .positionTreeClass::-webkit-scrollbar-track {\r\n    /*滚动条里面轨道*/\r\n    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);\r\n    border-radius: 10px;\r\n    // background: rgba(79, 104, 145, 0.35);\r\n  }\r\n\r\n  .list_class::-webkit-scrollbar {\r\n    width: 5px;\r\n    height: 1px;\r\n  }\r\n  .list_class::-webkit-scrollbar-thumb {\r\n    /*滚动条里面小方块*/\r\n    border-radius: 10px;\r\n    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);\r\n    // background: #f0f2f7;\r\n  }\r\n  .list_class::-webkit-scrollbar-track {\r\n    /*滚动条里面轨道*/\r\n    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);\r\n    border-radius: 10px;\r\n    // background: rgba(79, 104, 145, 0.35);\r\n  }\r\n\r\n  .card_content {\r\n    height: 130px;\r\n    padding: 0px 24px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .left {\r\n      font-family: PingFang SC, PingFang SC;\r\n      font-weight: 600;\r\n      font-size: 20px;\r\n      color: #7f8ca2;\r\n      font-style: normal;\r\n      text-transform: none;\r\n      display: flex;\r\n      align-items: center;\r\n      .num {\r\n        font-family: Helvetica, Helvetica;\r\n        font-weight: bold;\r\n        font-size: 40px;\r\n        color: #298dff;\r\n        font-style: normal;\r\n        text-transform: none;\r\n        margin-right: 20px;\r\n      }\r\n    }\r\n    .right {\r\n      img {\r\n        width: 64px;\r\n        height: 64px;\r\n      }\r\n    }\r\n  }\r\n  .list_no_box {\r\n    padding: 50px 10px;\r\n    height: 100%;\r\n    .no_content {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      img {\r\n        width: 300px;\r\n        height: auto;\r\n      }\r\n      span {\r\n        margin-top: 10px;\r\n        font-family: Microsoft YaHei, Microsoft YaHei;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #c2cbe2;\r\n        text-align: center;\r\n        font-style: normal;\r\n        text-transform: none;\r\n      }\r\n    }\r\n  }\r\n\r\n  .list_box {\r\n    width: 100%;\r\n    // height: 100%;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    display: grid;\r\n    grid-template-columns: repeat(5, calc(20% - 10px));\r\n    grid-column-gap: 10px;\r\n    grid-row-gap: 10px;\r\n\r\n    .list_item {\r\n      display: flex;\r\n      flex-direction: column;\r\n      background: white;\r\n      // margin-right: 10px;\r\n      // margin-bottom: 10px;\r\n      cursor: pointer;\r\n      .list_logo {\r\n        height: 140px;\r\n        background-color: #f0f2f7;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        .el-image {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n      .list_info {\r\n        margin-top: 10px;\r\n        // cursor: pointer;\r\n      }\r\n      .title {\r\n        padding: 6px 10px;\r\n        font-family: Microsoft YaHei, Microsoft YaHei;\r\n        font-weight: bold;\r\n        font-size: 16px;\r\n        color: #666666;\r\n        font-style: normal;\r\n        text-transform: none;\r\n      }\r\n      .info {\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0px 10px;\r\n        margin-top: 6px;\r\n        .label {\r\n          font-family: PingFang SC, PingFang SC;\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          font-style: normal;\r\n          text-transform: none;\r\n          margin-right: 6px;\r\n        }\r\n        .value {\r\n          font-family: Helvetica, Helvetica;\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          font-style: normal;\r\n          text-transform: none;\r\n        }\r\n      }\r\n      .action {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding: 0px 10px;\r\n        margin-top: 6px;\r\n        margin-bottom: 8px;\r\n        .tags {\r\n          display: flex;\r\n          flex-direction: row;\r\n          .tags_item {\r\n            font-family: PingFang SC, PingFang SC;\r\n            font-weight: 500;\r\n            font-size: 11px;\r\n            padding: 1px 2px;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            margin-right: 6px;\r\n            border-radius: 2px;\r\n          }\r\n        }\r\n        .right {\r\n          color: #298dff;\r\n          font-size: 13px;\r\n          cursor: pointer;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .card_second_content {\r\n    height: 39px;\r\n    padding: 0px 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .left {\r\n      font-family: PingFang SC, PingFang SC;\r\n      font-weight: 600;\r\n      font-size: 16px;\r\n      font-style: normal;\r\n      text-transform: none;\r\n      display: flex;\r\n      align-items: center;\r\n      .textInfo {\r\n        display: flex;\r\n        flex-direction: column;\r\n        .textStyle {\r\n          margin-top: 8px;\r\n          color: #999999;\r\n        }\r\n      }\r\n      .popinfo {\r\n        width: 15px;\r\n        height: 15px;\r\n        margin-left: 20px;\r\n      }\r\n      .num {\r\n        font-family: Helvetica, Helvetica;\r\n        font-weight: bold;\r\n        font-size: 32px;\r\n        font-style: normal;\r\n        text-transform: none;\r\n        margin-right: 20px;\r\n        min-width: 30px;\r\n      }\r\n    }\r\n    .right {\r\n      img {\r\n        width: 36px;\r\n        height: 36px;\r\n      }\r\n    }\r\n  }\r\n  ::v-deep .el-card__body {\r\n    border: none !important;\r\n  }\r\n  ::v-deep .el-card__header {\r\n    border-bottom: none !important;\r\n  }\r\n  ::v-deep .el-progress__text {\r\n    font-size: 18px !important;\r\n    color: #666666 !important;\r\n  }\r\n  ::v-deep.el-table .row-one {\r\n    background: rgba(41, 141, 255, 0.03) !important;\r\n  }\r\n\r\n  ::v-deep .el-table .row-two {\r\n    background: rgba(255, 255, 255, 1) !important;\r\n  }\r\n\r\n  ::v-deep .el-radio-button__inner {\r\n    background-color: #ffffff;\r\n    height: 32px;\r\n    width: 80px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n</style>\r\n"]}]}