{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\pjEquipmentAssetList\\index.vue?vue&type=style&index=0&id=44ac6c40&scoped=true&lang=css", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\pjEquipmentAssetList\\index.vue", "mtime": 1755735448748}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5wakVxdWlwbWVudEFzc2V0TGlzdCB7CiAgLyogaGVpZ2h0OiBjYWxjKDEwMHZoIC0gOTBweCk7ICovCiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0tBA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/equipmentManagement/pjEquipmentAssetList", "sourcesContent": ["<template>\n  <div class=\"app-container abs100 pjEquipmentAssetList\">\n    <custom-layout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"submitForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          ref=\"table1\"\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        />\n      </template>\n    </custom-layout>\n\n    <el-dialog\n      v-dialogDrag\n      width=\"30%\"\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogVisible\"\n    >\n      <component\n        :is=\"currentComponent\"\n        ref=\"content\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport addRouterPage from '@/mixins/add-router-page'\nimport {\n  DeleteEquipmentAssetEntity,\n  ExportEquipmentAssetsList,\n  GetEquipmentAssetPageList,\n  AssetImportTemplatePJ,\n  AssetEquipmentImportPJ,\n  ExportEquipmentListPJ,\n  GetEquipmentAssetPageListPJ,\n  GetDictionaryDetailListByParentId,\n  ExportEquipCardInfo\n} from '@/api/business/eqptAsset'\nimport { GetGridByCode } from '@/api/sys'\nimport { timeFormat } from '@/filters'\nimport { getDictionary } from '@/utils/common'\nimport { downloadFile, downloadFileOnNewTag } from '@/utils/downloadFile'\nimport Print from './components/print.vue'\nimport importDialog from './components/import.vue'\nimport exportInfo from '@/views/business/energyManagement/mixins/export.js'\nexport default {\n  name: 'EquipmentAssetList',\n  components: { CustomTable, CustomLayout, CustomForm, Print, importDialog },\n  mixins: [addRouterPage, exportInfo],\n  data() {\n    return {\n      componentsConfig: {\n        interfaceName: AssetEquipmentImportPJ\n      },\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true\n        },\n        close: () => {\n          this.dialogVisible = false\n          this.fetchData()\n        }\n      },\n      addPageArray: [\n        {\n          path: this.$route.path + '/add',\n          hidden: true,\n          component: () => import('./add.vue'),\n          name: 'EquipmentAssetListAdd',\n          meta: { title: `新增` }\n        },\n        {\n          path: this.$route.path + '/edit',\n          hidden: true,\n          component: () => import('./add.vue'),\n          name: 'EquipmentAssetListEdit',\n          meta: { title: `编辑` }\n        },\n        {\n          path: this.$route.path + '/view',\n          hidden: true,\n          component: () => import('./add.vue'),\n          name: 'EquipmentAssetListView',\n          meta: { title: `查看` }\n        },\n        {\n          path: this.$route.path + '/dataAcquisition',\n          hidden: true,\n          component: () => import('./dataAcquisition.vue'),\n          name: 'DataAcquisition',\n          meta: { title: `查看数据` }\n        },\n        {\n          path: this.$route.path + '/equipmentData',\n          hidden: true,\n          component: () => import('./equipmentData.vue'),\n          name: 'PJEquipmentData',\n          meta: { title: `设备数采` }\n        }\n      ],\n      ruleForm: {\n        EquipmentName: '',\n        departName: '',\n        EquipmentType: '',\n        EquipmentItemType: ''\n      },\n      customForm: {\n        formItems: [\n          {\n            key: 'EquipmentName',\n            label: '设备名称',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'EquipmentType',\n            label: '设备类型',\n            type: 'select',\n            options: [],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              this.customForm.formItems.find(\n                (v) => v.key === 'EquipmentItemType'\n              ).options = []\n              this.ruleForm.EquipmentItemType = ''\n              GetDictionaryDetailListByParentId(e).then((res) => {\n                this.customForm.formItems.find(\n                  (v) => v.key === 'EquipmentItemType'\n                ).options = res.Data.map((v) => {\n                  return {\n                    label: v.Display_Name,\n                    value: v.Id\n                  }\n                })\n              })\n            }\n          },\n          {\n            key: 'EquipmentItemType',\n            label: '设备子类',\n            type: 'select',\n            options: [],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'departName',\n            label: '所属部门',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              console.log(e)\n            }\n          }\n        ],\n        rules: {},\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        // 表格\n        // rowKey: 'Id', // 唯一标识 用于保持选中状态\n        pageSizeOptions: [20, 40, 60, 80, 100],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        tableColumns: [\n          {\n            width: '55',\n            otherOptions: {\n              type: 'selection',\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '设备名称',\n            key: 'Display_Name',\n            otherOptions: {\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '设备SN',\n            key: 'Serial_Number',\n            otherOptions: {\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '设备编号',\n            key: 'Device_Number',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '品牌',\n            key: 'Brand',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '规格型号',\n            key: 'Spec',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '设备类型',\n            key: 'Type_Name',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '设备子类',\n            key: 'Type_Detail_Name',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '厂家名称',\n            key: 'Manufacturer',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '厂家联系方式',\n            key: 'Manufacturer_Contact_Info',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '经销商',\n            key: 'Dealer',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '经销商联系方式',\n            key: 'Dealer_Contact_Info',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '工程师',\n            key: 'Engineer',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '工程师联系方式',\n            key: 'Engineer_Contact_Info',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '所属部门',\n            key: 'Department',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '安装位置',\n            key: 'Position',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '安装时间',\n            key: 'Install_Date',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '设备管理员',\n            key: 'Administrator',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '设备管理员联系方式',\n            key: 'Administrator_Contact_Info',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '用途',\n            key: 'Usage',\n            otherOptions: {\n              align: 'center'\n            }\n          }\n        ],\n        tableData: [],\n        operateOptions: {\n          align: 'center',\n          width: '200px'\n        },\n        buttonConfig: {\n          buttonList: [\n            {\n              text: '新增',\n              round: false, // 是否圆角\n              plain: false, // 是否朴素\n              circle: false, // 是否圆形\n              loading: false, // 是否加载中\n              disabled: false, // 是否禁用\n              icon: '', //  图标\n              autofocus: false, // 是否聚焦\n              type: 'primary', // primary / success / warning / danger / info / text\n              size: 'small', // medium / small / mini\n              onclick: (item) => {\n                console.log(item)\n                this.handleCreate()\n              }\n            },\n            {\n              text: '下载模板',\n              disabled: false, // 是否禁用\n              onclick: (item) => {\n                console.log(item)\n                this.handleDownTemplate()\n              }\n            },\n            {\n              text: '批量导入',\n              disabled: false, // 是否禁用\n              onclick: (item) => {\n                console.log(item)\n                this.currentComponent = 'importDialog'\n                this.dialogVisible = true\n                this.dialogTitle = '批量导入'\n              }\n            },\n            {\n              text: '批量导出',\n              disabled: false,\n              onclick: () => {\n                this.handleExport()\n              }\n            },\n            {\n              text: '批量打印',\n              disabled: false,\n              loading: false,\n              onclick: () => {\n                this.handlePrint()\n              }\n            },\n            {\n              text: '确定',\n              type: 'primary',\n              disabled: false,\n              onclick: () => {\n                this.handleAssocia()\n              }\n            }\n          ]\n        },\n        tableActionsWidth: 180,\n        tableActions: [\n          {\n            actionLabel: '编辑',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.handleEdit(row.Id)\n            }\n          },\n          {\n            actionLabel: '删除',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.handleDelete(row.Id)\n            }\n          },\n          // {\n          //   actionLabel: \"打印二维码\",\n          //   otherOptions: {\n          //     type: \"text\",\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handlePrintQr(row.Id);\n          //   },\n          // },\n          {\n            actionLabel: '查看详情',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.handleInfo(row.Id)\n            }\n          }\n          // {\n          //   actionLabel: \"查看数据\",\n          //   otherOptions: {\n          //     type: \"text\",\n          //   },\n          //   onclick: (index, row) => {\n          //     this.viewData(row);\n          //   },\n          // },\n        ]\n      },\n      multipleSelection: [],\n      currentComponent: 'Print',\n      dialogVisible: false,\n      associa: false // 是否关联\n    }\n  },\n  watch: {\n    'multipleSelection.length': {\n      handler(newValue) {\n        this.customTableConfig.buttonConfig.buttonList.find(\n          (item) => item.text === '确定'\n        ).disabled = !newValue\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.associa = this.$route.query.associa === 'true'\n    if (this.associa) {\n      this.customTableConfig.buttonConfig.buttonList = this.customTableConfig.buttonConfig.buttonList.filter(v => v.text === '确定')\n    } else {\n      this.customTableConfig.buttonConfig.buttonList = this.customTableConfig.buttonConfig.buttonList.filter(v => v.text !== '确定')\n    }\n  },\n  mounted() {\n    // this.getGridByCode(\"EquipmentAssetList\");\n    this.fetchData()\n    getDictionary('deviceType').then((res) => {\n      const item = this.customForm.formItems.find(\n        (v) => v.key === 'EquipmentType'\n      )\n      console.log('res', res, item)\n      item.options = res.map((v) => {\n        return {\n          label: v.Display_Name,\n          value: v.Id\n        }\n      })\n    })\n  },\n  activated() {\n    this.fetchData()\n  },\n  methods: {\n    resetForm() {\n      this.ruleForm = {}\n      this.customForm.formItems.find(\n        (v) => v.key === 'EquipmentItemType'\n      ).options = []\n      this.fetchData()\n      this.$refs.table1.setClearSelection()\n    },\n    submitForm() {\n      this.customTableConfig.currentPage = 1\n      this.fetchData()\n      this.$refs.table1.setClearSelection()\n    },\n    fetchData() {\n      GetEquipmentAssetPageListPJ({\n        Display_Name: this.ruleForm.EquipmentName,\n        Device_Type_Id: this.ruleForm.EquipmentType,\n        Device_Type_Detail_Id: this.ruleForm.EquipmentItemType,\n        Department: this.ruleForm.departName,\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.customTableConfig.tableData = res.Data.Data.map((v) => {\n            v.Install_Date = timeFormat(v.Install_Date)\n            return v\n          })\n          this.customTableConfig.total = res.Data.TotalCount\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    handleCreate() {\n      // this.dialogTitle = '新增'\n      // this.dialogVisible = true\n      this.$router.push({\n        name: 'EquipmentAssetListAdd',\n        query: { pg_redirect: this.$route.name, type: 1 }\n      })\n    },\n    handleSizeChange(val) {\n      this.customTableConfig.pageSize = val\n      this.fetchData({\n        Page: this.customTableConfig.currentPage,\n        PageSize: val\n      })\n    },\n    handleCurrentChange(val) {\n      this.customTableConfig.currentPage = val\n      this.fetchData({ Page: val, PageSize: this.customTableConfig.pageSize })\n    },\n    handleSelectionChange(data) {\n      console.log(data)\n      this.multipleSelection = data\n    },\n    // handleExport() {\n    //   console.log('handleExport')\n    //   ExportEquipmentAssetsList({\n    //     ids: this.multipleSelection.map(v => v.Id)\n    //   }).then(res => {\n    //     if (res.IsSucceed) {\n    //       downloadFile(res.Data)\n    //     } else {\n    //       this.$message({\n    //         message: res.Message,\n    //         type: 'error'\n    //       })\n    //     }\n    //   })\n    // },\n    // v2 导出设备资产列表\n    handleExport() {\n      console.log('handleExport')\n      ExportEquipmentListPJ({\n        Id: this.multipleSelection.map((v) => v.Id).toString(),\n        Display_Name: this.ruleForm.EquipmentName,\n        Device_Type_Id: this.ruleForm.EquipmentType,\n        Department: this.ruleForm.departName,\n        Device_Type_Detail_Id: this.ruleForm.EquipmentItemType\n      }).then((res) => {\n        if (res.IsSucceed) {\n          downloadFile(res.Data)\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    async handlePrint() {\n      this.customTableConfig.buttonConfig.buttonList[4].loading = true\n      const res = await ExportEquipCardInfo({\n        Display_Name: this.ruleForm.EquipmentName,\n        Device_Type_Id: this.ruleForm.EquipmentType,\n        Device_Type_Detail_Id: this.ruleForm.EquipmentItemType,\n        Department: this.ruleForm.departName,\n        Ids: this.multipleSelection.map((v) => v.Id)\n      })\n      if (res.IsSucceed) {\n        downloadFileOnNewTag(res.Data)\n      } else {\n        this.$message({\n          message: res.Message,\n          type: 'error'\n        })\n      }\n      this.customTableConfig.buttonConfig.buttonList[4].loading = false\n    },\n    handleDelete(id) {\n      this.$confirm('是否删除该设备, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteEquipmentAssetEntity({ ids: [id] }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.fetchData()\n            } else {\n              this.$message({\n                message: res.Message,\n                type: 'error'\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    handleEdit(id) {\n      this.$router.push({\n        name: 'EquipmentAssetListEdit',\n        query: { pg_redirect: this.$route.name, id, type: 2 }\n      })\n    },\n    handlePrintQr(v) {\n      this.dialogVisible = true\n      this.dialogTitle = '设备二维码'\n      this.$nextTick((_) => {\n        this.$refs['content'].setCode(v)\n      })\n    },\n    handleInfo(id) {\n      this.$router.push({\n        name: 'EquipmentAssetListView',\n        query: { pg_redirect: this.$route.name, id, type: 3 }\n      })\n    },\n    // getGridByCode(code) {\n    //   GetGridByCode({ code }).then((res) => {\n    //     console.log(res.Data);\n    //     if (res.IsSucceed) {\n    //       const Grid = res.Data.Grid;\n    //       this.customTableConfig.tableColumns = res.Data?.ColumnList.map(\n    //         (item) => {\n    //           return Object.assign(\n    //             {},\n    //             {\n    //               key: item.Code,\n    //               label: item.Display_Name,\n    //               width: item.Width,\n    //               otherOptions: {\n    //                 align: item.Align ? item.Align : \"center\",\n    //                 sortable: item.Is_Sort,\n    //                 fixed: item.Is_Frozen === false ? false : \"left\",\n    //                 Digit_Number: item.Digit_Number,\n    //               },\n    //             }\n    //           );\n    //         }\n    //       );\n    //       if (Grid.Is_Select) {\n    //         this.customTableConfig.tableColumns.unshift({\n    //           otherOptions: {\n    //             type: \"selection\",\n    //             align: \"center\",\n    //           },\n    //         });\n    //       }\n    //       this.customTableConfig.pageSize = Number(Grid.Row_Number);\n    //     }\n    //   });\n    // },\n    // 查看数据\n    viewData(data) {\n      data.num = 1\n      data.historyRouter = this.$route.name\n      this.$router.push({\n        name: 'PJEquipmentData',\n        query: { pg_redirect: this.$route.name, data }\n      })\n\n      this.$store.dispatch('eqpt/changeEqptData', data)\n    },\n    // 下载模板\n    handleDownTemplate() {\n      AssetImportTemplatePJ({}).then((res) => {\n        if (res.IsSucceed) {\n          downloadFile(res.Data, '设备资产导入模板')\n        } else {\n          this.$message.error(res.Message)\n        }\n      })\n    },\n    // 处理关联设备数据 待开发\n    handleAssocia() {\n      console.log('multipleSelection', this.multipleSelection)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.pjEquipmentAssetList {\n  /* height: calc(100vh - 90px); */\n  overflow: hidden;\n}\n</style>\n"]}]}