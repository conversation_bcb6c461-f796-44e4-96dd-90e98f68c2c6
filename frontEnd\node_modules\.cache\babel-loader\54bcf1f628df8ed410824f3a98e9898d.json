{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJsmartBroadcasting\\broadcastMediaFiles\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJsmartBroadcasting\\broadcastMediaFiles\\index.vue", "mtime": 1755506574490}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "dayjs", "GetMediaFileListSZCJ", "PostMediaFileDataList", "name", "components", "data", "_this", "updateDate", "currentComponent", "componentsConfig", "Data", "componentsFuns", "open", "dialogVisible", "close", "initData", "dialogTitle", "tableSelection", "ruleForm", "FileName", "Date", "BeginCreateDate", "EndCreateDate", "customForm", "formItems", "key", "label", "type", "otherOptions", "clearable", "change", "e", "console", "log", "disabled", "placeholder", "length", "format", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "text", "onclick", "item", "handleResetData", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "align", "width", "tableData", "operateOptions", "tableActions", "computed", "created", "methods", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "IsSucceed", "$message", "message", "stop", "searchForm", "resetForm", "_this3", "_callee2", "_callee2$", "_context2", "_objectSpread", "Page", "PageSize", "TotalCount", "UpdateDate", "error", "Message", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "selection", "handleEdit", "row"], "sources": ["src/views/business/SZCJsmartBroadcasting/broadcastMediaFiles/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <div class=\"tableNotice\">\r\n          <span></span>\r\n          <span>数据更新时间�?{{ updateDate }}</span>\r\n          <!-- <span>数据更新时间�?{{ customTableConfig.tableData[0].UpdateDate }}</span> -->\r\n        </div>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\n\r\nimport dayjs from \"dayjs\";\r\nimport {\r\n  GetMediaFileListSZCJ,\r\n  PostMediaFileDataList,\r\n} from \"@/api/business/smartBroadcasting\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      updateDate: \"\",\r\n      currentComponent: null,\r\n      componentsConfig: {\r\n        Data: {},\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.initData();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"编辑\",\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        FileName: \"\",\r\n        Date: [],\r\n        BeginCreateDate: null,\r\n        EndCreateDate: null,\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"FileName\",\r\n            label: \"文件名称\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Date\", // 字段ID\r\n            label: \"创建时间\", // Form的label\r\n            type: \"datePicker\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              type: \"daterange\",\r\n              disabled: false,\r\n              placeholder: \"请输�?..\",\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.BeginCreateDate = dayjs(e[0]).format(\r\n                  \"YYYY-MM-DD\"\r\n                );\r\n                this.ruleForm.EndCreateDate = dayjs(e[1]).format(\"YYYY-MM-DD\");\r\n              }\r\n            },\r\n          },\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"更新数据\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleResetData();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: \"文件名称\",\r\n            key: \"FileName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"播放时长\",\r\n            key: \"PlaybackTime\",\r\n            width: 140,\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"ID\",\r\n            key: \"MediaFileId\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"创建时间\",\r\n            key: \"CreateDate\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"创建来源\",\r\n            key: \"MediaSource\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: \"center\",\r\n          width: \"180\",\r\n        },\r\n        tableActions: [],\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    async handleResetData() {\r\n      const res = await PostMediaFileDataList({});\r\n      console.log(res, \"res\");\r\n      if (res.IsSucceed) {\r\n        this.initData();\r\n        this.$message({\r\n          type: \"success\",\r\n          message: \"更新成功\",\r\n        });\r\n      }\r\n    },\r\n\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1;\r\n      this.initData();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.BeginCreateDate = null;\r\n      this.ruleForm.EndCreateDate = null;\r\n      this.ruleForm.Date = null;\r\n      this.initData();\r\n    },\r\n\r\n    async initData() {\r\n      const res = await GetMediaFileListSZCJ({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n        if (res.Data.Data.length > 0) {\r\n          this.updateDate = res.Data.Data[0].UpdateDate || \"\";\r\n        }\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.initData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.initData();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleEdit(row) {\r\n      this.dialogVisible = true;\r\n      this.componentsConfig.Data = row;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.tableNotice {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 10px 15px;\r\n  color: rgba(34, 40, 52, 0.65);\r\n  font-size: 14px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AAEA,OAAAC,KAAA;AACA,SACAC,oBAAA,EACAC,qBAAA,QACA;AACA;EACAC,IAAA;EACAC,UAAA;IACAN,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAQ,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,UAAA;MACAC,gBAAA;MACAC,gBAAA;QACAC,IAAA;MACA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAN,KAAA,CAAAO,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAR,KAAA,CAAAO,aAAA;UACAP,KAAA,CAAAS,QAAA;QACA;MACA;MACAF,aAAA;MACAG,WAAA;MACAC,cAAA;MACAC,QAAA;QACAC,QAAA;QACAC,IAAA;QACAC,eAAA;QACAC,aAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAAA;UACAC,YAAA;YACA;YACAC,SAAA;YACAF,IAAA;YACAO,QAAA;YACAC,WAAA;UACA;UACAL,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;YACA,IAAAA,CAAA,IAAAA,CAAA,CAAAK,MAAA;cACA9B,KAAA,CAAAY,QAAA,CAAAG,eAAA,GAAArB,KAAA,CAAA+B,CAAA,KAAAM,MAAA,CACA,YACA;cACA/B,KAAA,CAAAY,QAAA,CAAAI,aAAA,GAAAtB,KAAA,CAAA+B,CAAA,KAAAM,MAAA;YACA;UACA;QACA,EACA;QACAC,KAAA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAAC,IAAA;cACAf,OAAA,CAAAC,GAAA,CAAAc,IAAA;cACAzC,KAAA,CAAA0C,eAAA;YACA;UACA;QAEA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA,GACA;UACA3B,KAAA;UACAD,GAAA;UACAG,YAAA;YACA0B,KAAA;UACA;QACA,GACA;UACA5B,KAAA;UACAD,GAAA;UACA8B,KAAA;UACA3B,YAAA;YACA0B,KAAA;UACA;QACA,GACA;UACA5B,KAAA;UACAD,GAAA;UACAG,YAAA;YACA0B,KAAA;UACA;QACA,GACA;UACA5B,KAAA;UACAD,GAAA;UACAG,YAAA;YACA0B,KAAA;UACA;QACA,GACA;UACA5B,KAAA;UACAD,GAAA;UACAG,YAAA;YACA0B,KAAA;UACA;QACA,EACA;QACAE,SAAA;QACAC,cAAA;UACAH,KAAA;UACAC,KAAA;QACA;QACAG,YAAA;MACA;IACA;EACA;EACAC,QAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAA7C,QAAA;EACA;EACA8C,OAAA;IACAb,eAAA,WAAAA,gBAAA;MAAA,IAAAc,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAtE,qBAAA;YAAA;cAAAiE,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACAzC,OAAA,CAAAC,GAAA,CAAAkC,GAAA;cACA,IAAAA,GAAA,CAAAO,SAAA;gBACAZ,MAAA,CAAA/C,QAAA;gBACA+C,MAAA,CAAAa,QAAA;kBACAhD,IAAA;kBACAiD,OAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAN,QAAA,CAAAO,IAAA;UAAA;QAAA,GAAAX,OAAA;MAAA;IACA;IAEAY,UAAA,WAAAA,WAAAzE,IAAA;MACA2B,OAAA,CAAAC,GAAA,CAAA5B,IAAA;MACA,KAAAqC,iBAAA,CAAAQ,WAAA;MACA,KAAAnC,QAAA;IACA;IACAgE,SAAA,WAAAA,UAAA;MACA,KAAA7D,QAAA,CAAAG,eAAA;MACA,KAAAH,QAAA,CAAAI,aAAA;MACA,KAAAJ,QAAA,CAAAE,IAAA;MACA,KAAAL,QAAA;IACA;IAEAA,QAAA,WAAAA,SAAA;MAAA,IAAAiE,MAAA;MAAA,OAAAjB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAgB,SAAA;QAAA,IAAAd,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAc,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAZ,IAAA,GAAAY,SAAA,CAAAX,IAAA;YAAA;cAAAW,SAAA,CAAAX,IAAA;cAAA,OACAvE,oBAAA,CAAAmF,aAAA;gBACAC,IAAA,EAAAL,MAAA,CAAAtC,iBAAA,CAAAQ,WAAA;gBACAoC,QAAA,EAAAN,MAAA,CAAAtC,iBAAA,CAAAS;cAAA,GACA6B,MAAA,CAAA9D,QAAA,CACA;YAAA;cAJAiD,GAAA,GAAAgB,SAAA,CAAAV,IAAA;cAKA,IAAAN,GAAA,CAAAO,SAAA;gBACAM,MAAA,CAAAtC,iBAAA,CAAAc,SAAA,GAAAW,GAAA,CAAAzD,IAAA,CAAAA,IAAA;gBACAsE,MAAA,CAAAtC,iBAAA,CAAAU,KAAA,GAAAe,GAAA,CAAAzD,IAAA,CAAA6E,UAAA;gBACA,IAAApB,GAAA,CAAAzD,IAAA,CAAAA,IAAA,CAAA0B,MAAA;kBACA4C,MAAA,CAAAzE,UAAA,GAAA4D,GAAA,CAAAzD,IAAA,CAAAA,IAAA,IAAA8E,UAAA;gBACA;cACA;gBACAR,MAAA,CAAAL,QAAA,CAAAc,KAAA,CAAAtB,GAAA,CAAAuB,OAAA;cACA;YAAA;YAAA;cAAA,OAAAP,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IACA;IAEAU,gBAAA,WAAAA,iBAAAC,GAAA;MACA5D,OAAA,CAAAC,GAAA,iBAAA4D,MAAA,CAAAD,GAAA;MACA,KAAAlD,iBAAA,CAAAS,QAAA,GAAAyC,GAAA;MACA,KAAA7E,QAAA;IACA;IACA+E,mBAAA,WAAAA,oBAAAF,GAAA;MACA5D,OAAA,CAAAC,GAAA,wBAAA4D,MAAA,CAAAD,GAAA;MACA,KAAAlD,iBAAA,CAAAQ,WAAA,GAAA0C,GAAA;MACA,KAAA7E,QAAA;IACA;IACAgF,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA/E,cAAA,GAAA+E,SAAA;IACA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAArF,aAAA;MACA,KAAAJ,gBAAA,CAAAC,IAAA,GAAAwF,GAAA;IACA;EACA;AACA", "ignoreList": []}]}