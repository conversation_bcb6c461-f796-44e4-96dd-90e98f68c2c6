{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControlV2\\accessControlRecord\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControlV2\\accessControlRecord\\index.vue", "mtime": 1755506574185}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw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file": "index.vue", "sourceRoot": "src/views/business/accessControlV2/accessControlRecord", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        >\r\n          <template #formSlot=\"{ slotScope }\">\r\n            <el-tree-select\r\n              ref=\"treeSelectArea\"\r\n              v-model=\"ruleForm.Dept\"\r\n              :select-params=\"{\r\n                clearable: true,\r\n              }\"\r\n              @searchFun=\"searchFun\"\r\n              class=\"customTreeSelect\"\r\n              :tree-params=\"categoryOptions\"\r\n            />\r\n          </template>\r\n        </CustomForm>\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\nimport dayjs from \"dayjs\";\r\nimport {\r\n  ExportEntranceEquipmentList,\r\n  GetDictionaryDetailListByCode,\r\n  GetTrafficRecordPageList,\r\n  GetCompanyList,\r\n} from \"@/api/business/accessControl\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nimport { GetDepartmentTree } from \"@/api/sys\";\r\n\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      ruleForm: {\r\n        PName: \"\",\r\n        daterangeArr: \"\",\r\n        BeginTime: \"\",\r\n        EndTime: \"\",\r\n        EquipmentType: \"\",\r\n        Position: \"\",\r\n        TrafficType: \"\",\r\n        TrafficWay: \"\",\r\n        PType: \"\",\r\n        EquipmentName: \"\",\r\n        Phone: \"\",\r\n        Dept: \"\",\r\n        Company: \"\",\r\n      },\r\n      categoryOptions: {\r\n        \"default-expand-all\": true,\r\n        filterable: true,\r\n        clickParent: false,\r\n        data: [],\r\n        props: {\r\n          disabled: \"disabled\",\r\n          children: \"Children\",\r\n          label: \"Label\",\r\n          value: \"Id\",\r\n        },\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"PName\", // 字段ID\r\n            label: \"姓名\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            // width: '240px',\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"daterangeArr\",\r\n            label: \"通行时间\",\r\n            type: \"datePicker\",\r\n            otherOptions: {\r\n              rangeSeparator: \"�?,\r\n              startPlaceholder: \"开始日�?,\r\n              endPlaceholder: \"结束日期\",\r\n              type: \"daterange\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"EquipmentType\", // 字段ID\r\n            label: \"门禁类型\", // Form的label\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Position\", // 字段ID\r\n            label: \"安装位置\", // Form的label\r\n            type: \"input\",\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"TrafficWay\", // 字段ID\r\n            label: \"通行方式\", // Form的label\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"TrafficType\", // 字段ID\r\n            label: \"通行类型\", // Form的label\r\n            type: \"select\",\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            options: [\r\n              { label: \"�?, value: \"�? },\r\n              { label: \"�?, value: \"�? },\r\n            ],\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"PType\", // 字段ID\r\n            label: \"人员类型\", // Form的label\r\n            type: \"select\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"EquipmentName\", // 字段ID\r\n            label: \"门禁名称\", // Form的label\r\n            type: \"input\",\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Phone\", // 字段ID\r\n            label: \"联系方式\", // Form的label\r\n            type: \"input\",\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Dept\", // 字段ID\r\n            label: \"人员部门\", // Form的label\r\n            type: \"slot\",\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Company\", // 字段ID\r\n            label: \"所属公�?, // Form的label\r\n            type: \"select\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleExport();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: \"通行时间\",\r\n            key: \"TrafficTime\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"姓名\",\r\n            key: \"PName\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"联系方式\",\r\n            key: \"ContactWay\",\r\n          },\r\n          {\r\n            label: \"人员类型\",\r\n            key: \"PType\",\r\n          },\r\n          {\r\n            label: \"所属部�?,\r\n            key: \"Department\",\r\n          },\r\n          {\r\n            label: \"所属公�?,\r\n            key: \"CompanyName\",\r\n          },\r\n          {\r\n            label: \"通行类型\",\r\n            key: \"TrafficType\",\r\n          },\r\n          {\r\n            label: \"设备类型\",\r\n            key: \"EquipmentType\",\r\n          },\r\n          {\r\n            label: \"设备名称\",\r\n            key: \"EquipmentName\",\r\n          },\r\n          {\r\n            label: \"设备位置\",\r\n            key: \"Position\",\r\n          },\r\n          {\r\n            label: \"通行方式\",\r\n            key: \"TrafficWay\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看详情\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/detail\",\r\n          hidden: true,\r\n          component: () => import(\"./detail.vue\"),\r\n          meta: { title: `门禁通行详情` },\r\n          name: \"accessControlRecordDetail\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  computed: {},\r\n  async created() {\r\n    // 门禁类型\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === \"EquipmentType\"\r\n    ).options = await this.initDeviceType(\"Entrance_Type\");\r\n    // 人员类型\r\n    this.customForm.formItems.find((item) => item.key === \"PType\").options =\r\n      await this.initDeviceType(\"P_Type\");\r\n    // 所属公�?\n    this.customForm.formItems.find((item) => item.key === \"Company\").options =\r\n      await this.initGetCompanyList(\"P_Type\");\r\n    //\r\n    this.getDeptTreeData();\r\n    this.init();\r\n  },\r\n  mixins: [addRouterPage],\r\n  methods: {\r\n    async initDeviceType(code) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode: code,\r\n      });\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value,\r\n      }));\r\n      return options;\r\n    },\r\n    async initGetCompanyList(code) {\r\n      const res = await GetCompanyList({});\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Name,\r\n        value: item.Id,\r\n      }));\r\n      return options;\r\n    },\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1;\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm = {\r\n        PName: \"\",\r\n        daterangeArr: \"\",\r\n        BeginTime: \"\",\r\n        EndTime: \"\",\r\n        EquipmentType: \"\",\r\n        Position: \"\",\r\n        TrafficType: \"\",\r\n        TrafficWay: \"\",\r\n        PType: \"\",\r\n        EquipmentName: \"\",\r\n        Phone: \"\",\r\n        Dept: \"\",\r\n        Company: \"\",\r\n      };\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.getTrafficRecordPageList();\r\n    },\r\n    init() {\r\n      this.getTrafficRecordPageList();\r\n    },\r\n    async getTrafficRecordPageList() {\r\n      let BeginTime = \"\";\r\n      let EndTime = \"\";\r\n      const data = { ...this.ruleForm };\r\n      if ((this.ruleForm.daterangeArr ?? []).length > 0) {\r\n        BeginTime = dayjs(this.ruleForm.daterangeArr[0]).format(\"YYYY-MM-DD\");\r\n        EndTime = dayjs(this.ruleForm.daterangeArr[1]).format(\"YYYY-MM-DD\");\r\n      }\r\n      delete data.daterangeArr;\r\n      console.log({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...data,\r\n        BeginTime,\r\n        EndTime,\r\n      });\r\n      const res = await GetTrafficRecordPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...data,\r\n        BeginTime,\r\n        EndTime,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n          ...item,\r\n          Traffic_Time: dayjs(item.Traffic_Time).format(\"YYYY-MM-DD HH:mm:ss\"),\r\n        }));\r\n        console.log(res);\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    handleEdit(index, row) {\r\n      this.$router.push({\r\n        name: \"accessControlRecordDetail\",\r\n        query: { pg_redirect: this.$route.name, row: JSON.stringify(row) },\r\n      });\r\n    },\r\n    async handleExport() {\r\n      let BeginTime = \"\";\r\n      let EndTime = \"\";\r\n      if ((this.ruleForm.daterangeArr ?? []).length > 0) {\r\n        BeginTime = dayjs(this.ruleForm.daterangeArr[0]).format(\"YYYY-MM-DD\");\r\n        EndTime = dayjs(this.ruleForm.daterangeArr[1]).format(\"YYYY-MM-DD\");\r\n      }\r\n      const res = await ExportEntranceEquipmentList({\r\n        // id: this.tableSelection.map((item) => item.Id).join(','),\r\n        ...this.ruleForm,\r\n        BeginTime,\r\n        EndTime,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        downloadFile(res.Data, \"21\");\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.init();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.init();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    async getDeptTreeData() {\r\n      await GetDepartmentTree({ isAll: false }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.categoryOptions.data = res.Data;\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data);\r\n          });\r\n        }\r\n      });\r\n    },\r\n    searchFun(value) {\r\n      this.$refs.treeSelectArea.filterFun(value);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}