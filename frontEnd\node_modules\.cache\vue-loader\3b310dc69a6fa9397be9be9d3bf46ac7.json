{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\alarmSettings\\index.vue?vue&type=style&index=0&id=0ad30550&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\alarmSettings\\index.vue", "mtime": 1755506574319}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoubGF5b3V0IHsKICBoZWlnaHQ6IDEwMCU7CiAgd2lkdGg6IDEwMCU7CiAgcG9zaXRpb246IGFic29sdXRlOwogIDo6di1kZWVwIHsKICAgIC5DdXN0b21MYXlvdXQgewogICAgICAubGF5b3V0VGFibGUgewogICAgICAgIGhlaWdodDogMDsKICAgICAgICAuQ3VzdG9tVGFibGUgewogICAgICAgICAgaGVpZ2h0OiAxMDAlOwogICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAgICAudGFibGUgewogICAgICAgICAgICBmbGV4OiAxOwogICAgICAgICAgICBoZWlnaHQ6IDA7CiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAgICAgIC5lbC10YWJsZSB7CiAgICAgICAgICAgICAgZmxleDogMTsKICAgICAgICAgICAgICBoZWlnaHQ6IDA7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/eventManagement/alarmSettings", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout\n      :layout-config=\"{\n        isShowSearchForm: false,\n      }\"\n    >\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        />\n      </template>\n    </CustomLayout>\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <component\n        :is=\"currentComponent\"\n        v-if=\"dialogVisible\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n      /></el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\nimport DialogForm from './dialogForm.vue'\n\nimport {\n  GetWarningConfigPageList,\n  GetNoticeDropDownOption,\n  EnableWarningSiteNotice,\n  EnableWarningMobileMessageNotice,\n  EnableWarningConfig,\n  SetWarningNoticeObject\n} from '@/api/business/eventManagement'\nexport default {\n  name: '',\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout\n  },\n  data() {\n    return {\n      currentComponent: DialogForm,\n      componentsConfig: {\n        Data: {}\n      },\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true\n        },\n        close: () => {\n          this.dialogVisible = false\n          this.onFresh()\n        }\n      },\n      dialogVisible: false,\n      dialogTitle: '告警管理审核',\n      tableSelection: [],\n      ruleForm: {},\n      customForm: {\n        formItems: [],\n        rules: {},\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        buttonConfig: {\n          buttonList: [\n            {\n              text: '启用',\n              round: false, // 是否圆角\n              plain: false, // 是否朴素\n              circle: false, // 是否圆形\n              loading: false, // 是否加载�?              disabled: true, // 是否禁用\n              icon: '', //  图标\n              autofocus: false, // 是否聚焦\n              type: 'primary', // primary / success / warning / danger / info / text\n              size: 'small', // medium / small / mini\n              onclick: (item) => {\n                console.log(item)\n                this.handleEnable()\n              }\n            },\n            {\n              text: '关闭',\n              type: 'danger',\n              disabled: true, // 是否禁用\n              onclick: (item) => {\n                console.log(item)\n                this.handleClose()\n              }\n            }\n          ]\n        },\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        height: '100%',\n        tableColumns: [\n          {\n            otherOptions: {\n              type: 'selection',\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '告警层级',\n            key: 'SourceTypeDisplay',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '业务模块',\n            key: 'Module',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '告警类型',\n            key: 'WarningType',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '规则描述',\n            key: 'RuleDescription',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '站内通知',\n            key: 'EnableSiteNotice',\n            otherOptions: {\n              align: 'center'\n            },\n            render: (row) => {\n              return this.$createElement('el-switch', {\n                props: {\n                  value: row.EnableSiteNotice,\n                  'active-color': '#13ce66'\n                },\n                on: {\n                  change: (e) => {\n                    this.handleSiteNoticeCloseEnable(row.Id, e)\n                  }\n                }\n              })\n            }\n          },\n          {\n            label: '短信通知',\n            key: 'EnableMobileMessageNotice',\n            otherOptions: {\n              align: 'center'\n            },\n            render: (row) => {\n              return this.$createElement('el-switch', {\n                props: {\n                  value: row.EnableMobileMessageNotice,\n                  'active-color': '#13ce66'\n                },\n                on: {\n                  change: (e) => {\n                    this.handleMobileMessageCloseEnable(row.Id, e)\n                  }\n                }\n              })\n            }\n          },\n          {\n            label: '通知角色',\n            key: 'Roles',\n            otherOptions: {\n              align: 'center'\n            },\n            render: (row) => {\n              return this.$createElement(\n                'span',\n                {\n                  style: {\n                    color: '#3582fb'\n                  },\n                  on: {\n                    click: () => {\n                      this.handleEdit(row, 'role')\n                    }\n                  }\n                },\n                row.Roles.join(',') || '添加'\n              )\n            }\n          },\n          {\n            label: '通知�?,\n            key: 'Users',\n            otherOptions: {\n              align: 'center'\n            },\n            render: (row) => {\n              return this.$createElement(\n                'span',\n                {\n                  style: {\n                    color: '#3582fb'\n                  },\n                  on: {\n                    click: () => {\n                      this.handleEdit(row, 'user')\n                    }\n                  }\n                },\n                row.Users.join(',') || '添加'\n              )\n            }\n          },\n          {\n            label: '执行周期',\n            key: 'InformCycleDisplay',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '上次执行时间',\n            key: 'LastInformTime',\n            width: 180,\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '启用状�?,\n            key: 'Enable',\n            otherOptions: {\n              align: 'center'\n            },\n            render: (row) => {\n              return this.$createElement('el-switch', {\n                props: {\n                  value: row.Enable,\n                  'active-color': '#13ce66'\n                },\n                on: {\n                  change: (e) => {\n                    this.handleCloseEnable(row.Id, e)\n                  }\n                }\n              })\n            }\n          }\n        ],\n        tableData: [],\n        operateOptions: {\n          align: 'center'\n        },\n        tableActions: []\n      }\n    }\n  },\n  watch: {\n    tableSelection: {\n      handler(newval, oldval) {\n        console.log(newval)\n        if (newval.length > 0) {\n          this.customTableConfig.buttonConfig.buttonList.forEach((ele) => {\n            ele.disabled = false\n          })\n        } else {\n          this.customTableConfig.buttonConfig.buttonList.forEach((ele) => {\n            ele.disabled = true\n          })\n        }\n      }\n    }\n  },\n  created() {\n    this.init()\n  },\n  // mixins: [getGridByCode],\n  methods: {\n    searchForm(data) {\n      console.log(data)\n      this.customTableConfig.currentPage = 1\n      this.onFresh()\n    },\n    resetForm() {\n      this.onFresh()\n    },\n    onFresh() {\n      this.GetWarningConfigPageList()\n    },\n    init() {\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\n      this.GetWarningConfigPageList()\n      this.getNoticeDropDownOption()\n    },\n    async getNoticeDropDownOption() {\n      const res = await GetNoticeDropDownOption({})\n      if (res.IsSucceed) {\n        const result = res.Data || []\n        let noticeType = []\n        let noticeLevel = []\n        result.forEach((element) => {\n          if (element.TypeName === '通知类型') {\n            noticeType = element.Data.map((item) => ({\n              value: item.Value,\n              label: item.Name\n            }))\n          } else if (element.TypeName === '发布层级') {\n            noticeLevel = element.Data.map((item) => ({\n              value: item.Value,\n              label: item.Name\n            }))\n          }\n        })\n        // this.customForm.formItems.find((item) => item.key == \"Type\").options =\n        //   noticeType;\n      }\n    },\n    async GetWarningConfigPageList() {\n      const res = await GetWarningConfigPageList({\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n        ...this.ruleForm\n      })\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data\n        this.customTableConfig.total = res.Data.TotalCount\n        if (this.customTableConfig.tableData.length > 0) {\n          console.log(this.customTableConfig.tableData.length)\n        }\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n\n    async handleMobileMessageCloseEnable(id, status) {\n      const res = await EnableWarningMobileMessageNotice({\n        Ids: [id],\n        Enable: status\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        this.init()\n      }\n    },\n\n    async handleSiteNoticeCloseEnable(id, status) {\n      const res = await EnableWarningSiteNotice({\n        Ids: [id],\n        Enable: status\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        this.init()\n      }\n    },\n\n    async handleCloseEnable(id, status) {\n      const res = await EnableWarningConfig({\n        Ids: [id],\n        Enable: status\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        this.init()\n      }\n    },\n    handleEdit(row, type) {\n      console.log(row, 'row')\n      this.dialogTitle = `告警管理审核`\n      this.dialogVisible = true\n      this.componentsConfig = {\n        ID: row.Id,\n        type,\n        UserIds: row.UserIds,\n        Users: row.Users,\n        RoleIds: row.RoleIds,\n        Roles: row.Roles\n      }\n    },\n    async handleEnable() {\n      const res = await EnableWarningConfig({\n        Ids: this.tableSelection.map((item) => item.Id),\n        Enable: true\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        this.init()\n      }\n    },\n\n    async handleClose() {\n      const res = await EnableWarningConfig({\n        Ids: this.tableSelection.map((item) => item.Id),\n        Enable: false\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        this.init()\n      }\n    },\n\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.GetWarningConfigPageList()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`)\n      this.customTableConfig.currentPage = val\n      this.GetWarningConfigPageList()\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.layout {\n  height: 100%;\n  width: 100%;\n  position: absolute;\n  ::v-deep {\n    .CustomLayout {\n      .layoutTable {\n        height: 0;\n        .CustomTable {\n          height: 100%;\n          display: flex;\n          flex-direction: column;\n          .table {\n            flex: 1;\n            height: 0;\n            display: flex;\n            flex-direction: column;\n            .el-table {\n              flex: 1;\n              height: 0;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}