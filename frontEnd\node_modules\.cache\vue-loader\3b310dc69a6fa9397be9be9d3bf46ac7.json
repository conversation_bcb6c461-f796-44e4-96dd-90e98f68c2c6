{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\alarmSettings\\index.vue?vue&type=style&index=0&id=0ad30550&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\alarmSettings\\index.vue", "mtime": 1755674552422}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5sYXlvdXQgew0KICBoZWlnaHQ6IDEwMCU7DQogIHdpZHRoOiAxMDAlOw0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIDo6di1kZWVwIHsNCiAgICAuQ3VzdG9tTGF5b3V0IHsNCiAgICAgIC5sYXlvdXRUYWJsZSB7DQogICAgICAgIGhlaWdodDogMDsNCiAgICAgICAgLkN1c3RvbVRhYmxlIHsNCiAgICAgICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgICAgIC50YWJsZSB7DQogICAgICAgICAgICBmbGV4OiAxOw0KICAgICAgICAgICAgaGVpZ2h0OiAwOw0KICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgICAgICAuZWwtdGFibGUgew0KICAgICAgICAgICAgICBmbGV4OiAxOw0KICAgICAgICAgICAgICBoZWlnaHQ6IDA7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/eventManagement/alarmSettings", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout\r\n      :layout-config=\"{\r\n        isShowSearchForm: false,\r\n      }\"\r\n    >\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport DialogForm from './dialogForm.vue'\r\n\r\nimport {\r\n  GetWarningConfigPageList,\r\n  GetNoticeDropDownOption,\r\n  EnableWarningSiteNotice,\r\n  EnableWarningMobileMessageNotice,\r\n  EnableWarningConfig,\r\n  SetWarningNoticeObject\r\n} from '@/api/business/eventManagement'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '告警管理审核',\r\n      tableSelection: [],\r\n      ruleForm: {},\r\n      customForm: {\r\n        formItems: [],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '启用',\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: true, // 是否禁用\r\n              icon: '', //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleEnable()\r\n              }\r\n            },\r\n            {\r\n              text: '关闭',\r\n              type: 'danger',\r\n              disabled: true, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleClose()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          {\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center',\r\n              fixed: 'left'\r\n            }\r\n          },\r\n          {\r\n            label: '告警层级',\r\n            key: 'SourceTypeDisplay',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '业务模块',\r\n            key: 'Module',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '告警类型',\r\n            key: 'WarningType',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '规则描述',\r\n            key: 'RuleDescription',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '站内通知',\r\n            key: 'EnableSiteNotice',\r\n            otherOptions: {\r\n              align: 'center'\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement('el-switch', {\r\n                props: {\r\n                  value: row.EnableSiteNotice,\r\n                  'active-color': '#13ce66'\r\n                },\r\n                on: {\r\n                  change: (e) => {\r\n                    this.handleSiteNoticeCloseEnable(row.Id, e)\r\n                  }\r\n                }\r\n              })\r\n            }\r\n          },\r\n          {\r\n            label: '短信通知',\r\n            key: 'EnableMobileMessageNotice',\r\n            otherOptions: {\r\n              align: 'center'\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement('el-switch', {\r\n                props: {\r\n                  value: row.EnableMobileMessageNotice,\r\n                  'active-color': '#13ce66'\r\n                },\r\n                on: {\r\n                  change: (e) => {\r\n                    this.handleMobileMessageCloseEnable(row.Id, e)\r\n                  }\r\n                }\r\n              })\r\n            }\r\n          },\r\n          {\r\n            label: '通知角色',\r\n            key: 'Roles',\r\n            otherOptions: {\r\n              align: 'center'\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement(\r\n                'span',\r\n                {\r\n                  style: {\r\n                    color: '#3582fb'\r\n                  },\r\n                  on: {\r\n                    click: () => {\r\n                      this.handleEdit(row, 'role')\r\n                    }\r\n                  }\r\n                },\r\n                row.Roles.join(',') || '添加'\r\n              )\r\n            }\r\n          },\r\n          {\r\n            label: '通知人',\r\n            key: 'Users',\r\n            otherOptions: {\r\n              align: 'center'\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement(\r\n                'span',\r\n                {\r\n                  style: {\r\n                    color: '#3582fb'\r\n                  },\r\n                  on: {\r\n                    click: () => {\r\n                      this.handleEdit(row, 'user')\r\n                    }\r\n                  }\r\n                },\r\n                row.Users.join(',') || '添加'\r\n              )\r\n            }\r\n          },\r\n          {\r\n            label: '执行周期',\r\n            key: 'InformCycleDisplay',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '上次执行时间',\r\n            key: 'LastInformTime',\r\n            width: 180,\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '启用状态',\r\n            key: 'Enable',\r\n            otherOptions: {\r\n              align: 'center'\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement('el-switch', {\r\n                props: {\r\n                  value: row.Enable,\r\n                  'active-color': '#13ce66'\r\n                },\r\n                on: {\r\n                  change: (e) => {\r\n                    this.handleCloseEnable(row.Id, e)\r\n                  }\r\n                }\r\n              })\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center'\r\n        },\r\n        tableActions: []\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    tableSelection: {\r\n      handler(newval, oldval) {\r\n        console.log(newval)\r\n        if (newval.length > 0) {\r\n          this.customTableConfig.buttonConfig.buttonList.forEach((ele) => {\r\n            ele.disabled = false\r\n          })\r\n        } else {\r\n          this.customTableConfig.buttonConfig.buttonList.forEach((ele) => {\r\n            ele.disabled = true\r\n          })\r\n        }\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.init()\r\n  },\r\n  // mixins: [getGridByCode],\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.GetWarningConfigPageList()\r\n    },\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.GetWarningConfigPageList()\r\n      this.getNoticeDropDownOption()\r\n    },\r\n    async getNoticeDropDownOption() {\r\n      const res = await GetNoticeDropDownOption({})\r\n      if (res.IsSucceed) {\r\n        const result = res.Data || []\r\n        let noticeType = []\r\n        let noticeLevel = []\r\n        result.forEach((element) => {\r\n          if (element.TypeName === '通知类型') {\r\n            noticeType = element.Data.map((item) => ({\r\n              value: item.Value,\r\n              label: item.Name\r\n            }))\r\n          } else if (element.TypeName === '发布层级') {\r\n            noticeLevel = element.Data.map((item) => ({\r\n              value: item.Value,\r\n              label: item.Name\r\n            }))\r\n          }\r\n        })\r\n        // this.customForm.formItems.find((item) => item.key == \"Type\").options =\r\n        //   noticeType;\r\n      }\r\n    },\r\n    async GetWarningConfigPageList() {\r\n      const res = await GetWarningConfigPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n        if (this.customTableConfig.tableData.length > 0) {\r\n          console.log(this.customTableConfig.tableData.length)\r\n        }\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n\r\n    async handleMobileMessageCloseEnable(id, status) {\r\n      const res = await EnableWarningMobileMessageNotice({\r\n        Ids: [id],\r\n        Enable: status\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.init()\r\n      }\r\n    },\r\n\r\n    async handleSiteNoticeCloseEnable(id, status) {\r\n      const res = await EnableWarningSiteNotice({\r\n        Ids: [id],\r\n        Enable: status\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.init()\r\n      }\r\n    },\r\n\r\n    async handleCloseEnable(id, status) {\r\n      const res = await EnableWarningConfig({\r\n        Ids: [id],\r\n        Enable: status\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.init()\r\n      }\r\n    },\r\n    handleEdit(row, type) {\r\n      console.log(row, 'row')\r\n      this.dialogTitle = `告警管理审核`\r\n      this.dialogVisible = true\r\n      this.componentsConfig = {\r\n        ID: row.Id,\r\n        type,\r\n        UserIds: row.UserIds,\r\n        Users: row.Users,\r\n        RoleIds: row.RoleIds,\r\n        Roles: row.Roles\r\n      }\r\n    },\r\n    async handleEnable() {\r\n      const res = await EnableWarningConfig({\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n        Enable: true\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.init()\r\n      }\r\n    },\r\n\r\n    async handleClose() {\r\n      const res = await EnableWarningConfig({\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n        Enable: false\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.init()\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.GetWarningConfigPageList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.GetWarningConfigPageList()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: 100%;\r\n  width: 100%;\r\n  position: absolute;\r\n  ::v-deep {\r\n    .CustomLayout {\r\n      .layoutTable {\r\n        height: 0;\r\n        .CustomTable {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          .table {\r\n            flex: 1;\r\n            height: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            .el-table {\r\n              flex: 1;\r\n              height: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}