<template>
  <div class="app-container abs100">
    <div class="top">
      <CustomForm
        :custom-form-items="customForm.formItems"
        :custom-form-buttons="customForm.customFormButtons"
        :value="ruleForm"
        :inline="true"
        :rules="customForm.rules"
        @submitForm="searchForm"
        @resetForm="resetForm"
      />
    </div>
    <div class="bottom">
      <div class="tableNotice">
        <span></span>
        <span>数据更新时间： {{ updateDate }}</span>
        <!-- <span>数据更新时间： {{ customTableConfig.tableData[0].UpdateDate }}</span> -->
      </div>
      <div class="tableBox">
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </div>
    </div>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";

import dayjs from "dayjs";
import {
  GetMediaFileList,
  PostMediaFileDataList,
} from "@/api/business/smartBroadcasting";
export default {
  name: "",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
  },
  data() {
    return {
      updateDate: "",
      currentComponent: null,
      componentsConfig: {
        Data: {},
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.initData();
        },
      },
      dialogVisible: false,
      dialogTitle: "编辑",
      tableSelection: [],
      ruleForm: {
        FileName: "",
        Date: [],
        BeginCreateDate: null,
        EndCreateDate: null,
      },
      customForm: {
        formItems: [
          {
            key: "FileName",
            label: "文件名称",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
          {
            key: "Date", // 字段ID
            label: "创建时间", // Form的label
            type: "datePicker", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              type: "daterange",
              disabled: false,
              placeholder: "请输入...",
            },
            change: (e) => {
              // change事件
              console.log(e);
              if (e && e.length > 0) {
                this.ruleForm.BeginCreateDate = dayjs(e[0]).format(
                  "YYYY-MM-DD"
                );
                this.ruleForm.EndCreateDate = dayjs(e[1]).format("YYYY-MM-DD");
              }
            },
          },
        ],
        rules: {},
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "更新数据",
              onclick: (item) => {
                console.log(item);
                this.handleResetData();
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            label: "文件名称",
            key: "FileName",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "播放时长",
            key: "PlaybackTime",
            width: 140,
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "ID",
            key: "MediaFileId",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "创建时间",
            key: "CreateDate",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "创建来源",
            key: "MediaSource",
            otherOptions: {
              align: "center",
            },
          },
        ],
        tableData: [],
        operateOptions: {
          align: "center",
          width: "180",
        },
        tableActions: [],
      },
    };
  },
  computed: {},
  created() {
    this.initData();
  },
  methods: {
    async handleResetData() {
      const res = await PostMediaFileDataList({});
      console.log(res, "res");
      if (res.IsSucceed) {
        this.initData();
        this.$message({
          type: "success",
          message: "更新成功",
        });
      }
    },

    searchForm(data) {
      console.log(data);
      this.customTableConfig.currentPage = 1;
      this.initData();
    },
    resetForm() {
      this.ruleForm.BeginCreateDate = null;
      this.ruleForm.EndCreateDate = null;
      this.ruleForm.Date = null;
      this.initData();
    },

    async initData() {
      const res = await GetMediaFileList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data;
        this.customTableConfig.total = res.Data.TotalCount;
        if (res.Data.Data.length > 0) {
          this.updateDate = res.Data.Data[0].UpdateDate || "";
        }
      } else {
        this.$message.error(res.Message);
      }
    },

    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.initData();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
    handleEdit(row) {
      this.dialogVisible = true;
      this.componentsConfig.Data = row;
    },
  },
};
</script>

<style lang="scss" scoped>
.top {
  margin: 15px 15px 0px 15px;
  padding: 10px 15px;
  background-color: white;
}
.bottom {
  margin: 10px 15px;
  padding: 10px 15px;
  background-color: #fff;
  height: calc(100vh - 190px);
  .tableNotice {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    color: rgba(34, 40, 52, 0.65);
    font-size: 14px;
  }
  .tableBox {
    height: calc(100vh - 240px);
  }
}
</style>
