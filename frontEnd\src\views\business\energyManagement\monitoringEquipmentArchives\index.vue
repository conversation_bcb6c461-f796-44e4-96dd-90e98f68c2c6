<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      top="6vh"
      :destroy-on-close="true"
    >
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        ref="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      /></el-dialog>
  </div>
</template>

<script>
import { parseTime } from '@/utils'
// import { baseUrl } from '@/utils/baseurl'
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import DialogForm from './dialogForm.vue'
import DialogQuota from './dialogQuota.vue'
import { downloadFile } from '@/utils/downloadFile'
import { GetGridByCode } from '@/api/sys'
import {
  GetDictionaryDetailListByCode,
  GetEquipmentList,
  DeleteEquipment,
  ExportEnergyEquipment,
  GetEqtEntity,
  EnergyImportTemplate,
  EnergyEquipmentImport
} from '@/api/business/energyManagement'
import importDialog from '@/views/business/energyManagement/components/import.vue'
import exportInfo from '@/views/business/energyManagement/mixins/export.js'
import addRouterPage from '@/mixins/add-router-page'

export default {
  name: 'MonitoringEquipmentArchives',
  components: {
    CustomTable,
    // CustomButton,
    // CustomTitle,
    CustomForm,
    CustomLayout,
    importDialog
  },
  mixins: [exportInfo],
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {
        interfaceName: EnergyEquipmentImport
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      tableSelection: [],

      ruleForm: {
        Content: '',
        EqtType: '',
        Position: ''
      },
      customForm: {
        formItems: [
          {
            key: 'Content', // 字段ID
            label: '点表编号或名称', // Form的label
            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            placeholder: '输入点表编号或名称进行搜索',
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true
            },
            width: '240px',
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'EqtType',
            label: '点表类型',
            type: 'select',
            placeholder: '请选择点表类型',
            options: [], // 类型数据列表
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Position', // 字段ID
            label: '安装位置', // Form的label
            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            placeholder: '请输入安装位置',
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          }
        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: '新增',
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: false, // 是否禁用
              icon: '', //  图标
              autofocus: false, // 是否聚焦
              type: 'primary', // primary / success / warning / danger / info / text
              size: 'small', // medium / small / mini
              onclick: (item) => {
                console.log(item)
                this.handleCreate()
              }
            },
            {
              text: '统计节点配置',
              disabled: false, // 是否禁用
              type: 'primary',
              onclick: (item) => {
                console.log(item)
                this.handleClick()
              }
            },
            {
              text: '下载模板',
              disabled: false, // 是否禁用
              onclick: (item) => {
                console.log(item)
                this.handleDownTemplate()
              }
            },
            {
              text: '批量导入',
              disabled: false, // 是否禁用
              onclick: (item) => {
                console.log(item)
                this.currentComponent = 'importDialog'
                this.dialogVisible = true
                this.dialogTitle = '批量导入'
              }
            },
            // {
            //   text: '导出',
            //   disabled: true,
            //   onclick: (item) => {
            //     console.log(item)
            //     this.handleExport()
            //   }
            // },
            {
              text: '批量导出',
              onclick: (item) => {
                console.log(item)
                this.handleAllExport()
              }
            }
          ]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableActionsWidth:160,
        tableColumns: [
          {
            width: 50,
            otherOptions: {
              type: 'selection',
              align: 'center'
            }
          },
          {
            width: 60,
            label: '序号',
            otherOptions: {
              type: 'index',
              align: 'center'
            } // key
            // otherOptions: {
            //   width: 180, // 宽度
            //   fixed: 'left', // left, right
            //   align: 'center' //	left/center/right
            // }
          }
          //   {
          //     label: '设备编号',
          //     key: 'HId'
          //   }
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: '查看',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'view')
            }
          },
          {
            actionLabel: '编辑',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'edit')
            }
          },
          {
            actionLabel: '配额',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleQuota(row)
            }
          },
          {
            actionLabel: '删除',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleDelete(index, row)
            }
          }
        ]
      },
      addPageArray: [
        {
          path: this.$route.path + '/config',
          hidden: true,
          component: () => import('./config.vue'),
          name: 'MonitoringEquipmentArchivesConfig',
          meta: { title: `点表统计配置` }
        }
      ]
    }
  },
  computed: {},
  mixins: [addRouterPage],
  created() {
    if(localStorage.getItem('TenantId') !== 'pjszgc') {
      this.customTableConfig.buttonConfig.buttonList.splice(1, 1)
    }
    this.getBaseData()
    this.init()
  },
  methods: {
    getBaseData() {
      // 获取点表类型
      GetDictionaryDetailListByCode({ dictionaryCode: 'PointTableType' }).then(res => {
        if (res.IsSucceed) {
          const data = res.Data.map(item => {
            return {
              label: item.Display_Name,
              value: item.Value
            }
          })
          this.customForm.formItems[1].options = data
        } else {
          this.$message({
            type: 'error',
            data: res.Message
          })
        }
      })
      // 获取表格配置
      GetGridByCode({ code: 'monitoring_equipment_archives_list' }).then(res => {
        if (res.IsSucceed) {
          const data = res.Data.ColumnList.map(item => {
            return {
              label: item.Display_Name,
              key: item.Code,
              otherOptions: {
                fixed: item.Is_Frozen === false ? false : "left",
              }
            }
          })
          this.customTableConfig.tableColumns.push(...data)
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    searchForm(data) {
      console.log(data)
      this.customTableConfig.currentPage = 1
      this.onFresh()
    },
    resetForm() {
      this.onFresh()
    },
    onFresh() {
      this.getEquipmentList()
    },
    init() {
      this.getEquipmentList()
    },
    async getEquipmentList() {
      const res = await GetEquipmentList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        console.log(res)
        this.customTableConfig.tableData = res.Data.Data.map(item => {
          item.Date = item.Date ? parseTime(new Date(item.Date), '{y}-{m}-{d}') : ''
          return item
        })
        this.customTableConfig.total = res.Data.TotalCount
      } else {
        this.$message({
          type: 'error',
          message: res.Message
        })
      }
    },
    handleCreate() {
      this.dialogTitle = '新增'
      this.dialogVisible = true
      this.componentsConfig = {}
      this.currentComponent = DialogForm
      this.$nextTick(() => {
        this.$refs.currentComponent.init('add')
      })
    },
    handleDelete(index, row) {
      console.log(index, row)
      this.$confirm('该操作将在监测设备档案中删除该点表台账信息,请确认是否删除?', {
        type: 'warning'
      })
        .then(async(_) => {
          const res = await DeleteEquipment({
            IDs: [row.Id]
          })
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.init()
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        })
        .catch((_) => { })
    },
    handleEdit(index, row, type) {
      console.log(index, row, type)
      this.currentComponent = DialogForm
      this.dialogVisible = true
      if (type === 'view') {
        this.dialogTitle = '查看'
        this.componentsConfig = { ...row }
        this.$nextTick(() => {
          this.$refs.currentComponent.init(type)
        })
      } else if (type === 'edit') {
        this.dialogTitle = '编辑'
        this.componentsConfig = { ...row }
        this.$nextTick(() => {
          this.$refs.currentComponent.init(type)
        })
      }
    },
    async handleExport() {
      console.log(this.ruleForm)
      console.log(this.tableSelection, 'this.tableSelection')
      const res = await ExportEnergyEquipment({
        IsAll: false,
        Ids: this.tableSelection.map((item) => item.Id),
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '21')
        // const url = new URL(res.Data, baseUrl())
        // window.open(url.href, '_blank')
        this.$message({
          type: 'success',
          message: '导出成功!'
        })
      }
    },
    handleQuota(row) {
      this.currentComponent = DialogQuota
      this.dialogTitle = '编辑'
      this.dialogVisible = true
      let data = {}
      GetEqtEntity({ ID: row.Id }).then(res => {
        if (res.IsSucceed) {
          data = Object.assign(data, res.Data)
          console.log(1, data)
          this.componentsConfig = { ...row, ...data }
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },

    // async handleAllExport() {
    //   const res = await ExportEnergyEquipment({
    //     IsAll: true,
    //     Ids: [],
    //     ...this.ruleForm
    //   })
    //   if (res.IsSucceed) {
    //     console.log(res)
    //     downloadFile(res.Data, '21')
    //     // const url = new URL(res.Data, baseUrl())
    //     // window.open(url.href, '_blank')
    //     this.$message({
    //       type: 'success',
    //       message: '导出成功!'
    //     })
    //   }
    // },
    // v2 版本导出
    async handleAllExport() {
      const res = await ExportEnergyEquipment({
        IsAll: true,
        Ids: this.tableSelection.map((item) => item.Id),
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '21')
        // const url = new URL(res.Data, baseUrl())
        // window.open(url.href, '_blank')
        this.$message({
          type: 'success',
          message: '导出成功!'
        })
      }
    },
    // 下载模板
    handleDownTemplate() {
      EnergyImportTemplate({ }).then(res => {
        if (res.IsSucceed) {
          downloadFile(res.Data, '能耗监控设备管理导入模板')
        } else {
          this.$message.error(res.Message)
        }
      })
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.init()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.init()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
      this.customTableConfig.buttonConfig.buttonList[1].disabled = selection.length === 0
    },
    handleClick() {
      this.$router.push({ name: 'MonitoringEquipmentArchivesConfig', query: { pg_redirect: this.$route.name } })
    }
  }
}
</script>

  <style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
.layout{
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>

