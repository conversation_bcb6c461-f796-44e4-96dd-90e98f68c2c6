{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\barrierEquipment\\index.vue?vue&type=style&index=0&id=198768b6&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\barrierEquipment\\index.vue", "mtime": 1755674552436}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCkBpbXBvcnQgIkAvdmlld3MvYnVzaW5lc3MvdmVoaWNsZUJhcnJpZXIvaW5kZXguc2NzcyI7DQoubXQyMCB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQp9DQo6OnYtZGVlcCB7DQogIC5lbC1kaWFsb2dfX2JvZHkgew0KICAgIHBhZGRpbmc6IDBweCAyMHB4IDMwcHg7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0gBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/vehicleBarrier/pJVehicleBarrier/barrierEquipment", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          label-width=\"130px\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"600px\"\r\n      @closed=\"closedDialog\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"dialogRef\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\n\r\nimport baseInfo from \"./dialog/baseInfo.vue\";\r\nimport importDialog from \"@/views/business/vehicleBarrier/components/import.vue\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/pJVehicleBarrier/mixins/export\";\r\n\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\nimport {\r\n  GetVBEquipList,\r\n  DelEquip,\r\n  ExportVBData,\r\n  GetDropList,\r\n  DownloadVBEquipTemplate,\r\n  ImportVBEquipDataStream,\r\n} from \"@/api/business/vehicleBarrier.js\";\r\nimport getAddress from \"./index.js\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nexport default {\r\n  Name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n    baseInfo,\r\n    importDialog,\r\n  },\r\n  mixins: [exportInfo, getAddress, addRouterPage],\r\n  data() {\r\n    return {\r\n      currentComponent: baseInfo,\r\n      componentsConfig: {\r\n        interfaceName: ImportVBEquipDataStream,\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"\",\r\n      tableSelection: [],\r\n      selectIds: [],\r\n      ruleForm: {\r\n        Name: \"\",\r\n        Brand: \"\",\r\n        Model: \"\",\r\n        Vender: \"\",\r\n        Status: \"\",\r\n        PurposeCatetory: \"\",\r\n        Scene: \"\",\r\n        Site: \"\",\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Name\", // 字段ID\r\n            label: \"设备名称\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            placeholder: \"请输入输入停车场名称\",\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n          },\r\n          {\r\n            key: \"Brand\",\r\n            label: \"品牌\",\r\n            type: \"select\",\r\n            options: [],\r\n            placeholder: \"请输入停车场地址\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"Model\",\r\n            label: \"规格型号\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"position\",\r\n            label: \"安装位置\",\r\n            type: \"cascader\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n              require: true,\r\n              disabled: false,\r\n              separator: \"-\",\r\n              props: {\r\n                label: \"Label\",\r\n                children: \"Children\",\r\n                value: \"Label\",\r\n              },\r\n            },\r\n            change: (e) => {\r\n              if (e.length > 0) {\r\n                this.ruleForm.PurposeCatetory = e[0];\r\n                this.ruleForm.Scene = e[1];\r\n                this.ruleForm.Site = e[2];\r\n              } else {\r\n                this.ruleForm.PurposeCatetory = \"\";\r\n                this.ruleForm.Scene = \"\";\r\n                this.ruleForm.Site = \"\";\r\n              }\r\n            },\r\n          },\r\n          {\r\n            key: \"Vender\",\r\n            label: \"供应商\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"Status\",\r\n            label: \"状态\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"新增\",\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: false, // 是否禁用\r\n              icon: \"\", //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n            {\r\n              text: \"下载模板\",\r\n              disabled: false, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData(\r\n                  [],\r\n                  \"道闸设备管理模板\",\r\n                  DownloadVBEquipTemplate\r\n                );\r\n              },\r\n            },\r\n            {\r\n              text: \"批量导入\",\r\n              disabled: false, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.currentComponent = \"importDialog\";\r\n                this.dialogVisible = true;\r\n                this.dialogTitle = \"批量导入\";\r\n              },\r\n            },\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData(this.ruleForm, \"道闸设备管理\", ExportVBData);\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: \"100%\",\r\n        tableActionsWidth: 220,\r\n        tableColumns: [\r\n          // {\r\n          //   width: 50,\r\n          //   otherOptions: {\r\n          //     type: 'selection',\r\n          //     align: 'center'\r\n          //   }\r\n          // },\r\n          {\r\n            label: \"设备名称\",\r\n            key: \"Name\",\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: \"设备编码\",\r\n            key: \"Code\",\r\n          },\r\n\r\n          {\r\n            label: \"品牌\",\r\n            key: \"Brand\",\r\n          },\r\n          {\r\n            label: \"规格型号\",\r\n            key: \"Model\",\r\n          },\r\n\r\n          {\r\n            label: \"所属出入口\",\r\n            key: \"EntranceName\",\r\n          },\r\n          {\r\n            label: \"安装位置\",\r\n            key: \"Address\",\r\n          },\r\n          {\r\n            label: \"供应商\",\r\n            key: \"Vender\",\r\n          },\r\n          {\r\n            label: \"供应商联系方式\",\r\n            key: \"VenderPhone\",\r\n          },\r\n          {\r\n            label: \"状态\",\r\n            key: \"StatusName\",\r\n          },\r\n          {\r\n            label: \"更新人\",\r\n            key: \"ModifyUserName\",\r\n          },\r\n          {\r\n            label: \"更新时间\",\r\n            key: \"ModifyDate\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false, // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, \"edit\");\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false, // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"查看详情\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.$router.push({\r\n                name: \"EquipmentView\",\r\n                query: { pg_redirect: this.$route.name, Id: row.Id },\r\n              });\r\n            },\r\n          },\r\n        ],\r\n        operateOptions: {\r\n          // width: 300 // 操作栏宽度\r\n        },\r\n      },\r\n      allSelectOption: [],\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/view\",\r\n          hidden: true,\r\n          component: () => import(\"./dialog/view.vue\"),\r\n          meta: { title: \"道闸设备详情\" },\r\n          name: \"EquipmentView\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  computed: {},\r\n  async created() {\r\n    this.customForm.formItems.find((item) => item.key == \"position\").options =\r\n      await this.getAddress();\r\n    await this.getDropList();\r\n\r\n    this.init();\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.position = [];\r\n      this.ruleForm.PurposeCatetory = \"\";\r\n      this.ruleForm.Scene = \"\";\r\n      this.ruleForm.Site = \"\";\r\n      this.onFresh();\r\n      this.getDropList();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n      this.getDropList();\r\n    },\r\n    init() {\r\n      this.fetchData();\r\n      this.getDropList();\r\n    },\r\n    // 获取搜索下啦\r\n    async getDropList() {\r\n      await GetDropList({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data;\r\n          this.allSelectOption = data;\r\n        } else {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: res.Message,\r\n          });\r\n        }\r\n      });\r\n\r\n      this.customForm.formItems.find((item) => item.key === \"Brand\").options =\r\n        await this.handelOption(\"Brand\");\r\n      this.customForm.formItems.find((item) => item.key === \"Model\").options =\r\n        await this.handelOption(\"Model\");\r\n      this.customForm.formItems.find((item) => item.key === \"Vender\").options =\r\n        await this.handelOption(\"Vender\");\r\n      this.customForm.formItems.find((item) => item.key === \"Status\").options =\r\n        await this.handelOption(\"Status\");\r\n    },\r\n\r\n    async handelOption(key) {\r\n      return await this.allSelectOption\r\n        .find((item) => item.Name === key)\r\n        .List.map((i) => {\r\n          return {\r\n            label: i.Value,\r\n            value: i.Key,\r\n          };\r\n        });\r\n    },\r\n    async fetchData() {\r\n      const res = await GetVBEquipList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data || [];\r\n        this.customTableConfig.total = res.Data.Total;\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.currentComponent = \"baseInfo\";\r\n      this.dialogTitle = \"新增\";\r\n      this.dialogVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.add();\r\n      });\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row);\r\n      console.log(this);\r\n      this.$confirm(\"确认删除?\", {\r\n        type: \"warning\",\r\n      })\r\n        .then(async (_) => {\r\n          const res = await DelEquip({\r\n            Id: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: \"删除成功\",\r\n              type: \"success\",\r\n            });\r\n            this.init();\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: \"error\",\r\n            });\r\n          }\r\n        })\r\n        .catch((_) => {\r\n          this.$message({\r\n            type: \"info\",\r\n            message: \"已取消删除\",\r\n          });\r\n        });\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type);\r\n      this.currentComponent = \"baseInfo\";\r\n      if (type === \"view\") {\r\n        this.dialogTitle = \"查看\";\r\n      } else if (type === \"edit\") {\r\n        this.dialogTitle = \"编辑\";\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.init(index, row, type);\r\n      });\r\n\r\n      this.dialogVisible = true;\r\n    },\r\n    // 关闭弹窗\r\n    closedDialog() {\r\n      this.$refs.dialogRef.closeClearForm();\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.onFresh();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.onFresh();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      const Ids = [];\r\n      this.tableSelection = selection;\r\n      this.tableSelection.forEach((item) => {\r\n        Ids.push(item.Id);\r\n      });\r\n      console.log(Ids);\r\n      this.selectIds = Ids;\r\n      console.log(this.tableSelection);\r\n      if (this.tableSelection.length > 0) {\r\n        this.customTableConfig.buttonConfig.buttonList.find(\r\n          (v) => v.key == \"batch\"\r\n        ).disabled = false;\r\n      } else {\r\n        this.customTableConfig.buttonConfig.buttonList.find(\r\n          (v) => v.key == \"batch\"\r\n        ).disabled = true;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n::v-deep {\r\n  .el-dialog__body {\r\n    padding: 0px 20px 30px;\r\n  }\r\n}\r\n</style>\r\n"]}]}