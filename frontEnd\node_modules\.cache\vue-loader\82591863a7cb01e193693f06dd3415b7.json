{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\assessmentManagement\\configurationAssessment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\assessmentManagement\\configurationAssessment\\index.vue", "mtime": 1755506574193}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/assessmentManagement/configurationAssessment", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport dialogView from \"./dialog/view.vue\";\r\n// import { downloadFile } from \"@/utils/downloadFile\";\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport {\r\n  GetPageList,\r\n  GetDetail,\r\n  Distribute,\r\n  ExportData,\r\n} from \"@/api/business/processDocIssuance\";\r\nimport { GetDictionaryDetailListByCode } from \"@/api/sys\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/mixins/export.js\";\r\n// import * as moment from 'moment'\r\n// import dayjs from \"dayjs\";\r\nimport {\r\n  GetBarrierPageList,\r\n  DelEquipment,\r\n  ExportBarrierEquipment,\r\n  ImportBarrierEquipment,\r\n} from \"@/api/business/vehicleBarrier.js\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  mixins: [exportInfo],\r\n  // mixins: [deviceTypeMixins, otherMixin],\r\n  data() {\r\n    return {\r\n      currentComponent: dialogView,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"\",\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        ExcuteStartTime: null,\r\n        ExcuteEndTime: null,\r\n        ExcuteDate: [],\r\n        FileName: \"\",\r\n        EquipName: \"\",\r\n        EquipCode: \"\",\r\n        CreateTime: [],\r\n        CreateStartTime: null,\r\n        CreateEndTime: null,\r\n        Status: \"\",\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Status\", // 字段ID\r\n            label: \"考核角色\", // Form的label\r\n            type: \"select\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            options: [\r\n              {\r\n                label: \"下发�?,\r\n                value: \"1\",\r\n              },\r\n              {\r\n                label: \"下发成功\",\r\n                value: \"2\",\r\n              },\r\n              {\r\n                label: \"下发失败\",\r\n                value: \"3\",\r\n              },\r\n            ],\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"添加配置\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.dialogVisible = true;\r\n                this.dialogTitle = \"添加配置\";\r\n                // this.handleAllExport();\r\n                // this.ExportData(this.ruleForm, \"工艺文件下发\", ExportData);\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: \"考核角色\",\r\n            key: \"DistributeDate\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"考核�?,\r\n            key: \"FileName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"总权�?,\r\n            key: \"EquipName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 200,\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.dialogVisible = true;\r\n              this.dialogTitle = \"编辑配置\";\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"移除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      deceiveTypeList: [],\r\n    };\r\n  },\r\n  computed: {},\r\n  mounted() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.ExcuteStartTime = null;\r\n      this.ruleForm.ExcuteEndTime = null;\r\n      this.ruleForm.CreateStartTime = null;\r\n      this.ruleForm.CreateEndTime = null;\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.GetDataList();\r\n    },\r\n    init() {\r\n      this.GetDataList();\r\n    },\r\n    async GetDataList() {\r\n      this.customTableConfig.loading = true;\r\n      let res = await GetPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      this.customTableConfig.loading = false;\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.Total;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    async getDictionaryDetailListByCode(dictionaryCode = \"deviceType\", Value) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode,\r\n      });\r\n      if (res.IsSucceed) {\r\n        const options = [{ label: \"全部\", value: \"\" }];\r\n        res.Data.map((item) => {\r\n          options.push({\r\n            label: item.Display_Name,\r\n            value: item[Value],\r\n            ...item,\r\n          });\r\n        });\r\n        return options;\r\n      }\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type);\r\n      this.dialogVisible = true;\r\n      if (type === \"view\") {\r\n        this.dialogTitle = \"查看\";\r\n        this.currentComponent = null;\r\n        this.componentsConfig = {\r\n          ID: row.Id,\r\n          disabled: true,\r\n          title: \"查看\",\r\n          ...row,\r\n        };\r\n      }\r\n      // else if (type === 'edit') {\r\n      //   this.dialogTitle = '编辑'\r\n      //   this.componentsConfig = {\r\n      //     ID: row.ID,\r\n      //     disabled: false,\r\n      //     title: '编辑'\r\n      //   }\r\n      // }\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row);\r\n      console.log(this);\r\n      this.$confirm(\"确认删除?\", {\r\n        type: \"warning\",\r\n      })\r\n        .then(async (_) => {\r\n          const res = await DelEquipment({\r\n            Id: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: \"删除成功\",\r\n              type: \"success\",\r\n            });\r\n            this.init();\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: \"error\",\r\n            });\r\n          }\r\n        })\r\n        .catch((_) => {\r\n          this.$message({\r\n            type: \"info\",\r\n            message: \"已取消删�?,\r\n          });\r\n        });\r\n    },\r\n    // async handleExport() {\r\n    //   console.log(this.ruleForm)\r\n    //   const res = await ExportDataList({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: false,\r\n    //     Ids: this.tableSelection.map((item) => item.Id),\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n    // async handleAllExport() {\r\n    //   const res = await ExportDataList({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: true,\r\n    //     Ids: [],\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n    // v2 版本导出\r\n    // async handleAllExport() {\r\n    //   const res = await ExportData({\r\n    //     ...this.ruleForm,\r\n    //   });\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res);\r\n    //     downloadFile(res.Data, \"21\");\r\n    //   } else {\r\n    //     this.$message.error(res.Message);\r\n    //   }\r\n    // },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.init();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.init();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}