<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import getGridByCode from '../../safetyManagement/mixins/index'
import DialogForm from './dialogForm.vue'

import { downloadFile } from '@/utils/downloadFile'
import dayjs from 'dayjs'

import {
  GetTaskPageList,
  UpdateTask,
  GetTypesByModule
} from '@/api/business/eventManagement'
export default {
  name: '',
  components: {
    CustomTable,
    CustomForm,
    CustomLayout
  },
  mixins: [getGridByCode],
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {
        Data: {}
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '编辑',
      tableSelection: [],
      ruleForm: {
        TaskType: '',
        TaskName: '',
        Status: '0',
        TaskBeg: null,
        TaskEnd: null
      },
      customForm: {
        formItems: [
          {
            key: 'TaskType',
            label: '任务类型',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'TaskName',
            label: '任务名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'Status',
            label: '任务状态',
            type: 'select',
            options: [
              {
                label: '全部',
                value: '0'
              },
              {
                label: '未完成',
                value: '1'
              },
              {
                label: '已完成 ',
                value: '2'
              },
              {
                label: '已超期',
                value: '3'
              },
              {
                label: '超期完成',
                value: '4'
              }
            ],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'Date', // 字段ID
            label: '任务开始时间', // Form的label
            type: 'datePicker', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              type: 'daterange',
              disabled: false,
              placeholder: '请输入...'
            },
            change: (e) => {
              // change事件
              console.log(e)
              if (e && e.length > 0) {
                this.ruleForm.TaskBeg = dayjs(e[0]).format('YYYY-MM-DD')
                this.ruleForm.TaskEnd = dayjs(e[1]).format('YYYY-MM-DD')
              }
            }
          }
        ],
        rules: {},
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: []
          //   {
          //     text: "导出",
          //     onclick: (item) => {
          //       console.log(item);
          //       this.handleExport();
          //     },
          //   },
          // ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            otherOptions: {
              type: 'selection',
              align: 'center',
              fixed: 'left'
            }
          },
          {
            label: '任务开始时间',
            key: 'BegTime',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '计划完成时间',
            key: 'EndTime',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '实际完成时间',
            key: 'DoneTime',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '任务状态',
            key: 'StatusDisplay',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '负责人',
            key: 'ActualReceiveUsersNames',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '任务名称',
            key: 'Name',
            otherOptions: {
              align: 'center'
            }
          },

          {
            label: '通知方式',
            key: 'MessageNoticeModeDisplay',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '任务类型',
            key: 'TaskType',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '来源',
            key: 'SourceName',
            width: 180,
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '业务模块',
            key: 'Module',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '操作人',
            key: 'ModifyUserName',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '备注',
            key: 'Remark',
            otherOptions: {
              align: 'center'
            }
          }
        ],
        tableData: [],
        operateOptions: {
          align: 'center',
          width: '180'
        },
        tableActions: [
          {
            actionLabel: '查看详情',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              // this.handleEdit(row);
              const platform = 'digitalfactory'
              const code = 'szgc'
              const id = '97b119f9-e634-4d95-87b0-df2433dc7893'
              let url = ''
              // if (row.Module == "能耗管理") {
              //   url = "/business/energy/alarmDetail";
              // } else if (row.Module == "车辆道闸") {
              //   url = "/bussiness/vehicle/alarm-info";
              // } else if (row.Module == "门禁管理") {
              //   url = "/business/AccessControlAlarmDetails";
              // } else if (row.Module == "安防管理") {
              //   url = "/business/equipmentAlarm";
              // } else if (row.Module == "危化品管理") {
              //   url = "/business/hazchem/alarmInformation";
              // } else
              if (row.Module == '环境管理') {
                url = '/business/environment/alarmInformation'
              } else if (row.Module == '访客管理') {
                url = '/business/visitorList'
                console.log('访客管理')
              }
              this.$qiankun.switchMicroAppFn(platform, code, id, url)
            }
          },
          {
            actionLabel: '编辑',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(row)
            }
          }
        ]
      }
    }
  },
  computed: {},
  created() {
    this.init()
    this.GetTypesByModule()
  },
  methods: {
    async GetTypesByModule() {
      const res = await GetTypesByModule({
        Type: '3',
        Module: ''
      })
      console.log(res, 'res')
      if (res.IsSucceed) {
        const result = res.Data || []
        const typeList = result.map((item) => ({
          value: item,
          label: item
        }))
        this.customForm.formItems.find(
          (item) => item.key == 'TaskType'
        ).options = typeList
      }
    },
    searchForm(data) {
      console.log(data)
      this.customTableConfig.currentPage = 1
      this.onFresh()
    },
    resetForm() {
      this.ruleForm.TaskBeg = null
      this.ruleForm.TaskEnd = null
      this.ruleForm.Date = null
      this.onFresh()
    },
    onFresh() {
      this.getTaskPageList()
    },

    init() {
      // this.getGridByCode("AccessControlAlarmDetails1");
      this.getTaskPageList()
    },
    async getTaskPageList() {
      const res = await GetTaskPageList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data
        this.customTableConfig.total = res.Data.TotalCount
        // if (this.customTableConfig.tableData.length > 0) {
        //   this.customTableConfig.tableData.map((v) => {
        //     v.Warning_First_Time =
        //       (v.Warning_First_Time ?? "") != ""
        //         ? dayjs(v.Warning_First_Time).format("YYYY-MM-DD HH:mm:ss")
        //         : "";
        //     v.Warning_Last_Time =
        //       (v.Warning_Last_Time ?? "") != ""
        //         ? dayjs(v.Warning_Last_Time).format("YYYY-MM-DD HH:mm:ss")
        //         : "";
        //   });
        // }
      } else {
        this.$message.error(res.Message)
      }
    },
    async handleExport() {
      const res = await ExportEntranceWarning({
        id: this.tableSelection.map((item) => item.Id).toString(),
        code: 'AccessControlAlarmDetails1'
      })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '告警明细数据')
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.getTaskPageList()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.getTaskPageList()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
    },
    handleEdit(row) {
      this.dialogVisible = true
      this.componentsConfig.Data = row
    }
  }
}
</script>

<style lang="scss" scoped>
.layout {
  height: calc(100vh - 90px);
  width: 100%;
}
</style>
