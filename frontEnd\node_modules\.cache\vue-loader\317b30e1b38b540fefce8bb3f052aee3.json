{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomLayout\\index.vue?vue&type=style&index=0&id=e93307e4&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomLayout\\index.vue", "mtime": 1755505758775}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouQ3VzdG9tTGF5b3V0IHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwogIC5zZWFyY2hGb3JtIHsKICAgIC8vIG1hcmdpbjogMTVweCAxNXB4IDBweCAxNXB4OwogICAgcGFkZGluZzogMTBweCAxNXB4OwogICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7CiAgfQogIC5sYXlvdXRDaGFydCB7CiAgICBtYXJnaW46IDE1cHggMTVweCAwcHggMTVweDsKICAgIHBhZGRpbmc6IDEwcHggMTVweDsKICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlOwogIH0KICAubGF5b3V0VGFibGUgewogICAgZmxleDogMTsKICAgIG1hcmdpbjogMTBweCAwIDAgMDsKICAgIHBhZGRpbmc6IDEwcHggMTVweDsKICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlOwogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/businessComponents/CustomLayout", "sourcesContent": ["<template>\n  <div class=\"CustomLayout\">\n    <div v-if=\"isShowSearchForm\" class=\"searchForm\">\n      <slot name=\"searchForm\" />\n    </div>\n    <div v-if=\"isShowLayoutChart\" class=\"layoutChart\">\n      <slot name=\"layoutChart\" />\n    </div>\n    <div class=\"layoutTable\">\n      <slot name=\"layoutTable\" />\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  // layoutConfig\n  props: {\n    layoutConfig: {\n      type: Object,\n      default: () => { }\n    }\n  },\n  data() {\n    return {}\n  },\n  computed: {\n    isShowSearchForm() {\n      return (this.layoutObj && this.layoutObj.isShowSearchForm) || true\n    },\n    isShowLayoutChart() {\n      return (this.layoutObj && this.layoutObj.isShowLayoutChart) || false\n    },\n    layoutObj() {\n      return this.layoutConfig\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.CustomLayout {\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  height: 100%;\n  .searchForm {\n    // margin: 15px 15px 0px 15px;\n    padding: 10px 15px;\n    background-color: white;\n  }\n  .layoutChart {\n    margin: 15px 15px 0px 15px;\n    padding: 10px 15px;\n    background-color: white;\n  }\n  .layoutTable {\n    flex: 1;\n    margin: 10px 0 0 0;\n    padding: 10px 15px;\n    background-color: white;\n  }\n}\n</style>\n"]}]}