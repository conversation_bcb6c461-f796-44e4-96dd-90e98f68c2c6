{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomLayout\\index.vue?vue&type=style&index=0&id=e93307e4&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomLayout\\index.vue", "mtime": 1755506092623}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLkN1c3RvbUxheW91dCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQogIC5zZWFyY2hGb3JtIHsNCiAgICBtYXJnaW46IDE1cHggMTVweCAwcHggMTVweDsNCiAgICBwYWRkaW5nOiAxMHB4IDE1cHg7DQogICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7DQogIH0NCiAgLmxheW91dENoYXJ0IHsNCiAgICBtYXJnaW46IDE1cHggMTVweCAwcHggMTVweDsNCiAgICBwYWRkaW5nOiAxMHB4IDE1cHg7DQogICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7DQogIH0NCiAgLmxheW91dFRhYmxlIHsNCiAgICBmbGV4OiAxOw0KICAgIG1hcmdpbjogMTBweCAxNXB4Ow0KICAgIHBhZGRpbmc6IDEwcHggMTVweDsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/businessComponents/CustomLayout", "sourcesContent": ["<template>\r\n  <div class=\"CustomLayout\">\r\n    <div class=\"searchForm\" v-if=\"isShowSearchForm\">\r\n      <slot name=\"searchForm\" />\r\n    </div>\r\n    <div class=\"layoutChart\" v-if=\"isShowLayoutChart\">\r\n      <slot name=\"layoutChart\" />\r\n    </div>\r\n    <div class=\"layoutTable\">\r\n      <slot name=\"layoutTable\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  // layoutConfig\r\n  props: {\r\n    layoutConfig: {\r\n      type: Object,\r\n      default: () => { },\r\n    },\r\n  },\r\n  data() {\r\n    return {};\r\n  },\r\n  computed: {\r\n    isShowSearchForm() {\r\n      return (this.layoutObj && this.layoutObj.isShowSearchForm) || true;\r\n    },\r\n    isShowLayoutChart() {\r\n      return (this.layoutObj && this.layoutObj.isShowLayoutChart) || false;\r\n    },\r\n    layoutObj() {\r\n      return this.layoutConfig;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.CustomLayout {\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n  .searchForm {\r\n    margin: 15px 15px 0px 15px;\r\n    padding: 10px 15px;\r\n    background-color: white;\r\n  }\r\n  .layoutChart {\r\n    margin: 15px 15px 0px 15px;\r\n    padding: 10px 15px;\r\n    background-color: white;\r\n  }\r\n  .layoutTable {\r\n    flex: 1;\r\n    margin: 10px 15px;\r\n    padding: 10px 15px;\r\n    background-color: white;\r\n  }\r\n}\r\n</style>\r\n"]}]}