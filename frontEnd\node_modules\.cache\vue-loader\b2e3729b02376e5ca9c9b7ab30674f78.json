{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\smartBroadcasting\\broadcastMediaFiles\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\smartBroadcasting\\broadcastMediaFiles\\index.vue", "mtime": 1755506574429}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/smartBroadcasting/broadcastMediaFiles", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div class=\"top\">\r\n      <CustomForm\r\n        :custom-form-items=\"customForm.formItems\"\r\n        :custom-form-buttons=\"customForm.customFormButtons\"\r\n        :value=\"ruleForm\"\r\n        :inline=\"true\"\r\n        :rules=\"customForm.rules\"\r\n        @submitForm=\"searchForm\"\r\n        @resetForm=\"resetForm\"\r\n      />\r\n    </div>\r\n    <div class=\"bottom\">\r\n      <div class=\"tableNotice\">\r\n        <span></span>\r\n        <span>数据更新时间�?{{ updateDate }}</span>\r\n        <!-- <span>数据更新时间�?{{ customTableConfig.tableData[0].UpdateDate }}</span> -->\r\n      </div>\r\n      <div class=\"tableBox\">\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\n\r\nimport dayjs from \"dayjs\";\r\nimport {\r\n  GetMediaFileList,\r\n  PostMediaFileDataList,\r\n} from \"@/api/business/smartBroadcasting\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      updateDate: \"\",\r\n      currentComponent: null,\r\n      componentsConfig: {\r\n        Data: {},\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.initData();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"编辑\",\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        FileName: \"\",\r\n        Date: [],\r\n        BeginCreateDate: null,\r\n        EndCreateDate: null,\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"FileName\",\r\n            label: \"文件名称\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Date\", // 字段ID\r\n            label: \"创建时间\", // Form的label\r\n            type: \"datePicker\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              type: \"daterange\",\r\n              disabled: false,\r\n              placeholder: \"请输�?..\",\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.BeginCreateDate = dayjs(e[0]).format(\r\n                  \"YYYY-MM-DD\"\r\n                );\r\n                this.ruleForm.EndCreateDate = dayjs(e[1]).format(\"YYYY-MM-DD\");\r\n              }\r\n            },\r\n          },\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"更新数据\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleResetData();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: \"文件名称\",\r\n            key: \"FileName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"播放时长\",\r\n            key: \"PlaybackTime\",\r\n            width: 140,\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"ID\",\r\n            key: \"MediaFileId\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"创建时间\",\r\n            key: \"CreateDate\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"创建来源\",\r\n            key: \"MediaSource\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: \"center\",\r\n          width: \"180\",\r\n        },\r\n        tableActions: [],\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    async handleResetData() {\r\n      const res = await PostMediaFileDataList({});\r\n      console.log(res, \"res\");\r\n      if (res.IsSucceed) {\r\n        this.initData();\r\n        this.$message({\r\n          type: \"success\",\r\n          message: \"更新成功\",\r\n        });\r\n      }\r\n    },\r\n\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1;\r\n      this.initData();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.BeginCreateDate = null;\r\n      this.ruleForm.EndCreateDate = null;\r\n      this.ruleForm.Date = null;\r\n      this.initData();\r\n    },\r\n\r\n    async initData() {\r\n      const res = await GetMediaFileList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n        if (res.Data.Data.length > 0) {\r\n          this.updateDate = res.Data.Data[0].UpdateDate || \"\";\r\n        }\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.initData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.initData();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleEdit(row) {\r\n      this.dialogVisible = true;\r\n      this.componentsConfig.Data = row;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.top {\r\n  margin: 15px 15px 0px 15px;\r\n  padding: 10px 15px;\r\n  background-color: white;\r\n}\r\n.bottom {\r\n  margin: 10px 15px;\r\n  padding: 10px 15px;\r\n  background-color: #fff;\r\n  height: calc(100vh - 190px);\r\n  .tableNotice {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 10px 15px;\r\n    color: rgba(34, 40, 52, 0.65);\r\n    font-size: 14px;\r\n  }\r\n  .tableBox {\r\n    height: calc(100vh - 240px);\r\n  }\r\n}\r\n</style>\r\n"]}]}