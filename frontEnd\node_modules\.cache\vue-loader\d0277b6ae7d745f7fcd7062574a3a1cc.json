{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\behaviorAnalysisAlarm\\index.vue?vue&type=style&index=0&id=f535bb2c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\behaviorAnalysisAlarm\\index.vue", "mtime": 1755504643813}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoubXQyMCB7CiAgbWFyZ2luLXRvcDogMTBweDsKfQoubGF5b3V0IHsKICAvLyBoZWlnaHQ6IGNhbGMoMTAwdmggLSA5MHB4KTsKICBvdmVyZmxvdzogYXV0bzsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+aA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/behaviorAnalysis/behaviorAnalysisAlarm", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        />\n      </template>\n    </CustomLayout>\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <component\n        :is=\"currentComponent\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n      /></el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\nimport dialogForm from './dialogForm.vue'\nimport dayjs from 'dayjs'\n\nimport {\n  GetBehaviorWarningList,\n  GetBehaviorWarningEntity,\n  TriggerBehaviorWarning\n} from '@/api/business/behaviorAnalysis'\nimport { GetDictionaryDetailListByCode } from '@/api/sys'\nexport default {\n  name: '',\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout\n  },\n  data() {\n    return {\n      currentComponent: dialogForm,\n      componentsConfig: {\n        Data: {}\n      },\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true\n        },\n        close: () => {\n          this.dialogVisible = false\n          this.onFresh()\n        }\n      },\n      dialogVisible: false,\n      dialogTitle: '告警详情',\n      tableSelection: [],\n      ruleForm: {\n        DeviceName: '',\n        WarningType: '',\n        HandleStatus: '',\n        Date: [],\n        BeginWarningTime: null,\n        EndWarningTime: null\n      },\n      customForm: {\n        formItems: [\n          {\n            key: 'Date', // 字段ID\n            label: '告警时间', // Form的label\n            type: 'datePicker', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              type: 'daterange',\n              disabled: false,\n              placeholder: '请输入...'\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n              if (e && e.length > 0) {\n                this.ruleForm.BeginWarningTime = dayjs(e[0]).format(\n                  'YYYY-MM-DD'\n                )\n                this.ruleForm.EndWarningTime = dayjs(e[1]).format('YYYY-MM-DD')\n              } else {\n                this.ruleForm.BeginWarningTime = null\n                this.ruleForm.EndWarningTime = null\n              }\n            }\n          },\n          {\n            key: 'WarningType',\n            label: '告警类型',\n            type: 'select',\n            options: [],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n              this.GetTypesByModule()\n            }\n          },\n          {\n            key: 'DeviceName',\n            label: '告警设备',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'HandleStatus',\n            label: '状态',\n            type: 'select',\n            options: [\n              {\n                label: '待广播',\n                value: '1'\n              },\n              {\n                label: '已广播',\n                value: '2'\n              },\n              {\n                label: '广播成功',\n                value: '3'\n              },\n              {\n                label: '广播失败',\n                value: '4'\n              },\n              {\n                label: '无需广播',\n                value: '5'\n              },\n              {\n                label: '无广播配置',\n                value: '6'\n              }\n            ],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          }\n        ],\n        rules: {},\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        buttonConfig: {\n          buttonList: []\n          //   {\n          //     text: \"批量关闭\",\n          //     onclick: (item) => {\n          //       console.log(item);\n          //       this.handleClose();\n          //     },\n          //   },\n          // ],\n        },\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        tableColumns: [\n          // {\n          //   otherOptions: {\n          //     type: \"selection\",\n          //     align: \"center\",\n          //     fixed: \"left\",\n          //   },\n          // },\n          {\n            label: '告警时间',\n            key: 'WarningTime',\n            otherOptions: {\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '告警类型',\n            key: 'WarningTypeDes',\n            width: 140,\n            otherOptions: {\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '告警设备',\n            key: 'DeviceName',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '告警设备编码',\n            key: 'DeviceCode',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '设备地址',\n            key: 'Position',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '联动广播',\n            key: 'BroadcastEquipmentCount',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '广播时间',\n            key: 'BroadcastTime',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '状态',\n            key: 'HandleStatus',\n            otherOptions: {\n              align: 'center'\n            },\n            render: (row) => {\n              if (row.HandleStatus == 1) {\n                return this.$createElement('span', {}, '待广播')\n              } else if (row.HandleStatus == 2) {\n                return this.$createElement('span', {}, '已广播')\n              } else if (row.HandleStatus == 3) {\n                return this.$createElement(\n                  'span',\n                  {\n                    style: {\n                      color: 'green'\n                    }\n                  },\n                  '广播成功'\n                )\n              } else if (row.HandleStatus == 4) {\n                return this.$createElement(\n                  'span',\n                  {\n                    style: {\n                      color: 'red'\n                    }\n                  },\n                  '广播失败'\n                )\n              } else if (row.HandleStatus == 5) {\n                return this.$createElement(\n                  'span',\n                  {},\n                  '无需广播'\n                )\n              } else if (row.HandleStatus == 6) {\n                return this.$createElement(\n                  'span',\n                  {},\n                  '无广播配置'\n                )\n              }\n              return this.$createElement('span', {}, '')\n            }\n          }\n        ],\n        tableData: [],\n        operateOptions: {\n          align: 'center',\n          width: '180'\n        },\n        tableActions: [\n          {\n            actionLabel: '查看详情',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.handleEdit(row)\n            }\n          },\n          {\n            actionLabel: '重新广播',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.handleRebroadcast(row)\n            }\n          }\n        ]\n      }\n    }\n  },\n  computed: {},\n  created() {\n    this.init()\n\n    this.getDictionaryDetailListByCode()\n  },\n  // mixins: [getGridByCode],\n  methods: {\n    async handleClose() {\n      const res = await SetWarningStatus({\n        Status: '2',\n        Ids: this.tableSelection.map((item) => item.Id)\n      })\n      if (res.IsSucceed) {\n        this.$message.success('操作成功')\n        this.onFresh()\n      }\n    },\n    async getDictionaryDetailListByCode() {\n      const res = await GetDictionaryDetailListByCode({\n        dictionaryCode: 'BehaviorWarningType'\n      })\n      if (res.IsSucceed) {\n        const result = res.Data || []\n        const warningType = result.map((item) => ({\n          value: item.Value,\n          label: item.Display_Name\n        }))\n        this.customForm.formItems.find(\n          (item) => item.key == 'WarningType'\n        ).options = warningType\n      }\n    },\n\n    searchForm(data) {\n      console.log(data)\n      this.customTableConfig.currentPage = 1\n      this.onFresh()\n    },\n    resetForm() {\n      this.ruleForm.BeginWarningTime = null\n      this.ruleForm.EndWarningTime = null\n      this.ruleForm.Date = null\n      this.onFresh()\n    },\n    onFresh() {\n      this.GetBehaviorWarningList()\n    },\n\n    init() {\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\n      this.GetBehaviorWarningList()\n    },\n    async GetBehaviorWarningList() {\n      const res = await GetBehaviorWarningList({\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n        ...this.ruleForm\n      })\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data\n        this.customTableConfig.total = res.Data.TotalCount\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n\n    async handleRebroadcast(row) {\n      const res = await TriggerBehaviorWarning({\n        ID: row.Id\n      })\n      if (res.IsSucceed) {\n        this.$message.success('操作成功')\n        this.init()\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.GetBehaviorWarningList()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前页: ${val}`)\n      this.customTableConfig.currentPage = val\n      this.GetBehaviorWarningList()\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection\n    },\n    handleEdit(row) {\n      this.dialogVisible = true\n      this.componentsConfig.Data = row\n      this.componentsConfig = {\n        type: 'edit',\n        data: row\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.mt20 {\n  margin-top: 10px;\n}\n.layout {\n  // height: calc(100vh - 90px);\n  overflow: auto;\n}\n</style>\n"]}]}