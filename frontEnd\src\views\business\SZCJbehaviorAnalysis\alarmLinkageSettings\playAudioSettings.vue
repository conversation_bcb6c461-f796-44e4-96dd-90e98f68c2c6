<template>
  <div class="app-container abs100">
    <el-card class="box-card">
      <CustomTable
        :custom-table-config="customTableConfig"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
        @handleSelectionChange="handleSelectionChange"
      />
    </el-card>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";
// import getGridByCode from "../../safetyManagement/mixins/index";
import playAudioSettingsDialogForm from "./playAudioSettingsDialogForm.vue";

import { downloadFile } from "@/utils/downloadFile";
import dayjs from "dayjs";
import {
  GetWarningSettingList,
  EditWarningSetting,
  GetWarningSettingEntity,
  DeleteWarningSetting,
} from "@/api/business/behaviorAnalysis";
export default {
  name: "",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
  },
  data() {
    return {
      currentComponent: null,
      componentsConfig: {
        Data: {},
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogVisible: false,
      dialogTitle: "编辑",
      tableSelection: [],

      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "新增",
              type: "primary",
              onclick: (item) => {
                console.log(item);
                this.handleCreate();
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            label: "报警类型",
            key: "WarningTypeDes",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "联动广播文件",
            key: "MediaName",
            otherOptions: {
              align: "center",
            },
            render: (row) => {
              if (row.MediaName == "文件失效") {
                return this.$createElement(
                  "span",
                  {
                    style: {
                      color: "red",
                    },
                  },
                  row.MediaName
                );
              }
              return this.$createElement("span", {}, row.MediaName);
            },
          },
        ],
        tableData: [],
        operateOptions: {
          align: "center",
          width: "180",
        },
        tableActions: [
          {
            actionLabel: "修改",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleEdit(row);
            },
          },
          {
            actionLabel: "删除",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleDelete(index, row);
            },
          },
        ],
      },
    };
  },
  computed: {},
  created() {
    this.init();
  },
  methods: {
    async handleClose() {
      const res = await SetWarningStatus({
        Status: "2",
        Ids: this.tableSelection.map((item) => item.Id),
      });
      if (res.IsSucceed) {
        this.$message.success("操作成功");
        this.onFresh();
      }
    },

    onFresh() {
      this.GetWarningSettingList();
    },

    init() {
      // this.getGridByCode("AccessControlAlarmDetails1");
      this.GetWarningSettingList();
    },
    async GetWarningSettingList() {
      const res = await GetWarningSettingList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data;
        this.customTableConfig.total = res.Data.TotalCount;
      } else {
        this.$message.error(res.Message);
      }
    },

    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.GetWarningSettingList();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.GetWarningSettingList();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
    handleCreate() {
      this.dialogTitle = "新增告警设置";
      this.componentsConfig = {
        type: "add",
      };
      this.dialogVisible = true;
      this.currentComponent = playAudioSettingsDialogForm;
    },
    handleEdit(row) {
      this.dialogVisible = true;
      this.dialogTitle = "编辑告警设置";
      this.currentComponent = playAudioSettingsDialogForm;
      this.componentsConfig = {
        type: "edit",
        data: row,
      };
    },
    handleDelete(index, row) {
      this.$confirm("请确认是否删除？", "删除", {
        type: "error",
      })
        .then(async (_) => {
          const res = await DeleteWarningSetting({
            ID: row.Id,
          });
          if (res.IsSucceed) {
            this.init();
          } else {
            this.$message.error(res.Message);
          }
        })
        .catch((_) => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
</style>
