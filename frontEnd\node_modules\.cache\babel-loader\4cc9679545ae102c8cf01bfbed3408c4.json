{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\vehiclesInThePark\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\vehiclesInThePark\\index.vue", "mtime": 1755674552437}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "VBInParkVehiclesGetInParkVehiclesList", "VBInParkVehiclesGetInParkStatistics", "VBInParkVehiclesVehicleLeaving", "VBInParkVehiclesExportData", "VBPassRecordGetDropList", "exportInfo", "addRouterPage", "<PERSON><PERSON>", "use", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "GridComponent", "LegendComponent", "TooltipComponent", "TitleComponent", "DataZoomComponent", "Name", "components", "mixins", "data", "_this", "pieOptionOption", "tooltip", "trigger", "legend", "show", "series", "name", "type", "radius", "avoidLabelOverlap", "label", "position", "labelLine", "ruleForm", "Number", "Date", "StartTime", "EndTime", "AccessType", "UserName", "VehicleType", "Timeout", "tableSelection", "customForm", "formItems", "key", "otherOptions", "clearable", "input", "e", "change", "rangeSeparator", "startPlaceholder", "endPlaceholder", "valueFormat", "length", "options", "value", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "disabled", "text", "onclick", "item", "console", "log", "ExportData", "pageSizeOptions", "currentPage", "pageSize", "total", "height", "tableColumns", "fixed", "tableData", "tableActionsWidth", "tableActions", "actionLabel", "index", "row", "$router", "push", "query", "pg_redirect", "$route", "Id", "handleLeave", "addPageArray", "path", "hidden", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "meta", "title", "inParkStatistics", "AccessPrecent", "TimeoutPrecent", "created", "vBPassRecordGetDropList", "mounted", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "JumpParams", "wrap", "_callee$", "_context", "prev", "next", "$qiankun", "getMicroAppJumpParamsFn", "isJump", "onFresh", "stop", "<PERSON><PERSON><PERSON><PERSON>", "setMicroAppJumpParamsFn", "methods", "vBInParkVehiclesGetInParkStatistics", "_this3", "_callee2", "res", "AccessTotal", "TimeoutTotal", "_callee2$", "_context2", "_objectSpread", "sent", "IsSucceed", "Data", "Access", "reduce", "accumulator", "currentValue", "parseInt", "Value", "VehiclesType", "map", "Key", "_this4", "_callee3", "_callee3$", "_context3", "find", "v", "List", "searchForm", "resetForm", "fetchData", "init", "_this5", "_callee4", "_callee4$", "_context4", "_this6", "_callee5", "_callee5$", "_context5", "Page", "PageSize", "Total", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "selection", "_this7", "$confirm", "dangerouslyUseHTMLString", "_ref", "_callee6", "_", "_callee6$", "_context6", "$message", "success", "error", "Message", "_x", "apply", "arguments", "catch"], "sources": ["src/views/business/vehicleBarrier/pJVehicleBarrier/vehiclesInThePark/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout\r\n      :layoutConfig=\"{\r\n        isShowLayoutChart: true,\r\n      }\"\r\n    >\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutChart>\r\n        <div class=\"progressBox\">\r\n          <div class=\"progressTwo\">\r\n            <div class=\"left\">\r\n              <span class=\"title\">当前在园车辆总数</span>\r\n              <div class=\"info\" style=\"height: 60.5px\">\r\n                <span class=\"colorActive textActive\">{{\r\n                  inParkStatistics.Total\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"progressTwo\">\r\n            <div class=\"left\">\r\n              <span class=\"title\">访问类型统计</span>\r\n              <div class=\"info\">\r\n                <span class=\"num\">\r\n                  <span\r\n                    :class=\"[index == 0 ? 'colorActive textActive' : '']\"\r\n                    v-for=\"(item, index) in inParkStatistics.Access\"\r\n                    :key=\"index\"\r\n                    >{{ index == 0 ? \"\" : \"/\" }} {{ item.Value }}</span\r\n                  >\r\n                </span>\r\n                <span class=\"text\">\r\n                  <span\r\n                    :class=\"[index == 0 ? 'colorActive' : '']\"\r\n                    v-for=\"(item, index) in inParkStatistics.Access\"\r\n                    :key=\"index\"\r\n                    >{{ index == 0 ? \"\" : \"/\" }} {{ item.Key }}</span\r\n                  >\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <el-progress\r\n              class=\"right\"\r\n              type=\"circle\"\r\n              :stroke-width=\"20\"\r\n              :percentage=\"AccessPrecent.toFixed(2)\"\r\n            ></el-progress>\r\n          </div>\r\n          <div class=\"progressTwo\">\r\n            <div class=\"left\">\r\n              <span class=\"title\">入园超时统计</span>\r\n              <div class=\"info\">\r\n                <span class=\"num\">\r\n                  <span\r\n                    :class=\"[index == 0 ? 'colorActive textActive' : '']\"\r\n                    v-for=\"(item, index) in inParkStatistics.Timeout\"\r\n                    :key=\"index\"\r\n                    >{{ index == 0 ? \"\" : \"/\" }} {{ item.Value }}</span\r\n                  >\r\n                </span>\r\n                <span class=\"text\">\r\n                  <span\r\n                    :class=\"[index == 0 ? 'colorActive' : '']\"\r\n                    v-for=\"(item, index) in inParkStatistics.Timeout\"\r\n                    :key=\"index\"\r\n                    >{{ index == 0 ? \"\" : \"/\" }} {{ item.Key }}</span\r\n                  >\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <el-progress\r\n              class=\"right\"\r\n              type=\"circle\"\r\n              :stroke-width=\"20\"\r\n              :percentage=\"TimeoutPrecent.toFixed(2)\"\r\n            ></el-progress>\r\n          </div>\r\n          <div class=\"progressTwo\">\r\n            <div class=\"left\">\r\n              <span class=\"title\">车辆类型统计</span>\r\n              <div class=\"info\">\r\n                <span\r\n                  style=\"color: #298dff; margin-bottom: 10px\"\r\n                  v-for=\"(item, index) in inParkStatistics.VehiclesType\"\r\n                  :key=\"index\"\r\n                  >{{ item.name }}: {{ item.value }}\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <v-chart\r\n              ref=\"pieChartRef\"\r\n              class=\"pieChartDom\"\r\n              :option=\"pieOptionOption\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport {\r\n  VBInParkVehiclesGetInParkVehiclesList,\r\n  VBInParkVehiclesGetInParkStatistics,\r\n  VBInParkVehiclesVehicleLeaving,\r\n  VBInParkVehiclesExportData,\r\n  VBPassRecordGetDropList,\r\n} from \"@/api/business/vehicleBarrier.js\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/mixins/export.js\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\n\r\nimport VChart from \"vue-echarts\";\r\nimport { use } from \"echarts/core\";\r\nimport { CanvasRenderer } from \"echarts/renderers\";\r\nimport { PieChart } from \"echarts/charts\";\r\nimport {\r\n  GridComponent,\r\n  LegendComponent,\r\n  TooltipComponent,\r\n  TitleComponent,\r\n  DataZoomComponent,\r\n} from \"echarts/components\";\r\nuse([\r\n  CanvasRenderer,\r\n  PieChart,\r\n  DataZoomComponent,\r\n  GridComponent,\r\n  LegendComponent,\r\n  TitleComponent,\r\n  TooltipComponent,\r\n]);\r\n\r\nexport default {\r\n  Name: \"vehiclePeerRecord\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n    VChart,\r\n  },\r\n  mixins: [exportInfo, addRouterPage],\r\n  data() {\r\n    return {\r\n      pieOptionOption: {\r\n        tooltip: {\r\n          trigger: \"item\",\r\n        },\r\n        legend: {\r\n          show: false,\r\n        },\r\n        series: [\r\n          {\r\n            name: \"车辆类型统计\",\r\n            type: \"pie\",\r\n            radius: [\"60%\", \"90%\"],\r\n            avoidLabelOverlap: false,\r\n            label: {\r\n              show: false,\r\n              position: \"center\",\r\n            },\r\n            labelLine: {\r\n              show: false,\r\n            },\r\n            data: [],\r\n            // color:['#298DFF']\r\n          },\r\n        ],\r\n      },\r\n      ruleForm: {\r\n        Number: \"\",\r\n        Date: [],\r\n        StartTime: null,\r\n        EndTime: null,\r\n        AccessType: \"\",\r\n        UserName: \"\",\r\n        VehicleType: \"\",\r\n        Timeout: \"\",\r\n      },\r\n\r\n      tableSelection: [],\r\n\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Number\", // 字段ID\r\n            label: \"车牌号码\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"Date\",\r\n            label: \"通行时间\",\r\n            type: \"datePicker\",\r\n            otherOptions: {\r\n              type: \"datetimerange\",\r\n              rangeSeparator: \"至\",\r\n              startPlaceholder: \"开始日期\",\r\n              endPlaceholder: \"结束日期\",\r\n              clearable: true,\r\n              valueFormat: \"yyyy-MM-dd HH:mm\",\r\n            },\r\n            change: (e) => {\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.StartTime = e[0];\r\n                this.ruleForm.EndTime = e[1];\r\n              } else {\r\n                this.ruleForm.StartTime = null;\r\n                this.ruleForm.EndTime = null;\r\n              }\r\n            },\r\n          },\r\n          {\r\n            key: \"VehicleType\",\r\n            label: \"车辆类型\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"UserName\",\r\n            label: \"车主姓名\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"AccessType\",\r\n            label: \"访问类型\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"Timeout\",\r\n            label: \"是否超时\",\r\n            type: \"select\",\r\n            options: [\r\n              {\r\n                label: \"是\",\r\n                value: true,\r\n              },\r\n              {\r\n                label: \"否\",\r\n                value: false,\r\n              },\r\n            ],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n        ],\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData(\r\n                  this.ruleForm,\r\n                  \"在园车俩\",\r\n                  VBInParkVehiclesExportData\r\n                );\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: \"100%\",\r\n        tableColumns: [\r\n          // {\r\n          //   width: 50,\r\n          //   otherOptions: {\r\n          //     type: 'selection',\r\n          //     align: 'center'\r\n          //   }\r\n          // },\r\n          {\r\n            label: \"驶入时间\",\r\n            key: \"PassTime\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"车牌号码\",\r\n            key: \"Number\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"车辆类型\",\r\n            key: \"VehicleType\",\r\n          },\r\n          {\r\n            label: \"车主姓名\",\r\n            key: \"UserName\",\r\n          },\r\n          {\r\n            label: \"车主联系方式\",\r\n            key: \"UserPhone\",\r\n          },\r\n          {\r\n            label: \"访问类型\",\r\n            key: \"AccessType\",\r\n          },\r\n          {\r\n            label: \"出入口\",\r\n            key: \"EntranceName\",\r\n          },\r\n          {\r\n            label: \"入园时长\",\r\n            key: \"ParkTime\",\r\n          },\r\n          {\r\n            label: \"超时时长\",\r\n            key: \"Timeout\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActionsWidth: 120,\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.$router.push({\r\n                name: \"vehiclesInTheParkView\",\r\n                query: { pg_redirect: this.$route.name, Id: row.Id },\r\n              });\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"车辆出园\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleLeave(row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/view\",\r\n          hidden: true,\r\n          component: () => import(\"./dialog/view.vue\"),\r\n          meta: { title: \"在园车辆详情\" },\r\n          name: \"vehiclesInTheParkView\",\r\n        },\r\n      ],\r\n      inParkStatistics: {},\r\n\r\n      AccessPrecent: 0,\r\n      TimeoutPrecent: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.vBPassRecordGetDropList();\r\n    // this.init();\r\n    // this.vBInParkVehiclesGetInParkStatistics();\r\n  },\r\n  async mounted() {\r\n    // 跳转设置默认参数\r\n    let JumpParams = this.$qiankun.getMicroAppJumpParamsFn();\r\n    if (JumpParams.isJump == \"true\") {\r\n      this.ruleForm.Timeout = JumpParams.Timeout == \"true\";\r\n    }\r\n    this.onFresh();\r\n  },\r\n  beforeDestroy() {\r\n    this.$qiankun.setMicroAppJumpParamsFn();\r\n    this.ruleForm.Timeout = null;\r\n  },\r\n  methods: {\r\n    async vBInParkVehiclesGetInParkStatistics() {\r\n      if (this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null;\r\n        this.ruleForm.EndTime = null;\r\n      }\r\n      let res = await VBInParkVehiclesGetInParkStatistics({\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.inParkStatistics = res.Data;\r\n        let AccessTotal = res.Data.Access.reduce(\r\n          (accumulator, currentValue) =>\r\n            accumulator + parseInt(currentValue.Value),\r\n          0\r\n        );\r\n        let TimeoutTotal = res.Data.Timeout.reduce(\r\n          (accumulator, currentValue) =>\r\n            accumulator + parseInt(currentValue.Value),\r\n          0\r\n        );\r\n        // let VehiclesTypeTotal = res.Data.VehiclesType.map(\r\n        //   (obj) => obj.value\r\n        // ).reduce(\r\n        //   (accumulator, currentValue) => accumulator + currentValue.Value,\r\n        //   0\r\n        // );\r\n        this.inParkStatistics.Access = res.Data.Access;\r\n        this.inParkStatistics.Timeout = res.Data.Timeout;\r\n        this.inParkStatistics.VehiclesType = res.Data.VehiclesType.map(\r\n          (item) => ({\r\n            value: item.Value,\r\n            name: item.Key,\r\n          })\r\n        );\r\n        this.pieOptionOption.series[0].data =\r\n          this.inParkStatistics.VehiclesType;\r\n\r\n        this.AccessPrecent =\r\n          (Number(res.Data.Access[0].Value) / AccessTotal) * 100;\r\n        this.TimeoutPrecent =\r\n          (Number(res.Data.Timeout[0].Value) / TimeoutTotal) * 100;\r\n        if (AccessTotal == 0) {\r\n          this.AccessPrecent = 0;\r\n        }\r\n        if (TimeoutTotal == 0) {\r\n          this.TimeoutPrecent = 0;\r\n        }\r\n      }\r\n    },\r\n    async vBPassRecordGetDropList() {\r\n      let res = await VBPassRecordGetDropList({});\r\n      if (res.IsSucceed) {\r\n        this.customForm.formItems.find((v) => v.key == \"VehicleType\").options =\r\n          res.Data.find((v) => v.Name == \"VehicleType\").List.map((item) => ({\r\n            label: item.Key,\r\n            value: item.Value,\r\n          }));\r\n        this.customForm.formItems.find((v) => v.key == \"AccessType\").options =\r\n          res.Data.find((v) => v.Name == \"AccessType\").List.map((item) => ({\r\n            label: item.Key,\r\n            value: item.Value,\r\n          }));\r\n      }\r\n    },\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n      this.vBInParkVehiclesGetInParkStatistics();\r\n    },\r\n    async init() {\r\n      await this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      if (this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null;\r\n        this.ruleForm.EndTime = null;\r\n      }\r\n      const res = await VBInParkVehiclesGetInParkVehiclesList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.Total;\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.onFresh();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.onFresh();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleLeave(row) {\r\n      this.$confirm(\r\n        '<div><div>是否确认进行车辆出园</div><div style=\"font-size:12px;color:#aaa\">车辆出园操作将标识该车为出园状态</div></div>',\r\n        \"操作确认\",\r\n        {\r\n          type: \"warning\",\r\n          dangerouslyUseHTMLString: true,\r\n        }\r\n      )\r\n        .then(async (_) => {\r\n          const res = await VBInParkVehiclesVehicleLeaving({\r\n            Id: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.$message.success(\"操作成功\");\r\n            this.init();\r\n          } else {\r\n            this.$message.error(res.Message);\r\n          }\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n\r\n.imgwapper {\r\n  width: 100px;\r\n  height: 100px;\r\n}\r\n.empty-img {\r\n  text-align: center;\r\n}\r\n.progressBox {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-around;\r\n  padding: 20px 0px;\r\n  .pieChartDom {\r\n    width: 126px;\r\n    height: 126px;\r\n  }\r\n  .progressTwo {\r\n    height: 126px;\r\n    display: flex;\r\n    flex-direction: row;\r\n    .left {\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      .title {\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n      }\r\n      .info {\r\n        display: flex;\r\n        flex-direction: column;\r\n        .num {\r\n          color: #aaa;\r\n          margin-bottom: 10px;\r\n        }\r\n        .text {\r\n          color: #aaa;\r\n        }\r\n        .colorActive {\r\n          color: #298dff;\r\n        }\r\n        .textActive {\r\n          font-size: 28px;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyHA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,SACAC,qCAAA,EACAC,mCAAA,EACAC,8BAAA,EACAC,0BAAA,EACAC,uBAAA,QACA;AACA,OAAAC,UAAA;AACA,OAAAC,aAAA;AAEA,OAAAC,MAAA;AACA,SAAAC,GAAA;AACA,SAAAC,cAAA;AACA,SAAAC,QAAA;AACA,SACAC,aAAA,EACAC,eAAA,EACAC,gBAAA,EACAC,cAAA,EACAC,iBAAA,QACA;AACAP,GAAA,EACAC,cAAA,EACAC,QAAA,EACAK,iBAAA,EACAJ,aAAA,EACAC,eAAA,EACAE,cAAA,EACAD,gBAAA,CACA;AAEA;EACAG,IAAA;EACAC,UAAA;IACAnB,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA,YAAA;IACAU,MAAA,EAAAA;EACA;EACAW,MAAA,GAAAb,UAAA,EAAAC,aAAA;EACAa,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,eAAA;QACAC,OAAA;UACAC,OAAA;QACA;QACAC,MAAA;UACAC,IAAA;QACA;QACAC,MAAA,GACA;UACAC,IAAA;UACAC,IAAA;UACAC,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAN,IAAA;YACAO,QAAA;UACA;UACAC,SAAA;YACAR,IAAA;UACA;UACAN,IAAA;UACA;QACA;MAEA;MACAe,QAAA;QACAC,MAAA;QACAC,IAAA;QACAC,SAAA;QACAC,OAAA;QACAC,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,OAAA;MACA;MAEAC,cAAA;MAEAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UAAA;UACAf,KAAA;UAAA;UACAH,IAAA;UAAA;UACAmB,YAAA;YACA;YACAC,SAAA;UACA;UACAC,KAAA,WAAAA,MAAAC,CAAA;UACAC,MAAA,WAAAA,OAAA;QACA,GACA;UACAL,GAAA;UACAf,KAAA;UACAH,IAAA;UACAmB,YAAA;YACAnB,IAAA;YACAwB,cAAA;YACAC,gBAAA;YACAC,cAAA;YACAN,SAAA;YACAO,WAAA;UACA;UACAJ,MAAA,WAAAA,OAAAD,CAAA;YACA,IAAAA,CAAA,IAAAA,CAAA,CAAAM,MAAA;cACApC,KAAA,CAAAc,QAAA,CAAAG,SAAA,GAAAa,CAAA;cACA9B,KAAA,CAAAc,QAAA,CAAAI,OAAA,GAAAY,CAAA;YACA;cACA9B,KAAA,CAAAc,QAAA,CAAAG,SAAA;cACAjB,KAAA,CAAAc,QAAA,CAAAI,OAAA;YACA;UACA;QACA,GACA;UACAQ,GAAA;UACAf,KAAA;UACAH,IAAA;UACA6B,OAAA;UACAV,YAAA;YACAC,SAAA;UACA;UACAG,MAAA,WAAAA,OAAAD,CAAA;QACA,GACA;UACAJ,GAAA;UACAf,KAAA;UACAH,IAAA;UACAmB,YAAA;YACAC,SAAA;UACA;UACAC,KAAA,WAAAA,MAAAC,CAAA;UACAC,MAAA,WAAAA,OAAA;QACA,GACA;UACAL,GAAA;UACAf,KAAA;UACAH,IAAA;UACA6B,OAAA;UACAV,YAAA;YACAC,SAAA;UACA;UACAG,MAAA,WAAAA,OAAAD,CAAA;QACA,GACA;UACAJ,GAAA;UACAf,KAAA;UACAH,IAAA;UACA6B,OAAA,GACA;YACA1B,KAAA;YACA2B,KAAA;UACA,GACA;YACA3B,KAAA;YACA2B,KAAA;UACA,EACA;UACAX,YAAA;YACAC,SAAA;UACA;UACAG,MAAA,WAAAA,OAAAD,CAAA;QACA,EACA;QACAS,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAlB,GAAA;YACAmB,QAAA;YAAA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAAC,IAAA;cACAC,OAAA,CAAAC,GAAA,CAAAF,IAAA;cACAhD,KAAA,CAAAmD,UAAA,CACAnD,KAAA,CAAAc,QAAA,EACA,QACA/B,0BACA;YACA;UACA;QAEA;QACA;QACAqE,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;QACAC,YAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA9C,KAAA;UACAe,GAAA;UACAC,YAAA;YACA+B,KAAA;UACA;QACA,GACA;UACA/C,KAAA;UACAe,GAAA;UACAC,YAAA;YACA+B,KAAA;UACA;QACA,GACA;UACA/C,KAAA;UACAe,GAAA;QACA,GACA;UACAf,KAAA;UACAe,GAAA;QACA,GACA;UACAf,KAAA;UACAe,GAAA;QACA,GACA;UACAf,KAAA;UACAe,GAAA;QACA,GACA;UACAf,KAAA;UACAe,GAAA;QACA,GACA;UACAf,KAAA;UACAe,GAAA;QACA,GACA;UACAf,KAAA;UACAe,GAAA;QACA,EACA;QACAiC,SAAA;QACAC,iBAAA;QACAC,YAAA,GACA;UACAC,WAAA;UACAnC,YAAA;YACAnB,IAAA;UACA;UACAuC,OAAA,WAAAA,QAAAgB,KAAA,EAAAC,GAAA;YACAhE,KAAA,CAAAiE,OAAA,CAAAC,IAAA;cACA3D,IAAA;cACA4D,KAAA;gBAAAC,WAAA,EAAApE,KAAA,CAAAqE,MAAA,CAAA9D,IAAA;gBAAA+D,EAAA,EAAAN,GAAA,CAAAM;cAAA;YACA;UACA;QACA,GACA;UACAR,WAAA;UACAnC,YAAA;YACAnB,IAAA;UACA;UACAuC,OAAA,WAAAA,QAAAgB,KAAA,EAAAC,GAAA;YACAhE,KAAA,CAAAuE,WAAA,CAAAP,GAAA;UACA;QACA;MAEA;MACAQ,YAAA,GACA;QACAC,IAAA,OAAAJ,MAAA,CAAAI,IAAA;QACAC,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAC,IAAA;UAAAC,KAAA;QAAA;QACA3E,IAAA;MACA,EACA;MACA4E,gBAAA;MAEAC,aAAA;MACAC,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,uBAAA;IACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,UAAA;MAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACA;YACAL,UAAA,GAAAL,MAAA,CAAAW,QAAA,CAAAC,uBAAA;YACA,IAAAP,UAAA,CAAAQ,MAAA;cACAb,MAAA,CAAA3E,QAAA,CAAAQ,OAAA,GAAAwE,UAAA,CAAAxE,OAAA;YACA;YACAmE,MAAA,CAAAc,OAAA;UAAA;UAAA;YAAA,OAAAN,QAAA,CAAAO,IAAA;QAAA;MAAA,GAAAX,OAAA;IAAA;EACA;EACAY,aAAA,WAAAA,cAAA;IACA,KAAAL,QAAA,CAAAM,uBAAA;IACA,KAAA5F,QAAA,CAAAQ,OAAA;EACA;EACAqF,OAAA;IACAC,mCAAA,WAAAA,oCAAA;MAAA,IAAAC,MAAA;MAAA,OAAAnB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAkB,SAAA;QAAA,IAAAC,GAAA,EAAAC,WAAA,EAAAC,YAAA;QAAA,OAAAtB,mBAAA,GAAAI,IAAA,UAAAmB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjB,IAAA,GAAAiB,SAAA,CAAAhB,IAAA;YAAA;cACA,IAAAU,MAAA,CAAA/F,QAAA,CAAAE,IAAA,CAAAoB,MAAA;gBACAyE,MAAA,CAAA/F,QAAA,CAAAG,SAAA;gBACA4F,MAAA,CAAA/F,QAAA,CAAAI,OAAA;cACA;cAAAiG,SAAA,CAAAhB,IAAA;cAAA,OACAtH,mCAAA,CAAAuI,aAAA,KACAP,MAAA,CAAA/F,QAAA,CACA;YAAA;cAFAiG,GAAA,GAAAI,SAAA,CAAAE,IAAA;cAGA,IAAAN,GAAA,CAAAO,SAAA;gBACAT,MAAA,CAAA1B,gBAAA,GAAA4B,GAAA,CAAAQ,IAAA;gBACAP,WAAA,GAAAD,GAAA,CAAAQ,IAAA,CAAAC,MAAA,CAAAC,MAAA,CACA,UAAAC,WAAA,EAAAC,YAAA;kBAAA,OACAD,WAAA,GAAAE,QAAA,CAAAD,YAAA,CAAAE,KAAA;gBAAA,GACA,CACA;gBACAZ,YAAA,GAAAF,GAAA,CAAAQ,IAAA,CAAAjG,OAAA,CAAAmG,MAAA,CACA,UAAAC,WAAA,EAAAC,YAAA;kBAAA,OACAD,WAAA,GAAAE,QAAA,CAAAD,YAAA,CAAAE,KAAA;gBAAA,GACA,CACA,GACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACAhB,MAAA,CAAA1B,gBAAA,CAAAqC,MAAA,GAAAT,GAAA,CAAAQ,IAAA,CAAAC,MAAA;gBACAX,MAAA,CAAA1B,gBAAA,CAAA7D,OAAA,GAAAyF,GAAA,CAAAQ,IAAA,CAAAjG,OAAA;gBACAuF,MAAA,CAAA1B,gBAAA,CAAA2C,YAAA,GAAAf,GAAA,CAAAQ,IAAA,CAAAO,YAAA,CAAAC,GAAA,CACA,UAAA/E,IAAA;kBAAA;oBACAV,KAAA,EAAAU,IAAA,CAAA6E,KAAA;oBACAtH,IAAA,EAAAyC,IAAA,CAAAgF;kBACA;gBAAA,CACA;gBACAnB,MAAA,CAAA5G,eAAA,CAAAK,MAAA,IAAAP,IAAA,GACA8G,MAAA,CAAA1B,gBAAA,CAAA2C,YAAA;gBAEAjB,MAAA,CAAAzB,aAAA,GACArE,MAAA,CAAAgG,GAAA,CAAAQ,IAAA,CAAAC,MAAA,IAAAK,KAAA,IAAAb,WAAA;gBACAH,MAAA,CAAAxB,cAAA,GACAtE,MAAA,CAAAgG,GAAA,CAAAQ,IAAA,CAAAjG,OAAA,IAAAuG,KAAA,IAAAZ,YAAA;gBACA,IAAAD,WAAA;kBACAH,MAAA,CAAAzB,aAAA;gBACA;gBACA,IAAA6B,YAAA;kBACAJ,MAAA,CAAAxB,cAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA8B,SAAA,CAAAX,IAAA;UAAA;QAAA,GAAAM,QAAA;MAAA;IACA;IACAvB,uBAAA,WAAAA,wBAAA;MAAA,IAAA0C,MAAA;MAAA,OAAAvC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAsC,SAAA;QAAA,IAAAnB,GAAA;QAAA,OAAApB,mBAAA,GAAAI,IAAA,UAAAoC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlC,IAAA,GAAAkC,SAAA,CAAAjC,IAAA;YAAA;cAAAiC,SAAA,CAAAjC,IAAA;cAAA,OACAnH,uBAAA;YAAA;cAAA+H,GAAA,GAAAqB,SAAA,CAAAf,IAAA;cACA,IAAAN,GAAA,CAAAO,SAAA;gBACAW,MAAA,CAAAzG,UAAA,CAAAC,SAAA,CAAA4G,IAAA,WAAAC,CAAA;kBAAA,OAAAA,CAAA,CAAA5G,GAAA;gBAAA,GAAAW,OAAA,GACA0E,GAAA,CAAAQ,IAAA,CAAAc,IAAA,WAAAC,CAAA;kBAAA,OAAAA,CAAA,CAAA1I,IAAA;gBAAA,GAAA2I,IAAA,CAAAR,GAAA,WAAA/E,IAAA;kBAAA;oBACArC,KAAA,EAAAqC,IAAA,CAAAgF,GAAA;oBACA1F,KAAA,EAAAU,IAAA,CAAA6E;kBACA;gBAAA;gBACAI,MAAA,CAAAzG,UAAA,CAAAC,SAAA,CAAA4G,IAAA,WAAAC,CAAA;kBAAA,OAAAA,CAAA,CAAA5G,GAAA;gBAAA,GAAAW,OAAA,GACA0E,GAAA,CAAAQ,IAAA,CAAAc,IAAA,WAAAC,CAAA;kBAAA,OAAAA,CAAA,CAAA1I,IAAA;gBAAA,GAAA2I,IAAA,CAAAR,GAAA,WAAA/E,IAAA;kBAAA;oBACArC,KAAA,EAAAqC,IAAA,CAAAgF,GAAA;oBACA1F,KAAA,EAAAU,IAAA,CAAA6E;kBACA;gBAAA;cACA;YAAA;YAAA;cAAA,OAAAO,SAAA,CAAA5B,IAAA;UAAA;QAAA,GAAA0B,QAAA;MAAA;IACA;IACAM,UAAA,WAAAA,WAAAzI,IAAA;MACA,KAAA2C,iBAAA,CAAAW,WAAA;MACAJ,OAAA,CAAAC,GAAA,CAAAnD,IAAA;MACA,KAAAwG,OAAA;IACA;IACAkC,SAAA,WAAAA,UAAA;MACA,KAAAlC,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAAmC,SAAA;MACA,KAAA9B,mCAAA;IACA;IACA+B,IAAA,WAAAA,KAAA;MAAA,IAAAC,MAAA;MAAA,OAAAlD,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAiD,SAAA;QAAA,OAAAlD,mBAAA,GAAAI,IAAA,UAAA+C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7C,IAAA,GAAA6C,SAAA,CAAA5C,IAAA;YAAA;cAAA4C,SAAA,CAAA5C,IAAA;cAAA,OACAyC,MAAA,CAAAF,SAAA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAAvC,IAAA;UAAA;QAAA,GAAAqC,QAAA;MAAA;IACA;IACAH,SAAA,WAAAA,UAAA;MAAA,IAAAM,MAAA;MAAA,OAAAtD,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAqD,SAAA;QAAA,IAAAlC,GAAA;QAAA,OAAApB,mBAAA,GAAAI,IAAA,UAAAmD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjD,IAAA,GAAAiD,SAAA,CAAAhD,IAAA;YAAA;cACA,IAAA6C,MAAA,CAAAlI,QAAA,CAAAE,IAAA,CAAAoB,MAAA;gBACA4G,MAAA,CAAAlI,QAAA,CAAAG,SAAA;gBACA+H,MAAA,CAAAlI,QAAA,CAAAI,OAAA;cACA;cAAAiI,SAAA,CAAAhD,IAAA;cAAA,OACAvH,qCAAA,CAAAwI,aAAA;gBACAgC,IAAA,EAAAJ,MAAA,CAAAtG,iBAAA,CAAAW,WAAA;gBACAgG,QAAA,EAAAL,MAAA,CAAAtG,iBAAA,CAAAY;cAAA,GACA0F,MAAA,CAAAlI,QAAA,CACA;YAAA;cAJAiG,GAAA,GAAAoC,SAAA,CAAA9B,IAAA;cAKA,IAAAN,GAAA,CAAAO,SAAA;gBACA0B,MAAA,CAAAtG,iBAAA,CAAAiB,SAAA,GAAAoD,GAAA,CAAAQ,IAAA,CAAAA,IAAA;gBACAyB,MAAA,CAAAtG,iBAAA,CAAAa,KAAA,GAAAwD,GAAA,CAAAQ,IAAA,CAAA+B,KAAA;cACA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAA3C,IAAA;UAAA;QAAA,GAAAyC,QAAA;MAAA;IACA;IACAM,gBAAA,WAAAA,iBAAAC,GAAA;MACAvG,OAAA,CAAAC,GAAA,iBAAAuG,MAAA,CAAAD,GAAA;MACA,KAAA9G,iBAAA,CAAAY,QAAA,GAAAkG,GAAA;MACA,KAAAjD,OAAA;IACA;IACAmD,mBAAA,WAAAA,oBAAAF,GAAA;MACAvG,OAAA,CAAAC,GAAA,wBAAAuG,MAAA,CAAAD,GAAA;MACA,KAAA9G,iBAAA,CAAAW,WAAA,GAAAmG,GAAA;MACA,KAAAjD,OAAA;IACA;IACAoD,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAArI,cAAA,GAAAqI,SAAA;IACA;IACArF,WAAA,WAAAA,YAAAP,GAAA;MAAA,IAAA6F,MAAA;MACA,KAAAC,QAAA,CACA,iGACA,QACA;QACAtJ,IAAA;QACAuJ,wBAAA;MACA,CACA,EACAjF,IAAA;QAAA,IAAAkF,IAAA,GAAAtE,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAqE,SAAAC,CAAA;UAAA,IAAAnD,GAAA;UAAA,OAAApB,mBAAA,GAAAI,IAAA,UAAAoE,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAlE,IAAA,GAAAkE,SAAA,CAAAjE,IAAA;cAAA;gBAAAiE,SAAA,CAAAjE,IAAA;gBAAA,OACArH,8BAAA;kBACAwF,EAAA,EAAAN,GAAA,CAAAM;gBACA;cAAA;gBAFAyC,GAAA,GAAAqD,SAAA,CAAA/C,IAAA;gBAGA,IAAAN,GAAA,CAAAO,SAAA;kBACAuC,MAAA,CAAAQ,QAAA,CAAAC,OAAA;kBACAT,MAAA,CAAAlB,IAAA;gBACA;kBACAkB,MAAA,CAAAQ,QAAA,CAAAE,KAAA,CAAAxD,GAAA,CAAAyD,OAAA;gBACA;cAAA;cAAA;gBAAA,OAAAJ,SAAA,CAAA5D,IAAA;YAAA;UAAA,GAAAyD,QAAA;QAAA,CACA;QAAA,iBAAAQ,EAAA;UAAA,OAAAT,IAAA,CAAAU,KAAA,OAAAC,SAAA;QAAA;MAAA,KACAC,KAAA,WAAAV,CAAA;IACA;EACA;AACA", "ignoreList": []}]}