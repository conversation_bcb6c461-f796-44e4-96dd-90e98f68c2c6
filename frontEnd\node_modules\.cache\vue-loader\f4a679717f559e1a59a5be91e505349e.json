{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\workshopBulletinBoard\\pjProductionConfiguration\\index.vue?vue&type=style&index=0&id=36d989da&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\workshopBulletinBoard\\pjProductionConfiguration\\index.vue", "mtime": 1755674552441}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoubXQyMCB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQp9DQo6OnYtZGVlcCAuZWwtdGFibGVfX2ZpeGVkLWJvZHktd3JhcHBlciB7DQogIHRvcDogNDBweCAhaW1wb3J0YW50Ow0KfQ0KLmxheW91dHsNCiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gOTBweCk7DQogIG92ZXJmbG93OiBhdXRvOw0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/workshopBulletinBoard/pjProductionConfiguration", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      top=\"10vh\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n    <!-- 导入弹窗 -->\r\n    <!-- <dialogImport ref=\"dialogImport\" /> -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { parseTime } from \"@/utils\";\r\n// import { baseUrl } from '@/utils/baseurl'\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport DialogForm from \"./components/dialogTable.vue\";\r\nimport dialogImport from \"./components/dialogImport.vue\";\r\n// import { downloadFile } from '@/utils/downloadFile'\r\nimport { GetPreferenceSettingValue } from \"@/api/sys/system-setting\";\r\nimport { GetGridByCode } from \"@/api/sys\";\r\nimport { RoleAuthorization } from \"@/api/user\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nimport {\r\n  GetPageList,\r\n  DeleteEntity,\r\n} from \"@/api/business/productionConfiguration\";\r\n// import { divide } from 'xe-utils'\r\nexport default {\r\n  name: \"ProductionConfiguration\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/add\",\r\n          hidden: true,\r\n          component: () => import(\"./components/add.vue\"),\r\n          name: \"ProductionConfigurationAdd\",\r\n          meta: { title: `新增` },\r\n        },\r\n        {\r\n          path: this.$route.path + \"/edit\",\r\n          hidden: true,\r\n          component: () => import(\"./components/add.vue\"),\r\n          name: \"ProductionConfigurationEdit\",\r\n          meta: { title: `编辑` },\r\n        },\r\n        {\r\n          path: this.$route.path + \"/view\",\r\n          hidden: true,\r\n          component: () => import(\"./components/add.vue\"),\r\n          name: \"ProductionConfigurationView\",\r\n          meta: { title: `查看` },\r\n        },\r\n      ],\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"\",\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Board_Name: \"\",\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Board_Name\", // 字段ID\r\n            label: \"看板名称\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            placeholder: \"请输入看板\",\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"新增\",\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: false, // 是否禁用\r\n              icon: \"\", //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 60,\r\n            label: \"序号\",\r\n            otherOptions: {\r\n              type: \"index\",\r\n              align: \"center\",\r\n            },\r\n          },\r\n          //   {\r\n          //     label: '报表编码',\r\n          //     key: 'Code'\r\n          //   },\r\n          //   {\r\n          //     label: '报表名称',\r\n          //     key: 'Name'\r\n          //   },\r\n          //   {\r\n          //     label: '报表创建时间',\r\n          //     key: 'Create_Date'\r\n          //   },\r\n          //   {\r\n          //     label: '报表创建人',\r\n          //     key: 'Create_UserName'\r\n          //   }\r\n        ],\r\n        tableData: [],\r\n        tableActionsWidth: 240,\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleView2(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"复制链接\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleCopy(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"导入数据\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleImport(index, row);\r\n            },\r\n          },\r\n        ],\r\n        otherOptions: {},\r\n      },\r\n      roleAuthorizationList: [],\r\n      Big_Screen_Url:'',\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.getBaseData();\r\n    this.init();\r\n  },\r\n  activated() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    getBaseData() {\r\n      // 获取表格配置\r\n      GetGridByCode({ code: \"production_configuration_list\" }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data.ColumnList.map((item) => {\r\n            const temp = {\r\n              label: item.Display_Name,\r\n              key: item.Code,\r\n            };\r\n            if (item.Code === \"Ids\") {\r\n              temp.render = (row) => {\r\n                return this.$createElement(\r\n                  \"el-button\",\r\n                  {\r\n                    attrs: {\r\n                      type: \"text\",\r\n                    },\r\n                    on: {\r\n                      click: (val) => {\r\n                        this.handleView(val, row);\r\n                      },\r\n                    },\r\n                  },\r\n                  \"查看\"\r\n                );\r\n              };\r\n            }\r\n            return temp;\r\n          });\r\n          this.customTableConfig.tableColumns.push(...data);\r\n        } else {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: res.Message,\r\n          });\r\n        }\r\n      });\r\n      /**\r\n     *  menuType: 2, //1PC 2app\r\n        roleType: 3, //1菜单权限，2列权限 ，3按钮权限\r\n     */\r\n      RoleAuthorization({\r\n        workObjId: localStorage.getItem(\"Last_Working_Object_Id\"),\r\n        menuId: this.$route.meta.Id,\r\n        menuType: 1,\r\n        roleType: 3,\r\n        sign: 10,\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.roleAuthorizationList = res.Data;\r\n          this.customTableConfig.tableActions[1].otherOptions.disabled =\r\n            !this.roleAuthorizationList.find(\r\n              (item) => item.display_name === \"编辑\"\r\n            ).is_enabled;\r\n          this.customTableConfig.tableActions[2].otherOptions.disabled =\r\n            !this.roleAuthorizationList.find(\r\n              (item) => item.display_name === \"删除\"\r\n            ).is_enabled;\r\n          this.customTableConfig.tableActions[4].otherOptions.disabled =\r\n            !this.roleAuthorizationList.find(\r\n              (item) => item.display_name === \"导入数据\"\r\n            ).is_enabled;\r\n        } else {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: res.Message,\r\n          });\r\n        }\r\n      });\r\n\r\n      GetPreferenceSettingValue({\r\n        Code: \"Big_screen\",\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.Big_Screen_Url = res.Data;\r\n        }\r\n      });\r\n    },\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.getPageList();\r\n    },\r\n    init() {\r\n      this.getPageList();\r\n    },\r\n    async getPageList() {\r\n      const res = await GetPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => {\r\n          item.Modify_Date = item.Modify_Date\r\n            ? parseTime(new Date(item.Modify_Date), \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n            : \"\";\r\n          item.Create_Date = item.Create_Date\r\n            ? parseTime(new Date(item.Create_Date), \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n            : \"\";\r\n          // item.Is_Push = item.Is_Push ? '是' : '否'\r\n          return item;\r\n        });\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n      } else {\r\n        this.$message({\r\n          type: \"error\",\r\n          message: res.Message,\r\n        });\r\n      }\r\n    },\r\n    // 新增\r\n    handleCreate() {\r\n      //   this.dialogTitle = '新增'\r\n      //   this.dialogVisible = true\r\n      this.$router.push({\r\n        name: \"ProductionConfigurationAdd\",\r\n        query: { pg_redirect: this.$route.name, type: 1 },\r\n      });\r\n      // this.$qiankun.switchMicroAppFn(\r\n      //   \"project\",\r\n      //   \"szdn\",\r\n      //   \"1cf6f8ac-d9d0-4b18-9959-5e4ef37886f4\",\r\n      //   `/business/workshop/productionConfiguration/add?pg_redirect=${this.$route.name}&type=1`\r\n      // );\r\n    },\r\n    // 查看绑定设备明细\r\n    handleView(index, row) {\r\n      console.log(index, row);\r\n      this.currentComponent = DialogForm;\r\n      this.dialogTitle = \"查看绑定设备明细\";\r\n      this.dialogVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.currentComponent.initView(row.Equipment_Ids);\r\n      });\r\n    },\r\n    // 查看看板\r\n    handleView2(index, row) {\r\n      window.open(\r\n        `${this.Big_Screen_Url}/pjProductionBoard/index?id=${\r\n          row.Id\r\n        }&tenant=${localStorage.getItem(\"tenant\")}`,\r\n        \"_blank\"\r\n      );\r\n      // window.open(\r\n      //   `${process.env.VUE_APP_SCREEN_URL}/productionBoard/index?id=${\r\n      //     row.Id\r\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`,\r\n      //   \"_blank\"\r\n      // );\r\n      // if (process.env.NODE_ENV === \"development\") {\r\n      //   window.open(\r\n      //     `http://localhost:5173/productionBoard/index?id=${\r\n      //       row.Id\r\n      //     }&tenant=${localStorage.getItem(\"tenant\")}`,\r\n      //     \"_blank\"\r\n      //   );\r\n      // } else {\r\n      //   window.open(\r\n      //     `http://wnpzgc-test.bimtk.com/productionBoard/index?id=${\r\n      //       row.Id\r\n      //     }&tenant=${localStorage.getItem(\"tenant\")}`,\r\n      //     \"_blank\"\r\n      //   );\r\n      // }\r\n    },\r\n    // 编辑\r\n    handleEdit(index, row) {\r\n      console.log(index, row);\r\n      this.$router.push({\r\n        name: \"ProductionConfigurationAdd\",\r\n        query: { pg_redirect: this.$route.name, type: 2, id: row.Id },\r\n      });\r\n    },\r\n    // 复制链接\r\n    handleCopy(index, row) {\r\n      // console.log(\r\n      //   process.env.VUE_APP_SCREEN_URL,\r\n      //   \"process.env.VUE_APP_SCREEN_URL\"\r\n      // );\r\n      const textareaEle = document.createElement(\"textarea\");\r\n      // if (process.env.NODE_ENV === \"development\") {\r\n      //   textareaEle.value = `http://localhost:5173/productionBoard/index?id=${\r\n      //     row.Id\r\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`;\r\n      // } else {\r\n      //   textareaEle.value = `http://wnpzgc-test.bimtk.com/productionBoard/index?id=${\r\n      //     row.Id\r\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`;\r\n      // }\r\n      textareaEle.value = `${this.Big_Screen_Url}/pjProductionBoard/index?id=${\r\n        row.Id\r\n      }&tenant=${localStorage.getItem(\"tenant\")}`;\r\n\r\n      document.body.appendChild(textareaEle);\r\n      textareaEle.select();\r\n      document.execCommand(\"copy\");\r\n      document.body.removeChild(textareaEle);\r\n      this.$message({\r\n        type: \"success\",\r\n        message: \"已成功复制到截切板\",\r\n      });\r\n    },\r\n    // 导入数据\r\n    handleImport(index, row) {\r\n      console.log(index, row);\r\n      this.currentComponent = dialogImport;\r\n      this.dialogTitle = \"导入数据\";\r\n      this.dialogVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.currentComponent.init(row);\r\n      });\r\n    },\r\n    // 删除\r\n    handleDelete(index, row) {\r\n      console.log(index, row);\r\n      this.$confirm(\"确认删除？\", {\r\n        type: \"warning\",\r\n      })\r\n        .then(async (_) => {\r\n          const res = await DeleteEntity({\r\n            id: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: \"success\",\r\n              message: \"删除成功\",\r\n            });\r\n            this.init();\r\n          } else {\r\n            this.$message({\r\n              type: \"error\",\r\n              message: res.Message,\r\n            });\r\n          }\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.init();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.init();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n  <style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n::v-deep .el-table__fixed-body-wrapper {\r\n  top: 40px !important;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n\r\n"]}]}