{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\systemSettings\\messageCenter\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\systemSettings\\messageCenter\\index.vue", "mtime": 1755674552434}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/systemSettings/messageCenter", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n// import getGridByCode from \"../../safetyManagement/mixins/index\";\r\n// import DialogForm from \"./dialogForm.vue\";\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport dayjs from 'dayjs'\r\nimport {\r\n  GetWBMessageList,\r\n  GetMessageType,\r\n  GetPublishUnitList\r\n} from '@/api/business/eventManagement'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: null,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '编辑',\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        Title: '',\r\n        MessageType: '',\r\n        Source: '',\r\n        CreateUser: '',\r\n        ReceiveUserName: '',\r\n        StartTime: null,\r\n        EndTime: null,\r\n        Date: []\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Title',\r\n            label: '消息标题',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          // {\r\n          //   key: \"MessageType\",\r\n          //   label: \"事件类型\",\r\n          //   type: \"select\",\r\n          //   options: [],\r\n          //   otherOptions: {\r\n          //     clearable: true,\r\n          //   },\r\n          //   change: (e) => {\r\n          //     // change事件\r\n          //     console.log(e);\r\n          //     // this.GetTypesByModule();\r\n          //   },\r\n          // },\r\n          {\r\n            key: 'MessageType',\r\n            label: '消息类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Source',\r\n            label: '来源',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'CreateUser',\r\n            label: '创建人',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'ReceiveUserName',\r\n            label: '接收人',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Date', // 字段ID\r\n            label: '发送时间', // Form的label\r\n            type: 'datePicker', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              type: 'daterange',\r\n              disabled: false,\r\n              placeholder: '请输入...'\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.StartTime = dayjs(e[0]).format('YYYY-MM-DD')\r\n                this.ruleForm.EndTime = dayjs(e[1]).format('YYYY-MM-DD')\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        loading: false,\r\n        buttonConfig: {\r\n          buttonList: [\r\n            // {\r\n            //   text: \"批量关闭\",\r\n            //   onclick: (item) => {\r\n            //     console.log(item);\r\n            //     this.handleClose();\r\n            //   },\r\n            // },\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center',\r\n              fixed: 'left'\r\n            }\r\n          },\r\n          {\r\n            label: '消息标题',\r\n            key: 'Title',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '事件类型',\r\n            key: 'EventTypeName',\r\n            width: 140,\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '来源',\r\n            key: 'SourceName',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '业务模块',\r\n            key: 'Module',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '接收方',\r\n            key: 'ReceiveUser',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '创建人',\r\n            key: 'SendUserName',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '发送时间',\r\n            key: 'SendTime',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center',\r\n          width: '180'\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              // this.handleEdit(row);\r\n              const platform = 'digitalfactory'\r\n              const code = 'szgc'\r\n              const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\r\n              let url = ''\r\n              if (row.Module == '能耗管理') {\r\n                url = '/business/energy/alarmDetail'\r\n              } else if (row.Module == '车辆道闸') {\r\n                url = '/bussiness/vehicle/alarm-info'\r\n              } else if (row.Module == '门禁管理') {\r\n                url = '/business/AccessControlAlarmDetails'\r\n              } else if (row.Module == '安防管理') {\r\n                url = '/business/equipmentAlarm'\r\n              } else if (row.Module == '危化品管理') {\r\n                url = '/business/hazchem/alarmInformation'\r\n              } else if (row.Module == '环境管理') {\r\n                url = '/business/environment/alarmInformation'\r\n              } else if (row.Module == '访客管理') {\r\n                url = '/business/energy/alarmDetail'\r\n                console.log('访客管理')\r\n              }\r\n              this.$qiankun.switchMicroAppFn(platform, code, id, url)\r\n            }\r\n          }\r\n          // {\r\n          //   actionLabel: \"编辑\",\r\n          //   otherOptions: {\r\n          //     type: \"text\",\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleEdit(row);\r\n          //   },\r\n          // },\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init()\r\n\r\n    this.GetMessageType()\r\n    this.getPublishUnitList()\r\n  },\r\n  methods: {\r\n    async handleClose() {\r\n      const res = await SetWarningStatus({\r\n        Status: '2',\r\n        Ids: this.tableSelection.map((item) => item.Id)\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.$message.success('操作成功')\r\n        this.onFresh()\r\n      }\r\n    },\r\n    async getPublishUnitList() {\r\n      const res = await GetPublishUnitList({})\r\n      if (res.IsSucceed) {\r\n        const result = res.Data || []\r\n        this.customForm.formItems.find(\r\n          (item) => item.key == 'Source'\r\n        ).options = result.map((item) => ({\r\n          value: item.Id,\r\n          label: item.Name\r\n        }))\r\n      }\r\n    },\r\n    async GetMessageType() {\r\n      const res = await GetMessageType({})\r\n      console.log(res, 'res')\r\n      if (res.IsSucceed) {\r\n        const result = res.Data || []\r\n        const typeList = result.map((item) => ({\r\n          value: item.Value,\r\n          label: item.Name\r\n        }))\r\n        console.log(typeList, 'typeList')\r\n        this.customForm.formItems.find((item) => item.key == 'MessageType').options =\r\n        typeList\r\n      }\r\n    },\r\n\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.StartTime = null\r\n      this.ruleForm.EndTime = null\r\n      this.ruleForm.Date = null\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.GetWBMessageList()\r\n    },\r\n\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.GetWBMessageList()\r\n    },\r\n    async GetWBMessageList() {\r\n      this.customTableConfig.loading = true\r\n      const res = await GetWBMessageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      }).finally(() => {\r\n        this.customTableConfig.loading = false\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    async handleExport() {\r\n      const res = await ExportEntranceWarning({\r\n        id: this.tableSelection.map((item) => item.Id).toString(),\r\n        code: 'AccessControlAlarmDetails1'\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '告警明细数据')\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.GetWBMessageList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.GetWBMessageList()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    },\r\n    handleEdit(row) {\r\n      this.dialogVisible = true\r\n      this.componentsConfig.Data = row\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}