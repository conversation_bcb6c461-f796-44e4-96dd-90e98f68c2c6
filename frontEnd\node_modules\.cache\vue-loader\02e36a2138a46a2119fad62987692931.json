{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\systemSettings\\messageCenter\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\systemSettings\\messageCenter\\index.vue", "mtime": 1755506574440}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/systemSettings/messageCenter", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        />\n      </template>\n    </CustomLayout>\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <component\n        :is=\"currentComponent\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n      /></el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\n// import getGridByCode from \"../../safetyManagement/mixins/index\";\n// import DialogForm from \"./dialogForm.vue\";\n\nimport { downloadFile } from '@/utils/downloadFile'\nimport dayjs from 'dayjs'\nimport {\n  GetWBMessageList,\n  GetMessageType,\n  GetPublishUnitList\n} from '@/api/business/eventManagement'\nexport default {\n  name: '',\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout\n  },\n  data() {\n    return {\n      currentComponent: null,\n      componentsConfig: {\n        Data: {}\n      },\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true\n        },\n        close: () => {\n          this.dialogVisible = false\n          this.onFresh()\n        }\n      },\n      dialogVisible: false,\n      dialogTitle: '编辑',\n      tableSelection: [],\n      ruleForm: {\n        Title: '',\n        MessageType: '',\n        Source: '',\n        CreateUser: '',\n        ReceiveUserName: '',\n        StartTime: null,\n        EndTime: null,\n        Date: []\n      },\n      customForm: {\n        formItems: [\n          {\n            key: 'Title',\n            label: '消息标题',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          // {\n          //   key: \"MessageType\",\n          //   label: \"事件类型\",\n          //   type: \"select\",\n          //   options: [],\n          //   otherOptions: {\n          //     clearable: true,\n          //   },\n          //   change: (e) => {\n          //     // change事件\n          //     console.log(e);\n          //     // this.GetTypesByModule();\n          //   },\n          // },\n          {\n            key: 'MessageType',\n            label: '消息类型',\n            type: 'select',\n            options: [],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'Source',\n            label: '来源',\n            type: 'select',\n            options: [],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'CreateUser',\n            label: '创建�?,\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'ReceiveUserName',\n            label: '接收�?,\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'Date', // 字段ID\n            label: '发送时�?, // Form的label\n            type: 'datePicker', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              type: 'daterange',\n              disabled: false,\n              placeholder: '请输�?..'\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n              if (e && e.length > 0) {\n                this.ruleForm.StartTime = dayjs(e[0]).format('YYYY-MM-DD')\n                this.ruleForm.EndTime = dayjs(e[1]).format('YYYY-MM-DD')\n              }\n            }\n          }\n        ],\n        rules: {},\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        loading: false,\n        buttonConfig: {\n          buttonList: [\n            // {\n            //   text: \"批量关闭\",\n            //   onclick: (item) => {\n            //     console.log(item);\n            //     this.handleClose();\n            //   },\n            // },\n          ]\n        },\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        tableColumns: [\n          {\n            otherOptions: {\n              type: 'selection',\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '消息标题',\n            key: 'Title',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '事件类型',\n            key: 'EventTypeName',\n            width: 140,\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '来源',\n            key: 'SourceName',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '业务模块',\n            key: 'Module',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '接收�?,\n            key: 'ReceiveUser',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '创建�?,\n            key: 'SendUserName',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '发送时�?,\n            key: 'SendTime',\n            otherOptions: {\n              align: 'center'\n            }\n          }\n        ],\n        tableData: [],\n        operateOptions: {\n          align: 'center',\n          width: '180'\n        },\n        tableActions: [\n          {\n            actionLabel: '查看详情',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              // this.handleEdit(row);\n              const platform = 'digitalfactory'\n              const code = 'szgc'\n              const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\n              let url = ''\n              if (row.Module == '能耗管�?) {\n                url = '/business/energy/alarmDetail'\n              } else if (row.Module == '车辆道闸') {\n                url = '/bussiness/vehicle/alarm-info'\n              } else if (row.Module == '门禁管理') {\n                url = '/business/AccessControlAlarmDetails'\n              } else if (row.Module == '安防管理') {\n                url = '/business/equipmentAlarm'\n              } else if (row.Module == '危化品管�?) {\n                url = '/business/hazchem/alarmInformation'\n              } else if (row.Module == '环境管理') {\n                url = '/business/environment/alarmInformation'\n              } else if (row.Module == '访客管理') {\n                url = '/business/energy/alarmDetail'\n                console.log('访客管理')\n              }\n              this.$qiankun.switchMicroAppFn(platform, code, id, url)\n            }\n          }\n          // {\n          //   actionLabel: \"编辑\",\n          //   otherOptions: {\n          //     type: \"text\",\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handleEdit(row);\n          //   },\n          // },\n        ]\n      }\n    }\n  },\n  computed: {},\n  created() {\n    this.init()\n\n    this.GetMessageType()\n    this.getPublishUnitList()\n  },\n  methods: {\n    async handleClose() {\n      const res = await SetWarningStatus({\n        Status: '2',\n        Ids: this.tableSelection.map((item) => item.Id)\n      })\n      if (res.IsSucceed) {\n        this.$message.success('操作成功')\n        this.onFresh()\n      }\n    },\n    async getPublishUnitList() {\n      const res = await GetPublishUnitList({})\n      if (res.IsSucceed) {\n        const result = res.Data || []\n        this.customForm.formItems.find(\n          (item) => item.key == 'Source'\n        ).options = result.map((item) => ({\n          value: item.Id,\n          label: item.Name\n        }))\n      }\n    },\n    async GetMessageType() {\n      const res = await GetMessageType({})\n      console.log(res, 'res')\n      if (res.IsSucceed) {\n        const result = res.Data || []\n        const typeList = result.map((item) => ({\n          value: item.Value,\n          label: item.Name\n        }))\n        console.log(typeList, 'typeList')\n        this.customForm.formItems.find((item) => item.key == 'MessageType').options =\n        typeList\n      }\n    },\n\n    searchForm(data) {\n      console.log(data)\n      this.onFresh()\n    },\n    resetForm() {\n      this.ruleForm.StartTime = null\n      this.ruleForm.EndTime = null\n      this.ruleForm.Date = null\n      this.onFresh()\n    },\n    onFresh() {\n      this.GetWBMessageList()\n    },\n\n    init() {\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\n      this.GetWBMessageList()\n    },\n    async GetWBMessageList() {\n      this.customTableConfig.loading = true\n      const res = await GetWBMessageList({\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n        ...this.ruleForm\n      }).finally(() => {\n        this.customTableConfig.loading = false\n      })\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data\n        this.customTableConfig.total = res.Data.TotalCount\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n    async handleExport() {\n      const res = await ExportEntranceWarning({\n        id: this.tableSelection.map((item) => item.Id).toString(),\n        code: 'AccessControlAlarmDetails1'\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        downloadFile(res.Data, '告警明细数据')\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.GetWBMessageList()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`)\n      this.customTableConfig.currentPage = val\n      this.GetWBMessageList()\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection\n    },\n    handleEdit(row) {\n      this.dialogVisible = true\n      this.componentsConfig.Data = row\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.mt20 {\n  margin-top: 10px;\n}\n.layout{\n  height: calc(100vh - 90px);\n  overflow: auto;\n}\n</style>\n"]}]}