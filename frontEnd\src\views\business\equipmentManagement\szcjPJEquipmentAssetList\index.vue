<template>
  <div class="app-container abs100 szcjPJEquipmentAssetList">
    <el-row :gutter="12">
      <el-col :span="6">
        <el-card shadow="never">
          <div class="card_content">
            <div class="left">
              <span class="num">{{ getDeviceValue("设备总数") }}</span>
              <span>设备总数 </span>
            </div>
            <div class="right">
              <img src="@/assets/<EMAIL>" alt="">
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-row :gutter="12">
          <el-col :span="24">
            <el-card shadow="never">
              <div class="card_second_content">
                <div class="left" style="color: #4ebf8b">
                  <span class="num">{{ getDeviceValue("在线") }}</span>
                  <div class="textInfo">
                    <span>在线 </span>
                    <span class="textStyle">{{
                      getDevicePrecentValue("在线")
                    }}</span>
                  </div>
                </div>
                <div class="right">
                  <img src="@/assets/<EMAIL>" alt="">
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-top: 10px">
          <el-col :span="24">
            <el-card shadow="never">
              <div class="card_second_content">
                <div class="left" style="color: #1bb5e0">
                  <span class="num">{{ getDeviceValue("正常") }}</span>
                  <div class="textInfo">
                    <span>正常 </span>
                    <span class="textStyle">{{
                      getDevicePrecentValue("正常")
                    }}</span>
                  </div>
                </div>
                <div class="right">
                  <img src="@/assets/<EMAIL>" alt="">
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="6">
        <el-row :gutter="12">
          <el-col :span="24">
            <el-card shadow="never">
              <div class="card_second_content">
                <div class="left" style="color: #68748a">
                  <span class="num">{{ getDeviceValue("离线") }}</span>
                  <div class="textInfo">
                    <span>离线 </span>
                    <span class="textStyle">{{
                      getDevicePrecentValue("离线")
                    }}</span>
                  </div>
                </div>
                <div class="right">
                  <img src="@/assets/<EMAIL>" alt="">
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-top: 10px">
          <el-col :span="24">
            <el-card shadow="never">
              <div class="card_second_content">
                <div class="left" style="color: #ff5f7a">
                  <span class="num">{{ getDeviceValue("故障") }}</span>
                  <div class="textInfo">
                    <div>
                      <span>故障 </span>
                      <el-popover
                        placement="top-start"
                        title=""
                        width=""
                        trigger="hover"
                        content="对设备发起报修单记为设备故障"
                      >
                        <img
                          slot="reference"
                          class="popinfo"
                          src="@/assets/<EMAIL>"
                          alt=""
                        >
                      </el-popover>
                    </div>
                    <span class="textStyle">{{
                      getDevicePrecentValue("故障")
                    }}</span>
                  </div>
                </div>
                <div class="right">
                  <img src="@/assets/<EMAIL>" alt="">
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="6">
        <el-row :gutter="12">
          <el-col :span="24">
            <el-card shadow="never">
              <div class="card_second_content">
                <div class="left" style="color: #ff902c">
                  <span class="num">{{ getDeviceValue("异常") }}</span>
                  <div class="textInfo">
                    <div>
                      <span>异常 </span>
                      <el-popover
                        placement="top-start"
                        title=""
                        width=""
                        trigger="hover"
                        content="设备所传的一切非正常状态均记为异常"
                      >
                        <img
                          slot="reference"
                          class="popinfo"
                          src="@/assets/<EMAIL>"
                          alt=""
                        >
                      </el-popover>
                    </div>
                    <span class="textStyle">{{
                      getDevicePrecentValue("异常")
                    }}</span>
                  </div>
                </div>
                <div class="right">
                  <img src="@/assets/<EMAIL>" alt="">
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-top: 10px">
          <el-col :span="24">
            <el-card shadow="never">
              <div class="card_second_content">
                <div class="left" style="color: #6754d2">
                  <span class="num">{{ getDeviceValue("维修中") }}</span>
                  <div class="textInfo">
                    <span>维修中 </span>
                    <span class="textStyle">{{
                      getDevicePrecentValue("维修中")
                    }}</span>
                  </div>
                </div>
                <div class="right">
                  <img src="@/assets/<EMAIL>" alt="">
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-row :gutter="12" style="margin-top: 10px; flex: 1; height: 0;">
      <el-col :span="3" style="height: 100%">
        <el-card
          shadow="never"
          class="tree_card"
          style="height: 100%; padding: 0"
        >
          <el-tree
            ref="positionTreeRef"
            v-loading="positionTreeLoading"
            class="positionTreeClass"
            style="height: calc(100vh - 340px); overflow-y: auto"
            :data="positionTree"
            :props="defaultProps"
            node-key="Id"
            :expand-on-click-node="false"
            :default-expanded-keys="defaultExpandedKeys"
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>
      <el-col :span="21">
        <el-row :gutter="12">
          <el-col :span="24">
            <el-card shadow="never">
              <CustomForm
                :custom-form-items="customForm.formItems"
                :custom-form-buttons="customForm.customFormButtons"
                :value="ruleForm"
                :inline="true"
                :rules="customForm.rules"
                @submitForm="submitForm"
                @resetForm="resetForm"
              />
            </el-card>
          </el-col>
        </el-row>

        <el-row
          :gutter="12"
          style="
            margin-top: 10px;
            height: calc(100vh - 430px);
            overflow-y: auto;
          "
          class="list_class"
        >
          <el-col :span="24" style="height: 100%">
            <div v-loading="deviceListLoading" style="height: 100%">
              <div v-if="deviceList.length > 0" class="list_box">
                <div
                  v-for="(item, index) in deviceList"
                  :key="index"
                  class="list_item"
                  @click="nextRouteDetail(item)"
                >
                  <div class="list_logo">
                    <el-image
                      v-if="item.isHaveUrl"
                      style="width: auto; height: 100%"
                      :src="item.Url"
                      fit="cover"
                    >
                      <!-- :preview-src-list="[item.Url]" -->
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline" />
                      </div>
                    </el-image>

                    <el-image
                      v-if="!item.isHaveUrl"
                      style="width: auto; height: 100%"
                      :src="item.Url"
                      fit="cover"
                    >
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline" />
                      </div>
                    </el-image>
                  </div>
                  <div class="list_info">
                    <span class="title">{{ item.Name || "-" }}</span>
                    <div class="info">
                      <span class="label">位置</span>
                      <span class="value">{{ item.Postion || "-" }}</span>
                    </div>
                    <div class="info">
                      <span class="label">品牌</span>
                      <span class="value">{{ item.Brand || "-" }}</span>
                    </div>
                    <div class="action">
                      <div class="tags">
                        <div
                          v-for="(statusItem, statusIndex) in item.Status"
                          :key="statusIndex"
                          class="tags_item"
                          :style="{
                            background: getTagsStyle(statusItem).background,
                          }"
                        >
                          <span
                            v-if="statusItem == '在线'"
                            :style="{
                              color: getTagsStyle(statusItem).color,
                            }"
                          >{{ statusItem }}</span>
                          <span
                            v-if="statusItem == '正常'"
                            :style="{
                              color: getTagsStyle(statusItem).color,
                            }"
                          >{{ statusItem }}</span>
                          <span
                            v-if="statusItem == '离线'"
                            :style="{
                              color: getTagsStyle(statusItem).color,
                            }"
                          >{{ statusItem }}</span>
                          <span
                            v-if="statusItem == '故障'"
                            :style="{
                              color: getTagsStyle(statusItem).color,
                            }"
                          >{{ statusItem }}</span>
                          <span
                            v-if="statusItem == '异常'"
                            :style="{
                              color: getTagsStyle(statusItem).color,
                            }"
                          >{{ statusItem }}</span>
                          <span
                            v-if="statusItem == '维修中'"
                            :style="{
                              color: getTagsStyle(statusItem).color,
                            }"
                          >{{ statusItem }}</span>
                          <!-- <span>{{ statusItem }}</span>
                        <span>{{ statusItem }}</span> -->
                        </div>
                      </div>
                      <div class="right">
                        <span>查看</span>
                        <i class="el-icon-arrow-right" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="list_no_box">
                <div class="no_content">
                  <img src="@/assets/<EMAIL>" alt="">
                  <span>无相关设备信息</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import nopictures from '@/assets/<EMAIL>'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import editDialog from '@/views/business/maintenanceAndUpkeep/workOrderManagement/editDialog.vue'
import {
  GetDeviceStatus,
  GetDeviceList,
  GetPostionTreeList,
  GetDictionaryDetailListByParentId
} from '@/api/business/eqptAsset'
import { GetOssUrl, GetDictionaryDetailListByCode } from '@/api/sys/index'
import { getDictionary } from '@/utils/common'
import addRouterPage from '@/mixins/add-router-page'

export default {
  name: 'SzcjPJEquipmentAssetList',
  components: {
    editDialog,
    CustomForm
  },
  mixins: [addRouterPage],
  data() {
    return {
      tagsStyle: [
        {
          text: '在线',
          color: 'rgba(78, 191, 139, 1)',
          background: 'rgba(78, 191, 139, 0.1)'
        },
        {
          text: '正常',
          color: 'rgba(27, 181, 224, 1)',
          background: 'rgba(27, 181, 224, .1)'
        },
        {
          text: '离线',
          color: 'rgba(104, 116, 138, 1)',
          background: 'rgba(104, 116, 138, .1)'
        },
        {
          text: '故障',
          color: 'rgba(255, 95, 122, 1)',
          background: 'rgba(255, 95, 122, .1)'
        },
        {
          text: '异常',
          color: 'rgba(255, 144, 44, 1)',
          background: 'rgba(255, 144, 44, .1)'
        },
        {
          text: '维修中',
          color: 'rgba(103, 84, 210, 1)',
          background: 'rgba(103, 84, 210, .1)'
        }
      ],
      ruleForm: {
        Display_Name: '',
        Brand: '',
        Device_Type_Id: '',
        Device_Type_Detail_Id: '',
        Postion: ''
      },
      customForm: {
        formItems: [
          {
            key: 'Display_Name',
            label: '设备名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'Brand',
            label: '设备品牌',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Device_Type_Id',
            label: '设备类型',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              this.customForm.formItems.find(
                (v) => v.key === 'Device_Type_Detail_Id'
              ).options = []
              this.ruleForm.Device_Type_Detail_Id = ''
              GetDictionaryDetailListByParentId(e).then((res) => {
                this.customForm.formItems.find(
                  (v) => v.key === 'Device_Type_Detail_Id'
                ).options = res.Data.map((v) => {
                  return {
                    label: v.Display_Name,
                    value: v.Id
                  }
                })
              })
            }
          },
          {
            key: 'Device_Type_Detail_Id',
            label: '设备子类',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'Status',
            label: '设备状态',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'WhereOrderStatus',
            label: '维修状态',
            type: 'select',
            options: [
              {
                label: '正常',
                value: '3'
              },
              {
                label: '维修中',
                value: '1'
              },
              {
                label: '故障',
                value: '0'
              }
            ],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          }
        ],
        rules: {},
        customFormButtons: {
          marginLeft: '50px',
          submitName: '搜索',
          submitShow: true,
          resetShow: true,
          resetName: '重置'
        }
      },
      // 设备状态
      deviceStatusList: [],
      // 位置树
      positionTree: [],
      // 设备列表
      deviceList: [],
      deviceListLoading: false,
      positionTreeLoading: false,
      defaultProps: {
        children: 'Children',
        label: 'Name',
        value: 'Id'
      },
      defaultExpandedKeys: [],
      addPageArray: [
        {
          path: this.$route.path + '/equipmentData',
          hidden: true,
          component: () => import('./equipmentData.vue'),
          name: 'PJEquipmentData',
          meta: { title: `设备数采` }
        }
      ]
    }
  },
  activated() {},
  beforeDestroy() {},
  created() {
    this.getDeviceStatus()
    this.getPostionTreeList()
    this.getDeviceList()

    getDictionary('deviceType').then((res) => {
      const item = this.customForm.formItems.find(
        (v) => v.key === 'Device_Type_Id'
      )
      item.options = res.map((v) => {
        return {
          label: v.Display_Name,
          value: v.Id
        }
      })
    })

    GetDictionaryDetailListByCode({
      dictionaryCode: 'MonitorAudioStatus'
    }).then((res) => {
      const item = this.customForm.formItems.find((v) => v.key === 'Status')
      item.options = res.Data.map((v) => {
        return {
          label: v.Display_Name,
          value: v.Value
        }
      })
    })
  },
  mounted() {},
  methods: {
    getTagsStyle(name) {
      return this.tagsStyle.find((item) => item.text == name)
    },
    nextRouteDetail(data) {
      data.num = 1
      data.historyRouter = this.$route.name
      this.$router.push({
        name: 'PJEquipmentData',
        query: { pg_redirect: this.$route.name, Id: data.Id }
      })
      this.$store.dispatch('eqpt/changeEqptData', data)
    },

    async getDeviceStatus() {
      const res = await GetDeviceStatus({})
      if (res.IsSucceed) {
        console.log(res, '1221')
        this.deviceStatusList = res.Data.map((item) => ({
          ...item,
          percent: ((item.Value / res.Data[0].Value) * 100).toFixed(0) + '%'
        }))
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
    },
    getDeviceValue(name) {
      if (this.deviceStatusList.length > 0) {
        return this.deviceStatusList.find((item) => item.Label == name).Value
      }
      return ''
    },
    getDevicePrecentValue(name) {
      if (this.deviceStatusList.length > 0) {
        return this.deviceStatusList.find((item) => item.Label == name).percent
      }
      return ''
    },

    async getPostionTreeList() {
      this.positionTreeLoading = true
      const res = await GetPostionTreeList({})
      if (res.IsSucceed) {
        this.positionTreeLoading = false
        this.defaultExpandedKeys = res.Data.map((v) => v.Id)
        this.positionTree = res.Data
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
    },

    async getDeviceList() {
      this.deviceList = []
      // this.deviceListLoading = true
      const res = await GetDeviceList({
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        // this.userTableData.map(async item => {
        //   item.ImgUrl = await GetOssUrl({ url: item.ImgUrl }).then(res => {
        //     return res.Data
        //   })
        // })
        this.deviceList = res.Data.map((item) => {
          if (item.Url) {
            return {
              ...item,
              isHaveUrl: true
            }
          } else {
            return {
              ...item,
              isHaveUrl: false,
              Url: nopictures
            }
          }
        })
        this.deviceListLoading = false
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
        this.deviceListLoading = false
      }
    },
    submitForm() {
      this.getDeviceList()
    },
    resetForm() {
      this.getDeviceList()
    },

    async handleNodeClick(data, node) {
      const parents = await this.findParentIds(this.positionTree, data.Id)
      const newNode = [
        ...parents,
        {
          Id: data.Id,
          Name: data.Name
        }
      ]
      this.ruleForm.Postion = newNode.map((v) => v.Name).join('/')
      this.getDeviceList()
    },
    findParentIds(tree, targetId) {
      const parentNodes = [] // 存储唯一的父节点ID和Name

      // 辅助函数，用于检查父节点是否已存在于数组中
      function parentNodeExists(id, name, array) {
        return array.some((node) => node.Id === id && node.Name === name)
      }

      function traverse(nodes, parentId, parentName) {
        if (!nodes) return false
        for (const node of nodes) {
          if (node.Id === targetId) {
            // 如果当前节点是目标节点，并且它有父节点，则添加父节点信息（避免重复）
            if (
              parentId !== '' &&
              !parentNodeExists(parentId, parentName, parentNodes)
            ) {
              parentNodes.push({ Id: parentId, Name: parentName })
            }
            return true // 已找到目标节点，停止遍历
          }

          // 递归遍历子节点
          if (node.Children && traverse(node.Children, node.Id, node.Name)) {
            // 如果在子节点中找到了目标节点，并且当前节点信息未收集，则添加它
            if (!parentNodeExists(node.Id, node.Name, parentNodes)) {
              parentNodes.push({ Id: node.Id, Name: node.Name })
            }
            return true // 继续向上遍历
          }
        }
        return false // 在当前层级未找到目标节点
      }
      // 从树的根节点开始遍历
      traverse(tree, '', '') // 根节点没有父节点，所以parentId和parentName为空字符串
      // 如果需要，可以根据实际需求决定是否反转数组
      parentNodes.reverse().push()
      return parentNodes // 返回包含唯一父节点ID和名称的数组
    }
  }
}
</script>

<style lang="scss" scoped>
.szcjPJEquipmentAssetList {
  // padding: 10px 15px;
  // height: calc(100vh - 90px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  .tree_card {
    ::v-deep .el-card__body {
      padding: 10px 0px 10px 0px !important;
    }
    ::v-deep .el-tree-node__label {
      text-wrap: wrap !important;
      padding: 6px !important;
    }
    ::v-deep .el-tree-node__content {
      min-height: 32px !important;
      height: auto !important;
    }
  }

  .positionTreeClass::-webkit-scrollbar {
    width: 5px;
    height: 1px;
  }
  .positionTreeClass::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);
    background: #f0f2f7;
  }
  .positionTreeClass::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);
    border-radius: 10px;
    // background: rgba(79, 104, 145, 0.35);
  }

  .list_class::-webkit-scrollbar {
    width: 5px;
    height: 1px;
  }
  .list_class::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);
    // background: #f0f2f7;
  }
  .list_class::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);
    border-radius: 10px;
    // background: rgba(79, 104, 145, 0.35);
  }

  .card_content {
    height: 130px;
    padding: 0px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #7f8ca2;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
      .num {
        font-family: Helvetica, Helvetica;
        font-weight: bold;
        font-size: 40px;
        color: #298dff;
        font-style: normal;
        text-transform: none;
        margin-right: 20px;
      }
    }
    .right {
      img {
        width: 64px;
        height: 64px;
      }
    }
  }
  .list_no_box {
    padding: 50px 10px;
    height: 100%;
    .no_content {
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
        width: 300px;
        height: auto;
      }
      span {
        margin-top: 10px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #c2cbe2;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .list_box {
    width: 100%;
    // height: 100%;
    display: flex;
    flex-wrap: wrap;
    display: grid;
    grid-template-columns: repeat(5, calc(20% - 10px));
    grid-column-gap: 10px;
    grid-row-gap: 10px;

    .list_item {
      display: flex;
      flex-direction: column;
      background: white;
      // margin-right: 10px;
      // margin-bottom: 10px;
      cursor: pointer;
      .list_logo {
        height: 140px;
        background-color: #f0f2f7;
        display: flex;
        align-items: center;
        justify-content: center;
        .el-image {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .list_info {
        margin-top: 10px;
        // cursor: pointer;
      }
      .title {
        padding: 6px 10px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #666666;
        font-style: normal;
        text-transform: none;
      }
      .info {
        display: flex;
        align-items: center;
        padding: 0px 10px;
        margin-top: 6px;
        .label {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #999999;
          font-style: normal;
          text-transform: none;
          margin-right: 6px;
        }
        .value {
          font-family: Helvetica, Helvetica;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          font-style: normal;
          text-transform: none;
        }
      }
      .action {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding: 0px 10px;
        margin-top: 6px;
        margin-bottom: 8px;
        .tags {
          display: flex;
          flex-direction: row;
          .tags_item {
            font-family: PingFang SC, PingFang SC;
            font-weight: 500;
            font-size: 11px;
            padding: 1px 2px;
            font-style: normal;
            text-transform: none;
            margin-right: 6px;
            border-radius: 2px;
          }
        }
        .right {
          color: #298dff;
          font-size: 13px;
          cursor: pointer;
        }
      }
    }
  }

  .card_second_content {
    height: 39px;
    padding: 0px 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 16px;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
      .textInfo {
        display: flex;
        flex-direction: column;
        .textStyle {
          margin-top: 8px;
          color: #999999;
        }
      }
      .popinfo {
        width: 15px;
        height: 15px;
        margin-left: 20px;
      }
      .num {
        font-family: Helvetica, Helvetica;
        font-weight: bold;
        font-size: 32px;
        font-style: normal;
        text-transform: none;
        margin-right: 20px;
        min-width: 30px;
      }
    }
    .right {
      img {
        width: 36px;
        height: 36px;
      }
    }
  }
  ::v-deep .el-card__body {
    border: none !important;
  }
  ::v-deep .el-card__header {
    border-bottom: none !important;
  }
  ::v-deep .el-progress__text {
    font-size: 18px !important;
    color: #666666 !important;
  }
  ::v-deep.el-table .row-one {
    background: rgba(41, 141, 255, 0.03) !important;
  }

  ::v-deep .el-table .row-two {
    background: rgba(255, 255, 255, 1) !important;
  }

  ::v-deep .el-radio-button__inner {
    background-color: #ffffff;
    height: 32px;
    width: 80px;
    font-size: 14px;
  }
}
</style>
