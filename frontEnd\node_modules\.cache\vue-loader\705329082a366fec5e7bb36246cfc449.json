{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\lampManagement\\operationLog\\index.vue?vue&type=style&index=0&id=74755902&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\lampManagement\\operationLog\\index.vue", "mtime": 1755506574370}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCiogew0KICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KfQ0KDQoubGF5b3V0IHsNCiAgaGVpZ2h0OiAxMDAlOw0KICB3aWR0aDogMTAwJTsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICA6OnYtZGVlcCB7DQogICAgLkN1c3RvbUxheW91dCB7DQogICAgICAubGF5b3V0VGFibGUgew0KICAgICAgICBoZWlnaHQ6IDA7DQogICAgICAgIC5DdXN0b21UYWJsZSB7DQogICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgICAgICAudGFibGUgew0KICAgICAgICAgICAgZmxleDogMTsNCiAgICAgICAgICAgIGhlaWdodDogMDsNCiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgICAgICAgLmVsLXRhYmxlIHsNCiAgICAgICAgICAgICAgZmxleDogMTsNCiAgICAgICAgICAgICAgaGVpZ2h0OiAwOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6UA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/lampManagement/operationLog", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        >\r\n          <!-- <template #customBtn=\"{slotScope}\"><el-button type=\"text\" @click=\"handelStart(slotScope)\">{{ slotScope.Status == 1 ? '停用' :'启用' }}</el-button></template> -->\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport {\r\n  GetOperateLogPageList,\r\n  ExportExcelAsync,\r\n  ClearOpearteLog\r\n} from '@/api/business/lampManagement'\r\nexport default {\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        EquipmentName: '',\r\n        OperateType: '',\r\n        StartTime: null,\r\n        EndTime: null,\r\n        ProjectName: '',\r\n        Date: []\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'EquipmentName',\r\n            label: '设备查询',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'OperateType',\r\n            label: '操作类型',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [\r\n              {\r\n                label: '查看图纸',\r\n                value: '查看图纸'\r\n              },\r\n              {\r\n                label: '查看联系�?,\r\n                value: '查看联系�?\r\n              },\r\n              {\r\n                label: '连接联系�?,\r\n                value: '连接联系�?\r\n              },\r\n              {\r\n                label: '评价联系�?,\r\n                value: '评价联系�?\r\n              }\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Date',\r\n            label: '时间范围',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              type: 'datetimerange',\r\n              rangeSeparator: '�?,\r\n              startPlaceholder: '开始日�?,\r\n              endPlaceholder: '结束日期',\r\n              clearable: true,\r\n              valueFormat: 'yyyy-MM-dd HH:mm'\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n              if (e && e.length !== 0) {\r\n                this.ruleForm.StartTime = e[0]\r\n                this.ruleForm.EndTime = e[1]\r\n              } else {\r\n                this.ruleForm.StartTime = null\r\n                this.ruleForm.EndTime = null\r\n              }\r\n            }\r\n          },\r\n          {\r\n            key: 'ProjectName',\r\n            label: '项目名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'DrawingName',\r\n            label: '图纸',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n\r\n            {\r\n              key: 'batch',\r\n              disabled: false, // 是否禁用\r\n              text: '导出',\r\n              onclick: (item) => {\r\n                this.ExportData()\r\n              }\r\n            },\r\n            {\r\n              text: '清空',\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载�?\n              disabled: false, // 是否禁用\r\n              icon: '', //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (index, row) => {\r\n                this.handleDelete(index, row)\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          {\r\n            label: '日志时间',\r\n            key: 'CreateDate'\r\n          },\r\n          {\r\n            label: '终端设备',\r\n            key: 'EquipmentName'\r\n          },\r\n          {\r\n            label: '设备ID',\r\n            key: 'EquipmentId'\r\n          },\r\n          {\r\n            label: '操作类型',\r\n            key: 'OperateType'\r\n          },\r\n          {\r\n            label: '操作人员',\r\n            key: 'CreateUserName'\r\n          },\r\n          {\r\n            label: 'IP地址',\r\n            key: 'IPAddress'\r\n          },\r\n          {\r\n            label: '项目名称',\r\n            key: 'ProjectName'\r\n          },\r\n          {\r\n            label: '图纸',\r\n            key: 'DrawingName'\r\n          },\r\n          {\r\n            label: '服务提供�?,\r\n            key: 'ServiceUserName'\r\n          },\r\n          {\r\n            label: '内容',\r\n            key: 'Content'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.onFresh()\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.fetchData()\r\n    },\r\n    async fetchData() {\r\n      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null\r\n        this.ruleForm.EndTime = null\r\n      }\r\n      await GetOperateLogPageList({\r\n        ...this.ruleForm,\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.tableData = res.Data.Data\r\n          this.customTableConfig.total = res.Data.Total\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 导出\r\n    ExportData() {\r\n      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null\r\n        this.ruleForm.EndTime = null\r\n      }\r\n      ExportExcelAsync({ ...this.ruleForm,\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize }).then((res) => {\r\n        const url = window.URL.createObjectURL(new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }))\r\n        const link = document.createElement('a')\r\n        link.style.display = 'none'\r\n        link.href = url\r\n        // 文件�?\n        link.setAttribute('download', '操作日志.xlsx')\r\n        document.body.appendChild(link)\r\n        link.click()\r\n      })\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row)\r\n      this.$confirm('该操作将清空所有数据，是否继续�?, {\r\n        type: 'warning'\r\n      })\r\n        .then((_) => {\r\n          ClearOpearteLog().then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n              this.customTableConfig.pageSize = 1\r\n              this.onFresh()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch((_) => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删�?\r\n          })\r\n        })\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.onFresh()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.onFresh()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      console.log(selection)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.layout {\r\n  height: 100%;\r\n  width: 100%;\r\n  position: absolute;\r\n  ::v-deep {\r\n    .CustomLayout {\r\n      .layoutTable {\r\n        height: 0;\r\n        .CustomTable {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          .table {\r\n            flex: 1;\r\n            height: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            .el-table {\r\n              flex: 1;\r\n              height: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}