{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\purviewConfig\\index.vue?vue&type=style&index=0&id=44210a6c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\purviewConfig\\index.vue", "mtime": 1755506574162}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoubXQyMCB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQp9DQoubGF5b3V0ew0KICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA5MHB4KTsNCiAgb3ZlcmZsb3c6IGF1dG87DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoYA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/accessControl/purviewConfig", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      width=\"30%\"\r\n      :visible.sync=\"dialogVisible\"\r\n      destroy-on-close\r\n    >\r\n      <el-form\r\n        :model=\"addForm\"\r\n        :rules=\"rules\"\r\n        ref=\"ruleForm\"\r\n        label-width=\"100px\"\r\n        class=\"demo-ruleForm\"\r\n      >\r\n        <el-form-item label=\"权限组名�? prop=\"Name\">\r\n          <el-input v-model=\"addForm.Name\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n          <el-button @click=\"resetAddForm\">取消</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport getGridByCode from \"../../safetyManagement/mixins/index\";\r\nimport { GetList, DelAuthGroup, SaveAuthGroup, ExportData } from \"@/api/business/purviewConfig\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      dialogTitle: \"新增权限�?,\r\n      tableSelection: \"\",\r\n      ruleForm: {\r\n        Name: \"\",\r\n      },\r\n      addForm: {\r\n        Name: \"\",\r\n      },\r\n      rules: {\r\n        Name: [{ required: true, message: '请输入权限组名称', trigger: 'blur' }]\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Name\",\r\n            label: \"门禁组名�?,\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"新增\",\r\n              type: \"primary\",\r\n              size: \"small\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '门禁�?,\r\n            key: 'P_Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备数量',\r\n            width: '50px',\r\n            key: 'num',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '可通行人员',\r\n            width: '50px',\r\n            key: 'P_Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '同步状�?,\r\n            key: 'SyncRemark',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: \"240px\",\r\n          align: \"center\",\r\n        },\r\n        tableActionsWidth: 160,\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleWatch(row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDele(row.Id)\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"导出\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              console.log(index, row);\r\n              this.handleExport(row.Id);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/watchDevice\",\r\n          hidden: true,\r\n          component: () => import(\"./watchDevice.vue\"),\r\n          meta: { title: `门禁权限配置详情` },\r\n          name: \"purviewConfigWatchDevice\",\r\n        },\r\n        {\r\n          path: this.$route.path + \"/editDevice\",\r\n          hidden: true,\r\n          component: () => import(\"./editDevice.vue\"),\r\n          meta: { title: `门禁权限配置编辑` },\r\n          name: \"purviewConfigEditDevice\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  async created() {\r\n    this.init();\r\n  },\r\n  watch: {\r\n    'customTableConfig.tableData': {\r\n      handler(newValue, oldValue) {\r\n        this.customTableConfig.tableColumns = [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '门禁�?,\r\n            key: 'Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备数量',\r\n           \r\n            key: 'EquipCount',\r\n            otherOptions: {\r\n              align: 'center',\r\n              width:'250px',\r\n            }\r\n          },\r\n          {\r\n            label: '可通行人员',\r\n           \r\n            key: 'UserCount',\r\n            otherOptions: {\r\n              align: 'center',\r\n              width:'250px',\r\n            }\r\n          },\r\n          {\r\n            label: '同步状�?,\r\n            key: 'SyncRemark',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n        ]\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mixins: [getGridByCode, addRouterPage],\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData();\r\n    },\r\n    resetForm() {\r\n      this.fetchData();\r\n    },\r\n    init() {\r\n      this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      const res = await GetList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.Total;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    async handleCreate() {\r\n      this.ruleForm.Name = ''\r\n      this.dialogVisible = true\r\n    },\r\n    submitForm() {\r\n      this.$refs.ruleForm.validate((valid) => {\r\n        if (valid) {\r\n          SaveAuthGroup(this.addForm).then(res => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success('新增成功')\r\n              this.fetchData()\r\n              this.resetAddForm()\r\n            } else {\r\n              this.$message.error(res.Message)\r\n            }\r\n          })\r\n        } else {\r\n          console.log('error submit!!');\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    resetAddForm() {\r\n      this.$refs.ruleForm.resetFields();\r\n      this.dialogVisible = false\r\n    },\r\n    async handleWatch(row) {\r\n      this.$router.push({\r\n        name: \"purviewConfigWatchDevice\",\r\n        query: { pg_redirect: this.$route.name, Id: row.Id },\r\n      });\r\n    },\r\n    async handleEdit(row) {\r\n      this.$router.push({\r\n        name: \"purviewConfigEditDevice\",\r\n        query: { pg_redirect: this.$route.name, Id: row.Id },\r\n      });\r\n    },\r\n    async handleExport(Id) {\r\n      const res = await ExportData({ Id });\r\n      const url = window.URL.createObjectURL(\r\n        new Blob([res], {\r\n          type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\r\n        })\r\n      );\r\n      const link = document.createElement(\"a\");\r\n      link.style.display = \"none\";\r\n      link.href = url;\r\n      link.setAttribute(\"download\", \"门禁权限配置.xlsx\");\r\n      document.body.appendChild(link);\r\n      link.click();\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.fetchData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.fetchData();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleDele(Id) {\r\n      this.$confirm('是否确定删除该数�?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DelAuthGroup({ Id }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            });\r\n            this.fetchData()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n\r\n      }).catch(() => {\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}