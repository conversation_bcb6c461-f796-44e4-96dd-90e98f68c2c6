{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\equipmentManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\equipmentManagement\\index.vue", "mtime": 1755674552432}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/safetyManagement/equipmentManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 equipmentManagement\">\r\n    <custom-layout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"submitForm\"\r\n          @resetForm=\"fetchData\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </custom-layout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport getGridByCode from '../mixins/index'\r\nimport {\r\n  GetEquipmentList,\r\n  MonitoreImportTemplate,\r\n  ExportMonitoreEquipment,\r\n  LookVideo,\r\n  DelEquipment,\r\n  MonitoreEquipmentInfo,\r\n  ExportEquipmentList\r\n} from '@/api/business/safetyManagement'\r\nimport DialogForm from './components/dialogForm.vue'\r\nimport WatchVideoDialog from './components/watchVideoDialog.vue'\r\nimport DeviceInfoDialog from './components/deviceInfoDialog.vue'\r\nimport ImportFile from './components/importFile.vue'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport { GetDictionaryTreeDetailListByCode } from '@/api/sys'\r\nimport {\r\n  GetParkArea,\r\n  GetTreeAddress\r\n} from '@/api/business/energyManagement.js'\r\n\r\nexport default {\r\n  components: {\r\n    CustomLayout,\r\n    CustomTable,\r\n    CustomForm\r\n  },\r\n  mixins: [getGridByCode],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        EquipmentName: '',\r\n        EquipmentNumber: '',\r\n        InstallSite: '',\r\n        EquipmentType: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'EquipmentName',\r\n            label: '设备名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentNumber',\r\n            label: '设备编码',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'InstallSite',\r\n            label: '设备部署位置',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentType',\r\n            label: '设备类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '下载模板',\r\n              onclick: () => {\r\n                this.handleDownTemplate()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导入',\r\n              onclick: () => {\r\n                this.handleImport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导出',\r\n              onclick: () => {\r\n                this.handleExport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量删除',\r\n              onclick: () => {\r\n                this.handleDelete('batch')\r\n              }\r\n            },\r\n            {\r\n              text: '新增',\r\n              type: 'primary',\r\n              onclick: () => {\r\n                this.handleEdit()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [20, 40, 60, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 1000,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center'\r\n        },\r\n        tableActionsWidth: 220,\r\n        tableActions: [\r\n          {\r\n            actionLabel: '监控链接',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDeviceInfo(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete('single', row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '查看视频',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleLookVideo(row.Id)\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '新增',\r\n      currentComponent: null,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n        },\r\n        fetchData: () => {\r\n          this.fetchData()\r\n        }\r\n      },\r\n      multipleSelection: [],\r\n      Park_Area: ''\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchData()\r\n    this.getGridByCode('equipmentManagement')\r\n    GetParkArea().then((res) => {\r\n      this.Park_Area = res.Data\r\n    })\r\n  },\r\n  async mounted() {\r\n    this.customForm.formItems[3].options =\r\n      await this.getDictionaryDetailListByCode()\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      GetEquipmentList({\r\n        ...this.ruleForm,\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n          this.customTableConfig.tableData = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    handleDelete(type, id) {\r\n      if (type == 'batch') {\r\n        if (this.multipleSelection.length == 0) {\r\n          this.$message.warning('请选择数据!')\r\n          return\r\n        } else {\r\n          id = this.multipleSelection.map((item) => item.Id).join(',')\r\n        }\r\n      }\r\n      this.$confirm('确认删除？', {\r\n        type: 'warning'\r\n      })\r\n        .then(async(_) => {\r\n          await DelEquipment({ id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success('删除成功!')\r\n              this.fetchData()\r\n            } else {\r\n              this.$message.error(res.Message)\r\n            }\r\n          })\r\n        })\r\n        .catch((_) => { })\r\n    },\r\n    handleEdit(index, row, type = 'add') {\r\n      this.currentComponent = DialogForm\r\n      if (type === 'add') {\r\n        this.dialogTitle = '新增'\r\n        this.componentsConfig.Data = {\r\n          Monitore_Equipment_Number: '',\r\n          Monitore_Equipment_Name: '',\r\n          Monitore_Equipment_SN_Number: '',\r\n          Monitore_Equipment_Type: '',\r\n          Pisition: '',\r\n          Monitore_Equipment_Name: '',\r\n          Park_Area: this.Park_Area,\r\n          Site: '',\r\n          Address: '',\r\n          Brand: '',\r\n          Version: '',\r\n          Equipment_Purpose_Catetory: ''\r\n        }\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        row.Park_area = [row.Purpose_Catetory, row.Scene, row.Site]\r\n        this.componentsConfig.Data = { ...row, Park_Area: this.Park_Area }\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    submitForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData()\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.fetchData()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.customTableConfig.currentPage = val\r\n      this.fetchData()\r\n    },\r\n    handleSelectionChange(data) {\r\n      console.log(data)\r\n      this.multipleSelection = data\r\n    },\r\n    handleLookVideo(id) {\r\n      this.currentComponent = WatchVideoDialog\r\n      this.dialogTitle = '查看视频'\r\n      this.dialogVisible = true\r\n      this.componentsConfig.Data = id\r\n    },\r\n    handleDeviceInfo(id) {\r\n      this.currentComponent = DeviceInfoDialog\r\n      this.dialogTitle = '监控链接'\r\n      this.dialogVisible = true\r\n      MonitoreEquipmentInfo({ id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.componentsConfig.Data = {\r\n            Mainstream_Code: res.Data.Mainstream_Code,\r\n            Substream_Code: res.Data.Substream_Code,\r\n            Url: res.Data.Url\r\n          }\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleDownTemplate() {\r\n      MonitoreImportTemplate({ code: 'equipmentManagement' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data, '安防监控设备管理导入模板')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    // handleExport() {\r\n    //   let id = \"\";\r\n    //   if (this.multipleSelection.length == 0) {\r\n    //     this.$message.warning(\"请选择数据!\");\r\n    //     return;\r\n    //   } else {\r\n    //     id = this.multipleSelection.map((item) => item.Id).join(\",\");\r\n    //   }\r\n    //   ExportMonitoreEquipment({\r\n    //     code: \"equipmentManagement\",\r\n    //     id,\r\n    //   }).then((res) => {\r\n    //     if (res.IsSucceed) {\r\n    //       this.$message.success(\"导出成功\");\r\n    //       downloadFile(res.Data, \"安防监控设备管理数据\");\r\n    //     } else {\r\n    //       this.$message.error(res.Message);\r\n    //     }\r\n    //   });\r\n    // },\r\n    handleExport() {\r\n      const Id = this.multipleSelection.map((item) => item.Id).join(',')\r\n      ExportEquipmentList({\r\n        ...this.ruleForm,\r\n        Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('导出成功')\r\n          downloadFile(res.Data, '安防监控设备管理数据')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleImport() {\r\n      this.currentComponent = ImportFile\r\n      this.dialogTitle = '批量导入'\r\n      this.dialogVisible = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang='scss'>\r\n.equipmentManagement {\r\n  // height: calc(100vh - 90px);\r\n  // overflow: hidden;\r\n}\r\n</style>\r\n"]}]}