<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      top="10vh"
    >
      <component
        :is="currentComponent"
        ref="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      />
    </el-dialog>
    <!-- 导入弹窗 -->
    <!-- <dialogImport ref="dialogImport" /> -->
  </div>
</template>

<script>
import { parseTime } from "@/utils";
// import { baseUrl } from '@/utils/baseurl'
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";
import DialogForm from "./components/dialogTable.vue";
import dialogImport from "./components/dialogImport.vue";
// import { downloadFile } from '@/utils/downloadFile'
import { GetPreferenceSettingValue } from "@/api/sys/system-setting";
import { GetGridByCode } from "@/api/sys";
import { RoleAuthorization } from "@/api/user";
import addRouterPage from "@/mixins/add-router-page";
import {
  GetPageList,
  DeleteEntity,
} from "@/api/business/productionConfiguration";
// import { divide } from 'xe-utils'
export default {
  name: "ProductionConfiguration",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
  },
  mixins: [addRouterPage],
  data() {
    return {
      addPageArray: [
        {
          path: this.$route.path + "/add",
          hidden: true,
          component: () => import("./components/add.vue"),
          name: "ProductionConfigurationAdd",
          meta: { title: `新增` },
        },
        {
          path: this.$route.path + "/edit",
          hidden: true,
          component: () => import("./components/add.vue"),
          name: "ProductionConfigurationEdit",
          meta: { title: `编辑` },
        },
        {
          path: this.$route.path + "/view",
          hidden: true,
          component: () => import("./components/add.vue"),
          name: "ProductionConfigurationView",
          meta: { title: `查看` },
        },
      ],
      currentComponent: DialogForm,
      componentsConfig: {},
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogVisible: false,
      dialogTitle: "",
      tableSelection: [],

      ruleForm: {
        Board_Name: "",
      },
      customForm: {
        formItems: [
          {
            key: "Board_Name", // 字段ID
            label: "看板名称", // Form的label
            type: "input", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            placeholder: "请输入看板",
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
            },
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "新增",
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: false, // 是否禁用
              icon: "", //  图标
              autofocus: false, // 是否聚焦
              type: "primary", // primary / success / warning / danger / info / text
              size: "small", // medium / small / mini
              onclick: (item) => {
                console.log(item);
                this.handleCreate();
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            width: 60,
            label: "序号",
            otherOptions: {
              type: "index",
              align: "center",
            },
          },
          //   {
          //     label: '报表编码',
          //     key: 'Code'
          //   },
          //   {
          //     label: '报表名称',
          //     key: 'Name'
          //   },
          //   {
          //     label: '报表创建时间',
          //     key: 'Create_Date'
          //   },
          //   {
          //     label: '报表创建人',
          //     key: 'Create_UserName'
          //   }
        ],
        tableData: [],
        tableActionsWidth: 240,
        tableActions: [
          {
            actionLabel: "查看",
            otherOptions: {
              type: "text",
              disabled: false,
            },
            onclick: (index, row) => {
              this.handleView2(index, row);
            },
          },
          {
            actionLabel: "编辑",
            otherOptions: {
              type: "text",
              disabled: false,
            },
            onclick: (index, row) => {
              this.handleEdit(index, row);
            },
          },
          {
            actionLabel: "删除",
            otherOptions: {
              type: "text",
              disabled: false,
            },
            onclick: (index, row) => {
              this.handleDelete(index, row);
            },
          },
          {
            actionLabel: "复制链接",
            otherOptions: {
              type: "text",
              disabled: false,
            },
            onclick: (index, row) => {
              this.handleCopy(index, row);
            },
          },
          {
            actionLabel: "导入数据",
            otherOptions: {
              type: "text",
              disabled: false,
            },
            onclick: (index, row) => {
              this.handleImport(index, row);
            },
          },
        ],
        otherOptions: {},
      },
      roleAuthorizationList: [],
      Big_Screen_Url:'',
    };
  },
  computed: {},
  created() {
    this.getBaseData();
    this.init();
  },
  activated() {
    this.init();
  },
  methods: {
    getBaseData() {
      // 获取表格配置
      GetGridByCode({ code: "production_configuration_list" }).then((res) => {
        if (res.IsSucceed) {
          const data = res.Data.ColumnList.map((item) => {
            const temp = {
              label: item.Display_Name,
              key: item.Code,
            };
            if (item.Code === "Ids") {
              temp.render = (row) => {
                return this.$createElement(
                  "el-button",
                  {
                    attrs: {
                      type: "text",
                    },
                    on: {
                      click: (val) => {
                        this.handleView(val, row);
                      },
                    },
                  },
                  "查看"
                );
              };
            }
            return temp;
          });
          this.customTableConfig.tableColumns.push(...data);
        } else {
          this.$message({
            type: "error",
            message: res.Message,
          });
        }
      });
      /**
     *  menuType: 2, //1PC 2app
        roleType: 3, //1菜单权限，2列权限 ，3按钮权限
     */
      RoleAuthorization({
        workObjId: localStorage.getItem("Last_Working_Object_Id"),
        menuId: this.$route.meta.Id,
        menuType: 1,
        roleType: 3,
        sign: 10,
      }).then((res) => {
        if (res.IsSucceed) {
          this.roleAuthorizationList = res.Data;
          this.customTableConfig.tableActions[1].otherOptions.disabled =
            !this.roleAuthorizationList.find(
              (item) => item.display_name === "编辑"
            ).is_enabled;
          this.customTableConfig.tableActions[2].otherOptions.disabled =
            !this.roleAuthorizationList.find(
              (item) => item.display_name === "删除"
            ).is_enabled;
          this.customTableConfig.tableActions[4].otherOptions.disabled =
            !this.roleAuthorizationList.find(
              (item) => item.display_name === "导入数据"
            ).is_enabled;
        } else {
          this.$message({
            type: "error",
            message: res.Message,
          });
        }
      });

      GetPreferenceSettingValue({
        Code: "Big_screen",
      }).then((res) => {
        if (res.IsSucceed) {
          this.Big_Screen_Url = res.Data;
        }
      });
    },
    searchForm(data) {
      console.log(data);
      this.onFresh();
    },
    resetForm() {
      this.onFresh();
    },
    onFresh() {
      this.getPageList();
    },
    init() {
      this.getPageList();
    },
    async getPageList() {
      const res = await GetPageList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data.map((item) => {
          item.Modify_Date = item.Modify_Date
            ? parseTime(new Date(item.Modify_Date), "{y}-{m}-{d} {h}:{i}:{s}")
            : "";
          item.Create_Date = item.Create_Date
            ? parseTime(new Date(item.Create_Date), "{y}-{m}-{d} {h}:{i}:{s}")
            : "";
          // item.Is_Push = item.Is_Push ? '是' : '否'
          return item;
        });
        this.customTableConfig.total = res.Data.TotalCount;
      } else {
        this.$message({
          type: "error",
          message: res.Message,
        });
      }
    },
    // 新增
    handleCreate() {
      //   this.dialogTitle = '新增'
      //   this.dialogVisible = true
      this.$router.push({
        name: "ProductionConfigurationAdd",
        query: { pg_redirect: this.$route.name, type: 1 },
      });
      // this.$qiankun.switchMicroAppFn(
      //   "project",
      //   "szdn",
      //   "1cf6f8ac-d9d0-4b18-9959-5e4ef37886f4",
      //   `/business/workshop/productionConfiguration/add?pg_redirect=${this.$route.name}&type=1`
      // );
    },
    // 查看绑定设备明细
    handleView(index, row) {
      console.log(index, row);
      this.currentComponent = DialogForm;
      this.dialogTitle = "查看绑定设备明细";
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.currentComponent.initView(row.Equipment_Ids);
      });
    },
    // 查看看板
    handleView2(index, row) {
      window.open(
        `${this.Big_Screen_Url}/pjProductionBoard/index?id=${
          row.Id
        }&tenant=${localStorage.getItem("tenant")}`,
        "_blank"
      );
      // window.open(
      //   `${process.env.VUE_APP_SCREEN_URL}/productionBoard/index?id=${
      //     row.Id
      //   }&tenant=${localStorage.getItem("tenant")}`,
      //   "_blank"
      // );
      // if (process.env.NODE_ENV === "development") {
      //   window.open(
      //     `http://localhost:5173/productionBoard/index?id=${
      //       row.Id
      //     }&tenant=${localStorage.getItem("tenant")}`,
      //     "_blank"
      //   );
      // } else {
      //   window.open(
      //     `http://wnpzgc-test.bimtk.com/productionBoard/index?id=${
      //       row.Id
      //     }&tenant=${localStorage.getItem("tenant")}`,
      //     "_blank"
      //   );
      // }
    },
    // 编辑
    handleEdit(index, row) {
      console.log(index, row);
      this.$router.push({
        name: "ProductionConfigurationAdd",
        query: { pg_redirect: this.$route.name, type: 2, id: row.Id },
      });
    },
    // 复制链接
    handleCopy(index, row) {
      // console.log(
      //   process.env.VUE_APP_SCREEN_URL,
      //   "process.env.VUE_APP_SCREEN_URL"
      // );
      const textareaEle = document.createElement("textarea");
      // if (process.env.NODE_ENV === "development") {
      //   textareaEle.value = `http://localhost:5173/productionBoard/index?id=${
      //     row.Id
      //   }&tenant=${localStorage.getItem("tenant")}`;
      // } else {
      //   textareaEle.value = `http://wnpzgc-test.bimtk.com/productionBoard/index?id=${
      //     row.Id
      //   }&tenant=${localStorage.getItem("tenant")}`;
      // }
      textareaEle.value = `${this.Big_Screen_Url}/pjProductionBoard/index?id=${
        row.Id
      }&tenant=${localStorage.getItem("tenant")}`;

      document.body.appendChild(textareaEle);
      textareaEle.select();
      document.execCommand("copy");
      document.body.removeChild(textareaEle);
      this.$message({
        type: "success",
        message: "已成功复制到截切板",
      });
    },
    // 导入数据
    handleImport(index, row) {
      console.log(index, row);
      this.currentComponent = dialogImport;
      this.dialogTitle = "导入数据";
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.currentComponent.init(row);
      });
    },
    // 删除
    handleDelete(index, row) {
      console.log(index, row);
      this.$confirm("确认删除？", {
        type: "warning",
      })
        .then(async (_) => {
          const res = await DeleteEntity({
            id: row.Id,
          });
          if (res.IsSucceed) {
            this.$message({
              type: "success",
              message: "删除成功",
            });
            this.init();
          } else {
            this.$message({
              type: "error",
              message: res.Message,
            });
          }
        })
        .catch((_) => {});
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.init();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
  },
};
</script>

  <style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
::v-deep .el-table__fixed-body-wrapper {
  top: 40px !important;
}
.layout{
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>

