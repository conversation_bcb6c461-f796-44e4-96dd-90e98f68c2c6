{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\szcjPJEquipmentAssetList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\szcjPJEquipmentAssetList\\index.vue", "mtime": 1755674552420}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgbm9waWN0dXJlcyBmcm9tICdAL2Fzc2V0cy9ub3BpY3R1cmVzQDJ4LnBuZycNCmltcG9ydCBDdXN0b21Gb3JtIGZyb20gJ0AvYnVzaW5lc3NDb21wb25lbnRzL0N1c3RvbUZvcm0vaW5kZXgudnVlJw0KaW1wb3J0IGVkaXREaWFsb2cgZnJvbSAnQC92aWV3cy9idXNpbmVzcy9tYWludGVuYW5jZUFuZFVwa2VlcC93b3JrT3JkZXJNYW5hZ2VtZW50L2VkaXREaWFsb2cudnVlJw0KaW1wb3J0IHsNCiAgR2V0RGV2aWNlU3RhdHVzLA0KICBHZXREZXZpY2VMaXN0LA0KICBHZXRQb3N0aW9uVHJlZUxpc3QsDQogIEdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlQYXJlbnRJZA0KfSBmcm9tICdAL2FwaS9idXNpbmVzcy9lcXB0QXNzZXQnDQppbXBvcnQgeyBHZXRPc3NVcmwsIEdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlDb2RlIH0gZnJvbSAnQC9hcGkvc3lzL2luZGV4Jw0KaW1wb3J0IHsgZ2V0RGljdGlvbmFyeSB9IGZyb20gJ0AvdXRpbHMvY29tbW9uJw0KaW1wb3J0IGFkZFJvdXRlclBhZ2UgZnJvbSAnQC9taXhpbnMvYWRkLXJvdXRlci1wYWdlJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdTemNqUEpFcXVpcG1lbnRBc3NldExpc3QnLA0KICBjb21wb25lbnRzOiB7DQogICAgZWRpdERpYWxvZywNCiAgICBDdXN0b21Gb3JtDQogIH0sDQogIG1peGluczogW2FkZFJvdXRlclBhZ2VdLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB0YWdzU3R5bGU6IFsNCiAgICAgICAgew0KICAgICAgICAgIHRleHQ6ICflnKjnur8nLA0KICAgICAgICAgIGNvbG9yOiAncmdiYSg3OCwgMTkxLCAxMzksIDEpJywNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSg3OCwgMTkxLCAxMzksIDAuMSknDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0ZXh0OiAn5q2j5bi4JywNCiAgICAgICAgICBjb2xvcjogJ3JnYmEoMjcsIDE4MSwgMjI0LCAxKScsDQogICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMjcsIDE4MSwgMjI0LCAuMSknDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0ZXh0OiAn56a757q/JywNCiAgICAgICAgICBjb2xvcjogJ3JnYmEoMTA0LCAxMTYsIDEzOCwgMSknLA0KICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDEwNCwgMTE2LCAxMzgsIC4xKScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRleHQ6ICfmlYXpmpwnLA0KICAgICAgICAgIGNvbG9yOiAncmdiYSgyNTUsIDk1LCAxMjIsIDEpJywNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgyNTUsIDk1LCAxMjIsIC4xKScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRleHQ6ICflvILluLgnLA0KICAgICAgICAgIGNvbG9yOiAncmdiYSgyNTUsIDE0NCwgNDQsIDEpJywNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgyNTUsIDE0NCwgNDQsIC4xKScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRleHQ6ICfnu7Tkv67kuK0nLA0KICAgICAgICAgIGNvbG9yOiAncmdiYSgxMDMsIDg0LCAyMTAsIDEpJywNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgxMDMsIDg0LCAyMTAsIC4xKScNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIHJ1bGVGb3JtOiB7DQogICAgICAgIERpc3BsYXlfTmFtZTogJycsDQogICAgICAgIEJyYW5kOiAnJywNCiAgICAgICAgRGV2aWNlX1R5cGVfSWQ6ICcnLA0KICAgICAgICBEZXZpY2VfVHlwZV9EZXRhaWxfSWQ6ICcnLA0KICAgICAgICBQb3N0aW9uOiAnJw0KICAgICAgfSwNCiAgICAgIGN1c3RvbUZvcm06IHsNCiAgICAgICAgZm9ybUl0ZW1zOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiAnRGlzcGxheV9OYW1lJywNCiAgICAgICAgICAgIGxhYmVsOiAn6K6+5aSH5ZCN56ewJywNCiAgICAgICAgICAgIHR5cGU6ICdpbnB1dCcsDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgY2hhbmdlOiAoZSkgPT4gew0KICAgICAgICAgICAgICAvLyBjaGFuZ2Xkuovku7YNCiAgICAgICAgICAgICAgY29uc29sZS5sb2coZSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGtleTogJ0JyYW5kJywNCiAgICAgICAgICAgIGxhYmVsOiAn6K6+5aSH5ZOB54mMJywNCiAgICAgICAgICAgIHR5cGU6ICdpbnB1dCcsDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgY2hhbmdlOiAoZSkgPT4gew0KICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiAnRGV2aWNlX1R5cGVfSWQnLA0KICAgICAgICAgICAgbGFiZWw6ICforr7lpIfnsbvlnosnLA0KICAgICAgICAgICAgdHlwZTogJ3NlbGVjdCcsDQogICAgICAgICAgICBvcHRpb25zOiBbXSwNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICBjbGVhcmFibGU6IHRydWUNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBjaGFuZ2U6IChlKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuY3VzdG9tRm9ybS5mb3JtSXRlbXMuZmluZCgNCiAgICAgICAgICAgICAgICAodikgPT4gdi5rZXkgPT09ICdEZXZpY2VfVHlwZV9EZXRhaWxfSWQnDQogICAgICAgICAgICAgICkub3B0aW9ucyA9IFtdDQogICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0uRGV2aWNlX1R5cGVfRGV0YWlsX0lkID0gJycNCiAgICAgICAgICAgICAgR2V0RGljdGlvbmFyeURldGFpbExpc3RCeVBhcmVudElkKGUpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMuY3VzdG9tRm9ybS5mb3JtSXRlbXMuZmluZCgNCiAgICAgICAgICAgICAgICAgICh2KSA9PiB2LmtleSA9PT0gJ0RldmljZV9UeXBlX0RldGFpbF9JZCcNCiAgICAgICAgICAgICAgICApLm9wdGlvbnMgPSByZXMuRGF0YS5tYXAoKHYpID0+IHsNCiAgICAgICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgICAgIGxhYmVsOiB2LkRpc3BsYXlfTmFtZSwNCiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHYuSWQNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiAnRGV2aWNlX1R5cGVfRGV0YWlsX0lkJywNCiAgICAgICAgICAgIGxhYmVsOiAn6K6+5aSH5a2Q57G7JywNCiAgICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLA0KICAgICAgICAgICAgb3B0aW9uczogW10sDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgY2hhbmdlOiAoZSkgPT4gew0KICAgICAgICAgICAgICAvLyBjaGFuZ2Xkuovku7YNCiAgICAgICAgICAgICAgY29uc29sZS5sb2coZSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGtleTogJ1N0YXR1cycsDQogICAgICAgICAgICBsYWJlbDogJ+iuvuWkh+eKtuaAgScsDQogICAgICAgICAgICB0eXBlOiAnc2VsZWN0JywNCiAgICAgICAgICAgIG9wdGlvbnM6IFtdLA0KICAgICAgICAgICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGNoYW5nZTogKGUpID0+IHsNCiAgICAgICAgICAgICAgY29uc29sZS5sb2coZSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGtleTogJ1doZXJlT3JkZXJTdGF0dXMnLA0KICAgICAgICAgICAgbGFiZWw6ICfnu7Tkv67nirbmgIEnLA0KICAgICAgICAgICAgdHlwZTogJ3NlbGVjdCcsDQogICAgICAgICAgICBvcHRpb25zOiBbDQogICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICBsYWJlbDogJ+ato+W4uCcsDQogICAgICAgICAgICAgICAgdmFsdWU6ICczJw0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgbGFiZWw6ICfnu7Tkv67kuK0nLA0KICAgICAgICAgICAgICAgIHZhbHVlOiAnMScNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgIGxhYmVsOiAn5pWF6ZqcJywNCiAgICAgICAgICAgICAgICB2YWx1ZTogJzAnDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIF0sDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgY2hhbmdlOiAoZSkgPT4gew0KICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgXSwNCiAgICAgICAgcnVsZXM6IHt9LA0KICAgICAgICBjdXN0b21Gb3JtQnV0dG9uczogew0KICAgICAgICAgIG1hcmdpbkxlZnQ6ICc1MHB4JywNCiAgICAgICAgICBzdWJtaXROYW1lOiAn5pCc57SiJywNCiAgICAgICAgICBzdWJtaXRTaG93OiB0cnVlLA0KICAgICAgICAgIHJlc2V0U2hvdzogdHJ1ZSwNCiAgICAgICAgICByZXNldE5hbWU6ICfph43nva4nDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICAvLyDorr7lpIfnirbmgIENCiAgICAgIGRldmljZVN0YXR1c0xpc3Q6IFtdLA0KICAgICAgLy8g5L2N572u5qCRDQogICAgICBwb3NpdGlvblRyZWU6IFtdLA0KICAgICAgLy8g6K6+5aSH5YiX6KGoDQogICAgICBkZXZpY2VMaXN0OiBbXSwNCiAgICAgIGRldmljZUxpc3RMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHBvc2l0aW9uVHJlZUxvYWRpbmc6IGZhbHNlLA0KICAgICAgZGVmYXVsdFByb3BzOiB7DQogICAgICAgIGNoaWxkcmVuOiAnQ2hpbGRyZW4nLA0KICAgICAgICBsYWJlbDogJ05hbWUnLA0KICAgICAgICB2YWx1ZTogJ0lkJw0KICAgICAgfSwNCiAgICAgIGRlZmF1bHRFeHBhbmRlZEtleXM6IFtdLA0KICAgICAgYWRkUGFnZUFycmF5OiBbDQogICAgICAgIHsNCiAgICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy9lcXVpcG1lbnREYXRhJywNCiAgICAgICAgICBoaWRkZW46IHRydWUsDQogICAgICAgICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoJy4vZXF1aXBtZW50RGF0YS52dWUnKSwNCiAgICAgICAgICBuYW1lOiAnUEpFcXVpcG1lbnREYXRhJywNCiAgICAgICAgICBtZXRhOiB7IHRpdGxlOiBg6K6+5aSH5pWw6YeHYCB9DQogICAgICAgIH0NCiAgICAgIF0NCiAgICB9DQogIH0sDQogIGFjdGl2YXRlZCgpIHt9LA0KICBiZWZvcmVEZXN0cm95KCkge30sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXREZXZpY2VTdGF0dXMoKQ0KICAgIHRoaXMuZ2V0UG9zdGlvblRyZWVMaXN0KCkNCiAgICB0aGlzLmdldERldmljZUxpc3QoKQ0KDQogICAgZ2V0RGljdGlvbmFyeSgnZGV2aWNlVHlwZScpLnRoZW4oKHJlcykgPT4gew0KICAgICAgY29uc3QgaXRlbSA9IHRoaXMuY3VzdG9tRm9ybS5mb3JtSXRlbXMuZmluZCgNCiAgICAgICAgKHYpID0+IHYua2V5ID09PSAnRGV2aWNlX1R5cGVfSWQnDQogICAgICApDQogICAgICBpdGVtLm9wdGlvbnMgPSByZXMubWFwKCh2KSA9PiB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgbGFiZWw6IHYuRGlzcGxheV9OYW1lLA0KICAgICAgICAgIHZhbHVlOiB2LklkDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSkNCg0KICAgIEdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlDb2RlKHsNCiAgICAgIGRpY3Rpb25hcnlDb2RlOiAnTW9uaXRvckF1ZGlvU3RhdHVzJw0KICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgY29uc3QgaXRlbSA9IHRoaXMuY3VzdG9tRm9ybS5mb3JtSXRlbXMuZmluZCgodikgPT4gdi5rZXkgPT09ICdTdGF0dXMnKQ0KICAgICAgaXRlbS5vcHRpb25zID0gcmVzLkRhdGEubWFwKCh2KSA9PiB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgbGFiZWw6IHYuRGlzcGxheV9OYW1lLA0KICAgICAgICAgIHZhbHVlOiB2LlZhbHVlDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSkNCiAgfSwNCiAgbW91bnRlZCgpIHt9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0VGFnc1N0eWxlKG5hbWUpIHsNCiAgICAgIHJldHVybiB0aGlzLnRhZ3NTdHlsZS5maW5kKChpdGVtKSA9PiBpdGVtLnRleHQgPT0gbmFtZSkNCiAgICB9LA0KICAgIG5leHRSb3V0ZURldGFpbChkYXRhKSB7DQogICAgICBkYXRhLm51bSA9IDENCiAgICAgIGRhdGEuaGlzdG9yeVJvdXRlciA9IHRoaXMuJHJvdXRlLm5hbWUNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgbmFtZTogJ1BKRXF1aXBtZW50RGF0YScsDQogICAgICAgIHF1ZXJ5OiB7IHBnX3JlZGlyZWN0OiB0aGlzLiRyb3V0ZS5uYW1lLCBJZDogZGF0YS5JZCB9DQogICAgICB9KQ0KICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2VxcHQvY2hhbmdlRXFwdERhdGEnLCBkYXRhKQ0KICAgIH0sDQoNCiAgICBhc3luYyBnZXREZXZpY2VTdGF0dXMoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXREZXZpY2VTdGF0dXMoe30pDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICBjb25zb2xlLmxvZyhyZXMsICcxMjIxJykNCiAgICAgICAgdGhpcy5kZXZpY2VTdGF0dXNMaXN0ID0gcmVzLkRhdGEubWFwKChpdGVtKSA9PiAoew0KICAgICAgICAgIC4uLml0ZW0sDQogICAgICAgICAgcGVyY2VudDogKChpdGVtLlZhbHVlIC8gcmVzLkRhdGFbMF0uVmFsdWUpICogMTAwKS50b0ZpeGVkKDApICsgJyUnDQogICAgICAgIH0pKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0RGV2aWNlVmFsdWUobmFtZSkgew0KICAgICAgaWYgKHRoaXMuZGV2aWNlU3RhdHVzTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgIHJldHVybiB0aGlzLmRldmljZVN0YXR1c0xpc3QuZmluZCgoaXRlbSkgPT4gaXRlbS5MYWJlbCA9PSBuYW1lKS5WYWx1ZQ0KICAgICAgfQ0KICAgICAgcmV0dXJuICcnDQogICAgfSwNCiAgICBnZXREZXZpY2VQcmVjZW50VmFsdWUobmFtZSkgew0KICAgICAgaWYgKHRoaXMuZGV2aWNlU3RhdHVzTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgIHJldHVybiB0aGlzLmRldmljZVN0YXR1c0xpc3QuZmluZCgoaXRlbSkgPT4gaXRlbS5MYWJlbCA9PSBuYW1lKS5wZXJjZW50DQogICAgICB9DQogICAgICByZXR1cm4gJycNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0UG9zdGlvblRyZWVMaXN0KCkgew0KICAgICAgdGhpcy5wb3NpdGlvblRyZWVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0UG9zdGlvblRyZWVMaXN0KHt9KQ0KICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgdGhpcy5wb3NpdGlvblRyZWVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgdGhpcy5kZWZhdWx0RXhwYW5kZWRLZXlzID0gcmVzLkRhdGEubWFwKCh2KSA9PiB2LklkKQ0KICAgICAgICB0aGlzLnBvc2l0aW9uVHJlZSA9IHJlcy5EYXRhDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCg0KICAgIGFzeW5jIGdldERldmljZUxpc3QoKSB7DQogICAgICB0aGlzLmRldmljZUxpc3QgPSBbXQ0KICAgICAgLy8gdGhpcy5kZXZpY2VMaXN0TG9hZGluZyA9IHRydWUNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldERldmljZUxpc3Qoew0KICAgICAgICAuLi50aGlzLnJ1bGVGb3JtDQogICAgICB9KQ0KICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgLy8gdGhpcy51c2VyVGFibGVEYXRhLm1hcChhc3luYyBpdGVtID0+IHsNCiAgICAgICAgLy8gICBpdGVtLkltZ1VybCA9IGF3YWl0IEdldE9zc1VybCh7IHVybDogaXRlbS5JbWdVcmwgfSkudGhlbihyZXMgPT4gew0KICAgICAgICAvLyAgICAgcmV0dXJuIHJlcy5EYXRhDQogICAgICAgIC8vICAgfSkNCiAgICAgICAgLy8gfSkNCiAgICAgICAgdGhpcy5kZXZpY2VMaXN0ID0gcmVzLkRhdGEubWFwKChpdGVtKSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0uVXJsKSB7DQogICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAuLi5pdGVtLA0KICAgICAgICAgICAgICBpc0hhdmVVcmw6IHRydWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgICAgLi4uaXRlbSwNCiAgICAgICAgICAgICAgaXNIYXZlVXJsOiBmYWxzZSwNCiAgICAgICAgICAgICAgVXJsOiBub3BpY3R1cmVzDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLmRldmljZUxpc3RMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy5kZXZpY2VMaXN0TG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy5nZXREZXZpY2VMaXN0KCkNCiAgICB9LA0KICAgIHJlc2V0Rm9ybSgpIHsNCiAgICAgIHRoaXMuZ2V0RGV2aWNlTGlzdCgpDQogICAgfSwNCg0KICAgIGFzeW5jIGhhbmRsZU5vZGVDbGljayhkYXRhLCBub2RlKSB7DQogICAgICBjb25zdCBwYXJlbnRzID0gYXdhaXQgdGhpcy5maW5kUGFyZW50SWRzKHRoaXMucG9zaXRpb25UcmVlLCBkYXRhLklkKQ0KICAgICAgY29uc3QgbmV3Tm9kZSA9IFsNCiAgICAgICAgLi4ucGFyZW50cywNCiAgICAgICAgew0KICAgICAgICAgIElkOiBkYXRhLklkLA0KICAgICAgICAgIE5hbWU6IGRhdGEuTmFtZQ0KICAgICAgICB9DQogICAgICBdDQogICAgICB0aGlzLnJ1bGVGb3JtLlBvc3Rpb24gPSBuZXdOb2RlLm1hcCgodikgPT4gdi5OYW1lKS5qb2luKCcvJykNCiAgICAgIHRoaXMuZ2V0RGV2aWNlTGlzdCgpDQogICAgfSwNCiAgICBmaW5kUGFyZW50SWRzKHRyZWUsIHRhcmdldElkKSB7DQogICAgICBjb25zdCBwYXJlbnROb2RlcyA9IFtdIC8vIOWtmOWCqOWUr+S4gOeahOeItuiKgueCuUlE5ZKMTmFtZQ0KDQogICAgICAvLyDovoXliqnlh73mlbDvvIznlKjkuo7mo4Dmn6XniLboioLngrnmmK/lkKblt7LlrZjlnKjkuo7mlbDnu4TkuK0NCiAgICAgIGZ1bmN0aW9uIHBhcmVudE5vZGVFeGlzdHMoaWQsIG5hbWUsIGFycmF5KSB7DQogICAgICAgIHJldHVybiBhcnJheS5zb21lKChub2RlKSA9PiBub2RlLklkID09PSBpZCAmJiBub2RlLk5hbWUgPT09IG5hbWUpDQogICAgICB9DQoNCiAgICAgIGZ1bmN0aW9uIHRyYXZlcnNlKG5vZGVzLCBwYXJlbnRJZCwgcGFyZW50TmFtZSkgew0KICAgICAgICBpZiAoIW5vZGVzKSByZXR1cm4gZmFsc2UNCiAgICAgICAgZm9yIChjb25zdCBub2RlIG9mIG5vZGVzKSB7DQogICAgICAgICAgaWYgKG5vZGUuSWQgPT09IHRhcmdldElkKSB7DQogICAgICAgICAgICAvLyDlpoLmnpzlvZPliY3oioLngrnmmK/nm67moIfoioLngrnvvIzlubbkuJTlroPmnInniLboioLngrnvvIzliJnmt7vliqDniLboioLngrnkv6Hmga/vvIjpgb/lhY3ph43lpI3vvIkNCiAgICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICAgcGFyZW50SWQgIT09ICcnICYmDQogICAgICAgICAgICAgICFwYXJlbnROb2RlRXhpc3RzKHBhcmVudElkLCBwYXJlbnROYW1lLCBwYXJlbnROb2RlcykNCiAgICAgICAgICAgICkgew0KICAgICAgICAgICAgICBwYXJlbnROb2Rlcy5wdXNoKHsgSWQ6IHBhcmVudElkLCBOYW1lOiBwYXJlbnROYW1lIH0pDQogICAgICAgICAgICB9DQogICAgICAgICAgICByZXR1cm4gdHJ1ZSAvLyDlt7Lmib7liLDnm67moIfoioLngrnvvIzlgZzmraLpgY3ljoYNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDpgJLlvZLpgY3ljoblrZDoioLngrkNCiAgICAgICAgICBpZiAobm9kZS5DaGlsZHJlbiAmJiB0cmF2ZXJzZShub2RlLkNoaWxkcmVuLCBub2RlLklkLCBub2RlLk5hbWUpKSB7DQogICAgICAgICAgICAvLyDlpoLmnpzlnKjlrZDoioLngrnkuK3mib7liLDkuobnm67moIfoioLngrnvvIzlubbkuJTlvZPliY3oioLngrnkv6Hmga/mnKrmlLbpm4bvvIzliJnmt7vliqDlroMNCiAgICAgICAgICAgIGlmICghcGFyZW50Tm9kZUV4aXN0cyhub2RlLklkLCBub2RlLk5hbWUsIHBhcmVudE5vZGVzKSkgew0KICAgICAgICAgICAgICBwYXJlbnROb2Rlcy5wdXNoKHsgSWQ6IG5vZGUuSWQsIE5hbWU6IG5vZGUuTmFtZSB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgcmV0dXJuIHRydWUgLy8g57un57ut5ZCR5LiK6YGN5Y6GDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiBmYWxzZSAvLyDlnKjlvZPliY3lsYLnuqfmnKrmib7liLDnm67moIfoioLngrkNCiAgICAgIH0NCiAgICAgIC8vIOS7juagkeeahOagueiKgueCueW8gOWni+mBjeWOhg0KICAgICAgdHJhdmVyc2UodHJlZSwgJycsICcnKSAvLyDmoLnoioLngrnmsqHmnInniLboioLngrnvvIzmiYDku6VwYXJlbnRJZOWSjHBhcmVudE5hbWXkuLrnqbrlrZfnrKbkuLINCiAgICAgIC8vIOWmguaenOmcgOimge+8jOWPr+S7peagueaNruWunumZhemcgOaxguWGs+WumuaYr+WQpuWPjei9rOaVsOe7hA0KICAgICAgcGFyZW50Tm9kZXMucmV2ZXJzZSgpLnB1c2goKQ0KICAgICAgcmV0dXJuIHBhcmVudE5vZGVzIC8vIOi/lOWbnuWMheWQq+WUr+S4gOeItuiKgueCuUlE5ZKM5ZCN56ew55qE5pWw57uEDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/equipmentManagement/szcjPJEquipmentAssetList", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 szcjPJEquipmentAssetList\">\r\n    <el-row :gutter=\"12\">\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"never\">\r\n          <div class=\"card_content\">\r\n            <div class=\"left\">\r\n              <span class=\"num\">{{ getDeviceValue(\"设备总数\") }}</span>\r\n              <span>设备总数 </span>\r\n            </div>\r\n            <div class=\"right\">\r\n              <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #4ebf8b\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"在线\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <span>在线 </span>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"在线\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #1bb5e0\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"正常\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <span>正常 </span>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"正常\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #68748a\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"离线\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <span>离线 </span>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"离线\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #ff5f7a\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"故障\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <div>\r\n                      <span>故障 </span>\r\n                      <el-popover\r\n                        placement=\"top-start\"\r\n                        title=\"\"\r\n                        width=\"\"\r\n                        trigger=\"hover\"\r\n                        content=\"对设备发起报修单记为设备故障\"\r\n                      >\r\n                        <img\r\n                          slot=\"reference\"\r\n                          class=\"popinfo\"\r\n                          src=\"@/assets/<EMAIL>\"\r\n                          alt=\"\"\r\n                        >\r\n                      </el-popover>\r\n                    </div>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"故障\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #ff902c\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"异常\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <div>\r\n                      <span>异常 </span>\r\n                      <el-popover\r\n                        placement=\"top-start\"\r\n                        title=\"\"\r\n                        width=\"\"\r\n                        trigger=\"hover\"\r\n                        content=\"设备所传的一切非正常状态均记为异常\"\r\n                      >\r\n                        <img\r\n                          slot=\"reference\"\r\n                          class=\"popinfo\"\r\n                          src=\"@/assets/<EMAIL>\"\r\n                          alt=\"\"\r\n                        >\r\n                      </el-popover>\r\n                    </div>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"异常\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #6754d2\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"维修中\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <span>维修中 </span>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"维修中\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px; flex: 1; height: 0;\">\r\n      <el-col :span=\"3\" style=\"height: 100%\">\r\n        <el-card\r\n          shadow=\"never\"\r\n          class=\"tree_card\"\r\n          style=\"height: 100%; padding: 0\"\r\n        >\r\n          <el-tree\r\n            ref=\"positionTreeRef\"\r\n            v-loading=\"positionTreeLoading\"\r\n            class=\"positionTreeClass\"\r\n            style=\"height: calc(100vh - 340px); overflow-y: auto\"\r\n            :data=\"positionTree\"\r\n            :props=\"defaultProps\"\r\n            node-key=\"Id\"\r\n            :expand-on-click-node=\"false\"\r\n            :default-expanded-keys=\"defaultExpandedKeys\"\r\n            @node-click=\"handleNodeClick\"\r\n          />\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"21\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <CustomForm\r\n                :custom-form-items=\"customForm.formItems\"\r\n                :custom-form-buttons=\"customForm.customFormButtons\"\r\n                :value=\"ruleForm\"\r\n                :inline=\"true\"\r\n                :rules=\"customForm.rules\"\r\n                @submitForm=\"submitForm\"\r\n                @resetForm=\"resetForm\"\r\n              />\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row\r\n          :gutter=\"12\"\r\n          style=\"\r\n            margin-top: 10px;\r\n            height: calc(100vh - 430px);\r\n            overflow-y: auto;\r\n          \"\r\n          class=\"list_class\"\r\n        >\r\n          <el-col :span=\"24\" style=\"height: 100%\">\r\n            <div v-loading=\"deviceListLoading\" style=\"height: 100%\">\r\n              <div v-if=\"deviceList.length > 0\" class=\"list_box\">\r\n                <div\r\n                  v-for=\"(item, index) in deviceList\"\r\n                  :key=\"index\"\r\n                  class=\"list_item\"\r\n                  @click=\"nextRouteDetail(item)\"\r\n                >\r\n                  <div class=\"list_logo\">\r\n                    <el-image\r\n                      v-if=\"item.isHaveUrl\"\r\n                      style=\"width: auto; height: 100%\"\r\n                      :src=\"item.Url\"\r\n                      fit=\"cover\"\r\n                    >\r\n                      <!-- :preview-src-list=\"[item.Url]\" -->\r\n                      <div slot=\"error\" class=\"image-slot\">\r\n                        <i class=\"el-icon-picture-outline\" />\r\n                      </div>\r\n                    </el-image>\r\n\r\n                    <el-image\r\n                      v-if=\"!item.isHaveUrl\"\r\n                      style=\"width: auto; height: 100%\"\r\n                      :src=\"item.Url\"\r\n                      fit=\"cover\"\r\n                    >\r\n                      <div slot=\"error\" class=\"image-slot\">\r\n                        <i class=\"el-icon-picture-outline\" />\r\n                      </div>\r\n                    </el-image>\r\n                  </div>\r\n                  <div class=\"list_info\">\r\n                    <span class=\"title\">{{ item.Name || \"-\" }}</span>\r\n                    <div class=\"info\">\r\n                      <span class=\"label\">位置</span>\r\n                      <span class=\"value\">{{ item.Postion || \"-\" }}</span>\r\n                    </div>\r\n                    <div class=\"info\">\r\n                      <span class=\"label\">品牌</span>\r\n                      <span class=\"value\">{{ item.Brand || \"-\" }}</span>\r\n                    </div>\r\n                    <div class=\"action\">\r\n                      <div class=\"tags\">\r\n                        <div\r\n                          v-for=\"(statusItem, statusIndex) in item.Status\"\r\n                          :key=\"statusIndex\"\r\n                          class=\"tags_item\"\r\n                          :style=\"{\r\n                            background: getTagsStyle(statusItem).background,\r\n                          }\"\r\n                        >\r\n                          <span\r\n                            v-if=\"statusItem == '在线'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '正常'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '离线'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '故障'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '异常'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '维修中'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <!-- <span>{{ statusItem }}</span>\r\n                        <span>{{ statusItem }}</span> -->\r\n                        </div>\r\n                      </div>\r\n                      <div class=\"right\">\r\n                        <span>查看</span>\r\n                        <i class=\"el-icon-arrow-right\" />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div v-else class=\"list_no_box\">\r\n                <div class=\"no_content\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                  <span>无相关设备信息</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport nopictures from '@/assets/<EMAIL>'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport editDialog from '@/views/business/maintenanceAndUpkeep/workOrderManagement/editDialog.vue'\r\nimport {\r\n  GetDeviceStatus,\r\n  GetDeviceList,\r\n  GetPostionTreeList,\r\n  GetDictionaryDetailListByParentId\r\n} from '@/api/business/eqptAsset'\r\nimport { GetOssUrl, GetDictionaryDetailListByCode } from '@/api/sys/index'\r\nimport { getDictionary } from '@/utils/common'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\n\r\nexport default {\r\n  name: 'SzcjPJEquipmentAssetList',\r\n  components: {\r\n    editDialog,\r\n    CustomForm\r\n  },\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n      tagsStyle: [\r\n        {\r\n          text: '在线',\r\n          color: 'rgba(78, 191, 139, 1)',\r\n          background: 'rgba(78, 191, 139, 0.1)'\r\n        },\r\n        {\r\n          text: '正常',\r\n          color: 'rgba(27, 181, 224, 1)',\r\n          background: 'rgba(27, 181, 224, .1)'\r\n        },\r\n        {\r\n          text: '离线',\r\n          color: 'rgba(104, 116, 138, 1)',\r\n          background: 'rgba(104, 116, 138, .1)'\r\n        },\r\n        {\r\n          text: '故障',\r\n          color: 'rgba(255, 95, 122, 1)',\r\n          background: 'rgba(255, 95, 122, .1)'\r\n        },\r\n        {\r\n          text: '异常',\r\n          color: 'rgba(255, 144, 44, 1)',\r\n          background: 'rgba(255, 144, 44, .1)'\r\n        },\r\n        {\r\n          text: '维修中',\r\n          color: 'rgba(103, 84, 210, 1)',\r\n          background: 'rgba(103, 84, 210, .1)'\r\n        }\r\n      ],\r\n      ruleForm: {\r\n        Display_Name: '',\r\n        Brand: '',\r\n        Device_Type_Id: '',\r\n        Device_Type_Detail_Id: '',\r\n        Postion: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Display_Name',\r\n            label: '设备名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Brand',\r\n            label: '设备品牌',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Device_Type_Id',\r\n            label: '设备类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              this.customForm.formItems.find(\r\n                (v) => v.key === 'Device_Type_Detail_Id'\r\n              ).options = []\r\n              this.ruleForm.Device_Type_Detail_Id = ''\r\n              GetDictionaryDetailListByParentId(e).then((res) => {\r\n                this.customForm.formItems.find(\r\n                  (v) => v.key === 'Device_Type_Detail_Id'\r\n                ).options = res.Data.map((v) => {\r\n                  return {\r\n                    label: v.Display_Name,\r\n                    value: v.Id\r\n                  }\r\n                })\r\n              })\r\n            }\r\n          },\r\n          {\r\n            key: 'Device_Type_Detail_Id',\r\n            label: '设备子类',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Status',\r\n            label: '设备状态',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'WhereOrderStatus',\r\n            label: '维修状态',\r\n            type: 'select',\r\n            options: [\r\n              {\r\n                label: '正常',\r\n                value: '3'\r\n              },\r\n              {\r\n                label: '维修中',\r\n                value: '1'\r\n              },\r\n              {\r\n                label: '故障',\r\n                value: '0'\r\n              }\r\n            ],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          marginLeft: '50px',\r\n          submitName: '搜索',\r\n          submitShow: true,\r\n          resetShow: true,\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      // 设备状态\r\n      deviceStatusList: [],\r\n      // 位置树\r\n      positionTree: [],\r\n      // 设备列表\r\n      deviceList: [],\r\n      deviceListLoading: false,\r\n      positionTreeLoading: false,\r\n      defaultProps: {\r\n        children: 'Children',\r\n        label: 'Name',\r\n        value: 'Id'\r\n      },\r\n      defaultExpandedKeys: [],\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/equipmentData',\r\n          hidden: true,\r\n          component: () => import('./equipmentData.vue'),\r\n          name: 'PJEquipmentData',\r\n          meta: { title: `设备数采` }\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  activated() {},\r\n  beforeDestroy() {},\r\n  created() {\r\n    this.getDeviceStatus()\r\n    this.getPostionTreeList()\r\n    this.getDeviceList()\r\n\r\n    getDictionary('deviceType').then((res) => {\r\n      const item = this.customForm.formItems.find(\r\n        (v) => v.key === 'Device_Type_Id'\r\n      )\r\n      item.options = res.map((v) => {\r\n        return {\r\n          label: v.Display_Name,\r\n          value: v.Id\r\n        }\r\n      })\r\n    })\r\n\r\n    GetDictionaryDetailListByCode({\r\n      dictionaryCode: 'MonitorAudioStatus'\r\n    }).then((res) => {\r\n      const item = this.customForm.formItems.find((v) => v.key === 'Status')\r\n      item.options = res.Data.map((v) => {\r\n        return {\r\n          label: v.Display_Name,\r\n          value: v.Value\r\n        }\r\n      })\r\n    })\r\n  },\r\n  mounted() {},\r\n  methods: {\r\n    getTagsStyle(name) {\r\n      return this.tagsStyle.find((item) => item.text == name)\r\n    },\r\n    nextRouteDetail(data) {\r\n      data.num = 1\r\n      data.historyRouter = this.$route.name\r\n      this.$router.push({\r\n        name: 'PJEquipmentData',\r\n        query: { pg_redirect: this.$route.name, Id: data.Id }\r\n      })\r\n      this.$store.dispatch('eqpt/changeEqptData', data)\r\n    },\r\n\r\n    async getDeviceStatus() {\r\n      const res = await GetDeviceStatus({})\r\n      if (res.IsSucceed) {\r\n        console.log(res, '1221')\r\n        this.deviceStatusList = res.Data.map((item) => ({\r\n          ...item,\r\n          percent: ((item.Value / res.Data[0].Value) * 100).toFixed(0) + '%'\r\n        }))\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    getDeviceValue(name) {\r\n      if (this.deviceStatusList.length > 0) {\r\n        return this.deviceStatusList.find((item) => item.Label == name).Value\r\n      }\r\n      return ''\r\n    },\r\n    getDevicePrecentValue(name) {\r\n      if (this.deviceStatusList.length > 0) {\r\n        return this.deviceStatusList.find((item) => item.Label == name).percent\r\n      }\r\n      return ''\r\n    },\r\n\r\n    async getPostionTreeList() {\r\n      this.positionTreeLoading = true\r\n      const res = await GetPostionTreeList({})\r\n      if (res.IsSucceed) {\r\n        this.positionTreeLoading = false\r\n        this.defaultExpandedKeys = res.Data.map((v) => v.Id)\r\n        this.positionTree = res.Data\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n\r\n    async getDeviceList() {\r\n      this.deviceList = []\r\n      // this.deviceListLoading = true\r\n      const res = await GetDeviceList({\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        // this.userTableData.map(async item => {\r\n        //   item.ImgUrl = await GetOssUrl({ url: item.ImgUrl }).then(res => {\r\n        //     return res.Data\r\n        //   })\r\n        // })\r\n        this.deviceList = res.Data.map((item) => {\r\n          if (item.Url) {\r\n            return {\r\n              ...item,\r\n              isHaveUrl: true\r\n            }\r\n          } else {\r\n            return {\r\n              ...item,\r\n              isHaveUrl: false,\r\n              Url: nopictures\r\n            }\r\n          }\r\n        })\r\n        this.deviceListLoading = false\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n        this.deviceListLoading = false\r\n      }\r\n    },\r\n    submitForm() {\r\n      this.getDeviceList()\r\n    },\r\n    resetForm() {\r\n      this.getDeviceList()\r\n    },\r\n\r\n    async handleNodeClick(data, node) {\r\n      const parents = await this.findParentIds(this.positionTree, data.Id)\r\n      const newNode = [\r\n        ...parents,\r\n        {\r\n          Id: data.Id,\r\n          Name: data.Name\r\n        }\r\n      ]\r\n      this.ruleForm.Postion = newNode.map((v) => v.Name).join('/')\r\n      this.getDeviceList()\r\n    },\r\n    findParentIds(tree, targetId) {\r\n      const parentNodes = [] // 存储唯一的父节点ID和Name\r\n\r\n      // 辅助函数，用于检查父节点是否已存在于数组中\r\n      function parentNodeExists(id, name, array) {\r\n        return array.some((node) => node.Id === id && node.Name === name)\r\n      }\r\n\r\n      function traverse(nodes, parentId, parentName) {\r\n        if (!nodes) return false\r\n        for (const node of nodes) {\r\n          if (node.Id === targetId) {\r\n            // 如果当前节点是目标节点，并且它有父节点，则添加父节点信息（避免重复）\r\n            if (\r\n              parentId !== '' &&\r\n              !parentNodeExists(parentId, parentName, parentNodes)\r\n            ) {\r\n              parentNodes.push({ Id: parentId, Name: parentName })\r\n            }\r\n            return true // 已找到目标节点，停止遍历\r\n          }\r\n\r\n          // 递归遍历子节点\r\n          if (node.Children && traverse(node.Children, node.Id, node.Name)) {\r\n            // 如果在子节点中找到了目标节点，并且当前节点信息未收集，则添加它\r\n            if (!parentNodeExists(node.Id, node.Name, parentNodes)) {\r\n              parentNodes.push({ Id: node.Id, Name: node.Name })\r\n            }\r\n            return true // 继续向上遍历\r\n          }\r\n        }\r\n        return false // 在当前层级未找到目标节点\r\n      }\r\n      // 从树的根节点开始遍历\r\n      traverse(tree, '', '') // 根节点没有父节点，所以parentId和parentName为空字符串\r\n      // 如果需要，可以根据实际需求决定是否反转数组\r\n      parentNodes.reverse().push()\r\n      return parentNodes // 返回包含唯一父节点ID和名称的数组\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.szcjPJEquipmentAssetList {\r\n  // padding: 10px 15px;\r\n  // height: calc(100vh - 90px);\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .tree_card {\r\n    ::v-deep .el-card__body {\r\n      padding: 10px 0px 10px 0px !important;\r\n    }\r\n    ::v-deep .el-tree-node__label {\r\n      text-wrap: wrap !important;\r\n      padding: 6px !important;\r\n    }\r\n    ::v-deep .el-tree-node__content {\r\n      min-height: 32px !important;\r\n      height: auto !important;\r\n    }\r\n  }\r\n\r\n  .positionTreeClass::-webkit-scrollbar {\r\n    width: 5px;\r\n    height: 1px;\r\n  }\r\n  .positionTreeClass::-webkit-scrollbar-thumb {\r\n    /*滚动条里面小方块*/\r\n    border-radius: 10px;\r\n    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);\r\n    background: #f0f2f7;\r\n  }\r\n  .positionTreeClass::-webkit-scrollbar-track {\r\n    /*滚动条里面轨道*/\r\n    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);\r\n    border-radius: 10px;\r\n    // background: rgba(79, 104, 145, 0.35);\r\n  }\r\n\r\n  .list_class::-webkit-scrollbar {\r\n    width: 5px;\r\n    height: 1px;\r\n  }\r\n  .list_class::-webkit-scrollbar-thumb {\r\n    /*滚动条里面小方块*/\r\n    border-radius: 10px;\r\n    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);\r\n    // background: #f0f2f7;\r\n  }\r\n  .list_class::-webkit-scrollbar-track {\r\n    /*滚动条里面轨道*/\r\n    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);\r\n    border-radius: 10px;\r\n    // background: rgba(79, 104, 145, 0.35);\r\n  }\r\n\r\n  .card_content {\r\n    height: 130px;\r\n    padding: 0px 24px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .left {\r\n      font-family: PingFang SC, PingFang SC;\r\n      font-weight: 600;\r\n      font-size: 20px;\r\n      color: #7f8ca2;\r\n      font-style: normal;\r\n      text-transform: none;\r\n      display: flex;\r\n      align-items: center;\r\n      .num {\r\n        font-family: Helvetica, Helvetica;\r\n        font-weight: bold;\r\n        font-size: 40px;\r\n        color: #298dff;\r\n        font-style: normal;\r\n        text-transform: none;\r\n        margin-right: 20px;\r\n      }\r\n    }\r\n    .right {\r\n      img {\r\n        width: 64px;\r\n        height: 64px;\r\n      }\r\n    }\r\n  }\r\n  .list_no_box {\r\n    padding: 50px 10px;\r\n    height: 100%;\r\n    .no_content {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      img {\r\n        width: 300px;\r\n        height: auto;\r\n      }\r\n      span {\r\n        margin-top: 10px;\r\n        font-family: Microsoft YaHei, Microsoft YaHei;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #c2cbe2;\r\n        text-align: center;\r\n        font-style: normal;\r\n        text-transform: none;\r\n      }\r\n    }\r\n  }\r\n\r\n  .list_box {\r\n    width: 100%;\r\n    // height: 100%;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    display: grid;\r\n    grid-template-columns: repeat(5, calc(20% - 10px));\r\n    grid-column-gap: 10px;\r\n    grid-row-gap: 10px;\r\n\r\n    .list_item {\r\n      display: flex;\r\n      flex-direction: column;\r\n      background: white;\r\n      // margin-right: 10px;\r\n      // margin-bottom: 10px;\r\n      cursor: pointer;\r\n      .list_logo {\r\n        height: 140px;\r\n        background-color: #f0f2f7;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        .el-image {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n      .list_info {\r\n        margin-top: 10px;\r\n        // cursor: pointer;\r\n      }\r\n      .title {\r\n        padding: 6px 10px;\r\n        font-family: Microsoft YaHei, Microsoft YaHei;\r\n        font-weight: bold;\r\n        font-size: 16px;\r\n        color: #666666;\r\n        font-style: normal;\r\n        text-transform: none;\r\n      }\r\n      .info {\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0px 10px;\r\n        margin-top: 6px;\r\n        .label {\r\n          font-family: PingFang SC, PingFang SC;\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          font-style: normal;\r\n          text-transform: none;\r\n          margin-right: 6px;\r\n        }\r\n        .value {\r\n          font-family: Helvetica, Helvetica;\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          font-style: normal;\r\n          text-transform: none;\r\n        }\r\n      }\r\n      .action {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding: 0px 10px;\r\n        margin-top: 6px;\r\n        margin-bottom: 8px;\r\n        .tags {\r\n          display: flex;\r\n          flex-direction: row;\r\n          .tags_item {\r\n            font-family: PingFang SC, PingFang SC;\r\n            font-weight: 500;\r\n            font-size: 11px;\r\n            padding: 1px 2px;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            margin-right: 6px;\r\n            border-radius: 2px;\r\n          }\r\n        }\r\n        .right {\r\n          color: #298dff;\r\n          font-size: 13px;\r\n          cursor: pointer;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .card_second_content {\r\n    height: 39px;\r\n    padding: 0px 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .left {\r\n      font-family: PingFang SC, PingFang SC;\r\n      font-weight: 600;\r\n      font-size: 16px;\r\n      font-style: normal;\r\n      text-transform: none;\r\n      display: flex;\r\n      align-items: center;\r\n      .textInfo {\r\n        display: flex;\r\n        flex-direction: column;\r\n        .textStyle {\r\n          margin-top: 8px;\r\n          color: #999999;\r\n        }\r\n      }\r\n      .popinfo {\r\n        width: 15px;\r\n        height: 15px;\r\n        margin-left: 20px;\r\n      }\r\n      .num {\r\n        font-family: Helvetica, Helvetica;\r\n        font-weight: bold;\r\n        font-size: 32px;\r\n        font-style: normal;\r\n        text-transform: none;\r\n        margin-right: 20px;\r\n        min-width: 30px;\r\n      }\r\n    }\r\n    .right {\r\n      img {\r\n        width: 36px;\r\n        height: 36px;\r\n      }\r\n    }\r\n  }\r\n  ::v-deep .el-card__body {\r\n    border: none !important;\r\n  }\r\n  ::v-deep .el-card__header {\r\n    border-bottom: none !important;\r\n  }\r\n  ::v-deep .el-progress__text {\r\n    font-size: 18px !important;\r\n    color: #666666 !important;\r\n  }\r\n  ::v-deep.el-table .row-one {\r\n    background: rgba(41, 141, 255, 0.03) !important;\r\n  }\r\n\r\n  ::v-deep .el-table .row-two {\r\n    background: rgba(255, 255, 255, 1) !important;\r\n  }\r\n\r\n  ::v-deep .el-radio-button__inner {\r\n    background-color: #ffffff;\r\n    height: 32px;\r\n    width: 80px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n</style>\r\n"]}]}