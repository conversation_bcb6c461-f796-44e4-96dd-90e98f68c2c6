{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\smartBroadcasting\\broadcastMediaFiles\\index.vue?vue&type=style&index=0&id=103c737d&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\smartBroadcasting\\broadcastMediaFiles\\index.vue", "mtime": 1755506574429}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnRvcCB7DQogIG1hcmdpbjogMTVweCAxNXB4IDBweCAxNXB4Ow0KICBwYWRkaW5nOiAxMHB4IDE1cHg7DQogIGJhY2tncm91bmQtY29sb3I6IHdoaXRlOw0KfQ0KLmJvdHRvbSB7DQogIG1hcmdpbjogMTBweCAxNXB4Ow0KICBwYWRkaW5nOiAxMHB4IDE1cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGhlaWdodDogY2FsYygxMDB2aCAtIDE5MHB4KTsNCiAgLnRhYmxlTm90aWNlIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIHBhZGRpbmc6IDEwcHggMTVweDsNCiAgICBjb2xvcjogcmdiYSgzNCwgNDAsIDUyLCAwLjY1KTsNCiAgICBmb250LXNpemU6IDE0cHg7DQogIH0NCiAgLnRhYmxlQm94IHsNCiAgICBoZWlnaHQ6IGNhbGMoMTAwdmggLSAyNDBweCk7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/smartBroadcasting/broadcastMediaFiles", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div class=\"top\">\r\n      <CustomForm\r\n        :custom-form-items=\"customForm.formItems\"\r\n        :custom-form-buttons=\"customForm.customFormButtons\"\r\n        :value=\"ruleForm\"\r\n        :inline=\"true\"\r\n        :rules=\"customForm.rules\"\r\n        @submitForm=\"searchForm\"\r\n        @resetForm=\"resetForm\"\r\n      />\r\n    </div>\r\n    <div class=\"bottom\">\r\n      <div class=\"tableNotice\">\r\n        <span></span>\r\n        <span>数据更新时间�?{{ updateDate }}</span>\r\n        <!-- <span>数据更新时间�?{{ customTableConfig.tableData[0].UpdateDate }}</span> -->\r\n      </div>\r\n      <div class=\"tableBox\">\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\n\r\nimport dayjs from \"dayjs\";\r\nimport {\r\n  GetMediaFileList,\r\n  PostMediaFileDataList,\r\n} from \"@/api/business/smartBroadcasting\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      updateDate: \"\",\r\n      currentComponent: null,\r\n      componentsConfig: {\r\n        Data: {},\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.initData();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"编辑\",\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        FileName: \"\",\r\n        Date: [],\r\n        BeginCreateDate: null,\r\n        EndCreateDate: null,\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"FileName\",\r\n            label: \"文件名称\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Date\", // 字段ID\r\n            label: \"创建时间\", // Form的label\r\n            type: \"datePicker\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              type: \"daterange\",\r\n              disabled: false,\r\n              placeholder: \"请输�?..\",\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.BeginCreateDate = dayjs(e[0]).format(\r\n                  \"YYYY-MM-DD\"\r\n                );\r\n                this.ruleForm.EndCreateDate = dayjs(e[1]).format(\"YYYY-MM-DD\");\r\n              }\r\n            },\r\n          },\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"更新数据\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleResetData();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: \"文件名称\",\r\n            key: \"FileName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"播放时长\",\r\n            key: \"PlaybackTime\",\r\n            width: 140,\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"ID\",\r\n            key: \"MediaFileId\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"创建时间\",\r\n            key: \"CreateDate\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"创建来源\",\r\n            key: \"MediaSource\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: \"center\",\r\n          width: \"180\",\r\n        },\r\n        tableActions: [],\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    async handleResetData() {\r\n      const res = await PostMediaFileDataList({});\r\n      console.log(res, \"res\");\r\n      if (res.IsSucceed) {\r\n        this.initData();\r\n        this.$message({\r\n          type: \"success\",\r\n          message: \"更新成功\",\r\n        });\r\n      }\r\n    },\r\n\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1;\r\n      this.initData();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.BeginCreateDate = null;\r\n      this.ruleForm.EndCreateDate = null;\r\n      this.ruleForm.Date = null;\r\n      this.initData();\r\n    },\r\n\r\n    async initData() {\r\n      const res = await GetMediaFileList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n        if (res.Data.Data.length > 0) {\r\n          this.updateDate = res.Data.Data[0].UpdateDate || \"\";\r\n        }\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.initData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.initData();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleEdit(row) {\r\n      this.dialogVisible = true;\r\n      this.componentsConfig.Data = row;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.top {\r\n  margin: 15px 15px 0px 15px;\r\n  padding: 10px 15px;\r\n  background-color: white;\r\n}\r\n.bottom {\r\n  margin: 10px 15px;\r\n  padding: 10px 15px;\r\n  background-color: #fff;\r\n  height: calc(100vh - 190px);\r\n  .tableNotice {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 10px 15px;\r\n    color: rgba(34, 40, 52, 0.65);\r\n    font-size: 14px;\r\n  }\r\n  .tableBox {\r\n    height: calc(100vh - 240px);\r\n  }\r\n}\r\n</style>\r\n"]}]}