{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomTable\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomTable\\index.vue", "mtime": 1755680838854}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomButton", "components", "renderDom", "functional", "props", "render", "Function", "createElement", "renDom", "h", "arguments", "customTableConfig", "type", "Object", "default", "data", "tableHeight", "showIndex", "showSelection", "computed", "customTableObj", "tableColumns", "_this", "jsonArray", "some", "obj", "otherOptions", "keys", "length", "newsColumns", "filter", "for<PERSON>ach", "element", "width", "flexColumn<PERSON>idth", "label", "key", "tableWidth", "getTableWidth", "realWidth", "reduce", "cur", "next", "console", "log", "tableActionsWidth", "columns", "actions", "tableActions", "tableData", "concat", "watch", "newValue", "oldValue", "_this2", "setTimeout", "getTableHeight", "window", "addEventListener", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "table", "document", "querySelector", "clientWidth", "getTextWidth", "str", "html", "innerText", "className", "append<PERSON><PERSON><PERSON>", "offsetWidth", "remove", "getMaxLength", "arr", "_this3", "acc", "item", "calcLen", "prop", "map", "x", "push", "pageHeight", "documentElement", "clientHeight", "otherElementHeight", "handleSizeChange", "val", "$emit", "handleCurrentChange", "selectionChange", "selection", "select", "row", "selectAll", "setSelection", "rowList", "selected", "_this4", "tmp", "find", "Id", "$nextTick", "$refs", "toggleRowSelection"], "sources": ["src/businessComponents/CustomTable/index.vue"], "sourcesContent": ["\n<template>\n  <div class=\"CustomTable\">\n    <div\n      v-if=\"customTableObj.buttonConfig.buttonList.length > 0\"\n      class=\"button\"\n    >\n      <CustomButton :custom-button-config=\"customTableObj.buttonConfig\" />\n    </div>\n    <div\n      class=\"table\"\n      :style=\"{\n        marginTop:\n          customTableObj.buttonConfig.buttonList.length == 0 ? 0 : '10px',\n      }\"\n    >\n      <el-table\n        ref=\"table\"\n        v-loading=\"customTableObj.loading\"\n        :data=\"tableData\"\n        :border=\"customTableObj.border == false ? false : true\"\n        :fit=\"true\"\n        stripe\n        :height=\"customTableObj.height ? customTableObj.height : tableHeight\"\n        :max-height=\"\n          customTableObj.height ? customTableObj.height : tableHeight\n        \"\n        style=\"width: 100%\"\n        :row-key=\"getRowKey\"\n        @selection-change=\"selectionChange\"\n        @select=\"select\"\n        @select-all=\"selectAll\"\n      >\n        <el-table-column\n          v-if=\"showSelection\"\n          type=\"selection\"\n          label=\"selection\"\n        />\n        <el-table-column v-if=\"showIndex\" type=\"index\" label=\"序号\" fixed=\"left\" />\n        <!-- 循环动态列 -->\n        <template v-for=\"(column, index) in tableColumns.columns\">\n          <el-table-column\n            v-if=\"!column.hide\"\n            :key=\"index\"\n            v-bind=\"column.otherOptions\"\n            :prop=\"column.key\"\n            :label=\"column.label\"\n            :min-width=\"column.width\"\n            :resizable=\"true\"\n          >\n            <!-- show-overflow-tooltip -->\n            <!-- :width=\"column.width\" -->\n            <!-- :width=\"flexColumnWidth(column.label, column.key)\" -->\n            <template v-slot=\"scope\">\n              <render-dom\n                v-if=\"column.render\"\n                :render=\"() => column.render(scope.row)\"\n              />\n              <span v-else style=\"white-space: nowrap\">\n                {{\n                  scope.row[column.key] === 0\n                    ? \"0\"\n                    : scope.row[column.key]\n                      ? scope.row[column.key]\n                      : \"-\"\n                }}</span>\n            </template>\n          </el-table-column>\n        </template>\n        <el-table-column\n          v-if=\"tableColumns.actions.length > 0\"\n          label=\"操作\"\n          v-bind=\"customTableObj.operateOptions\"\n          fixed=\"right\"\n          :min-width=\"customTableObj.tableActionsWidth\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              v-for=\"(item, index) in tableColumns.actions\"\n              :key=\"index\"\n              v-bind=\"item.otherOptions\"\n              size=\"mini\"\n              @click=\"item.onclick(scope.$index, scope.row)\"\n            >{{ item.actionLabel }}</el-button>\n            <slot :slot-scope=\"scope.row\" name=\"customBtn\" />\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <div v-if=\"!customTableObj.disablidPagination\" class=\"pagination\">\n      <el-pagination\n        :total=\"customTableObj.total\"\n        :page-sizes=\"customTableObj.pageSizeOptions\"\n        :current-page=\"customTableObj.currentPage\"\n        :page-size=\"customTableObj.pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n      />\n    </div>\n  </div>\n</template>\n<script>\nimport CustomButton from '@/businessComponents/CustomButton/index.vue'\nexport default {\n  components: {\n    CustomButton,\n    renderDom: {\n      functional: true,\n      props: {\n        render: Function\n      },\n      render(createElement, renDom) {\n        return <div>{renDom.props.render()}</div>\n      }\n    }\n  },\n  props: {\n    customTableConfig: {\n      type: Object,\n      default: () => { }\n    }\n  },\n  data() {\n    return {\n      tableHeight: '100%', // 页面高度\n      showIndex: false,\n      showSelection: false\n    }\n  },\n\n  computed: {\n    customTableObj() {\n      return this.customTableConfig\n    },\n    tableColumns() {\n      const jsonArray = this.customTableObj.tableColumns\n      this.showIndex = jsonArray.some(\n        (obj) =>\n          obj.otherOptions &&\n          obj.otherOptions.type &&\n          Object.keys(obj.otherOptions).length > 0 &&\n          obj.otherOptions.type === 'index'\n      )\n      this.showSelection = jsonArray.some(\n        (obj) =>\n          obj.otherOptions &&\n          obj.otherOptions.type &&\n          Object.keys(obj.otherOptions).length > 0 &&\n          obj.otherOptions.type === 'selection'\n      )\n      const newsColumns = jsonArray\n        .filter((obj) => {\n          // 检查 otherOptions 是否存在，并且 type 是否等于 'index'\n          return !obj.otherOptions || obj.otherOptions.type !== 'index'\n        })\n        .filter((obj) => {\n          // 检查 otherOptions 是否存在，并且 type 是否等于 'index'\n          return !obj.otherOptions || obj.otherOptions.type !== 'selection'\n        })\n\n      // .filter(\n      //   (obj) =>\n      //     // obj.otherOptions &&\n      //     // obj.otherOptions.type &&\n      //     // Object.keys(obj.otherOptions).length > 0 &&\n      //     obj.otherOptions.type != \"index\"\n      // )\n      // .filter(\n      //   (obj) =>\n      //     // obj.otherOptions &&\n      //     // obj.otherOptions.type &&\n      //     // Object.keys(obj.otherOptions).length > 0 &&\n      //     obj.otherOptions.type != \"selection\"\n      // );\n      newsColumns.forEach((element) => {\n        element.width = this.flexColumnWidth(element.label, element.key)\n      })\n      const tableWidth = this.getTableWidth()\n      const realWidth = newsColumns.reduce((cur, next) => {\n        return cur + next.width\n      }, 0)\n      console.log(tableWidth, realWidth, 'realWidth')\n\n      newsColumns.forEach((element) => {\n        const width =\n          (tableWidth - this.customTableObj.tableActionsWidth - realWidth) /\n          newsColumns.length\n        if (!element.render) {\n          element.width =\n            tableWidth - this.customTableObj.tableActionsWidth > realWidth\n              ? this.flexColumnWidth(element.label, element.key) + width\n              : this.flexColumnWidth(element.label, element.key)\n        } else {\n          element.width = 140\n        }\n      })\n      return {\n        columns: newsColumns,\n        actions: this.customTableObj.tableActions\n      }\n    },\n    tableData() {\n      const data = []\n      return [].concat(data, this.customTableObj.tableData)\n    }\n  },\n  watch: {\n    // 监视name属性的变化\n    tableColumns(newValue, oldValue) {\n      if (newValue.length > 0) {\n        setTimeout(() => {\n          this.getTableHeight()\n          window.addEventListener('resize', this.getTableHeight) // 监听窗口大小变化，重新计算高度 }\n        }, 0)\n      }\n    }\n  },\n  mounted() { },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.getTableHeight) // 移除事件监听器\n  },\n  methods: {\n    getTableWidth() {\n      // 页面表格宽度\n      const table = document.querySelector('.app-main')\n      if (table) {\n        const tableWidth = table.clientWidth\n        console.log(tableWidth, 'tableWidth')\n        return tableWidth\n      }\n    },\n    getTextWidth(str) {\n      let width = 0\n      const html = document.createElement('span')\n      html.innerText = str\n      html.className = 'getTextWidth'\n      document.querySelector('body').appendChild(html)\n      width = document.querySelector('.getTextWidth').offsetWidth\n      document.querySelector('.getTextWidth').remove()\n      console.log()\n      return width\n    },\n    getMaxLength(arr) {\n      return arr.reduce((acc, item) => {\n        if (item) {\n          const calcLen = this.getTextWidth(item)\n          if (acc < calcLen) {\n            acc = calcLen\n          }\n        }\n        return acc\n      }, 0)\n    },\n    flexColumnWidth(label, prop) {\n      // 1.获取该列的所有数据\n      const arr = this.tableData.map((x) => x[prop])\n      arr.push(label) // 把每列的表头也加进去算\n      // console.log(arr)\n      // 2.计算每列内容最大的宽度 + 表格的内间距（依据实际情况而定）\n      return this.getMaxLength(arr) + 30\n    },\n    getTableHeight() {\n      // 计算页面高度，并减去其他元素的高度（如页眉、页脚等）\n      const pageHeight = document.documentElement.clientHeight\n      const otherElementHeight = 340 // 其他元素的高度，根据实际情况设置\n      this.tableHeight = pageHeight - otherElementHeight\n    },\n    handleSizeChange(val) {\n      this.$emit('handleSizeChange', val)\n    },\n    handleCurrentChange(val) {\n      this.$emit('handleCurrentChange', val)\n    },\n    selectionChange(selection) {\n      this.$emit('handleSelectionChange', selection)\n    },\n    select(selection, row) {\n      this.$emit('select', selection, row)\n    },\n    selectAll(selection) {\n      console.log('ffffffffffffffffff ', selection)\n      this.$emit('selectall', selection, this.tableData)\n    },\n    setSelection(rowList, selected) {\n      rowList.map((tmp) => {\n        const row = this.tableData.find((item) => item.Id === tmp.Id)\n        this.$nextTick(() => {\n          this.$refs.table.toggleRowSelection(row, selected)\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.CustomTable {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  .button {\n    display: flex;\n    flex-direction: row;\n    justify-content: flex-end;\n  }\n  .table {\n    flex: 1;\n    overflow: hidden;\n    margin-top: 10px;\n  }\n  .pagination {\n    margin-top: 10px;\n    display: flex;\n    flex-direction: row;\n    justify-content: flex-end;\n  }\n  .no-wrap-cell {\n    white-space: nowrap;\n  }\n  // display: flex;\n  // flex-direction: column;\n  // background-color: white;\n  // // padding: 10px 15px;\n  // .table{\n  //   padding: 2px 5px;\n  // }\n  // .el-pagination{\n  //   display: flex;\n  //   justify-content: flex-end;\n\n  // }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwGA,OAAAA,YAAA;AACA;EACAC,UAAA;IACAD,YAAA,EAAAA,YAAA;IACAE,SAAA;MACAC,UAAA;MACAC,KAAA;QACAC,MAAA,EAAAC;MACA;MACAD,MAAA,WAAAA,OAAAE,aAAA,EAAAC,MAAA;QAAA,IAAAC,CAAA,GAAAC,SAAA;QACA,OAAAD,CAAA,SAAAD,MAAA,CAAAJ,KAAA,CAAAC,MAAA;MACA;IACA;EACA;EACAD,KAAA;IACAO,iBAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MAAA;MACAC,SAAA;MACAC,aAAA;IACA;EACA;EAEAC,QAAA;IACAC,cAAA,WAAAA,eAAA;MACA,YAAAT,iBAAA;IACA;IACAU,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,SAAA,QAAAH,cAAA,CAAAC,YAAA;MACA,KAAAJ,SAAA,GAAAM,SAAA,CAAAC,IAAA,CACA,UAAAC,GAAA;QAAA,OACAA,GAAA,CAAAC,YAAA,IACAD,GAAA,CAAAC,YAAA,CAAAd,IAAA,IACAC,MAAA,CAAAc,IAAA,CAAAF,GAAA,CAAAC,YAAA,EAAAE,MAAA,QACAH,GAAA,CAAAC,YAAA,CAAAd,IAAA;MAAA,CACA;MACA,KAAAM,aAAA,GAAAK,SAAA,CAAAC,IAAA,CACA,UAAAC,GAAA;QAAA,OACAA,GAAA,CAAAC,YAAA,IACAD,GAAA,CAAAC,YAAA,CAAAd,IAAA,IACAC,MAAA,CAAAc,IAAA,CAAAF,GAAA,CAAAC,YAAA,EAAAE,MAAA,QACAH,GAAA,CAAAC,YAAA,CAAAd,IAAA;MAAA,CACA;MACA,IAAAiB,WAAA,GAAAN,SAAA,CACAO,MAAA,WAAAL,GAAA;QACA;QACA,QAAAA,GAAA,CAAAC,YAAA,IAAAD,GAAA,CAAAC,YAAA,CAAAd,IAAA;MACA,GACAkB,MAAA,WAAAL,GAAA;QACA;QACA,QAAAA,GAAA,CAAAC,YAAA,IAAAD,GAAA,CAAAC,YAAA,CAAAd,IAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAiB,WAAA,CAAAE,OAAA,WAAAC,OAAA;QACAA,OAAA,CAAAC,KAAA,GAAAX,KAAA,CAAAY,eAAA,CAAAF,OAAA,CAAAG,KAAA,EAAAH,OAAA,CAAAI,GAAA;MACA;MACA,IAAAC,UAAA,QAAAC,aAAA;MACA,IAAAC,SAAA,GAAAV,WAAA,CAAAW,MAAA,WAAAC,GAAA,EAAAC,IAAA;QACA,OAAAD,GAAA,GAAAC,IAAA,CAAAT,KAAA;MACA;MACAU,OAAA,CAAAC,GAAA,CAAAP,UAAA,EAAAE,SAAA;MAEAV,WAAA,CAAAE,OAAA,WAAAC,OAAA;QACA,IAAAC,KAAA,GACA,CAAAI,UAAA,GAAAf,KAAA,CAAAF,cAAA,CAAAyB,iBAAA,GAAAN,SAAA,IACAV,WAAA,CAAAD,MAAA;QACA,KAAAI,OAAA,CAAA3B,MAAA;UACA2B,OAAA,CAAAC,KAAA,GACAI,UAAA,GAAAf,KAAA,CAAAF,cAAA,CAAAyB,iBAAA,GAAAN,SAAA,GACAjB,KAAA,CAAAY,eAAA,CAAAF,OAAA,CAAAG,KAAA,EAAAH,OAAA,CAAAI,GAAA,IAAAH,KAAA,GACAX,KAAA,CAAAY,eAAA,CAAAF,OAAA,CAAAG,KAAA,EAAAH,OAAA,CAAAI,GAAA;QACA;UACAJ,OAAA,CAAAC,KAAA;QACA;MACA;MACA;QACAa,OAAA,EAAAjB,WAAA;QACAkB,OAAA,OAAA3B,cAAA,CAAA4B;MACA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,IAAAlC,IAAA;MACA,UAAAmC,MAAA,CAAAnC,IAAA,OAAAK,cAAA,CAAA6B,SAAA;IACA;EACA;EACAE,KAAA;IACA;IACA9B,YAAA,WAAAA,aAAA+B,QAAA,EAAAC,QAAA;MAAA,IAAAC,MAAA;MACA,IAAAF,QAAA,CAAAxB,MAAA;QACA2B,UAAA;UACAD,MAAA,CAAAE,cAAA;UACAC,MAAA,CAAAC,gBAAA,WAAAJ,MAAA,CAAAE,cAAA;QACA;MACA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;EACAC,aAAA,WAAAA,cAAA;IACAH,MAAA,CAAAI,mBAAA,gBAAAL,cAAA;EACA;EACAM,OAAA;IACAxB,aAAA,WAAAA,cAAA;MACA;MACA,IAAAyB,KAAA,GAAAC,QAAA,CAAAC,aAAA;MACA,IAAAF,KAAA;QACA,IAAA1B,UAAA,GAAA0B,KAAA,CAAAG,WAAA;QACAvB,OAAA,CAAAC,GAAA,CAAAP,UAAA;QACA,OAAAA,UAAA;MACA;IACA;IACA8B,YAAA,WAAAA,aAAAC,GAAA;MACA,IAAAnC,KAAA;MACA,IAAAoC,IAAA,GAAAL,QAAA,CAAAzD,aAAA;MACA8D,IAAA,CAAAC,SAAA,GAAAF,GAAA;MACAC,IAAA,CAAAE,SAAA;MACAP,QAAA,CAAAC,aAAA,SAAAO,WAAA,CAAAH,IAAA;MACApC,KAAA,GAAA+B,QAAA,CAAAC,aAAA,kBAAAQ,WAAA;MACAT,QAAA,CAAAC,aAAA,kBAAAS,MAAA;MACA/B,OAAA,CAAAC,GAAA;MACA,OAAAX,KAAA;IACA;IACA0C,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,OAAAD,GAAA,CAAApC,MAAA,WAAAsC,GAAA,EAAAC,IAAA;QACA,IAAAA,IAAA;UACA,IAAAC,OAAA,GAAAH,MAAA,CAAAV,YAAA,CAAAY,IAAA;UACA,IAAAD,GAAA,GAAAE,OAAA;YACAF,GAAA,GAAAE,OAAA;UACA;QACA;QACA,OAAAF,GAAA;MACA;IACA;IACA5C,eAAA,WAAAA,gBAAAC,KAAA,EAAA8C,IAAA;MACA;MACA,IAAAL,GAAA,QAAA3B,SAAA,CAAAiC,GAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAF,IAAA;MAAA;MACAL,GAAA,CAAAQ,IAAA,CAAAjD,KAAA;MACA;MACA;MACA,YAAAwC,YAAA,CAAAC,GAAA;IACA;IACApB,cAAA,WAAAA,eAAA;MACA;MACA,IAAA6B,UAAA,GAAArB,QAAA,CAAAsB,eAAA,CAAAC,YAAA;MACA,IAAAC,kBAAA;MACA,KAAAxE,WAAA,GAAAqE,UAAA,GAAAG,kBAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAC,KAAA,qBAAAD,GAAA;IACA;IACAE,mBAAA,WAAAA,oBAAAF,GAAA;MACA,KAAAC,KAAA,wBAAAD,GAAA;IACA;IACAG,eAAA,WAAAA,gBAAAC,SAAA;MACA,KAAAH,KAAA,0BAAAG,SAAA;IACA;IACAC,MAAA,WAAAA,OAAAD,SAAA,EAAAE,GAAA;MACA,KAAAL,KAAA,WAAAG,SAAA,EAAAE,GAAA;IACA;IACAC,SAAA,WAAAA,UAAAH,SAAA;MACAnD,OAAA,CAAAC,GAAA,wBAAAkD,SAAA;MACA,KAAAH,KAAA,cAAAG,SAAA,OAAA7C,SAAA;IACA;IACAiD,YAAA,WAAAA,aAAAC,OAAA,EAAAC,QAAA;MAAA,IAAAC,MAAA;MACAF,OAAA,CAAAjB,GAAA,WAAAoB,GAAA;QACA,IAAAN,GAAA,GAAAK,MAAA,CAAApD,SAAA,CAAAsD,IAAA,WAAAxB,IAAA;UAAA,OAAAA,IAAA,CAAAyB,EAAA,KAAAF,GAAA,CAAAE,EAAA;QAAA;QACAH,MAAA,CAAAI,SAAA;UACAJ,MAAA,CAAAK,KAAA,CAAA3C,KAAA,CAAA4C,kBAAA,CAAAX,GAAA,EAAAI,QAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}