{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\szcjPJEquipmentAssetList\\index_old.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\szcjPJEquipmentAssetList\\index_old.vue", "mtime": 1755674552420}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index_old.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index_old.vue", "sourceRoot": "src/views/business/equipmentManagement/szcjPJEquipmentAssetList", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 szcjPJEquipmentAssetList\">\r\n    <custom-layout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"submitForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </custom-layout>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      width=\"30%\"\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport {\r\n  DeleteEquipmentAssetEntity,\r\n  ExportEquipmentAssetsList,\r\n  GetEquipmentAssetPageList,\r\n  AssetImportTemplatePJ,\r\n  AssetEquipmentImportPJ,\r\n  ExportEquipmentListPJ,\r\n  ExportEquipmentListPJByData,\r\n  GetEquipmentAssetPageListPJ,\r\n  GetEquipmentAssetPageListPJByData,\r\n  GetDictionaryDetailListByParentId\r\n} from '@/api/business/eqptAsset'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { timeFormat } from '@/filters'\r\nimport { getDictionary } from '@/utils/common'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport Print from './components/print.vue'\r\nimport importDialog from '@/views/business/energyManagement/components/import.vue'\r\nimport exportInfo from '@/views/business/energyManagement/mixins/export.js'\r\nexport default {\r\n  name: 'EquipmentAssetList',\r\n  components: { CustomTable, CustomLayout, CustomForm, Print, importDialog },\r\n  mixins: [addRouterPage, exportInfo],\r\n  data() {\r\n    return {\r\n      componentsConfig: {\r\n        interfaceName: AssetEquipmentImportPJ\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.fetchData()\r\n        }\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/add',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'EquipmentAssetListAdd',\r\n          meta: { title: `新增` }\r\n        },\r\n        {\r\n          path: this.$route.path + '/edit',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'EquipmentAssetListEdit',\r\n          meta: { title: `编辑` }\r\n        },\r\n        {\r\n          path: this.$route.path + '/view',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'EquipmentAssetListView',\r\n          meta: { title: `查看` }\r\n        },\r\n        {\r\n          path: this.$route.path + '/dataAcquisition',\r\n          hidden: true,\r\n          component: () => import('./dataAcquisition.vue'),\r\n          name: 'DataAcquisition',\r\n          meta: { title: `查看数据` }\r\n        },\r\n        {\r\n          path: this.$route.path + '/equipmentData',\r\n          hidden: true,\r\n          component: () => import('./equipmentData.vue'),\r\n          name: 'PJEquipmentData',\r\n          meta: { title: `设备数采` }\r\n        }\r\n      ],\r\n      ruleForm: {\r\n        EquipmentName: '',\r\n        departName: '',\r\n        EquipmentType: '',\r\n        EquipmentItemType: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'EquipmentName',\r\n            label: '设备名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentType',\r\n            label: '设备类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              this.customForm.formItems.find(\r\n                (v) => v.key === 'EquipmentItemType'\r\n              ).options = []\r\n              this.ruleForm.EquipmentItemType = ''\r\n              GetDictionaryDetailListByParentId(e).then((res) => {\r\n                this.customForm.formItems.find(\r\n                  (v) => v.key === 'EquipmentItemType'\r\n                ).options = res.Data.map((v) => {\r\n                  return {\r\n                    label: v.Display_Name,\r\n                    value: v.Id\r\n                  }\r\n                })\r\n              })\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentItemType',\r\n            label: '设备子类',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'departName',\r\n            label: '所属部门',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        // 表格\r\n        pageSizeOptions: [20, 40, 60, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: '设备名称',\r\n            key: 'Display_Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备SN',\r\n            key: 'Serial_Number',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备编号',\r\n            key: 'Device_Number',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '品牌',\r\n            key: 'Brand',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '规格型号',\r\n            key: 'Spec',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备类型',\r\n            key: 'Type_Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备子类',\r\n            key: 'Type_Detail_Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '厂家名称',\r\n            key: 'Manufacturer',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '厂家联系方式',\r\n            key: 'Manufacturer_Contact_Info',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '经销商',\r\n            key: 'Dealer',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '经销商联系方式',\r\n            key: 'Dealer_Contact_Info',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '工程师',\r\n            key: 'Engineer',\r\n            otherOptions: {\r\n              align: 'center'\r\n\r\n            }\r\n          },\r\n          {\r\n            label: '工程师联系方式',\r\n            key: 'Engineer_Contact_Info',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '所属部门',\r\n            key: 'Department',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '安装位置',\r\n            key: 'Position',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '安装时间',\r\n            key: 'Install_Date',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备管理员',\r\n            key: 'Administrator',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备管理员联系方式',\r\n            key: 'Administrator_Contact_Info',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '用途',\r\n            key: 'Usage',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center',\r\n          width: '285px'\r\n        },\r\n        buttonConfig: {\r\n          buttonList: [\r\n            // {\r\n            //   text: \"新增\",\r\n            //   round: false, // 是否圆角\r\n            //   plain: false, // 是否朴素\r\n            //   circle: false, // 是否圆形\r\n            //   loading: false, // 是否加载中\r\n            //   disabled: false, // 是否禁用\r\n            //   icon: \"\", //  图标\r\n            //   autofocus: false, // 是否聚焦\r\n            //   type: \"primary\", // primary / success / warning / danger / info / text\r\n            //   size: \"small\", // medium / small / mini\r\n            //   onclick: (item) => {\r\n            //     console.log(item);\r\n            //     this.handleCreate();\r\n            //   },\r\n            // },\r\n            // {\r\n            //   text: \"下载模板\",\r\n            //   disabled: false, // 是否禁用\r\n            //   onclick: (item) => {\r\n            //     console.log(item);\r\n            //     this.handleDownTemplate();\r\n            //   },\r\n            // },\r\n            // {\r\n            //   text: \"批量导入\",\r\n            //   disabled: false, // 是否禁用\r\n            //   onclick: (item) => {\r\n            //     console.log(item);\r\n            //     this.currentComponent = \"importDialog\";\r\n            //     this.dialogVisible = true;\r\n            //     this.dialogTitle = \"批量导入\";\r\n            //   },\r\n            // },\r\n            {\r\n              text: '批量导出',\r\n              disabled: false,\r\n              onclick: () => {\r\n                this.handleExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        tableActionsWidth: 280,\r\n        tableActions: [\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '打印二维码',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handlePrintQr(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleInfo(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '查看数据',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.viewData(row)\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      multipleSelection: [],\r\n      currentComponent: 'Print',\r\n      dialogVisible: false\r\n    }\r\n  },\r\n  // watch: {\r\n  //   \"multipleSelection.length\": {\r\n  //     handler(newValue) {\r\n  //       this.customTableConfig.buttonConfig.buttonList.find(\r\n  //         (item) => item.text == \"批量导出\"\r\n  //       ).disabled = !newValue;\r\n  //     },\r\n  //     immediate: true,\r\n  //   },\r\n  // },\r\n  mounted() {\r\n    // this.getGridByCode(\"EquipmentAssetList\");\r\n    this.fetchData()\r\n    getDictionary('deviceType').then((res) => {\r\n      const item = this.customForm.formItems.find(\r\n        (v) => v.key === 'EquipmentType'\r\n      )\r\n      console.log('res', res, item)\r\n      item.options = res.map((v) => {\r\n        return {\r\n          label: v.Display_Name,\r\n          value: v.Id\r\n        }\r\n      })\r\n    })\r\n  },\r\n  activated() {\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    resetForm() {\r\n      this.ruleForm = {}\r\n      this.customForm.formItems.find(\r\n        (v) => v.key === 'EquipmentItemType'\r\n      ).options = []\r\n      this.fetchData()\r\n    },\r\n    submitForm() {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData()\r\n    },\r\n    fetchData() {\r\n      GetEquipmentAssetPageListPJByData({\r\n        Display_Name: this.ruleForm.EquipmentName,\r\n        Device_Type_Id: this.ruleForm.EquipmentType,\r\n        Device_Type_Detail_Id: this.ruleForm.EquipmentItemType,\r\n        Department: this.ruleForm.departName,\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.tableData = res.Data.Data.map((v) => {\r\n            v.Install_Date = timeFormat(v.Install_Date)\r\n            return v\r\n          })\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleCreate() {\r\n      // this.dialogTitle = '新增'\r\n      // this.dialogVisible = true\r\n      this.$router.push({\r\n        name: 'EquipmentAssetListAdd',\r\n        query: { pg_redirect: this.$route.name, type: 1 }\r\n      })\r\n    },\r\n    handleSizeChange(val) {\r\n      this.customTableConfig.pageSize = val\r\n      this.fetchData({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: val\r\n      })\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.customTableConfig.currentPage = val\r\n      this.fetchData({ Page: val, PageSize: this.customTableConfig.pageSize })\r\n    },\r\n    handleSelectionChange(data) {\r\n      console.log(data)\r\n      this.multipleSelection = data\r\n    },\r\n    // handleExport() {\r\n    //   console.log('handleExport')\r\n    //   ExportEquipmentAssetsList({\r\n    //     ids: this.multipleSelection.map(v => v.Id)\r\n    //   }).then(res => {\r\n    //     if (res.IsSucceed) {\r\n    //       downloadFile(res.Data)\r\n    //     } else {\r\n    //       this.$message({\r\n    //         message: res.Message,\r\n    //         type: 'error'\r\n    //       })\r\n    //     }\r\n    //   })\r\n    // },\r\n    // v2 导出设备资产列表\r\n    handleExport() {\r\n      console.log('handleExport')\r\n      ExportEquipmentListPJByData({\r\n        Id: this.multipleSelection.map((v) => v.Id).toString(),\r\n        Display_Name: this.ruleForm.EquipmentName,\r\n        Device_Type_Id: this.ruleForm.EquipmentType,\r\n        Department: this.ruleForm.departName,\r\n        Device_Type_Detail_Id: this.ruleForm.EquipmentItemType\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleDelete(id) {\r\n      this.$confirm('是否删除该设备, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeleteEquipmentAssetEntity({ ids: [id] }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    handleEdit(id) {\r\n      this.$router.push({\r\n        name: 'EquipmentAssetListEdit',\r\n        query: { pg_redirect: this.$route.name, id, type: 2 }\r\n      })\r\n    },\r\n    handlePrintQr(v) {\r\n      this.dialogVisible = true\r\n      this.dialogTitle = '设备二维码'\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].setCode(v)\r\n      })\r\n    },\r\n    handleInfo(id) {\r\n      this.$router.push({\r\n        name: 'EquipmentAssetListView',\r\n        query: { pg_redirect: this.$route.name, id, type: 3 }\r\n      })\r\n    },\r\n    // getGridByCode(code) {\r\n    //   GetGridByCode({ code }).then((res) => {\r\n    //     console.log(res.Data);\r\n    //     if (res.IsSucceed) {\r\n    //       const Grid = res.Data.Grid;\r\n    //       this.customTableConfig.tableColumns = res.Data?.ColumnList.map(\r\n    //         (item) => {\r\n    //           return Object.assign(\r\n    //             {},\r\n    //             {\r\n    //               key: item.Code,\r\n    //               label: item.Display_Name,\r\n    //               width: item.Width,\r\n    //               otherOptions: {\r\n    //                 align: item.Align ? item.Align : \"center\",\r\n    //                 sortable: item.Is_Sort,\r\n    //                 fixed: item.Is_Frozen === false ? false : \"left\",\r\n    //                 Digit_Number: item.Digit_Number,\r\n    //               },\r\n    //             }\r\n    //           );\r\n    //         }\r\n    //       );\r\n    //       if (Grid.Is_Select) {\r\n    //         this.customTableConfig.tableColumns.unshift({\r\n    //           otherOptions: {\r\n    //             type: \"selection\",\r\n    //             align: \"center\",\r\n    //           },\r\n    //         });\r\n    //       }\r\n    //       this.customTableConfig.pageSize = Number(Grid.Row_Number);\r\n    //     }\r\n    //   });\r\n    // },\r\n    // 查看数据\r\n    viewData(data) {\r\n      data.num = 1\r\n      data.historyRouter = this.$route.name\r\n      this.$router.push({\r\n        name: 'PJEquipmentData',\r\n        query: { pg_redirect: this.$route.name, data }\r\n      })\r\n\r\n      this.$store.dispatch('eqpt/changeEqptData', data)\r\n    },\r\n    // 下载模板\r\n    handleDownTemplate() {\r\n      AssetImportTemplatePJ({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data, '设备资产导入模板')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.szcjPJEquipmentAssetList{\r\n  /* height: calc(100vh - 90px); */\r\n  /* overflow: hidden; */\r\n}\r\n</style>\r\n"]}]}