<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          @submitForm="searchForm"
          @resetForm="resetForm"
        >
          <template #formSlot="{ slotScope }">
            <el-cascader
              v-if="slotScope.key == 'DeptId'"
              style="width: 100%"
              ref="departmentPersonnelCascader"
              v-model="departmentPersonnelValue"
              :options="departmentPersonnelOptions"
              :props="{
                expandTrigger: 'hover',
                checkStrictly: true,
              }"
              :clearable="true"
              @change="departmentPersonnelHandleChange"
            ></el-cascader>
          </template>
        </CustomForm>
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @closed="closedDialog"
    >
      <component
        :is="currentComponent"
        ref="dialogRef"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";
import {
  VehiclesGetVehicleList,
  VehiclesDelVehicle,
  VehiclesExportData,
  VehiclesGetDeptList,
  GetDictionaryDetailListByCode,
  VehiclesDownloadTemplate,
  VehiclesImportDataStream,
} from "@/api/business/vehicleBarrier.js";
import baseInfo from "./dialog/baseInfo.vue";
import importDialog from "@/views/business/vehicleBarrier/components/import.vue";
import exportInfo from "@/views/business/vehicleBarrier/pJVehicleBarrier/mixins/export";
import { downloadFile } from "@/utils/downloadFile";
import addRouterPage from "@/mixins/add-router-page";
export default {
  Name: "internalVehicleManagement",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
    baseInfo,
    importDialog,
  },
  mixins: [exportInfo, addRouterPage],
  data() {
    return {
      // 部门人员
      departmentPersonnelValue: [],
      departmentPersonnelOptions: [],

      ruleForm: {
        Number: "",
        UserName: "",
        UserPhone: "",
        DeptId: "",
        VehicleType: "",
      },
      componentsConfig: {
        interfaceName: VehiclesImportDataStream,
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogTitle: "",
      dialogVisible: false,
      currentComponent: null,
      //
      PassImg: "", // 图片
      vehicleTypeOption: [], // 车辆类型
      tableSelection: [],
      selectIds: [],
      customForm: {
        formItems: [
          {
            key: "Number", // 字段ID
            label: "车牌号码", // Form的label
            type: "input", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
            },
            input: (e) => {},
            change: () => {},
          },
          {
            key: "UserName",
            label: "车主姓名",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            input: (e) => {},
            change: () => {},
          },
          {
            key: "UserPhone",
            label: "联系方式",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            input: (e) => {},
            change: () => {},
          },
          {
            key: "DeptId",
            label: "所属部门",
            type: "slot",
          },
          // {
          //   key: "DeptId",
          //   label: "所属部门",
          //   type: "select",
          //   options: [],
          //   otherOptions: {
          //     clearable: true,
          //   },
          //   change: (e) => {},
          // },
          {
            key: "VehicleType",
            label: "车辆类型",
            type: "select",
            options: [],
            otherOptions: {
              clearable: true,
            },
            change: (e) => {},
          },
        ],
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              key: "batch",
              disabled: false, // 是否禁用
              text: "新增",
              type: "primary",
              onclick: (item) => {
                console.log(item);
                this.handleCreate();
              },
            },
            {
              text: "下载模板",
              onclick: (item) => {
                this.ExportData(
                  [],
                  "内部车辆管理模板",
                  VehiclesDownloadTemplate
                );
              },
            },
            {
              text: "批量导入",
              onclick: (item) => {
                this.currentComponent = "importDialog";
                this.dialogVisible = true;
                this.dialogTitle = "批量导入";
                this.componentsConfig = {
                  interfaceName: VehiclesImportDataStream,
                };
              },
            },

            {
              key: "batch",
              disabled: false, // 是否禁用
              text: "批量导出",
              onclick: (item) => {
                this.ExportData(
                  this.ruleForm,
                  "内部车辆管理",
                  VehiclesExportData
                );
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [20, 50, 80, 100],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        height: "100%",
        tableActionsWidth: "160",
        tableColumns: [
          {
            label: "车牌号码",
            key: "Number",
            otherOptions: {
              fixed: 'left'
            },
          },
          {
            label: "车主姓名",
            key: "UserName",
          },
          {
            label: "车主联系方式",
            key: "UserPhone",
          },
          {
            label: "所属部门",
            key: "DeptName",
          },

          {
            label: "车辆类型",
            key: "VehicleTypeName",
          },
          {
            label: "更新人",
            key: "ModifyUserName",
          },
          {
            label: "更新时间",
            key: "ModifyDate",
          },
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: "查看",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.$router.push({
                name: "InternalVehicleManagementView",
                query: { pg_redirect: this.$route.name, Id: row.Id },
              });
            },
          },
          {
            actionLabel: "编辑",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, "edit");
            },
          },
          {
            actionLabel: "删除",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleDelete(index, row);
            },
          },
        ],
      },
      addPageArray: [
        {
          path: this.$route.path + "/view",
          hidden: true,
          component: () => import("./dialog/view.vue"),
          meta: { title: "内部车辆管理详情" },
          name: "InternalVehicleManagementView",
        },
      ],
    };
  },
  created() {
    // 测试
    this.init();
    //
    this.getDictionaryDetailListByCode();

    this.vehiclesGetDeptList();
  },
  methods: {
    // 车辆类型
    async getDictionaryDetailListByCode() {
      await GetDictionaryDetailListByCode({
        dictionaryCode: "VehiclesType",
      }).then((res) => {
        if (res.IsSucceed) {
          this.customForm.formItems.find(
            (item) => item.key == "VehicleType"
          ).options = res.Data.map((item) => ({
            label: item.Display_Name,
            value: item.Value,
          }));
        } else {
          this.$message({
            type: "error",
            message: res.Message,
          });
        }
      });
    },
    // 部门人员change
    departmentPersonnelHandleChange(values) {
      console.log(values, "values");
      this.ruleForm.DeptId = values[values.length - 1];
      // let userInfo =
      //   this.$refs["departmentPersonnelCascader"].getCheckedNodes()[0].data;
      // this.ruleForm.UserPhone = userInfo.UserPhone;
      // this.ruleForm.UserDeptName = userInfo.UserDeptName;
      // this.ruleForm.UserDept = userInfo.UserDeptId;
      // this.ruleForm.UserId = userInfo.Id;
      // this.ruleForm.UserName = userInfo.Name;
    },

    // 部门
    async vehiclesGetDeptList() {
      await VehiclesGetDeptList({}).then((res) => {
        if (res.IsSucceed) {
          console.log(res, "res");
          this.departmentPersonnelOptions = this.setCascadeData([res.Data]);
          // this.customForm.formItems.find(
          //   (item) => item.key == "DeptId"
          // ).options = res.Data.map((item) => ({
          //   label: item.DeptName,
          //   value: item.DeptId,
          // }));
        } else {
          this.$message({
            type: "error",
            message: res.Message,
          });
        }
      });
    },
    // 递归
    setCascadeData(data) {
      return data.map((item) => {
        //   label: item.DeptName,
        //   value: item.DeptId,
        let newItem = { ...item, label: item.DeptName, value: item.DeptId };
        if (newItem.Child && newItem.Child.length > 0) {
          newItem.children = this.setCascadeData(newItem.Child);
        } else {
          delete newItem.Child;
        }
        return newItem;
      });
    },

    // v2 版本导出
    async handleAllExport() {
      const res = await VehiclesExportData({
        // Id: this.tableSelection.map((item) => item.Id).toString(),
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        console.log(res);
        downloadFile(res.Data, "21");
      } else {
        this.$message.error(res.Message);
      }
    },
    searchForm(data) {
      this.customTableConfig.currentPage = 1;
      console.log(data);
      this.onFresh();
    },
    resetForm() {
      this.departmentPersonnelValue = [];
      this.ruleForm.DeptId = "";
      this.onFresh();
    },
    onFresh() {
      this.fetchData();
    },
    async init() {
      await this.fetchData();
    },
    async fetchData() {
      const res = await VehiclesGetVehicleList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data;
        this.customTableConfig.total = res.Data.Total;
      }
    },
    handleCreate() {
      this.currentComponent = "baseInfo";
      this.dialogTitle = "新增";
      this.dialogVisible = true;
      this.componentsConfig = {
        type: "add",
      };
    },
    handleDelete(index, row) {
      console.log(index, row);
      console.log(this);
      this.$confirm("确认删除?", {
        type: "warning",
      })
        .then(async (_) => {
          const res = await VehiclesDelVehicle({
            Id: row.Id,
          });
          if (res.IsSucceed) {
            this.$message({
              message: "删除成功",
              type: "success",
            });
            this.init();
          } else {
            this.$message({
              message: res.Message,
              type: "error",
            });
          }
        })
        .catch((_) => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleEdit(index, row, type) {
      console.log(index, row, type);
      this.currentComponent = "baseInfo";
      if (type === "edit") {
        this.dialogTitle = "编辑";
      }
      this.componentsConfig = {
        row,
        type,
      };
      this.dialogVisible = true;
    },
    // 关闭弹窗
    closedDialog() {
      this.dialogVisible = false;
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.onFresh();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.onFresh();
    },
    handleSelectionChange(selection) {
      // const Ids = [];
      this.tableSelection = selection;
      // this.tableSelection.forEach((item) => {
      //   Ids.push(item.Id);
      // });
      // this.selectIds = Ids;
    },
    handleview(row) {
      this.dialogVisible = true;
      this.PassImg = row.PassImg;
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/views/business/vehicleBarrier/index.scss";

.imgwapper {
  width: 100px;
  height: 100px;
}
.empty-img {
  text-align: center;
}
</style>
