<template>
  <div class="CustomLayout">
    <div v-if="isShowSearchForm" class="searchForm">
      <slot name="searchForm" />
    </div>
    <div v-if="isShowLayoutChart" class="layoutChart">
      <slot name="layoutChart" />
    </div>
    <div class="layoutTable">
      <slot name="layoutTable" />
    </div>
  </div>
</template>

<script>
export default {
  // layoutConfig
  props: {
    layoutConfig: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {}
  },
  computed: {
    isShowSearchForm() {
      return (this.layoutObj && this.layoutObj.isShowSearchForm) || true
    },
    isShowLayoutChart() {
      return (this.layoutObj && this.layoutObj.isShowLayoutChart) || false
    },
    layoutObj() {
      return this.layoutConfig
    }
  }
}
</script>

<style lang="scss" scoped>
.CustomLayout {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .searchForm {
    margin: 0 0 10px 0;
    padding: 10px 15px;
    background-color: white;
  }
  .layoutChart {
    margin: 15px 15px 0px 15px;
    padding: 10px 15px;
    background-color: white;
  }
  .layoutTable {
    flex: 1;
    // margin: 10px 15px;
    padding: 10px 15px;
    background-color: white;
  }
}
</style>
