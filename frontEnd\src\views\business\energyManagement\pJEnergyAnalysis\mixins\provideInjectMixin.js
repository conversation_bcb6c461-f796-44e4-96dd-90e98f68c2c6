/**
 * 用于处理 provide/inject 响应式数据的 mixin
 * 解决爷爷组件和孙子组件之间的数据传递响应式问题
 */
export default {
  inject: ['DateType', 'StartTime', 'randomInteger'],
  
  computed: {
    // 将注入的函数转换为响应式的 computed 属性
    currentDateType() {
      return this.DateType()
    },
    currentStartTime() {
      return this.StartTime()
    },
    currentRandomInteger() {
      return this.randomInteger()
    },
    parentData() {
      return {
        DateType: this.currentDateType,
        StartTime: this.currentStartTime,
        randomInteger: this.currentRandomInteger
      }
    }
  },
  
  watch: {
    // 监听每个 computed 属性的变化
    currentDateType: {
      handler() {
        // 子组件需要实现 refreshData 方法
        if (this.refreshData && typeof this.refreshData === 'function') {
          this.refreshData()
        }
      }
    },
    currentStartTime: {
      handler() {
        if (this.refreshData && typeof this.refreshData === 'function') {
          this.refreshData()
        }
      }
    },
    currentRandomInteger: {
      handler() {
        if (this.refreshData && typeof this.refreshData === 'function') {
          this.refreshData()
        }
      }
    }
  }
}
