<template>
  <div class="app-container abs100">
    <div
      class="card"
      v-for="(item, index) in cardList"
      :key="index"
      :style="{
        backgroundColor: item.backgroundcolor,
        height: '100px',
        borderRadius: '8px',
        margin: item.backgroundcolor ? '' : 'auto'
      }"
    >
      <div class="cardleft" :style="item.backgroundcolor ? 'margin-right: 16px;' : ''">
        <img :src="item.cardimg" />
      </div>
      <div class="cardright">
        <div class="title">{{ item.cardtitle }}</div>
        <div class="num" :style="{ color: item.numcolor }" v-if="item.cardtitle">
          <count-to
            :startVal="0"
            :endVal="item.cardNum"
            :duration="50"
            :decimals="item.unit === '件' ? 0 : 3"
          ></count-to>
          <span>{{ item.unit }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import countTo from 'vue-count-to'
export default {
  components: { countTo },
  props: {
    cardList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  computed: {}
}
</script>

<style lang="scss" scoped>
.layout {
  height: 130px;
  font-family: PingFang SC, PingFang SC;
  display: flex;
  .card {
    flex: 1;
    float: left;
    margin: 0px 12px; /* 添加间距 */
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
    background-color: #ffffff; /* 设置背景颜色 */

    .cardleft {
      float: left;
    }
    .cardright {
      float: left;
      font-style: normal;
      .title {
        height: 25px;
        font-weight: bold;
        font-size: 1rem;
        color: #333333;
        line-height: 25px;
      }
      .num {
        height: 33px;
        font-weight: 600;
        font-size: 1.2rem;
        line-height: 33px;
      }
    }
  }
}
</style>
