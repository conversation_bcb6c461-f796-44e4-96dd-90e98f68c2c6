<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'

import DialogForm from './dialogForm.vue'
import DialogFormLook from './dialogFormLook.vue'

// import { downloadFile } from '@/utils/downloadFile'
import deviceTypeMixins from '../../mixins/index.js'
// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'
// import CustomButton from '@/businessComponents/CustomButton/index.vue'

import {
  GetQuotaList,
  DeleteQuota,
  DeleteAllQuota,
  GetHazchemDTCList
} from '@/api/business/hazardousChemicals'
// import * as moment from 'moment'
import dayjs from 'dayjs'
export default {
  name: '',
  components: {
    CustomTable,
    // CustomButton,
    // CustomTitle,
    CustomForm,
    CustomLayout
  },
  mixins: [deviceTypeMixins],
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {},
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      tableSelection: [],

      ruleForm: {
        EquipmentTypeId: '',
        AlarmType: '',
        TriggerItem: ''
      },
      customForm: {
        formItems: [
          {
            key: 'EquipmentTypeId', // 字段ID
            label: '设备类型', // Form的label
            type: 'select', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            options: [],
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: '请输入设备类型'
            },
            width: '240px',
            change: (e) => {
              this.customForm.formItems.find((item) => item.key === 'TriggerItem').otherOptions.disabled = !e
              this.ruleForm.TriggerItem = ''
              this.getEnviromentDTCList(GetHazchemDTCList, e)
              console.log(e)
            }
          },
          {
            key: 'TriggerItem',
            label: '配置项',
            type: 'select',

            options: [],
            otherOptions: {
              clearable: true,
              disabled: true,
              placeholder: '请选择...'
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'AlarmType',
            label: '告警类型',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true,
              placeholder: '请选择...'
            },
            change: (e) => {
              console.log(e)
            }
          }

        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: '新增',
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: false, // 是否禁用
              icon: '', //  图标
              autofocus: false, // 是否聚焦
              type: 'primary', // primary / success / warning / danger / info / text
              size: 'small', // medium / small / mini
              onclick: (item) => {
                console.log(item)
                this.handleCreate()
              }
            },
            {
              text: '全部删除',
              type: 'danger',
              disabled: false, // 是否禁用
              onclick: (item) => {
                console.log(item)
                this.handleAllDelete(item)
              }
            }
            // {
            //   text: '导出',
            //   onclick: (item) => {
            //     console.log(item)
            //     this.handleExport()
            //   }
            // },
            // {
            //   text: '批量导出',
            //   onclick: (item) => {
            //     console.log(item)
            //     this.handleAllExport()
            //   }
            // }
          ]
        },
        // 表格
        loading: false,
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          // {
          //   width: 50,
          //   otherOptions: {
          //     type: 'selection',
          //     align: 'center'
          //   }
          // },
          {
            width: 60,
            label: '序号',
            otherOptions: {
              type: 'index',
              align: 'center'
            } // key
            // otherOptions: {
            //   width: 180, // 宽度
            //   fixed: 'left', // left, right
            //   align: 'center' //	left/center/right
            // }
          },
          {
            label: '设备类型',
            key: 'EqtType',
            otherOptions: {
              fixed: 'left'
            },
          },
          {
            label: '告警类型',
            key: 'AlarmType'
          },
          {
            label: '配置项',
            key: 'TriggerItem'
          },
          {
            label: '对比方式',
            key: 'ContrastModeStr'
          },
          {
            label: '阈值',
            key: 'LimitValue'
          }
        ],
        tableData: [],
        operateOptions: {
          width: 200
        },
        tableActions: [
          {
            actionLabel: '查看',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'view')
            }
          },
          {
            actionLabel: '编辑',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'edit')
            }
          },
          {
            actionLabel: '删除',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleDelete(index, row)
            }
          }
        ]
      }
    }
  },
  computed: {},
  async mounted() {
    this.customForm.formItems.find((v) => v.key === 'EquipmentTypeId').options = await this.getDictionaryDetailListByCode('HazchemEqtType', 'Id')
    this.customForm.formItems.find((v) => v.key === 'AlarmType').options = await this.getDictionaryDetailListByCode('HazchemAlarmType', 'Value')
    this.customForm.formItems.find((v) => v.key === 'EquipmentTypeId').options.unshift({ label: '全部', value: '' })
    this.customForm.formItems.find((v) => v.key === 'AlarmType').options.unshift({ label: '全部', value: '' })
    this.getEnviromentDTCList(GetHazchemDTCList, '')
    this.init()
    // this.initDeviceType('EqtType', 'HazchemEqtType')
  },
  methods: {
    searchForm(data) {
      console.log(data)
      this.customTableConfig.currentPage = 1
      this.onFresh()
    },
    resetForm() {
      this.customForm.formItems.find((item) => item.key === 'TriggerItem').otherOptions.disabled = !this.ruleForm.EquipmentTypeId
      this.getEnviromentDTCList(GetHazchemDTCList, '')
      this.onFresh()
    },
    onFresh() {
      this.GetQuotaList()
    },
    init() {
      this.GetQuotaList()
    },
    async GetQuotaList() {
      this.customTableConfig.loading = true
      const res = await GetQuotaList({
        ParameterJson: [
          {
            Key: '',
            Value: [null],
            Type: '',
            Filter_Type: ''
          }
        ],
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,

        SortName: '',
        SortOrder: '',
        Search: '',
        Content: '',
        ...this.ruleForm
      })
      this.customTableConfig.loading = false
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data.map((item) => ({
          ...item,
          Date: dayjs(item.Date).format('YYYY-MM-DD HH:mm:ss')
        }))

        this.customTableConfig.total = res.Data.TotalCount
        console.log(this.customTableConfig.total)
        this.customTableConfig.buttonConfig.buttonList.find(
          (item) => item.text == '全部删除'
        ).disabled = this.customTableConfig.total == 0
      } else {
        this.$message.error(res.Message)
      }
    },
    handleCreate() {
      this.dialogTitle = '新增'

      this.componentsConfig = {
        disabled: false,
        title: '新增'
      }
      this.dialogVisible = true
      this.currentComponent = DialogForm
    },
    handleDelete(index, row) {
      console.log(index, row)
      console.log(this)
      this.$confirm('该操作将删除当前配置，是否确认删除？', '删除', {
        type: 'error'
      })
        .then(async(_) => {
          const res = await DeleteQuota({
            IDs: [row.ID]
          })
          if (res.IsSucceed) {
            this.init()
          } else {
            this.$message.error(res.Message)
          }
        })
        .catch((_) => {})
    },
    handleAllDelete(index, row) {
      console.log(index, row)
      console.log(this)
      this.$confirm('该操作将删除全部配置，是否确认删除？', '删除', {
        type: 'error'
      })
        .then(async(_) => {
          const res = await DeleteAllQuota({
            // IDs: [row.ID]
          })
          if (res.IsSucceed) {
            this.init()
          } else {
            this.$message.error(res.Message)
          }
        })
        .catch((_) => {})
    },
    handleEdit(index, row, type) {
      console.log(index, row, type)
      this.dialogVisible = true
      if (type === 'view') {
        this.dialogTitle = '查看'
        this.currentComponent = DialogFormLook
        this.componentsConfig = {
          ID: row.ID,
          disabled: true,
          title: '查看'
        }
      } else if (type === 'edit') {
        this.dialogTitle = '编辑'
        this.currentComponent = DialogForm
        this.componentsConfig = {
          ID: row.ID,
          disabled: false,
          title: '编辑'
        }
      }
    },
    // async handleExport() {
    //   console.log(this.ruleForm)
    //   const res = await ExportHazchemEquipment({
    //     Content: '',
    //     EqtType: '',
    //     Position: '',
    //     IsAll: false,
    //     Ids: this.tableSelection.map((item) => item.ID),
    //     ...this.ruleForm
    //   })
    //   if (res.IsSucceed) {
    //     console.log(res)
    //     downloadFile(res.Data, '21')
    //   } else {
    //     this.$message.error(res.Message)
    //   }
    // },
    // async handleAllExport() {
    //   const res = await ExportHazchemEquipment({
    //     Content: '',
    //     EqtType: '',
    //     Position: '',
    //     IsAll: true,
    //     Ids: [],
    //     ...this.ruleForm
    //   })
    //   if (res.IsSucceed) {
    //     console.log(res)
    //     downloadFile(res.Data, '21')
    //   } else {
    //     this.$message.error(res.Message)
    //   }
    // },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.init()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.init()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
    }
  }
}
</script>

<style lang="scss" scoped>
.layout{
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
