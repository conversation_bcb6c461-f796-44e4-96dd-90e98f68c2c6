{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\purviewConfig\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\purviewConfig\\index.vue", "mtime": 1755674552409}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/accessControl/purviewConfig", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      width=\"30%\"\r\n      :visible.sync=\"dialogVisible\"\r\n      destroy-on-close\r\n    >\r\n      <el-form\r\n        :model=\"addForm\"\r\n        :rules=\"rules\"\r\n        ref=\"ruleForm\"\r\n        label-width=\"100px\"\r\n        class=\"demo-ruleForm\"\r\n      >\r\n        <el-form-item label=\"权限组名称\" prop=\"Name\">\r\n          <el-input v-model=\"addForm.Name\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n          <el-button @click=\"resetAddForm\">取消</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport getGridByCode from \"../../safetyManagement/mixins/index\";\r\nimport { GetList, DelAuthGroup, SaveAuthGroup, ExportData } from \"@/api/business/purviewConfig\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      dialogTitle: \"新增权限组\",\r\n      tableSelection: \"\",\r\n      ruleForm: {\r\n        Name: \"\",\r\n      },\r\n      addForm: {\r\n        Name: \"\",\r\n      },\r\n      rules: {\r\n        Name: [{ required: true, message: '请输入权限组名称', trigger: 'blur' }]\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Name\",\r\n            label: \"门禁组名称\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"新增\",\r\n              type: \"primary\",\r\n              size: \"small\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '门禁组',\r\n            key: 'P_Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备数量',\r\n            width: '50px',\r\n            key: 'num',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '可通行人员',\r\n            width: '50px',\r\n            key: 'P_Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '同步状态',\r\n            key: 'SyncRemark',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: \"240px\",\r\n          align: \"center\",\r\n        },\r\n        tableActionsWidth: 160,\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleWatch(row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDele(row.Id)\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"导出\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              console.log(index, row);\r\n              this.handleExport(row.Id);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/watchDevice\",\r\n          hidden: true,\r\n          component: () => import(\"./watchDevice.vue\"),\r\n          meta: { title: `门禁权限配置详情` },\r\n          name: \"purviewConfigWatchDevice\",\r\n        },\r\n        {\r\n          path: this.$route.path + \"/editDevice\",\r\n          hidden: true,\r\n          component: () => import(\"./editDevice.vue\"),\r\n          meta: { title: `门禁权限配置编辑` },\r\n          name: \"purviewConfigEditDevice\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  async created() {\r\n    this.init();\r\n  },\r\n  watch: {\r\n    'customTableConfig.tableData': {\r\n      handler(newValue, oldValue) {\r\n        this.customTableConfig.tableColumns = [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '门禁组',\r\n            key: 'Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备数量',\r\n\r\n            key: 'EquipCount',\r\n            otherOptions: {\r\n              align: 'center',\r\n              width:'250px',\r\n            }\r\n          },\r\n          {\r\n            label: '可通行人员',\r\n\r\n            key: 'UserCount',\r\n            otherOptions: {\r\n              align: 'center',\r\n              width:'250px',\r\n            }\r\n          },\r\n          {\r\n            label: '同步状态',\r\n            key: 'SyncRemark',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n        ]\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mixins: [getGridByCode, addRouterPage],\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData();\r\n    },\r\n    resetForm() {\r\n      this.fetchData();\r\n    },\r\n    init() {\r\n      this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      const res = await GetList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.Total;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    async handleCreate() {\r\n      this.ruleForm.Name = ''\r\n      this.dialogVisible = true\r\n    },\r\n    submitForm() {\r\n      this.$refs.ruleForm.validate((valid) => {\r\n        if (valid) {\r\n          SaveAuthGroup(this.addForm).then(res => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success('新增成功')\r\n              this.fetchData()\r\n              this.resetAddForm()\r\n            } else {\r\n              this.$message.error(res.Message)\r\n            }\r\n          })\r\n        } else {\r\n          console.log('error submit!!');\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    resetAddForm() {\r\n      this.$refs.ruleForm.resetFields();\r\n      this.dialogVisible = false\r\n    },\r\n    async handleWatch(row) {\r\n      this.$router.push({\r\n        name: \"purviewConfigWatchDevice\",\r\n        query: { pg_redirect: this.$route.name, Id: row.Id },\r\n      });\r\n    },\r\n    async handleEdit(row) {\r\n      this.$router.push({\r\n        name: \"purviewConfigEditDevice\",\r\n        query: { pg_redirect: this.$route.name, Id: row.Id },\r\n      });\r\n    },\r\n    async handleExport(Id) {\r\n      const res = await ExportData({ Id });\r\n      const url = window.URL.createObjectURL(\r\n        new Blob([res], {\r\n          type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\r\n        })\r\n      );\r\n      const link = document.createElement(\"a\");\r\n      link.style.display = \"none\";\r\n      link.href = url;\r\n      link.setAttribute(\"download\", \"门禁权限配置.xlsx\");\r\n      document.body.appendChild(link);\r\n      link.click();\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.fetchData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.fetchData();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleDele(Id) {\r\n      this.$confirm('是否确定删除该数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DelAuthGroup({ Id }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            });\r\n            this.fetchData()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n\r\n      }).catch(() => {\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}