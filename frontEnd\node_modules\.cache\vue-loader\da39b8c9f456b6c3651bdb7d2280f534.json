{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\maintenanceAndUpkeep\\workOrderManagement\\components\\immediate.vue?vue&type=style&index=0&id=753060fd&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\maintenanceAndUpkeep\\workOrderManagement\\components\\immediate.vue", "mtime": 1755506574384}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": ***********23}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQGltcG9ydCAiQC92aWV3cy9idXNpbmVzcy92ZWhpY2xlQmFycmllci9pbmRleC5zY3NzIjsKLnRvb2xib3ggewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogNXB4OwogIDo6di1kZWVwIC5lbC1mb3JtLWl0ZW0gewogICAgbWFyZ2luLWJvdHRvbTogMHB4OwogIH0KfQoudHlwZWxpbmUgewogIDo6di1kZWVwIC5lbC1yYWRpby1idXR0b25fX2lubmVyIHsKICAgIGJvcmRlci1yYWRpdXM6IDJweDsKICB9CiAgOjp2LWRlZXAgLmlzLWFjdGl2ZSB7CiAgICAuZWwtcmFkaW8tYnV0dG9uX19pbm5lciB7CiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7CiAgICAgIGNvbG9yOiAjMjk4ZGZmOwogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["immediate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgtBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "immediate.vue", "sourceRoot": "src/views/business/maintenanceAndUpkeep/workOrderManagement/components", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <div class=\"toolbox\">\n          <!-- <div>\n              <el-button @click=\"openDialog('add')\" type=\"primary\">�?�?/el-button>\n            </div> -->\n          <div>\n            <el-form inline>\n              <el-form-item label=\"工单�?\" style=\"margin-bottom: 10px\">\n                <el-input\n                  v-model=\"query.Order_Code\"\n                  clearable\n                  style=\"width: 150px\"\n                />\n              </el-form-item>\n              <el-form-item label=\"工单名称:\" style=\"margin-bottom: 10px\">\n                <el-input\n                  v-model=\"query.Order_Name\"\n                  clearable\n                  style=\"width: 150px\"\n                />\n              </el-form-item>\n              <el-form-item label=\"发起时间:\" style=\"margin-bottom: 10px\">\n                <el-date-picker\n                  v-model=\"query.Date\"\n                  align=\"right\"\n                  type=\"daterange\"\n                  placeholder=\"选择日期\"\n                  style=\"width: 300px\"\n                  value-format=\"yyyy-MM-dd\"\n                  :picker-options=\"pickerOptions\"\n                  @change=\"changeDate\"\n                />\n              </el-form-item>\n              <el-form-item label=\"工单状�?\" style=\"margin-bottom: 10px\">\n                <el-select\n                  v-model=\"query.State\"\n                  clearable\n                  filterable\n                  style=\"width: 120px\"\n                >\n                  <el-option\n                    v-for=\"item in stateList\"\n                    :key=\"item.code\"\n                    :label=\"item.name\"\n                    :value=\"item.code\"\n                  />\n                </el-select>\n              </el-form-item>\n              <!-- <el-form-item label=\"工单类型:\" style=\"margin-bottom: 10px\">\n                <el-select\n                  v-model=\"query.WorkOrder_Setup_Id\"\n                  clearable\n                  filterable\n                  style=\"width: 120px\"\n                >\n                  <el-option\n                    v-for=\"item in workTypeList\"\n                    :key=\"item.Value\"\n                    :label=\"item.Display_Name\"\n                    :value=\"item.Value\"\n                  />\n                </el-select>\n              </el-form-item> -->\n              <el-form-item label=\"维修�?\" style=\"margin-bottom: 10px\">\n                <el-select\n                  v-model=\"query.Maintain_Person\"\n                  clearable\n                  filterable\n                  style=\"width: 150px\"\n                >\n                  <el-option\n                    v-for=\"item in personList\"\n                    :key=\"item.Id\"\n                    :label=\"item.Name\"\n                    :value=\"item.Id\"\n                  />\n                </el-select>\n              </el-form-item>\n              <el-form-item label=\"设备查询:\" style=\"margin-bottom: 10px\">\n                <el-select\n                  v-model=\"query.EquipId\"\n                  filterable\n                  clearable\n                  placeholder=\"请输入设�?\n                >\n                  <el-option\n                    v-for=\"item in equipOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  />\n                </el-select>\n              </el-form-item>\n              <el-button @click=\"reset()\">�?�?/el-button>\n              <el-button type=\"primary\" @click=\"searchForm()\">�?�?/el-button>\n            </el-form>\n          </div>\n        </div>\n      </template>\n      <template v-slot:layoutTable>\n        <div class=\"toolbox\">\n          <div>\n            <el-radio-group\n              v-model=\"query.WorkOrder_State\"\n              class=\"typeline\"\n              @input=\"searchForm\"\n            >\n              <el-radio-button :label=\"null\">全部</el-radio-button>\n              <el-radio-button :label=\"0\">待处�?/el-radio-button>\n              <el-radio-button :label=\"1\">处理�?/el-radio-button>\n              <el-radio-button :label=\"2\">已处�?/el-radio-button>\n            </el-radio-group>\n          </div>\n          <div>\n            <el-button\n              type=\"primary\"\n              @click=\"openDialog('add')\"\n            >�?�?/el-button>\n          </div>\n        </div>\n        <CustomTable\n          style=\"height: calc(100vh - 350px)\"\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        >\n          <template #customBtn=\"{ slotScope }\">\n            <template v-if=\"slotScope.State === '0' && slotScope.Is_Anth\">\n              <el-button\n                v-if=\"getBtnAuth('dispatch')\"\n                type=\"text\"\n                @click=\"openDialog('dispatch', slotScope, slotScope.Order_Type)\"\n              >派工</el-button>\n              <el-button\n                v-if=\"getBtnAuth('dispatch-myorder')\"\n                type=\"text\"\n                code=\"dispatch-myorder\"\n                @click=\"openDialog('dispatch', slotScope, slotScope.Order_Type)\"\n              >派工</el-button>\n              <el-button\n                v-if=\"getBtnAuth('receive')\"\n                type=\"text\"\n                @click=\"receivingOrders(slotScope)\"\n              >接单</el-button>\n              <el-button\n                v-if=\"getBtnAuth('receive-myorder')\"\n                type=\"text\"\n                code=\"receive-myorder\"\n                @click=\"receivingOrders(slotScope)\"\n              >接单</el-button>\n            </template>\n            <el-button\n              v-if=\"getBtnAuth('detail')\"\n              type=\"text\"\n              @click=\"openDialog('detail', slotScope, slotScope.Order_Type)\"\n            >查看详情</el-button>\n            <el-button\n              v-if=\"getBtnAuth('detail-myorder')\"\n              type=\"text\"\n              @click=\"openDialog('detail', slotScope, slotScope.Order_Type)\"\n            >查看详情</el-button>\n            <template\n              v-if=\"\n                slotScope.State === '1' &&\n                  slotScope.Maintain_Person_Id === userId &&\n                  slotScope.Order_Type === 'jsbx'\n              \"\n            >\n              <el-button\n                v-if=\"getBtnAuth('handle')\"\n                type=\"text\"\n                @click=\"openDialog('handle', slotScope, slotScope.Order_Type)\"\n              >工单处理</el-button>\n              <el-button\n                v-if=\"getBtnAuth('handle-myorder')\"\n                type=\"text\"\n                @click=\"openDialog('handle', slotScope, slotScope.Order_Type)\"\n              >工单处理</el-button>\n            </template>\n            <template\n              v-if=\"\n                slotScope.State === '2' &&\n                  slotScope.Is_Anth &&\n                  slotScope.Order_Type === 'jsbx'\n              \"\n            >\n              <el-button\n                v-if=\"getBtnAuth('recheck')\"\n                type=\"text\"\n                @click=\"openDialog('recheck', slotScope, slotScope.Order_Type)\"\n              >工单复检</el-button>\n              <el-button\n                v-if=\"getBtnAuth('recheck-myorder')\"\n                type=\"text\"\n                @click=\"openDialog('recheck', slotScope, slotScope.Order_Type)\"\n              >工单复检</el-button>\n            </template>\n            <template\n              v-if=\"\n                slotScope.State === '3' &&\n                  slotScope.Order_Type === 'jsbx' &&\n                  slotScope.Create_UserId === userId\n              \"\n            >\n              <el-button\n                v-if=\"getBtnAuth('rate')\"\n                type=\"text\"\n                @click=\"openCloseRate(slotScope, 'rate')\"\n              >工单评价</el-button>\n              <el-button\n                v-if=\"getBtnAuth('rate-myorder')\"\n                type=\"text\"\n                @click=\"openCloseRate(slotScope, 'rate')\"\n              >工单评价</el-button>\n            </template>\n            <template v-if=\"slotScope.State === '0' || slotScope.State === '1'\">\n              <el-button\n                v-if=\"getBtnAuth('close')\"\n                type=\"text\"\n                @click=\"openCloseRate(slotScope, 'close')\"\n              >关闭</el-button>\n              <el-button\n                v-if=\"getBtnAuth('close-myorder')\"\n                type=\"text\"\n                @click=\"openCloseRate(slotScope, 'close')\"\n              >关闭</el-button>\n            </template>\n          </template>\n        </CustomTable>\n      </template>\n    </CustomLayout>\n    <editDialog ref=\"editDialog\" @refresh=\"fetchData\" />\n    <closeRateDialog ref=\"closeRateDialog\" @refresh=\"fetchData\" />\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\n// import AuthButtons from \"@/mixins/auth-buttons\";\nimport editDialog from '../editDialog.vue'\nimport closeRateDialog from '../closeRateDialog.vue'\nimport {\n  GetWorkOrderManageList,\n  GetWorkOrderType,\n  GetPersonList,\n  DeleteCoatingRequir,\n  SendWorkOrderPerson,\n  GetEquipDropList\n} from '@/api/business/maintenanceAndUpkeep.js'\n\nexport default {\n  Name: '',\n  components: {\n    CustomTable,\n    CustomLayout,\n    editDialog,\n    closeRateDialog\n  },\n  props: {\n    flag: {\n      type: Boolean,\n      default: false\n    },\n    personList: {\n      type: Array,\n      default: () => []\n    },\n    equipOptions: {\n      type: Array,\n      default: () => []\n    },\n    authButtons: {\n      type: Object,\n      default: () => {}\n    }\n  },\n  data() {\n    return {\n      userId: '',\n      query: {\n        Date: [],\n        Order_Code: '',\n        Order_Name: '',\n        Create_Date: '',\n        Create_EDate: '',\n        State: '',\n        WorkOrder_Setup_Id: 'jsbx',\n        Maintain_Person: '',\n        WorkOrder_State: null,\n        Type: 1\n      },\n      type: '',\n      pickerOptions: {\n        shortcuts: [\n          {\n            text: '今天',\n            onClick(picker) {\n              picker.$emit('pick', [new Date(), new Date()])\n            }\n          },\n          {\n            text: '�?�?,\n            onClick(picker) {\n              const end = new Date()\n              const start = new Date()\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\n              picker.$emit('pick', [start, end])\n            }\n          },\n          {\n            text: '�?0�?,\n            onClick(picker) {\n              const end = new Date()\n              const start = new Date()\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\n              picker.$emit('pick', [start, end])\n            }\n          },\n          {\n            text: '本月',\n            onClick(picker) {\n              const today = new Date()\n              const end = new Date(\n                today.getFullYear(),\n                today.getMonth() + 1,\n                0\n              )\n              const start = new Date(today.getFullYear(), today.getMonth(), 1)\n              picker.$emit('pick', [start, end])\n            }\n          }\n        ]\n      },\n      stateList: [\n        {\n          name: '待处�?,\n          code: 0\n        },\n        {\n          name: '处理�?,\n          code: 1\n        },\n        {\n          name: '待复检',\n          code: 2\n        },\n        {\n          name: '待评�?,\n          code: 3\n        },\n        {\n          name: '处理完成',\n          code: 4\n        },\n        {\n          name: '已关�?,\n          code: 5\n        }\n      ],\n      workTypeList: [\n        // {\n        //   Display_Name: \"已关�?,\n        //   Value: 5,\n        // },\n      ],\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true\n        },\n        close: () => {\n          this.dialogVisible = false\n          this.onFresh()\n        },\n        closeAndFresh: () => {\n          this.dialogVisible = false\n          this.onFresh()\n        }\n      },\n      dialogVisible: false,\n      dialogTitle: '',\n      tableSelection: [],\n      selectIds: [],\n      customTableConfig: {\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        height: '100vh',\n        tableColumns: [\n          {\n            width: 50,\n            label: '序号',\n            otherOptions: {\n              type: 'index',\n              align: 'center'\n            }\n          },\n          {\n            label: '发起时间',\n            key: 'Create_Date'\n          },\n          {\n            label: '工单名称',\n            key: 'Order_Name'\n          },\n          {\n            label: '工单类型',\n            key: 'Order_Type',\n            render: (row) => {\n              return this.$createElement(\n                'span',\n                {},\n                row.Order_Type === null\n                  ? '-'\n                  : row.Order_Type === 'jsbx'\n                    ? '即时报修'\n                    : '设备维保'\n              )\n            }\n          },\n          {\n            label: '工单�?,\n            key: 'Order_Code'\n          },\n          {\n            label: '开始处理时�?,\n            key: 'Start_Time'\n          },\n          {\n            label: '处理完成时间',\n            key: 'End_Time'\n          },\n          {\n            label: '处理用时',\n            key: 'Time'\n          },\n          {\n            label: '报修部门',\n            key: 'Depart_Name'\n          },\n          {\n            label: '报修�?,\n            key: 'Warranty_Person'\n          },\n          {\n            label: '维修�?,\n            key: 'Maintain_Person'\n          },\n          {\n            label: '工单状�?,\n            key: 'State',\n            render: (row) => {\n              return this.$createElement(\n                'span',\n                {\n                  style: {\n                    color:\n                      row.State === '0'\n                        ? '#FF5E7C'\n                        : row.State === '1'\n                          ? '#298DFF'\n                          : row.State === '2'\n                            ? '#FF902C'\n                            : row.State === '3'\n                              ? '#298DFF'\n                              : row.State === '4'\n                                ? '#00D3A7'\n                                : '#333333'\n                  }\n                },\n                row.State === '0'\n                  ? '待处�?\n                  : row.State === '1'\n                    ? '处理�?\n                    : row.State === '2'\n                      ? '待复检'\n                      : row.State === '3'\n                        ? '待评�?\n                        : row.State === '4'\n                          ? '处理完成'\n                          : '已关�?\n              )\n            }\n          }\n        ],\n        tableData: [],\n        tableActionsWidth: 220,\n        tableActions: [\n          {\n            actionLabel: '',\n            otherOptions: {\n              type: 'text'\n            }\n          }\n        ],\n        buttonConfig: {\n          buttonList: []\n        },\n        operateOptions: {\n          width: 300 // 操作栏宽�?        }\n      }\n    }\n  },\n  computed: {},\n  // mixins: [AuthButtons],\n  watch: {\n    // 'AuthButtons.buttons':{\n    //   handler(val,oldval){\n    //     console.log('dddss',val,oldval);\n    //       this.show=true\n    //     }\n\n    //   }\n    // }\n    flag: {\n      handler(val) {\n        console.log('aaaa', val)\n        this.initData()\n      }\n    }\n  },\n  created() {},\n  mounted() {\n    // 跳转设置默认参数\n    // const JumpParams = this.$qiankun.getMicroAppJumpParamsFn()\n    // console.log(JumpParams.Create_Date, '跳转参数-----------------------')\n    // if (JumpParams.isJump == 'true') {\n    //   this.query.State = Number(JumpParams.State)\n    //   // this.query.Create_Date = JumpParams.Create_Date;\n    //   // this.query.Create_EDate = JumpParams.Create_EDate;\n    //   // this.query.Date = [JumpParams.Create_Date, JumpParams.Create_EDate];\n    // }\n    this.initData()\n  },\n  beforeDestroy() {\n    this.$qiankun.setMicroAppJumpParamsFn()\n    this.query.State = null\n    // this.query.Create_Date = null;\n    // this.query.Create_EDate = null;\n    // this.query.Date = [];\n  },\n  methods: {\n    async initData() {\n      // let res = await GetWorkOrderType({ Code: \"WorkOrderType\" });\n      // if (res.IsSucceed) {\n      //   this.workTypeList = res.Data;\n      // }\n\n      this.userId = localStorage.getItem('UserId')\n      if (this.$route.query.type === 'my') {\n        this.query.type = 0\n      } else {\n        this.query.type = 1\n      }\n      await this.init()\n    },\n    openAdd() {\n      this.dialogTitle = '新增'\n      this.dialogVisible = true\n      this.$nextTick(() => {\n        this.$refs.dialogRef.init(0, {}, 'add')\n      })\n    },\n    searchForm() {\n      this.customTableConfig.currentPage = 1\n      this.onFresh()\n    },\n    reset() {\n      this.query = {\n        Date: [],\n        Order_Code: '',\n        Order_Name: '',\n        Create_Date: '',\n        Create_EDate: '',\n        State: '',\n        WorkOrder_Setup_Id: 'jsbx',\n        Maintain_Person: '',\n        WorkOrder_State: this.query.WorkOrder_State\n      }\n      if (this.$route.query.type === 'my') {\n        this.query.type = 0\n      } else {\n        this.query.type = 1\n      }\n      this.customTableConfig.currentPage = 1\n      this.onFresh()\n    },\n    resetForm() {\n      this.onFresh()\n    },\n    onFresh() {\n      this.fetchData()\n    },\n    init() {\n      this.fetchData()\n    },\n    getTypeList() {\n      console.log('res.Datares.Datares.Datares.Data-------------------')\n      // GetWorkOrderType({ Code: \"WorkOrderType\" }).then((res) => {\n      //   console.log(\n      //     res.Data,\n      //     \"res.Datares.Datares.Datares.Data-------------------\"\n      //   );\n      //   if (res.IsSucceed) {\n      //     this.typeList = res.Data;\n      //   }\n      // });\n    },\n    async fetchData() {\n      console.log(123)\n      const res = await GetWorkOrderManageList({\n        model: this.query,\n        pageInfo: {\n          Page: this.customTableConfig.currentPage,\n          PageSize: this.customTableConfig.pageSize,\n          SortName: 'Create_Date',\n          SortOrder: 'DESC'\n        }\n      })\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data\n        this.customTableConfig.total = res.Data.TotalCount\n      }\n    },\n    handleCreate() {\n      this.dialogTitle = '新增'\n      this.dialogVisible = true\n      this.$nextTick(() => {\n        this.$refs.dialogRef.init(0, {}, 'dispatch')\n      })\n    },\n    handleDelete(index, row) {\n      this.$confirm('请确认，是否删除该数�?', {\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteCoatingRequir({ Id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                message: '删除成功',\n                type: 'success'\n              })\n              this.init()\n            } else {\n              this.$message({\n                message: res.Message,\n                type: 'error'\n              })\n            }\n          })\n        })\n        .catch((_) => {\n          this.$message({\n            type: 'info',\n            message: '已取消删�?\n          })\n        })\n    },\n    // 打开新增编辑弹窗\n    async openDialog(type, row, orderType) {\n      const res = await GetWorkOrderType({ Code: 'WorkOrderType' })\n      if (res.IsSucceed) {\n        this.workTypeList = res.Data\n      }\n      this.$refs.editDialog.handleOpen(type, row, orderType, this.workTypeList)\n    },\n    // 打开关闭工单弹窗或评价弹�?    openCloseRate(row, type) {\n      this.$refs.closeRateDialog.handleOpen(type, row)\n    },\n    // 接单\n    receivingOrders(row) {\n      SendWorkOrderPerson({ Id: row.Id }).then((res) => {\n        if (res.IsSucceed) {\n          this.fetchData()\n          this.$message.success('接单成功')\n        } else {\n          this.$message.error(res.Message)\n        }\n      })\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.onFresh()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`)\n      this.customTableConfig.currentPage = val\n      this.onFresh()\n    },\n    handleSelectionChange(selection) {\n      const Ids = []\n      this.tableSelection = selection\n      this.tableSelection.forEach((item) => {\n        Ids.push(item.Id)\n      })\n      console.log(Ids)\n      this.selectIds = Ids\n      console.log(this.tableSelection)\n    },\n\n    changeDate() {\n      this.query.Create_Date = this.query.Date ? this.query.Date[0] : null\n      this.query.Create_EDate = this.query.Date ? this.query.Date[1] : null\n    },\n    getBtnAuth(code) {\n      // console.log(code,this.AuthButtons,this.AuthButtons.buttons.find(item=>item.Code===code));\n      return this.authButtons.buttons.find((item) => item.Code === code)\n    }\n  }\n}\n</script>\n\n  <style lang=\"scss\" scoped>\n@import \"@/views/business/vehicleBarrier/index.scss\";\n.toolbox {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 5px;\n  ::v-deep .el-form-item {\n    margin-bottom: 0px;\n  }\n}\n.typeline {\n  ::v-deep .el-radio-button__inner {\n    border-radius: 2px;\n  }\n  ::v-deep .is-active {\n    .el-radio-button__inner {\n      background-color: #ffffff;\n      color: #298dff;\n    }\n  }\n}\n</style>\n\n"]}]}