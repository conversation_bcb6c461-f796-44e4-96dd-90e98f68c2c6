<template>
  <div class="app-container abs100">
    <CustomLayout
      :layoutConfig="{
        isShowSearchForm: false,
      }"
    >
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";
import DialogForm from "./dialogForm.vue";

import {
  GetTaskConfigPageList,
  EnableMobileMessageNotice,
  EnableSiteNotice,
  EnableTaskConfig,
  GetNoticeDropDownOption,
} from "@/api/business/eventManagement";
export default {
  name: "",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
  },
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {
        Data: {},
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogVisible: false,
      dialogTitle: "访客管理审核",
      tableSelection: [],
      ruleForm: {},
      customForm: {
        formItems: [],
        rules: {},
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "启用",
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: true, // 是否禁用
              icon: "", //  图标
              autofocus: false, // 是否聚焦
              type: "primary", // primary / success / warning / danger / info / text
              size: "small", // medium / small / mini
              onclick: (item) => {
                console.log(item);
                this.handleEnable();
              },
            },
            {
              text: "关闭",
              type: "danger",
              disabled: true, // 是否禁用
              onclick: (item) => {
                console.log(item);
                this.handleClose();
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            otherOptions: {
              type: "selection",
              align: "center",
              fixed: "left",
            },
          },
          {
            label: "任务名称",
            key: "Name",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "任务层级",
            key: "SourceTypeDisplay",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "业务模块",
            key: "Module",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "任务类型",
            key: "TaskType",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "触发任务条件",
            key: "TriggerConditionDescription",
            width: 180,
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "任务时长",
            key: "Duration",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "单位",
            key: "DurationUnitDisplay",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "规则描述",
            key: "RuleDescription",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "站内通知",
            key: "EnableSiteNotice",
            otherOptions: {
              align: "center",
            },
            render: (row) => {
              return this.$createElement("el-switch", {
                props: {
                  value: row.EnableSiteNotice,
                  "active-color": "#13ce66",
                },
                on: {
                  change: (e) => {
                    this.handleSiteNoticeCloseEnable(row.Id, e);
                  },
                },
              });
            },
          },
          {
            label: "短信通知",
            key: "EnableMobileMessageNotice",
            otherOptions: {
              align: "center",
            },
            render: (row) => {
              return this.$createElement("el-switch", {
                props: {
                  value: row.EnableMobileMessageNotice,
                  "active-color": "#13ce66",
                },
                on: {
                  change: (e) => {
                    this.handleMobileMessageCloseEnable(row.Id, e);
                  },
                },
              });
            },
          },
          {
            label: "指派角色",
            key: "Roles",
            otherOptions: {
              align: "center",
            },
            render: (row) => {
              return this.$createElement(
                "span",
                {
                  style: {
                    color: "#3582fb",
                  },
                  on: {
                    click: () => {
                      this.handleEdit(row, "role");
                    },
                  },
                },
                row.Roles.join(",") || "添加"
              );
            },
          },
          {
            label: "指派人",
            key: "Users",
            otherOptions: {
              align: "center",
            },
            render: (row) => {
              return this.$createElement(
                "span",
                {
                  style: {
                    color: "#3582fb",
                  },
                  on: {
                    click: () => {
                      this.handleEdit(row, "user");
                    },
                  },
                },
                row.Users.join(",") || "添加"
              );
            },
          },
          {
            label: "执行周期",
            key: "InformCycleDisplay",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "上次执行时间",
            key: "LastInformTime",
            width: 180,
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "启用状态",
            key: "Enable",
            otherOptions: {
              align: "center",
            },
            render: (row) => {
              return this.$createElement("el-switch", {
                props: {
                  value: row.Enable,
                  "active-color": "#13ce66",
                },
                on: {
                  change: (e) => {
                    this.handleCloseEnable(row.Id, e);
                  },
                },
              });
            },
          },
        ],
        tableData: [],
        operateOptions: {
          align: "center",
        },
        tableActions: [],
      },
    };
  },
  watch: {
    tableSelection: {
      handler(newval, oldval) {
        console.log(newval);
        if (newval.length > 0) {
          this.customTableConfig.buttonConfig.buttonList.forEach((ele) => {
            ele.disabled = false;
          });
        } else {
          this.customTableConfig.buttonConfig.buttonList.forEach((ele) => {
            ele.disabled = true;
          });
        }
      },
    },
  },
  computed: {},
  created() {
    this.init();
  },
  // mixins: [getGridByCode],
  methods: {
    searchForm(data) {
      console.log(data);
      this.customTableConfig.currentPage = 1;
      this.onFresh();
    },
    resetForm() {
      this.onFresh();
    },
    onFresh() {
      this.GetTaskConfigPageList();
    },
    init() {
      // this.getGridByCode("AccessControlAlarmDetails1");
      this.GetTaskConfigPageList();
      this.getNoticeDropDownOption();
    },
    async getNoticeDropDownOption() {
      const res = await GetNoticeDropDownOption({});
      if (res.IsSucceed) {
        let result = res.Data || [];
        let noticeType = [];
        let noticeLevel = [];
        result.forEach((element) => {
          if (element.TypeName == "通知类型") {
            noticeType = element.Data.map((item) => ({
              value: item.Value,
              label: item.Name,
            }));
          } else if (element.TypeName == "发布层级") {
            noticeLevel = element.Data.map((item) => ({
              value: item.Value,
              label: item.Name,
            }));
          }
        });
        // this.customForm.formItems.find((item) => item.key == "Type").options =
        //   noticeType;
      }
    },
    async GetTaskConfigPageList() {
      const res = await GetTaskConfigPageList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data;
        this.customTableConfig.total = res.Data.TotalCount;
        if (this.customTableConfig.tableData.length > 0) {
        }
      } else {
        this.$message.error(res.Message);
      }
    },

    async handleMobileMessageCloseEnable(id, status) {
      const res = await EnableMobileMessageNotice({
        Ids: [id],
        Enable: status,
      });
      if (res.IsSucceed) {
        console.log(res);
        this.init();
      }
    },

    async handleSiteNoticeCloseEnable(id, status) {
      const res = await EnableSiteNotice({
        Ids: [id],
        Enable: status,
      });
      if (res.IsSucceed) {
        console.log(res);
        this.init();
      }
    },

    async handleCloseEnable(id, status) {
      const res = await EnableTaskConfig({
        Ids: [id],
        Enable: status,
      });
      if (res.IsSucceed) {
        console.log(res);
        this.init();
      }
    },
    handleEdit(row, type) {
      console.log(row, "row");
      this.dialogTitle = `访客管理审核（${row.Name}）`;
      this.dialogVisible = true;
      this.componentsConfig = {
        ID: row.Id,
        type,
        UserIds: row.UserIds,
        Users: row.Users,
        RoleIds: row.RoleIds,
        Roles: row.Roles,
      };
    },
    async handleEnable() {
      const res = await EnableTaskConfig({
        Ids: this.tableSelection.map((item) => item.Id),
        Enable: true,
      });
      if (res.IsSucceed) {
        console.log(res);
        this.init();
      }
    },

    async handleClose() {
      const res = await EnableTaskConfig({
        Ids: this.tableSelection.map((item) => item.Id),
        Enable: false,
      });
      if (res.IsSucceed) {
        console.log(res);
        this.init();
      }
    },

    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.GetTaskConfigPageList();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.GetTaskConfigPageList();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
  },
};
</script>

<style lang="scss" scoped>
.layout {
  height: 100%;
  width: 100%;
  position: absolute;
  ::v-deep {
    .CustomLayout {
      .layoutTable {
        height: 0;
        .CustomTable {
          height: 100%;
          display: flex;
          flex-direction: column;
          .table {
            flex: 1;
            height: 0;
            display: flex;
            flex-direction: column;
            .el-table {
              flex: 1;
              height: 0;
            }
          }
        }
      }
    }
  }
}
</style>
