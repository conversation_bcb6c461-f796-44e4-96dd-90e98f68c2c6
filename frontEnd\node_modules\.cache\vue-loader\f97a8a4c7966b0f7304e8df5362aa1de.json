{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\entryExitList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\entryExitList\\index.vue", "mtime": 1755506574509}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/vehicleBarrier/entryExitList", "sourcesContent": ["<!-- 出入口管�?-->\r\n<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n          ><template #customBtn=\"{ slotScope }\"\r\n            ><el-button\r\n              type=\"text\"\r\n              :disabled=\"true\"\r\n              @click=\"handelStart(slotScope)\"\r\n              >{{ slotScope.Status == 1 ? \"停用\" : \"启用\" }}</el-button\r\n            ></template\r\n          ></CustomTable\r\n        >\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"600px\"\r\n      @closed=\"closedDialog\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"dialogRef\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\n\r\nimport baseInfo from \"./dialog/baseInfo.vue\";\r\nimport associatedDevices from \"./dialog/associatedDevices\";\r\nimport importDialog from \"@/views/business/vehicleBarrier/components/import.vue\";\r\n\r\nimport exportInfo from \"@/views/business/vehicleBarrier/mixins/export.js\";\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\n\r\nimport {\r\n  GetGateWayPageList,\r\n  DelGateway,\r\n  SetGatewayStatus,\r\n  ExportParkingGateway,\r\n  ImportParkingGateway,\r\n} from \"@/api/business/vehicleBarrier\";\r\nexport default {\r\n  GatewayName: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n    associatedDevices,\r\n    baseInfo,\r\n    importDialog,\r\n  },\r\n  mixins: [exportInfo],\r\n  data() {\r\n    return {\r\n      currentComponent: baseInfo,\r\n      componentsConfig: {\r\n        interfaceName: ImportParkingGateway,\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"\",\r\n      tableSelection: [],\r\n      selectIds: [],\r\n      ruleForm: {\r\n        GatewayName: \"\",\r\n        ParkingName: \"\",\r\n        GatewayType: null,\r\n        GatewayStatus: null,\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"GatewayName\", // 字段ID\r\n            label: \"出入口名�?, // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            placeholder: \"请输入输入停车场名称\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            width: \"240px\",\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"GatewayType\",\r\n            label: \"出入口类�?,\r\n            type: \"select\",\r\n            placeholder: \"请输入出入口类型\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            options: [\r\n              {\r\n                label: \"出入�?,\r\n                value: 1,\r\n              },\r\n              {\r\n                label: \"入口\",\r\n                value: 2,\r\n              },\r\n              {\r\n                label: \"出口\",\r\n                value: 3,\r\n              },\r\n            ],\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"ParkingName\", // 字段ID\r\n            label: \"停车场名�?, // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            placeholder: \"请输入输入停车场名称\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            width: \"240px\",\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"GatewayStatus\", // 字段ID\r\n            label: \"状�?, // Form的label\r\n            type: \"select\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            placeholder: \"请选择状�?,\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            options: [\r\n              {\r\n                label: \"启用\",\r\n                value: 1,\r\n              },\r\n              {\r\n                label: \"停用\",\r\n                value: 0,\r\n              },\r\n            ],\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"新增\",\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载�?\n              disabled: true, // 是否禁用\r\n              icon: \"\", //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n            {\r\n              text: \"下载模板\",\r\n              disabled: true, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData([], \"出入口列表模�?, ExportParkingGateway);\r\n              },\r\n            },\r\n            {\r\n              text: \"批量导入\",\r\n              disabled: true, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.currentComponent = \"importDialog\";\r\n                this.dialogVisible = true;\r\n                this.dialogTitle = \"批量导入\";\r\n              },\r\n            },\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData(\r\n                  {\r\n                    ...this.ruleForm,\r\n                    Ids: this.selectIds.toString(),\r\n                  },\r\n                  \"出入口列�?,\r\n                  ExportParkingGateway\r\n                );\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: \"100%\",\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: \"selection\",\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"出入口名�?,\r\n            key: \"Name\",\r\n          },\r\n          {\r\n            label: \"出入口编�?,\r\n            key: \"Code\",\r\n          },\r\n          {\r\n            label: \"出入口类�?,\r\n            key: \"TypeName\",\r\n          },\r\n          {\r\n            label: \"出入口位�?,\r\n            key: \"PositionName\",\r\n          },\r\n          {\r\n            label: \"停车场名�?,\r\n            key: \"ParkingName\",\r\n          },\r\n          {\r\n            label: \"停车场编�?,\r\n            key: \"ParkingCode\",\r\n          },\r\n          {\r\n            label: \"状�?,\r\n            key: \"StatusName\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActionsWidth: 300,\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: true, // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, \"edit\");\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: true, // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"查看详情\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, \"view\");\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"关联道闸设备\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: true, // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.configRules(index, row, \"view\");\r\n            },\r\n          },\r\n          // {\r\n          //   actionLabel: '启动',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n\r\n          //   }\r\n          // }\r\n        ],\r\n        operateOptions: {\r\n          width: 280, // 操作栏宽�?\n        },\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1;\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n    },\r\n    init() {\r\n      this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      const res = await GetGateWayPageList({\r\n        ParameterJson: [\r\n          {\r\n            Key: \"\",\r\n            Value: [null],\r\n            Type: \"\",\r\n            Filter_Type: \"\",\r\n          },\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((v) => {\r\n          if (v.Position) {\r\n            const {\r\n              ParkPosition,\r\n              Position1,\r\n              Position2,\r\n              Position3,\r\n              DetailPostition,\r\n            } = v.Position;\r\n            const arr = [\r\n              ParkPosition,\r\n              Position1,\r\n              Position2,\r\n              Position3,\r\n              DetailPostition,\r\n            ];\r\n            v.PositionName = arr.filter((item) => !!item).join(\"-\");\r\n          } else {\r\n            v.PositionName = null;\r\n          }\r\n\r\n          v.TypeName =\r\n            v.Type == 1\r\n              ? \"出入�?\r\n              : v.Type == 2\r\n              ? \"入口\"\r\n              : v.Type == 3\r\n              ? \"出口\"\r\n              : \"\";\r\n\r\n          v.StatusName = v.Status == 0 ? \"停用\" : \"启用\";\r\n          return v;\r\n        });\r\n        console.log(res);\r\n        this.customTableConfig.total = res.Data.Total;\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.currentComponent = \"baseInfo\";\r\n      this.dialogTitle = \"新增\";\r\n      this.dialogVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.init(0, {}, \"add\");\r\n      });\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row);\r\n      console.log(this);\r\n      this.$confirm(\"确认删除?\", {\r\n        type: \"warning\",\r\n      })\r\n        .then(async (_) => {\r\n          const res = await DelGateway({\r\n            Id: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: \"删除成功\",\r\n              type: \"success\",\r\n            });\r\n            this.init();\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: \"error\",\r\n            });\r\n          }\r\n        })\r\n        .catch((_) => {\r\n          this.$message({\r\n            type: \"info\",\r\n            message: \"已取消删�?,\r\n          });\r\n        });\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type);\r\n      this.currentComponent = \"baseInfo\";\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.init(index, row, type);\r\n      });\r\n      if (type === \"view\") {\r\n        this.dialogTitle = \"查看\";\r\n      } else if (type === \"edit\") {\r\n        this.dialogTitle = \"编辑\";\r\n      }\r\n      this.dialogVisible = true;\r\n    },\r\n    configRules(index, row, type) {\r\n      this.dialogTitle = \"关联道闸设备\";\r\n      this.currentComponent = \"associatedDevices\";\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.init(row);\r\n      });\r\n      this.dialogVisible = true;\r\n    },\r\n\r\n    // 启动 停用\r\n    handelStart(slotScope) {\r\n      console.log(slotScope);\r\n      SetGatewayStatus({ Id: slotScope.Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: `${slotScope.Status == 1 ? \"停用\" : \"启用\"}成功`,\r\n            type: \"success\",\r\n          });\r\n          this.onFresh();\r\n        }\r\n      });\r\n    },\r\n    // 关闭弹窗\r\n    closedDialog() {\r\n      this.$refs.dialogRef.closeClearForm();\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.onFresh();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.onFresh();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      const Ids = [];\r\n      this.tableSelection = selection;\r\n      this.tableSelection.forEach((item) => {\r\n        Ids.push(item.Id);\r\n      });\r\n      console.log(Ids);\r\n      this.selectIds = Ids;\r\n      console.log(this.tableSelection);\r\n      // if (this.tableSelection.length > 0) {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = false\r\n      // } else {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = true\r\n      // }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n"]}]}