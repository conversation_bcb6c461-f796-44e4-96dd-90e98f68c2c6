{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\equipmentManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\equipmentManagement\\index.vue", "mtime": 1755674552435}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/vehicleBarrier/equipmentManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          label-width=\"130px\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"600px\"\r\n      @closed=\"closedDialog\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"dialogRef\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\n\r\nimport baseInfo from \"./dialog/baseInfo.vue\";\r\nimport importDialog from \"@/views/business/vehicleBarrier/components/import.vue\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/mixins/export.js\";\r\n\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\nimport {\r\n  GetBarrierPageList,\r\n  DelEquipment,\r\n  ExportBarrierEquipment,\r\n  ImportBarrierEquipment,\r\n} from \"@/api/business/vehicleBarrier.js\";\r\nexport default {\r\n  Name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n    baseInfo,\r\n    importDialog,\r\n  },\r\n  mixins: [exportInfo],\r\n  data() {\r\n    return {\r\n      currentComponent: baseInfo,\r\n      componentsConfig: {\r\n        interfaceName: ImportBarrierEquipment,\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"\",\r\n      tableSelection: [],\r\n      selectIds: [],\r\n      ruleForm: {\r\n        Name: \"\",\r\n        Code: \"\",\r\n        Brand: \"\",\r\n        EquipmentModel: \"\",\r\n        Factory: \"\",\r\n        Vender: \"\",\r\n        Engineer: \"\",\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Name\", // 字段ID\r\n            label: \"设备名称\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            placeholder: \"请输入输入停车场名称\",\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n          },\r\n          {\r\n            key: \"Brand\",\r\n            label: \"设备品牌\",\r\n            type: \"input\",\r\n            placeholder: \"请输入停车场地址\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n          },\r\n          {\r\n            key: \"EquipmentModel\",\r\n            label: \"规格型号\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n          },\r\n          {\r\n            key: \"Factory\",\r\n            label: \"厂家\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n          },\r\n          {\r\n            key: \"Code\",\r\n            label: \"智能道闸设备编码\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n          },\r\n          {\r\n            key: \"Engineer\",\r\n            label: \"维修工程师\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n          },\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"新增\",\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: true, // 是否禁用\r\n              icon: \"\", //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n            {\r\n              text: \"下载模板\",\r\n              disabled: true, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData(\r\n                  [],\r\n                  \"智能道闸设备管理模板\",\r\n                  ExportBarrierEquipment\r\n                );\r\n              },\r\n            },\r\n            {\r\n              text: \"批量导入\",\r\n              disabled: true, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.currentComponent = \"importDialog\";\r\n                this.dialogVisible = true;\r\n                this.dialogTitle = \"批量导入\";\r\n              },\r\n            },\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData(\r\n                  {\r\n                    ...this.ruleForm,\r\n                    Ids: this.selectIds.toString(),\r\n                  },\r\n                  \"智能道闸设备管理\",\r\n                  ExportBarrierEquipment\r\n                );\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: \"100%\",\r\n        tableActionsWidth: 220,\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: \"selection\",\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"智能道闸设备编码\",\r\n            key: \"Code\",\r\n          },\r\n          {\r\n            label: \"智能道闸设备名称\",\r\n            key: \"Name\",\r\n          },\r\n          {\r\n            label: \"品牌\",\r\n            key: \"Brand\",\r\n          },\r\n          {\r\n            label: \"规格型号\",\r\n            key: \"EquipmentModel\",\r\n          },\r\n          {\r\n            label: \"厂家\",\r\n            key: \"Factory\",\r\n          },\r\n          {\r\n            label: \"厂家联系方式\",\r\n            key: \"FactoryPhone\",\r\n          },\r\n          {\r\n            label: \"供应商名称\",\r\n            key: \"Vender\",\r\n          },\r\n          {\r\n            label: \"供应商联系方式\",\r\n            key: \"VenderPhone\",\r\n          },\r\n          {\r\n            label: \"维修工程师\",\r\n            key: \"Engineer\",\r\n          },\r\n          {\r\n            label: \"维修工程师联系方式\",\r\n            key: \"EngineerPhone\",\r\n          },\r\n          {\r\n            label: \"设备状态\",\r\n            key: \"StatusName\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: true, // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, \"edit\");\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: true, // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"查看详情\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, \"view\");\r\n            },\r\n          },\r\n        ],\r\n        operateOptions: {\r\n          // width: 300 // 操作栏宽度\r\n        },\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n    },\r\n    init() {\r\n      this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      const res = await GetBarrierPageList({\r\n        ParameterJson: [\r\n          {\r\n            Key: \"\",\r\n            Value: [null],\r\n            Type: \"\",\r\n            Filter_Type: \"\",\r\n          },\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((v) => {\r\n          v.StatusName = v.Status == 1 ? \"启用\" : \"停用\";\r\n          return v;\r\n        });\r\n        console.log(res);\r\n        this.customTableConfig.total = res.Data.Total;\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.currentComponent = \"baseInfo\";\r\n      this.dialogTitle = \"新增\";\r\n      this.dialogVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.add();\r\n      });\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row);\r\n      console.log(this);\r\n      this.$confirm(\"确认删除?\", {\r\n        type: \"warning\",\r\n      })\r\n        .then(async (_) => {\r\n          const res = await DelEquipment({\r\n            Id: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: \"删除成功\",\r\n              type: \"success\",\r\n            });\r\n            this.init();\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: \"error\",\r\n            });\r\n          }\r\n        })\r\n        .catch((_) => {\r\n          this.$message({\r\n            type: \"info\",\r\n            message: \"已取消删除\",\r\n          });\r\n        });\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type);\r\n      this.currentComponent = \"baseInfo\";\r\n      if (type === \"view\") {\r\n        this.dialogTitle = \"查看\";\r\n      } else if (type === \"edit\") {\r\n        this.dialogTitle = \"编辑\";\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.init(index, row, type);\r\n      });\r\n\r\n      this.dialogVisible = true;\r\n    },\r\n    // 关闭弹窗\r\n    closedDialog() {\r\n      this.$refs.dialogRef.closeClearForm();\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.onFresh();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.onFresh();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      const Ids = [];\r\n      this.tableSelection = selection;\r\n      this.tableSelection.forEach((item) => {\r\n        Ids.push(item.Id);\r\n      });\r\n      console.log(Ids);\r\n      this.selectIds = Ids;\r\n      console.log(this.tableSelection);\r\n      // if (this.tableSelection.length > 0) {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find(\r\n      //     (v) => v.key == \"batch\"\r\n      //   ).disabled = false;\r\n      // } else {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find(\r\n      //     (v) => v.key == \"batch\"\r\n      //   ).disabled = true;\r\n      // }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n::v-deep {\r\n  .el-dialog__body {\r\n    padding: 0px 20px 30px;\r\n  }\r\n}\r\n</style>\r\n"]}]}