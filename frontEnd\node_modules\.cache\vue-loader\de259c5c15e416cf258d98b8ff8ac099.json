{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\trafficRecords\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\trafficRecords\\index.vue", "mtime": 1755506574534}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/vehicleBarrier/pJVehicleBarrier/trafficRecords", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"查看\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"600px\"\r\n    >\r\n      <el-image v-if=\"PassImg\" :src=\"PassImg\" class=\"imgwapper\" />\r\n      <div v-else class=\"empty-img\">暂无图片</div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport {\r\n  VBPassRecordGetDropList,\r\n  VBPassRecordGetPassRecordList,\r\n  VBPassRecordGetPassRecordDetail,\r\n  VBPassRecordExportData,\r\n} from \"@/api/business/vehicleBarrier.js\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/mixins/export.js\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nimport dayjs from \"dayjs\";\r\nexport default {\r\n  Name: \"vehiclePeerRecord\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  mixins: [exportInfo, addRouterPage],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        Number: \"\",\r\n        StartTime: null,\r\n        EndTime: null,\r\n        AccessType: \"\",\r\n        UserName: \"\",\r\n        PassType: \"\",\r\n        PassMode: \"\",\r\n        Date: [],\r\n      },\r\n      dialogVisible: false,\r\n      PassImg: \"\", // 图片\r\n      vehicleTypeOption: [], // 车辆类型\r\n      tableSelection: [],\r\n      selectIds: [],\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Number\", // 字段ID\r\n            label: \"车牌号码\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"Date\",\r\n            label: \"通行时间\",\r\n            type: \"datePicker\",\r\n            otherOptions: {\r\n              type: \"datetimerange\",\r\n              rangeSeparator: \"�?,\r\n              startPlaceholder: \"开始日�?,\r\n              endPlaceholder: \"结束日期\",\r\n              clearable: true,\r\n              valueFormat: \"yyyy-MM-dd HH:mm\",\r\n            },\r\n            change: (e) => {\r\n              // this.ruleForm.StartTime = e[0];\r\n              // this.ruleForm.EndTime = e[1];\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.StartTime = e[0];\r\n                this.ruleForm.EndTime = e[1];\r\n              } else {\r\n                this.ruleForm.StartTime = null;\r\n                this.ruleForm.EndTime = null;\r\n              }\r\n            },\r\n          },\r\n          {\r\n            key: \"AccessType\",\r\n            label: \"访问类型\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"UserName\",\r\n            label: \"车主姓名\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"PassType\",\r\n            label: \"过车方向\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"PassMode\",\r\n            label: \"通行方式\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n        ],\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData(\r\n                  this.ruleForm,\r\n                  \"车辆通行记录\",\r\n                  VBPassRecordExportData\r\n                );\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: \"100%\",\r\n        tableColumns: [\r\n          // {\r\n          //   width: 50,\r\n          //   otherOptions: {\r\n          //     type: \"selection\",\r\n          //     align: \"center\",\r\n          //   },\r\n          // },\r\n          {\r\n            label: \"通行时间\",\r\n            key: \"PassTime\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"车牌号码\",\r\n            key: \"Number\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"车辆类型\",\r\n            key: \"VehicleType\",\r\n          },\r\n          {\r\n            label: \"车主姓名\",\r\n            key: \"UserName\",\r\n          },\r\n          {\r\n            label: \"车主联系方式\",\r\n            key: \"UserPhone\",\r\n          },\r\n          {\r\n            label: \"访问类型\",\r\n            key: \"AccessType\",\r\n          },\r\n          {\r\n            label: \"过车方向\",\r\n            key: \"PassType\",\r\n          },\r\n          {\r\n            label: \"出入�?,\r\n            key: \"EntranceName\",\r\n          },\r\n          {\r\n            label: \"通行方式\",\r\n            key: \"PassMode\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.$router.push({\r\n                name: \"trafficRecordsView\",\r\n                query: { pg_redirect: this.$route.name, Id: row.Id },\r\n              });\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/view\",\r\n          hidden: true,\r\n          component: () => import(\"./dialog/view.vue\"),\r\n          meta: { title: \"内部车辆管理详情\" },\r\n          name: \"trafficRecordsView\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.vBPassRecordGetDropList();\r\n  },\r\n  async mounted() {\r\n    // 跳转设置默认参数\r\n    let JumpParams = this.$qiankun.getMicroAppJumpParamsFn();\r\n    if (JumpParams.isJump == \"true\") {\r\n      let StartTime = dayjs(Number(JumpParams.StartTime)).format(\r\n        \"YYYY-MM-DD HH:mm\"\r\n      );\r\n      let EndTime = dayjs(Number(JumpParams.EndTime)).format(\r\n        \"YYYY-MM-DD HH:mm\"\r\n      );\r\n      this.ruleForm.PassType = JumpParams.PassType\r\n      this.ruleForm.Date = [StartTime, EndTime];\r\n      this.ruleForm.StartTime = StartTime;\r\n      this.ruleForm.EndTime = EndTime;\r\n    }\r\n    this.onFresh()\r\n  },\r\n  beforeDestroy() {\r\n    this.$qiankun.setMicroAppJumpParamsFn();\r\n    this.ruleForm.StartTime = null;\r\n    this.ruleForm.EndTime = null;\r\n  },\r\n  methods: {\r\n    async vBPassRecordGetDropList() {\r\n      let res = await VBPassRecordGetDropList({});\r\n      if (res.IsSucceed) {\r\n        this.customForm.formItems.find((v) => v.key == \"AccessType\").options =\r\n          res.Data.find((v) => v.Name == \"AccessType\").List.map((item) => ({\r\n            label: item.Key,\r\n            value: item.Value,\r\n          }));\r\n\r\n        this.customForm.formItems.find((v) => v.key == \"PassType\").options =\r\n          res.Data.find((v) => v.Name == \"PassType\").List.map((item) => ({\r\n            label: item.Key,\r\n            value: item.Value,\r\n          }));\r\n\r\n        this.customForm.formItems.find((v) => v.key == \"PassMode\").options =\r\n          res.Data.find((v) => v.Name == \"PassMode\").List.map((item) => ({\r\n            label: item.Key,\r\n            value: item.Value,\r\n          }));\r\n      }\r\n    },\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n    },\r\n    async init() {\r\n      await this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      if (this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null;\r\n        this.ruleForm.EndTime = null;\r\n      }\r\n      const res = await VBPassRecordGetPassRecordList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.Total;\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.onFresh();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.onFresh();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      const Ids = [];\r\n      this.tableSelection = selection;\r\n      this.tableSelection.forEach((item) => {\r\n        Ids.push(item.Id);\r\n      });\r\n      console.log(Ids);\r\n      this.selectIds = Ids;\r\n      console.log(this.tableSelection);\r\n    },\r\n    handleview(row) {\r\n      this.dialogVisible = true;\r\n      this.PassImg = row.PassImg;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n\r\n.imgwapper {\r\n  width: 100px;\r\n  height: 100px;\r\n}\r\n.empty-img {\r\n  text-align: center;\r\n}\r\n</style>\r\n"]}]}