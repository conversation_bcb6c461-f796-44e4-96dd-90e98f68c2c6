{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControlV2\\accessControlRecord\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControlV2\\accessControlRecord\\index.vue", "mtime": 1755674552410}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "downloadFile", "dayjs", "ExportEntranceEquipmentList", "GetDictionaryDetailListByCode", "GetTrafficRecordPageList", "GetCompanyList", "addRouterPage", "GetDepartmentTree", "name", "components", "data", "_this", "componentsConfig", "componentsFuns", "open", "dialogVisible", "close", "onFresh", "ruleForm", "PName", "daterangeArr", "BeginTime", "EndTime", "EquipmentType", "Position", "TrafficType", "TrafficWay", "PType", "EquipmentName", "Phone", "Dept", "Company", "categoryOptions", "filterable", "clickParent", "props", "disabled", "children", "label", "value", "customForm", "formItems", "key", "type", "otherOptions", "clearable", "change", "e", "console", "log", "rangeSeparator", "startPlaceholder", "endPlaceholder", "options", "placeholder", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "text", "onclick", "item", "handleExport", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "fixed", "tableData", "tableActions", "actionLabel", "index", "row", "handleEdit", "addPageArray", "path", "$route", "hidden", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "meta", "title", "computed", "created", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "initDeviceType", "find", "sent", "initGetCompanyList", "getDeptTreeData", "init", "stop", "mixins", "methods", "code", "_callee2", "res", "_callee2$", "_context2", "dictionaryCode", "Data", "map", "Display_Name", "Value", "abrupt", "_callee3", "_callee3$", "_context3", "Name", "Id", "searchForm", "resetForm", "getTrafficRecordPageList", "_this3", "_callee4", "_this3$ruleForm$dater", "_callee4$", "_context4", "_objectSpread", "length", "format", "Page", "PageSize", "IsSucceed", "Traffic_Time", "TotalCount", "$message", "error", "Message", "$router", "push", "query", "pg_redirect", "JSON", "stringify", "_this4", "_callee5", "_this4$ruleForm$dater", "_callee5$", "_context5", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "selection", "tableSelection", "_this5", "_callee6", "_callee6$", "_context6", "isAll", "$nextTick", "_", "$refs", "treeSelectArea", "treeDataUpdateFun", "searchFun", "filterFun"], "sources": ["src/views/business/accessControlV2/accessControlRecord/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        >\r\n          <template #formSlot=\"{ slotScope }\">\r\n            <el-tree-select\r\n              ref=\"treeSelectArea\"\r\n              v-model=\"ruleForm.Dept\"\r\n              :select-params=\"{\r\n                clearable: true,\r\n              }\"\r\n              @searchFun=\"searchFun\"\r\n              class=\"customTreeSelect\"\r\n              :tree-params=\"categoryOptions\"\r\n            />\r\n          </template>\r\n        </CustomForm>\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\nimport dayjs from \"dayjs\";\r\nimport {\r\n  ExportEntranceEquipmentList,\r\n  GetDictionaryDetailListByCode,\r\n  GetTrafficRecordPageList,\r\n  GetCompanyList,\r\n} from \"@/api/business/accessControl\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nimport { GetDepartmentTree } from \"@/api/sys\";\r\n\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      ruleForm: {\r\n        PName: \"\",\r\n        daterangeArr: \"\",\r\n        BeginTime: \"\",\r\n        EndTime: \"\",\r\n        EquipmentType: \"\",\r\n        Position: \"\",\r\n        TrafficType: \"\",\r\n        TrafficWay: \"\",\r\n        PType: \"\",\r\n        EquipmentName: \"\",\r\n        Phone: \"\",\r\n        Dept: \"\",\r\n        Company: \"\",\r\n      },\r\n      categoryOptions: {\r\n        \"default-expand-all\": true,\r\n        filterable: true,\r\n        clickParent: false,\r\n        data: [],\r\n        props: {\r\n          disabled: \"disabled\",\r\n          children: \"Children\",\r\n          label: \"Label\",\r\n          value: \"Id\",\r\n        },\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"PName\", // 字段ID\r\n            label: \"姓名\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            // width: '240px',\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"daterangeArr\",\r\n            label: \"通行时间\",\r\n            type: \"datePicker\",\r\n            otherOptions: {\r\n              rangeSeparator: \"至\",\r\n              startPlaceholder: \"开始日期\",\r\n              endPlaceholder: \"结束日期\",\r\n              type: \"daterange\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"EquipmentType\", // 字段ID\r\n            label: \"门禁类型\", // Form的label\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Position\", // 字段ID\r\n            label: \"安装位置\", // Form的label\r\n            type: \"input\",\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"TrafficWay\", // 字段ID\r\n            label: \"通行方式\", // Form的label\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"TrafficType\", // 字段ID\r\n            label: \"通行类型\", // Form的label\r\n            type: \"select\",\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            options: [\r\n              { label: \"进\", value: \"进\" },\r\n              { label: \"出\", value: \"出\" },\r\n            ],\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"PType\", // 字段ID\r\n            label: \"人员类型\", // Form的label\r\n            type: \"select\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"EquipmentName\", // 字段ID\r\n            label: \"门禁名称\", // Form的label\r\n            type: \"input\",\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Phone\", // 字段ID\r\n            label: \"联系方式\", // Form的label\r\n            type: \"input\",\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Dept\", // 字段ID\r\n            label: \"人员部门\", // Form的label\r\n            type: \"slot\",\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Company\", // 字段ID\r\n            label: \"所属公司\", // Form的label\r\n            type: \"select\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleExport();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: \"通行时间\",\r\n            key: \"TrafficTime\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"姓名\",\r\n            key: \"PName\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"联系方式\",\r\n            key: \"ContactWay\",\r\n          },\r\n          {\r\n            label: \"人员类型\",\r\n            key: \"PType\",\r\n          },\r\n          {\r\n            label: \"所属部门\",\r\n            key: \"Department\",\r\n          },\r\n          {\r\n            label: \"所属公司\",\r\n            key: \"CompanyName\",\r\n          },\r\n          {\r\n            label: \"通行类型\",\r\n            key: \"TrafficType\",\r\n          },\r\n          {\r\n            label: \"设备类型\",\r\n            key: \"EquipmentType\",\r\n          },\r\n          {\r\n            label: \"设备名称\",\r\n            key: \"EquipmentName\",\r\n          },\r\n          {\r\n            label: \"设备位置\",\r\n            key: \"Position\",\r\n          },\r\n          {\r\n            label: \"通行方式\",\r\n            key: \"TrafficWay\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看详情\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/detail\",\r\n          hidden: true,\r\n          component: () => import(\"./detail.vue\"),\r\n          meta: { title: `门禁通行详情` },\r\n          name: \"accessControlRecordDetail\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  computed: {},\r\n  async created() {\r\n    // 门禁类型\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === \"EquipmentType\"\r\n    ).options = await this.initDeviceType(\"Entrance_Type\");\r\n    // 人员类型\r\n    this.customForm.formItems.find((item) => item.key === \"PType\").options =\r\n      await this.initDeviceType(\"P_Type\");\r\n    // 所属公司\r\n    this.customForm.formItems.find((item) => item.key === \"Company\").options =\r\n      await this.initGetCompanyList(\"P_Type\");\r\n    //\r\n    this.getDeptTreeData();\r\n    this.init();\r\n  },\r\n  mixins: [addRouterPage],\r\n  methods: {\r\n    async initDeviceType(code) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode: code,\r\n      });\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value,\r\n      }));\r\n      return options;\r\n    },\r\n    async initGetCompanyList(code) {\r\n      const res = await GetCompanyList({});\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Name,\r\n        value: item.Id,\r\n      }));\r\n      return options;\r\n    },\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1;\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm = {\r\n        PName: \"\",\r\n        daterangeArr: \"\",\r\n        BeginTime: \"\",\r\n        EndTime: \"\",\r\n        EquipmentType: \"\",\r\n        Position: \"\",\r\n        TrafficType: \"\",\r\n        TrafficWay: \"\",\r\n        PType: \"\",\r\n        EquipmentName: \"\",\r\n        Phone: \"\",\r\n        Dept: \"\",\r\n        Company: \"\",\r\n      };\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.getTrafficRecordPageList();\r\n    },\r\n    init() {\r\n      this.getTrafficRecordPageList();\r\n    },\r\n    async getTrafficRecordPageList() {\r\n      let BeginTime = \"\";\r\n      let EndTime = \"\";\r\n      const data = { ...this.ruleForm };\r\n      if ((this.ruleForm.daterangeArr ?? []).length > 0) {\r\n        BeginTime = dayjs(this.ruleForm.daterangeArr[0]).format(\"YYYY-MM-DD\");\r\n        EndTime = dayjs(this.ruleForm.daterangeArr[1]).format(\"YYYY-MM-DD\");\r\n      }\r\n      delete data.daterangeArr;\r\n      console.log({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...data,\r\n        BeginTime,\r\n        EndTime,\r\n      });\r\n      const res = await GetTrafficRecordPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...data,\r\n        BeginTime,\r\n        EndTime,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n          ...item,\r\n          Traffic_Time: dayjs(item.Traffic_Time).format(\"YYYY-MM-DD HH:mm:ss\"),\r\n        }));\r\n        console.log(res);\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    handleEdit(index, row) {\r\n      this.$router.push({\r\n        name: \"accessControlRecordDetail\",\r\n        query: { pg_redirect: this.$route.name, row: JSON.stringify(row) },\r\n      });\r\n    },\r\n    async handleExport() {\r\n      let BeginTime = \"\";\r\n      let EndTime = \"\";\r\n      if ((this.ruleForm.daterangeArr ?? []).length > 0) {\r\n        BeginTime = dayjs(this.ruleForm.daterangeArr[0]).format(\"YYYY-MM-DD\");\r\n        EndTime = dayjs(this.ruleForm.daterangeArr[1]).format(\"YYYY-MM-DD\");\r\n      }\r\n      const res = await ExportEntranceEquipmentList({\r\n        // id: this.tableSelection.map((item) => item.Id).join(','),\r\n        ...this.ruleForm,\r\n        BeginTime,\r\n        EndTime,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        downloadFile(res.Data, \"21\");\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.init();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.init();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    async getDeptTreeData() {\r\n      await GetDepartmentTree({ isAll: false }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.categoryOptions.data = res.Data;\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data);\r\n          });\r\n        }\r\n      });\r\n    },\r\n    searchFun(value) {\r\n      this.$refs.treeSelectArea.filterFun(value);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,SAAAC,YAAA;AACA,OAAAC,KAAA;AACA,SACAC,2BAAA,EACAC,6BAAA,EACAC,wBAAA,EACAC,cAAA,QACA;AACA,OAAAC,aAAA;AACA,SAAAC,iBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAX,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAa,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,gBAAA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAH,KAAA,CAAAI,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAL,KAAA,CAAAI,aAAA;UACAJ,KAAA,CAAAM,OAAA;QACA;MACA;MACAC,QAAA;QACAC,KAAA;QACAC,YAAA;QACAC,SAAA;QACAC,OAAA;QACAC,aAAA;QACAC,QAAA;QACAC,WAAA;QACAC,UAAA;QACAC,KAAA;QACAC,aAAA;QACAC,KAAA;QACAC,IAAA;QACAC,OAAA;MACA;MACAC,eAAA;QACA;QACAC,UAAA;QACAC,WAAA;QACAxB,IAAA;QACAyB,KAAA;UACAC,QAAA;UACAC,QAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UAAA;UACAJ,KAAA;UAAA;UACAK,IAAA;UAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAL,GAAA;UACAJ,KAAA;UACAK,IAAA;UACAC,YAAA;YACAM,cAAA;YACAC,gBAAA;YACAC,cAAA;YACAT,IAAA;YACAE,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAL,GAAA;UAAA;UACAJ,KAAA;UAAA;UACAK,IAAA;UACAU,OAAA;UACAT,YAAA;YACAU,WAAA;YACAT,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAL,GAAA;UAAA;UACAJ,KAAA;UAAA;UACAK,IAAA;UACAC,YAAA;YACAU,WAAA;YACAT,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAL,GAAA;UAAA;UACAJ,KAAA;UAAA;UACAK,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAL,GAAA;UAAA;UACAJ,KAAA;UAAA;UACAK,IAAA;UACAC,YAAA;YACAU,WAAA;YACAT,SAAA;UACA;UACAQ,OAAA,GACA;YAAAf,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,EACA;UACAO,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAL,GAAA;UAAA;UACAJ,KAAA;UAAA;UACAK,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAQ,OAAA;UACAP,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAL,GAAA;UAAA;UACAJ,KAAA;UAAA;UACAK,IAAA;UACAC,YAAA;YACAU,WAAA;YACAT,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAL,GAAA;UAAA;UACAJ,KAAA;UAAA;UACAK,IAAA;UACAC,YAAA;YACAU,WAAA;YACAT,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAL,GAAA;UAAA;UACAJ,KAAA;UAAA;UACAK,IAAA;UACAG,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAL,GAAA;UAAA;UACAJ,KAAA;UAAA;UACAK,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAQ,OAAA;UACAP,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,EACA;QACAQ,KAAA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAAC,IAAA;cACAhB,OAAA,CAAAC,GAAA,CAAAe,IAAA;cACArD,KAAA,CAAAsD,YAAA;YACA;UACA;QAEA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA,GACA;UACAhC,KAAA;UACAI,GAAA;UACAE,YAAA;YACA2B,KAAA;UACA;QACA,GACA;UACAjC,KAAA;UACAI,GAAA;UACAE,YAAA;YACA2B,KAAA;UACA;QACA,GACA;UACAjC,KAAA;UACAI,GAAA;QACA,GACA;UACAJ,KAAA;UACAI,GAAA;QACA,GACA;UACAJ,KAAA;UACAI,GAAA;QACA,GACA;UACAJ,KAAA;UACAI,GAAA;QACA,GACA;UACAJ,KAAA;UACAI,GAAA;QACA,GACA;UACAJ,KAAA;UACAI,GAAA;QACA,GACA;UACAJ,KAAA;UACAI,GAAA;QACA,GACA;UACAJ,KAAA;UACAI,GAAA;QACA,GACA;UACAJ,KAAA;UACAI,GAAA;QACA,EACA;QACA8B,SAAA;QACAC,YAAA,GACA;UACAC,WAAA;UACA9B,YAAA;YACAD,IAAA;UACA;UACAoB,OAAA,WAAAA,QAAAY,KAAA,EAAAC,GAAA;YACAjE,KAAA,CAAAkE,UAAA,CAAAF,KAAA,EAAAC,GAAA;UACA;QACA;MAEA;MACAE,YAAA,GACA;QACAC,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAC,IAAA;UAAAC,KAAA;QAAA;QACAjF,IAAA;MACA;IAEA;EACA;EACAkF,QAAA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OAIAT,MAAA,CAAAU,cAAA;UAAA;YAFAV,MAAA,CAAApD,UAAA,CAAAC,SAAA,CAAA8D,IAAA,CACA,UAAAvC,IAAA;cAAA,OAAAA,IAAA,CAAAtB,GAAA;YAAA,CACA,EAAAW,OAAA,GAAA8C,QAAA,CAAAK,IAAA;YAAAL,QAAA,CAAAE,IAAA;YAAA,OAGAT,MAAA,CAAAU,cAAA;UAAA;YADAV,MAAA,CAAApD,UAAA,CAAAC,SAAA,CAAA8D,IAAA,WAAAvC,IAAA;cAAA,OAAAA,IAAA,CAAAtB,GAAA;YAAA,GAAAW,OAAA,GAAA8C,QAAA,CAAAK,IAAA;YAAAL,QAAA,CAAAE,IAAA;YAAA,OAIAT,MAAA,CAAAa,kBAAA;UAAA;YADAb,MAAA,CAAApD,UAAA,CAAAC,SAAA,CAAA8D,IAAA,WAAAvC,IAAA;cAAA,OAAAA,IAAA,CAAAtB,GAAA;YAAA,GAAAW,OAAA,GAAA8C,QAAA,CAAAK,IAAA;YAEA;YACAZ,MAAA,CAAAc,eAAA;YACAd,MAAA,CAAAe,IAAA;UAAA;UAAA;YAAA,OAAAR,QAAA,CAAAS,IAAA;QAAA;MAAA,GAAAZ,OAAA;IAAA;EACA;EACAa,MAAA,GAAAvG,aAAA;EACAwG,OAAA;IACAR,cAAA,WAAAA,eAAAS,IAAA;MAAA,OAAAlB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAiB,SAAA;QAAA,IAAAC,GAAA,EAAA5D,OAAA;QAAA,OAAAyC,mBAAA,GAAAG,IAAA,UAAAiB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAf,IAAA,GAAAe,SAAA,CAAAd,IAAA;YAAA;cAAAc,SAAA,CAAAd,IAAA;cAAA,OACAlG,6BAAA;gBACAiH,cAAA,EAAAL;cACA;YAAA;cAFAE,GAAA,GAAAE,SAAA,CAAAX,IAAA;cAGAnD,OAAA,GAAA4D,GAAA,CAAAI,IAAA,CAAAC,GAAA,WAAAtD,IAAA,EAAAW,KAAA;gBAAA;kBACArC,KAAA,EAAA0B,IAAA,CAAAuD,YAAA;kBACAhF,KAAA,EAAAyB,IAAA,CAAAwD;gBACA;cAAA;cAAA,OAAAL,SAAA,CAAAM,MAAA,WACApE,OAAA;YAAA;YAAA;cAAA,OAAA8D,SAAA,CAAAP,IAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IACA;IACAP,kBAAA,WAAAA,mBAAAM,IAAA;MAAA,OAAAlB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA2B,SAAA;QAAA,IAAAT,GAAA,EAAA5D,OAAA;QAAA,OAAAyC,mBAAA,GAAAG,IAAA,UAAA0B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxB,IAAA,GAAAwB,SAAA,CAAAvB,IAAA;YAAA;cAAAuB,SAAA,CAAAvB,IAAA;cAAA,OACAhG,cAAA;YAAA;cAAA4G,GAAA,GAAAW,SAAA,CAAApB,IAAA;cACAnD,OAAA,GAAA4D,GAAA,CAAAI,IAAA,CAAAC,GAAA,WAAAtD,IAAA,EAAAW,KAAA;gBAAA;kBACArC,KAAA,EAAA0B,IAAA,CAAA6D,IAAA;kBACAtF,KAAA,EAAAyB,IAAA,CAAA8D;gBACA;cAAA;cAAA,OAAAF,SAAA,CAAAH,MAAA,WACApE,OAAA;YAAA;YAAA;cAAA,OAAAuE,SAAA,CAAAhB,IAAA;UAAA;QAAA,GAAAc,QAAA;MAAA;IACA;IACAK,UAAA,WAAAA,WAAArH,IAAA;MACAsC,OAAA,CAAAC,GAAA,CAAAvC,IAAA;MACA,KAAAiD,iBAAA,CAAAQ,WAAA;MACA,KAAAlD,OAAA;IACA;IACA+G,SAAA,WAAAA,UAAA;MACA,KAAA9G,QAAA;QACAC,KAAA;QACAC,YAAA;QACAC,SAAA;QACAC,OAAA;QACAC,aAAA;QACAC,QAAA;QACAC,WAAA;QACAC,UAAA;QACAC,KAAA;QACAC,aAAA;QACAC,KAAA;QACAC,IAAA;QACAC,OAAA;MACA;MACA,KAAAd,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAAgH,wBAAA;IACA;IACAtB,IAAA,WAAAA,KAAA;MACA,KAAAsB,wBAAA;IACA;IACAA,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MAAA,OAAArC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAoC,SAAA;QAAA,IAAAC,qBAAA;QAAA,IAAA/G,SAAA,EAAAC,OAAA,EAAAZ,IAAA,EAAAuG,GAAA;QAAA,OAAAnB,mBAAA,GAAAG,IAAA,UAAAoC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlC,IAAA,GAAAkC,SAAA,CAAAjC,IAAA;YAAA;cACAhF,SAAA;cACAC,OAAA;cACAZ,IAAA,GAAA6H,aAAA,KAAAL,MAAA,CAAAhH,QAAA;cACA,MAAAkH,qBAAA,GAAAF,MAAA,CAAAhH,QAAA,CAAAE,YAAA,cAAAgH,qBAAA,cAAAA,qBAAA,OAAAI,MAAA;gBACAnH,SAAA,GAAApB,KAAA,CAAAiI,MAAA,CAAAhH,QAAA,CAAAE,YAAA,KAAAqH,MAAA;gBACAnH,OAAA,GAAArB,KAAA,CAAAiI,MAAA,CAAAhH,QAAA,CAAAE,YAAA,KAAAqH,MAAA;cACA;cACA,OAAA/H,IAAA,CAAAU,YAAA;cACA4B,OAAA,CAAAC,GAAA,CAAAsF,aAAA,CAAAA,aAAA;gBACAG,IAAA,EAAAR,MAAA,CAAAvE,iBAAA,CAAAQ,WAAA;gBACAwE,QAAA,EAAAT,MAAA,CAAAvE,iBAAA,CAAAS;cAAA,GACA1D,IAAA;gBACAW,SAAA,EAAAA,SAAA;gBACAC,OAAA,EAAAA;cAAA,EACA;cAAAgH,SAAA,CAAAjC,IAAA;cAAA,OACAjG,wBAAA,CAAAmI,aAAA,CAAAA,aAAA;gBACAG,IAAA,EAAAR,MAAA,CAAAvE,iBAAA,CAAAQ,WAAA;gBACAwE,QAAA,EAAAT,MAAA,CAAAvE,iBAAA,CAAAS;cAAA,GACA1D,IAAA;gBACAW,SAAA,EAAAA,SAAA;gBACAC,OAAA,EAAAA;cAAA,EACA;YAAA;cANA2F,GAAA,GAAAqB,SAAA,CAAA9B,IAAA;cAOA,IAAAS,GAAA,CAAA2B,SAAA;gBACAV,MAAA,CAAAvE,iBAAA,CAAAa,SAAA,GAAAyC,GAAA,CAAAI,IAAA,CAAAA,IAAA,CAAAC,GAAA,WAAAtD,IAAA;kBAAA,OAAAuE,aAAA,CAAAA,aAAA,KACAvE,IAAA;oBACA6E,YAAA,EAAA5I,KAAA,CAAA+D,IAAA,CAAA6E,YAAA,EAAAJ,MAAA;kBAAA;gBAAA,CACA;gBACAzF,OAAA,CAAAC,GAAA,CAAAgE,GAAA;gBACAiB,MAAA,CAAAvE,iBAAA,CAAAU,KAAA,GAAA4C,GAAA,CAAAI,IAAA,CAAAyB,UAAA;cACA;gBACAZ,MAAA,CAAAa,QAAA,CAAAC,KAAA,CAAA/B,GAAA,CAAAgC,OAAA;cACA;YAAA;YAAA;cAAA,OAAAX,SAAA,CAAA1B,IAAA;UAAA;QAAA,GAAAuB,QAAA;MAAA;IACA;IACAtD,UAAA,WAAAA,WAAAF,KAAA,EAAAC,GAAA;MACA,KAAAsE,OAAA,CAAAC,IAAA;QACA3I,IAAA;QACA4I,KAAA;UAAAC,WAAA,OAAArE,MAAA,CAAAxE,IAAA;UAAAoE,GAAA,EAAA0E,IAAA,CAAAC,SAAA,CAAA3E,GAAA;QAAA;MACA;IACA;IACAX,YAAA,WAAAA,aAAA;MAAA,IAAAuF,MAAA;MAAA,OAAA3D,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA0D,SAAA;QAAA,IAAAC,qBAAA;QAAA,IAAArI,SAAA,EAAAC,OAAA,EAAA2F,GAAA;QAAA,OAAAnB,mBAAA,GAAAG,IAAA,UAAA0D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxD,IAAA,GAAAwD,SAAA,CAAAvD,IAAA;YAAA;cACAhF,SAAA;cACAC,OAAA;cACA,MAAAoI,qBAAA,GAAAF,MAAA,CAAAtI,QAAA,CAAAE,YAAA,cAAAsI,qBAAA,cAAAA,qBAAA,OAAAlB,MAAA;gBACAnH,SAAA,GAAApB,KAAA,CAAAuJ,MAAA,CAAAtI,QAAA,CAAAE,YAAA,KAAAqH,MAAA;gBACAnH,OAAA,GAAArB,KAAA,CAAAuJ,MAAA,CAAAtI,QAAA,CAAAE,YAAA,KAAAqH,MAAA;cACA;cAAAmB,SAAA,CAAAvD,IAAA;cAAA,OACAnG,2BAAA,CAAAqI,aAAA,CAAAA,aAAA,KAEAiB,MAAA,CAAAtI,QAAA;gBACAG,SAAA,EAAAA,SAAA;gBACAC,OAAA,EAAAA;cAAA,EACA;YAAA;cALA2F,GAAA,GAAA2C,SAAA,CAAApD,IAAA;cAMA,IAAAS,GAAA,CAAA2B,SAAA;gBACA5F,OAAA,CAAAC,GAAA,CAAAgE,GAAA;gBACAjH,YAAA,CAAAiH,GAAA,CAAAI,IAAA;cACA;gBACAmC,MAAA,CAAAT,QAAA,CAAAC,KAAA,CAAA/B,GAAA,CAAAgC,OAAA;cACA;YAAA;YAAA;cAAA,OAAAW,SAAA,CAAAhD,IAAA;UAAA;QAAA,GAAA6C,QAAA;MAAA;IACA;IACAI,gBAAA,WAAAA,iBAAAC,GAAA;MACA9G,OAAA,CAAAC,GAAA,iBAAA8G,MAAA,CAAAD,GAAA;MACA,KAAAnG,iBAAA,CAAAS,QAAA,GAAA0F,GAAA;MACA,KAAAnD,IAAA;IACA;IACAqD,mBAAA,WAAAA,oBAAAF,GAAA;MACA9G,OAAA,CAAAC,GAAA,wBAAA8G,MAAA,CAAAD,GAAA;MACA,KAAAnG,iBAAA,CAAAQ,WAAA,GAAA2F,GAAA;MACA,KAAAnD,IAAA;IACA;IACAsD,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAC,cAAA,GAAAD,SAAA;IACA;IACAxD,eAAA,WAAAA,gBAAA;MAAA,IAAA0D,MAAA;MAAA,OAAAvE,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAsE,SAAA;QAAA,OAAAvE,mBAAA,GAAAG,IAAA,UAAAqE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnE,IAAA,GAAAmE,SAAA,CAAAlE,IAAA;YAAA;cAAAkE,SAAA,CAAAlE,IAAA;cAAA,OACA9F,iBAAA;gBAAAiK,KAAA;cAAA,GAAAnF,IAAA,WAAA4B,GAAA;gBACA,IAAAA,GAAA,CAAA2B,SAAA;kBACAwB,MAAA,CAAApI,eAAA,CAAAtB,IAAA,GAAAuG,GAAA,CAAAI,IAAA;kBACA+C,MAAA,CAAAK,SAAA,WAAAC,CAAA;oBACAN,MAAA,CAAAO,KAAA,CAAAC,cAAA,CAAAC,iBAAA,CAAA5D,GAAA,CAAAI,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAkD,SAAA,CAAA3D,IAAA;UAAA;QAAA,GAAAyD,QAAA;MAAA;IACA;IACAS,SAAA,WAAAA,UAAAvI,KAAA;MACA,KAAAoI,KAAA,CAAAC,cAAA,CAAAG,SAAA,CAAAxI,KAAA;IACA;EACA;AACA", "ignoreList": []}]}