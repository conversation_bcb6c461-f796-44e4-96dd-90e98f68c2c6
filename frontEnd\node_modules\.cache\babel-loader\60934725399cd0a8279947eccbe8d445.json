{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\szcjPJEquipmentAssetList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\szcjPJEquipmentAssetList\\index.vue", "mtime": 1755674552420}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["nopictures", "CustomForm", "editDialog", "GetDeviceStatus", "GetDeviceList", "GetPostionTreeList", "GetDictionaryDetailListByParentId", "GetOssUrl", "GetDictionaryDetailListByCode", "getDictionary", "addRouterPage", "name", "components", "mixins", "data", "_this", "tagsStyle", "text", "color", "background", "ruleForm", "Display_Name", "Brand", "Device_Type_Id", "Device_Type_Detail_Id", "Postion", "customForm", "formItems", "key", "label", "type", "otherOptions", "clearable", "change", "e", "console", "log", "options", "find", "v", "then", "res", "Data", "map", "value", "Id", "rules", "customFormButtons", "marginLeft", "submitName", "submitShow", "resetShow", "resetName", "deviceStatusList", "positionTree", "deviceList", "deviceListLoading", "positionTreeLoading", "defaultProps", "children", "defaultExpandedKeys", "addPageArray", "path", "$route", "hidden", "component", "Promise", "resolve", "_interopRequireWildcard", "require", "meta", "title", "activated", "<PERSON><PERSON><PERSON><PERSON>", "created", "_this2", "getDeviceStatus", "getPostionTreeList", "getDeviceList", "item", "dictionaryCode", "Value", "mounted", "methods", "getTagsStyle", "nextRouteDetail", "num", "historyRouter", "$router", "push", "query", "pg_redirect", "$store", "dispatch", "_this3", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "sent", "IsSucceed", "_objectSpread", "percent", "toFixed", "$message", "message", "Message", "stop", "getDeviceValue", "length", "Label", "getDevicePrecentValue", "_this4", "_callee2", "_callee2$", "_context2", "_this5", "_callee3", "_callee3$", "_context3", "Url", "isHaveUrl", "submitForm", "resetForm", "handleNodeClick", "node", "_this6", "_callee4", "parents", "newNode", "_callee4$", "_context4", "findParentIds", "concat", "_toConsumableArray", "Name", "join", "tree", "targetId", "parentNodes", "parentNodeExists", "id", "array", "some", "traverse", "nodes", "parentId", "parentName", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "Children", "err", "f", "reverse"], "sources": ["src/views/business/equipmentManagement/szcjPJEquipmentAssetList/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 szcjPJEquipmentAssetList\">\r\n    <el-row :gutter=\"12\">\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"never\">\r\n          <div class=\"card_content\">\r\n            <div class=\"left\">\r\n              <span class=\"num\">{{ getDeviceValue(\"设备总数\") }}</span>\r\n              <span>设备总数 </span>\r\n            </div>\r\n            <div class=\"right\">\r\n              <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #4ebf8b\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"在线\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <span>在线 </span>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"在线\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #1bb5e0\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"正常\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <span>正常 </span>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"正常\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #68748a\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"离线\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <span>离线 </span>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"离线\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #ff5f7a\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"故障\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <div>\r\n                      <span>故障 </span>\r\n                      <el-popover\r\n                        placement=\"top-start\"\r\n                        title=\"\"\r\n                        width=\"\"\r\n                        trigger=\"hover\"\r\n                        content=\"对设备发起报修单记为设备故障\"\r\n                      >\r\n                        <img\r\n                          slot=\"reference\"\r\n                          class=\"popinfo\"\r\n                          src=\"@/assets/<EMAIL>\"\r\n                          alt=\"\"\r\n                        >\r\n                      </el-popover>\r\n                    </div>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"故障\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #ff902c\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"异常\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <div>\r\n                      <span>异常 </span>\r\n                      <el-popover\r\n                        placement=\"top-start\"\r\n                        title=\"\"\r\n                        width=\"\"\r\n                        trigger=\"hover\"\r\n                        content=\"设备所传的一切非正常状态均记为异常\"\r\n                      >\r\n                        <img\r\n                          slot=\"reference\"\r\n                          class=\"popinfo\"\r\n                          src=\"@/assets/<EMAIL>\"\r\n                          alt=\"\"\r\n                        >\r\n                      </el-popover>\r\n                    </div>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"异常\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <div class=\"card_second_content\">\r\n                <div class=\"left\" style=\"color: #6754d2\">\r\n                  <span class=\"num\">{{ getDeviceValue(\"维修中\") }}</span>\r\n                  <div class=\"textInfo\">\r\n                    <span>维修中 </span>\r\n                    <span class=\"textStyle\">{{\r\n                      getDevicePrecentValue(\"维修中\")\r\n                    }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"right\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px; flex: 1; height: 0;\">\r\n      <el-col :span=\"3\" style=\"height: 100%\">\r\n        <el-card\r\n          shadow=\"never\"\r\n          class=\"tree_card\"\r\n          style=\"height: 100%; padding: 0\"\r\n        >\r\n          <el-tree\r\n            ref=\"positionTreeRef\"\r\n            v-loading=\"positionTreeLoading\"\r\n            class=\"positionTreeClass\"\r\n            style=\"height: calc(100vh - 340px); overflow-y: auto\"\r\n            :data=\"positionTree\"\r\n            :props=\"defaultProps\"\r\n            node-key=\"Id\"\r\n            :expand-on-click-node=\"false\"\r\n            :default-expanded-keys=\"defaultExpandedKeys\"\r\n            @node-click=\"handleNodeClick\"\r\n          />\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"21\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"24\">\r\n            <el-card shadow=\"never\">\r\n              <CustomForm\r\n                :custom-form-items=\"customForm.formItems\"\r\n                :custom-form-buttons=\"customForm.customFormButtons\"\r\n                :value=\"ruleForm\"\r\n                :inline=\"true\"\r\n                :rules=\"customForm.rules\"\r\n                @submitForm=\"submitForm\"\r\n                @resetForm=\"resetForm\"\r\n              />\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row\r\n          :gutter=\"12\"\r\n          style=\"\r\n            margin-top: 10px;\r\n            height: calc(100vh - 430px);\r\n            overflow-y: auto;\r\n          \"\r\n          class=\"list_class\"\r\n        >\r\n          <el-col :span=\"24\" style=\"height: 100%\">\r\n            <div v-loading=\"deviceListLoading\" style=\"height: 100%\">\r\n              <div v-if=\"deviceList.length > 0\" class=\"list_box\">\r\n                <div\r\n                  v-for=\"(item, index) in deviceList\"\r\n                  :key=\"index\"\r\n                  class=\"list_item\"\r\n                  @click=\"nextRouteDetail(item)\"\r\n                >\r\n                  <div class=\"list_logo\">\r\n                    <el-image\r\n                      v-if=\"item.isHaveUrl\"\r\n                      style=\"width: auto; height: 100%\"\r\n                      :src=\"item.Url\"\r\n                      fit=\"cover\"\r\n                    >\r\n                      <!-- :preview-src-list=\"[item.Url]\" -->\r\n                      <div slot=\"error\" class=\"image-slot\">\r\n                        <i class=\"el-icon-picture-outline\" />\r\n                      </div>\r\n                    </el-image>\r\n\r\n                    <el-image\r\n                      v-if=\"!item.isHaveUrl\"\r\n                      style=\"width: auto; height: 100%\"\r\n                      :src=\"item.Url\"\r\n                      fit=\"cover\"\r\n                    >\r\n                      <div slot=\"error\" class=\"image-slot\">\r\n                        <i class=\"el-icon-picture-outline\" />\r\n                      </div>\r\n                    </el-image>\r\n                  </div>\r\n                  <div class=\"list_info\">\r\n                    <span class=\"title\">{{ item.Name || \"-\" }}</span>\r\n                    <div class=\"info\">\r\n                      <span class=\"label\">位置</span>\r\n                      <span class=\"value\">{{ item.Postion || \"-\" }}</span>\r\n                    </div>\r\n                    <div class=\"info\">\r\n                      <span class=\"label\">品牌</span>\r\n                      <span class=\"value\">{{ item.Brand || \"-\" }}</span>\r\n                    </div>\r\n                    <div class=\"action\">\r\n                      <div class=\"tags\">\r\n                        <div\r\n                          v-for=\"(statusItem, statusIndex) in item.Status\"\r\n                          :key=\"statusIndex\"\r\n                          class=\"tags_item\"\r\n                          :style=\"{\r\n                            background: getTagsStyle(statusItem).background,\r\n                          }\"\r\n                        >\r\n                          <span\r\n                            v-if=\"statusItem == '在线'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '正常'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '离线'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '故障'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '异常'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <span\r\n                            v-if=\"statusItem == '维修中'\"\r\n                            :style=\"{\r\n                              color: getTagsStyle(statusItem).color,\r\n                            }\"\r\n                          >{{ statusItem }}</span>\r\n                          <!-- <span>{{ statusItem }}</span>\r\n                        <span>{{ statusItem }}</span> -->\r\n                        </div>\r\n                      </div>\r\n                      <div class=\"right\">\r\n                        <span>查看</span>\r\n                        <i class=\"el-icon-arrow-right\" />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div v-else class=\"list_no_box\">\r\n                <div class=\"no_content\">\r\n                  <img src=\"@/assets/<EMAIL>\" alt=\"\">\r\n                  <span>无相关设备信息</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport nopictures from '@/assets/<EMAIL>'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport editDialog from '@/views/business/maintenanceAndUpkeep/workOrderManagement/editDialog.vue'\r\nimport {\r\n  GetDeviceStatus,\r\n  GetDeviceList,\r\n  GetPostionTreeList,\r\n  GetDictionaryDetailListByParentId\r\n} from '@/api/business/eqptAsset'\r\nimport { GetOssUrl, GetDictionaryDetailListByCode } from '@/api/sys/index'\r\nimport { getDictionary } from '@/utils/common'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\n\r\nexport default {\r\n  name: 'SzcjPJEquipmentAssetList',\r\n  components: {\r\n    editDialog,\r\n    CustomForm\r\n  },\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n      tagsStyle: [\r\n        {\r\n          text: '在线',\r\n          color: 'rgba(78, 191, 139, 1)',\r\n          background: 'rgba(78, 191, 139, 0.1)'\r\n        },\r\n        {\r\n          text: '正常',\r\n          color: 'rgba(27, 181, 224, 1)',\r\n          background: 'rgba(27, 181, 224, .1)'\r\n        },\r\n        {\r\n          text: '离线',\r\n          color: 'rgba(104, 116, 138, 1)',\r\n          background: 'rgba(104, 116, 138, .1)'\r\n        },\r\n        {\r\n          text: '故障',\r\n          color: 'rgba(255, 95, 122, 1)',\r\n          background: 'rgba(255, 95, 122, .1)'\r\n        },\r\n        {\r\n          text: '异常',\r\n          color: 'rgba(255, 144, 44, 1)',\r\n          background: 'rgba(255, 144, 44, .1)'\r\n        },\r\n        {\r\n          text: '维修中',\r\n          color: 'rgba(103, 84, 210, 1)',\r\n          background: 'rgba(103, 84, 210, .1)'\r\n        }\r\n      ],\r\n      ruleForm: {\r\n        Display_Name: '',\r\n        Brand: '',\r\n        Device_Type_Id: '',\r\n        Device_Type_Detail_Id: '',\r\n        Postion: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Display_Name',\r\n            label: '设备名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Brand',\r\n            label: '设备品牌',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Device_Type_Id',\r\n            label: '设备类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              this.customForm.formItems.find(\r\n                (v) => v.key === 'Device_Type_Detail_Id'\r\n              ).options = []\r\n              this.ruleForm.Device_Type_Detail_Id = ''\r\n              GetDictionaryDetailListByParentId(e).then((res) => {\r\n                this.customForm.formItems.find(\r\n                  (v) => v.key === 'Device_Type_Detail_Id'\r\n                ).options = res.Data.map((v) => {\r\n                  return {\r\n                    label: v.Display_Name,\r\n                    value: v.Id\r\n                  }\r\n                })\r\n              })\r\n            }\r\n          },\r\n          {\r\n            key: 'Device_Type_Detail_Id',\r\n            label: '设备子类',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Status',\r\n            label: '设备状态',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'WhereOrderStatus',\r\n            label: '维修状态',\r\n            type: 'select',\r\n            options: [\r\n              {\r\n                label: '正常',\r\n                value: '3'\r\n              },\r\n              {\r\n                label: '维修中',\r\n                value: '1'\r\n              },\r\n              {\r\n                label: '故障',\r\n                value: '0'\r\n              }\r\n            ],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          marginLeft: '50px',\r\n          submitName: '搜索',\r\n          submitShow: true,\r\n          resetShow: true,\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      // 设备状态\r\n      deviceStatusList: [],\r\n      // 位置树\r\n      positionTree: [],\r\n      // 设备列表\r\n      deviceList: [],\r\n      deviceListLoading: false,\r\n      positionTreeLoading: false,\r\n      defaultProps: {\r\n        children: 'Children',\r\n        label: 'Name',\r\n        value: 'Id'\r\n      },\r\n      defaultExpandedKeys: [],\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/equipmentData',\r\n          hidden: true,\r\n          component: () => import('./equipmentData.vue'),\r\n          name: 'PJEquipmentData',\r\n          meta: { title: `设备数采` }\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  activated() {},\r\n  beforeDestroy() {},\r\n  created() {\r\n    this.getDeviceStatus()\r\n    this.getPostionTreeList()\r\n    this.getDeviceList()\r\n\r\n    getDictionary('deviceType').then((res) => {\r\n      const item = this.customForm.formItems.find(\r\n        (v) => v.key === 'Device_Type_Id'\r\n      )\r\n      item.options = res.map((v) => {\r\n        return {\r\n          label: v.Display_Name,\r\n          value: v.Id\r\n        }\r\n      })\r\n    })\r\n\r\n    GetDictionaryDetailListByCode({\r\n      dictionaryCode: 'MonitorAudioStatus'\r\n    }).then((res) => {\r\n      const item = this.customForm.formItems.find((v) => v.key === 'Status')\r\n      item.options = res.Data.map((v) => {\r\n        return {\r\n          label: v.Display_Name,\r\n          value: v.Value\r\n        }\r\n      })\r\n    })\r\n  },\r\n  mounted() {},\r\n  methods: {\r\n    getTagsStyle(name) {\r\n      return this.tagsStyle.find((item) => item.text == name)\r\n    },\r\n    nextRouteDetail(data) {\r\n      data.num = 1\r\n      data.historyRouter = this.$route.name\r\n      this.$router.push({\r\n        name: 'PJEquipmentData',\r\n        query: { pg_redirect: this.$route.name, Id: data.Id }\r\n      })\r\n      this.$store.dispatch('eqpt/changeEqptData', data)\r\n    },\r\n\r\n    async getDeviceStatus() {\r\n      const res = await GetDeviceStatus({})\r\n      if (res.IsSucceed) {\r\n        console.log(res, '1221')\r\n        this.deviceStatusList = res.Data.map((item) => ({\r\n          ...item,\r\n          percent: ((item.Value / res.Data[0].Value) * 100).toFixed(0) + '%'\r\n        }))\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    getDeviceValue(name) {\r\n      if (this.deviceStatusList.length > 0) {\r\n        return this.deviceStatusList.find((item) => item.Label == name).Value\r\n      }\r\n      return ''\r\n    },\r\n    getDevicePrecentValue(name) {\r\n      if (this.deviceStatusList.length > 0) {\r\n        return this.deviceStatusList.find((item) => item.Label == name).percent\r\n      }\r\n      return ''\r\n    },\r\n\r\n    async getPostionTreeList() {\r\n      this.positionTreeLoading = true\r\n      const res = await GetPostionTreeList({})\r\n      if (res.IsSucceed) {\r\n        this.positionTreeLoading = false\r\n        this.defaultExpandedKeys = res.Data.map((v) => v.Id)\r\n        this.positionTree = res.Data\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n\r\n    async getDeviceList() {\r\n      this.deviceList = []\r\n      // this.deviceListLoading = true\r\n      const res = await GetDeviceList({\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        // this.userTableData.map(async item => {\r\n        //   item.ImgUrl = await GetOssUrl({ url: item.ImgUrl }).then(res => {\r\n        //     return res.Data\r\n        //   })\r\n        // })\r\n        this.deviceList = res.Data.map((item) => {\r\n          if (item.Url) {\r\n            return {\r\n              ...item,\r\n              isHaveUrl: true\r\n            }\r\n          } else {\r\n            return {\r\n              ...item,\r\n              isHaveUrl: false,\r\n              Url: nopictures\r\n            }\r\n          }\r\n        })\r\n        this.deviceListLoading = false\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n        this.deviceListLoading = false\r\n      }\r\n    },\r\n    submitForm() {\r\n      this.getDeviceList()\r\n    },\r\n    resetForm() {\r\n      this.getDeviceList()\r\n    },\r\n\r\n    async handleNodeClick(data, node) {\r\n      const parents = await this.findParentIds(this.positionTree, data.Id)\r\n      const newNode = [\r\n        ...parents,\r\n        {\r\n          Id: data.Id,\r\n          Name: data.Name\r\n        }\r\n      ]\r\n      this.ruleForm.Postion = newNode.map((v) => v.Name).join('/')\r\n      this.getDeviceList()\r\n    },\r\n    findParentIds(tree, targetId) {\r\n      const parentNodes = [] // 存储唯一的父节点ID和Name\r\n\r\n      // 辅助函数，用于检查父节点是否已存在于数组中\r\n      function parentNodeExists(id, name, array) {\r\n        return array.some((node) => node.Id === id && node.Name === name)\r\n      }\r\n\r\n      function traverse(nodes, parentId, parentName) {\r\n        if (!nodes) return false\r\n        for (const node of nodes) {\r\n          if (node.Id === targetId) {\r\n            // 如果当前节点是目标节点，并且它有父节点，则添加父节点信息（避免重复）\r\n            if (\r\n              parentId !== '' &&\r\n              !parentNodeExists(parentId, parentName, parentNodes)\r\n            ) {\r\n              parentNodes.push({ Id: parentId, Name: parentName })\r\n            }\r\n            return true // 已找到目标节点，停止遍历\r\n          }\r\n\r\n          // 递归遍历子节点\r\n          if (node.Children && traverse(node.Children, node.Id, node.Name)) {\r\n            // 如果在子节点中找到了目标节点，并且当前节点信息未收集，则添加它\r\n            if (!parentNodeExists(node.Id, node.Name, parentNodes)) {\r\n              parentNodes.push({ Id: node.Id, Name: node.Name })\r\n            }\r\n            return true // 继续向上遍历\r\n          }\r\n        }\r\n        return false // 在当前层级未找到目标节点\r\n      }\r\n      // 从树的根节点开始遍历\r\n      traverse(tree, '', '') // 根节点没有父节点，所以parentId和parentName为空字符串\r\n      // 如果需要，可以根据实际需求决定是否反转数组\r\n      parentNodes.reverse().push()\r\n      return parentNodes // 返回包含唯一父节点ID和名称的数组\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.szcjPJEquipmentAssetList {\r\n  // padding: 10px 15px;\r\n  // height: calc(100vh - 90px);\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .tree_card {\r\n    ::v-deep .el-card__body {\r\n      padding: 10px 0px 10px 0px !important;\r\n    }\r\n    ::v-deep .el-tree-node__label {\r\n      text-wrap: wrap !important;\r\n      padding: 6px !important;\r\n    }\r\n    ::v-deep .el-tree-node__content {\r\n      min-height: 32px !important;\r\n      height: auto !important;\r\n    }\r\n  }\r\n\r\n  .positionTreeClass::-webkit-scrollbar {\r\n    width: 5px;\r\n    height: 1px;\r\n  }\r\n  .positionTreeClass::-webkit-scrollbar-thumb {\r\n    /*滚动条里面小方块*/\r\n    border-radius: 10px;\r\n    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);\r\n    background: #f0f2f7;\r\n  }\r\n  .positionTreeClass::-webkit-scrollbar-track {\r\n    /*滚动条里面轨道*/\r\n    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);\r\n    border-radius: 10px;\r\n    // background: rgba(79, 104, 145, 0.35);\r\n  }\r\n\r\n  .list_class::-webkit-scrollbar {\r\n    width: 5px;\r\n    height: 1px;\r\n  }\r\n  .list_class::-webkit-scrollbar-thumb {\r\n    /*滚动条里面小方块*/\r\n    border-radius: 10px;\r\n    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);\r\n    // background: #f0f2f7;\r\n  }\r\n  .list_class::-webkit-scrollbar-track {\r\n    /*滚动条里面轨道*/\r\n    // -webkit-box-shadow: inset 0 0 5px rgba(79, 104, 145, 0.35);\r\n    border-radius: 10px;\r\n    // background: rgba(79, 104, 145, 0.35);\r\n  }\r\n\r\n  .card_content {\r\n    height: 130px;\r\n    padding: 0px 24px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .left {\r\n      font-family: PingFang SC, PingFang SC;\r\n      font-weight: 600;\r\n      font-size: 20px;\r\n      color: #7f8ca2;\r\n      font-style: normal;\r\n      text-transform: none;\r\n      display: flex;\r\n      align-items: center;\r\n      .num {\r\n        font-family: Helvetica, Helvetica;\r\n        font-weight: bold;\r\n        font-size: 40px;\r\n        color: #298dff;\r\n        font-style: normal;\r\n        text-transform: none;\r\n        margin-right: 20px;\r\n      }\r\n    }\r\n    .right {\r\n      img {\r\n        width: 64px;\r\n        height: 64px;\r\n      }\r\n    }\r\n  }\r\n  .list_no_box {\r\n    padding: 50px 10px;\r\n    height: 100%;\r\n    .no_content {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      img {\r\n        width: 300px;\r\n        height: auto;\r\n      }\r\n      span {\r\n        margin-top: 10px;\r\n        font-family: Microsoft YaHei, Microsoft YaHei;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #c2cbe2;\r\n        text-align: center;\r\n        font-style: normal;\r\n        text-transform: none;\r\n      }\r\n    }\r\n  }\r\n\r\n  .list_box {\r\n    width: 100%;\r\n    // height: 100%;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    display: grid;\r\n    grid-template-columns: repeat(5, calc(20% - 10px));\r\n    grid-column-gap: 10px;\r\n    grid-row-gap: 10px;\r\n\r\n    .list_item {\r\n      display: flex;\r\n      flex-direction: column;\r\n      background: white;\r\n      // margin-right: 10px;\r\n      // margin-bottom: 10px;\r\n      cursor: pointer;\r\n      .list_logo {\r\n        height: 140px;\r\n        background-color: #f0f2f7;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        .el-image {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n      .list_info {\r\n        margin-top: 10px;\r\n        // cursor: pointer;\r\n      }\r\n      .title {\r\n        padding: 6px 10px;\r\n        font-family: Microsoft YaHei, Microsoft YaHei;\r\n        font-weight: bold;\r\n        font-size: 16px;\r\n        color: #666666;\r\n        font-style: normal;\r\n        text-transform: none;\r\n      }\r\n      .info {\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0px 10px;\r\n        margin-top: 6px;\r\n        .label {\r\n          font-family: PingFang SC, PingFang SC;\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          font-style: normal;\r\n          text-transform: none;\r\n          margin-right: 6px;\r\n        }\r\n        .value {\r\n          font-family: Helvetica, Helvetica;\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          font-style: normal;\r\n          text-transform: none;\r\n        }\r\n      }\r\n      .action {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding: 0px 10px;\r\n        margin-top: 6px;\r\n        margin-bottom: 8px;\r\n        .tags {\r\n          display: flex;\r\n          flex-direction: row;\r\n          .tags_item {\r\n            font-family: PingFang SC, PingFang SC;\r\n            font-weight: 500;\r\n            font-size: 11px;\r\n            padding: 1px 2px;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            margin-right: 6px;\r\n            border-radius: 2px;\r\n          }\r\n        }\r\n        .right {\r\n          color: #298dff;\r\n          font-size: 13px;\r\n          cursor: pointer;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .card_second_content {\r\n    height: 39px;\r\n    padding: 0px 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .left {\r\n      font-family: PingFang SC, PingFang SC;\r\n      font-weight: 600;\r\n      font-size: 16px;\r\n      font-style: normal;\r\n      text-transform: none;\r\n      display: flex;\r\n      align-items: center;\r\n      .textInfo {\r\n        display: flex;\r\n        flex-direction: column;\r\n        .textStyle {\r\n          margin-top: 8px;\r\n          color: #999999;\r\n        }\r\n      }\r\n      .popinfo {\r\n        width: 15px;\r\n        height: 15px;\r\n        margin-left: 20px;\r\n      }\r\n      .num {\r\n        font-family: Helvetica, Helvetica;\r\n        font-weight: bold;\r\n        font-size: 32px;\r\n        font-style: normal;\r\n        text-transform: none;\r\n        margin-right: 20px;\r\n        min-width: 30px;\r\n      }\r\n    }\r\n    .right {\r\n      img {\r\n        width: 36px;\r\n        height: 36px;\r\n      }\r\n    }\r\n  }\r\n  ::v-deep .el-card__body {\r\n    border: none !important;\r\n  }\r\n  ::v-deep .el-card__header {\r\n    border-bottom: none !important;\r\n  }\r\n  ::v-deep .el-progress__text {\r\n    font-size: 18px !important;\r\n    color: #666666 !important;\r\n  }\r\n  ::v-deep.el-table .row-one {\r\n    background: rgba(41, 141, 255, 0.03) !important;\r\n  }\r\n\r\n  ::v-deep .el-table .row-two {\r\n    background: rgba(255, 255, 255, 1) !important;\r\n  }\r\n\r\n  ::v-deep .el-radio-button__inner {\r\n    background-color: #ffffff;\r\n    height: 32px;\r\n    width: 80px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkVA,OAAAA,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,SACAC,eAAA,EACAC,aAAA,EACAC,kBAAA,EACAC,iCAAA,QACA;AACA,SAAAC,SAAA,EAAAC,6BAAA;AACA,SAAAC,aAAA;AACA,OAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAV,UAAA,EAAAA,UAAA;IACAD,UAAA,EAAAA;EACA;EACAY,MAAA,GAAAH,aAAA;EACAI,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,SAAA,GACA;QACAC,IAAA;QACAC,KAAA;QACAC,UAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,UAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,UAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,UAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,UAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,UAAA;MACA,EACA;MACAC,QAAA;QACAC,YAAA;QACAC,KAAA;QACAC,cAAA;QACAC,qBAAA;QACAC,OAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAO,OAAA;UACAN,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAnB,KAAA,CAAAW,UAAA,CAAAC,SAAA,CAAAW,IAAA,CACA,UAAAC,CAAA;cAAA,OAAAA,CAAA,CAAAX,GAAA;YAAA,CACA,EAAAS,OAAA;YACAtB,KAAA,CAAAK,QAAA,CAAAI,qBAAA;YACAlB,iCAAA,CAAA4B,CAAA,EAAAM,IAAA,WAAAC,GAAA;cACA1B,KAAA,CAAAW,UAAA,CAAAC,SAAA,CAAAW,IAAA,CACA,UAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAAX,GAAA;cAAA,CACA,EAAAS,OAAA,GAAAI,GAAA,CAAAC,IAAA,CAAAC,GAAA,WAAAJ,CAAA;gBACA;kBACAV,KAAA,EAAAU,CAAA,CAAAlB,YAAA;kBACAuB,KAAA,EAAAL,CAAA,CAAAM;gBACA;cACA;YACA;UACA;QACA,GACA;UACAjB,GAAA;UACAC,KAAA;UACAC,IAAA;UACAO,OAAA;UACAN,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAO,OAAA;UACAN,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAO,OAAA,GACA;YACAR,KAAA;YACAe,KAAA;UACA,GACA;YACAf,KAAA;YACAe,KAAA;UACA,GACA;YACAf,KAAA;YACAe,KAAA;UACA,EACA;UACAb,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,EACA;QACAY,KAAA;QACAC,iBAAA;UACAC,UAAA;UACAC,UAAA;UACAC,UAAA;UACAC,SAAA;UACAC,SAAA;QACA;MACA;MACA;MACAC,gBAAA;MACA;MACAC,YAAA;MACA;MACAC,UAAA;MACAC,iBAAA;MACAC,mBAAA;MACAC,YAAA;QACAC,QAAA;QACA9B,KAAA;QACAe,KAAA;MACA;MACAgB,mBAAA;MACAC,YAAA,GACA;QACAC,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAA3B,IAAA;YAAA,OAAA4B,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACA1D,IAAA;QACA2D,IAAA;UAAAC,KAAA;QAAA;MACA;IAEA;EACA;EACAC,SAAA,WAAAA,UAAA;EACAC,aAAA,WAAAA,cAAA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,KAAAC,eAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,aAAA;IAEArE,aAAA,eAAA+B,IAAA,WAAAC,GAAA;MACA,IAAAsC,IAAA,GAAAJ,MAAA,CAAAjD,UAAA,CAAAC,SAAA,CAAAW,IAAA,CACA,UAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAX,GAAA;MAAA,CACA;MACAmD,IAAA,CAAA1C,OAAA,GAAAI,GAAA,CAAAE,GAAA,WAAAJ,CAAA;QACA;UACAV,KAAA,EAAAU,CAAA,CAAAlB,YAAA;UACAuB,KAAA,EAAAL,CAAA,CAAAM;QACA;MACA;IACA;IAEArC,6BAAA;MACAwE,cAAA;IACA,GAAAxC,IAAA,WAAAC,GAAA;MACA,IAAAsC,IAAA,GAAAJ,MAAA,CAAAjD,UAAA,CAAAC,SAAA,CAAAW,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAX,GAAA;MAAA;MACAmD,IAAA,CAAA1C,OAAA,GAAAI,GAAA,CAAAC,IAAA,CAAAC,GAAA,WAAAJ,CAAA;QACA;UACAV,KAAA,EAAAU,CAAA,CAAAlB,YAAA;UACAuB,KAAA,EAAAL,CAAA,CAAA0C;QACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAzE,IAAA;MACA,YAAAK,SAAA,CAAAsB,IAAA,WAAAyC,IAAA;QAAA,OAAAA,IAAA,CAAA9D,IAAA,IAAAN,IAAA;MAAA;IACA;IACA0E,eAAA,WAAAA,gBAAAvE,IAAA;MACAA,IAAA,CAAAwE,GAAA;MACAxE,IAAA,CAAAyE,aAAA,QAAAxB,MAAA,CAAApD,IAAA;MACA,KAAA6E,OAAA,CAAAC,IAAA;QACA9E,IAAA;QACA+E,KAAA;UAAAC,WAAA,OAAA5B,MAAA,CAAApD,IAAA;UAAAkC,EAAA,EAAA/B,IAAA,CAAA+B;QAAA;MACA;MACA,KAAA+C,MAAA,CAAAC,QAAA,wBAAA/E,IAAA;IACA;IAEA8D,eAAA,WAAAA,gBAAA;MAAA,IAAAkB,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAzD,GAAA;QAAA,OAAAuD,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACApG,eAAA;YAAA;cAAAsC,GAAA,GAAA4D,QAAA,CAAAG,IAAA;cACA,IAAA/D,GAAA,CAAAgE,SAAA;gBACAtE,OAAA,CAAAC,GAAA,CAAAK,GAAA;gBACAqD,MAAA,CAAAzC,gBAAA,GAAAZ,GAAA,CAAAC,IAAA,CAAAC,GAAA,WAAAoC,IAAA;kBAAA,OAAA2B,aAAA,CAAAA,aAAA,KACA3B,IAAA;oBACA4B,OAAA,GAAA5B,IAAA,CAAAE,KAAA,GAAAxC,GAAA,CAAAC,IAAA,IAAAuC,KAAA,QAAA2B,OAAA;kBAAA;gBAAA,CACA;cACA;gBACAd,MAAA,CAAAe,QAAA;kBACAC,OAAA,EAAArE,GAAA,CAAAsE,OAAA;kBACAjF,IAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAuE,QAAA,CAAAW,IAAA;UAAA;QAAA,GAAAd,OAAA;MAAA;IACA;IACAe,cAAA,WAAAA,eAAAtG,IAAA;MACA,SAAA0C,gBAAA,CAAA6D,MAAA;QACA,YAAA7D,gBAAA,CAAAf,IAAA,WAAAyC,IAAA;UAAA,OAAAA,IAAA,CAAAoC,KAAA,IAAAxG,IAAA;QAAA,GAAAsE,KAAA;MACA;MACA;IACA;IACAmC,qBAAA,WAAAA,sBAAAzG,IAAA;MACA,SAAA0C,gBAAA,CAAA6D,MAAA;QACA,YAAA7D,gBAAA,CAAAf,IAAA,WAAAyC,IAAA;UAAA,OAAAA,IAAA,CAAAoC,KAAA,IAAAxG,IAAA;QAAA,GAAAgG,OAAA;MACA;MACA;IACA;IAEA9B,kBAAA,WAAAA,mBAAA;MAAA,IAAAwC,MAAA;MAAA,OAAAtB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAqB,SAAA;QAAA,IAAA7E,GAAA;QAAA,OAAAuD,mBAAA,GAAAG,IAAA,UAAAoB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlB,IAAA,GAAAkB,SAAA,CAAAjB,IAAA;YAAA;cACAc,MAAA,CAAA5D,mBAAA;cAAA+D,SAAA,CAAAjB,IAAA;cAAA,OACAlG,kBAAA;YAAA;cAAAoC,GAAA,GAAA+E,SAAA,CAAAhB,IAAA;cACA,IAAA/D,GAAA,CAAAgE,SAAA;gBACAY,MAAA,CAAA5D,mBAAA;gBACA4D,MAAA,CAAAzD,mBAAA,GAAAnB,GAAA,CAAAC,IAAA,CAAAC,GAAA,WAAAJ,CAAA;kBAAA,OAAAA,CAAA,CAAAM,EAAA;gBAAA;gBACAwE,MAAA,CAAA/D,YAAA,GAAAb,GAAA,CAAAC,IAAA;cACA;gBACA2E,MAAA,CAAAR,QAAA;kBACAC,OAAA,EAAArE,GAAA,CAAAsE,OAAA;kBACAjF,IAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA0F,SAAA,CAAAR,IAAA;UAAA;QAAA,GAAAM,QAAA;MAAA;IACA;IAEAxC,aAAA,WAAAA,cAAA;MAAA,IAAA2C,MAAA;MAAA,OAAA1B,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAyB,SAAA;QAAA,IAAAjF,GAAA;QAAA,OAAAuD,mBAAA,GAAAG,IAAA,UAAAwB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtB,IAAA,GAAAsB,SAAA,CAAArB,IAAA;YAAA;cACAkB,MAAA,CAAAlE,UAAA;cACA;cAAAqE,SAAA,CAAArB,IAAA;cAAA,OACAnG,aAAA,CAAAsG,aAAA,KACAe,MAAA,CAAArG,QAAA,CACA;YAAA;cAFAqB,GAAA,GAAAmF,SAAA,CAAApB,IAAA;cAGA,IAAA/D,GAAA,CAAAgE,SAAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACAgB,MAAA,CAAAlE,UAAA,GAAAd,GAAA,CAAAC,IAAA,CAAAC,GAAA,WAAAoC,IAAA;kBACA,IAAAA,IAAA,CAAA8C,GAAA;oBACA,OAAAnB,aAAA,CAAAA,aAAA,KACA3B,IAAA;sBACA+C,SAAA;oBAAA;kBAEA;oBACA,OAAApB,aAAA,CAAAA,aAAA,KACA3B,IAAA;sBACA+C,SAAA;sBACAD,GAAA,EAAA7H;oBAAA;kBAEA;gBACA;gBACAyH,MAAA,CAAAjE,iBAAA;cACA;gBACAiE,MAAA,CAAAZ,QAAA;kBACAC,OAAA,EAAArE,GAAA,CAAAsE,OAAA;kBACAjF,IAAA;gBACA;gBACA2F,MAAA,CAAAjE,iBAAA;cACA;YAAA;YAAA;cAAA,OAAAoE,SAAA,CAAAZ,IAAA;UAAA;QAAA,GAAAU,QAAA;MAAA;IACA;IACAK,UAAA,WAAAA,WAAA;MACA,KAAAjD,aAAA;IACA;IACAkD,SAAA,WAAAA,UAAA;MACA,KAAAlD,aAAA;IACA;IAEAmD,eAAA,WAAAA,gBAAAnH,IAAA,EAAAoH,IAAA;MAAA,IAAAC,MAAA;MAAA,OAAApC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAmC,SAAA;QAAA,IAAAC,OAAA,EAAAC,OAAA;QAAA,OAAAtC,mBAAA,GAAAG,IAAA,UAAAoC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlC,IAAA,GAAAkC,SAAA,CAAAjC,IAAA;YAAA;cAAAiC,SAAA,CAAAjC,IAAA;cAAA,OACA4B,MAAA,CAAAM,aAAA,CAAAN,MAAA,CAAA7E,YAAA,EAAAxC,IAAA,CAAA+B,EAAA;YAAA;cAAAwF,OAAA,GAAAG,SAAA,CAAAhC,IAAA;cACA8B,OAAA,MAAAI,MAAA,CAAAC,kBAAA,CACAN,OAAA,IACA;gBACAxF,EAAA,EAAA/B,IAAA,CAAA+B,EAAA;gBACA+F,IAAA,EAAA9H,IAAA,CAAA8H;cACA;cAEAT,MAAA,CAAA/G,QAAA,CAAAK,OAAA,GAAA6G,OAAA,CAAA3F,GAAA,WAAAJ,CAAA;gBAAA,OAAAA,CAAA,CAAAqG,IAAA;cAAA,GAAAC,IAAA;cACAV,MAAA,CAAArD,aAAA;YAAA;YAAA;cAAA,OAAA0D,SAAA,CAAAxB,IAAA;UAAA;QAAA,GAAAoB,QAAA;MAAA;IACA;IACAK,aAAA,WAAAA,cAAAK,IAAA,EAAAC,QAAA;MACA,IAAAC,WAAA;;MAEA;MACA,SAAAC,iBAAAC,EAAA,EAAAvI,IAAA,EAAAwI,KAAA;QACA,OAAAA,KAAA,CAAAC,IAAA,WAAAlB,IAAA;UAAA,OAAAA,IAAA,CAAArF,EAAA,KAAAqG,EAAA,IAAAhB,IAAA,CAAAU,IAAA,KAAAjI,IAAA;QAAA;MACA;MAEA,SAAA0I,SAAAC,KAAA,EAAAC,QAAA,EAAAC,UAAA;QACA,KAAAF,KAAA;QAAA,IAAAG,SAAA,GAAAC,0BAAA,CACAJ,KAAA;UAAAK,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;YAAA,IAAA5B,IAAA,GAAAyB,KAAA,CAAA/G,KAAA;YACA,IAAAsF,IAAA,CAAArF,EAAA,KAAAkG,QAAA;cACA;cACA,IACAQ,QAAA,WACA,CAAAN,gBAAA,CAAAM,QAAA,EAAAC,UAAA,EAAAR,WAAA,GACA;gBACAA,WAAA,CAAAvD,IAAA;kBAAA5C,EAAA,EAAA0G,QAAA;kBAAAX,IAAA,EAAAY;gBAAA;cACA;cACA;YACA;;YAEA;YACA,IAAAtB,IAAA,CAAA6B,QAAA,IAAAV,QAAA,CAAAnB,IAAA,CAAA6B,QAAA,EAAA7B,IAAA,CAAArF,EAAA,EAAAqF,IAAA,CAAAU,IAAA;cACA;cACA,KAAAK,gBAAA,CAAAf,IAAA,CAAArF,EAAA,EAAAqF,IAAA,CAAAU,IAAA,EAAAI,WAAA;gBACAA,WAAA,CAAAvD,IAAA;kBAAA5C,EAAA,EAAAqF,IAAA,CAAArF,EAAA;kBAAA+F,IAAA,EAAAV,IAAA,CAAAU;gBAAA;cACA;cACA;YACA;UACA;QAAA,SAAAoB,GAAA;UAAAP,SAAA,CAAAvH,CAAA,CAAA8H,GAAA;QAAA;UAAAP,SAAA,CAAAQ,CAAA;QAAA;QACA;MACA;MACA;MACAZ,QAAA,CAAAP,IAAA;MACA;MACAE,WAAA,CAAAkB,OAAA,GAAAzE,IAAA;MACA,OAAAuD,WAAA;IACA;EACA;AACA", "ignoreList": []}]}