{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\taskSet\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\taskSet\\index.vue", "mtime": 1755674552424}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "DialogForm", "GetTaskConfigPageList", "EnableMobileMessageNotice", "EnableSiteNotice", "EnableTaskConfig", "GetNoticeDropDownOption", "name", "components", "data", "_this", "currentComponent", "componentsConfig", "Data", "componentsFuns", "open", "dialogVisible", "close", "onFresh", "dialogTitle", "tableSelection", "ruleForm", "customForm", "formItems", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "text", "round", "plain", "circle", "loading", "disabled", "icon", "autofocus", "type", "size", "onclick", "item", "console", "log", "handleEnable", "handleClose", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "otherOptions", "align", "fixed", "label", "key", "width", "render", "row", "$createElement", "props", "value", "on", "change", "e", "handleSiteNoticeCloseEnable", "Id", "handleMobileMessageCloseEnable", "style", "color", "click", "handleEdit", "Roles", "join", "Users", "Enable", "handleCloseEnable", "tableData", "operateOptions", "tableActions", "watch", "handler", "newval", "oldval", "length", "for<PERSON>ach", "ele", "computed", "created", "init", "methods", "searchForm", "resetForm", "getNoticeDropDownOption", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "result", "noticeType", "noticeLevel", "wrap", "_callee$", "_context", "prev", "next", "sent", "IsSucceed", "element", "TypeName", "map", "Value", "Name", "stop", "_this2", "_callee2", "_callee2$", "_context2", "_objectSpread", "Page", "PageSize", "TotalCount", "$message", "error", "Message", "id", "status", "_this3", "_callee3", "_callee3$", "_context3", "Ids", "_this4", "_callee4", "_callee4$", "_context4", "_this5", "_callee5", "_callee5$", "_context5", "concat", "ID", "UserIds", "RoleIds", "_this6", "_callee6", "_callee6$", "_context6", "_this7", "_callee7", "_callee7$", "_context7", "handleSizeChange", "val", "handleCurrentChange", "handleSelectionChange", "selection"], "sources": ["src/views/business/eventManagement/taskSet/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout\r\n      :layoutConfig=\"{\r\n        isShowSearchForm: false,\r\n      }\"\r\n    >\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport DialogForm from \"./dialogForm.vue\";\r\n\r\nimport {\r\n  GetTaskConfigPageList,\r\n  EnableMobileMessageNotice,\r\n  EnableSiteNotice,\r\n  EnableTaskConfig,\r\n  GetNoticeDropDownOption,\r\n} from \"@/api/business/eventManagement\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        Data: {},\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"访客管理审核\",\r\n      tableSelection: [],\r\n      ruleForm: {},\r\n      customForm: {\r\n        formItems: [],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"启用\",\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: true, // 是否禁用\r\n              icon: \"\", //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleEnable();\r\n              },\r\n            },\r\n            {\r\n              text: \"关闭\",\r\n              type: \"danger\",\r\n              disabled: true, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleClose();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            otherOptions: {\r\n              type: \"selection\",\r\n              align: \"center\",\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"任务名称\",\r\n            key: \"Name\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"任务层级\",\r\n            key: \"SourceTypeDisplay\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"业务模块\",\r\n            key: \"Module\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"任务类型\",\r\n            key: \"TaskType\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"触发任务条件\",\r\n            key: \"TriggerConditionDescription\",\r\n            width: 180,\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"任务时长\",\r\n            key: \"Duration\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"单位\",\r\n            key: \"DurationUnitDisplay\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"规则描述\",\r\n            key: \"RuleDescription\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"站内通知\",\r\n            key: \"EnableSiteNotice\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement(\"el-switch\", {\r\n                props: {\r\n                  value: row.EnableSiteNotice,\r\n                  \"active-color\": \"#13ce66\",\r\n                },\r\n                on: {\r\n                  change: (e) => {\r\n                    this.handleSiteNoticeCloseEnable(row.Id, e);\r\n                  },\r\n                },\r\n              });\r\n            },\r\n          },\r\n          {\r\n            label: \"短信通知\",\r\n            key: \"EnableMobileMessageNotice\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement(\"el-switch\", {\r\n                props: {\r\n                  value: row.EnableMobileMessageNotice,\r\n                  \"active-color\": \"#13ce66\",\r\n                },\r\n                on: {\r\n                  change: (e) => {\r\n                    this.handleMobileMessageCloseEnable(row.Id, e);\r\n                  },\r\n                },\r\n              });\r\n            },\r\n          },\r\n          {\r\n            label: \"指派角色\",\r\n            key: \"Roles\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement(\r\n                \"span\",\r\n                {\r\n                  style: {\r\n                    color: \"#3582fb\",\r\n                  },\r\n                  on: {\r\n                    click: () => {\r\n                      this.handleEdit(row, \"role\");\r\n                    },\r\n                  },\r\n                },\r\n                row.Roles.join(\",\") || \"添加\"\r\n              );\r\n            },\r\n          },\r\n          {\r\n            label: \"指派人\",\r\n            key: \"Users\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement(\r\n                \"span\",\r\n                {\r\n                  style: {\r\n                    color: \"#3582fb\",\r\n                  },\r\n                  on: {\r\n                    click: () => {\r\n                      this.handleEdit(row, \"user\");\r\n                    },\r\n                  },\r\n                },\r\n                row.Users.join(\",\") || \"添加\"\r\n              );\r\n            },\r\n          },\r\n          {\r\n            label: \"执行周期\",\r\n            key: \"InformCycleDisplay\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"上次执行时间\",\r\n            key: \"LastInformTime\",\r\n            width: 180,\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"启用状态\",\r\n            key: \"Enable\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement(\"el-switch\", {\r\n                props: {\r\n                  value: row.Enable,\r\n                  \"active-color\": \"#13ce66\",\r\n                },\r\n                on: {\r\n                  change: (e) => {\r\n                    this.handleCloseEnable(row.Id, e);\r\n                  },\r\n                },\r\n              });\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: \"center\",\r\n        },\r\n        tableActions: [],\r\n      },\r\n    };\r\n  },\r\n  watch: {\r\n    tableSelection: {\r\n      handler(newval, oldval) {\r\n        console.log(newval);\r\n        if (newval.length > 0) {\r\n          this.customTableConfig.buttonConfig.buttonList.forEach((ele) => {\r\n            ele.disabled = false;\r\n          });\r\n        } else {\r\n          this.customTableConfig.buttonConfig.buttonList.forEach((ele) => {\r\n            ele.disabled = true;\r\n          });\r\n        }\r\n      },\r\n    },\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init();\r\n  },\r\n  // mixins: [getGridByCode],\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1;\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.GetTaskConfigPageList();\r\n    },\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.GetTaskConfigPageList();\r\n      this.getNoticeDropDownOption();\r\n    },\r\n    async getNoticeDropDownOption() {\r\n      const res = await GetNoticeDropDownOption({});\r\n      if (res.IsSucceed) {\r\n        let result = res.Data || [];\r\n        let noticeType = [];\r\n        let noticeLevel = [];\r\n        result.forEach((element) => {\r\n          if (element.TypeName == \"通知类型\") {\r\n            noticeType = element.Data.map((item) => ({\r\n              value: item.Value,\r\n              label: item.Name,\r\n            }));\r\n          } else if (element.TypeName == \"发布层级\") {\r\n            noticeLevel = element.Data.map((item) => ({\r\n              value: item.Value,\r\n              label: item.Name,\r\n            }));\r\n          }\r\n        });\r\n        // this.customForm.formItems.find((item) => item.key == \"Type\").options =\r\n        //   noticeType;\r\n      }\r\n    },\r\n    async GetTaskConfigPageList() {\r\n      const res = await GetTaskConfigPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n        if (this.customTableConfig.tableData.length > 0) {\r\n        }\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n\r\n    async handleMobileMessageCloseEnable(id, status) {\r\n      const res = await EnableMobileMessageNotice({\r\n        Ids: [id],\r\n        Enable: status,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        this.init();\r\n      }\r\n    },\r\n\r\n    async handleSiteNoticeCloseEnable(id, status) {\r\n      const res = await EnableSiteNotice({\r\n        Ids: [id],\r\n        Enable: status,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        this.init();\r\n      }\r\n    },\r\n\r\n    async handleCloseEnable(id, status) {\r\n      const res = await EnableTaskConfig({\r\n        Ids: [id],\r\n        Enable: status,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        this.init();\r\n      }\r\n    },\r\n    handleEdit(row, type) {\r\n      console.log(row, \"row\");\r\n      this.dialogTitle = `访客管理审核（${row.Name}）`;\r\n      this.dialogVisible = true;\r\n      this.componentsConfig = {\r\n        ID: row.Id,\r\n        type,\r\n        UserIds: row.UserIds,\r\n        Users: row.Users,\r\n        RoleIds: row.RoleIds,\r\n        Roles: row.Roles,\r\n      };\r\n    },\r\n    async handleEnable() {\r\n      const res = await EnableTaskConfig({\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n        Enable: true,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        this.init();\r\n      }\r\n    },\r\n\r\n    async handleClose() {\r\n      const res = await EnableTaskConfig({\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n        Enable: false,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        this.init();\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.GetTaskConfigPageList();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.GetTaskConfigPageList();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: 100%;\r\n  width: 100%;\r\n  position: absolute;\r\n  ::v-deep {\r\n    .CustomLayout {\r\n      .layoutTable {\r\n        height: 0;\r\n        .CustomTable {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          .table {\r\n            flex: 1;\r\n            height: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            .el-table {\r\n              flex: 1;\r\n              height: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AAEA,SACAC,qBAAA,IAAAA,sBAAA,EACAC,yBAAA,EACAC,gBAAA,EACAC,gBAAA,EACAC,uBAAA,QACA;AACA;EACAC,IAAA;EACAC,UAAA;IACAT,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAW,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,gBAAA,EAAAV,UAAA;MACAW,gBAAA;QACAC,IAAA;MACA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAL,KAAA,CAAAM,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAP,KAAA,CAAAM,aAAA;UACAN,KAAA,CAAAQ,OAAA;QACA;MACA;MACAF,aAAA;MACAG,WAAA;MACAC,cAAA;MACAC,QAAA;MACAC,UAAA;QACAC,SAAA;QACAC,KAAA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAC,IAAA;YACAC,KAAA;YAAA;YACAC,KAAA;YAAA;YACAC,MAAA;YAAA;YACAC,OAAA;YAAA;YACAC,QAAA;YAAA;YACAC,IAAA;YAAA;YACAC,SAAA;YAAA;YACAC,IAAA;YAAA;YACAC,IAAA;YAAA;YACAC,OAAA,WAAAA,QAAAC,IAAA;cACAC,OAAA,CAAAC,GAAA,CAAAF,IAAA;cACAhC,KAAA,CAAAmC,YAAA;YACA;UACA,GACA;YACAd,IAAA;YACAQ,IAAA;YACAH,QAAA;YAAA;YACAK,OAAA,WAAAA,QAAAC,IAAA;cACAC,OAAA,CAAAC,GAAA,CAAAF,IAAA;cACAhC,KAAA,CAAAoC,WAAA;YACA;UACA;QAEA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA,GACA;UACAC,YAAA;YACAb,IAAA;YACAc,KAAA;YACAC,KAAA;UACA;QACA,GACA;UACAC,KAAA;UACAC,GAAA;UACAJ,YAAA;YACAC,KAAA;UACA;QACA,GACA;UACAE,KAAA;UACAC,GAAA;UACAJ,YAAA;YACAC,KAAA;UACA;QACA,GACA;UACAE,KAAA;UACAC,GAAA;UACAJ,YAAA;YACAC,KAAA;UACA;QACA,GACA;UACAE,KAAA;UACAC,GAAA;UACAJ,YAAA;YACAC,KAAA;UACA;QACA,GACA;UACAE,KAAA;UACAC,GAAA;UACAC,KAAA;UACAL,YAAA;YACAC,KAAA;UACA;QACA,GACA;UACAE,KAAA;UACAC,GAAA;UACAJ,YAAA;YACAC,KAAA;UACA;QACA,GACA;UACAE,KAAA;UACAC,GAAA;UACAJ,YAAA;YACAC,KAAA;UACA;QACA,GACA;UACAE,KAAA;UACAC,GAAA;UACAJ,YAAA;YACAC,KAAA;UACA;QACA,GACA;UACAE,KAAA;UACAC,GAAA;UACAJ,YAAA;YACAC,KAAA;UACA;UACAK,MAAA,WAAAA,OAAAC,GAAA;YACA,OAAAjD,KAAA,CAAAkD,cAAA;cACAC,KAAA;gBACAC,KAAA,EAAAH,GAAA,CAAAvD,gBAAA;gBACA;cACA;cACA2D,EAAA;gBACAC,MAAA,WAAAA,OAAAC,CAAA;kBACAvD,KAAA,CAAAwD,2BAAA,CAAAP,GAAA,CAAAQ,EAAA,EAAAF,CAAA;gBACA;cACA;YACA;UACA;QACA,GACA;UACAV,KAAA;UACAC,GAAA;UACAJ,YAAA;YACAC,KAAA;UACA;UACAK,MAAA,WAAAA,OAAAC,GAAA;YACA,OAAAjD,KAAA,CAAAkD,cAAA;cACAC,KAAA;gBACAC,KAAA,EAAAH,GAAA,CAAAxD,yBAAA;gBACA;cACA;cACA4D,EAAA;gBACAC,MAAA,WAAAA,OAAAC,CAAA;kBACAvD,KAAA,CAAA0D,8BAAA,CAAAT,GAAA,CAAAQ,EAAA,EAAAF,CAAA;gBACA;cACA;YACA;UACA;QACA,GACA;UACAV,KAAA;UACAC,GAAA;UACAJ,YAAA;YACAC,KAAA;UACA;UACAK,MAAA,WAAAA,OAAAC,GAAA;YACA,OAAAjD,KAAA,CAAAkD,cAAA,CACA,QACA;cACAS,KAAA;gBACAC,KAAA;cACA;cACAP,EAAA;gBACAQ,KAAA,WAAAA,MAAA;kBACA7D,KAAA,CAAA8D,UAAA,CAAAb,GAAA;gBACA;cACA;YACA,GACAA,GAAA,CAAAc,KAAA,CAAAC,IAAA,aACA;UACA;QACA,GACA;UACAnB,KAAA;UACAC,GAAA;UACAJ,YAAA;YACAC,KAAA;UACA;UACAK,MAAA,WAAAA,OAAAC,GAAA;YACA,OAAAjD,KAAA,CAAAkD,cAAA,CACA,QACA;cACAS,KAAA;gBACAC,KAAA;cACA;cACAP,EAAA;gBACAQ,KAAA,WAAAA,MAAA;kBACA7D,KAAA,CAAA8D,UAAA,CAAAb,GAAA;gBACA;cACA;YACA,GACAA,GAAA,CAAAgB,KAAA,CAAAD,IAAA,aACA;UACA;QACA,GACA;UACAnB,KAAA;UACAC,GAAA;UACAJ,YAAA;YACAC,KAAA;UACA;QACA,GACA;UACAE,KAAA;UACAC,GAAA;UACAC,KAAA;UACAL,YAAA;YACAC,KAAA;UACA;QACA,GACA;UACAE,KAAA;UACAC,GAAA;UACAJ,YAAA;YACAC,KAAA;UACA;UACAK,MAAA,WAAAA,OAAAC,GAAA;YACA,OAAAjD,KAAA,CAAAkD,cAAA;cACAC,KAAA;gBACAC,KAAA,EAAAH,GAAA,CAAAiB,MAAA;gBACA;cACA;cACAb,EAAA;gBACAC,MAAA,WAAAA,OAAAC,CAAA;kBACAvD,KAAA,CAAAmE,iBAAA,CAAAlB,GAAA,CAAAQ,EAAA,EAAAF,CAAA;gBACA;cACA;YACA;UACA;QACA,EACA;QACAa,SAAA;QACAC,cAAA;UACA1B,KAAA;QACA;QACA2B,YAAA;MACA;IACA;EACA;EACAC,KAAA;IACA7D,cAAA;MACA8D,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACAzC,OAAA,CAAAC,GAAA,CAAAuC,MAAA;QACA,IAAAA,MAAA,CAAAE,MAAA;UACA,KAAAzD,iBAAA,CAAAC,YAAA,CAAAC,UAAA,CAAAwD,OAAA,WAAAC,GAAA;YACAA,GAAA,CAAAnD,QAAA;UACA;QACA;UACA,KAAAR,iBAAA,CAAAC,YAAA,CAAAC,UAAA,CAAAwD,OAAA,WAAAC,GAAA;YACAA,GAAA,CAAAnD,QAAA;UACA;QACA;MACA;IACA;EACA;EACAoD,QAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAAnF,IAAA;MACAkC,OAAA,CAAAC,GAAA,CAAAnC,IAAA;MACA,KAAAmB,iBAAA,CAAAoB,WAAA;MACA,KAAA9B,OAAA;IACA;IACA2E,SAAA,WAAAA,UAAA;MACA,KAAA3E,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAAhB,qBAAA;IACA;IACAwF,IAAA,WAAAA,KAAA;MACA;MACA,KAAAxF,qBAAA;MACA,KAAA4F,uBAAA;IACA;IACAA,uBAAA,WAAAA,wBAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA,EAAAC,MAAA,EAAAC,UAAA,EAAAC,WAAA;QAAA,OAAAN,mBAAA,GAAAO,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACArG,uBAAA;YAAA;cAAA6F,GAAA,GAAAM,QAAA,CAAAG,IAAA;cACA,IAAAT,GAAA,CAAAU,SAAA;gBACAT,MAAA,GAAAD,GAAA,CAAAtF,IAAA;gBACAwF,UAAA;gBACAC,WAAA;gBACAF,MAAA,CAAAd,OAAA,WAAAwB,OAAA;kBACA,IAAAA,OAAA,CAAAC,QAAA;oBACAV,UAAA,GAAAS,OAAA,CAAAjG,IAAA,CAAAmG,GAAA,WAAAtE,IAAA;sBAAA;wBACAoB,KAAA,EAAApB,IAAA,CAAAuE,KAAA;wBACA1D,KAAA,EAAAb,IAAA,CAAAwE;sBACA;oBAAA;kBACA,WAAAJ,OAAA,CAAAC,QAAA;oBACAT,WAAA,GAAAQ,OAAA,CAAAjG,IAAA,CAAAmG,GAAA,WAAAtE,IAAA;sBAAA;wBACAoB,KAAA,EAAApB,IAAA,CAAAuE,KAAA;wBACA1D,KAAA,EAAAb,IAAA,CAAAwE;sBACA;oBAAA;kBACA;gBACA;gBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAT,QAAA,CAAAU,IAAA;UAAA;QAAA,GAAAjB,OAAA;MAAA;IACA;IACAhG,qBAAA,WAAAA,sBAAA;MAAA,IAAAkH,MAAA;MAAA,OAAArB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAoB,SAAA;QAAA,IAAAlB,GAAA;QAAA,OAAAH,mBAAA,GAAAO,IAAA,UAAAe,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAb,IAAA,GAAAa,SAAA,CAAAZ,IAAA;YAAA;cAAAY,SAAA,CAAAZ,IAAA;cAAA,OACAzG,sBAAA,CAAAsH,aAAA;gBACAC,IAAA,EAAAL,MAAA,CAAAxF,iBAAA,CAAAoB,WAAA;gBACA0E,QAAA,EAAAN,MAAA,CAAAxF,iBAAA,CAAAqB;cAAA,GACAmE,MAAA,CAAA/F,QAAA,CACA;YAAA;cAJA8E,GAAA,GAAAoB,SAAA,CAAAX,IAAA;cAKA,IAAAT,GAAA,CAAAU,SAAA;gBACAO,MAAA,CAAAxF,iBAAA,CAAAkD,SAAA,GAAAqB,GAAA,CAAAtF,IAAA,CAAAA,IAAA;gBACAuG,MAAA,CAAAxF,iBAAA,CAAAsB,KAAA,GAAAiD,GAAA,CAAAtF,IAAA,CAAA8G,UAAA;gBACA,IAAAP,MAAA,CAAAxF,iBAAA,CAAAkD,SAAA,CAAAO,MAAA,OACA;cACA;gBACA+B,MAAA,CAAAQ,QAAA,CAAAC,KAAA,CAAA1B,GAAA,CAAA2B,OAAA;cACA;YAAA;YAAA;cAAA,OAAAP,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IAEAjD,8BAAA,WAAAA,+BAAA2D,EAAA,EAAAC,MAAA;MAAA,IAAAC,MAAA;MAAA,OAAAlC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAiC,SAAA;QAAA,IAAA/B,GAAA;QAAA,OAAAH,mBAAA,GAAAO,IAAA,UAAA4B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1B,IAAA,GAAA0B,SAAA,CAAAzB,IAAA;YAAA;cAAAyB,SAAA,CAAAzB,IAAA;cAAA,OACAxG,yBAAA;gBACAkI,GAAA,GAAAN,EAAA;gBACAnD,MAAA,EAAAoD;cACA;YAAA;cAHA7B,GAAA,GAAAiC,SAAA,CAAAxB,IAAA;cAIA,IAAAT,GAAA,CAAAU,SAAA;gBACAlE,OAAA,CAAAC,GAAA,CAAAuD,GAAA;gBACA8B,MAAA,CAAAvC,IAAA;cACA;YAAA;YAAA;cAAA,OAAA0C,SAAA,CAAAjB,IAAA;UAAA;QAAA,GAAAe,QAAA;MAAA;IACA;IAEAhE,2BAAA,WAAAA,4BAAA6D,EAAA,EAAAC,MAAA;MAAA,IAAAM,MAAA;MAAA,OAAAvC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAsC,SAAA;QAAA,IAAApC,GAAA;QAAA,OAAAH,mBAAA,GAAAO,IAAA,UAAAiC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/B,IAAA,GAAA+B,SAAA,CAAA9B,IAAA;YAAA;cAAA8B,SAAA,CAAA9B,IAAA;cAAA,OACAvG,gBAAA;gBACAiI,GAAA,GAAAN,EAAA;gBACAnD,MAAA,EAAAoD;cACA;YAAA;cAHA7B,GAAA,GAAAsC,SAAA,CAAA7B,IAAA;cAIA,IAAAT,GAAA,CAAAU,SAAA;gBACAlE,OAAA,CAAAC,GAAA,CAAAuD,GAAA;gBACAmC,MAAA,CAAA5C,IAAA;cACA;YAAA;YAAA;cAAA,OAAA+C,SAAA,CAAAtB,IAAA;UAAA;QAAA,GAAAoB,QAAA;MAAA;IACA;IAEA1D,iBAAA,WAAAA,kBAAAkD,EAAA,EAAAC,MAAA;MAAA,IAAAU,MAAA;MAAA,OAAA3C,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA0C,SAAA;QAAA,IAAAxC,GAAA;QAAA,OAAAH,mBAAA,GAAAO,IAAA,UAAAqC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnC,IAAA,GAAAmC,SAAA,CAAAlC,IAAA;YAAA;cAAAkC,SAAA,CAAAlC,IAAA;cAAA,OACAtG,gBAAA;gBACAgI,GAAA,GAAAN,EAAA;gBACAnD,MAAA,EAAAoD;cACA;YAAA;cAHA7B,GAAA,GAAA0C,SAAA,CAAAjC,IAAA;cAIA,IAAAT,GAAA,CAAAU,SAAA;gBACAlE,OAAA,CAAAC,GAAA,CAAAuD,GAAA;gBACAuC,MAAA,CAAAhD,IAAA;cACA;YAAA;YAAA;cAAA,OAAAmD,SAAA,CAAA1B,IAAA;UAAA;QAAA,GAAAwB,QAAA;MAAA;IACA;IACAnE,UAAA,WAAAA,WAAAb,GAAA,EAAApB,IAAA;MACAI,OAAA,CAAAC,GAAA,CAAAe,GAAA;MACA,KAAAxC,WAAA,gDAAA2H,MAAA,CAAAnF,GAAA,CAAAuD,IAAA;MACA,KAAAlG,aAAA;MACA,KAAAJ,gBAAA;QACAmI,EAAA,EAAApF,GAAA,CAAAQ,EAAA;QACA5B,IAAA,EAAAA,IAAA;QACAyG,OAAA,EAAArF,GAAA,CAAAqF,OAAA;QACArE,KAAA,EAAAhB,GAAA,CAAAgB,KAAA;QACAsE,OAAA,EAAAtF,GAAA,CAAAsF,OAAA;QACAxE,KAAA,EAAAd,GAAA,CAAAc;MACA;IACA;IACA5B,YAAA,WAAAA,aAAA;MAAA,IAAAqG,MAAA;MAAA,OAAAnD,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAkD,SAAA;QAAA,IAAAhD,GAAA;QAAA,OAAAH,mBAAA,GAAAO,IAAA,UAAA6C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3C,IAAA,GAAA2C,SAAA,CAAA1C,IAAA;YAAA;cAAA0C,SAAA,CAAA1C,IAAA;cAAA,OACAtG,gBAAA;gBACAgI,GAAA,EAAAa,MAAA,CAAA9H,cAAA,CAAA4F,GAAA,WAAAtE,IAAA;kBAAA,OAAAA,IAAA,CAAAyB,EAAA;gBAAA;gBACAS,MAAA;cACA;YAAA;cAHAuB,GAAA,GAAAkD,SAAA,CAAAzC,IAAA;cAIA,IAAAT,GAAA,CAAAU,SAAA;gBACAlE,OAAA,CAAAC,GAAA,CAAAuD,GAAA;gBACA+C,MAAA,CAAAxD,IAAA;cACA;YAAA;YAAA;cAAA,OAAA2D,SAAA,CAAAlC,IAAA;UAAA;QAAA,GAAAgC,QAAA;MAAA;IACA;IAEArG,WAAA,WAAAA,YAAA;MAAA,IAAAwG,MAAA;MAAA,OAAAvD,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAsD,SAAA;QAAA,IAAApD,GAAA;QAAA,OAAAH,mBAAA,GAAAO,IAAA,UAAAiD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/C,IAAA,GAAA+C,SAAA,CAAA9C,IAAA;YAAA;cAAA8C,SAAA,CAAA9C,IAAA;cAAA,OACAtG,gBAAA;gBACAgI,GAAA,EAAAiB,MAAA,CAAAlI,cAAA,CAAA4F,GAAA,WAAAtE,IAAA;kBAAA,OAAAA,IAAA,CAAAyB,EAAA;gBAAA;gBACAS,MAAA;cACA;YAAA;cAHAuB,GAAA,GAAAsD,SAAA,CAAA7C,IAAA;cAIA,IAAAT,GAAA,CAAAU,SAAA;gBACAlE,OAAA,CAAAC,GAAA,CAAAuD,GAAA;gBACAmD,MAAA,CAAA5D,IAAA;cACA;YAAA;YAAA;cAAA,OAAA+D,SAAA,CAAAtC,IAAA;UAAA;QAAA,GAAAoC,QAAA;MAAA;IACA;IAEAG,gBAAA,WAAAA,iBAAAC,GAAA;MACAhH,OAAA,CAAAC,GAAA,iBAAAkG,MAAA,CAAAa,GAAA;MACA,KAAA/H,iBAAA,CAAAqB,QAAA,GAAA0G,GAAA;MACA,KAAAzJ,qBAAA;IACA;IACA0J,mBAAA,WAAAA,oBAAAD,GAAA;MACAhH,OAAA,CAAAC,GAAA,wBAAAkG,MAAA,CAAAa,GAAA;MACA,KAAA/H,iBAAA,CAAAoB,WAAA,GAAA2G,GAAA;MACA,KAAAzJ,qBAAA;IACA;IACA2J,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA1I,cAAA,GAAA0I,SAAA;IACA;EACA;AACA", "ignoreList": []}]}