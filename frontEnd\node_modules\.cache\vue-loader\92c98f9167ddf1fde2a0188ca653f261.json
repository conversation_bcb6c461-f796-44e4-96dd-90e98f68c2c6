{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\barrierEquipment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\barrierEquipment\\index.vue", "mtime": 1755506574527}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy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file": "index.vue", "sourceRoot": "src/views/business/vehicleBarrier/pJVehicleBarrier/barrierEquipment", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          label-width=\"130px\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"600px\"\r\n      @closed=\"closedDialog\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"dialogRef\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\n\r\nimport baseInfo from \"./dialog/baseInfo.vue\";\r\nimport importDialog from \"@/views/business/vehicleBarrier/components/import.vue\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/pJVehicleBarrier/mixins/export\";\r\n\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\nimport {\r\n  GetVBEquipList,\r\n  DelEquip,\r\n  ExportVBData,\r\n  GetDropList,\r\n  DownloadVBEquipTemplate,\r\n  ImportVBEquipDataStream,\r\n} from \"@/api/business/vehicleBarrier.js\";\r\nimport getAddress from \"./index.js\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nexport default {\r\n  Name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n    baseInfo,\r\n    importDialog,\r\n  },\r\n  mixins: [exportInfo, getAddress, addRouterPage],\r\n  data() {\r\n    return {\r\n      currentComponent: baseInfo,\r\n      componentsConfig: {\r\n        interfaceName: ImportVBEquipDataStream,\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"\",\r\n      tableSelection: [],\r\n      selectIds: [],\r\n      ruleForm: {\r\n        Name: \"\",\r\n        Brand: \"\",\r\n        Model: \"\",\r\n        Vender: \"\",\r\n        Status: \"\",\r\n        PurposeCatetory: \"\",\r\n        Scene: \"\",\r\n        Site: \"\",\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Name\", // 字段ID\r\n            label: \"设备名称\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            placeholder: \"请输入输入停车场名称\",\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n          },\r\n          {\r\n            key: \"Brand\",\r\n            label: \"品牌\",\r\n            type: \"select\",\r\n            options: [],\r\n            placeholder: \"请输入停车场地址\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"Model\",\r\n            label: \"规格型号\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"position\",\r\n            label: \"安装位置\",\r\n            type: \"cascader\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n              require: true,\r\n              disabled: false,\r\n              separator: \"-\",\r\n              props: {\r\n                label: \"Label\",\r\n                children: \"Children\",\r\n                value: \"Label\",\r\n              },\r\n            },\r\n            change: (e) => {\r\n              if (e.length > 0) {\r\n                this.ruleForm.PurposeCatetory = e[0];\r\n                this.ruleForm.Scene = e[1];\r\n                this.ruleForm.Site = e[2];\r\n              } else {\r\n                this.ruleForm.PurposeCatetory = \"\";\r\n                this.ruleForm.Scene = \"\";\r\n                this.ruleForm.Site = \"\";\r\n              }\r\n            },\r\n          },\r\n          {\r\n            key: \"Vender\",\r\n            label: \"供应�?,\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"Status\",\r\n            label: \"状�?,\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"新增\",\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载�?\n              disabled: false, // 是否禁用\r\n              icon: \"\", //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n            {\r\n              text: \"下载模板\",\r\n              disabled: false, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData(\r\n                  [],\r\n                  \"道闸设备管理模板\",\r\n                  DownloadVBEquipTemplate\r\n                );\r\n              },\r\n            },\r\n            {\r\n              text: \"批量导入\",\r\n              disabled: false, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.currentComponent = \"importDialog\";\r\n                this.dialogVisible = true;\r\n                this.dialogTitle = \"批量导入\";\r\n              },\r\n            },\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData(this.ruleForm, \"道闸设备管理\", ExportVBData);\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: \"100%\",\r\n        tableActionsWidth: 220,\r\n        tableColumns: [\r\n          // {\r\n          //   width: 50,\r\n          //   otherOptions: {\r\n          //     type: 'selection',\r\n          //     align: 'center'\r\n          //   }\r\n          // },\r\n          {\r\n            label: \"设备名称\",\r\n            key: \"Name\",\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: \"设备编码\",\r\n            key: \"Code\",\r\n          },\r\n\r\n          {\r\n            label: \"品牌\",\r\n            key: \"Brand\",\r\n          },\r\n          {\r\n            label: \"规格型号\",\r\n            key: \"Model\",\r\n          },\r\n\r\n          {\r\n            label: \"所属出入口\",\r\n            key: \"EntranceName\",\r\n          },\r\n          {\r\n            label: \"安装位置\",\r\n            key: \"Address\",\r\n          },\r\n          {\r\n            label: \"供应�?,\r\n            key: \"Vender\",\r\n          },\r\n          {\r\n            label: \"供应商联系方�?,\r\n            key: \"VenderPhone\",\r\n          },\r\n          {\r\n            label: \"状�?,\r\n            key: \"StatusName\",\r\n          },\r\n          {\r\n            label: \"更新�?,\r\n            key: \"ModifyUserName\",\r\n          },\r\n          {\r\n            label: \"更新时间\",\r\n            key: \"ModifyDate\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false, // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, \"edit\");\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false, // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"查看详情\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.$router.push({\r\n                name: \"EquipmentView\",\r\n                query: { pg_redirect: this.$route.name, Id: row.Id },\r\n              });\r\n            },\r\n          },\r\n        ],\r\n        operateOptions: {\r\n          // width: 300 // 操作栏宽�?\n        },\r\n      },\r\n      allSelectOption: [],\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/view\",\r\n          hidden: true,\r\n          component: () => import(\"./dialog/view.vue\"),\r\n          meta: { title: \"道闸设备详情\" },\r\n          name: \"EquipmentView\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  computed: {},\r\n  async created() {\r\n    this.customForm.formItems.find((item) => item.key == \"position\").options =\r\n      await this.getAddress();\r\n    await this.getDropList();\r\n\r\n    this.init();\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.position = [];\r\n      this.ruleForm.PurposeCatetory = \"\";\r\n      this.ruleForm.Scene = \"\";\r\n      this.ruleForm.Site = \"\";\r\n      this.onFresh();\r\n      this.getDropList();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n      this.getDropList();\r\n    },\r\n    init() {\r\n      this.fetchData();\r\n      this.getDropList();\r\n    },\r\n    // 获取搜索下啦\r\n    async getDropList() {\r\n      await GetDropList({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data;\r\n          this.allSelectOption = data;\r\n        } else {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: res.Message,\r\n          });\r\n        }\r\n      });\r\n\r\n      this.customForm.formItems.find((item) => item.key === \"Brand\").options =\r\n        await this.handelOption(\"Brand\");\r\n      this.customForm.formItems.find((item) => item.key === \"Model\").options =\r\n        await this.handelOption(\"Model\");\r\n      this.customForm.formItems.find((item) => item.key === \"Vender\").options =\r\n        await this.handelOption(\"Vender\");\r\n      this.customForm.formItems.find((item) => item.key === \"Status\").options =\r\n        await this.handelOption(\"Status\");\r\n    },\r\n\r\n    async handelOption(key) {\r\n      return await this.allSelectOption\r\n        .find((item) => item.Name === key)\r\n        .List.map((i) => {\r\n          return {\r\n            label: i.Value,\r\n            value: i.Key,\r\n          };\r\n        });\r\n    },\r\n    async fetchData() {\r\n      const res = await GetVBEquipList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data || [];\r\n        this.customTableConfig.total = res.Data.Total;\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.currentComponent = \"baseInfo\";\r\n      this.dialogTitle = \"新增\";\r\n      this.dialogVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.add();\r\n      });\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row);\r\n      console.log(this);\r\n      this.$confirm(\"确认删除?\", {\r\n        type: \"warning\",\r\n      })\r\n        .then(async (_) => {\r\n          const res = await DelEquip({\r\n            Id: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: \"删除成功\",\r\n              type: \"success\",\r\n            });\r\n            this.init();\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: \"error\",\r\n            });\r\n          }\r\n        })\r\n        .catch((_) => {\r\n          this.$message({\r\n            type: \"info\",\r\n            message: \"已取消删�?,\r\n          });\r\n        });\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type);\r\n      this.currentComponent = \"baseInfo\";\r\n      if (type === \"view\") {\r\n        this.dialogTitle = \"查看\";\r\n      } else if (type === \"edit\") {\r\n        this.dialogTitle = \"编辑\";\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.init(index, row, type);\r\n      });\r\n\r\n      this.dialogVisible = true;\r\n    },\r\n    // 关闭弹窗\r\n    closedDialog() {\r\n      this.$refs.dialogRef.closeClearForm();\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.onFresh();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.onFresh();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      const Ids = [];\r\n      this.tableSelection = selection;\r\n      this.tableSelection.forEach((item) => {\r\n        Ids.push(item.Id);\r\n      });\r\n      console.log(Ids);\r\n      this.selectIds = Ids;\r\n      console.log(this.tableSelection);\r\n      if (this.tableSelection.length > 0) {\r\n        this.customTableConfig.buttonConfig.buttonList.find(\r\n          (v) => v.key == \"batch\"\r\n        ).disabled = false;\r\n      } else {\r\n        this.customTableConfig.buttonConfig.buttonList.find(\r\n          (v) => v.key == \"batch\"\r\n        ).disabled = true;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n::v-deep {\r\n  .el-dialog__body {\r\n    padding: 0px 20px 30px;\r\n  }\r\n}\r\n</style>\r\n"]}]}