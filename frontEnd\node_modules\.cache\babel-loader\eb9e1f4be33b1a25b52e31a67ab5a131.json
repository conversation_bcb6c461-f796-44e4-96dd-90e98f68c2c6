{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\intelligentControl\\components\\layoutCard.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\intelligentControl\\components\\layoutCard.vue", "mtime": 1755674552427}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBjb3VudFRvIGZyb20gJ3Z1ZS1jb3VudC10byc7CmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICBjb3VudFRvOiBjb3VudFRvCiAgfSwKICBwcm9wczogewogICAgY2FyZExpc3Q6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiBbXTsKICAgICAgfQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7fTsKICB9LAogIGNvbXB1dGVkOiB7fQp9Ow=="}, {"version": 3, "names": ["countTo", "components", "props", "cardList", "type", "Array", "default", "data", "computed"], "sources": ["src/views/business/intelligentControl/components/layoutCard.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div\r\n      class=\"card\"\r\n      v-for=\"(item, index) in cardList\"\r\n      :key=\"index\"\r\n      :style=\"{\r\n        backgroundColor: item.backgroundcolor,\r\n        height: '100px',\r\n        borderRadius: '8px',\r\n        margin: item.backgroundcolor ? '' : 'auto'\r\n      }\"\r\n    >\r\n      <div class=\"cardleft\" :style=\"item.backgroundcolor ? 'margin-right: 16px;' : ''\">\r\n        <img :src=\"item.cardimg\" />\r\n      </div>\r\n      <div class=\"cardright\">\r\n        <div class=\"title\">{{ item.cardtitle }}</div>\r\n        <div class=\"num\" :style=\"{ color: item.numcolor }\" v-if=\"item.cardtitle\">\r\n          <count-to\r\n            :startVal=\"0\"\r\n            :endVal=\"item.cardNum\"\r\n            :duration=\"50\"\r\n            :decimals=\"item.unit === '件' ? 0 : 3\"\r\n          ></count-to>\r\n          <span>{{ item.unit }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport countTo from 'vue-count-to'\r\nexport default {\r\n  components: { countTo },\r\n  props: {\r\n    cardList: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {}\r\n  },\r\n  computed: {}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: 130px;\r\n  font-family: PingFang SC, PingFang SC;\r\n  display: flex;\r\n  .card {\r\n    flex: 1;\r\n    float: left;\r\n    margin: 0px 12px; /* 添加间距 */\r\n    display: flex;\r\n    justify-content: center; /* 水平居中 */\r\n    align-items: center; /* 垂直居中 */\r\n    background-color: #ffffff; /* 设置背景颜色 */\r\n\r\n    .cardleft {\r\n      float: left;\r\n    }\r\n    .cardright {\r\n      float: left;\r\n      font-style: normal;\r\n      .title {\r\n        height: 25px;\r\n        font-weight: bold;\r\n        font-size: 1rem;\r\n        color: #333333;\r\n        line-height: 25px;\r\n      }\r\n      .num {\r\n        height: 33px;\r\n        font-weight: 600;\r\n        font-size: 1.2rem;\r\n        line-height: 33px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,OAAAA,OAAA;AACA;EACAC,UAAA;IAAAD,OAAA,EAAAA;EAAA;EACAE,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;EACA;EACAC,QAAA;AACA", "ignoreList": []}]}