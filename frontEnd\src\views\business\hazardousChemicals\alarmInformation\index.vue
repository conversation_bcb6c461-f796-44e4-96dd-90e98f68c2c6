<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
          ><template #customBtn="{ slotScope }"
            ><el-button
              v-if="slotScope.Handle_Status == 1"
              type="text"
              @click="handelClose(slotScope)"
              >关闭</el-button
            ></template
          ></CustomTable
        >
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";

import DialogForm from "./dialogForm.vue";
import DialogFormLook from "./dialogFormLook.vue";

import { downloadFile } from "@/utils/downloadFile";
// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'
// import CustomButton from '@/businessComponents/CustomButton/index.vue'
import { deviceTypeMixins } from "../../mixins/deviceType.js";
import {
  GetWarningList,
  GetWarningType,
  ExportWarning,
  UpdateWarningStatus,
} from "@/api/business/hazardousChemicals";
// import * as moment from 'moment'
import dayjs from "dayjs";

export default {
  name: "",
  components: {
    CustomTable,
    // CustomButton,
    // CustomTitle,
    CustomForm,
    CustomLayout,
  },
  mixins: [deviceTypeMixins],
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {},
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogVisible: false,
      dialogTitle: "",
      tableSelection: [],

      ruleForm: {
        Content: "",
        EqtType: "",
        Position: "",
      },
      customForm: {
        formItems: [
          {
            key: "Content", // 字段ID
            label: "", // Form的label
            type: "input", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器

            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: "输入设备编号或名称进行搜索",
            },
            width: "240px",
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
          {
            key: "EqtType",
            label: "设备类型",
            type: "select",

            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: "请选择设备类型",
            },
            options: [],
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "WarningType",
            label: "告警类型",
            type: "select",

            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: "请选择告警类型",
            },
            options: [],
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "Handle_Status",
            label: "告警状态",
            type: "select",
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: "请选择告警状态",
            },
            options: [
              {
                label: "告警中",
                value: 1,
              },
              {
                label: "已关闭",
                value: 2,
              },
              // {
              //   label: '已处理',
              //   value: 3
              // }
            ],
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "Position", // 字段ID
            label: "安装位置", // Form的label
            type: "input", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器

            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: "请输入安装位置",
            },
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            // {
            //   text: '新增',
            //   round: false, // 是否圆角
            //   plain: false, // 是否朴素
            //   circle: false, // 是否圆形
            //   loading: false, // 是否加载中
            //   disabled: false, // 是否禁用
            //   icon: '', //  图标
            //   autofocus: false, // 是否聚焦
            //   type: 'primary', // primary / success / warning / danger / info / text
            //   size: 'small', // medium / small / mini
            //   onclick: (item) => {
            //     console.log(item)
            //     this.handleCreate()
            //   }
            // },
            // {
            //   text: '导出',
            //   onclick: (item) => {
            //     console.log(item)
            //     this.handleExport()
            //   }
            // },
            {
              text: "批量导出",
              onclick: (item) => {
                console.log(item);
                this.handleAllExport();
              },
            },
          ],
        },
        // 表格
        loading: false,
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          // {
          //   width: 50,
          //   otherOptions: {
          //     type: 'selection',
          //     align: 'center'
          //   }
          // },
          {
            width: 60,
            label: "序号",
            otherOptions: {
              type: "index",
              align: "center",
            }, // key
            // otherOptions: {
            //   width: 180, // 宽度
            //   fixed: 'left', // left, right
            //   align: 'center' //	left/center/right
            // }
          },
          {
            label: "告警时间",
            key: "Time",
            otherOptions: {
              fixed: "left",
            },
          },
          // {
          //   label: "告警事件名称",
          //   key: "EqtNameType",
          //   otherOptions: {
          //     fixed: "left",
          //   },
          // },
          {
            label: "告警编号",
            key: "WId",
            width: 160,
          },
          {
            label: "告警设备编号",
            key: "EId",
          },
          {
            label: "告警事件名称",
            key: "EnvEventName",
          },
          {
            label: "告警类型",
            key: "Type",
            width: 90,
          },
          {
            label: "触发项",
            key: "TriggerItem",
            width: 90,
          },
          {
            label: "触发值",
            key: "WarningValue",
            width: 90,
          },
          {
            label: "安装位置",
            key: "Position",
          },
          {
            label: "告警状态",
            key: "HandleStatusStr",
          },
          {
            label: "操作人",
            key: "Handler_UserName",
          },
          {
            label: "操作时间",
            key: "Handle_Time",
          },
        ],
        tableData: [],
        operateOptions: {
          width: 200,
        },
        tableActions: [
          // {
          //   actionLabel: '关闭',
          //   otherOptions: {
          //     type: 'text'
          //   },
          //   onclick: (index, row) => {
          //     this.handelClose(row)
          //   }
          // },
          {
            actionLabel: "查看",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, "view");
            },
          },
          // {
          //   actionLabel: '编辑',
          //   otherOptions: {
          //     type: 'text'
          //   },
          //   onclick: (index, row) => {
          //     this.handleEdit(index, row, 'edit')
          //   }
          // },
          // {
          //   actionLabel: '删除',
          //   otherOptions: {
          //     type: 'text'
          //   },
          //   onclick: (index, row) => {
          //     this.handleDelete(index, row)
          //   }
          // }
        ],
      },
    };
  },
  computed: {},
  mounted() {
    this.init();
    this.initDeviceType("EqtType", "HazchemEqtType");
  },
  methods: {
    searchForm(data) {
      console.log(data);
      this.customTableConfig.currentPage = 1;
      this.onFresh();
    },
    resetForm() {
      this.onFresh();
    },
    onFresh() {
      this.GetWarningList();
    },
    init() {
      this.GetWarningList();
      this.GetWarningType();
    },
    async GetWarningList() {
      this.customTableConfig.loading = true;
      const res = await GetWarningList({
        ParameterJson: [
          {
            Key: "",
            Value: [null],
            Type: "",
            Filter_Type: "",
          },
        ],
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,

        SortName: "",
        SortOrder: "",
        Search: "",
        Content: "",
        EqtType: "",
        Position: "",
        IsAll: true,
        ...this.ruleForm,
      });
      this.customTableConfig.loading = false;
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data.map((item) => ({
          ...item,
          Time: dayjs(item.Time).format("YYYY-MM-DD HH:mm:ss"),
        }));
        console.log(res);
        this.customTableConfig.total = res.Data.TotalCount;
      } else {
        this.$message.error(res.Message);
      }
    },
    async GetWarningType() {
      const res = await GetWarningType({});
      if (res.IsSucceed) {
        console.log(res, "res");
        this.customForm.formItems.find(
          (item, index) => item.key === "WarningType"
        ).options = res.Data.map((item) => ({
          label: item.Type,
          value: item.Type,
        }));
        // console.log(res)
      } else {
        this.$message.error(res.Message);
      }
    },
    handleCreate() {
      this.dialogTitle = "新增";
      this.componentsConfig = {
        disabled: false,
        title: "新增",
      };
      this.dialogVisible = true;
    },
    // handleDelete(index, row) {
    //   console.log(index, row)
    //   console.log(this)
    //   this.$confirm('该操作将在监测设备档案中删除该设备信息,请确认是否删除?', '删除', {
    //     type: 'error'
    //   })
    //     .then(async(_) => {
    //       const res = await DeleteEquipment({
    //         IDs: [row.ID]
    //       })
    //       if (res.IsSucceed) {
    //         this.init()
    //       } else {
    //         this.$message.error(res.Message)
    //       }
    //     })
    //     .catch((_) => {})
    // },
    handleEdit(index, row, type) {
      console.log(index, row, type);
      this.dialogVisible = true;
      if (type === "view") {
        this.dialogTitle = "查看";
        this.currentComponent = DialogForm;
        this.componentsConfig = {
          ID: row.ID,
          disabled: true,
          title: "查看",
          row: row,
        };
      }
      // else if (type === 'edit') {
      //   this.dialogTitle = '编辑'
      //   this.componentsConfig = {
      //     ID: row.ID,
      //     disabled: false,
      //     title: '编辑'
      //   }
      // }
    },
    // async handleExport() {
    //   console.log(this.ruleForm)
    //   const res = await ExportWarning({
    //     Content: '',
    //     EqtType: '',
    //     Position: '',
    //     IsAll: false,
    //     Ids: this.tableSelection.map((item) => item.ID),
    //     ...this.ruleForm
    //   })
    //   if (res.IsSucceed) {
    //     console.log(res)
    //     downloadFile(res.Data, '21')
    //   } else {
    //     this.$message.error(res.Message)
    //   }
    // },
    async handleAllExport() {
      const res = await ExportWarning({
        Content: "",
        EqtType: "",
        Position: "",
        IsAll: true,
        Ids: [],
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        console.log(res);
        downloadFile(res.Data, "21");
      } else {
        this.$message.error(res.Message);
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.init();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
    handelClose(row) {
      if (row.HandleStatusStr == "关闭") {
        this.$message.warning("请勿重复操作");
      } else {
        UpdateWarningStatus({ id: row.Id, wid: row.WId, StatusEnum: 2 }).then(
          (res) => {
            if (res.IsSucceed) {
              this.$message.success("操作成功");
              this.init();
            } else {
              this.$message.error(res.Message);
            }
          }
        );
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.layout {
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
