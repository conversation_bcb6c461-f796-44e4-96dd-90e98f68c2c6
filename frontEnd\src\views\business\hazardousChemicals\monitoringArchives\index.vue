<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";

import DialogForm from "./dialogForm.vue";
import DialogType from "./dialogType.vue";
import DialogFormLook from "./dialogFormLook.vue";

import { downloadFile } from "@/utils/downloadFile";
// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'
// import CustomButton from '@/businessComponents/CustomButton/index.vue'

import {
  GetEquipmentList,
  DeleteEquipment,
  ExportHazchemEquipment,
  ExportEquipmentList,
  HazchemImportTemplate,
  HazchemEquipmentImport,
} from "@/api/business/hazardousChemicals";
import importDialog from "@/views/business/energyManagement/components/import.vue";
import { deviceTypeMixins } from "../../mixins/deviceType.js";
// import * as moment from 'moment'
import dayjs from "dayjs";
export default {
  name: "",
  components: {
    CustomTable,
    // CustomButton,
    // CustomTitle,
    CustomForm,
    CustomLayout,
    importDialog,
  },
  mixins: [deviceTypeMixins],
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {
        interfaceName: HazchemEquipmentImport,
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogVisible: false,
      dialogTitle: "",
      tableSelection: [],

      ruleForm: {
        Content: "",
        EqtType: "",
        Position: "",
      },
      customForm: {
        formItems: [
          {
            key: "Content", // 字段ID
            label: "", // Form的label
            type: "input", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: "输入设备编号或名称进行搜索",
            },
            width: "240px",
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
          {
            key: "EqtType",
            label: "设备类型",
            type: "select",
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: "请选择设备类型",
            },
            options: [],
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "Position", // 字段ID
            label: "安装位置", // Form的label
            type: "input", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: "请输入安装位置",
            },
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "设备类型配置",
              type: "primary", // primary / success / warning / danger / info / text
              size: "small", // medium / small / mini
              onclick: (item) => {
                console.log(item);
                this.handleClick();
              },
            },
            {
              text: "新增",
              type: "primary", // primary / success / warning / danger / info / text
              size: "small", // medium / small / mini
              onclick: (item) => {
                console.log(item);
                this.handleCreate();
              },
            },
            {
              text: "下载模板",
              disabled: false, // 是否禁用
              onclick: (item) => {
                console.log(item);
                this.handleDownTemplate();
              },
            },
            {
              text: "批量导入",
              disabled: false, // 是否禁用
              onclick: (item) => {
                console.log(item);
                this.currentComponent = "importDialog";
                this.dialogVisible = true;
                this.dialogTitle = "批量导入";
              },
            },
            // {
            //   text: '导出',
            //   key: 'batch',
            //   disabled: true,
            //   onclick: (item) => {
            //     console.log(item)
            //     this.handleExport()
            //   }
            // },
            {
              text: "批量导出",
              onclick: (item) => {
                console.log(item);
                this.handleAllExport();
              },
            },
          ],
        },
        // 表格
        loading: false,
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            width: 50,
            otherOptions: {
              type: "selection",
              align: "center",
            },
          },
          {
            width: 60,
            label: "序号",
            otherOptions: {
              type: "index",
              align: "center",
            }, // key
            // otherOptions: {
            //   width: 180, // 宽度
            //   fixed: 'left', // left, right
            //   align: 'center' //	left/center/right
            // }
          },
          {
            label: "设备编号",
            key: "EId",
            otherOptions: {
              fixed: 'left'
            },
            // render: row => {
            //   return (
            //     <span>
            //       {row.EId}
            //     </span>
            //   )
            // }
          },
          {
            label: "设备名称",
            key: "Name",
            otherOptions: {
              fixed: 'left'
            },
          },
          {
            label: "品牌",
            key: "Brand",
          },
          // {
          //   label: '型号',
          //   key: 'Type'
          // },
          {
            label: "设备类型",
            key: "EqtType",
          },
          {
            label: "安装位置",
            key: "Position",
          },
          {
            label: "安装日期",
            key: "Date",
          },
        ],
        tableData: [],
        operateOptions: {
          width: 200,
        },
        tableActions: [
          {
            actionLabel: "查看",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, "view");
            },
          },
          {
            actionLabel: "编辑",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, "edit");
            },
          },
          {
            actionLabel: "删除",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleDelete(index, row);
            },
          },
        ],
      },
    };
  },
  computed: {},
  created() {
    this.init();
  },
  mounted() {
    this.initDeviceType("EqtType", "HazchemEqtType");
  },
  methods: {
    searchForm(data) {
      console.log(data);
      this.customTableConfig.currentPage = 1;
      this.onFresh();
    },
    resetForm() {
      this.onFresh();
    },
    onFresh() {
      this.getEquipmentList();
    },
    init() {
      this.getEquipmentList();
    },
    async getEquipmentList() {
      this.customTableConfig.loading = true;
      const res = await GetEquipmentList({
        ParameterJson: [
          {
            Key: "",
            Value: [null],
            Type: "",
            Filter_Type: "",
          },
        ],
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,

        SortName: "",
        SortOrder: "",
        Search: "",
        Content: "",
        EqtType: "",
        Position: "",
        IsAll: true,
        ...this.ruleForm,
      });
      this.customTableConfig.loading = false;
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data.map((item) => ({
          ...item,
          Date: dayjs(item.Date).format("YYYY-MM-DD"),
        }));
        console.log(res);
        this.customTableConfig.total = res.Data.TotalCount;
      } else {
        this.$message.error(res.Message);
      }
    },

    handleCreate() {
      this.dialogTitle = "新增";
      this.currentComponent = DialogForm;
      this.componentsConfig = {
        disabled: false,
        title: "新增",
      };
      this.dialogVisible = true;
    },
    handleDelete(index, row) {
      console.log(index, row);
      console.log(this);
      this.$confirm(
        "该操作将在监测设备档案中删除该设备信息，请确认是否删除？",
        "删除",
        {
          type: "error",
        }
      )
        .then(async (_) => {
          const res = await DeleteEquipment({
            IDs: [row.ID],
          });
          if (res.IsSucceed) {
            this.init();
          } else {
            this.$message.error(res.Message);
          }
        })
        .catch((_) => {});
    },
    handleEdit(index, row, type) {
      console.log(index, row, type);
      if (type === "view") {
        this.currentComponent = DialogFormLook;
        this.dialogTitle = "查看";
        this.componentsConfig = {
          ID: row.ID,
          disabled: true,
          title: "查看",
        };
      } else if (type === "edit") {
        this.dialogTitle = "编辑";
        this.currentComponent = DialogForm;
        this.componentsConfig = {
          ID: row.ID,
          disabled: false,
          title: "编辑",
        };
      }
      this.dialogVisible = true;
    },
    // async handleExport() {
    //   console.log(this.ruleForm)
    //   const res = await ExportHazchemEquipment({
    //     Content: '',
    //     EqtType: '',
    //     Position: '',
    //     IsAll: false,
    //     Ids: this.tableSelection.map((item) => item.ID),
    //     ...this.ruleForm
    //   })
    //   if (res.IsSucceed) {
    //     console.log(res)
    //     downloadFile(res.Data, '21')
    //   } else {
    //     this.$message.error(res.Message)
    //   }
    // },
    // async handleAllExport() {
    //   const res = await ExportHazchemEquipment({
    //     Content: "",
    //     EqtType: "",
    //     Position: "",
    //     IsAll: true,
    //     Ids: [],
    //     ...this.ruleForm,
    //   });
    //   if (res.IsSucceed) {
    //     console.log(res);
    //     downloadFile(res.Data, "21");
    //   } else {
    //     this.$message.error(res.Message);
    //   }
    // },
    // v2 版本 升级导出
    async handleAllExport() {
      const res = await ExportEquipmentList({
        Content: "",
        EqtType: "",
        Position: "",
        IsAll: true,
        Id: this.tableSelection.map((item) => item.ID).toString(),
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        console.log(res);
        downloadFile(res.Data, "21");
      } else {
        this.$message.error(res.Message);
      }
    },
    // 下载模板
    handleDownTemplate() {
      HazchemImportTemplate({}).then((res) => {
        if (res.IsSucceed) {
          downloadFile(res.Data, "危化品监测设备档案导出");
        } else {
          this.$message.error(res.Message);
        }
      });
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.init();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
      // if (this.tableSelection.length > 0) {
      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = false
      // } else {
      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = true
      // }
    },
    handleClick() {
      this.currentComponent = DialogType;
      this.componentsConfig = {
        disabled: false,
        title: "新增",
      };
      this.dialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.layout{
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
