{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\equipmentAnalysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\equipmentAnalysis\\index.vue", "mtime": 1755674552419}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBHZXRTdGF0dXNBbmFseXNlRXF0LA0KICBHZXRFcnJvclJhbmssDQogIEdldFRvcEVxdEVycm9yLA0KICBHZXRTdGFydEFuYWx5c2VFcXQsDQogIEdldExvYWRBbmFseXNlRXF0LA0KICBHZXRDb25zdW1wdGlvbkFuYWx5c2VFcXQsDQogIEdldERldmljZUFuYWx5c2VXb3JrT3JkZXJDb3VudCwNCiAgR2V0V29ya09yZGVySGFuZGxpbmdDb3VudCwNCiAgR2V0V29ya09yZGVyRXJyb3JUeXBlTGlzdCwNCiAgR2V0RGV2aWNlU2VydmljZWFiaWxpdHlSYXRlLA0KICBHZXRBR1ZBbmFseXNlRXF0LA0KICBHZXREaWN0aW9uYXJ5RGV0YWlsTGlzdEJ5UGFyZW50SWQsDQogIEdldFByb2R1Y2VUcmVuZCwNCiAgR2V0RGV2aWNlU3RhdHVzRGV0YWlscywNCiAgR2V0U3RhcnRBbmFseXNlRXF0RGV0YWlscw0KfSBmcm9tICdAL2FwaS9idXNpbmVzcy9lcXB0QXNzZXQnDQoNCmltcG9ydCBlcXVpcG1lbnRJbnRlZ3JpdHlSYXRlIGZyb20gJy4vY29tcG9uZW50cy9lcXVpcG1lbnRJbnRlZ3JpdHlSYXRlJw0KaW1wb3J0IGVxdWlwbWVudE9wZXJhdGlvbiBmcm9tICcuL2NvbXBvbmVudHMvZXF1aXBtZW50T3BlcmF0aW9uJw0KaW1wb3J0IHJlcGFpckZhdWx0VHlwZSBmcm9tICcuL2NvbXBvbmVudHMvcmVwYWlyRmF1bHRUeXBlJw0KaW1wb3J0IGN1c3RvbVByb2Nlc3MgZnJvbSAnLi9jb21wb25lbnRzL2N1c3RvbVByb2Nlc3MnDQppbXBvcnQgbG9hZFJhdGVSYW5raW5nIGZyb20gJy4vY29tcG9uZW50cy9sb2FkUmF0ZVJhbmtpbmcnDQppbXBvcnQgbWFpbnRlbmFuY2VXb3JrT3JkZXIgZnJvbSAnLi9jb21wb25lbnRzL21haW50ZW5hbmNlV29ya09yZGVyJw0KaW1wb3J0IGVxdWlwbWVudE9wZXJhdGlvblN0YXR1cyBmcm9tICcuL2NvbXBvbmVudHMvZXF1aXBtZW50T3BlcmF0aW9uU3RhdHVzJw0KaW1wb3J0IGFndkJhdHRlcnlMZXZlbCBmcm9tICcuL2NvbXBvbmVudHMvYWd2QmF0dGVyeUxldmVsJw0KaW1wb3J0IGVmZmljaWVuY3lBbmFseXNpcyBmcm9tICcuL2NvbXBvbmVudHMvZWZmaWNpZW5jeUFuYWx5c2lzJw0KaW1wb3J0IGVxdWlwbWVudEZhaWx1cmUgZnJvbSAnLi9jb21wb25lbnRzL2VxdWlwbWVudEZhaWx1cmUnDQoNCmltcG9ydCBlZmZpY2llbmN5QW5hbHlzaXNEZXRhaWwgZnJvbSAnLi9jb21wb25lbnRzL2VmZmljaWVuY3lBbmFseXNpc0RldGFpbCcNCmltcG9ydCBlcXVpcG1lbnRGYWlsdXJlRGV0YWlsIGZyb20gJy4vY29tcG9uZW50cy9lcXVpcG1lbnRGYWlsdXJlRGV0YWlsJw0KaW1wb3J0IHsgR2V0RXF1aXBtZW50QXNzZXRQYWdlTGlzdFBKIH0gZnJvbSAnQC9hcGkvYnVzaW5lc3MvZXFwdEFzc2V0Jw0KaW1wb3J0IGRheWpzIGZyb20gJ2RheWpzJw0KaW1wb3J0IFZDaGFydCBmcm9tICd2dWUtZWNoYXJ0cycNCmltcG9ydCB7IHVzZSB9IGZyb20gJ2VjaGFydHMvY29yZScNCmltcG9ydCB7IENhbnZhc1JlbmRlcmVyIH0gZnJvbSAnZWNoYXJ0cy9yZW5kZXJlcnMnDQppbXBvcnQgeyBCYXJDaGFydCwgTGluZUNoYXJ0LCBQaWVDaGFydCB9IGZyb20gJ2VjaGFydHMvY2hhcnRzJw0KaW1wb3J0IHsNCiAgR3JpZENvbXBvbmVudCwNCiAgTGVnZW5kQ29tcG9uZW50LA0KICBUb29sdGlwQ29tcG9uZW50LA0KICBUaXRsZUNvbXBvbmVudCwNCiAgRGF0YVpvb21Db21wb25lbnQNCn0gZnJvbSAnZWNoYXJ0cy9jb21wb25lbnRzJw0KdXNlKFsNCiAgQ2FudmFzUmVuZGVyZXIsDQogIEJhckNoYXJ0LA0KICBMaW5lQ2hhcnQsDQogIFBpZUNoYXJ0LA0KICBEYXRhWm9vbUNvbXBvbmVudCwNCiAgR3JpZENvbXBvbmVudCwNCiAgTGVnZW5kQ29tcG9uZW50LA0KICBUaXRsZUNvbXBvbmVudCwNCiAgVG9vbHRpcENvbXBvbmVudA0KXSkNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0VxdWlwbWVudEFuYWx5c2lzJywNCiAgY29tcG9uZW50czogew0KICAgIFZDaGFydCwNCiAgICBlcXVpcG1lbnRPcGVyYXRpb24sDQogICAgZXF1aXBtZW50SW50ZWdyaXR5UmF0ZSwNCiAgICByZXBhaXJGYXVsdFR5cGUsDQogICAgY3VzdG9tUHJvY2VzcywNCiAgICBsb2FkUmF0ZVJhbmtpbmcsDQogICAgbWFpbnRlbmFuY2VXb3JrT3JkZXIsDQogICAgZXF1aXBtZW50T3BlcmF0aW9uU3RhdHVzLA0KICAgIGFndkJhdHRlcnlMZXZlbCwNCiAgICBlZmZpY2llbmN5QW5hbHlzaXMsDQogICAgZXF1aXBtZW50RmFpbHVyZQ0KICB9LA0KICBtaXhpbnM6IFtdLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBjdXJyZW50Q29tcG9uZW50OiBudWxsLA0KICAgICAgY29tcG9uZW50c0NvbmZpZzoge30sDQogICAgICBjb21wb25lbnRzRnVuczogew0KICAgICAgICBvcGVuOiAoKSA9PiB7DQogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICBjbG9zZTogKCkgPT4gew0KICAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBjdXJyZW50Q29tcG9uZW50VGl0bGU6ICcnLA0KICAgICAgLy8NCiAgICAgIGRldmljZVN0YXR1c0xpc3RMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGRldmljZVN0YXR1c0xpc3Q6IFtdLA0KICAgICAgLy8g6K6+5aSH6L+Q6KGM54q25Ya1DQogICAgICBlcXVpcG1lbnRPcGVyYXRpb25TdGF0dXM6IFtdLA0KICAgICAgLy8gUkdW55S16YeP5a6e5pe255uR5rWLDQogICAgICByZ3ZEYXRhTGlzdExvYWRpbmc6IGZhbHNlLA0KICAgICAgcmd2RGF0YUxpc3Q6IFtdLA0KICAgICAgLy8g5pys5pyI6K6+5aSH57u05L+u5a6M5aW9546HDQogICAgICBlcXVpcG1lbnRNYWludGVuYW5jZUludGVncml0eVJhdGVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGVxdWlwbWVudE1haW50ZW5hbmNlUmVhZGluZXNzUmF0ZU1vbnRoOiBkYXlqcyhuZXcgRGF0ZSgpKS5mb3JtYXQoDQogICAgICAgICdZWVlZLU1NJw0KICAgICAgKSwNCiAgICAgIC8vIOe7tOS/ruW3peWNleWkhOeQhuaDheWGtQ0KICAgICAgbWFpbnRlbmFuY2VXb3JrT3JkZXJQcm9jZXNzaW5nU3RhdHVzTG9hZGluZzogZmFsc2UsDQogICAgICBtYWludGVuYW5jZVdvcmtPcmRlclByb2Nlc3NpbmdTdGF0dXNPbmU6IFtdLA0KICAgICAgLy8g57u05L+d5bel5Y2V5aSE55CG5oOF5Ya1DQogICAgICBtYWludGVuYW5jZVdvcmtPcmRlclByb2Nlc3NpbmdTdGF0dXNUd286IFtdLA0KICAgICAgLy8g5pys5pyI54SK5o6l6K6+5aSH6ICX5p2Q5L2/55So5pWI546H5o6S6KGMDQogICAgICBtb250aFJhbmtXZWxkaW5nRXF1aXBtZW50Q29uc3VtYWJsZXNVc2FnZUVmZmljaWVuY3lWYWx1ZToNCiAgICAgICAgJ1dpcmVDb25zdW1wdGlvbicsDQogICAgICAvLyDmnKzmnIjnhIrkuJ3kvb/nlKjmlYjnjocNCiAgICAgIHJhbmtQcm9kRXF1aXBtZW50UmF0ZU1vbnRoTG9hZGluZzogZmFsc2UsDQogICAgICByYW5rUHJvZEVxdWlwbWVudFJhdGVNb250aDoge30sDQogICAgICAvLyDmnKzmnIjnhIrmjqXorr7lpIfogJfmnZDkvb/nlKjmlYjnjofmjpLooYwNCiAgICAgIG1vbnRoUmFua1dlbGRpbmdFcXVpcG1lbnRDb25zdW1hYmxlc1VzYWdlRWZmaWNpZW5jeUxvYWRpbmc6IGZhbHNlLA0KICAgICAgbW9udGhSYW5rV2VsZGluZ0VxdWlwbWVudENvbnN1bWFibGVzVXNhZ2VFZmZpY2llbmN5OiB7fSwNCiAgICAgIC8vIOiuvuWkh+W8guW4uOaDheWGteaOkuihjA0KICAgICAgZXF1aXBtZW50QWJub3JtYWxpdHlSYW5raW5nRGF0YUxvYWRpbmc6IGZhbHNlLA0KICAgICAgZXF1aXBtZW50QWJub3JtYWxpdHlSYW5raW5nRGF0YTogW10sDQogICAgICAvLw0KICAgICAgbGF0ZXN0QWxhcm1JbmZvcm1hdGlvbkRhdGFMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGxhdGVzdEFsYXJtSW5mb3JtYXRpb25EYXRhOiBbXSwNCiAgICAgIC8vIOacrOaciOiuvuWkh+W8gOacuuaDheWGtQ0KICAgICAgZXF1aXBtZW50U3RhcnR1cFN0YXR1c01vbnRoVmFsdWU6ICcnLA0KICAgICAgZXF1aXBtZW50T3BlcmF0aW9uVmFsdWU6ICcnLA0KICAgICAgZXF1aXBtZW50U3RhcnR1cFN0YXR1c01vbnRoT3B0aW9uczogW10sDQoNCiAgICAgIC8vIOiDveaViO+8iOeUte+8ieWIhuaekA0KICAgICAgcHJvZHVjdGlvblZvbHVtZVRyZW5kVmFsdWU6ICcnLA0KICAgICAgcHJvZHVjdGlvblZvbHVtZVRyZW5kTG9hZGluZzogZmFsc2UsDQogICAgICBwcm9kdWN0aW9uVm9sdW1lVHJlbmRTZWxlY3RPcHRpb25zOiBbDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ+WFqOmDqCcsDQogICAgICAgICAgdmFsdWU6ICcnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ+S4gOi9pumXtCcsDQogICAgICAgICAgdmFsdWU6ICfkuIDovabpl7QnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ+S6jOi9pumXtCcsDQogICAgICAgICAgdmFsdWU6ICfkuozovabpl7QnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ+mFjemAgeS4reW/gycsDQogICAgICAgICAgdmFsdWU6ICfphY3pgIHkuK3lv4MnDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBwcm9kdWN0aW9uVm9sdW1lVHJlbmRPcHRpb25zOiB7DQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycNCiAgICAgICAgfSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICAvLyB0b3A6ICIwIiwNCiAgICAgICAgICAvLyBsZWZ0OiAiMCIsDQogICAgICAgICAgLy8gaXRlbVdpZHRoOiAxMCwNCiAgICAgICAgICAvLyBpdGVtSGVpZ2h0OiA1LA0KICAgICAgICAgIC8vIGljb246ICJyZWN0IiwNCiAgICAgICAgICAvLyB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAvLyAgIGZvbnRTaXplOiAxMiwNCiAgICAgICAgICAvLyAgIGNvbG9yOiAiIzk5OTk5OSIsDQogICAgICAgICAgLy8gfSwNCiAgICAgICAgfSwNCiAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgIGRhdGE6IFtdLA0KICAgICAgICAgIGF4aXNMaW5lOiB7DQogICAgICAgICAgICBzaG93OiBmYWxzZQ0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc1RpY2s6IHsNCiAgICAgICAgICAgIHNob3c6IGZhbHNlDQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICfnlKjnlLXph48oa1fCt2gpJywNCiAgICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgICBwb3NpdGlvbjogJ2xlZnQnLA0KICAgICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICAgIGZvcm1hdHRlcjogJ3t2YWx1ZX0nLA0KICAgICAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgICAgICBjb2xvcjogJyMyOThERkYnIC8vIOiuvue9riB5IOi9tOagh+etvuaWh+Wtl+minOiJsuS4uue6ouiJsg0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbmFtZVRleHRTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyMyOThERkYnLA0KICAgICAgICAgICAgICBmb250U2l6ZTogJzEzcHgnDQogICAgICAgICAgICB9DQogICAgICAgICAgICAvLyBsb2dCYXNlOiAxMCwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICfnlJ/kuqfkuqfph48odCknLA0KICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICAgIC8vIG1pbjogMCwNCiAgICAgICAgICAgIC8vIG1heDogMjUsDQogICAgICAgICAgICAvLyBpbnRlcnZhbDogNSwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgICBmb3JtYXR0ZXI6ICd7dmFsdWV9JywNCiAgICAgICAgICAgICAgdGV4dFN0eWxlOiB7DQogICAgICAgICAgICAgICAgY29sb3I6ICcjRkY5MDJDJyAvLyDorr7nva4geSDovbTmoIfnrb7mloflrZfpopzoibLkuLrnuqLoibINCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIG5hbWVUZXh0U3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjRkY5MDJDJywNCiAgICAgICAgICAgICAgZm9udFNpemU6ICcxM3B4Jw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgXSwNCiAgICAgICAgY29sb3I6IFsnIzI5OERGRicsICcjRkY5MDJDJ10sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICfnlKjnlLXph48nLA0KICAgICAgICAgICAgLy8gc3ltYm9sOiAibm9uZSIsDQogICAgICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgICAgIC8vIGljb246ICdyZWN0JywNCiAgICAgICAgICAgIC8vIHlBeGlzSW5kZXg6IDEsDQogICAgICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgICAgIHZhbHVlRm9ybWF0dGVyOiBmdW5jdGlvbih2YWx1ZSkgew0KICAgICAgICAgICAgICAgIHJldHVybiBgJHt2YWx1ZSB8fCAwfWAgKyAnIGtXwrdoJw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn55Sf5Lqn5Lqn6YePJywNCiAgICAgICAgICAgIC8vIHN5bWJvbDogIm5vbmUiLA0KICAgICAgICAgICAgZGF0YTogW10sDQogICAgICAgICAgICB5QXhpc0luZGV4OiAxLA0KICAgICAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgICAgICB2YWx1ZUZvcm1hdHRlcjogZnVuY3Rpb24odmFsdWUpIHsNCiAgICAgICAgICAgICAgICByZXR1cm4gYCR7dmFsdWUgfHwgMH1gICsgJyB0Jw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy8gaWNvbjogJ3JlY3QnLA0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnDQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgLy8g6K6+5aSH5pWF6ZqcDQogICAgICBlcXVpcG1lbnRGYWlsdXJlVHJlbmRWYWx1ZTogJycsDQogICAgICBlcXVpcG1lbnRGYWlsdXJlVHJlbmRTZWxlY3RPcHRpb25zOiBbXSwNCiAgICAgIGVxdWlwbWVudEZhaWx1cmVUcmVuZExvYWRpbmc6IGZhbHNlLA0KICAgICAgZXF1aXBtZW50RmFpbHVyZVRyZW5kT3B0aW9uczogew0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnDQogICAgICAgIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgICBheGlzTGluZTogew0KICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNUaWNrOiB7DQogICAgICAgICAgICBzaG93OiBmYWxzZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCg0KICAgICAgICBjb2xvcjogWydyZ2JhKDQxLCAxNDEsIDI1NSwgMSknXSwNCiAgICAgICAgeUF4aXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgICAgcG9zaXRpb246ICdsZWZ0JywNCiAgICAgICAgICAgIGxvZ0Jhc2U6IDEwDQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBzbW9vdGg6IHRydWUsDQogICAgICAgICAgICAvLyBzeW1ib2w6ICJub25lIiwNCiAgICAgICAgICAgIGRhdGE6IFtdLA0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgICAgICB2YWx1ZUZvcm1hdHRlcjogZnVuY3Rpb24odmFsdWUpIHsNCiAgICAgICAgICAgICAgICByZXR1cm4gYCR7dmFsdWUgfHwgMH1gICsgJyDkuKonDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIC8vIGFyZWFTdHlsZTogew0KICAgICAgICAgICAgLy8gICBjb2xvcjogbmV3IGVjaGFydHMuZ3JhcGhpYy5MaW5lYXJHcmFkaWVudCgwLCAwLCAwLCAxLCBbDQogICAgICAgICAgICAvLyAgICAgew0KICAgICAgICAgICAgLy8gICAgICAgb2Zmc2V0OiAwLA0KICAgICAgICAgICAgLy8gICAgICAgY29sb3I6ICJyZ2JhKDYyLCAyMDQsIDE0NywgLjUpIiwNCiAgICAgICAgICAgIC8vICAgICB9LA0KICAgICAgICAgICAgLy8gICAgIHsNCiAgICAgICAgICAgIC8vICAgICAgIG9mZnNldDogMSwNCiAgICAgICAgICAgIC8vICAgICAgIGNvbG9yOiAicmdiYSg1NywgMTMzLCAyMzgsIDApIiwNCiAgICAgICAgICAgIC8vICAgICB9LA0KICAgICAgICAgICAgLy8gICBdKSwNCiAgICAgICAgICAgIC8vIH0sDQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgLy8g5pys5pyI6K6+5aSH57u05L+u5a6M5aW9546HDQogICAgICBlcXVpcG1lbnRNYWludGVuYW5jZUludGVncml0eVJhdGVPYmo6IHt9LA0KICAgICAgd29ya09yZGVyRXJyb3JUeXBlTG9hZGluZzogZmFsc2UsDQogICAgICB3b3JrT3JkZXJFcnJvclR5cGVMaXN0OiBbXSwNCiAgICAgIGVxdWlwbWVudE9wZXJhdGlvbkxpc3RMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGVxdWlwbWVudE9wZXJhdGlvbkxpc3Q6IFtdDQogICAgfQ0KICB9LA0KICBhY3RpdmF0ZWQoKSB7fSwNCiAgbW91bnRlZCgpIHsNCiAgICAvLyDojrflj5borr7lpIcNCiAgICB0aGlzLmdldEVxdWlwbWVudEFzc2V0UGFnZUxpc3RQSigpDQogICAgLy8g6I635Y+W6K6+5aSH5a2Q57G7DQogICAgdGhpcy5nZXREaWN0aW9uYXJ5RGV0YWlsTGlzdEJ5UGFyZW50SWQoKQ0KICAgIC8vIOiOt+WPluiuvuWkh+aVsOmHh+WIhuaekOiuvuWkh+i/kOihjOeKtuWGtQ0KICAgIHRoaXMuZ2V0U3RhdHVzQW5hbHlzZUVxdCgpDQogICAgLy8g6I635Y+W6K6+5aSH5byC5bi45oOF5Ya15o6S6KGMDQogICAgdGhpcy5nZXRFcnJvclJhbmsoKQ0KICAgIC8vIOiOt+WPluiuvuWkh+aVsOmHh+W8guW4uOS/oeaBrw0KICAgIHRoaXMuZ2V0VG9wRXF0RXJyb3IoKQ0KICAgIC8vIC8vIOiOt+WPluiuvuWkh+aVsOmHh+WIhuaekOW8gOacuuaXtumXtOWIhuaekA0KICAgIC8vIHRoaXMuZ2V0U3RhcnRBbmFseXNlRXF0KCk7DQogICAgLy8g6I635Y+W6K6+5aSH5pWw6YeH5YiG5p6Q6LSf6L29546H5YiG5p6QDQogICAgdGhpcy5nZXRMb2FkQW5hbHlzZUVxdCgpDQogICAgLy8g6I635Y+W5pys5pyI6ICX5p2Q6K6+5aSH5L2/55So5pWI546H5o6S6KGMDQogICAgdGhpcy5nZXRDb25zdW1wdGlvbkFuYWx5c2VFcXQoKQ0KICAgIC8vIOiOt+WPluiuvuWkh+aVhemanA0KICAgIHRoaXMuZ2V0RGV2aWNlQW5hbHlzZVdvcmtPcmRlckNvdW50KCkNCiAgICAvLyDojrflj5blt6XljZXlpITnkIbmg4XlhrUNCiAgICB0aGlzLmdldFdvcmtPcmRlckhhbmRsaW5nQ291bnQoKQ0KICAgIC8vIOiOt+WPluW3peWNleexu+Wei+aVhemanOasoeaVsA0KICAgIHRoaXMuZ2V0V29ya09yZGVyRXJyb3JUeXBlTGlzdCgpDQogICAgLy8g6I635Y+W6K6+5aSH5a6M5aW9546HDQogICAgdGhpcy5nZXREZXZpY2VTZXJ2aWNlYWJpbGl0eVJhdGUoKQ0KICAgIC8vIOiOt+WPlkFHVui/kOihjOeKtuaAgQ0KICAgIHRoaXMuZ2V0QUdWQW5hbHlzZUVxdCgpDQogICAgLy8g6I635Y+W6IO95pWI77yI55S177yJ5YiG5p6QDQogICAgdGhpcy5nZXRQcm9kdWNlVHJlbmQoKQ0KICAgIC8vIOiOt+WPluiuvuWkh+eKtuaAgQ0KICAgIHRoaXMuZ2V0RGV2aWNlU3RhdHVzRGV0YWlscygpDQogICAgLy8g6I635Y+W6K6+5aSH6L+Q6KGMDQogICAgdGhpcy5nZXRTdGFydEFuYWx5c2VFcXREZXRhaWxzKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOaJk+W8gCDog73mlYjvvIjnlLXvvInliIbmnpANCiAgICBwcm9kdWN0aW9uVm9sdW1lVHJlbmRDbGljaygpIHsNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudFRpdGxlID0gJ+iDveaViO+8iOeUte+8ieWIhuaekCcNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9IGVmZmljaWVuY3lBbmFseXNpc0RldGFpbA0KICAgICAgdGhpcy5jb21wb25lbnRzQ29uZmlnID0gew0KICAgICAgICBzZWxlY3RPdGhlck9wdGlvbnM6IHRoaXMucHJvZHVjdGlvblZvbHVtZVRyZW5kU2VsZWN0T3B0aW9ucw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5omT5byAIOiuvuWkh+aVhemanA0KICAgIGVxdWlwbWVudEZhaWx1cmVUcmVuZENsaWNrKCkgew0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50VGl0bGUgPSAn6K6+5aSH5pWF6ZqcJw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gZXF1aXBtZW50RmFpbHVyZURldGFpbA0KICAgICAgdGhpcy5jb21wb25lbnRzQ29uZmlnID0gew0KICAgICAgICBzZWxlY3RPdGhlck9wdGlvbnM6IHRoaXMuZXF1aXBtZW50RmFpbHVyZVRyZW5kU2VsZWN0T3B0aW9ucw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6I635Y+W6K6+5aSH54q25oCBDQogICAgYXN5bmMgZ2V0RGV2aWNlU3RhdHVzRGV0YWlscygpIHsNCiAgICAgIHRoaXMuZGV2aWNlU3RhdHVzTGlzdExvYWRpbmcgPSBmYWxzZQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0RGV2aWNlU3RhdHVzRGV0YWlscyh7fSkNCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIHRoaXMuZGV2aWNlU3RhdHVzTGlzdCA9IHJlcy5EYXRhDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgIH0pDQogICAgICB9DQogICAgICB0aGlzLmRldmljZVN0YXR1c0xpc3RMb2FkaW5nID0gZmFsc2UNCiAgICB9LA0KICAgIC8vIOiOt+WPluiuvuWkhw0KICAgIGFzeW5jIGdldEVxdWlwbWVudEFzc2V0UGFnZUxpc3RQSigpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldEVxdWlwbWVudEFzc2V0UGFnZUxpc3RQSih7DQogICAgICAgIERpc3BsYXlfTmFtZTogJycsDQogICAgICAgIERldmljZV9UeXBlX0lkOiAnY2M4MWZlOGEtZjBhMy00MGMxLWFiZjYtZjU1M2ZhNTAyYTMzJywNCiAgICAgICAgRGV2aWNlX1R5cGVfRGV0YWlsX0lkOiAnJywNCiAgICAgICAgRGVwYXJ0bWVudDogJycsDQogICAgICAgIFBhZ2U6IDEsDQogICAgICAgIFBhZ2VTaXplOiAxMDANCiAgICAgIH0pDQogICAgICB0aGlzLmVxdWlwbWVudEZhaWx1cmVUcmVuZFNlbGVjdE9wdGlvbnMgPSByZXMuRGF0YS5EYXRhLmZpbHRlcigNCiAgICAgICAgKGl0ZW0pID0+IGl0ZW0uSXNTaG93ID09IDENCiAgICAgICkNCiAgICAgIHRoaXMuZXF1aXBtZW50RmFpbHVyZVRyZW5kU2VsZWN0T3B0aW9ucy51bnNoaWZ0KHsNCiAgICAgICAgRGlzcGxheV9OYW1lOiAn5YWo6YOoJywNCiAgICAgICAgSWQ6ICcnDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g6I635Y+W6K6+5aSH5a2Q57G7DQogICAgYXN5bmMgZ2V0RGljdGlvbmFyeURldGFpbExpc3RCeVBhcmVudElkKCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0RGljdGlvbmFyeURldGFpbExpc3RCeVBhcmVudElkKA0KICAgICAgICAnY2M4MWZlOGEtZjBhMy00MGMxLWFiZjYtZjU1M2ZhNTAyYTMzJw0KICAgICAgKQ0KICAgICAgdGhpcy5lcXVpcG1lbnRTdGFydHVwU3RhdHVzTW9udGhPcHRpb25zID0gcmVzLkRhdGENCiAgICAgIHRoaXMuZXF1aXBtZW50U3RhcnR1cFN0YXR1c01vbnRoT3B0aW9ucy51bnNoaWZ0KHsNCiAgICAgICAgRGlzcGxheV9OYW1lOiAn5YWo6YOoJywNCiAgICAgICAgSWQ6ICcnDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g6IO95pWI77yI55S177yJ5YiG5p6QDQogICAgYXN5bmMgZ2V0UHJvZHVjZVRyZW5kKCkgew0KICAgICAgdGhpcy5wcm9kdWN0aW9uVm9sdW1lVHJlbmRMb2FkaW5nID0gZmFsc2UNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldFByb2R1Y2VUcmVuZCh7DQogICAgICAgIE5vZGVOYW1lOiB0aGlzLnByb2R1Y3Rpb25Wb2x1bWVUcmVuZFZhbHVlDQogICAgICB9KQ0KDQogICAgICB0aGlzLnByb2R1Y3Rpb25Wb2x1bWVUcmVuZE9wdGlvbnMueEF4aXMuZGF0YSA9IHJlcy5EYXRhLm1hcCgNCiAgICAgICAgKGl0ZW0pID0+IGl0ZW0uS2V5DQogICAgICApDQogICAgICB0aGlzLnByb2R1Y3Rpb25Wb2x1bWVUcmVuZE9wdGlvbnMuc2VyaWVzWzBdLmRhdGEgPSByZXMuRGF0YS5tYXAoDQogICAgICAgIChpdGVtKSA9PiBpdGVtLkVsZWN0cmljDQogICAgICApDQogICAgICB0aGlzLnByb2R1Y3Rpb25Wb2x1bWVUcmVuZE9wdGlvbnMuc2VyaWVzWzFdLmRhdGEgPSByZXMuRGF0YS5tYXAoDQogICAgICAgIChpdGVtKSA9PiBpdGVtLlByb2R1Y2UNCiAgICAgICkNCiAgICAgIHRoaXMucHJvZHVjdGlvblZvbHVtZVRyZW5kTG9hZGluZyA9IGZhbHNlDQogICAgfSwNCiAgICAvLyDojrflj5borr7lpIfmlbDph4fliIbmnpDorr7lpIfov5DooYznirblhrUNCiAgICBhc3luYyBnZXRTdGF0dXNBbmFseXNlRXF0KCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0U3RhdHVzQW5hbHlzZUVxdCh7fSkNCiAgICAgIHRoaXMuZXF1aXBtZW50T3BlcmF0aW9uU3RhdHVzID0gcmVzLkRhdGENCiAgICB9LA0KICAgIC8vIOiOt+WPluiuvuWkh+W8guW4uOaDheWGteaOkuihjA0KICAgIGFzeW5jIGdldEVycm9yUmFuaygpIHsNCiAgICAgIHRoaXMuZXF1aXBtZW50QWJub3JtYWxpdHlSYW5raW5nRGF0YUxvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRFcnJvclJhbmsoe30pDQoNCiAgICAgIHRoaXMuZXF1aXBtZW50QWJub3JtYWxpdHlSYW5raW5nRGF0YSA9IHJlcy5EYXRhDQogICAgICB0aGlzLmVxdWlwbWVudEFibm9ybWFsaXR5UmFua2luZ0RhdGFMb2FkaW5nID0gZmFsc2UNCiAgICB9LA0KICAgIC8vIOiOt+WPluiuvuWkh+aVsOmHh+W8guW4uOS/oeaBrw0KICAgIGFzeW5jIGdldFRvcEVxdEVycm9yKCkgew0KICAgICAgdGhpcy5sYXRlc3RBbGFybUluZm9ybWF0aW9uRGF0YUxvYWRpbmcgPSB0cnVlDQoNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldFRvcEVxdEVycm9yKHt9KQ0KICAgICAgdGhpcy5sYXRlc3RBbGFybUluZm9ybWF0aW9uRGF0YSA9IHJlcy5EYXRhLnNsaWNlKDAsIDUpDQogICAgICB0aGlzLmxhdGVzdEFsYXJtSW5mb3JtYXRpb25EYXRhTG9hZGluZyA9IGZhbHNlDQogICAgfSwNCiAgICAvLyDojrflj5borr7lpIfmlbDph4fliIbmnpDlvIDmnLrml7bpl7TliIbmnpANCiAgICAvLyBhc3luYyBnZXRTdGFydEFuYWx5c2VFcXQoKSB7DQogICAgLy8gICBsZXQgcmVzID0gYXdhaXQgR2V0U3RhcnRBbmFseXNlRXF0KHsNCiAgICAvLyAgICAgSUQ6IHRoaXMuZXF1aXBtZW50U3RhcnR1cFN0YXR1c01vbnRoVmFsdWUsDQogICAgLy8gICB9KTsNCiAgICAvLyB9LA0KICAgIC8vIOiOt+WPluiuvuWkh+aVsOmHh+WIhuaekOi0n+i9veeOh+WIhuaekA0KICAgIGFzeW5jIGdldExvYWRBbmFseXNlRXF0KCkgew0KICAgICAgdGhpcy5yYW5rUHJvZEVxdWlwbWVudFJhdGVNb250aExvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRMb2FkQW5hbHlzZUVxdCh7fSkNCiAgICAgIHRoaXMucmFua1Byb2RFcXVpcG1lbnRSYXRlTW9udGggPSByZXMuRGF0YQ0KICAgICAgdGhpcy5yYW5rUHJvZEVxdWlwbWVudFJhdGVNb250aExvYWRpbmcgPSBmYWxzZQ0KICAgIH0sDQogICAgLy8g6I635Y+W5pys5pyI6ICX5p2Q6K6+5aSH5L2/55So5pWI546H5o6S6KGMDQogICAgYXN5bmMgZ2V0Q29uc3VtcHRpb25BbmFseXNlRXF0KCkgew0KICAgICAgdGhpcy5tb250aFJhbmtXZWxkaW5nRXF1aXBtZW50Q29uc3VtYWJsZXNVc2FnZUVmZmljaWVuY3lMb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0Q29uc3VtcHRpb25BbmFseXNlRXF0KHsNCiAgICAgICAgQ29udGVudDogdGhpcy5tb250aFJhbmtXZWxkaW5nRXF1aXBtZW50Q29uc3VtYWJsZXNVc2FnZUVmZmljaWVuY3lWYWx1ZQ0KICAgICAgfSkNCiAgICAgIHRoaXMubW9udGhSYW5rV2VsZGluZ0VxdWlwbWVudENvbnN1bWFibGVzVXNhZ2VFZmZpY2llbmN5ID0gcmVzLkRhdGENCiAgICAgIHRoaXMubW9udGhSYW5rV2VsZGluZ0VxdWlwbWVudENvbnN1bWFibGVzVXNhZ2VFZmZpY2llbmN5TG9hZGluZyA9IGZhbHNlDQogICAgfSwNCiAgICAvLyDojrflj5borr7lpIfmlYXpmpwNCiAgICBhc3luYyBnZXREZXZpY2VBbmFseXNlV29ya09yZGVyQ291bnQoKSB7DQogICAgICB0aGlzLmVxdWlwbWVudEZhaWx1cmVUcmVuZExvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXREZXZpY2VBbmFseXNlV29ya09yZGVyQ291bnQoew0KICAgICAgICBJRDogdGhpcy5lcXVpcG1lbnRGYWlsdXJlVHJlbmRWYWx1ZSwNCiAgICAgICAgVGltZTogJycNCiAgICAgIH0pDQogICAgICB0aGlzLmVxdWlwbWVudEZhaWx1cmVUcmVuZE9wdGlvbnMueEF4aXMuZGF0YSA9IHJlcy5EYXRhLm1hcCgNCiAgICAgICAgKGl0ZW0pID0+IGl0ZW0uTGFiZWwNCiAgICAgICkNCiAgICAgIHRoaXMuZXF1aXBtZW50RmFpbHVyZVRyZW5kT3B0aW9ucy5zZXJpZXNbMF0uZGF0YSA9IHJlcy5EYXRhLm1hcCgNCiAgICAgICAgKGl0ZW0pID0+IGl0ZW0uVmFsdWUNCiAgICAgICkNCiAgICAgIHRoaXMuZXF1aXBtZW50RmFpbHVyZVRyZW5kTG9hZGluZyA9IGZhbHNlDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluW3peWNleWkhOeQhuaDheWGtQ0KICAgIGFzeW5jIGdldFdvcmtPcmRlckhhbmRsaW5nQ291bnQoKSB7DQogICAgICB0aGlzLm1haW50ZW5hbmNlV29ya09yZGVyUHJvY2Vzc2luZ1N0YXR1c0xvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRXb3JrT3JkZXJIYW5kbGluZ0NvdW50KHt9KQ0KICAgICAgdGhpcy5tYWludGVuYW5jZVdvcmtPcmRlclByb2Nlc3NpbmdTdGF0dXNPbmUgPSByZXMuRGF0YS5SZWNvbmRpdGlvbnMNCiAgICAgIHRoaXMubWFpbnRlbmFuY2VXb3JrT3JkZXJQcm9jZXNzaW5nU3RhdHVzVHdvID0gcmVzLkRhdGEuTWFpbnRlbmFuY2VzDQogICAgICB0aGlzLm1haW50ZW5hbmNlV29ya09yZGVyUHJvY2Vzc2luZ1N0YXR1c0xvYWRpbmcgPSBmYWxzZQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5blt6XljZXnsbvlnovmlYXpmpzmrKHmlbANCiAgICBhc3luYyBnZXRXb3JrT3JkZXJFcnJvclR5cGVMaXN0KCkgew0KICAgICAgdGhpcy53b3JrT3JkZXJFcnJvclR5cGVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0V29ya09yZGVyRXJyb3JUeXBlTGlzdCh7fSkNCiAgICAgIHRoaXMud29ya09yZGVyRXJyb3JUeXBlTGlzdCA9IHJlcy5EYXRhLm1hcCgoaXRlbSkgPT4gKHsNCiAgICAgICAgVmFsdWU6IGl0ZW0uUmF0ZSwNCiAgICAgICAgTGFiZWw6IGl0ZW0uVHlwZQ0KICAgICAgfSkpDQogICAgICB0aGlzLndvcmtPcmRlckVycm9yVHlwZUxvYWRpbmcgPSBmYWxzZQ0KICAgIH0sDQogICAgLy8g6I635Y+W6K6+5aSH5a6M5aW9546HDQogICAgYXN5bmMgZ2V0RGV2aWNlU2VydmljZWFiaWxpdHlSYXRlKCkgew0KICAgICAgdGhpcy5lcXVpcG1lbnRNYWludGVuYW5jZUludGVncml0eVJhdGVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0RGV2aWNlU2VydmljZWFiaWxpdHlSYXRlKHsNCiAgICAgICAgVGltZTogZGF5anModGhpcy5lcXVpcG1lbnRNYWludGVuYW5jZVJlYWRpbmVzc1JhdGVNb250aCkuZm9ybWF0KA0KICAgICAgICAgICdZWVlZLU1NJw0KICAgICAgICApDQogICAgICB9KQ0KICAgICAgdGhpcy5lcXVpcG1lbnRNYWludGVuYW5jZUludGVncml0eVJhdGVPYmogPSByZXMuRGF0YQ0KICAgICAgdGhpcy5lcXVpcG1lbnRNYWludGVuYW5jZUludGVncml0eVJhdGVMb2FkaW5nID0gZmFsc2UNCiAgICB9LA0KDQogICAgLy8g6I635Y+WQUdW6L+Q6KGM54q25oCBDQogICAgYXN5bmMgZ2V0QUdWQW5hbHlzZUVxdCgpIHsNCiAgICAgIHRoaXMucmd2RGF0YUxpc3RMb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0QUdWQW5hbHlzZUVxdCh7fSkNCiAgICAgIHRoaXMucmd2RGF0YUxpc3QgPSByZXMuRGF0YQ0KICAgICAgdGhpcy5yZ3ZEYXRhTGlzdExvYWRpbmcgPSBmYWxzZQ0KICAgIH0sDQogICAgLy8g6K6+572u6KGo5qC86aKc6ImyDQogICAgbGF0ZXN0QWxhcm1JbmZvcm1hdGlvbkRhdGFDbGFzc05hbWUoeyByb3csIHJvd0luZGV4IH0pIHsNCiAgICAgIGlmICh0aGlzLmlzRXZlbk9yT2RkKHJvd0luZGV4KSkgew0KICAgICAgICByZXR1cm4gJ3Jvdy1vbmUnDQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJ3Jvdy10d28nDQogICAgICB9DQogICAgfSwNCg0KICAgIEhlYWRlclJvd0NsYXNzTmFtZSh7IHJvdywgcm93SW5kZXggfSkgew0KICAgICAgcmV0dXJuICdyb3ctaGVhZGVyJw0KICAgIH0sDQogICAgY2VsbENsYXNzTmFtZSh7IHJvdywgcm93SW5kZXggfSkgew0KICAgICAgcmV0dXJuICdyb3ctYm9keScNCiAgICB9LA0KDQogICAgLy8g6K6+572u6KGo5qC86aKc6ImyDQogICAgcm93Q2xhc3NOYW1lKHsgcm93LCByb3dJbmRleCB9KSB7DQogICAgICBpZiAodGhpcy5pc0V2ZW5Pck9kZChyb3dJbmRleCArIDEpKSB7DQogICAgICAgIHJldHVybiAncm93LW9uZScNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiAncm93LXR3bycNCiAgICAgIH0NCiAgICB9LA0KDQogICAgcHJvZHVjdGlvblZvbHVtZVRyZW5kQ2hhbmdlKCkgew0KICAgICAgdGhpcy5nZXRQcm9kdWNlVHJlbmQoKQ0KICAgIH0sDQogICAgZXF1aXBtZW50RmFpbHVyZVRyZW5kQ2hhbmdlKCkgew0KICAgICAgdGhpcy5nZXREZXZpY2VBbmFseXNlV29ya09yZGVyQ291bnQoKQ0KICAgIH0sDQogICAgZXF1aXBtZW50TWFpbnRlbmFuY2VSZWFkaW5lc3NSYXRlTW9udGhDaGFuZ2UoKSB7DQogICAgICB0aGlzLmdldERldmljZVNlcnZpY2VhYmlsaXR5UmF0ZSgpDQogICAgfSwNCiAgICBoYW5kbGVDbGljayh0YWIsIGV2ZW50KSB7fSwNCiAgICAvLyAg5Yik5pat5piv5ZCm5piv5YG25pWw6KGMIOi/mOaYr+Wlh+aVsOihjA0KICAgIGlzRXZlbk9yT2RkKG51bWJlcikgew0KICAgICAgaWYgKG51bWJlciAlIDIgPT09IDApIHsNCiAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6K6+5aSH6L+Q6KGMDQogICAgYXN5bmMgZ2V0U3RhcnRBbmFseXNlRXF0RGV0YWlscygpIHsNCiAgICAgIHRoaXMuZXF1aXBtZW50T3BlcmF0aW9uTGlzdExvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRTdGFydEFuYWx5c2VFcXREZXRhaWxzKHsNCiAgICAgICAgSUQ6IHRoaXMuZXF1aXBtZW50T3BlcmF0aW9uVmFsdWUNCiAgICAgIH0pDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICB0aGlzLmVxdWlwbWVudE9wZXJhdGlvbkxpc3QgPSByZXMuRGF0YQ0KICAgICAgfQ0KICAgICAgdGhpcy5lcXVpcG1lbnRPcGVyYXRpb25MaXN0TG9hZGluZyA9IGZhbHNlDQogICAgfSwNCiAgICBlcXVpcG1lbnRPcGVyYXRpb25DaGFuZ2UoKSB7DQogICAgICB0aGlzLmdldFN0YXJ0QW5hbHlzZUVxdERldGFpbHMoKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAge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file": "index.vue", "sourceRoot": "src/views/business/equipmentManagement/equipmentAnalysis", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 equipmentAnalysis\">\r\n    <el-row :gutter=\"12\">\r\n      <el-col :span=\"24\">\r\n        <equipmentOperationStatus\r\n          v-loading=\"deviceStatusListLoading\"\r\n          :component-data=\"{\r\n            deviceStatusList: deviceStatusList,\r\n          }\"\r\n        />\r\n        <!-- <el-card shadow=\"never\" v-loading=\"deviceStatusListLoading\">\r\n          <equipmentOperationStatus\r\n            :componentData=\"{\r\n              deviceStatusList: deviceStatusList,\r\n            }\"\r\n          ></equipmentOperationStatus>\r\n        </el-card> -->\r\n      </el-col>\r\n\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"24\">\r\n        <el-card v-loading=\"rgvDataListLoading\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>AGV电量实时监测</span>\r\n          </div>\r\n          <agvBatteryLevel\r\n            style=\"margin-top: -20px\"\r\n            :component-data=\"{\r\n              rgvDataList: rgvDataList,\r\n            }\"\r\n          />\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"12\">\r\n        <el-card\r\n          v-loading=\"equipmentAbnormalityRankingDataLoading\"\r\n          shadow=\"never\"\r\n        >\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>设备异常情况排行</span>\r\n          </div>\r\n          <div style=\"margin-top: -20px\">\r\n            <el-table\r\n              :data=\"equipmentAbnormalityRankingData\"\r\n              style=\"width: 100%\"\r\n              height=\"240\"\r\n              :highlight-current-row=\"false\"\r\n              :row-class-name=\"rowClassName\"\r\n              :header-row-class-name=\"HeaderRowClassName\"\r\n              :cell-class-name=\"cellClassName\"\r\n            >\r\n              <el-table-column label=\"排名\" width=\"60\">\r\n                <template slot-scope=\"scope\">\r\n                  <div\r\n                    v-if=\"scope.$index < 3\"\r\n                    class=\"tablenumber\"\r\n                    :style=\"{\r\n                      backgroundImage:\r\n                        'url(' +\r\n                        require(`@/assets/no_${scope.$index + 1}.png`) +\r\n                        ')',\r\n                    }\"\r\n                  >\r\n                    <span> {{ scope.$index + 1 }}</span>\r\n                  </div>\r\n                  <div v-else class=\"tablenumber\">\r\n                    <span> {{ scope.$index + 1 }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Name\" label=\"设备名称\" />\r\n              <el-table-column prop=\"Area\" label=\"所属车间\" />\r\n              <el-table-column\r\n                prop=\"ErrorTime\"\r\n                label=\"设备异常时间\"\r\n                width=\"140\"\r\n              />\r\n              <el-table-column\r\n                prop=\"StartTime\"\r\n                label=\"设备开机时间\"\r\n                width=\"140\"\r\n              />\r\n              <el-table-column prop=\"ErrPercent\" label=\"异常率\" width=\"100\" />\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-card v-loading=\"latestAlarmInformationDataLoading\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>最新告警消息</span>\r\n          </div>\r\n          <div style=\"margin-top: -20px\">\r\n            <el-table\r\n              :data=\"latestAlarmInformationData\"\r\n              style=\"width: 100%\"\r\n              height=\"240\"\r\n              :show-header=\"true\"\r\n              :highlight-current-row=\"false\"\r\n              :row-class-name=\"rowClassName\"\r\n              :header-row-class-name=\"HeaderRowClassName\"\r\n              :cell-class-name=\"cellClassName\"\r\n            >\r\n              <el-table-column prop=\"Time\" label=\"告警时间\" width=\"160\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.Time || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Name\" label=\"告警设备\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.Name || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"StatusDes\" label=\"设备状态\" width=\"90\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.StatusDes || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Brand\" label=\"设备品牌\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.Brand || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <!-- <el-table-column prop=\"Content\" label=\"异常信息\" width=\"90\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.Content || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column> -->\r\n              <el-table-column label=\"\" width=\"40\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-popover placement=\"bottom-end\" width=\"\" trigger=\"hover\">\r\n                    <div class=\"popover_latestAlarmInformation\">\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >告警时间</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.Time || \"-\" }}</span>\r\n                      </div>\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >告警设备</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.Name || \"-\" }}</span>\r\n                      </div>\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >设备状态</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.StatusDes || \"-\" }}</span>\r\n                      </div>\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >设备品牌</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.Brand || \"-\" }}</span>\r\n                      </div>\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >异常信息</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.Content || \"-\" }}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div\r\n                      slot=\"reference\"\r\n                      style=\"\r\n                        height: 100%;\r\n                        display: flex;\r\n                        justify-content: center;\r\n                      \"\r\n                    >\r\n                      <i class=\"el-icon-arrow-right\" />\r\n                    </div>\r\n                  </el-popover>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"18\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"12\">\r\n            <el-card v-loading=\"productionVolumeTrendLoading\" shadow=\"never\">\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>能效（电）分析</span>\r\n                <span\r\n                  class=\"right\"\r\n                  @click=\"productionVolumeTrendClick\"\r\n                >更多<i class=\"el-icon-arrow-right\" /></span>\r\n              </div>\r\n              <div class=\"chartCardConent\">\r\n                <div class=\"chartCardItem\">\r\n                  <el-select\r\n                    v-model=\"productionVolumeTrendValue\"\r\n                    :clearable=\"true\"\r\n                    placeholder=\"请选择\"\r\n                    @change=\"productionVolumeTrendChange\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in productionVolumeTrendSelectOptions\"\r\n                      :key=\"item.value\"\r\n                      :label=\"item.label\"\r\n                      :value=\"item.value\"\r\n                    />\r\n                  </el-select>\r\n                </div>\r\n                <efficiencyAnalysis\r\n                  :component-data=\"{\r\n                    productionVolumeTrendOptions: productionVolumeTrendOptions,\r\n                  }\"\r\n                />\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card v-loading=\"equipmentFailureTrendLoading\" shadow=\"never\">\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>设备故障</span>\r\n                <span\r\n                  class=\"right\"\r\n                  @click=\"equipmentFailureTrendClick\"\r\n                >更多<i class=\"el-icon-arrow-right\" /></span>\r\n              </div>\r\n              <div class=\"chartCardConent\">\r\n                <div class=\"chartCardItem\">\r\n                  <el-select\r\n                    v-model=\"equipmentFailureTrendValue\"\r\n                    placeholder=\"请选择\"\r\n                    :clearable=\"true\"\r\n                    @change=\"equipmentFailureTrendChange\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in equipmentFailureTrendSelectOptions\"\r\n                      :key=\"item.Id\"\r\n                      :label=\"item.Display_Name\"\r\n                      :value=\"item.Id\"\r\n                    />\r\n                  </el-select>\r\n                </div>\r\n                <equipmentFailure\r\n                  :component-data=\"{\r\n                    equipmentFailureTrendOptions: equipmentFailureTrendOptions,\r\n                  }\"\r\n                />\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"12\">\r\n            <el-card\r\n              v-loading=\"rankProdEquipmentRateMonthLoading\"\r\n              shadow=\"never\"\r\n            >\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>本月生产设备负载率排行\r\n                  <el-tooltip\r\n                    class=\"item\"\r\n                    content=\"负载率=设备运行时间/设备开机时间\"\r\n                    placement=\"top-start\"\r\n                  >\r\n                    <img src=\"@/assets/tooltip.png\" alt=\"\">\r\n                  </el-tooltip>\r\n                </span>\r\n                <span\r\n                  class=\"unit\"\r\n                >单位：{{ rankProdEquipmentRateMonth.Unit }}</span>\r\n              </div>\r\n              <customProcess\r\n                :component-data=\"{ list: rankProdEquipmentRateMonth.Charts }\"\r\n              />\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card\r\n              v-loading=\"\r\n                monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading\r\n              \"\r\n              shadow=\"never\"\r\n            >\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>本月焊丝使用效率\r\n                  <el-tooltip\r\n                    class=\"item\"\r\n                    content=\"负载率=设备运行时间/设备开机时间\"\r\n                    placement=\"top-start\"\r\n                  >\r\n                    <template #content>\r\n                      <p>1.使用效率=使用量/运行时间（小时）</p>\r\n                      <p>2.使用量单位标识： -焊剂KG -焊丝KG -导电嘴（个）</p>\r\n                    </template>\r\n                    <img src=\"@/assets/tooltip.png\" alt=\"\">\r\n                  </el-tooltip>\r\n                </span>\r\n                <span\r\n                  class=\"unit\"\r\n                >单位：{{\r\n                  monthRankWeldingEquipmentConsumablesUsageEfficiency.Unit\r\n                }}</span>\r\n              </div>\r\n              <customProcess\r\n                :component-data=\"{\r\n                  list: monthRankWeldingEquipmentConsumablesUsageEfficiency.Charts,\r\n                }\"\r\n              />\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"12\">\r\n            <el-row :gutter=\"12\">\r\n              <el-col :span=\"8\">\r\n                <el-card\r\n                  v-loading=\"maintenanceWorkOrderProcessingStatusLoading\"\r\n                  shadow=\"never\"\r\n                >\r\n                  <div slot=\"header\" class=\"header\">\r\n                    <span>维修工单</span>\r\n                  </div>\r\n                  <maintenanceWorkOrder\r\n                    :component-data=\"{\r\n                      data: maintenanceWorkOrderProcessingStatusOne,\r\n                    }\"\r\n                  />\r\n                </el-card>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-card\r\n                  v-loading=\"maintenanceWorkOrderProcessingStatusLoading\"\r\n                  shadow=\"never\"\r\n                >\r\n                  <div slot=\"header\" class=\"header\">\r\n                    <span>维保工单</span>\r\n                  </div>\r\n                  <maintenanceWorkOrder\r\n                    :component-data=\"{\r\n                      data: maintenanceWorkOrderProcessingStatusTwo,\r\n                    }\"\r\n                  />\r\n                </el-card>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-card v-loading=\"workOrderErrorTypeLoading\" shadow=\"never\">\r\n                  <div slot=\"header\" class=\"header\">\r\n                    <span>维修故障类型</span>\r\n                  </div>\r\n                  <repairFaultType\r\n                    :component-data=\"{ workOrderErrorTypeList }\"\r\n                  />\r\n                </el-card>\r\n              </el-col>\r\n            </el-row>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card\r\n              v-loading=\"equipmentMaintenanceIntegrityRateLoading\"\r\n              shadow=\"never\"\r\n            >\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>设备维修完好率</span>\r\n                <el-date-picker\r\n                  v-model=\"equipmentMaintenanceReadinessRateMonth\"\r\n                  size=\"small\"\r\n                  type=\"month\"\r\n                  placeholder=\"选择月\"\r\n                  :clearable=\"false\"\r\n                  :editable=\"false\"\r\n                  @change=\"equipmentMaintenanceReadinessRateMonthChange\"\r\n                />\r\n              </div>\r\n              <equipmentIntegrityRate\r\n                :component-data=\"{ data: equipmentMaintenanceIntegrityRateObj }\"\r\n              />\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-card v-loading=\"equipmentOperationListLoading\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>本月设备运行</span>\r\n            <el-select\r\n              v-model=\"equipmentOperationValue\"\r\n              :clearable=\"true\"\r\n              placeholder=\"请选择\"\r\n              @change=\"equipmentOperationChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in equipmentStartupStatusMonthOptions\"\r\n                :key=\"item.Id\"\r\n                :label=\"item.Display_Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n          <equipmentOperation\r\n            :component-data=\"{ data: equipmentOperationList }\"\r\n          />\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"currentComponentTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      custom-class=\"dialogCustomClass\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :component-data=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  GetStatusAnalyseEqt,\r\n  GetErrorRank,\r\n  GetTopEqtError,\r\n  GetStartAnalyseEqt,\r\n  GetLoadAnalyseEqt,\r\n  GetConsumptionAnalyseEqt,\r\n  GetDeviceAnalyseWorkOrderCount,\r\n  GetWorkOrderHandlingCount,\r\n  GetWorkOrderErrorTypeList,\r\n  GetDeviceServiceabilityRate,\r\n  GetAGVAnalyseEqt,\r\n  GetDictionaryDetailListByParentId,\r\n  GetProduceTrend,\r\n  GetDeviceStatusDetails,\r\n  GetStartAnalyseEqtDetails\r\n} from '@/api/business/eqptAsset'\r\n\r\nimport equipmentIntegrityRate from './components/equipmentIntegrityRate'\r\nimport equipmentOperation from './components/equipmentOperation'\r\nimport repairFaultType from './components/repairFaultType'\r\nimport customProcess from './components/customProcess'\r\nimport loadRateRanking from './components/loadRateRanking'\r\nimport maintenanceWorkOrder from './components/maintenanceWorkOrder'\r\nimport equipmentOperationStatus from './components/equipmentOperationStatus'\r\nimport agvBatteryLevel from './components/agvBatteryLevel'\r\nimport efficiencyAnalysis from './components/efficiencyAnalysis'\r\nimport equipmentFailure from './components/equipmentFailure'\r\n\r\nimport efficiencyAnalysisDetail from './components/efficiencyAnalysisDetail'\r\nimport equipmentFailureDetail from './components/equipmentFailureDetail'\r\nimport { GetEquipmentAssetPageListPJ } from '@/api/business/eqptAsset'\r\nimport dayjs from 'dayjs'\r\nimport VChart from 'vue-echarts'\r\nimport { use } from 'echarts/core'\r\nimport { CanvasRenderer } from 'echarts/renderers'\r\nimport { BarChart, LineChart, PieChart } from 'echarts/charts'\r\nimport {\r\n  GridComponent,\r\n  LegendComponent,\r\n  TooltipComponent,\r\n  TitleComponent,\r\n  DataZoomComponent\r\n} from 'echarts/components'\r\nuse([\r\n  CanvasRenderer,\r\n  BarChart,\r\n  LineChart,\r\n  PieChart,\r\n  DataZoomComponent,\r\n  GridComponent,\r\n  LegendComponent,\r\n  TitleComponent,\r\n  TooltipComponent\r\n])\r\nexport default {\r\n  name: 'EquipmentAnalysis',\r\n  components: {\r\n    VChart,\r\n    equipmentOperation,\r\n    equipmentIntegrityRate,\r\n    repairFaultType,\r\n    customProcess,\r\n    loadRateRanking,\r\n    maintenanceWorkOrder,\r\n    equipmentOperationStatus,\r\n    agvBatteryLevel,\r\n    efficiencyAnalysis,\r\n    equipmentFailure\r\n  },\r\n  mixins: [],\r\n  data() {\r\n    return {\r\n      currentComponent: null,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.fetchData()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      currentComponentTitle: '',\r\n      //\r\n      deviceStatusListLoading: false,\r\n      deviceStatusList: [],\r\n      // 设备运行状况\r\n      equipmentOperationStatus: [],\r\n      // RGV电量实时监测\r\n      rgvDataListLoading: false,\r\n      rgvDataList: [],\r\n      // 本月设备维修完好率\r\n      equipmentMaintenanceIntegrityRateLoading: false,\r\n      equipmentMaintenanceReadinessRateMonth: dayjs(new Date()).format(\r\n        'YYYY-MM'\r\n      ),\r\n      // 维修工单处理情况\r\n      maintenanceWorkOrderProcessingStatusLoading: false,\r\n      maintenanceWorkOrderProcessingStatusOne: [],\r\n      // 维保工单处理情况\r\n      maintenanceWorkOrderProcessingStatusTwo: [],\r\n      // 本月焊接设备耗材使用效率排行\r\n      monthRankWeldingEquipmentConsumablesUsageEfficiencyValue:\r\n        'WireConsumption',\r\n      // 本月焊丝使用效率\r\n      rankProdEquipmentRateMonthLoading: false,\r\n      rankProdEquipmentRateMonth: {},\r\n      // 本月焊接设备耗材使用效率排行\r\n      monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading: false,\r\n      monthRankWeldingEquipmentConsumablesUsageEfficiency: {},\r\n      // 设备异常情况排行\r\n      equipmentAbnormalityRankingDataLoading: false,\r\n      equipmentAbnormalityRankingData: [],\r\n      //\r\n      latestAlarmInformationDataLoading: false,\r\n      latestAlarmInformationData: [],\r\n      // 本月设备开机情况\r\n      equipmentStartupStatusMonthValue: '',\r\n      equipmentOperationValue: '',\r\n      equipmentStartupStatusMonthOptions: [],\r\n\r\n      // 能效（电）分析\r\n      productionVolumeTrendValue: '',\r\n      productionVolumeTrendLoading: false,\r\n      productionVolumeTrendSelectOptions: [\r\n        {\r\n          label: '全部',\r\n          value: ''\r\n        },\r\n        {\r\n          label: '一车间',\r\n          value: '一车间'\r\n        },\r\n        {\r\n          label: '二车间',\r\n          value: '二车间'\r\n        },\r\n        {\r\n          label: '配送中心',\r\n          value: '配送中心'\r\n        }\r\n      ],\r\n      productionVolumeTrendOptions: {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        legend: {\r\n          show: false\r\n          // top: \"0\",\r\n          // left: \"0\",\r\n          // itemWidth: 10,\r\n          // itemHeight: 5,\r\n          // icon: \"rect\",\r\n          // textStyle: {\r\n          //   fontSize: 12,\r\n          //   color: \"#999999\",\r\n          // },\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        yAxis: [\r\n          {\r\n            name: '用电量(kW·h)',\r\n            type: 'value',\r\n            position: 'left',\r\n            axisLabel: {\r\n              formatter: '{value}',\r\n              textStyle: {\r\n                color: '#298DFF' // 设置 y 轴标签文字颜色为红色\r\n              }\r\n            },\r\n            nameTextStyle: {\r\n              color: '#298DFF',\r\n              fontSize: '13px'\r\n            }\r\n            // logBase: 10,\r\n          },\r\n          {\r\n            name: '生产产量(t)',\r\n            type: 'value',\r\n            // min: 0,\r\n            // max: 25,\r\n            // interval: 5,\r\n            axisLabel: {\r\n              formatter: '{value}',\r\n              textStyle: {\r\n                color: '#FF902C' // 设置 y 轴标签文字颜色为红色\r\n              }\r\n            },\r\n            nameTextStyle: {\r\n              color: '#FF902C',\r\n              fontSize: '13px'\r\n            }\r\n          }\r\n        ],\r\n        color: ['#298DFF', '#FF902C'],\r\n        series: [\r\n          {\r\n            name: '用电量',\r\n            // symbol: \"none\",\r\n            data: [],\r\n            // icon: 'rect',\r\n            // yAxisIndex: 1,\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return `${value || 0}` + ' kW·h'\r\n              }\r\n            },\r\n            type: 'line'\r\n          },\r\n          {\r\n            name: '生产产量',\r\n            // symbol: \"none\",\r\n            data: [],\r\n            yAxisIndex: 1,\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return `${value || 0}` + ' t'\r\n              }\r\n            },\r\n            // icon: 'rect',\r\n            type: 'line'\r\n          }\r\n        ]\r\n      },\r\n      // 设备故障\r\n      equipmentFailureTrendValue: '',\r\n      equipmentFailureTrendSelectOptions: [],\r\n      equipmentFailureTrendLoading: false,\r\n      equipmentFailureTrendOptions: {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n\r\n        color: ['rgba(41, 141, 255, 1)'],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            position: 'left',\r\n            logBase: 10\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            smooth: true,\r\n            // symbol: \"none\",\r\n            data: [],\r\n            type: 'line',\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return `${value || 0}` + ' 个'\r\n              }\r\n            }\r\n            // areaStyle: {\r\n            //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n            //     {\r\n            //       offset: 0,\r\n            //       color: \"rgba(62, 204, 147, .5)\",\r\n            //     },\r\n            //     {\r\n            //       offset: 1,\r\n            //       color: \"rgba(57, 133, 238, 0)\",\r\n            //     },\r\n            //   ]),\r\n            // },\r\n          }\r\n        ]\r\n      },\r\n      // 本月设备维修完好率\r\n      equipmentMaintenanceIntegrityRateObj: {},\r\n      workOrderErrorTypeLoading: false,\r\n      workOrderErrorTypeList: [],\r\n      equipmentOperationListLoading: false,\r\n      equipmentOperationList: []\r\n    }\r\n  },\r\n  activated() {},\r\n  mounted() {\r\n    // 获取设备\r\n    this.getEquipmentAssetPageListPJ()\r\n    // 获取设备子类\r\n    this.getDictionaryDetailListByParentId()\r\n    // 获取设备数采分析设备运行状况\r\n    this.getStatusAnalyseEqt()\r\n    // 获取设备异常情况排行\r\n    this.getErrorRank()\r\n    // 获取设备数采异常信息\r\n    this.getTopEqtError()\r\n    // // 获取设备数采分析开机时间分析\r\n    // this.getStartAnalyseEqt();\r\n    // 获取设备数采分析负载率分析\r\n    this.getLoadAnalyseEqt()\r\n    // 获取本月耗材设备使用效率排行\r\n    this.getConsumptionAnalyseEqt()\r\n    // 获取设备故障\r\n    this.getDeviceAnalyseWorkOrderCount()\r\n    // 获取工单处理情况\r\n    this.getWorkOrderHandlingCount()\r\n    // 获取工单类型故障次数\r\n    this.getWorkOrderErrorTypeList()\r\n    // 获取设备完好率\r\n    this.getDeviceServiceabilityRate()\r\n    // 获取AGV运行状态\r\n    this.getAGVAnalyseEqt()\r\n    // 获取能效（电）分析\r\n    this.getProduceTrend()\r\n    // 获取设备状态\r\n    this.getDeviceStatusDetails()\r\n    // 获取设备运行\r\n    this.getStartAnalyseEqtDetails()\r\n  },\r\n  methods: {\r\n    // 打开 能效（电）分析\r\n    productionVolumeTrendClick() {\r\n      this.currentComponentTitle = '能效（电）分析'\r\n      this.dialogVisible = true\r\n      this.currentComponent = efficiencyAnalysisDetail\r\n      this.componentsConfig = {\r\n        selectOtherOptions: this.productionVolumeTrendSelectOptions\r\n      }\r\n    },\r\n    // 打开 设备故障\r\n    equipmentFailureTrendClick() {\r\n      this.currentComponentTitle = '设备故障'\r\n      this.dialogVisible = true\r\n      this.currentComponent = equipmentFailureDetail\r\n      this.componentsConfig = {\r\n        selectOtherOptions: this.equipmentFailureTrendSelectOptions\r\n      }\r\n    },\r\n    // 获取设备状态\r\n    async getDeviceStatusDetails() {\r\n      this.deviceStatusListLoading = false\r\n      const res = await GetDeviceStatusDetails({})\r\n      if (res.IsSucceed) {\r\n        this.deviceStatusList = res.Data\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n      this.deviceStatusListLoading = false\r\n    },\r\n    // 获取设备\r\n    async getEquipmentAssetPageListPJ() {\r\n      const res = await GetEquipmentAssetPageListPJ({\r\n        Display_Name: '',\r\n        Device_Type_Id: 'cc81fe8a-f0a3-40c1-abf6-f553fa502a33',\r\n        Device_Type_Detail_Id: '',\r\n        Department: '',\r\n        Page: 1,\r\n        PageSize: 100\r\n      })\r\n      this.equipmentFailureTrendSelectOptions = res.Data.Data.filter(\r\n        (item) => item.IsShow == 1\r\n      )\r\n      this.equipmentFailureTrendSelectOptions.unshift({\r\n        Display_Name: '全部',\r\n        Id: ''\r\n      })\r\n    },\r\n    // 获取设备子类\r\n    async getDictionaryDetailListByParentId() {\r\n      const res = await GetDictionaryDetailListByParentId(\r\n        'cc81fe8a-f0a3-40c1-abf6-f553fa502a33'\r\n      )\r\n      this.equipmentStartupStatusMonthOptions = res.Data\r\n      this.equipmentStartupStatusMonthOptions.unshift({\r\n        Display_Name: '全部',\r\n        Id: ''\r\n      })\r\n    },\r\n    // 能效（电）分析\r\n    async getProduceTrend() {\r\n      this.productionVolumeTrendLoading = false\r\n      const res = await GetProduceTrend({\r\n        NodeName: this.productionVolumeTrendValue\r\n      })\r\n\r\n      this.productionVolumeTrendOptions.xAxis.data = res.Data.map(\r\n        (item) => item.Key\r\n      )\r\n      this.productionVolumeTrendOptions.series[0].data = res.Data.map(\r\n        (item) => item.Electric\r\n      )\r\n      this.productionVolumeTrendOptions.series[1].data = res.Data.map(\r\n        (item) => item.Produce\r\n      )\r\n      this.productionVolumeTrendLoading = false\r\n    },\r\n    // 获取设备数采分析设备运行状况\r\n    async getStatusAnalyseEqt() {\r\n      const res = await GetStatusAnalyseEqt({})\r\n      this.equipmentOperationStatus = res.Data\r\n    },\r\n    // 获取设备异常情况排行\r\n    async getErrorRank() {\r\n      this.equipmentAbnormalityRankingDataLoading = true\r\n      const res = await GetErrorRank({})\r\n\r\n      this.equipmentAbnormalityRankingData = res.Data\r\n      this.equipmentAbnormalityRankingDataLoading = false\r\n    },\r\n    // 获取设备数采异常信息\r\n    async getTopEqtError() {\r\n      this.latestAlarmInformationDataLoading = true\r\n\r\n      const res = await GetTopEqtError({})\r\n      this.latestAlarmInformationData = res.Data.slice(0, 5)\r\n      this.latestAlarmInformationDataLoading = false\r\n    },\r\n    // 获取设备数采分析开机时间分析\r\n    // async getStartAnalyseEqt() {\r\n    //   let res = await GetStartAnalyseEqt({\r\n    //     ID: this.equipmentStartupStatusMonthValue,\r\n    //   });\r\n    // },\r\n    // 获取设备数采分析负载率分析\r\n    async getLoadAnalyseEqt() {\r\n      this.rankProdEquipmentRateMonthLoading = true\r\n      const res = await GetLoadAnalyseEqt({})\r\n      this.rankProdEquipmentRateMonth = res.Data\r\n      this.rankProdEquipmentRateMonthLoading = false\r\n    },\r\n    // 获取本月耗材设备使用效率排行\r\n    async getConsumptionAnalyseEqt() {\r\n      this.monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading = true\r\n      const res = await GetConsumptionAnalyseEqt({\r\n        Content: this.monthRankWeldingEquipmentConsumablesUsageEfficiencyValue\r\n      })\r\n      this.monthRankWeldingEquipmentConsumablesUsageEfficiency = res.Data\r\n      this.monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading = false\r\n    },\r\n    // 获取设备故障\r\n    async getDeviceAnalyseWorkOrderCount() {\r\n      this.equipmentFailureTrendLoading = true\r\n      const res = await GetDeviceAnalyseWorkOrderCount({\r\n        ID: this.equipmentFailureTrendValue,\r\n        Time: ''\r\n      })\r\n      this.equipmentFailureTrendOptions.xAxis.data = res.Data.map(\r\n        (item) => item.Label\r\n      )\r\n      this.equipmentFailureTrendOptions.series[0].data = res.Data.map(\r\n        (item) => item.Value\r\n      )\r\n      this.equipmentFailureTrendLoading = false\r\n    },\r\n\r\n    // 获取工单处理情况\r\n    async getWorkOrderHandlingCount() {\r\n      this.maintenanceWorkOrderProcessingStatusLoading = true\r\n      const res = await GetWorkOrderHandlingCount({})\r\n      this.maintenanceWorkOrderProcessingStatusOne = res.Data.Reconditions\r\n      this.maintenanceWorkOrderProcessingStatusTwo = res.Data.Maintenances\r\n      this.maintenanceWorkOrderProcessingStatusLoading = false\r\n    },\r\n\r\n    // 获取工单类型故障次数\r\n    async getWorkOrderErrorTypeList() {\r\n      this.workOrderErrorTypeLoading = true\r\n      const res = await GetWorkOrderErrorTypeList({})\r\n      this.workOrderErrorTypeList = res.Data.map((item) => ({\r\n        Value: item.Rate,\r\n        Label: item.Type\r\n      }))\r\n      this.workOrderErrorTypeLoading = false\r\n    },\r\n    // 获取设备完好率\r\n    async getDeviceServiceabilityRate() {\r\n      this.equipmentMaintenanceIntegrityRateLoading = true\r\n      const res = await GetDeviceServiceabilityRate({\r\n        Time: dayjs(this.equipmentMaintenanceReadinessRateMonth).format(\r\n          'YYYY-MM'\r\n        )\r\n      })\r\n      this.equipmentMaintenanceIntegrityRateObj = res.Data\r\n      this.equipmentMaintenanceIntegrityRateLoading = false\r\n    },\r\n\r\n    // 获取AGV运行状态\r\n    async getAGVAnalyseEqt() {\r\n      this.rgvDataListLoading = true\r\n      const res = await GetAGVAnalyseEqt({})\r\n      this.rgvDataList = res.Data\r\n      this.rgvDataListLoading = false\r\n    },\r\n    // 设置表格颜色\r\n    latestAlarmInformationDataClassName({ row, rowIndex }) {\r\n      if (this.isEvenOrOdd(rowIndex)) {\r\n        return 'row-one'\r\n      } else {\r\n        return 'row-two'\r\n      }\r\n    },\r\n\r\n    HeaderRowClassName({ row, rowIndex }) {\r\n      return 'row-header'\r\n    },\r\n    cellClassName({ row, rowIndex }) {\r\n      return 'row-body'\r\n    },\r\n\r\n    // 设置表格颜色\r\n    rowClassName({ row, rowIndex }) {\r\n      if (this.isEvenOrOdd(rowIndex + 1)) {\r\n        return 'row-one'\r\n      } else {\r\n        return 'row-two'\r\n      }\r\n    },\r\n\r\n    productionVolumeTrendChange() {\r\n      this.getProduceTrend()\r\n    },\r\n    equipmentFailureTrendChange() {\r\n      this.getDeviceAnalyseWorkOrderCount()\r\n    },\r\n    equipmentMaintenanceReadinessRateMonthChange() {\r\n      this.getDeviceServiceabilityRate()\r\n    },\r\n    handleClick(tab, event) {},\r\n    //  判断是否是偶数行 还是奇数行\r\n    isEvenOrOdd(number) {\r\n      if (number % 2 === 0) {\r\n        return true\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    // 设备运行\r\n    async getStartAnalyseEqtDetails() {\r\n      this.equipmentOperationListLoading = true\r\n      const res = await GetStartAnalyseEqtDetails({\r\n        ID: this.equipmentOperationValue\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.equipmentOperationList = res.Data\r\n      }\r\n      this.equipmentOperationListLoading = false\r\n    },\r\n    equipmentOperationChange() {\r\n      this.getStartAnalyseEqtDetails()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.equipmentAnalysis {\r\n  // padding: 10px 15px;\r\n  overflow-y: scroll;\r\n  // height: calc(100vh - 96px);\r\n  .dialogCustomClass {\r\n    .formBox {\r\n      height: 580px;\r\n      padding: 0 16px;\r\n      &::-webkit-scrollbar {\r\n        display: none;\r\n      }\r\n    }\r\n    .costomTitle {\r\n      display: flex;\r\n      align-items: center;\r\n      color: #333;\r\n      margin-bottom: 16px;\r\n      span {\r\n        display: inline-block;\r\n        width: 2px;\r\n        height: 14px;\r\n        background: #009dff;\r\n        margin-right: 6px;\r\n      }\r\n    }\r\n    .dialogButton {\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      border-top: 1px solid #d0d3db;\r\n      padding-top: 16px;\r\n    }\r\n  }\r\n  .header {\r\n    display: flex;\r\n    // align-items: center;\r\n    justify-content: space-between;\r\n    height: 22px;\r\n    > span {\r\n      font-weight: bold;\r\n    }\r\n    .right {\r\n      font-family: Helvetica, Helvetica;\r\n      font-weight: bold;\r\n      font-size: 14px;\r\n      color: #298cfc;\r\n      line-height: 0px;\r\n      text-align: left;\r\n      font-style: normal;\r\n      text-transform: none;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .unit {\r\n      font-family: Microsoft YaHei, Microsoft YaHei;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #999999;\r\n      font-style: normal;\r\n      text-transform: none;\r\n    }\r\n  }\r\n  .chartCardConent {\r\n    margin-top: -30px;\r\n    .chartCardItem {\r\n      display: flex;\r\n      justify-content: flex-end;\r\n    }\r\n  }\r\n  .content {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: row;\r\n\r\n    .left {\r\n      width: 50%;\r\n      .title {\r\n        font-weight: bold;\r\n      }\r\n    }\r\n    .right {\r\n      width: 50%;\r\n      .title {\r\n        font-weight: bold;\r\n      }\r\n    }\r\n  }\r\n\r\n  .tablenumber {\r\n    width: 30px;\r\n    height: 23px;\r\n    background-size: 100%;\r\n    background-repeat: no-repeat;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    > span {\r\n      margin-top: 10px;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  ::v-deep .popover_latestAlarmInformation {\r\n    .item {\r\n      padding: 4px 0px;\r\n      display: flex;\r\n      span:first-of-type {\r\n        font-weight: 500;\r\n        font-size: 14px;\r\n        color: #999999;\r\n        width: 74px;\r\n      }\r\n      span:last-of-type {\r\n        font-weight: 500;\r\n        font-size: 14px;\r\n        color: #333333;\r\n      }\r\n    }\r\n  }\r\n\r\n  ::v-deep .el-card__header {\r\n    border-bottom: none !important;\r\n  }\r\n  ::v-deep .el-progress__text {\r\n    font-size: 18px !important;\r\n    color: #666666 !important;\r\n    // font-weight: bold;\r\n  }\r\n  ::v-deep.el-table .row-one {\r\n    background: rgba(41, 141, 255, 0.03) !important;\r\n  }\r\n\r\n  ::v-deep .el-table .row-two {\r\n    background: rgba(255, 255, 255, 1) !important;\r\n  }\r\n\r\n  ::v-deep .el-table .row-header {\r\n    color: #333333 !important;\r\n    font-weight: 500;\r\n  }\r\n  ::v-deep .el-table .row-body {\r\n    color: #333333 !important;\r\n    border: none !important;\r\n  }\r\n  ::v-deep .el-card {\r\n    border: none !important;\r\n  }\r\n  ::v-deep .el-table::before {\r\n    background: none !important;\r\n  }\r\n  ::v-deep .el-table th.is-leaf {\r\n    border-bottom: none !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}