{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\szcjPJEquipmentAssetList\\index_old.vue?vue&type=style&index=0&id=5fde39b6&scoped=true&lang=css", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\szcjPJEquipmentAssetList\\index_old.vue", "mtime": 1755674552420}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouc3pjalBKRXF1aXBtZW50QXNzZXRMaXN0ew0KICAvKiBoZWlnaHQ6IGNhbGMoMTAwdmggLSA5MHB4KTsgKi8NCiAgLyogb3ZlcmZsb3c6IGhpZGRlbjsgKi8NCn0NCg=="}, {"version": 3, "sources": ["index_old.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8pBA;AACA;AACA;AACA", "file": "index_old.vue", "sourceRoot": "src/views/business/equipmentManagement/szcjPJEquipmentAssetList", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 szcjPJEquipmentAssetList\">\r\n    <custom-layout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"submitForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </custom-layout>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      width=\"30%\"\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport {\r\n  DeleteEquipmentAssetEntity,\r\n  ExportEquipmentAssetsList,\r\n  GetEquipmentAssetPageList,\r\n  AssetImportTemplatePJ,\r\n  AssetEquipmentImportPJ,\r\n  ExportEquipmentListPJ,\r\n  ExportEquipmentListPJByData,\r\n  GetEquipmentAssetPageListPJ,\r\n  GetEquipmentAssetPageListPJByData,\r\n  GetDictionaryDetailListByParentId\r\n} from '@/api/business/eqptAsset'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { timeFormat } from '@/filters'\r\nimport { getDictionary } from '@/utils/common'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport Print from './components/print.vue'\r\nimport importDialog from '@/views/business/energyManagement/components/import.vue'\r\nimport exportInfo from '@/views/business/energyManagement/mixins/export.js'\r\nexport default {\r\n  name: 'EquipmentAssetList',\r\n  components: { CustomTable, CustomLayout, CustomForm, Print, importDialog },\r\n  mixins: [addRouterPage, exportInfo],\r\n  data() {\r\n    return {\r\n      componentsConfig: {\r\n        interfaceName: AssetEquipmentImportPJ\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.fetchData()\r\n        }\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/add',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'EquipmentAssetListAdd',\r\n          meta: { title: `新增` }\r\n        },\r\n        {\r\n          path: this.$route.path + '/edit',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'EquipmentAssetListEdit',\r\n          meta: { title: `编辑` }\r\n        },\r\n        {\r\n          path: this.$route.path + '/view',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'EquipmentAssetListView',\r\n          meta: { title: `查看` }\r\n        },\r\n        {\r\n          path: this.$route.path + '/dataAcquisition',\r\n          hidden: true,\r\n          component: () => import('./dataAcquisition.vue'),\r\n          name: 'DataAcquisition',\r\n          meta: { title: `查看数据` }\r\n        },\r\n        {\r\n          path: this.$route.path + '/equipmentData',\r\n          hidden: true,\r\n          component: () => import('./equipmentData.vue'),\r\n          name: 'PJEquipmentData',\r\n          meta: { title: `设备数采` }\r\n        }\r\n      ],\r\n      ruleForm: {\r\n        EquipmentName: '',\r\n        departName: '',\r\n        EquipmentType: '',\r\n        EquipmentItemType: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'EquipmentName',\r\n            label: '设备名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentType',\r\n            label: '设备类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              this.customForm.formItems.find(\r\n                (v) => v.key === 'EquipmentItemType'\r\n              ).options = []\r\n              this.ruleForm.EquipmentItemType = ''\r\n              GetDictionaryDetailListByParentId(e).then((res) => {\r\n                this.customForm.formItems.find(\r\n                  (v) => v.key === 'EquipmentItemType'\r\n                ).options = res.Data.map((v) => {\r\n                  return {\r\n                    label: v.Display_Name,\r\n                    value: v.Id\r\n                  }\r\n                })\r\n              })\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentItemType',\r\n            label: '设备子类',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'departName',\r\n            label: '所属部门',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        // 表格\r\n        pageSizeOptions: [20, 40, 60, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: '设备名称',\r\n            key: 'Display_Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备SN',\r\n            key: 'Serial_Number',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备编号',\r\n            key: 'Device_Number',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '品牌',\r\n            key: 'Brand',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '规格型号',\r\n            key: 'Spec',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备类型',\r\n            key: 'Type_Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备子类',\r\n            key: 'Type_Detail_Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '厂家名称',\r\n            key: 'Manufacturer',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '厂家联系方式',\r\n            key: 'Manufacturer_Contact_Info',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '经销商',\r\n            key: 'Dealer',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '经销商联系方式',\r\n            key: 'Dealer_Contact_Info',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '工程师',\r\n            key: 'Engineer',\r\n            otherOptions: {\r\n              align: 'center'\r\n\r\n            }\r\n          },\r\n          {\r\n            label: '工程师联系方式',\r\n            key: 'Engineer_Contact_Info',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '所属部门',\r\n            key: 'Department',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '安装位置',\r\n            key: 'Position',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '安装时间',\r\n            key: 'Install_Date',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备管理员',\r\n            key: 'Administrator',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备管理员联系方式',\r\n            key: 'Administrator_Contact_Info',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '用途',\r\n            key: 'Usage',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center',\r\n          width: '285px'\r\n        },\r\n        buttonConfig: {\r\n          buttonList: [\r\n            // {\r\n            //   text: \"新增\",\r\n            //   round: false, // 是否圆角\r\n            //   plain: false, // 是否朴素\r\n            //   circle: false, // 是否圆形\r\n            //   loading: false, // 是否加载中\r\n            //   disabled: false, // 是否禁用\r\n            //   icon: \"\", //  图标\r\n            //   autofocus: false, // 是否聚焦\r\n            //   type: \"primary\", // primary / success / warning / danger / info / text\r\n            //   size: \"small\", // medium / small / mini\r\n            //   onclick: (item) => {\r\n            //     console.log(item);\r\n            //     this.handleCreate();\r\n            //   },\r\n            // },\r\n            // {\r\n            //   text: \"下载模板\",\r\n            //   disabled: false, // 是否禁用\r\n            //   onclick: (item) => {\r\n            //     console.log(item);\r\n            //     this.handleDownTemplate();\r\n            //   },\r\n            // },\r\n            // {\r\n            //   text: \"批量导入\",\r\n            //   disabled: false, // 是否禁用\r\n            //   onclick: (item) => {\r\n            //     console.log(item);\r\n            //     this.currentComponent = \"importDialog\";\r\n            //     this.dialogVisible = true;\r\n            //     this.dialogTitle = \"批量导入\";\r\n            //   },\r\n            // },\r\n            {\r\n              text: '批量导出',\r\n              disabled: false,\r\n              onclick: () => {\r\n                this.handleExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        tableActionsWidth: 280,\r\n        tableActions: [\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '打印二维码',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handlePrintQr(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleInfo(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '查看数据',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.viewData(row)\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      multipleSelection: [],\r\n      currentComponent: 'Print',\r\n      dialogVisible: false\r\n    }\r\n  },\r\n  // watch: {\r\n  //   \"multipleSelection.length\": {\r\n  //     handler(newValue) {\r\n  //       this.customTableConfig.buttonConfig.buttonList.find(\r\n  //         (item) => item.text == \"批量导出\"\r\n  //       ).disabled = !newValue;\r\n  //     },\r\n  //     immediate: true,\r\n  //   },\r\n  // },\r\n  mounted() {\r\n    // this.getGridByCode(\"EquipmentAssetList\");\r\n    this.fetchData()\r\n    getDictionary('deviceType').then((res) => {\r\n      const item = this.customForm.formItems.find(\r\n        (v) => v.key === 'EquipmentType'\r\n      )\r\n      console.log('res', res, item)\r\n      item.options = res.map((v) => {\r\n        return {\r\n          label: v.Display_Name,\r\n          value: v.Id\r\n        }\r\n      })\r\n    })\r\n  },\r\n  activated() {\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    resetForm() {\r\n      this.ruleForm = {}\r\n      this.customForm.formItems.find(\r\n        (v) => v.key === 'EquipmentItemType'\r\n      ).options = []\r\n      this.fetchData()\r\n    },\r\n    submitForm() {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData()\r\n    },\r\n    fetchData() {\r\n      GetEquipmentAssetPageListPJByData({\r\n        Display_Name: this.ruleForm.EquipmentName,\r\n        Device_Type_Id: this.ruleForm.EquipmentType,\r\n        Device_Type_Detail_Id: this.ruleForm.EquipmentItemType,\r\n        Department: this.ruleForm.departName,\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.tableData = res.Data.Data.map((v) => {\r\n            v.Install_Date = timeFormat(v.Install_Date)\r\n            return v\r\n          })\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleCreate() {\r\n      // this.dialogTitle = '新增'\r\n      // this.dialogVisible = true\r\n      this.$router.push({\r\n        name: 'EquipmentAssetListAdd',\r\n        query: { pg_redirect: this.$route.name, type: 1 }\r\n      })\r\n    },\r\n    handleSizeChange(val) {\r\n      this.customTableConfig.pageSize = val\r\n      this.fetchData({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: val\r\n      })\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.customTableConfig.currentPage = val\r\n      this.fetchData({ Page: val, PageSize: this.customTableConfig.pageSize })\r\n    },\r\n    handleSelectionChange(data) {\r\n      console.log(data)\r\n      this.multipleSelection = data\r\n    },\r\n    // handleExport() {\r\n    //   console.log('handleExport')\r\n    //   ExportEquipmentAssetsList({\r\n    //     ids: this.multipleSelection.map(v => v.Id)\r\n    //   }).then(res => {\r\n    //     if (res.IsSucceed) {\r\n    //       downloadFile(res.Data)\r\n    //     } else {\r\n    //       this.$message({\r\n    //         message: res.Message,\r\n    //         type: 'error'\r\n    //       })\r\n    //     }\r\n    //   })\r\n    // },\r\n    // v2 导出设备资产列表\r\n    handleExport() {\r\n      console.log('handleExport')\r\n      ExportEquipmentListPJByData({\r\n        Id: this.multipleSelection.map((v) => v.Id).toString(),\r\n        Display_Name: this.ruleForm.EquipmentName,\r\n        Device_Type_Id: this.ruleForm.EquipmentType,\r\n        Department: this.ruleForm.departName,\r\n        Device_Type_Detail_Id: this.ruleForm.EquipmentItemType\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleDelete(id) {\r\n      this.$confirm('是否删除该设备, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeleteEquipmentAssetEntity({ ids: [id] }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    handleEdit(id) {\r\n      this.$router.push({\r\n        name: 'EquipmentAssetListEdit',\r\n        query: { pg_redirect: this.$route.name, id, type: 2 }\r\n      })\r\n    },\r\n    handlePrintQr(v) {\r\n      this.dialogVisible = true\r\n      this.dialogTitle = '设备二维码'\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].setCode(v)\r\n      })\r\n    },\r\n    handleInfo(id) {\r\n      this.$router.push({\r\n        name: 'EquipmentAssetListView',\r\n        query: { pg_redirect: this.$route.name, id, type: 3 }\r\n      })\r\n    },\r\n    // getGridByCode(code) {\r\n    //   GetGridByCode({ code }).then((res) => {\r\n    //     console.log(res.Data);\r\n    //     if (res.IsSucceed) {\r\n    //       const Grid = res.Data.Grid;\r\n    //       this.customTableConfig.tableColumns = res.Data?.ColumnList.map(\r\n    //         (item) => {\r\n    //           return Object.assign(\r\n    //             {},\r\n    //             {\r\n    //               key: item.Code,\r\n    //               label: item.Display_Name,\r\n    //               width: item.Width,\r\n    //               otherOptions: {\r\n    //                 align: item.Align ? item.Align : \"center\",\r\n    //                 sortable: item.Is_Sort,\r\n    //                 fixed: item.Is_Frozen === false ? false : \"left\",\r\n    //                 Digit_Number: item.Digit_Number,\r\n    //               },\r\n    //             }\r\n    //           );\r\n    //         }\r\n    //       );\r\n    //       if (Grid.Is_Select) {\r\n    //         this.customTableConfig.tableColumns.unshift({\r\n    //           otherOptions: {\r\n    //             type: \"selection\",\r\n    //             align: \"center\",\r\n    //           },\r\n    //         });\r\n    //       }\r\n    //       this.customTableConfig.pageSize = Number(Grid.Row_Number);\r\n    //     }\r\n    //   });\r\n    // },\r\n    // 查看数据\r\n    viewData(data) {\r\n      data.num = 1\r\n      data.historyRouter = this.$route.name\r\n      this.$router.push({\r\n        name: 'PJEquipmentData',\r\n        query: { pg_redirect: this.$route.name, data }\r\n      })\r\n\r\n      this.$store.dispatch('eqpt/changeEqptData', data)\r\n    },\r\n    // 下载模板\r\n    handleDownTemplate() {\r\n      AssetImportTemplatePJ({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data, '设备资产导入模板')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.szcjPJEquipmentAssetList{\r\n  /* height: calc(100vh - 90px); */\r\n  /* overflow: hidden; */\r\n}\r\n</style>\r\n"]}]}