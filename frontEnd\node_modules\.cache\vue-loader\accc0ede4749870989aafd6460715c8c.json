{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\water\\components\\bar.vue?vue&type=style&index=0&id=1018ffe4&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\water\\components\\bar.vue", "mtime": 1755743644481}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouY2hhcnQtY29udGFpbmVyIHsKICB3aWR0aDogMTAwJTsKICBoZWlnaHQ6IDEwMCU7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIHBhZGRpbmc6IDE2cHggMjRweDsKICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogIGJhY2tncm91bmQ6ICNmZmY7CiAgLnRpdGxlIHsKICAgIGhlaWdodDogNDBweDsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgLy8ganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgcGFkZGluZy10b3A6IDE2cHg7CiAgICAuY291bnQgewogICAgICBmb250LXdlaWdodDogYm9sZDsKICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICBjb2xvcjogIzY2NjY2NjsKICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7CiAgICAgID4gc3BhbiB7CiAgICAgICAgbWFyZ2luOiAwIDVweDsKICAgICAgfQogICAgfQogICAgLmxlbmdlbmQgewogICAgICA+IHNwYW4gewogICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgICAgICB3aWR0aDogMTBweDsKICAgICAgICBoZWlnaHQ6IDRweDsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDhweDsKICAgICAgfQogICAgICBmb250LXNpemU6IDEycHg7CiAgICAgIGNvbG9yOiAjOTk5OTk5OwogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgfQogIH0KCiAgLmNzLWNoYXJ0IHsKICAgIGZsZXg6IDE7CgogICAgLm5vLWRhdGEgewogICAgICBmbGV4OiAxOwogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgICAgZm9udC1zaXplOiAyNnB4OwogICAgICB3aWR0aDogMTAwJTsKICAgICAgaGVpZ2h0OiAxMDAlOwogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["bar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "bar.vue", "sourceRoot": "src/views/business/energyManagement/pJEnergyAnalysis/water/components", "sourcesContent": ["<template>\n  <div class=\"chart-container\">\n    <div class=\"title\">\n      <div class=\"count\">\n        共计用水<span style=\"color: #00d3a7\">{{ Total }}</span>吨\n      </div>\n    </div>\n    <div class=\"cs-chart\">\n      <v-chart\n        ref=\"barChartRef\"\n        class=\"barChartDom\"\n        :option=\"barOptionRef\"\n        :autoresize=\"true\"\n      />\n    </div>\n  </div>\n</template>\n\n<script>\nimport VChart from 'vue-echarts'\nimport { use } from 'echarts/core'\nimport { CanvasRenderer } from 'echarts/renderers'\nimport { BarChart } from 'echarts/charts'\nimport {\n  GridComponent,\n  LegendComponent,\n  TooltipComponent,\n  TitleComponent,\n  DataZoomComponent\n} from 'echarts/components'\nimport { GetWaterAnalyseStatistic } from '@/api/business/pJEnergyAnalysis'\nuse([\n  CanvasRenderer,\n  BarChart,\n  DataZoomComponent,\n  GridComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent\n])\nexport default {\n  components: {\n    VChart\n  },\n  data() {\n    return {\n      barOptionRef: {\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: [],\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: false\n          }\n        },\n        grid: {\n          left: '2%',\n          right: '0%',\n          bottom: '10%',\n          containLabel: true\n        },\n        yAxis: {\n          type: 'value',\n          nameTextStyle: {\n            color: '#888888'\n          }\n        },\n        series: [\n          {\n            name: '共计用水',\n            data: [],\n            type: 'bar',\n            symbol: 'emptyCircle',\n            tooltip: {\n              valueFormatter: function(value) {\n                return value + ' 吨'\n              }\n            },\n            barWidth: 10,\n            itemStyle: {\n              color: '#00D3A7'\n            }\n          }\n        ]\n      },\n      Total: 0\n    }\n  },\n  computed: {\n    parentData() {\n      return {\n        DateType: this.DateType(),\n        StartTime: this.StartTime(),\n        EndTime: this.EndTime(),\n        randomInteger: this.randomInteger()\n      }\n    }\n  },\n  watch: {\n    parentData: {\n      handler(nv, ov) {\n        console.log(12)\n        this.fetchData()\n      }\n    }\n  },\n  created() {\n    this.fetchData()\n  },\n  inject: ['DateType', 'StartTime', 'EndTime', 'randomInteger'],\n  methods: {\n    async fetchData() {\n      const res = await GetWaterAnalyseStatistic(this.parentData)\n      if (res.IsSucceed) {\n        this.Total = res.Data.Total\n        const xAxisData = (res.Data.List ?? []).map(item => item.Key)\n        const seriesData = (res.Data.List ?? []).map(item => item.Value)\n        this.barOptionRef.xAxis.data = xAxisData\n        this.barOptionRef.series[0].data = seriesData\n      }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.chart-container {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  padding: 16px 24px;\n  box-sizing: border-box;\n  background: #fff;\n  .title {\n    height: 40px;\n    display: flex;\n    align-items: center;\n    // justify-content: space-between;\n    padding-top: 16px;\n    .count {\n      font-weight: bold;\n      font-size: 16px;\n      color: #666666;\n      margin-right: 8px;\n      > span {\n        margin: 0 5px;\n      }\n    }\n    .lengend {\n      > span {\n        display: inline-block;\n        width: 10px;\n        height: 4px;\n        margin-right: 8px;\n      }\n      font-size: 12px;\n      color: #999999;\n      display: flex;\n      align-items: center;\n    }\n  }\n\n  .cs-chart {\n    flex: 1;\n\n    .no-data {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 26px;\n      width: 100%;\n      height: 100%;\n    }\n  }\n}\n</style>\n"]}]}