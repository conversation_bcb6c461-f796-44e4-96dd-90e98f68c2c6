{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\components\\electricityLoss.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\components\\electricityLoss.vue", "mtime": 1755741159520}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["electricityLoss.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "electricityLoss.vue", "sourceRoot": "src/views/business/energyManagement/pJEnergyAnalysis/eleNew/components", "sourcesContent": ["<template>\n  <div class=\"electricityLoss\">\n    <div class=\"title\">\n      <div class=\"left\">\n        电量损耗<el-tooltip\n          class=\"item\"\n          content=\"正常的用电，高低压之间的转换不会小于2%\"\n          placement=\"top-start\"\n        >\n          <img src=\"@/assets/tooltip.png\" alt=\"\">\n        </el-tooltip>\n      </div>\n    </div>\n    <div\n      v-loading=\"loading\"\n      class=\"linearGradient\"\n      element-loading-text=\"加载中...\"\n    >\n      <div class=\"top\">\n        <div>\n          <p class=\"mb16\">电量损耗</p>\n          <p class=\"mb8\">\n            <b>{{ Value }}</b><span>度</span>\n          </p>\n        </div>\n      </div>\n      <div class=\"bottom\">\n        <p>\n          损耗率\n          <span :class=\"['customTag', Percent > 2 ? 'warning' : 'green']\">\n            {{ Percent > 2 ? \"损耗超限\" : \"损耗正常\" }}\n          </span>\n        </p>\n        <div class=\"chartBox\">\n          <v-chart\n            ref=\"pie2ChartRef\"\n            class=\"pie2ChartDom\"\n            :option=\"pie1OptionRef\"\n            :autoresize=\"true\"\n          />\n        </div>\n      </div>\n      <div class=\"divider\" />\n      <div class=\"tips\">高低压转换及线路损耗 <br>正常损耗率 < 2%</div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport VChart from 'vue-echarts'\nimport { use } from 'echarts/core'\nimport { CanvasRenderer } from 'echarts/renderers'\nimport { PieChart, GaugeChart } from 'echarts/charts'\nimport {\n  GridComponent,\n  LegendComponent,\n  TooltipComponent,\n  TitleComponent,\n  DataZoomComponent\n} from 'echarts/components'\nuse([\n  CanvasRenderer,\n  PieChart,\n  DataZoomComponent,\n  GridComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent,\n  GaugeChart\n])\nimport { GetElectricEnergyLoss } from '@/api/business/energyManagement'\nexport default {\n  components: {\n    VChart\n  },\n  data() {\n    return {\n      Value: 0,\n      Percent: 0,\n      Color: '#15C49C',\n      colorStops: [],\n      loading: true\n    }\n  },\n  computed: {\n    parentData() {\n      return {\n        DateType: this.DateType(),\n        StartTime: this.StartTime(),\n        randomInteger: this.randomInteger()\n      }\n    },\n    pie1OptionRef() {\n      return {\n        tooltip: {\n          show: false\n        },\n        series: [\n          {\n            silent: false,\n            type: 'gauge',\n            zlevel: 2,\n            startAngle: 0,\n            endAngle: 360,\n            clockwise: true,\n            radius: '100%',\n            splitNumber: 5,\n            avoidLabelOverlap: false,\n            axisLine: {\n              show: true,\n              lineStyle: {\n                color: [\n                  [this.Percent / 100, this.Color],\n                  [1, '#f0f2f8']\n                ],\n                width: 16\n              }\n            },\n            itemStyle: {\n              color: 'rgba(255,255,255,0)'\n            },\n            progress: {\n              show: true\n            },\n            axisTick: {\n              show: true,\n              splitNumber: 1,\n              distance: -16,\n              lineStyle: {\n                color: '#ffffff',\n                width: 3\n              },\n              length: 20\n            }, // 刻度样式\n            splitLine: {\n              show: false\n            },\n            axisLabel: {\n              show: false\n            },\n            pointer: {\n              show: false\n            },\n            title: {\n              show: false\n            },\n            detail: {\n              // formatter: \"{value}%\",\n              show: false\n            }\n          },\n          {\n            type: 'pie',\n            silent: true,\n            center: ['50%', '50%'],\n            radius: ['0%', '65%'],\n            avoidLabelOverlap: false,\n            zlevel: 3,\n            itemStyle: {\n              color: {\n                type: 'linear',\n                x: 0,\n                y: 1,\n                x2: 0,\n                y2: 0,\n                colorStops: this.colorStops\n              }\n              //   borderColor: 'rgba(41, 141, 255, 0.2)'\n            },\n            label: {\n              show: true,\n              position: 'center',\n              formatter: (pamars) => {\n                return `${this.Percent}%`\n              },\n              fontSize: 18,\n              color: '#3f4652'\n            },\n            labelLine: {\n              show: false\n            },\n            data: [1]\n          }\n        ]\n      }\n    }\n  },\n  watch: {\n    parentData: {\n      handler(nv, ov) {\n        this.getElectricEnergyLoss()\n      }\n    }\n  },\n  created() {\n    this.getElectricEnergyLoss()\n  },\n  mounted() {},\n  inject: ['DateType', 'StartTime', 'randomInteger'],\n  methods: {\n    async getElectricEnergyLoss() {\n      this.loading = true\n      const res = await GetElectricEnergyLoss(this.parentData)\n      if (res.IsSucceed) {\n        const { Value, Percent } = res.Data\n        this.Value = Value\n        this.Percent = Percent\n        if (this.Percent < 2) {\n          this.Color = '#15C49C'\n          this.colorStops = [\n            {\n              offset: 0,\n              color: 'rgba(217, 247, 233, 1)'\n            },\n            {\n              offset: 1,\n              color: 'rgba(247, 252, 254, 1)'\n            }\n          ]\n        } else {\n          this.Color = '#FFC62C'\n          this.colorStops = [\n            {\n              offset: 0,\n              color: 'rgba(255, 235, 223, 1)'\n            },\n            {\n              offset: 1,\n              color: 'rgba(255, 250, 247, 1)'\n            }\n          ]\n        }\n      }\n      this.loading = false\n    }\n  }\n}\n</script>\n<style scoped lang=\"scss\">\n.electricityLoss {\n  height: 510px;\n  background: #fff;\n  border-radius: 4px;\n  width: 100%;\n  padding: 16px;\n  box-sizing: border-box;\n  margin-bottom: 16px;\n  .title {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n    .left {\n      color: #666;\n      font-weight: bold;\n      font-size: 16px;\n      img {\n        margin-left: 8px;\n      }\n    }\n  }\n  .linearGradient {\n    height: 440px;\n    border-radius: 2px;\n    box-sizing: border-box;\n    .top {\n      height: 128px;\n      margin-bottom: 35px;\n      border-radius: 2px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      border: 1px solid rgba(41, 141, 255, 0.1);\n      background: linear-gradient(\n        90deg,\n        rgba(41, 141, 255, 0.05) 0%,\n        rgba(41, 141, 255, 0) 100%\n      );\n      div {\n        min-width: 70px;\n      }\n      p {\n        font-size: 16px;\n        color: #1d2541;\n        b {\n          color: #394f7f;\n          font-size: 28px;\n        }\n        span {\n          color: #999;\n          font-size: 14px;\n          margin-left: 8px;\n        }\n      }\n    }\n    .divider {\n      width: 100%;\n      height: 1px;\n      background: #eee;\n      margin-bottom: 24px;\n    }\n    .bottom {\n      text-align: center;\n      color: #1d2541;\n      font-size: 16px;\n      .customTag {\n        display: inline-block;\n        width: 56px;\n        height: 20px;\n        line-height: 20px;\n        font-size: 12px;\n        border-radius: 2px;\n        margin-left: 16px;\n      }\n      .warning {\n        color: #ff902c;\n        background: rgba(255, 144, 44, 0.1);\n      }\n      .green {\n        color: #4ebf8b;\n        background: rgba(78, 191, 139, 0.1);\n      }\n      .chartBox {\n        height: 200px;\n        padding: 20px;\n      }\n    }\n  }\n  .tips {\n    color: #b8bec8;\n    text-align: center;\n    font-size: 12px;\n    line-height: 18px;\n  }\n  .mb8 {\n    margin-bottom: 8px;\n  }\n  .mb16 {\n    margin-bottom: 16px;\n  }\n  .mb24 {\n    margin-bottom: 24px;\n  }\n}\n</style>\n"]}]}