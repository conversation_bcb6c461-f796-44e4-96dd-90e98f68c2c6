<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      title="查看"
      :visible.sync="dialogVisible"
      width="600px"
    >
      <el-image v-if="PassImg" :src="PassImg" class="imgwapper" />
      <div v-else class="empty-img">暂无图片</div>
    </el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";
import {
  VBPassRecordGetDropList,
  VBPassRecordGetPassRecordList,
  VBPassRecordGetPassRecordDetail,
  VBPassRecordExportData,
} from "@/api/business/vehicleBarrier.js";
import exportInfo from "@/views/business/vehicleBarrier/mixins/export.js";
import addRouterPage from "@/mixins/add-router-page";
import dayjs from "dayjs";
export default {
  Name: "vehiclePeerRecord",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
  },
  mixins: [exportInfo, addRouterPage],
  data() {
    return {
      ruleForm: {
        Number: "",
        StartTime: null,
        EndTime: null,
        AccessType: "",
        UserName: "",
        PassType: "",
        PassMode: "",
        Date: [],
      },
      dialogVisible: false,
      PassImg: "", // 图片
      vehicleTypeOption: [], // 车辆类型
      tableSelection: [],
      selectIds: [],
      customForm: {
        formItems: [
          {
            key: "Number", // 字段ID
            label: "车牌号码", // Form的label
            type: "input", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
            },
            input: (e) => {},
            change: () => {},
          },
          {
            key: "Date",
            label: "通行时间",
            type: "datePicker",
            otherOptions: {
              type: "datetimerange",
              rangeSeparator: "至",
              startPlaceholder: "开始日期",
              endPlaceholder: "结束日期",
              clearable: true,
              valueFormat: "yyyy-MM-dd HH:mm",
            },
            change: (e) => {
              // this.ruleForm.StartTime = e[0];
              // this.ruleForm.EndTime = e[1];
              if (e && e.length > 0) {
                this.ruleForm.StartTime = e[0];
                this.ruleForm.EndTime = e[1];
              } else {
                this.ruleForm.StartTime = null;
                this.ruleForm.EndTime = null;
              }
            },
          },
          {
            key: "AccessType",
            label: "访问类型",
            type: "select",
            options: [],
            otherOptions: {
              clearable: true,
            },
            change: (e) => {},
          },
          {
            key: "UserName",
            label: "车主姓名",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            input: (e) => {},
            change: () => {},
          },
          {
            key: "PassType",
            label: "过车方向",
            type: "select",
            options: [],
            otherOptions: {
              clearable: true,
            },
            change: (e) => {},
          },
          {
            key: "PassMode",
            label: "通行方式",
            type: "select",
            options: [],
            otherOptions: {
              clearable: true,
            },
            change: (e) => {},
          },
        ],
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              key: "batch",
              disabled: false, // 是否禁用
              text: "批量导出",
              onclick: (item) => {
                console.log(item);
                this.ExportData(
                  this.ruleForm,
                  "车辆通行记录",
                  VBPassRecordExportData
                );
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        height: "100%",
        tableColumns: [
          // {
          //   width: 50,
          //   otherOptions: {
          //     type: "selection",
          //     align: "center",
          //   },
          // },
          {
            label: "通行时间",
            key: "PassTime",
            otherOptions: {
              fixed: "left",
            },
          },
          {
            label: "车牌号码",
            key: "Number",
            otherOptions: {
              fixed: "left",
            },
          },
          {
            label: "车辆类型",
            key: "VehicleType",
          },
          {
            label: "车主姓名",
            key: "UserName",
          },
          {
            label: "车主联系方式",
            key: "UserPhone",
          },
          {
            label: "访问类型",
            key: "AccessType",
          },
          {
            label: "过车方向",
            key: "PassType",
          },
          {
            label: "出入口",
            key: "EntranceName",
          },
          {
            label: "通行方式",
            key: "PassMode",
          },
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: "查看",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.$router.push({
                name: "trafficRecordsView",
                query: { pg_redirect: this.$route.name, Id: row.Id },
              });
            },
          },
        ],
      },
      addPageArray: [
        {
          path: this.$route.path + "/view",
          hidden: true,
          component: () => import("./dialog/view.vue"),
          meta: { title: "内部车辆管理详情" },
          name: "trafficRecordsView",
        },
      ],
    };
  },
  created() {
    this.vBPassRecordGetDropList();
  },
  async mounted() {
    // 跳转设置默认参数
    let JumpParams = this.$qiankun.getMicroAppJumpParamsFn();
    if (JumpParams.isJump == "true") {
      let StartTime = dayjs(Number(JumpParams.StartTime)).format(
        "YYYY-MM-DD HH:mm"
      );
      let EndTime = dayjs(Number(JumpParams.EndTime)).format(
        "YYYY-MM-DD HH:mm"
      );
      this.ruleForm.PassType = JumpParams.PassType
      this.ruleForm.Date = [StartTime, EndTime];
      this.ruleForm.StartTime = StartTime;
      this.ruleForm.EndTime = EndTime;
    }
    this.onFresh()
  },
  beforeDestroy() {
    this.$qiankun.setMicroAppJumpParamsFn();
    this.ruleForm.StartTime = null;
    this.ruleForm.EndTime = null;
  },
  methods: {
    async vBPassRecordGetDropList() {
      let res = await VBPassRecordGetDropList({});
      if (res.IsSucceed) {
        this.customForm.formItems.find((v) => v.key == "AccessType").options =
          res.Data.find((v) => v.Name == "AccessType").List.map((item) => ({
            label: item.Key,
            value: item.Value,
          }));

        this.customForm.formItems.find((v) => v.key == "PassType").options =
          res.Data.find((v) => v.Name == "PassType").List.map((item) => ({
            label: item.Key,
            value: item.Value,
          }));

        this.customForm.formItems.find((v) => v.key == "PassMode").options =
          res.Data.find((v) => v.Name == "PassMode").List.map((item) => ({
            label: item.Key,
            value: item.Value,
          }));
      }
    },
    searchForm(data) {
      this.customTableConfig.currentPage = 1;
      console.log(data);
      this.onFresh();
    },
    resetForm() {
      this.onFresh();
    },
    onFresh() {
      this.fetchData();
    },
    async init() {
      await this.fetchData();
    },
    async fetchData() {
      if (this.ruleForm.Date.length == 0) {
        this.ruleForm.StartTime = null;
        this.ruleForm.EndTime = null;
      }
      const res = await VBPassRecordGetPassRecordList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data;
        this.customTableConfig.total = res.Data.Total;
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.onFresh();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.onFresh();
    },
    handleSelectionChange(selection) {
      const Ids = [];
      this.tableSelection = selection;
      this.tableSelection.forEach((item) => {
        Ids.push(item.Id);
      });
      console.log(Ids);
      this.selectIds = Ids;
      console.log(this.tableSelection);
    },
    handleview(row) {
      this.dialogVisible = true;
      this.PassImg = row.PassImg;
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/views/business/vehicleBarrier/index.scss";

.imgwapper {
  width: 100px;
  height: 100px;
}
.empty-img {
  text-align: center;
}
</style>
