<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        >
          <!-- <template #customBtn="{slotScope}"><el-button type="text" @click="handelStart(slotScope)">{{ slotScope.Status == 1 ? '停用' :'启用' }}</el-button></template> -->
        </CustomTable>
      </template>
    </CustomLayout>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import {
  GetOperateLogPageList,
  ExportExcelAsync,
  ClearOpearteLog
} from '@/api/business/lampManagement'
export default {
  components: {
    CustomTable,
    CustomForm,
    CustomLayout
  },
  data() {
    return {
      ruleForm: {
        EquipmentName: '',
        OperateType: '',
        StartTime: null,
        EndTime: null,
        ProjectName: '',
        Date: []
      },
      customForm: {
        formItems: [
          {
            key: 'EquipmentName',
            label: '设备查询',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            width: '240px',
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'OperateType',
            label: '操作类型',
            type: 'select',
            otherOptions: {
              clearable: true
            },
            options: [
              {
                label: '查看图纸',
                value: '查看图纸'
              },
              {
                label: '查看联系人',
                value: '查看联系人'
              },
              {
                label: '连接联系人',
                value: '连接联系人'
              },
              {
                label: '评价联系人',
                value: '评价联系人'
              }
            ],
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Date',
            label: '时间范围',
            type: 'datePicker',
            otherOptions: {
              type: 'datetimerange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              clearable: true,
              valueFormat: 'yyyy-MM-dd HH:mm'
            },
            change: (e) => {
              console.log(e)
              if (e && e.length !== 0) {
                this.ruleForm.StartTime = e[0]
                this.ruleForm.EndTime = e[1]
              } else {
                this.ruleForm.StartTime = null
                this.ruleForm.EndTime = null
              }
            }
          },
          {
            key: 'ProjectName',
            label: '项目名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'DrawingName',
            label: '图纸',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          }
        ],
        rules: {},
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [

            {
              key: 'batch',
              disabled: false, // 是否禁用
              text: '导出',
              onclick: (item) => {
                this.ExportData()
              }
            },
            {
              text: '清空',
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: false, // 是否禁用
              icon: '', //  图标
              autofocus: false, // 是否聚焦
              type: 'primary', // primary / success / warning / danger / info / text
              size: 'small', // medium / small / mini
              onclick: (index, row) => {
                this.handleDelete(index, row)
              }
            }
          ]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        height: '100%',
        tableColumns: [
          {
            label: '日志时间',
            key: 'CreateDate'
          },
          {
            label: '终端设备',
            key: 'EquipmentName'
          },
          {
            label: '设备ID',
            key: 'EquipmentId'
          },
          {
            label: '操作类型',
            key: 'OperateType'
          },
          {
            label: '操作人员',
            key: 'CreateUserName'
          },
          {
            label: 'IP地址',
            key: 'IPAddress'
          },
          {
            label: '项目名称',
            key: 'ProjectName'
          },
          {
            label: '图纸',
            key: 'DrawingName'
          },
          {
            label: '服务提供人',
            key: 'ServiceUserName'
          },
          {
            label: '内容',
            key: 'Content'
          }
        ],
        tableData: [],
        tableActions: [
        ]
      }
    }
  },
  mounted() {
    this.onFresh()
  },
  methods: {
    searchForm(data) {
      console.log(data)
      this.customTableConfig.currentPage = 1
      this.onFresh()
    },
    resetForm() {
      this.onFresh()
    },
    onFresh() {
      this.fetchData()
    },
    async fetchData() {
      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {
        this.ruleForm.StartTime = null
        this.ruleForm.EndTime = null
      }
      await GetOperateLogPageList({
        ...this.ruleForm,
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize
      }).then((res) => {
        if (res.IsSucceed) {
          this.customTableConfig.tableData = res.Data.Data
          this.customTableConfig.total = res.Data.Total
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    // 导出
    ExportData() {
      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {
        this.ruleForm.StartTime = null
        this.ruleForm.EndTime = null
      }
      ExportExcelAsync({ ...this.ruleForm,
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize }).then((res) => {
        const url = window.URL.createObjectURL(new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }))
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        // 文件名
        link.setAttribute('download', '操作日志.xlsx')
        document.body.appendChild(link)
        link.click()
      })
    },
    handleDelete(index, row) {
      console.log(index, row)
      this.$confirm('该操作将清空所有数据，是否继续？', {
        type: 'warning'
      })
        .then((_) => {
          ClearOpearteLog().then((res) => {
            if (res.IsSucceed) {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.customTableConfig.pageSize = 1
              this.onFresh()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        })
        .catch((_) => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.onFresh()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.onFresh()
    },
    handleSelectionChange(selection) {
      console.log(selection)
    }
  }
}
</script>

<style scoped lang="scss">
* {
  box-sizing: border-box;
}

.layout {
  height: 100%;
  width: 100%;
  position: absolute;
  ::v-deep {
    .CustomLayout {
      .layoutTable {
        height: 0;
        .CustomTable {
          height: 100%;
          display: flex;
          flex-direction: column;
          .table {
            flex: 1;
            height: 0;
            display: flex;
            flex-direction: column;
            .el-table {
              flex: 1;
              height: 0;
            }
          }
        }
      }
    }
  }
}
</style>
