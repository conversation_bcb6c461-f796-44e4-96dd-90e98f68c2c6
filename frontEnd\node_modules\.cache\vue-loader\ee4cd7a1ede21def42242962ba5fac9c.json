{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\myTasks\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\myTasks\\index.vue", "mtime": 1755674552430}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/myWorkBench/myTasks", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n        >\r\n          <!-- <template #customBtn=\"{slotScope}\"><el-button type=\"text\" @click=\"goHandelAlarm(slotScope)\">去处理</el-button></template> -->\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport {\r\n  GetTaskPageList,\r\n  GetTaskType,\r\n  GetTaskStatus\r\n} from '@/api/business/myWorkBench'\r\nexport default {\r\n  name: 'MyTasks',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        Name: '',\r\n        Type: '',\r\n        StartTime: null,\r\n        EndTime: null,\r\n        Status: null,\r\n        Date: []\r\n      },\r\n      taskTypeData: [],\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Name',\r\n            label: '任务名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Type',\r\n            label: '任务类型',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [\r\n\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Status',\r\n            label: '任务状态',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Date',\r\n            label: '开始时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              type: 'datetimerange',\r\n              rangeSeparator: '至',\r\n              startPlaceholder: '开始日期',\r\n              endPlaceholder: '结束日期',\r\n              clearable: true,\r\n              valueFormat: 'yyyy-MM-dd HH:mm'\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n              if (e && e.length !== 0) {\r\n                this.ruleForm.StartTime = e[0]\r\n                this.ruleForm.EndTime = e[1]\r\n              } else {\r\n                this.ruleForm.StartTime = null\r\n                this.ruleForm.EndTime = null\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        loading: false,\r\n        buttonConfig: {\r\n          buttonList: [\r\n\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          {\r\n            label: '任务状态',\r\n            key: 'StatusName'\r\n          },\r\n          {\r\n            label: '任务类型',\r\n            key: 'Type'\r\n          },\r\n          {\r\n            label: '任务名称',\r\n            key: 'Name'\r\n          },\r\n          {\r\n            label: '来源',\r\n            key: 'Source'\r\n          },\r\n          {\r\n            label: '业务模块',\r\n            key: 'Module'\r\n          },\r\n          {\r\n            label: '任务开始时间',\r\n            key: 'StartTime'\r\n          },\r\n          {\r\n            label: '计划完成时间',\r\n            key: 'EndTime'\r\n          },\r\n          {\r\n            label: '实际完成时间',\r\n            key: 'DoneTime'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '去处理',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.goHandelAlarm(row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getTaskType()\r\n    this.getTaskStatus()\r\n    this.onFresh()\r\n  },\r\n  methods: {\r\n    // 获取任务类型\r\n    getTaskType() {\r\n      GetTaskType({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const arr = []\r\n          const data = res.Data || null\r\n          data.forEach((item) => {\r\n            const obj = {\r\n              label: item.Name,\r\n              value: item.Value\r\n            }\r\n            arr.push(obj)\r\n          })\r\n          this.customForm.formItems.find((v) => v.key == 'Type').options = arr\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 获取任务类型\r\n    getTaskStatus() {\r\n      this.customTableConfig.loading = true\r\n      GetTaskStatus({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const arr = []\r\n          const data = res.Data || null\r\n          data.forEach((item) => {\r\n            const obj = {\r\n              label: item.Name,\r\n              value: item.Value\r\n            }\r\n            arr.push(obj)\r\n          })\r\n          this.customForm.formItems.find((v) => v.key == 'Status').options = arr\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.customTableConfig.loading = false\r\n      })\r\n    },\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.fetchData()\r\n    },\r\n    async fetchData() {\r\n      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null\r\n        this.ruleForm.EndTime = null\r\n      }\r\n      await GetTaskPageList({\r\n        ...this.ruleForm,\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.tableData = res.Data.Data\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 去处理路由跳转\r\n    goHandelAlarm(item) {\r\n      // this.$router.push({ name: item.Code })\r\n      console.log(item, 'item')\r\n      if (item.Url) {\r\n        let platform = '' // 子应用\r\n        if (item.ModuleName == '后台设置') {\r\n          platform = 'management'\r\n        } else {\r\n          platform = 'digitalfactory'\r\n        }\r\n        this.$qiankun.switchMicroAppFn(\r\n          platform,\r\n          item.ModuleCode,\r\n          item.ModuleId,\r\n          item.Url\r\n        )\r\n      } else {\r\n        const platform = 'digitalfactory'\r\n        const code = 'szgc'\r\n        const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\r\n        let url = ''\r\n        if (item.Module == '环境管理') {\r\n          url = '/business/environment/alarmInformation'\r\n        } else\r\n        if (item.Module == '访客管理') {\r\n          url = '/business/visitorList'\r\n          console.log('访客管理')\r\n        }\r\n        this.$qiankun.switchMicroAppFn(platform, code, id, url)\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.onFresh()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.onFresh()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.layout {\r\n  height: 100%;\r\n  width: 100%;\r\n  position: absolute;\r\n  ::v-deep {\r\n    .CustomLayout {\r\n      .layoutTable {\r\n        height: 0;\r\n        .CustomTable {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          .table {\r\n            flex: 1;\r\n            height: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            .el-table {\r\n              flex: 1;\r\n              height: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}