{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\myTasks\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\myTasks\\index.vue", "mtime": 1755506574396}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/myWorkBench/myTasks", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n        >\n          <!-- <template #customBtn=\"{slotScope}\"><el-button type=\"text\" @click=\"goHandelAlarm(slotScope)\">去处�?/el-button></template> -->\n        </CustomTable>\n      </template>\n    </CustomLayout>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\nimport {\n  GetTaskPageList,\n  GetTaskType,\n  GetTaskStatus\n} from '@/api/business/myWorkBench'\nexport default {\n  name: 'MyTasks',\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout\n  },\n  data() {\n    return {\n      ruleForm: {\n        Name: '',\n        Type: '',\n        StartTime: null,\n        EndTime: null,\n        Status: null,\n        Date: []\n      },\n      taskTypeData: [],\n      customForm: {\n        formItems: [\n          {\n            key: 'Name',\n            label: '任务名称',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            width: '240px',\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'Type',\n            label: '任务类型',\n            type: 'select',\n            otherOptions: {\n              clearable: true\n            },\n            options: [\n\n            ],\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'Status',\n            label: '任务状�?,\n            type: 'select',\n            otherOptions: {\n              clearable: true\n            },\n            options: [],\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'Date',\n            label: '开始时�?,\n            type: 'datePicker',\n            otherOptions: {\n              type: 'datetimerange',\n              rangeSeparator: '�?,\n              startPlaceholder: '开始日�?,\n              endPlaceholder: '结束日期',\n              clearable: true,\n              valueFormat: 'yyyy-MM-dd HH:mm'\n            },\n            change: (e) => {\n              console.log(e)\n              if (e && e.length !== 0) {\n                this.ruleForm.StartTime = e[0]\n                this.ruleForm.EndTime = e[1]\n              } else {\n                this.ruleForm.StartTime = null\n                this.ruleForm.EndTime = null\n              }\n            }\n          }\n        ],\n        rules: {},\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        loading: false,\n        buttonConfig: {\n          buttonList: [\n\n          ]\n        },\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        height: '100%',\n        tableColumns: [\n          {\n            label: '任务状�?,\n            key: 'StatusName'\n          },\n          {\n            label: '任务类型',\n            key: 'Type'\n          },\n          {\n            label: '任务名称',\n            key: 'Name'\n          },\n          {\n            label: '来源',\n            key: 'Source'\n          },\n          {\n            label: '业务模块',\n            key: 'Module'\n          },\n          {\n            label: '任务开始时�?,\n            key: 'StartTime'\n          },\n          {\n            label: '计划完成时间',\n            key: 'EndTime'\n          },\n          {\n            label: '实际完成时间',\n            key: 'DoneTime'\n          }\n        ],\n        tableData: [],\n        tableActions: [\n          {\n            actionLabel: '去处�?,\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.goHandelAlarm(row)\n            }\n          }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.getTaskType()\n    this.getTaskStatus()\n    this.onFresh()\n  },\n  methods: {\n    // 获取任务类型\n    getTaskType() {\n      GetTaskType({}).then((res) => {\n        if (res.IsSucceed) {\n          const arr = []\n          const data = res.Data || null\n          data.forEach((item) => {\n            const obj = {\n              label: item.Name,\n              value: item.Value\n            }\n            arr.push(obj)\n          })\n          this.customForm.formItems.find((v) => v.key == 'Type').options = arr\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    // 获取任务类型\n    getTaskStatus() {\n      this.customTableConfig.loading = true\n      GetTaskStatus({}).then((res) => {\n        if (res.IsSucceed) {\n          const arr = []\n          const data = res.Data || null\n          data.forEach((item) => {\n            const obj = {\n              label: item.Name,\n              value: item.Value\n            }\n            arr.push(obj)\n          })\n          this.customForm.formItems.find((v) => v.key == 'Status').options = arr\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      }).finally(() => {\n        this.customTableConfig.loading = false\n      })\n    },\n    searchForm(data) {\n      console.log(data)\n      this.onFresh()\n    },\n    resetForm() {\n      this.onFresh()\n    },\n    onFresh() {\n      this.fetchData()\n    },\n    async fetchData() {\n      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {\n        this.ruleForm.StartTime = null\n        this.ruleForm.EndTime = null\n      }\n      await GetTaskPageList({\n        ...this.ruleForm,\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.customTableConfig.tableData = res.Data.Data\n          this.customTableConfig.total = res.Data.TotalCount\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n\n    // 去处理路由跳�?    goHandelAlarm(item) {\n      // this.$router.push({ name: item.Code })\n      console.log(item, 'item')\n      if (item.Url) {\n        let platform = '' // 子应�?        if (item.ModuleName == '后台设置') {\n          platform = 'management'\n        } else {\n          platform = 'digitalfactory'\n        }\n        this.$qiankun.switchMicroAppFn(\n          platform,\n          item.ModuleCode,\n          item.ModuleId,\n          item.Url\n        )\n      } else {\n        const platform = 'digitalfactory'\n        const code = 'szgc'\n        const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\n        let url = ''\n        if (item.Module == '环境管理') {\n          url = '/business/environment/alarmInformation'\n        } else\n        if (item.Module == '访客管理') {\n          url = '/business/visitorList'\n          console.log('访客管理')\n        }\n        this.$qiankun.switchMicroAppFn(platform, code, id, url)\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.onFresh()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`)\n      this.customTableConfig.currentPage = val\n      this.onFresh()\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n* {\n  box-sizing: border-box;\n}\n\n.layout {\n  height: 100%;\n  width: 100%;\n  position: absolute;\n  ::v-deep {\n    .CustomLayout {\n      .layoutTable {\n        height: 0;\n        .CustomTable {\n          height: 100%;\n          display: flex;\n          flex-direction: column;\n          .table {\n            flex: 1;\n            height: 0;\n            display: flex;\n            flex-direction: column;\n            .el-table {\n              flex: 1;\n              height: 0;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}