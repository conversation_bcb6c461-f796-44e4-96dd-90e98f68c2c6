{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControlV2\\accessControlRecord\\index.vue?vue&type=style&index=0&id=30d15bbe&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControlV2\\accessControlRecord\\index.vue", "mtime": 1755674552410}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoubXQyMCB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQp9DQoubGF5b3V0IHsNCiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gOTBweCk7DQogIG92ZXJmbG93OiBhdXRvOw0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6eA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/accessControlV2/accessControlRecord", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        >\r\n          <template #formSlot=\"{ slotScope }\">\r\n            <el-tree-select\r\n              ref=\"treeSelectArea\"\r\n              v-model=\"ruleForm.Dept\"\r\n              :select-params=\"{\r\n                clearable: true,\r\n              }\"\r\n              @searchFun=\"searchFun\"\r\n              class=\"customTreeSelect\"\r\n              :tree-params=\"categoryOptions\"\r\n            />\r\n          </template>\r\n        </CustomForm>\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\nimport dayjs from \"dayjs\";\r\nimport {\r\n  ExportEntranceEquipmentList,\r\n  GetDictionaryDetailListByCode,\r\n  GetTrafficRecordPageList,\r\n  GetCompanyList,\r\n} from \"@/api/business/accessControl\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nimport { GetDepartmentTree } from \"@/api/sys\";\r\n\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      ruleForm: {\r\n        PName: \"\",\r\n        daterangeArr: \"\",\r\n        BeginTime: \"\",\r\n        EndTime: \"\",\r\n        EquipmentType: \"\",\r\n        Position: \"\",\r\n        TrafficType: \"\",\r\n        TrafficWay: \"\",\r\n        PType: \"\",\r\n        EquipmentName: \"\",\r\n        Phone: \"\",\r\n        Dept: \"\",\r\n        Company: \"\",\r\n      },\r\n      categoryOptions: {\r\n        \"default-expand-all\": true,\r\n        filterable: true,\r\n        clickParent: false,\r\n        data: [],\r\n        props: {\r\n          disabled: \"disabled\",\r\n          children: \"Children\",\r\n          label: \"Label\",\r\n          value: \"Id\",\r\n        },\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"PName\", // 字段ID\r\n            label: \"姓名\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            // width: '240px',\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"daterangeArr\",\r\n            label: \"通行时间\",\r\n            type: \"datePicker\",\r\n            otherOptions: {\r\n              rangeSeparator: \"至\",\r\n              startPlaceholder: \"开始日期\",\r\n              endPlaceholder: \"结束日期\",\r\n              type: \"daterange\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"EquipmentType\", // 字段ID\r\n            label: \"门禁类型\", // Form的label\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Position\", // 字段ID\r\n            label: \"安装位置\", // Form的label\r\n            type: \"input\",\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"TrafficWay\", // 字段ID\r\n            label: \"通行方式\", // Form的label\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"TrafficType\", // 字段ID\r\n            label: \"通行类型\", // Form的label\r\n            type: \"select\",\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            options: [\r\n              { label: \"进\", value: \"进\" },\r\n              { label: \"出\", value: \"出\" },\r\n            ],\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"PType\", // 字段ID\r\n            label: \"人员类型\", // Form的label\r\n            type: \"select\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"EquipmentName\", // 字段ID\r\n            label: \"门禁名称\", // Form的label\r\n            type: \"input\",\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Phone\", // 字段ID\r\n            label: \"联系方式\", // Form的label\r\n            type: \"input\",\r\n            otherOptions: {\r\n              placeholder: \"\",\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Dept\", // 字段ID\r\n            label: \"人员部门\", // Form的label\r\n            type: \"slot\",\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Company\", // 字段ID\r\n            label: \"所属公司\", // Form的label\r\n            type: \"select\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleExport();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: \"通行时间\",\r\n            key: \"TrafficTime\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"姓名\",\r\n            key: \"PName\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"联系方式\",\r\n            key: \"ContactWay\",\r\n          },\r\n          {\r\n            label: \"人员类型\",\r\n            key: \"PType\",\r\n          },\r\n          {\r\n            label: \"所属部门\",\r\n            key: \"Department\",\r\n          },\r\n          {\r\n            label: \"所属公司\",\r\n            key: \"CompanyName\",\r\n          },\r\n          {\r\n            label: \"通行类型\",\r\n            key: \"TrafficType\",\r\n          },\r\n          {\r\n            label: \"设备类型\",\r\n            key: \"EquipmentType\",\r\n          },\r\n          {\r\n            label: \"设备名称\",\r\n            key: \"EquipmentName\",\r\n          },\r\n          {\r\n            label: \"设备位置\",\r\n            key: \"Position\",\r\n          },\r\n          {\r\n            label: \"通行方式\",\r\n            key: \"TrafficWay\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看详情\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/detail\",\r\n          hidden: true,\r\n          component: () => import(\"./detail.vue\"),\r\n          meta: { title: `门禁通行详情` },\r\n          name: \"accessControlRecordDetail\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  computed: {},\r\n  async created() {\r\n    // 门禁类型\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === \"EquipmentType\"\r\n    ).options = await this.initDeviceType(\"Entrance_Type\");\r\n    // 人员类型\r\n    this.customForm.formItems.find((item) => item.key === \"PType\").options =\r\n      await this.initDeviceType(\"P_Type\");\r\n    // 所属公司\r\n    this.customForm.formItems.find((item) => item.key === \"Company\").options =\r\n      await this.initGetCompanyList(\"P_Type\");\r\n    //\r\n    this.getDeptTreeData();\r\n    this.init();\r\n  },\r\n  mixins: [addRouterPage],\r\n  methods: {\r\n    async initDeviceType(code) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode: code,\r\n      });\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value,\r\n      }));\r\n      return options;\r\n    },\r\n    async initGetCompanyList(code) {\r\n      const res = await GetCompanyList({});\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Name,\r\n        value: item.Id,\r\n      }));\r\n      return options;\r\n    },\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1;\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm = {\r\n        PName: \"\",\r\n        daterangeArr: \"\",\r\n        BeginTime: \"\",\r\n        EndTime: \"\",\r\n        EquipmentType: \"\",\r\n        Position: \"\",\r\n        TrafficType: \"\",\r\n        TrafficWay: \"\",\r\n        PType: \"\",\r\n        EquipmentName: \"\",\r\n        Phone: \"\",\r\n        Dept: \"\",\r\n        Company: \"\",\r\n      };\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.getTrafficRecordPageList();\r\n    },\r\n    init() {\r\n      this.getTrafficRecordPageList();\r\n    },\r\n    async getTrafficRecordPageList() {\r\n      let BeginTime = \"\";\r\n      let EndTime = \"\";\r\n      const data = { ...this.ruleForm };\r\n      if ((this.ruleForm.daterangeArr ?? []).length > 0) {\r\n        BeginTime = dayjs(this.ruleForm.daterangeArr[0]).format(\"YYYY-MM-DD\");\r\n        EndTime = dayjs(this.ruleForm.daterangeArr[1]).format(\"YYYY-MM-DD\");\r\n      }\r\n      delete data.daterangeArr;\r\n      console.log({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...data,\r\n        BeginTime,\r\n        EndTime,\r\n      });\r\n      const res = await GetTrafficRecordPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...data,\r\n        BeginTime,\r\n        EndTime,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n          ...item,\r\n          Traffic_Time: dayjs(item.Traffic_Time).format(\"YYYY-MM-DD HH:mm:ss\"),\r\n        }));\r\n        console.log(res);\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    handleEdit(index, row) {\r\n      this.$router.push({\r\n        name: \"accessControlRecordDetail\",\r\n        query: { pg_redirect: this.$route.name, row: JSON.stringify(row) },\r\n      });\r\n    },\r\n    async handleExport() {\r\n      let BeginTime = \"\";\r\n      let EndTime = \"\";\r\n      if ((this.ruleForm.daterangeArr ?? []).length > 0) {\r\n        BeginTime = dayjs(this.ruleForm.daterangeArr[0]).format(\"YYYY-MM-DD\");\r\n        EndTime = dayjs(this.ruleForm.daterangeArr[1]).format(\"YYYY-MM-DD\");\r\n      }\r\n      const res = await ExportEntranceEquipmentList({\r\n        // id: this.tableSelection.map((item) => item.Id).join(','),\r\n        ...this.ruleForm,\r\n        BeginTime,\r\n        EndTime,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        downloadFile(res.Data, \"21\");\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.init();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.init();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    async getDeptTreeData() {\r\n      await GetDepartmentTree({ isAll: false }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.categoryOptions.data = res.Data;\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data);\r\n          });\r\n        }\r\n      });\r\n    },\r\n    searchFun(value) {\r\n      this.$refs.treeSelectArea.filterFun(value);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}