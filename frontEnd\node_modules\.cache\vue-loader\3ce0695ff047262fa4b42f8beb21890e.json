{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\intelligentControl\\components\\layoutCard.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\intelligentControl\\components\\layoutCard.vue", "mtime": 1755506761043}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBjb3VudFRvIGZyb20gJ3Z1ZS1jb3VudC10bycKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsgY291bnRUbyB9LAogIHByb3BzOiB7CiAgICBjYXJkTGlzdDogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4ge30KICB9LAogIGNvbXB1dGVkOiB7fQp9Cg=="}, {"version": 3, "sources": ["layoutCard.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "layoutCard.vue", "sourceRoot": "src/views/business/intelligentControl/components", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <div\n      class=\"card\"\n      v-for=\"(item, index) in cardList\"\n      :key=\"index\"\n      :style=\"{\n        backgroundColor: item.backgroundcolor,\n        height: '100px',\n        borderRadius: '8px',\n        margin: item.backgroundcolor ? '' : 'auto'\n      }\"\n    >\n      <div class=\"cardleft\" :style=\"item.backgroundcolor ? 'margin-right: 16px;' : ''\">\n        <img :src=\"item.cardimg\" />\n      </div>\n      <div class=\"cardright\">\n        <div class=\"title\">{{ item.cardtitle }}</div>\n        <div class=\"num\" :style=\"{ color: item.numcolor }\" v-if=\"item.cardtitle\">\n          <count-to\n            :startVal=\"0\"\n            :endVal=\"item.cardNum\"\n            :duration=\"50\"\n            :decimals=\"item.unit === '件' ? 0 : 3\"\n          ></count-to>\n          <span>{{ item.unit }}</span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport countTo from 'vue-count-to'\nexport default {\n  components: { countTo },\n  props: {\n    cardList: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {}\n  },\n  computed: {}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.layout {\n  height: 130px;\n  font-family: PingFang SC, PingFang SC;\n  display: flex;\n  .card {\n    flex: 1;\n    float: left;\n    margin: 0px 12px; /* 添加间距 */\n    display: flex;\n    justify-content: center; /* 水平居中 */\n    align-items: center; /* 垂直居中 */\n    background-color: #ffffff; /* 设置背景颜色 */\n\n    .cardleft {\n      float: left;\n    }\n    .cardright {\n      float: left;\n      font-style: normal;\n      .title {\n        height: 25px;\n        font-weight: bold;\n        font-size: 1rem;\n        color: #333333;\n        line-height: 25px;\n      }\n      .num {\n        height: 33px;\n        font-weight: 600;\n        font-size: 1.2rem;\n        line-height: 33px;\n      }\n    }\n  }\n}\n</style>\n"]}]}