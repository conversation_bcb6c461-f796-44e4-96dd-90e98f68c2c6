{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\taskCenter\\index.vue?vue&type=style&index=0&id=433d4a58&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\taskCenter\\index.vue", "mtime": 1755506574324}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoubGF5b3V0IHsKICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA5MHB4KTsKICB3aWR0aDogMTAwJTsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4aA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/eventManagement/taskCenter", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        />\n      </template>\n    </CustomLayout>\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <component\n        :is=\"currentComponent\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n      /></el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\nimport getGridByCode from '../../safetyManagement/mixins/index'\nimport DialogForm from './dialogForm.vue'\n\nimport { downloadFile } from '@/utils/downloadFile'\nimport dayjs from 'dayjs'\n\nimport {\n  GetTaskPageList,\n  UpdateTask,\n  GetTypesByModule\n} from '@/api/business/eventManagement'\nexport default {\n  name: '',\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout\n  },\n  mixins: [getGridByCode],\n  data() {\n    return {\n      currentComponent: DialogForm,\n      componentsConfig: {\n        Data: {}\n      },\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true\n        },\n        close: () => {\n          this.dialogVisible = false\n          this.onFresh()\n        }\n      },\n      dialogVisible: false,\n      dialogTitle: '编辑',\n      tableSelection: [],\n      ruleForm: {\n        TaskType: '',\n        TaskName: '',\n        Status: '0',\n        TaskBeg: null,\n        TaskEnd: null\n      },\n      customForm: {\n        formItems: [\n          {\n            key: 'TaskType',\n            label: '任务类型',\n            type: 'select',\n            options: [],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'TaskName',\n            label: '任务名称',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'Status',\n            label: '任务状�?,\n            type: 'select',\n            options: [\n              {\n                label: '全部',\n                value: '0'\n              },\n              {\n                label: '未完�?,\n                value: '1'\n              },\n              {\n                label: '已完�?',\n                value: '2'\n              },\n              {\n                label: '已超�?,\n                value: '3'\n              },\n              {\n                label: '超期完成',\n                value: '4'\n              }\n            ],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'Date', // 字段ID\n            label: '任务开始时�?, // Form的label\n            type: 'datePicker', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              type: 'daterange',\n              disabled: false,\n              placeholder: '请输�?..'\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n              if (e && e.length > 0) {\n                this.ruleForm.TaskBeg = dayjs(e[0]).format('YYYY-MM-DD')\n                this.ruleForm.TaskEnd = dayjs(e[1]).format('YYYY-MM-DD')\n              }\n            }\n          }\n        ],\n        rules: {},\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        buttonConfig: {\n          buttonList: []\n          //   {\n          //     text: \"导出\",\n          //     onclick: (item) => {\n          //       console.log(item);\n          //       this.handleExport();\n          //     },\n          //   },\n          // ],\n        },\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        tableColumns: [\n          {\n            otherOptions: {\n              type: 'selection',\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '任务开始时�?,\n            key: 'BegTime',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '计划完成时间',\n            key: 'EndTime',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '实际完成时间',\n            key: 'DoneTime',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '任务状�?,\n            key: 'StatusDisplay',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '负责�?,\n            key: 'ActualReceiveUsersNames',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '任务名称',\n            key: 'Name',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n\n          {\n            label: '通知方式',\n            key: 'MessageNoticeModeDisplay',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '任务类型',\n            key: 'TaskType',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '来源',\n            key: 'SourceName',\n            width: 180,\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '业务模块',\n            key: 'Module',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '操作�?,\n            key: 'ModifyUserName',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '备注',\n            key: 'Remark',\n            otherOptions: {\n              align: 'center'\n            }\n          }\n        ],\n        tableData: [],\n        operateOptions: {\n          align: 'center',\n          width: '180'\n        },\n        tableActions: [\n          {\n            actionLabel: '查看详情',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              // this.handleEdit(row);\n              const platform = 'digitalfactory'\n              const code = 'szgc'\n              const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\n              let url = ''\n              // if (row.Module == \"能耗管�?) {\n              //   url = \"/business/energy/alarmDetail\";\n              // } else if (row.Module == \"车辆道闸\") {\n              //   url = \"/bussiness/vehicle/alarm-info\";\n              // } else if (row.Module == \"门禁管理\") {\n              //   url = \"/business/AccessControlAlarmDetails\";\n              // } else if (row.Module == \"安防管理\") {\n              //   url = \"/business/equipmentAlarm\";\n              // } else if (row.Module == \"危化品管�?) {\n              //   url = \"/business/hazchem/alarmInformation\";\n              // } else\n              if (row.Module == '环境管理') {\n                url = '/business/environment/alarmInformation'\n              } else if (row.Module == '访客管理') {\n                url = '/business/visitorList'\n                console.log('访客管理')\n              }\n              this.$qiankun.switchMicroAppFn(platform, code, id, url)\n            }\n          },\n          {\n            actionLabel: '编辑',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.handleEdit(row)\n            }\n          }\n        ]\n      }\n    }\n  },\n  computed: {},\n  created() {\n    this.init()\n    this.GetTypesByModule()\n  },\n  methods: {\n    async GetTypesByModule() {\n      const res = await GetTypesByModule({\n        Type: '3',\n        Module: ''\n      })\n      console.log(res, 'res')\n      if (res.IsSucceed) {\n        const result = res.Data || []\n        const typeList = result.map((item) => ({\n          value: item,\n          label: item\n        }))\n        this.customForm.formItems.find(\n          (item) => item.key == 'TaskType'\n        ).options = typeList\n      }\n    },\n    searchForm(data) {\n      console.log(data)\n      this.customTableConfig.currentPage = 1\n      this.onFresh()\n    },\n    resetForm() {\n      this.ruleForm.TaskBeg = null\n      this.ruleForm.TaskEnd = null\n      this.ruleForm.Date = null\n      this.onFresh()\n    },\n    onFresh() {\n      this.getTaskPageList()\n    },\n\n    init() {\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\n      this.getTaskPageList()\n    },\n    async getTaskPageList() {\n      const res = await GetTaskPageList({\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n        ...this.ruleForm\n      })\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data\n        this.customTableConfig.total = res.Data.TotalCount\n        // if (this.customTableConfig.tableData.length > 0) {\n        //   this.customTableConfig.tableData.map((v) => {\n        //     v.Warning_First_Time =\n        //       (v.Warning_First_Time ?? \"\") != \"\"\n        //         ? dayjs(v.Warning_First_Time).format(\"YYYY-MM-DD HH:mm:ss\")\n        //         : \"\";\n        //     v.Warning_Last_Time =\n        //       (v.Warning_Last_Time ?? \"\") != \"\"\n        //         ? dayjs(v.Warning_Last_Time).format(\"YYYY-MM-DD HH:mm:ss\")\n        //         : \"\";\n        //   });\n        // }\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n    async handleExport() {\n      const res = await ExportEntranceWarning({\n        id: this.tableSelection.map((item) => item.Id).toString(),\n        code: 'AccessControlAlarmDetails1'\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        downloadFile(res.Data, '告警明细数据')\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.getTaskPageList()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`)\n      this.customTableConfig.currentPage = val\n      this.getTaskPageList()\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection\n    },\n    handleEdit(row) {\n      this.dialogVisible = true\n      this.componentsConfig.Data = row\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.layout {\n  height: calc(100vh - 90px);\n  width: 100%;\n}\n</style>\n"]}]}