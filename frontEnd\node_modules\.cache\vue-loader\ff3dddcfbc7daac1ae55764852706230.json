{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\taskCenter\\index.vue?vue&type=style&index=0&id=433d4a58&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\taskCenter\\index.vue", "mtime": 1755674552423}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5sYXlvdXQgew0KICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA5MHB4KTsNCiAgd2lkdGg6IDEwMCU7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6aA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/eventManagement/taskCenter", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport getGridByCode from '../../safetyManagement/mixins/index'\r\nimport DialogForm from './dialogForm.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport dayjs from 'dayjs'\r\n\r\nimport {\r\n  GetTaskPageList,\r\n  UpdateTask,\r\n  GetTypesByModule\r\n} from '@/api/business/eventManagement'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  mixins: [getGridByCode],\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '编辑',\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        TaskType: '',\r\n        TaskName: '',\r\n        Status: '0',\r\n        TaskBeg: null,\r\n        TaskEnd: null\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'TaskType',\r\n            label: '任务类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'TaskName',\r\n            label: '任务名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Status',\r\n            label: '任务状态',\r\n            type: 'select',\r\n            options: [\r\n              {\r\n                label: '全部',\r\n                value: '0'\r\n              },\r\n              {\r\n                label: '未完成',\r\n                value: '1'\r\n              },\r\n              {\r\n                label: '已完成 ',\r\n                value: '2'\r\n              },\r\n              {\r\n                label: '已超期',\r\n                value: '3'\r\n              },\r\n              {\r\n                label: '超期完成',\r\n                value: '4'\r\n              }\r\n            ],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Date', // 字段ID\r\n            label: '任务开始时间', // Form的label\r\n            type: 'datePicker', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              type: 'daterange',\r\n              disabled: false,\r\n              placeholder: '请输入...'\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.TaskBeg = dayjs(e[0]).format('YYYY-MM-DD')\r\n                this.ruleForm.TaskEnd = dayjs(e[1]).format('YYYY-MM-DD')\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: []\r\n          //   {\r\n          //     text: \"导出\",\r\n          //     onclick: (item) => {\r\n          //       console.log(item);\r\n          //       this.handleExport();\r\n          //     },\r\n          //   },\r\n          // ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center',\r\n              fixed: 'left'\r\n            }\r\n          },\r\n          {\r\n            label: '任务开始时间',\r\n            key: 'BegTime',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '计划完成时间',\r\n            key: 'EndTime',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '实际完成时间',\r\n            key: 'DoneTime',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '任务状态',\r\n            key: 'StatusDisplay',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '负责人',\r\n            key: 'ActualReceiveUsersNames',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '任务名称',\r\n            key: 'Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n\r\n          {\r\n            label: '通知方式',\r\n            key: 'MessageNoticeModeDisplay',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '任务类型',\r\n            key: 'TaskType',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '来源',\r\n            key: 'SourceName',\r\n            width: 180,\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '业务模块',\r\n            key: 'Module',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '操作人',\r\n            key: 'ModifyUserName',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '备注',\r\n            key: 'Remark',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center',\r\n          width: '180'\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              // this.handleEdit(row);\r\n              const platform = 'digitalfactory'\r\n              const code = 'szgc'\r\n              const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\r\n              let url = ''\r\n              // if (row.Module == \"能耗管理\") {\r\n              //   url = \"/business/energy/alarmDetail\";\r\n              // } else if (row.Module == \"车辆道闸\") {\r\n              //   url = \"/bussiness/vehicle/alarm-info\";\r\n              // } else if (row.Module == \"门禁管理\") {\r\n              //   url = \"/business/AccessControlAlarmDetails\";\r\n              // } else if (row.Module == \"安防管理\") {\r\n              //   url = \"/business/equipmentAlarm\";\r\n              // } else if (row.Module == \"危化品管理\") {\r\n              //   url = \"/business/hazchem/alarmInformation\";\r\n              // } else\r\n              if (row.Module == '环境管理') {\r\n                url = '/business/environment/alarmInformation'\r\n              } else if (row.Module == '访客管理') {\r\n                url = '/business/visitorList'\r\n                console.log('访客管理')\r\n              }\r\n              this.$qiankun.switchMicroAppFn(platform, code, id, url)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init()\r\n    this.GetTypesByModule()\r\n  },\r\n  methods: {\r\n    async GetTypesByModule() {\r\n      const res = await GetTypesByModule({\r\n        Type: '3',\r\n        Module: ''\r\n      })\r\n      console.log(res, 'res')\r\n      if (res.IsSucceed) {\r\n        const result = res.Data || []\r\n        const typeList = result.map((item) => ({\r\n          value: item,\r\n          label: item\r\n        }))\r\n        this.customForm.formItems.find(\r\n          (item) => item.key == 'TaskType'\r\n        ).options = typeList\r\n      }\r\n    },\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.TaskBeg = null\r\n      this.ruleForm.TaskEnd = null\r\n      this.ruleForm.Date = null\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getTaskPageList()\r\n    },\r\n\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.getTaskPageList()\r\n    },\r\n    async getTaskPageList() {\r\n      const res = await GetTaskPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n        // if (this.customTableConfig.tableData.length > 0) {\r\n        //   this.customTableConfig.tableData.map((v) => {\r\n        //     v.Warning_First_Time =\r\n        //       (v.Warning_First_Time ?? \"\") != \"\"\r\n        //         ? dayjs(v.Warning_First_Time).format(\"YYYY-MM-DD HH:mm:ss\")\r\n        //         : \"\";\r\n        //     v.Warning_Last_Time =\r\n        //       (v.Warning_Last_Time ?? \"\") != \"\"\r\n        //         ? dayjs(v.Warning_Last_Time).format(\"YYYY-MM-DD HH:mm:ss\")\r\n        //         : \"\";\r\n        //   });\r\n        // }\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    async handleExport() {\r\n      const res = await ExportEntranceWarning({\r\n        id: this.tableSelection.map((item) => item.Id).toString(),\r\n        code: 'AccessControlAlarmDetails1'\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '告警明细数据')\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.getTaskPageList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.getTaskPageList()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    },\r\n    handleEdit(row) {\r\n      this.dialogVisible = true\r\n      this.componentsConfig.Data = row\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  width: 100%;\r\n}\r\n</style>\r\n"]}]}