{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\equipmentAnalysis\\index.vue?vue&type=template&id=b8ce1e8c&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\equipmentAnalysis\\index.vue", "mtime": 1755674552419}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1724304688265}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}