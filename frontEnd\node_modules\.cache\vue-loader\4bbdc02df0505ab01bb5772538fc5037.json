{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\hazardousChemicals\\alarmConfiguration\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\hazardousChemicals\\alarmConfiguration\\index.vue", "mtime": 1755506574329}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk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file": "index.vue", "sourceRoot": "src/views/business/hazardousChemicals/alarmConfiguration", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport DialogForm from './dialogForm.vue'\r\nimport DialogFormLook from './dialogFormLook.vue'\r\n\r\n// import { downloadFile } from '@/utils/downloadFile'\r\nimport deviceTypeMixins from '../../mixins/index.js'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport {\r\n  GetQuotaList,\r\n  DeleteQuota,\r\n  DeleteAllQuota,\r\n  GetHazchemDTCList\r\n} from '@/api/business/hazardousChemicals'\r\n// import * as moment from 'moment'\r\nimport dayjs from 'dayjs'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  mixins: [deviceTypeMixins],\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        EquipmentTypeId: '',\r\n        AlarmType: '',\r\n        TriggerItem: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'EquipmentTypeId', // 字段ID\r\n            label: '设备类型', // Form的label\r\n            type: 'select', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            options: [],\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请输入设备类�?\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              this.customForm.formItems.find((item) => item.key === 'TriggerItem').otherOptions.disabled = !e\r\n              this.ruleForm.TriggerItem = ''\r\n              this.getEnviromentDTCList(GetHazchemDTCList, e)\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'TriggerItem',\r\n            label: '配置�?,\r\n            type: 'select',\r\n\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n              disabled: true,\r\n              placeholder: '请选择...'\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'AlarmType',\r\n            label: '告警类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n              placeholder: '请选择...'\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '新增',\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载�?\n              disabled: false, // 是否禁用\r\n              icon: '', //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleCreate()\r\n              }\r\n            },\r\n            {\r\n              text: '全部删除',\r\n              type: 'danger',\r\n              disabled: false, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleAllDelete(item)\r\n              }\r\n            }\r\n            // {\r\n            //   text: '导出',\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleExport()\r\n            //   }\r\n            // },\r\n            // {\r\n            //   text: '批量导出',\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleAllExport()\r\n            //   }\r\n            // }\r\n          ]\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          // {\r\n          //   width: 50,\r\n          //   otherOptions: {\r\n          //     type: 'selection',\r\n          //     align: 'center'\r\n          //   }\r\n          // },\r\n          {\r\n            width: 60,\r\n            label: '序号',\r\n            otherOptions: {\r\n              type: 'index',\r\n              align: 'center'\r\n            } // key\r\n            // otherOptions: {\r\n            //   width: 180, // 宽度\r\n            //   fixed: 'left', // left, right\r\n            //   align: 'center' //\tleft/center/right\r\n            // }\r\n          },\r\n          {\r\n            label: '设备类型',\r\n            key: 'EqtType',\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: '告警类型',\r\n            key: 'AlarmType'\r\n          },\r\n          {\r\n            label: '配置�?,\r\n            key: 'TriggerItem'\r\n          },\r\n          {\r\n            label: '对比方式',\r\n            key: 'ContrastModeStr'\r\n          },\r\n          {\r\n            label: '阈�?,\r\n            key: 'LimitValue'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 200\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  async mounted() {\r\n    this.customForm.formItems.find((v) => v.key === 'EquipmentTypeId').options = await this.getDictionaryDetailListByCode('HazchemEqtType', 'Id')\r\n    this.customForm.formItems.find((v) => v.key === 'AlarmType').options = await this.getDictionaryDetailListByCode('HazchemAlarmType', 'Value')\r\n    this.customForm.formItems.find((v) => v.key === 'EquipmentTypeId').options.unshift({ label: '全部', value: '' })\r\n    this.customForm.formItems.find((v) => v.key === 'AlarmType').options.unshift({ label: '全部', value: '' })\r\n    this.getEnviromentDTCList(GetHazchemDTCList, '')\r\n    this.init()\r\n    // this.initDeviceType('EqtType', 'HazchemEqtType')\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.customForm.formItems.find((item) => item.key === 'TriggerItem').otherOptions.disabled = !this.ruleForm.EquipmentTypeId\r\n      this.getEnviromentDTCList(GetHazchemDTCList, '')\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.GetQuotaList()\r\n    },\r\n    init() {\r\n      this.GetQuotaList()\r\n    },\r\n    async GetQuotaList() {\r\n      this.customTableConfig.loading = true\r\n      const res = await GetQuotaList({\r\n        ParameterJson: [\r\n          {\r\n            Key: '',\r\n            Value: [null],\r\n            Type: '',\r\n            Filter_Type: ''\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n\r\n        SortName: '',\r\n        SortOrder: '',\r\n        Search: '',\r\n        Content: '',\r\n        ...this.ruleForm\r\n      })\r\n      this.customTableConfig.loading = false\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n          ...item,\r\n          Date: dayjs(item.Date).format('YYYY-MM-DD HH:mm:ss')\r\n        }))\r\n\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n        console.log(this.customTableConfig.total)\r\n        this.customTableConfig.buttonConfig.buttonList.find(\r\n          (item) => item.text == '全部删除'\r\n        ).disabled = this.customTableConfig.total == 0\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n\r\n      this.componentsConfig = {\r\n        disabled: false,\r\n        title: '新增'\r\n      }\r\n      this.dialogVisible = true\r\n      this.currentComponent = DialogForm\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row)\r\n      console.log(this)\r\n      this.$confirm('该操作将删除当前配置，是否确认删除？', '删除', {\r\n        type: 'error'\r\n      })\r\n        .then(async(_) => {\r\n          const res = await DeleteQuota({\r\n            IDs: [row.ID]\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.init()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n        .catch((_) => {})\r\n    },\r\n    handleAllDelete(index, row) {\r\n      console.log(index, row)\r\n      console.log(this)\r\n      this.$confirm('该操作将删除全部配置，是否确认删除？', '删除', {\r\n        type: 'error'\r\n      })\r\n        .then(async(_) => {\r\n          const res = await DeleteAllQuota({\r\n            // IDs: [row.ID]\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.init()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n        .catch((_) => {})\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      this.dialogVisible = true\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.currentComponent = DialogFormLook\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: true,\r\n          title: '查看'\r\n        }\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        this.currentComponent = DialogForm\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: false,\r\n          title: '编辑'\r\n        }\r\n      }\r\n    },\r\n    // async handleExport() {\r\n    //   console.log(this.ruleForm)\r\n    //   const res = await ExportHazchemEquipment({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: false,\r\n    //     Ids: this.tableSelection.map((item) => item.ID),\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n    // async handleAllExport() {\r\n    //   const res = await ExportHazchemEquipment({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: true,\r\n    //     Ids: [],\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}