{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\workOrderStatistics\\repair\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\workOrderStatistics\\repair\\index.vue", "mtime": 1755674552421}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBHZXRXb3JrT3JkZXJNYW5hZ2VMaXN0LA0KICBHZXRXb3Jrb3JkZXJTdGF0aXN0aWNzLA0KICBHZXRUaW1lb3V0U3RhdGlzdGljcywNCiAgR2V0U2F0aXNmYWN0aW9uU3RhdGlzdGljcywNCiAgR2V0UHJvY2Vzc2VkUmFuaywNCiAgR2V0V29ya1Nob3BDYXNlLA0KICBHZXRXb3JrT3JkZXJUcmVuZCwNCiAgR2V0V29ya09yZGVyRXJyb3JUeXBlLA0KICBHZXRFcXVpcEZhaWx1cmVSYXRlUmFuaywNCiAgR2V0RGV2aWNlU2VydmljZWFiaWxpdHlSYXRlDQp9IGZyb20gJ0AvYXBpL2J1c2luZXNzL2VxdWlwbWVudE1hbmFnZW1lbnQnDQppbXBvcnQgZGF5anMgZnJvbSAnZGF5anMnDQppbXBvcnQgVkNoYXJ0IGZyb20gJ3Z1ZS1lY2hhcnRzJw0KaW1wb3J0IHsgdXNlIH0gZnJvbSAnZWNoYXJ0cy9jb3JlJw0KaW1wb3J0IHsgQ2FudmFzUmVuZGVyZXIgfSBmcm9tICdlY2hhcnRzL3JlbmRlcmVycycNCmltcG9ydCB7IEJhckNoYXJ0LCBMaW5lQ2hhcnQsIFBpZUNoYXJ0LCBHYXVnZUNoYXJ0IH0gZnJvbSAnZWNoYXJ0cy9jaGFydHMnDQoNCmltcG9ydCB7DQogIEdyaWRDb21wb25lbnQsDQogIExlZ2VuZENvbXBvbmVudCwNCiAgVG9vbHRpcENvbXBvbmVudCwNCiAgVGl0bGVDb21wb25lbnQsDQogIERhdGFab29tQ29tcG9uZW50LA0KICBUb29sYm94Q29tcG9uZW50DQp9IGZyb20gJ2VjaGFydHMvY29tcG9uZW50cycNCg0KaW1wb3J0IGVkaXREaWFsb2cgZnJvbSAnQC92aWV3cy9idXNpbmVzcy9tYWludGVuYW5jZUFuZFVwa2VlcC93b3JrT3JkZXJNYW5hZ2VtZW50L2VkaXREaWFsb2cudnVlJw0KdXNlKFsNCiAgQ2FudmFzUmVuZGVyZXIsDQogIEJhckNoYXJ0LA0KICBMaW5lQ2hhcnQsDQogIFBpZUNoYXJ0LA0KICBHYXVnZUNoYXJ0LA0KICBEYXRhWm9vbUNvbXBvbmVudCwNCiAgR3JpZENvbXBvbmVudCwNCiAgTGVnZW5kQ29tcG9uZW50LA0KICBUaXRsZUNvbXBvbmVudCwNCiAgVG9vbHRpcENvbXBvbmVudCwNCiAgVG9vbGJveENvbXBvbmVudA0KXSkNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0VxdWlwbWVudEFuYWx5c2lzJywNCiAgY29tcG9uZW50czogew0KICAgIFZDaGFydCwNCiAgICBlZGl0RGlhbG9nDQogIH0sDQogIG1peGluczogW10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOafpeeci+abtOWkmui3s+i9rOWIl+ihqA0KICAgICAganVtcFVybExpc3Q6IFtdLA0KICAgICAgLy8g5b6F5Yqe5oql5L+uDQogICAgICBwZW5kaW5nUmVwYWlyUmVxdWVzdFN0YXR1czogWw0KICAgICAgICB7DQogICAgICAgICAgdGV4dDogJ+W+heWkhOeQhicsDQogICAgICAgICAgdmFsdWU6ICcwJywNCiAgICAgICAgICBjb2xvcjogJyNGRjVFN0MnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0ZXh0OiAn5aSE55CG5LitJywNCiAgICAgICAgICB2YWx1ZTogJzEnLA0KICAgICAgICAgIGNvbG9yOiAnIzI5OERGRicNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRleHQ6ICflvoXlpI3mo4AnLA0KICAgICAgICAgIHZhbHVlOiAnMicsDQogICAgICAgICAgY29sb3I6ICcjRkY5MDJDJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGV4dDogJ+W+heivhOS7tycsDQogICAgICAgICAgdmFsdWU6ICczJywNCiAgICAgICAgICBjb2xvcjogJyMyOThERkYnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0ZXh0OiAn5aSE55CG5a6M5oiQJywNCiAgICAgICAgICB2YWx1ZTogJzQnLA0KICAgICAgICAgIGNvbG9yOiAnIzAwRDNBNycNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRleHQ6ICflt7LlhbPpl60nLA0KICAgICAgICAgIHZhbHVlOiAnNScsDQogICAgICAgICAgY29sb3I6ICcjMzMzMzMzJw0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgcGVuZGluZ1JlcGFpclJlcXVlc3REYXRhOiBbXSwNCiAgICAgIHJlcGFpck92ZXJ2aWV3OiB7fSwNCiAgICAgIHllYXJNb250aFJhZGlvOiAnMicsDQogICAgICB5ZWFyTW9udGhUeXBlOiAnbW9udGgnLA0KICAgICAgeWVhck1vbnRoVmFsdWU6IGRheWpzKG5ldyBEYXRlKCkpLmZvcm1hdCgnWVlZWS1NTScpLA0KICAgICAgc2Nyb2xsdGltZXI6ICcnLCAvLyDoh6rliqjmu5rliqjnmoTlrprml7bku7vliqENCiAgICAgIC8vIOWQhOi9pumXtOaKpeS/rui2i+WKvw0KICAgICAgdHJlbmRSZXBhaXJSZXBvcnRzVmFyaW91c1dvcmtzaG9wc09wdGlvbnM6IHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogew0KICAgICAgICAgICAgdHlwZTogJ3NoYWRvdycNCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIGxlZ2VuZDogew0KICAgICAgICAgIHRvcDogJzAnLA0KICAgICAgICAgIHJpZ2h0OiAnMCcsDQogICAgICAgICAgaXRlbVdpZHRoOiAxNiwNCiAgICAgICAgICBpdGVtSGVpZ2h0OiA4LA0KICAgICAgICAgIGl0ZW1HYXA6IDEwDQogICAgICAgIH0sDQogICAgICAgIGdyaWQ6IHsNCiAgICAgICAgICBsZWZ0OiAnMyUnLA0KICAgICAgICAgIHJpZ2h0OiAnNCUnLA0KICAgICAgICAgIGJvdHRvbTogJzMlJywNCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgY29sb3I6IFsnIzRFQkY4QicsICcjNjZDQkYwJywgJyMyOThERkYnLCAnI0ZGOTAyQyddLA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgZGF0YTogW10sDQogICAgICAgICAgYXhpc0xpbmU6IHsNCiAgICAgICAgICAgIHNob3c6IGZhbHNlDQogICAgICAgICAgfSwNCiAgICAgICAgICBheGlzVGljazogew0KICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICAgIHBvc2l0aW9uOiAnbGVmdCcsDQogICAgICAgICAgICBsb2dCYXNlOiAxMA0KICAgICAgICAgIH0NCiAgICAgICAgXSwNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgLy8gew0KICAgICAgICAgIC8vICAgbmFtZTogIuS4gOi9pumXtCIsDQogICAgICAgICAgLy8gICB0eXBlOiAiYmFyIiwNCiAgICAgICAgICAvLyAgIGJhcldpZHRoOiAyMCwNCiAgICAgICAgICAvLyAgIHN0YWNrOiAidmVoaWNsZSIsDQogICAgICAgICAgLy8gICBlbXBoYXNpczogew0KICAgICAgICAgIC8vICAgICBmb2N1czogInNlcmllcyIsDQogICAgICAgICAgLy8gICB9LA0KICAgICAgICAgIC8vICAgZGF0YTogW10sDQogICAgICAgICAgLy8gfSwNCiAgICAgICAgICAvLyB7DQogICAgICAgICAgLy8gICBuYW1lOiAi5LqM6L2m6Ze0IiwNCiAgICAgICAgICAvLyAgIHR5cGU6ICJiYXIiLA0KICAgICAgICAgIC8vICAgc3RhY2s6ICJ2ZWhpY2xlIiwNCiAgICAgICAgICAvLyAgIGVtcGhhc2lzOiB7DQogICAgICAgICAgLy8gICAgIGZvY3VzOiAic2VyaWVzIiwNCiAgICAgICAgICAvLyAgIH0sDQogICAgICAgICAgLy8gICBkYXRhOiBbXSwNCiAgICAgICAgICAvLyB9LA0KICAgICAgICAgIC8vIHsNCiAgICAgICAgICAvLyAgIG5hbWU6ICLphY3pgIHkuK3lv4MiLA0KICAgICAgICAgIC8vICAgdHlwZTogImJhciIsDQogICAgICAgICAgLy8gICBzdGFjazogInZlaGljbGUiLA0KICAgICAgICAgIC8vICAgZW1waGFzaXM6IHsNCiAgICAgICAgICAvLyAgICAgZm9jdXM6ICJzZXJpZXMiLA0KICAgICAgICAgIC8vICAgfSwNCiAgICAgICAgICAvLyAgIGRhdGE6IFtdLA0KICAgICAgICAgIC8vIH0sDQogICAgICAgICAgLy8gew0KICAgICAgICAgIC8vICAgbmFtZTogIuWTjeW6lOWPiuaXtueOhyIsDQogICAgICAgICAgLy8gICB0eXBlOiAibGluZSIsDQogICAgICAgICAgLy8gICBzbW9vdGg6IHRydWUsDQogICAgICAgICAgLy8gICBzeW1ib2w6ICJub25lIiwNCiAgICAgICAgICAvLyAgIHlBeGlzSW5kZXg6IDEsDQogICAgICAgICAgLy8gICB0b29sdGlwOiB7DQogICAgICAgICAgLy8gICAgIHZhbHVlRm9ybWF0dGVyOiBmdW5jdGlvbiAodmFsdWUpIHsNCiAgICAgICAgICAvLyAgICAgICByZXR1cm4gdmFsdWUgKyAiICUiOw0KICAgICAgICAgIC8vICAgICB9LA0KICAgICAgICAgIC8vICAgfSwNCiAgICAgICAgICAvLyAgIGRhdGE6IFtdLA0KICAgICAgICAgIC8vIH0sDQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICAvLyDlkITovabpl7TmiqXkv67mg4XlhrUNCiAgICAgIHJlcGFpclN0YXR1c0VhY2hXb3Jrc2hvcE9wdGlvbnM6IHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdpdGVtJywNCiAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uKHBhcmFtcykgew0KICAgICAgICAgICAgcmV0dXJuIGAgJHtwYXJhbXMubmFtZX0gJHtwYXJhbXMucGVyY2VudH0lICBgDQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBjb2xvcjogWycjNEVCRjhCJywgJyMyOThERkYnLCAnIzUxQTFGRCddLA0KICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICBvcmllbnQ6ICd2ZXJ0aWNhbCcsDQogICAgICAgICAgcmlnaHQ6ICcwJywNCiAgICAgICAgICBib3R0b206ICdjZW50ZXInLA0KICAgICAgICAgIGl0ZW1XaWR0aDogMTIsDQogICAgICAgICAgaXRlbUhlaWdodDogNiwNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgIGNvbG9yOiAncmdiYSgzNCwgNDAsIDUyLCAwLjY1KScNCiAgICAgICAgICB9LA0KICAgICAgICAgIHRleHRTdHlsZTogew0KICAgICAgICAgICAgcmljaDogew0KICAgICAgICAgICAgICBsYWJlbE1hcms6IHsNCiAgICAgICAgICAgICAgICB3aWR0aDogNjAsDQogICAgICAgICAgICAgICAgY29sb3I6ICcjMjIyODM0Jw0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICB2YWx1ZU1hcms6IHsNCiAgICAgICAgICAgICAgICB3aWR0aDogNDAsDQogICAgICAgICAgICAgICAgY29sb3I6ICcjNjZDQkYwJw0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICBwZXJjZW50TWFyazogew0KICAgICAgICAgICAgICAgIHdpZHRoOiA0MCwNCiAgICAgICAgICAgICAgICBjb2xvcjogJyMyOThERkYnDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHR5cGU6ICdwaWUnLA0KICAgICAgICAgICAgcmFkaXVzOiAnNTAlJywNCiAgICAgICAgICAgIHJpZ2h0OiAxNTAsDQogICAgICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgICAgIGxhYmVsTGluZTogew0KICAgICAgICAgICAgICAvLyDorr7nva7lu7bplb/nur/nmoTplb/luqYNCiAgICAgICAgICAgICAgbm9ybWFsOiB7DQogICAgICAgICAgICAgICAgbGVuZ3RoOiA1LCAvLyDorr7nva7lu7bplb/nur/nmoTplb/luqYNCiAgICAgICAgICAgICAgICBsZW5ndGgyOiAxMCwgLy8g6K6+572u56ys5LqM5q615bu26ZW/57q/55qE6ZW/5bqmDQogICAgICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMTk0LCAyMDMsIDIyNiwgMSknDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgbm9ybWFsOiB7DQogICAgICAgICAgICAgICAgLy8gZm9ybWF0dGVyOiAne2R9JSwge2N9IFxuXG4nLA0KICAgICAgICAgICAgICAgIGZvcm1hdHRlcjogJyB7Y3x7Yn19ICB7cGVyfHtkfSV9IFxue2hyfH1cbnthfH0nLCAvLyDov5nph4zmnIDlkI7lj6bkuIDooYzorr7nva7kuobkuIDkuKrnqbrmlbDmja7mmK/kuLrkuobog73orqnlu7bplb/nur/kuI5ocue6v+WvueaOpei1t+adpQ0KICAgICAgICAgICAgICAgIHBhZGRpbmc6IFswLCAtNF0sIC8vIOWPlua2iGhy57q/6Lef5bu26ZW/57q/5LmL6Ze055qE6Ze06ZqZDQogICAgICAgICAgICAgICAgcmljaDogew0KICAgICAgICAgICAgICAgICAgYTogew0KICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyM5OTknLA0KICAgICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAyMCwgLy8g6K6+572u5pyA5ZCO5LiA6KGM56m65pWw5o2u6auY5bqm77yM5Li65LqG6IO96K6p5bu26ZW/57q/5LiOaHLnur/lr7nmjqXotbfmnaUNCiAgICAgICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInDQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgaHI6IHsNCiAgICAgICAgICAgICAgICAgICAgLy8g6K6+572uaHLmmK/kuLrkuoborqnkuK3pl7Tnur/og73lpJ/oh6rpgILlupTplb/luqYNCiAgICAgICAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICdyZ2JhKDE5NCwgMjAzLCAyMjYsIDEpJywgLy8gaHLnmoTpopzoibLkuLphdXRv5pe25YCZ5Lya5Li75Yqo5pi+56S66aKc6Imy55qEDQogICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnMTA1JScsDQogICAgICAgICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAwLjUsDQogICAgICAgICAgICAgICAgICAgIGhlaWdodDogMC41DQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgcGVyOiB7DQogICAgICAgICAgICAgICAgICAgIC8vIOeUqOeZvuWIhuavlOaVsOaNruadpeiwg+aVtOS4i+aVsOWtl+S9jee9ru+8jOaYvueahOWlveeci+S6m+OAguWmguaenOS4jeiuvue9ru+8jGZvcm1hdHRlcuacgOWQjuS4gOihjOeahOepuuaVsOaNruWwseS4jemcgOimgQ0KICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiBbNCwgMF0sDQogICAgICAgICAgICAgICAgICAgIC8vIGNvbG9yOiAicmdiYSgxOTQsIDIwMywgMjI2LCAxKSIsDQogICAgICAgICAgICAgICAgICAgIGNvbG9yOiBmdW5jdGlvbihwYXJhbXMpIHsNCiAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhwYXJhbXMsICdwYXJhbXMtLS0tLS0tLS0tLS0tJykNCiAgICAgICAgICAgICAgICAgICAgICAvLyDpgJrov4fmlbDmja7pobnnmoTpopzoibLmnaXorr7nva7mloflrZfpopzoibINCiAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gcGFyYW1zLmNvbG9yDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBlbXBoYXNpczogew0KICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgICAgICBzaGFkb3dCbHVyOiAxMCwNCiAgICAgICAgICAgICAgICBzaGFkb3dPZmZzZXRYOiAwLA0KICAgICAgICAgICAgICAgIHNoYWRvd0NvbG9yOiAncmdiYSgwLCAwLCAwLCAwLjUpJw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgLy8g5oql5L+u5pWF6Zqc57G75Z6LDQogICAgICByZXBhaXJGYXVsdFR5cGVPcHRpb25zOiB7DQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnaXRlbScsDQogICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbihwYXJhbXMpIHsNCiAgICAgICAgICAgIHJldHVybiBgICR7cGFyYW1zLm5hbWV9ICR7cGFyYW1zLnBlcmNlbnR9JSAgYA0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgY29sb3I6IFsnIzRFQkY4QicsICcjMjk4REZGJywgJyM1MUExRkQnXSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgb3JpZW50OiAndmVydGljYWwnLA0KICAgICAgICAgIHJpZ2h0OiAnMCcsDQogICAgICAgICAgYm90dG9tOiAnY2VudGVyJywNCiAgICAgICAgICBpdGVtV2lkdGg6IDEyLA0KICAgICAgICAgIGl0ZW1IZWlnaHQ6IDYsDQogICAgICAgICAgdGV4dFN0eWxlOiB7DQogICAgICAgICAgICBjb2xvcjogJ3JnYmEoMzQsIDQwLCA1MiwgMC42NSknDQogICAgICAgICAgfSwNCiAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uKG5hbWUpIHsNCiAgICAgICAgICAgIHJldHVybiBgJHtuYW1lfWANCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHR5cGU6ICdwaWUnLA0KICAgICAgICAgICAgcmFkaXVzOiAnNTAlJywNCiAgICAgICAgICAgIC8vIHJpZ2h0OiAxMDAsDQogICAgICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgICAgIGxhYmVsTGluZTogew0KICAgICAgICAgICAgICAvLyDorr7nva7lu7bplb/nur/nmoTplb/luqYNCiAgICAgICAgICAgICAgbm9ybWFsOiB7DQogICAgICAgICAgICAgICAgbGVuZ3RoOiA1LCAvLyDorr7nva7lu7bplb/nur/nmoTplb/luqYNCiAgICAgICAgICAgICAgICBsZW5ndGgyOiAxMCwgLy8g6K6+572u56ys5LqM5q615bu26ZW/57q/55qE6ZW/5bqmDQogICAgICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMTk0LCAyMDMsIDIyNiwgMSknDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgbm9ybWFsOiB7DQogICAgICAgICAgICAgICAgLy8gZm9ybWF0dGVyOiAne2R9JSwge2N9IFxuXG4nLA0KICAgICAgICAgICAgICAgIGZvcm1hdHRlcjogJyB7Y3x7Yn19ICB7cGVyfHtkfSV9IFxue2hyfH1cbnthfH0nLCAvLyDov5nph4zmnIDlkI7lj6bkuIDooYzorr7nva7kuobkuIDkuKrnqbrmlbDmja7mmK/kuLrkuobog73orqnlu7bplb/nur/kuI5ocue6v+WvueaOpei1t+adpQ0KICAgICAgICAgICAgICAgIHBhZGRpbmc6IFswLCAtNF0sIC8vIOWPlua2iGhy57q/6Lef5bu26ZW/57q/5LmL6Ze055qE6Ze06ZqZDQogICAgICAgICAgICAgICAgcmljaDogew0KICAgICAgICAgICAgICAgICAgYTogew0KICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyM5OTknLA0KICAgICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAyMCwgLy8g6K6+572u5pyA5ZCO5LiA6KGM56m65pWw5o2u6auY5bqm77yM5Li65LqG6IO96K6p5bu26ZW/57q/5LiOaHLnur/lr7nmjqXotbfmnaUNCiAgICAgICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInDQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgaHI6IHsNCiAgICAgICAgICAgICAgICAgICAgLy8g6K6+572uaHLmmK/kuLrkuoborqnkuK3pl7Tnur/og73lpJ/oh6rpgILlupTplb/luqYNCiAgICAgICAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICdyZ2JhKDE5NCwgMjAzLCAyMjYsIDEpJywgLy8gaHLnmoTpopzoibLkuLphdXRv5pe25YCZ5Lya5Li75Yqo5pi+56S66aKc6Imy55qEDQogICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnMTA1JScsDQogICAgICAgICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAwLjUsDQogICAgICAgICAgICAgICAgICAgIGhlaWdodDogMC41DQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgcGVyOiB7DQogICAgICAgICAgICAgICAgICAgIC8vIOeUqOeZvuWIhuavlOaVsOaNruadpeiwg+aVtOS4i+aVsOWtl+S9jee9ru+8jOaYvueahOWlveeci+S6m+OAguWmguaenOS4jeiuvue9ru+8jGZvcm1hdHRlcuacgOWQjuS4gOihjOeahOepuuaVsOaNruWwseS4jemcgOimgQ0KICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiBbNCwgMF0NCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBlbXBoYXNpczogew0KICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgICAgICBzaGFkb3dCbHVyOiAxMCwNCiAgICAgICAgICAgICAgICBzaGFkb3dPZmZzZXRYOiAwLA0KICAgICAgICAgICAgICAgIHNoYWRvd0NvbG9yOiAncmdiYSgwLCAwLCAwLCAwLjUpJw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgLy8g5oql5L+u5ZON5bqUDQogICAgICByZXBhaXJSZXNwb25zZUNvbmZpZzoge30sDQogICAgICByZXBhaXJSZXNwb25zZU9wdGlvbnM6IHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogew0KICAgICAgICAgICAgdHlwZTogJ2Nyb3NzJywNCiAgICAgICAgICAgIGNyb3NzU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjOTk5Jw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgdG9wOiAnMCcsDQogICAgICAgICAgcmlnaHQ6ICcwJywNCiAgICAgICAgICBpdGVtV2lkdGg6IDE2LA0KICAgICAgICAgIGl0ZW1IZWlnaHQ6IDgsDQogICAgICAgICAgaXRlbUdhcDogMTANCiAgICAgICAgfSwNCiAgICAgICAgY29sb3I6IFsnIzI5OERGRicsICcjRkY5MDJDJywgJyMwMEQzQTcnXSwNCiAgICAgICAgeEF4aXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgICAgZGF0YTogW10sDQogICAgICAgICAgICBheGlzUG9pbnRlcjogew0KICAgICAgICAgICAgICB0eXBlOiAnc2hhZG93Jw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGF4aXNMaW5lOiB7DQogICAgICAgICAgICAgIHNob3c6IGZhbHNlDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYXhpc1RpY2s6IHsNCiAgICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIF0sDQogICAgICAgIHlBeGlzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJw0KICAgICAgICAgICAgLy8gbWluOiAwLA0KICAgICAgICAgICAgLy8gbWF4OiAyNTAsDQogICAgICAgICAgICAvLyBpbnRlcnZhbDogNTAsDQogICAgICAgICAgICAvLyBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIC8vICAgZm9ybWF0dGVyOiAie3ZhbHVlfSBtbCIsDQogICAgICAgICAgICAvLyB9LA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICAgIC8vIG1pbjogMCwNCiAgICAgICAgICAgIC8vIG1heDogMjUsDQogICAgICAgICAgICAvLyBpbnRlcnZhbDogNSwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgICBmb3JtYXR0ZXI6ICd7dmFsdWV9ICUnDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5ZON5bqU5Y+K5pe25pWwJywNCiAgICAgICAgICAgIHR5cGU6ICdiYXInLA0KICAgICAgICAgICAgYmFyV2lkdGg6IDIwLA0KICAgICAgICAgICAgc3RhY2s6ICd2ZWhpY2xlJywNCiAgICAgICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICAgICAgdmFsdWVGb3JtYXR0ZXI6IGZ1bmN0aW9uKHZhbHVlKSB7DQogICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBkYXRhOiBbXQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+WTjeW6lOi2heaXtuaVsCcsDQogICAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICAgIGJhcldpZHRoOiAyMCwNCiAgICAgICAgICAgIHN0YWNrOiAndmVoaWNsZScsDQogICAgICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgICAgIHZhbHVlRm9ybWF0dGVyOiBmdW5jdGlvbih2YWx1ZSkgew0KICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZGF0YTogW10NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICflk43lupTlj4rml7bnjocnLA0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgc21vb3RoOiB0cnVlLA0KICAgICAgICAgICAgc3ltYm9sOiAnbm9uZScsDQogICAgICAgICAgICB5QXhpc0luZGV4OiAxLA0KICAgICAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgICAgICB2YWx1ZUZvcm1hdHRlcjogZnVuY3Rpb24odmFsdWUpIHsNCiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWUgKyAnICUnDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBkYXRhOiBbXQ0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIC8vIOaKpeS/rua7oeaEj+W6pg0KICAgICAgcmVwYWlyU2F0aXNmYWN0aW9uQ29uZmlnOiB7fSwNCiAgICAgIHJlcGFpclNhdGlzZmFjdGlvbk9wdGlvbnM6IHsNCiAgICAgICAgc2VyaWVzOiBbXQ0KICAgICAgfSwNCiAgICAgIC8vIOiuvuWkh+WujOWlveeOhw0KICAgICAgZXF1aXBtZW50SW50ZWdyaXR5UmF0ZToge30sDQogICAgICBlcXVpcG1lbnRJbnRlZ3JpdHlSYXRlT3B0aW9uczogew0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgLy8g5aSW5ZyGDQogICAgICAgICAgICBzaWxlbnQ6IGZhbHNlLA0KICAgICAgICAgICAgdHlwZTogJ2dhdWdlJywNCiAgICAgICAgICAgIHpsZXZlbDogMiwNCiAgICAgICAgICAgIHN0YXJ0QW5nbGU6IDAsDQogICAgICAgICAgICBlbmRBbmdsZTogMzYwLA0KICAgICAgICAgICAgY2xvY2t3aXNlOiB0cnVlLA0KICAgICAgICAgICAgcmFkaXVzOiAnNzUlJywNCiAgICAgICAgICAgIHNwbGl0TnVtYmVyOiA1LA0KICAgICAgICAgICAgYXZvaWRMYWJlbE92ZXJsYXA6IGZhbHNlLA0KICAgICAgICAgICAgYXhpc0xpbmU6IHsNCiAgICAgICAgICAgICAgc2hvdzogdHJ1ZQ0KICAgICAgICAgICAgICAvLyBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgLy8gICBjb2xvcjogWw0KICAgICAgICAgICAgICAvLyAgICAgWzgwIC8gMTAwLCAicmdiYSgwLCAyMTEsIDE2NywgMC4zKSJdLA0KICAgICAgICAgICAgICAvLyAgICAgWzEsICIjZjBmMmY4Il0sDQogICAgICAgICAgICAgIC8vICAgXSwNCiAgICAgICAgICAgICAgLy8gICB3aWR0aDogMTYsDQogICAgICAgICAgICAgIC8vIH0sDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwKScNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBwcm9ncmVzczogew0KICAgICAgICAgICAgICBzaG93OiB0cnVlDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYXhpc1RpY2s6IHsNCiAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgICAgc3BsaXROdW1iZXI6IDEsDQogICAgICAgICAgICAgIGRpc3RhbmNlOiAtMTYsDQogICAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICAgIGNvbG9yOiAnI2ZmZmZmZicsDQogICAgICAgICAgICAgICAgd2lkdGg6IDMNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgbGVuZ3RoOiAyMA0KICAgICAgICAgICAgfSwgLy8g5Yi75bqm5qC35byPDQogICAgICAgICAgICBzcGxpdExpbmU6IHsNCiAgICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBwb2ludGVyOiB7DQogICAgICAgICAgICAgIHNob3c6IGZhbHNlDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgdGl0bGU6IHsNCiAgICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBkZXRhaWw6IHsNCiAgICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIC8vIOWkluWchjINCiAgICAgICAgICAgIHR5cGU6ICdwaWUnLA0KICAgICAgICAgICAgc2lsZW50OiB0cnVlLA0KICAgICAgICAgICAgY2VudGVyOiBbJzUwJScsICc1MCUnXSwNCiAgICAgICAgICAgIHJhZGl1czogWycwJScsICc1MCUnXSwNCiAgICAgICAgICAgIGF2b2lkTGFiZWxPdmVybGFwOiBmYWxzZSwNCiAgICAgICAgICAgIHpsZXZlbDogMywNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogew0KICAgICAgICAgICAgICAgIHR5cGU6ICdsaW5lYXInLA0KICAgICAgICAgICAgICAgIHg6IDAsDQogICAgICAgICAgICAgICAgeTogMSwNCiAgICAgICAgICAgICAgICB4MjogMCwNCiAgICAgICAgICAgICAgICB5MjogMCwNCiAgICAgICAgICAgICAgICBjb2xvclN0b3BzOiBbDQogICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgIG9mZnNldDogMCwNCiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDAsIDIxMSwgMTY3LCAwLjMpJw0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgb2Zmc2V0OiAxLA0KICAgICAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoNTcsIDEzMywgMjM4LCAwKScNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBdDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAncmdiYSgwLCAyMTEsIDE2NywgMC4yKScNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBsYWJlbDogew0KICAgICAgICAgICAgICBzaG93OiB0cnVlLA0KICAgICAgICAgICAgICBwb3NpdGlvbjogJ2NlbnRlcicsDQogICAgICAgICAgICAgIGZvcm1hdHRlcjogKHBhbWFycykgPT4gew0KICAgICAgICAgICAgICAgIHJldHVybiBgMCVgDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIGZvbnRTaXplOiAyNCwNCiAgICAgICAgICAgICAgY29sb3I6ICcjM2Y0NjUyJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGxhYmVsTGluZTogew0KICAgICAgICAgICAgICBzaG93OiBmYWxzZQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGRhdGE6IFsxXQ0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIC8vIOaKpeS/ruWkhOeQhuS6uuWRmOWujOaIkOaOkuWQjQ0KICAgICAgcmVwYWlyUHJvY2Vzc2luZ1BlcnNvbm5lbENvbXBsZXRlUmFua2luZ0RhdGE6IFtdLA0KICAgICAgLy8g6I635Y+W6K6+5aSH5pWF6Zqc546H5o6S6KGMDQogICAgICBlcXVpcG1lbnRGYWlsdXJlUmF0ZVJhbmtpbmc6IFtdDQogICAgfQ0KICB9LA0KICBhY3RpdmF0ZWQoKSB7fSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICB0aGlzLmF1dG9TY3JvbGwodHJ1ZSkNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmluaXREYXRhKCkNCg0KICAgIHRoaXMuYXV0b1Njcm9sbCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDmiZPlvIDlvLnmoYYNCiAgICBvcGVuRGlhbG9nKHR5cGUsIHJvdywgb3JkZXJUeXBlKSB7DQogICAgICB0aGlzLiRyZWZzLmVkaXREaWFsb2cuaGFuZGxlT3Blbih0eXBlLCByb3csIG9yZGVyVHlwZSkNCiAgICB9LA0KICAgIC8vIOWIneWni+WMluWKoOi9veaVsOaNrg0KICAgIGluaXREYXRhKCkgew0KICAgICAgLy8g5b6F5Yqe5oql5L+uDQogICAgICB0aGlzLmdldFdvcmtPcmRlck1hbmFnZUxpc3QoKQ0KICAgICAgLy8g6I635Y+W5bel5Y2V5oC76KeI57uf6K6hDQogICAgICB0aGlzLmdldFRpbWVvdXRTdGF0aXN0aWNzKCkNCiAgICAgIC8vIOiOt+WPluW3peWNleWTjeW6lOi2heaXtue7n+iuoQ0KICAgICAgdGhpcy5nZXRXb3Jrb3JkZXJTdGF0aXN0aWNzKCkNCiAgICAgIC8vIOiOt+WPluW3peWNlea7oeaEj+W6pue7n+iuoQ0KICAgICAgdGhpcy5nZXRTYXRpc2ZhY3Rpb25TdGF0aXN0aWNzKCkNCiAgICAgIC8vIOiOt+WPluWkhOeQhuS6uuWRmOWujOaIkOaOkuWQjQ0KICAgICAgdGhpcy5nZXRQcm9jZXNzZWRSYW5rKCkNCg0KICAgICAgLy8g6I635Y+W5ZCE6L2m6Ze05bel5Y2V5oOF5Ya1DQogICAgICB0aGlzLmdldFdvcmtTaG9wQ2FzZSgpDQogICAgICAvLyDojrflj5blkITovabpl7Totovlir8NCiAgICAgIHRoaXMuZ2V0V29ya09yZGVyVHJlbmQoKQ0KICAgICAgLy8g6I635Y+W5oql5L+u5bel5Y2V5pWF6Zqc57G75Z6LDQogICAgICB0aGlzLmdldFdvcmtPcmRlckVycm9yVHlwZSgpDQoNCiAgICAgIC8vIOiOt+WPluiuvuWkh+WujOWlveeOhw0KICAgICAgdGhpcy5nZXRFcXVpcEZhaWx1cmVSYXRlUmFuaygpDQogICAgICAvLyDojrflj5borr7lpIfmlYXpmpznjofmjpLooYwNCiAgICAgIHRoaXMuZ2V0RGV2aWNlU2VydmljZWFiaWxpdHlSYXRlKCkNCiAgICB9LA0KICAgIC8vIOWFheWAvOihqOWNleaVsOaNruW5tuWKoOi9vQ0KICAgIHJlc2V0Rm9ybSgpIHsNCiAgICAgIHRoaXMueWVhck1vbnRoVHlwZSA9ICdtb250aCcNCiAgICAgIHRoaXMueWVhck1vbnRoVmFsdWUgPSBkYXlqcyhuZXcgRGF0ZSgpKS5mb3JtYXQoJ1lZWVktTU0nKQ0KICAgICAgdGhpcy5pbml0RGF0YSgpDQogICAgfSwNCiAgICAvLyDnrZvpgInmnaHku7YNCiAgICB5ZWFyTW9udGhSYWRpb0NoYW5nZShlKSB7DQogICAgICBpZiAoZSA9PSAxKSB7DQogICAgICAgIHRoaXMueWVhck1vbnRoVHlwZSA9ICd5ZWFyJw0KICAgICAgICB0aGlzLnllYXJNb250aFZhbHVlID0gZGF5anMobmV3IERhdGUoKSkuZm9ybWF0KCdZWVlZJykNCiAgICAgIH0gZWxzZSBpZiAoZSA9PSAyKSB7DQogICAgICAgIHRoaXMueWVhck1vbnRoVHlwZSA9ICdtb250aCcNCiAgICAgICAgdGhpcy55ZWFyTW9udGhWYWx1ZSA9IGRheWpzKG5ldyBEYXRlKCkpLmZvcm1hdCgnWVlZWS1NTScpDQogICAgICB9DQogICAgICB0aGlzLmluaXREYXRhKCkNCiAgICB9LA0KICAgIC8vIOW5tCDmnIgg5YiH5o2iDQogICAgeWVhck1vbnRoUGlja2VyQ2hhbmdlKCkgew0KICAgICAgdGhpcy5pbml0RGF0YSgpDQogICAgfSwNCiAgICAvLyDlvoXlip7miqXkv64NCiAgICBhc3luYyBnZXRXb3JrT3JkZXJNYW5hZ2VMaXN0KCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0V29ya09yZGVyTWFuYWdlTGlzdCh7DQogICAgICAgIG1vZGVsOiB7DQogICAgICAgICAgRGF0ZTogW10sDQogICAgICAgICAgT3JkZXJfQ29kZTogJycsDQogICAgICAgICAgT3JkZXJfTmFtZTogJycsDQogICAgICAgICAgQ3JlYXRlX0RhdGU6ICcnLA0KICAgICAgICAgIENyZWF0ZV9FRGF0ZTogJycsDQogICAgICAgICAgU3RhdGU6ICcnLA0KICAgICAgICAgIFdvcmtPcmRlcl9TZXR1cF9JZDogJ2pzYngnLA0KICAgICAgICAgIE1haW50YWluX1BlcnNvbjogJycsDQogICAgICAgICAgV29ya09yZGVyX1N0YXRlOiAwLA0KICAgICAgICAgIFR5cGU6IDEsDQogICAgICAgICAgdHlwZTogMQ0KICAgICAgICB9LA0KICAgICAgICBwYWdlSW5mbzogew0KICAgICAgICAgIFBhZ2U6IDEsDQogICAgICAgICAgUGFnZVNpemU6IDEwLA0KICAgICAgICAgIFNvcnROYW1lOiAnQ3JlYXRlX0RhdGUnLA0KICAgICAgICAgIFNvcnRPcmRlcjogJ0RFU0MnDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICB0aGlzLnBlbmRpbmdSZXBhaXJSZXF1ZXN0RGF0YSA9IHJlcy5EYXRhLkRhdGENCiAgICB9LA0KICAgIC8vIOiOt+WPluW3peWNlea7oeaEj+W6pue7n+iuoQ0KICAgIGFzeW5jIGdldFNhdGlzZmFjdGlvblN0YXRpc3RpY3MoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRTYXRpc2ZhY3Rpb25TdGF0aXN0aWNzKHsNCiAgICAgICAgV29ya09yZGVyVHlwZTogJ2pzYngnLA0KICAgICAgICBEYXRlVHlwZTogdGhpcy55ZWFyTW9udGhSYWRpbywNCiAgICAgICAgU3RhcnRUaW1lOiB0aGlzLmdldFN0YXJ0VGltZSh0aGlzLnllYXJNb250aFJhZGlvKQ0KICAgICAgfSkNCiAgICAgIHRoaXMucmVwYWlyU2F0aXNmYWN0aW9uQ29uZmlnID0gcmVzLkRhdGENCiAgICAgIGNvbnN0IGF2ZXJhZ2UgPSByZXMuRGF0YS5BdmcgfHwgMA0KICAgICAgbGV0IGF2ZXJhZ2VTdHIgPSAnJw0KICAgICAgaWYgKGF2ZXJhZ2UgPj0gNCkgew0KICAgICAgICBhdmVyYWdlU3RyID0gJ+S8mCcNCiAgICAgIH0gZWxzZSBpZiAoYXZlcmFnZSA8IDQgJiYgYXZlcmFnZSA+PSAzKSB7DQogICAgICAgIGF2ZXJhZ2VTdHIgPSAn6ImvJw0KICAgICAgfSBlbHNlIGlmIChhdmVyYWdlIDwgMyAmJiBhdmVyYWdlID49IDIpIHsNCiAgICAgICAgYXZlcmFnZVN0ciA9ICfkuK0nDQogICAgICB9IGVsc2UgaWYgKGF2ZXJhZ2UgPCAyKSB7DQogICAgICAgIGF2ZXJhZ2VTdHIgPSAn5beuJw0KICAgICAgfQ0KICAgICAgdGhpcy5yZXBhaXJTYXRpc2ZhY3Rpb25PcHRpb25zLnNlcmllcyA9IFsNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICflpJbpg6jliLvluqYnLA0KICAgICAgICAgIHR5cGU6ICdnYXVnZScsDQogICAgICAgICAgcmFkaXVzOiAnMTAwJScsDQogICAgICAgICAgc3BsaXROdW1iZXI6IDIwLA0KICAgICAgICAgIG1pbjogMCwNCiAgICAgICAgICBtYXg6IDEwMCwNCiAgICAgICAgICBzdGFydEFuZ2xlOiAyMjUsDQogICAgICAgICAgZW5kQW5nbGU6IC00NSwNCiAgICAgICAgICBheGlzTGluZTogew0KICAgICAgICAgICAgcm91bmRDYXA6IHRydWUsDQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgd2lkdGg6IDAsDQogICAgICAgICAgICAgIG9wYWNpdHk6IDANCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNUaWNrOiB7DQogICAgICAgICAgICBzaG93OiBmYWxzZQ0KICAgICAgICAgIH0sDQogICAgICAgICAgc3BsaXRMaW5lOiB7DQogICAgICAgICAgICBzaG93OiB0cnVlLA0KICAgICAgICAgICAgbGVuZ3RoOiAzLA0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnI2E5YWZiOCcsDQogICAgICAgICAgICAgIHdpZHRoOiAxDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBkZXRhaWw6IHsNCiAgICAgICAgICAgIHNob3c6IGZhbHNlDQogICAgICAgICAgfSwNCiAgICAgICAgICBwb2ludGVyOiB7DQogICAgICAgICAgICBzaG93OiBmYWxzZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICflhoXpg6jliLvluqYnLA0KICAgICAgICAgIHR5cGU6ICdnYXVnZScsDQogICAgICAgICAgcmFkaXVzOiAnODAlJywNCiAgICAgICAgICBzcGxpdE51bWJlcjogMjAsDQogICAgICAgICAgbWluOiAwLA0KICAgICAgICAgIG1heDogMTAwLA0KICAgICAgICAgIHN0YXJ0QW5nbGU6IDIyNSwNCiAgICAgICAgICBlbmRBbmdsZTogLTQ1LA0KICAgICAgICAgIHRpdGxlOiB7DQogICAgICAgICAgICBzaG93OiB0cnVlLA0KICAgICAgICAgICAgZm9udFNpemU6IDEyLA0KICAgICAgICAgICAgY29sb3I6ICcjNTA1RDZGJywNCiAgICAgICAgICAgIG9mZnNldENlbnRlcjogWycwJywgJy0yMCUnXQ0KICAgICAgICAgIH0sDQogICAgICAgICAgZGF0YTogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICB2YWx1ZTogW10sDQogICAgICAgICAgICAgIG5hbWU6ICfmiqXkv67lpITnkIbmu6HmhI/luqYnDQogICAgICAgICAgICB9DQogICAgICAgICAgXSwNCiAgICAgICAgICBkZXRhaWw6IHsNCiAgICAgICAgICAgIHZhbHVlQW5pbWF0aW9uOiB0cnVlLA0KICAgICAgICAgICAgZm9ybWF0dGVyOiAoKSA9PiB7DQogICAgICAgICAgICAgIHJldHVybiBhdmVyYWdlU3RyDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZm9udFNpemU6IDE0LA0KICAgICAgICAgICAgY29sb3I6ICcjMjk4REZGJywNCiAgICAgICAgICAgIG9mZnNldENlbnRlcjogWzAsICcxMCUnXQ0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xpbmU6IHsNCiAgICAgICAgICAgIHJvdW5kQ2FwOiB0cnVlLA0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIHdpZHRoOiAyMCwNCiAgICAgICAgICAgICAgY29sb3I6IFsNCiAgICAgICAgICAgICAgICBbDQogICAgICAgICAgICAgICAgICAoYXZlcmFnZSAqIDIwKSAvIDEwMCwNCiAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2xpbmVhcicsDQogICAgICAgICAgICAgICAgICAgIHg6IDAsDQogICAgICAgICAgICAgICAgICAgIHk6IDEsDQogICAgICAgICAgICAgICAgICAgIHgyOiAwLA0KICAgICAgICAgICAgICAgICAgICB5MjogMCwNCiAgICAgICAgICAgICAgICAgICAgY29sb3JTdG9wczogWw0KICAgICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIG9mZnNldDogMCwNCiAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzUwRkZFNCcNCiAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIG9mZnNldDogMSwNCiAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzI5OERGRicNCiAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIF0NCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgIFsxLCAncmdiYSgyMjUsMjI1LDIyNSwwLjQpJ10NCiAgICAgICAgICAgICAgXQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBzaG93OiBmYWxzZQ0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc1RpY2s6IHsNCiAgICAgICAgICAgIHNob3c6IGZhbHNlDQogICAgICAgICAgfSwNCiAgICAgICAgICBzcGxpdExpbmU6IHsNCiAgICAgICAgICAgIHNob3c6IGZhbHNlDQogICAgICAgICAgfSwNCiAgICAgICAgICBwb2ludGVyOiB7DQogICAgICAgICAgICBzaG93OiBmYWxzZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgXQ0KICAgIH0sDQogICAgLy8g6I635Y+W5aSE55CG5Lq65ZGY5a6M5oiQ5o6S5ZCNDQogICAgYXN5bmMgZ2V0UHJvY2Vzc2VkUmFuaygpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldFByb2Nlc3NlZFJhbmsoew0KICAgICAgICBXb3JrT3JkZXJUeXBlOiAnanNieCcsDQogICAgICAgIERhdGVUeXBlOiB0aGlzLnllYXJNb250aFJhZGlvLA0KICAgICAgICBTdGFydFRpbWU6IHRoaXMuZ2V0U3RhcnRUaW1lKHRoaXMueWVhck1vbnRoUmFkaW8pDQogICAgICB9KQ0KICAgICAgdGhpcy5yZXBhaXJQcm9jZXNzaW5nUGVyc29ubmVsQ29tcGxldGVSYW5raW5nRGF0YSA9IHJlcy5EYXRhDQogICAgfSwNCiAgICAvLyDojrflj5blkITovabpl7Tlt6XljZXmg4XlhrUNCiAgICBhc3luYyBnZXRXb3JrU2hvcENhc2UoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRXb3JrU2hvcENhc2Uoew0KICAgICAgICBXb3JrT3JkZXJUeXBlOiAnanNieCcsDQogICAgICAgIERhdGVUeXBlOiB0aGlzLnllYXJNb250aFJhZGlvLA0KICAgICAgICBTdGFydFRpbWU6IHRoaXMuZ2V0U3RhcnRUaW1lKHRoaXMueWVhck1vbnRoUmFkaW8pDQogICAgICB9KQ0KICAgICAgY29uc29sZS5sb2cocmVzLCAncmVzJykNCiAgICAgIGNvbnN0IHJlcGFpclN0YXR1c0VhY2hXb3Jrc2hvcE9wdGlvbnMgPSByZXMuRGF0YS5tYXAoKGl0ZW0pID0+ICh7DQogICAgICAgIG5hbWU6IGl0ZW0uTGFiZWwsDQogICAgICAgIHZhbHVlOiBpdGVtLlZhbHVlLA0KICAgICAgICBwZXJjZW50OiBpdGVtLlJhdGUNCiAgICAgIH0pKQ0KICAgICAgdGhpcy5yZXBhaXJTdGF0dXNFYWNoV29ya3Nob3BPcHRpb25zLnNlcmllc1swXS5kYXRhID0NCiAgICAgICAgcmVwYWlyU3RhdHVzRWFjaFdvcmtzaG9wT3B0aW9ucw0KICAgICAgdGhpcy5yZXBhaXJTdGF0dXNFYWNoV29ya3Nob3BPcHRpb25zLmxlZ2VuZC5mb3JtYXR0ZXIgPSBmdW5jdGlvbihuYW1lKSB7DQogICAgICAgIGNvbnN0IG9iaiA9IHJlcGFpclN0YXR1c0VhY2hXb3Jrc2hvcE9wdGlvbnMuZmluZCgNCiAgICAgICAgICAoaXRlbSkgPT4gaXRlbS5uYW1lID09IG5hbWUNCiAgICAgICAgKQ0KICAgICAgICByZXR1cm4gYHtsYWJlbE1hcmt8JHtvYmoubmFtZX19IHt2YWx1ZU1hcmt8JHtvYmoudmFsdWV9IOasoX0gIHtwZXJjZW50TWFya3wke29iai5wZXJjZW50fSAlfWANCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOiOt+WPluaKpeS/ruW3peWNleaVhemanOexu+Weiw0KICAgIGFzeW5jIGdldFdvcmtPcmRlckVycm9yVHlwZSgpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldFdvcmtPcmRlckVycm9yVHlwZSh7DQogICAgICAgIFdvcmtPcmRlclR5cGU6ICdqc2J4JywNCiAgICAgICAgRGF0ZVR5cGU6IHRoaXMueWVhck1vbnRoUmFkaW8sDQogICAgICAgIFN0YXJ0VGltZTogdGhpcy5nZXRTdGFydFRpbWUodGhpcy55ZWFyTW9udGhSYWRpbykNCiAgICAgIH0pDQogICAgICB0aGlzLnJlcGFpckZhdWx0VHlwZU9wdGlvbnMuc2VyaWVzWzBdLmRhdGEgPSByZXMuRGF0YS5tYXAoKGl0ZW0pID0+ICh7DQogICAgICAgIG5hbWU6IGl0ZW0uTGFiZWwsDQogICAgICAgIHZhbHVlOiBpdGVtLlZhbHVlLA0KICAgICAgICBwZXJjZW50OiBpdGVtLlJhdGUNCiAgICAgIH0pKQ0KICAgIH0sDQogICAgLy8g6I635Y+W5ZCE6L2m6Ze06LaL5Yq/DQogICAgYXN5bmMgZ2V0V29ya09yZGVyVHJlbmQoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRXb3JrT3JkZXJUcmVuZCh7DQogICAgICAgIFdvcmtPcmRlclR5cGU6ICdqc2J4JywNCiAgICAgICAgRGF0ZVR5cGU6IHRoaXMueWVhck1vbnRoUmFkaW8sDQogICAgICAgIFN0YXJ0VGltZTogdGhpcy5nZXRTdGFydFRpbWUodGhpcy55ZWFyTW9udGhSYWRpbykNCiAgICAgIH0pDQogICAgICBsZXQgeEF4aXNEYXRhID0gW10NCiAgICAgIHRoaXMudHJlbmRSZXBhaXJSZXBvcnRzVmFyaW91c1dvcmtzaG9wc09wdGlvbnMuc2VyaWVzID0gcmVzLkRhdGEubWFwKA0KICAgICAgICAoaXRlbSkgPT4gew0KICAgICAgICAgIHhBeGlzRGF0YSA9IGl0ZW0uU2hvcERhdGEubWFwKChlbGUpID0+IGVsZS5MYWJlbCkNCiAgICAgICAgICBpZiAoaXRlbS5TaG9wTmFtZSA9PSAn5oql5L+u5pWw6YePJykgew0KICAgICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgICAgbmFtZTogaXRlbS5TaG9wTmFtZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgICBzbW9vdGg6IHRydWUsDQogICAgICAgICAgICAgIHN5bWJvbDogJ25vbmUnLA0KICAgICAgICAgICAgICBkYXRhOiBpdGVtLlNob3BEYXRhLm1hcCgoZWxlKSA9PiBlbGUuVmFsdWUpDQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgIG5hbWU6IGl0ZW0uU2hvcE5hbWUsDQogICAgICAgICAgICAgIHR5cGU6ICdiYXInLA0KICAgICAgICAgICAgICBzdGFjazogJ3ZlaGljbGUnLA0KICAgICAgICAgICAgICBlbXBoYXNpczogew0KICAgICAgICAgICAgICAgIGZvY3VzOiAnc2VyaWVzJw0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICBkYXRhOiBpdGVtLlNob3BEYXRhLm1hcCgoZWxlKSA9PiBlbGUuVmFsdWUpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICApDQogICAgICB0aGlzLnRyZW5kUmVwYWlyUmVwb3J0c1ZhcmlvdXNXb3Jrc2hvcHNPcHRpb25zLnhBeGlzLmRhdGEgPSB4QXhpc0RhdGENCiAgICB9LA0KDQogICAgLy8g6I635Y+W6K6+5aSH5a6M5aW9546HDQogICAgYXN5bmMgZ2V0RGV2aWNlU2VydmljZWFiaWxpdHlSYXRlKCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0RGV2aWNlU2VydmljZWFiaWxpdHlSYXRlKHsNCiAgICAgICAgV29ya09yZGVyVHlwZTogJ2pzYngnLA0KICAgICAgICBEYXRlVHlwZTogdGhpcy55ZWFyTW9udGhSYWRpbywNCiAgICAgICAgU3RhcnRUaW1lOiB0aGlzLmdldFN0YXJ0VGltZSh0aGlzLnllYXJNb250aFJhZGlvKQ0KICAgICAgfSkNCiAgICAgIHRoaXMuZXF1aXBtZW50SW50ZWdyaXR5UmF0ZSA9IHJlcy5EYXRhDQogICAgICB0aGlzLmVxdWlwbWVudEludGVncml0eVJhdGVPcHRpb25zLnNlcmllc1swXS5heGlzTGluZS5saW5lU3R5bGUgPSB7DQogICAgICAgIGNvbG9yOiBbDQogICAgICAgICAgW3Jlcy5EYXRhLlNlcnZpY2VhYmlsaXR5UmF0ZSAvIDEwMCwgJ3JnYmEoMCwgMjExLCAxNjcsIDAuMyknXSwNCiAgICAgICAgICBbMSwgJyNmMGYyZjgnXQ0KICAgICAgICBdLA0KICAgICAgICB3aWR0aDogMTYNCiAgICAgIH0NCiAgICAgIHRoaXMuZXF1aXBtZW50SW50ZWdyaXR5UmF0ZU9wdGlvbnMuc2VyaWVzWzFdLmxhYmVsLmZvcm1hdHRlciA9IGZ1bmN0aW9uKA0KICAgICAgICBwYW1hcnMNCiAgICAgICkgew0KICAgICAgICByZXR1cm4gYCR7cmVzLkRhdGEuU2VydmljZWFiaWxpdHlSYXRlfSVgDQogICAgICB9DQogICAgfSwNCiAgICAvLyDojrflj5borr7lpIfmlYXpmpznjofmjpLooYwNCiAgICBhc3luYyBnZXRFcXVpcEZhaWx1cmVSYXRlUmFuaygpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldEVxdWlwRmFpbHVyZVJhdGVSYW5rKHsNCiAgICAgICAgV29ya09yZGVyVHlwZTogJ2pzYngnLA0KICAgICAgICBEYXRlVHlwZTogdGhpcy55ZWFyTW9udGhSYWRpbywNCiAgICAgICAgU3RhcnRUaW1lOiB0aGlzLmdldFN0YXJ0VGltZSh0aGlzLnllYXJNb250aFJhZGlvKQ0KICAgICAgfSkNCiAgICAgIHRoaXMuZXF1aXBtZW50RmFpbHVyZVJhdGVSYW5raW5nID0gcmVzLkRhdGENCiAgICB9LA0KDQogICAgLy8g6I635Y+W5bel5Y2V5oC76KeI57uf6K6hDQogICAgYXN5bmMgZ2V0VGltZW91dFN0YXRpc3RpY3MoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRUaW1lb3V0U3RhdGlzdGljcyh7DQogICAgICAgIFdvcmtPcmRlclR5cGU6ICdqc2J4JywNCiAgICAgICAgRGF0ZVR5cGU6IHRoaXMueWVhck1vbnRoUmFkaW8sDQogICAgICAgIFN0YXJ0VGltZTogdGhpcy5nZXRTdGFydFRpbWUodGhpcy55ZWFyTW9udGhSYWRpbykNCiAgICAgIH0pDQogICAgICB0aGlzLnJlcGFpclJlc3BvbnNlQ29uZmlnID0gcmVzLkRhdGENCiAgICAgIHRoaXMucmVwYWlyUmVzcG9uc2VPcHRpb25zLnhBeGlzWzBdLmRhdGEgPSByZXMuRGF0YS5MaXN0Lm1hcCgNCiAgICAgICAgKGl0ZW0pID0+IGl0ZW0uRGF0ZQ0KICAgICAgKQ0KICAgICAgdGhpcy5yZXBhaXJSZXNwb25zZU9wdGlvbnMuc2VyaWVzWzBdLmRhdGEgPSByZXMuRGF0YS5MaXN0Lm1hcCgNCiAgICAgICAgKGl0ZW0pID0+IGl0ZW0uVGltZWx5DQogICAgICApDQogICAgICB0aGlzLnJlcGFpclJlc3BvbnNlT3B0aW9ucy5zZXJpZXNbMV0uZGF0YSA9IHJlcy5EYXRhLkxpc3QubWFwKA0KICAgICAgICAoaXRlbSkgPT4gaXRlbS5UaW1lb3V0DQogICAgICApDQogICAgICB0aGlzLnJlcGFpclJlc3BvbnNlT3B0aW9ucy5zZXJpZXNbMl0uZGF0YSA9IHJlcy5EYXRhLkxpc3QubWFwKA0KICAgICAgICAoaXRlbSkgPT4gaXRlbS5QZXJjZW50DQogICAgICApDQogICAgfSwNCiAgICAvLyDojrflj5blt6XljZXlk43lupTotoXml7bnu5/orqENCiAgICBhc3luYyBnZXRXb3Jrb3JkZXJTdGF0aXN0aWNzKCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0V29ya29yZGVyU3RhdGlzdGljcyh7DQogICAgICAgIFdvcmtPcmRlclR5cGU6ICdqc2J4JywNCiAgICAgICAgRGF0ZVR5cGU6IHRoaXMueWVhck1vbnRoUmFkaW8sDQogICAgICAgIFN0YXJ0VGltZTogdGhpcy5nZXRTdGFydFRpbWUodGhpcy55ZWFyTW9udGhSYWRpbykNCiAgICAgIH0pDQogICAgICB0aGlzLnJlcGFpck92ZXJ2aWV3ID0gcmVzLkRhdGENCiAgICB9LA0KICAgIC8vIOiOt+WPluetieW+heaXtumVvw0KICAgIGdldFdhaXRpbmdUaW1lKGRhdGUpIHsNCiAgICAgIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKGRhdGUpDQogICAgICB2YXIgZW5kRGF0ZSA9IG5ldyBEYXRlKCkgLy8g6I635Y+W5b2T5YmN5pe26Ze0DQogICAgICB2YXIgZGlmZmVyZW5jZSA9IE1hdGguYWJzKGVuZERhdGUgLSBzdGFydERhdGUpDQogICAgICB2YXIgZGF5cyA9IE1hdGguZmxvb3IoZGlmZmVyZW5jZSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSkNCiAgICAgIHZhciBob3VycyA9IE1hdGguZmxvb3IoDQogICAgICAgIChkaWZmZXJlbmNlICUgKDEwMDAgKiA2MCAqIDYwICogMjQpKSAvICgxMDAwICogNjAgKiA2MCkNCiAgICAgICkNCiAgICAgIHZhciBtaW51dGVzID0gTWF0aC5mbG9vcigoZGlmZmVyZW5jZSAlICgxMDAwICogNjAgKiA2MCkpIC8gKDEwMDAgKiA2MCkpDQogICAgICB2YXIgZm9ybWF0dGVkRGlmZmVyZW5jZSA9ICcnDQoNCiAgICAgIGlmIChkYXlzID4gMCkgew0KICAgICAgICBmb3JtYXR0ZWREaWZmZXJlbmNlICs9IGRheXMgKyAn5aSpJw0KICAgICAgfQ0KICAgICAgaWYgKGhvdXJzID4gMCkgew0KICAgICAgICBmb3JtYXR0ZWREaWZmZXJlbmNlICs9IGhvdXJzICsgJ+Wwj+aXticNCiAgICAgIH0NCiAgICAgIGlmIChtaW51dGVzID4gMCB8fCAoZGF5cyA9PT0gMCAmJiBob3VycyA9PT0gMCkpIHsNCiAgICAgICAgZm9ybWF0dGVkRGlmZmVyZW5jZSArPSBtaW51dGVzICsgJ+WIhumSnycNCiAgICAgIH0NCiAgICAgIHJldHVybiBmb3JtYXR0ZWREaWZmZXJlbmNlDQogICAgfSwNCiAgICAvLyDojrflj5bnirbmgIHmoLflvI8NCiAgICBnZXRTdGF0dXNTdHlsZShyb3cpIHsNCiAgICAgIHJldHVybiB0aGlzLnBlbmRpbmdSZXBhaXJSZXF1ZXN0U3RhdHVzLmZpbmQoDQogICAgICAgIChpdGVtKSA9PiBpdGVtLnZhbHVlID09IHJvdy5TdGF0ZQ0KICAgICAgKQ0KICAgIH0sDQogICAgLy8g6I635Y+W5pel5pyf5pe26Ze0DQogICAgZ2V0U3RhcnRUaW1lKHR5cGUpIHsNCiAgICAgIGlmICh0eXBlID09IDEpIHsNCiAgICAgICAgcmV0dXJuIGRheWpzKHRoaXMueWVhck1vbnRoVmFsdWUpLmZvcm1hdCgnWVlZWScpDQogICAgICB9IGVsc2UgaWYgKHR5cGUgPT0gMikgew0KICAgICAgICByZXR1cm4gZGF5anModGhpcy55ZWFyTW9udGhWYWx1ZSkuZm9ybWF0KCdZWVlZLU1NJykNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOiuvue9ruihqOagvOiHquWKqOa7muWKqA0KICAgIGF1dG9TY3JvbGwoc3RvcCkgew0KICAgICAgY29uc3QgdGFibGUgPSB0aGlzLiRyZWZzLnNjcm9sbF9UYWJsZQ0KICAgICAgLy8g5ou/5Yiw6KGo5qC85Lit5om/6L295pWw5o2u55qEZGl25YWD57SgDQogICAgICBjb25zdCBkaXZEYXRhID0gdGFibGUuJHJlZnMuYm9keVdyYXBwZXINCiAgICAgIC8vIOaLv+WIsOWFg+e0oOWQju+8jOWvueWFg+e0oOi/m+ihjOWumuaXtuWinuWKoOi3neemu+mhtumDqOi3neemu++8jOWunueOsOa7muWKqOaViOaenCjmraTphY3nva7kuLrmr48xMDDmr6vnp5Lnp7vliqgx5YOP57SgKQ0KICAgICAgaWYgKHN0b3ApIHsNCiAgICAgICAgLy8g5YaN6YCa6L+H5LqL5Lu255uR5ZCs77yM55uR5ZCs5YiwIOe7hOS7tumUgOavgSDlkI7vvIzlho3miafooYzlhbPpl63orqHml7blmajjgIINCiAgICAgICAgd2luZG93LmNsZWFySW50ZXJ2YWwodGhpcy5zY3JvbGx0aW1lcikNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2Nyb2xsdGltZXIgPSB3aW5kb3cuc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICAgIC8vIOWFg+e0oOiHquWinui3neemu+mhtumDqDHlg4/ntKANCiAgICAgICAgICBkaXZEYXRhLnNjcm9sbFRvcCArPSAyDQogICAgICAgICAgLy8g5Yik5pat5YWD57Sg5piv5ZCm5rua5Yqo5Yiw5bqV6YOoKOWPr+inhumrmOW6pivot53nprvpobbpg6g95pW05Liq6auY5bqmKQ0KICAgICAgICAgIGlmICgNCiAgICAgICAgICAgIGRpdkRhdGEuY2xpZW50SGVpZ2h0ICsgZGl2RGF0YS5zY3JvbGxUb3AgPT0NCiAgICAgICAgICAgIGRpdkRhdGEuc2Nyb2xsSGVpZ2h0DQogICAgICAgICAgKSB7DQogICAgICAgICAgICAvLyDph43nva50YWJsZei3neemu+mhtumDqOi3neemuw0KICAgICAgICAgICAgZGl2RGF0YS5zY3JvbGxUb3AgPSAwDQogICAgICAgICAgICAvLyDph43nva50YWJsZei3neemu+mhtumDqOi3neemu+OAguWAvD0o5rua5Yqo5Yiw5bqV6YOo5pe277yM6Led56a76aG26YOo55qE5aSn5bCPKSAtIOaVtOS4qumrmOW6pi8yDQogICAgICAgICAgICAvLyBkaXZEYXRhLnNjcm9sbFRvcCA9IGRpdkRhdGEuc2Nyb2xsVG9wIC0gZGl2RGF0YS5zY3JvbGxIZWlnaHQgLyAyDQogICAgICAgICAgfQ0KICAgICAgICB9LCAxMjApIC8vIOa7muWKqOmAn+W6pg0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDorr7nva7ooajmoLzpopzoibINCiAgICByZXBhaXJQcm9jZXNzaW5nUGVyc29ubmVsQ29tcGxldGVSYW5raW5nRGF0YUNsYXNzTmFtZSh7IHJvdywgcm93SW5kZXggfSkgew0KICAgICAgaWYgKHRoaXMuaXNFdmVuT3JPZGQocm93SW5kZXggKyAxKSkgew0KICAgICAgICByZXR1cm4gJ3Jvdy1vbmUnDQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJ3Jvdy10d28nDQogICAgICB9DQogICAgfSwNCiAgICAvLyDorr7nva7ooajmoLzpopzoibINCiAgICBwZW5kaW5nUmVwYWlyUmVxdWVzdERhdGFDbGFzc05hbWUoeyByb3csIHJvd0luZGV4IH0pIHsNCiAgICAgIGlmICh0aGlzLmlzRXZlbk9yT2RkKHJvd0luZGV4ICsgMSkpIHsNCiAgICAgICAgcmV0dXJuICdyb3ctb25lJw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuICdyb3ctdHdvJw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8gIOWIpOaWreaYr+WQpuaYr+WBtuaVsOihjCDov5jmmK/lpYfmlbDooYwNCiAgICBpc0V2ZW5Pck9kZChudW1iZXIpIHsNCiAgICAgIGlmIChudW1iZXIgJSAyID09PSAwKSB7DQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOafpeeci+abtOWkmg0KICAgIGxvb2tNb3JlRGV0YWlsKCkgew0KICAgICAgLy8gbGV0IFVybCA9IHRoaXMuanVtcFVybExpc3QuZmluZCgNCiAgICAgIC8vICAgKGl0ZW0pID0+IGl0ZW0uTW9kdWxlQ29kZSA9PSBNb2R1bGVDb2RlDQogICAgICAvLyApLlVybDsNCiAgICAgIGNvbnN0IFBsYXRmb3JtID0gJ2RpZ2l0YWxmYWN0b3J5Jw0KICAgICAgY29uc3QgTW9kdWxlSWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnTW9kdWxlSWQnKQ0KICAgICAgY29uc3QgTW9kdWxlQ29kZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdNb2R1bGVDb2RlJykNCiAgICAgIC8vIOiOt+WPluacrOaciOeahOesrOS4gOWkqQ0KICAgICAgY29uc3Qgc3RhcnRPZk1vbnRoID0gZGF5anMoKS5zdGFydE9mKCdtb250aCcpDQogICAgICAvLyDojrflj5bmnKzmnIjnmoTmnIDlkI7kuIDlpKkNCiAgICAgIGNvbnN0IGVuZE9mTW9udGggPSBkYXlqcygpLmVuZE9mKCdtb250aCcpDQogICAgICAvLyDovpPlh7rnu5PmnpwNCiAgICAgIC8vIENyZWF0ZV9EYXRlOiIyMDI0LTA3LTE5Ig0KICAgICAgLy8gQ3JlYXRlX0VEYXRlOiIyMDI0LTA4LTE0Ig0KICAgICAgdGhpcy4kcWlhbmt1bi5zd2l0Y2hNaWNyb0FwcEZuKA0KICAgICAgICBQbGF0Zm9ybSwNCiAgICAgICAgJ2d6dCcsDQogICAgICAgICc3NjIyZDA0Mi1iMTE0LTQ2YTAtYjFiYS1iNTYyMTYyMmYwNTgnLA0KICAgICAgICBgL2J1c2luZXNzL21haW50ZW5hbmNlQW5kVXBrZWVwL3dvcmtPcmRlck1hbmFnZW1lbnQ/U3RhdGU9MCZBY3RpdmVOYW1lPWZpcnN0JmlzSnVtcD10cnVlYA0KICAgICAgKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/equipmentManagement/workOrderStatistics/repair", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 workOrderStatistics_repair\">\r\n    <el-row :gutter=\"12\">\r\n      <el-col :span=\"24\">\r\n        <el-card shadow=\"hover\">\r\n          <div class=\"search_content\">\r\n            <span class=\"label\">选择维度</span>\r\n            <el-radio-group\r\n              v-model=\"yearMonthRadio\"\r\n              class=\"radio\"\r\n              @change=\"yearMonthRadioChange\"\r\n            >\r\n              <el-radio-button label=\"1\">年</el-radio-button>\r\n              <el-radio-button label=\"2\">月</el-radio-button>\r\n            </el-radio-group>\r\n            <el-date-picker\r\n              v-model=\"yearMonthValue\"\r\n              class=\"picker\"\r\n              :editable=\"false\"\r\n              :clearable=\"false\"\r\n              :type=\"yearMonthType\"\r\n              @change=\"yearMonthPickerChange\"\r\n            />\r\n            <el-button @click=\"resetForm\">重置</el-button>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"10\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <div class=\"title_content\">\r\n              <span>报修总览</span>\r\n              <el-popover\r\n                placement=\"top-start\"\r\n                title=\"说明\"\r\n                width=\"420\"\r\n                trigger=\"hover\"\r\n              >\r\n                <div>\r\n                  <span>待处理：工单中心工单状态为待处理的所有工单数量</span><br>\r\n                  <span>处理中：工单中心工单状态为处理中，待复检的所有工单数量</span><br>\r\n                  <span>已处理：工单中心工单状态为待评价，处理完成，已关闭的所有工单数量</span>\r\n                </div>\r\n                <img\r\n                  slot=\"reference\"\r\n                  style=\"width: 16px; height: 16px\"\r\n                  src=\"@/assets/question.png\"\r\n                  alt=\"\"\r\n                >\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n          <div class=\"repairOverview_content\">\r\n            <div class=\"top\">\r\n              <div class=\"main\">\r\n                <img\r\n                  class=\"left\"\r\n                  src=\"@/assets/totalUmberSorkOrders.png\"\r\n                  alt=\"\"\r\n                >\r\n                <div class=\"right\">\r\n                  <span class=\"text\" style=\"color: #298dff\">{{\r\n                    repairOverview.Total\r\n                  }}</span>\r\n                  <span class=\"value\">报修总数</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"main\">\r\n                <img class=\"left\" src=\"@/assets/repairTime.png\" alt=\"\">\r\n                <div class=\"right\">\r\n                  <span class=\"text\" style=\"color: #ffae2c\">{{\r\n                    repairOverview.FixTime\r\n                  }}</span>\r\n                  <div class=\"value\">\r\n                    <span>平均修复时间</span>\r\n                    <el-popover\r\n                      placement=\"top-start\"\r\n                      title=\"说明\"\r\n                      trigger=\"hover\"\r\n                    >\r\n                      <span>平均修复时间=工单总处理时长/工单个数</span>\r\n                      <img\r\n                        slot=\"reference\"\r\n                        style=\"width: 16px; height: 16px\"\r\n                        src=\"@/assets/question.png\"\r\n                        alt=\"\"\r\n                      >\r\n                    </el-popover>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"bottom\">\r\n              <div class=\"main\">\r\n                <img class=\"left\" src=\"@/assets/pendingProcessing.png\" alt=\"\">\r\n                <div class=\"right\">\r\n                  <span class=\"value\">待处理</span>\r\n                  <span class=\"text\" style=\"color: #ff5e7c\">{{\r\n                    repairOverview.Pending\r\n                  }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"main\">\r\n                <img class=\"left\" src=\"@/assets/processing.png\" alt=\"\">\r\n                <div class=\"right\">\r\n                  <span class=\"value\">处理中</span>\r\n                  <span class=\"text\" style=\"color: #298dff\">{{\r\n                    repairOverview.Processing\r\n                  }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"main\">\r\n                <img class=\"left\" src=\"@/assets/processed.png\" alt=\"\">\r\n                <div class=\"right\">\r\n                  <span class=\"value\">已处理</span>\r\n                  <span class=\"text\" style=\"color: #00d3a7\">{{\r\n                    repairOverview.Processed\r\n                  }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"14\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>待办报修</span>\r\n            <el-button\r\n              type=\"text\"\r\n              @click=\"lookMoreDetail('WorkOrderManagement')\"\r\n            >查看更多 <i class=\"el-icon-arrow-right\" /></el-button>\r\n          </div>\r\n          <div style=\"margin-top: -20px\">\r\n            <el-table\r\n              ref=\"scroll_Table\"\r\n              :data=\"pendingRepairRequestData\"\r\n              style=\"width: 100%\"\r\n              height=\"240\"\r\n              :highlight-current-row=\"false\"\r\n              :row-class-name=\"pendingRepairRequestDataClassName\"\r\n              @mouseenter.native=\"autoScroll(true)\"\r\n              @mouseleave.native=\"autoScroll(false)\"\r\n            >\r\n              <el-table-column prop=\"Order_Name\" label=\"报修名称\" />\r\n              <el-table-column prop=\"State\" label=\"报修状态\">\r\n                <template slot-scope=\"scope\">\r\n                  <span\r\n                    :style=\"{\r\n                      color: getStatusStyle(scope.row).color,\r\n                    }\"\r\n                  >\r\n                    {{ getStatusStyle(scope.row).text }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Warranty_Person\" label=\"提交人员\" />\r\n              <el-table-column prop=\"Create_Date\" label=\"创建时间\" />\r\n              <el-table-column label=\"等待时长\">\r\n                <template slot-scope=\"scope\">\r\n                  <span> {{ getWaitingTime(scope.row.Create_Date) }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"ErrPercent\" label=\"操作\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    type=\"text\"\r\n                    size=\"small\"\r\n                    @click=\"\r\n                      openDialog('detail', scope.row, scope.row.Order_Type)\r\n                    \"\r\n                  >查看详情</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"9\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>报修故障类型</span>\r\n          </div>\r\n          <div class=\"equipmentStartupStatus_content\">\r\n            <v-chart\r\n              ref=\"repairFaultTypeRef\"\r\n              class=\"repairFaultType\"\r\n              :option=\"repairFaultTypeOptions\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"5\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <div class=\"title_content\">\r\n              <span>设备完好率</span>\r\n              <el-popover\r\n                placement=\"top-start\"\r\n                title=\"说明\"\r\n                width=\"420\"\r\n                trigger=\"hover\"\r\n              >\r\n                <div>\r\n                  <span>1.设备维修完好率=月完好天数/月总天数</span><br>\r\n                  <span>2.月完好天数：当天无未处理完成的工单即为完好，每天24：00进行当天工单状态统计</span>\r\n                </div>\r\n                <img\r\n                  slot=\"reference\"\r\n                  style=\"width: 16px; height: 16px\"\r\n                  src=\"@/assets/question.png\"\r\n                  alt=\"\"\r\n                >\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n          <div class=\"equipmentIntegrityRate_content\">\r\n            <div style=\"width: 60%; height: 100%\">\r\n              <v-chart\r\n                ref=\"equipmentIntegrityRateRef\"\r\n                class=\"equipmentIntegrityRate\"\r\n                :option=\"equipmentIntegrityRateOptions\"\r\n                :autoresize=\"true\"\r\n              />\r\n            </div>\r\n            <div class=\"equipmentIntegrityRatelists\">\r\n              <div class=\"equipmentIntegrityRatelist\">\r\n                <span class=\"label\">完好率</span>\r\n                <span\r\n                  class=\"value\"\r\n                  style=\"color: #00d3a7\"\r\n                >{{ equipmentIntegrityRate.ServiceabilityRate }}%</span>\r\n              </div>\r\n              <div class=\"equipmentIntegrityRatelist\">\r\n                <span class=\"label\">统计天数</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  equipmentIntegrityRate.StatisticsDays\r\n                }}</span>\r\n              </div>\r\n              <div class=\"equipmentIntegrityRatelist\">\r\n                <span class=\"label\">完好天数</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  equipmentIntegrityRate.ServiceabilityDays\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"5\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <div class=\"title_content\">\r\n              <span>设备故障率排行</span>\r\n              <el-popover\r\n                placement=\"top-start\"\r\n                title=\"说明\"\r\n                width=\"420\"\r\n                trigger=\"hover\"\r\n              >\r\n                <div>\r\n                  <span>代表单位时间内设备的故障次数多少，计算方式如下： 故障率=\r\n                    累计故障次数/设备开机总时间h×100%</span>\r\n                </div>\r\n                <img\r\n                  slot=\"reference\"\r\n                  style=\"width: 16px; height: 16px\"\r\n                  src=\"@/assets/question.png\"\r\n                  alt=\"\"\r\n                >\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n          <div class=\"productionEquipmentLoadRateRanking_content\">\r\n            <div\r\n              v-for=\"(item, index) in equipmentFailureRateRanking\"\r\n              :key=\"index\"\r\n              class=\"item\"\r\n            >\r\n              <div class=\"top\">\r\n                <span>{{ item.EquipName }}</span>\r\n                <span>{{ item.FailureRate }}</span>\r\n              </div>\r\n              <el-progress\r\n                class=\"bottom\"\r\n                :percentage=\"item.FailureRate\"\r\n                :show-text=\"false\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"5\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>报修满意度</span>\r\n          </div>\r\n          <div class=\"repairSatisfaction_content\">\r\n            <div style=\"width: 60%; height: 100%\">\r\n              <v-chart\r\n                ref=\"repairSatisfactionRef\"\r\n                class=\"repairSatisfaction\"\r\n                :option=\"repairSatisfactionOptions\"\r\n                :autoresize=\"true\"\r\n              />\r\n            </div>\r\n            <div class=\"repairSatisfactionlists\">\r\n              <div class=\"repairSatisfactionlist\">\r\n                <span class=\"label\">处理报修总数</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  repairSatisfactionConfig.Total\r\n                }}</span>\r\n              </div>\r\n              <div class=\"repairSatisfactionlist\" style=\"margin-top: 20px\">\r\n                <span class=\"label\">最高满意度</span>\r\n                <span\r\n                  class=\"value\"\r\n                  style=\"color: #00d3a7\"\r\n                >{{ repairSatisfactionConfig.Max }} 分</span>\r\n              </div>\r\n              <div class=\"repairSatisfactionlist\">\r\n                <span class=\"label\">最低满意度</span>\r\n                <span\r\n                  class=\"value\"\r\n                  style=\"color: #ff902c\"\r\n                >{{ repairSatisfactionConfig.Min }} 分</span>\r\n              </div>\r\n              <div class=\"repairSatisfactionlist\">\r\n                <span class=\"label\">综合满意度</span>\r\n                <span\r\n                  class=\"value\"\r\n                  style=\"color: #298dff\"\r\n                >{{ repairSatisfactionConfig.Avg }} 分</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"14\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <div class=\"title_content\">\r\n              <span>报修响应</span>\r\n              <el-popover\r\n                placement=\"top-start\"\r\n                title=\"说明\"\r\n                width=\"420\"\r\n                trigger=\"hover\"\r\n              >\r\n                <div>\r\n                  <span>1.响应及时率：计算工单创建时间到接单时间的时长，该时间越短则代表处理人员的响应速度越快。</span><br>\r\n                  <span>2.响应超时：接单响应时间超过 1小时\r\n                    的工单计算为响应超时。</span>\r\n                </div>\r\n                <img\r\n                  slot=\"reference\"\r\n                  style=\"width: 16px; height: 16px\"\r\n                  src=\"@/assets/question.png\"\r\n                  alt=\"\"\r\n                >\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n          <div class=\"repairResponse_content\">\r\n            <div class=\"repairResponselists\">\r\n              <div class=\"repairResponselist\">\r\n                <span class=\"label\">报修总数</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  repairResponseConfig.Total\r\n                }}</span>\r\n              </div>\r\n              <div class=\"repairResponselist\">\r\n                <span class=\"label\">响应及时</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  repairResponseConfig.Timely\r\n                }}</span>\r\n              </div>\r\n              <div class=\"repairResponselist\">\r\n                <span class=\"label\">响应超时</span>\r\n                <span class=\"value\" style=\"color: #ff902c\">{{\r\n                  repairResponseConfig.Timeout\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n            <v-chart\r\n              ref=\"repairResponseRef\"\r\n              class=\"repairResponse\"\r\n              :option=\"repairResponseOptions\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"10\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>报修处理人员完成排名</span>\r\n          </div>\r\n          <div class=\"equipmentFailureTrend_content\" style=\"margin-top: -20px\">\r\n            <el-table\r\n              :data=\"repairProcessingPersonnelCompleteRankingData\"\r\n              style=\"width: 100%\"\r\n              height=\"475\"\r\n              :highlight-current-row=\"false\"\r\n              :row-class-name=\"\r\n                repairProcessingPersonnelCompleteRankingDataClassName\r\n              \"\r\n            >\r\n              <!-- ref=\"scroll_Table\"\r\n              @mouseenter.native=\"autoScroll(true)\"\r\n              @mouseleave.native=\"autoScroll(false)\" -->\r\n              <el-table-column label=\"排名\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <div\r\n                    v-if=\"scope.$index < 3\"\r\n                    class=\"tablenumber\"\r\n                    :style=\"{\r\n                      backgroundImage:\r\n                        'url(' +\r\n                        require(`../../../../../assets/no_${\r\n                          scope.$index + 1\r\n                        }.png`) +\r\n                        ')',\r\n                    }\"\r\n                  >\r\n                    <span> {{ scope.$index + 1 }}</span>\r\n                  </div>\r\n                  <div v-else class=\"tablenumber\">\r\n                    <span> {{ scope.$index + 1 }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Name\" label=\"姓名\" width=\"100\" />\r\n              <el-table-column prop=\"Count\" sortable label=\"数量\" width=\"100\" />\r\n              <el-table-column prop=\"Duration\" sortable label=\"用时\" />\r\n              <el-table-column\r\n                prop=\"Timely\"\r\n                sortable\r\n                label=\"响应及时率\"\r\n                width=\"120\"\r\n              />\r\n              <el-table-column\r\n                prop=\"Satisfaction\"\r\n                sortable\r\n                label=\"综合满意度\"\r\n                width=\"120\"\r\n              />\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"9\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>各车间报修情况</span>\r\n          </div>\r\n          <div class=\"maintenanceWorkOrderProcessingStatus_content\">\r\n            <v-chart\r\n              ref=\"repairStatusEachWorkshopRef\"\r\n              class=\"repairStatusEachWorkshop\"\r\n              :option=\"repairStatusEachWorkshopOptions\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"15\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>各车间报修趋势</span>\r\n          </div>\r\n          <div class=\"repairStatusEachWorkshop_content\">\r\n            <v-chart\r\n              ref=\"trendRepairReportsVariousWorkshopsRef\"\r\n              class=\"trendRepairReportsVariousWorkshops\"\r\n              :option=\"trendRepairReportsVariousWorkshopsOptions\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <editDialog ref=\"editDialog\" @refresh=\"initData\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  GetWorkOrderManageList,\r\n  GetWorkorderStatistics,\r\n  GetTimeoutStatistics,\r\n  GetSatisfactionStatistics,\r\n  GetProcessedRank,\r\n  GetWorkShopCase,\r\n  GetWorkOrderTrend,\r\n  GetWorkOrderErrorType,\r\n  GetEquipFailureRateRank,\r\n  GetDeviceServiceabilityRate\r\n} from '@/api/business/equipmentManagement'\r\nimport dayjs from 'dayjs'\r\nimport VChart from 'vue-echarts'\r\nimport { use } from 'echarts/core'\r\nimport { CanvasRenderer } from 'echarts/renderers'\r\nimport { BarChart, LineChart, PieChart, GaugeChart } from 'echarts/charts'\r\n\r\nimport {\r\n  GridComponent,\r\n  LegendComponent,\r\n  TooltipComponent,\r\n  TitleComponent,\r\n  DataZoomComponent,\r\n  ToolboxComponent\r\n} from 'echarts/components'\r\n\r\nimport editDialog from '@/views/business/maintenanceAndUpkeep/workOrderManagement/editDialog.vue'\r\nuse([\r\n  CanvasRenderer,\r\n  BarChart,\r\n  LineChart,\r\n  PieChart,\r\n  GaugeChart,\r\n  DataZoomComponent,\r\n  GridComponent,\r\n  LegendComponent,\r\n  TitleComponent,\r\n  TooltipComponent,\r\n  ToolboxComponent\r\n])\r\nexport default {\r\n  name: 'EquipmentAnalysis',\r\n  components: {\r\n    VChart,\r\n    editDialog\r\n  },\r\n  mixins: [],\r\n  data() {\r\n    return {\r\n      // 查看更多跳转列表\r\n      jumpUrlList: [],\r\n      // 待办报修\r\n      pendingRepairRequestStatus: [\r\n        {\r\n          text: '待处理',\r\n          value: '0',\r\n          color: '#FF5E7C'\r\n        },\r\n        {\r\n          text: '处理中',\r\n          value: '1',\r\n          color: '#298DFF'\r\n        },\r\n        {\r\n          text: '待复检',\r\n          value: '2',\r\n          color: '#FF902C'\r\n        },\r\n        {\r\n          text: '待评价',\r\n          value: '3',\r\n          color: '#298DFF'\r\n        },\r\n        {\r\n          text: '处理完成',\r\n          value: '4',\r\n          color: '#00D3A7'\r\n        },\r\n        {\r\n          text: '已关闭',\r\n          value: '5',\r\n          color: '#333333'\r\n        }\r\n      ],\r\n      pendingRepairRequestData: [],\r\n      repairOverview: {},\r\n      yearMonthRadio: '2',\r\n      yearMonthType: 'month',\r\n      yearMonthValue: dayjs(new Date()).format('YYYY-MM'),\r\n      scrolltimer: '', // 自动滚动的定时任务\r\n      // 各车间报修趋势\r\n      trendRepairReportsVariousWorkshopsOptions: {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          }\r\n        },\r\n        legend: {\r\n          top: '0',\r\n          right: '0',\r\n          itemWidth: 16,\r\n          itemHeight: 8,\r\n          itemGap: 10\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        color: ['#4EBF8B', '#66CBF0', '#298DFF', '#FF902C'],\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            position: 'left',\r\n            logBase: 10\r\n          }\r\n        ],\r\n        series: [\r\n          // {\r\n          //   name: \"一车间\",\r\n          //   type: \"bar\",\r\n          //   barWidth: 20,\r\n          //   stack: \"vehicle\",\r\n          //   emphasis: {\r\n          //     focus: \"series\",\r\n          //   },\r\n          //   data: [],\r\n          // },\r\n          // {\r\n          //   name: \"二车间\",\r\n          //   type: \"bar\",\r\n          //   stack: \"vehicle\",\r\n          //   emphasis: {\r\n          //     focus: \"series\",\r\n          //   },\r\n          //   data: [],\r\n          // },\r\n          // {\r\n          //   name: \"配送中心\",\r\n          //   type: \"bar\",\r\n          //   stack: \"vehicle\",\r\n          //   emphasis: {\r\n          //     focus: \"series\",\r\n          //   },\r\n          //   data: [],\r\n          // },\r\n          // {\r\n          //   name: \"响应及时率\",\r\n          //   type: \"line\",\r\n          //   smooth: true,\r\n          //   symbol: \"none\",\r\n          //   yAxisIndex: 1,\r\n          //   tooltip: {\r\n          //     valueFormatter: function (value) {\r\n          //       return value + \" %\";\r\n          //     },\r\n          //   },\r\n          //   data: [],\r\n          // },\r\n        ]\r\n      },\r\n      // 各车间报修情况\r\n      repairStatusEachWorkshopOptions: {\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            return ` ${params.name} ${params.percent}%  `\r\n          }\r\n        },\r\n        color: ['#4EBF8B', '#298DFF', '#51A1FD'],\r\n        legend: {\r\n          orient: 'vertical',\r\n          right: '0',\r\n          bottom: 'center',\r\n          itemWidth: 12,\r\n          itemHeight: 6,\r\n          textStyle: {\r\n            color: 'rgba(34, 40, 52, 0.65)'\r\n          },\r\n          textStyle: {\r\n            rich: {\r\n              labelMark: {\r\n                width: 60,\r\n                color: '#222834'\r\n              },\r\n              valueMark: {\r\n                width: 40,\r\n                color: '#66CBF0'\r\n              },\r\n              percentMark: {\r\n                width: 40,\r\n                color: '#298DFF'\r\n              }\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            type: 'pie',\r\n            radius: '50%',\r\n            right: 150,\r\n            data: [],\r\n            labelLine: {\r\n              // 设置延长线的长度\r\n              normal: {\r\n                length: 5, // 设置延长线的长度\r\n                length2: 10, // 设置第二段延长线的长度\r\n                lineStyle: {\r\n                  color: 'rgba(194, 203, 226, 1)'\r\n                }\r\n              }\r\n            },\r\n            label: {\r\n              normal: {\r\n                // formatter: '{d}%, {c} \\n\\n',\r\n                formatter: ' {c|{b}}  {per|{d}%} \\n{hr|}\\n{a|}', // 这里最后另一行设置了一个空数据是为了能让延长线与hr线对接起来\r\n                padding: [0, -4], // 取消hr线跟延长线之间的间隙\r\n                rich: {\r\n                  a: {\r\n                    color: '#999',\r\n                    lineHeight: 20, // 设置最后一行空数据高度，为了能让延长线与hr线对接起来\r\n                    align: 'center'\r\n                  },\r\n                  hr: {\r\n                    // 设置hr是为了让中间线能够自适应长度\r\n                    borderColor: 'rgba(194, 203, 226, 1)', // hr的颜色为auto时候会主动显示颜色的\r\n                    width: '105%',\r\n                    borderWidth: 0.5,\r\n                    height: 0.5\r\n                  },\r\n                  per: {\r\n                    // 用百分比数据来调整下数字位置，显的好看些。如果不设置，formatter最后一行的空数据就不需要\r\n                    padding: [4, 0],\r\n                    // color: \"rgba(194, 203, 226, 1)\",\r\n                    color: function(params) {\r\n                      console.log(params, 'params-------------')\r\n                      // 通过数据项的颜色来设置文字颜色\r\n                      return params.color\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                shadowBlur: 10,\r\n                shadowOffsetX: 0,\r\n                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n              }\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      // 报修故障类型\r\n      repairFaultTypeOptions: {\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            return ` ${params.name} ${params.percent}%  `\r\n          }\r\n        },\r\n        color: ['#4EBF8B', '#298DFF', '#51A1FD'],\r\n        legend: {\r\n          orient: 'vertical',\r\n          right: '0',\r\n          bottom: 'center',\r\n          itemWidth: 12,\r\n          itemHeight: 6,\r\n          textStyle: {\r\n            color: 'rgba(34, 40, 52, 0.65)'\r\n          },\r\n          formatter: function(name) {\r\n            return `${name}`\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            type: 'pie',\r\n            radius: '50%',\r\n            // right: 100,\r\n            data: [],\r\n            labelLine: {\r\n              // 设置延长线的长度\r\n              normal: {\r\n                length: 5, // 设置延长线的长度\r\n                length2: 10, // 设置第二段延长线的长度\r\n                lineStyle: {\r\n                  color: 'rgba(194, 203, 226, 1)'\r\n                }\r\n              }\r\n            },\r\n            label: {\r\n              normal: {\r\n                // formatter: '{d}%, {c} \\n\\n',\r\n                formatter: ' {c|{b}}  {per|{d}%} \\n{hr|}\\n{a|}', // 这里最后另一行设置了一个空数据是为了能让延长线与hr线对接起来\r\n                padding: [0, -4], // 取消hr线跟延长线之间的间隙\r\n                rich: {\r\n                  a: {\r\n                    color: '#999',\r\n                    lineHeight: 20, // 设置最后一行空数据高度，为了能让延长线与hr线对接起来\r\n                    align: 'center'\r\n                  },\r\n                  hr: {\r\n                    // 设置hr是为了让中间线能够自适应长度\r\n                    borderColor: 'rgba(194, 203, 226, 1)', // hr的颜色为auto时候会主动显示颜色的\r\n                    width: '105%',\r\n                    borderWidth: 0.5,\r\n                    height: 0.5\r\n                  },\r\n                  per: {\r\n                    // 用百分比数据来调整下数字位置，显的好看些。如果不设置，formatter最后一行的空数据就不需要\r\n                    padding: [4, 0]\r\n                  }\r\n                }\r\n              }\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                shadowBlur: 10,\r\n                shadowOffsetX: 0,\r\n                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n              }\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      // 报修响应\r\n      repairResponseConfig: {},\r\n      repairResponseOptions: {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            crossStyle: {\r\n              color: '#999'\r\n            }\r\n          }\r\n        },\r\n        legend: {\r\n          top: '0',\r\n          right: '0',\r\n          itemWidth: 16,\r\n          itemHeight: 8,\r\n          itemGap: 10\r\n        },\r\n        color: ['#298DFF', '#FF902C', '#00D3A7'],\r\n        xAxis: [\r\n          {\r\n            type: 'category',\r\n            data: [],\r\n            axisPointer: {\r\n              type: 'shadow'\r\n            },\r\n            axisLine: {\r\n              show: false\r\n            },\r\n            axisTick: {\r\n              show: false\r\n            }\r\n          }\r\n        ],\r\n        yAxis: [\r\n          {\r\n            type: 'value'\r\n            // min: 0,\r\n            // max: 250,\r\n            // interval: 50,\r\n            // axisLabel: {\r\n            //   formatter: \"{value} ml\",\r\n            // },\r\n          },\r\n          {\r\n            type: 'value',\r\n            // min: 0,\r\n            // max: 25,\r\n            // interval: 5,\r\n            axisLabel: {\r\n              formatter: '{value} %'\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '响应及时数',\r\n            type: 'bar',\r\n            barWidth: 20,\r\n            stack: 'vehicle',\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return value\r\n              }\r\n            },\r\n            data: []\r\n          },\r\n          {\r\n            name: '响应超时数',\r\n            type: 'bar',\r\n            barWidth: 20,\r\n            stack: 'vehicle',\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return value\r\n              }\r\n            },\r\n            data: []\r\n          },\r\n          {\r\n            name: '响应及时率',\r\n            type: 'line',\r\n            smooth: true,\r\n            symbol: 'none',\r\n            yAxisIndex: 1,\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return value + ' %'\r\n              }\r\n            },\r\n            data: []\r\n          }\r\n        ]\r\n      },\r\n      // 报修满意度\r\n      repairSatisfactionConfig: {},\r\n      repairSatisfactionOptions: {\r\n        series: []\r\n      },\r\n      // 设备完好率\r\n      equipmentIntegrityRate: {},\r\n      equipmentIntegrityRateOptions: {\r\n        tooltip: {\r\n          show: false\r\n        },\r\n        series: [\r\n          {\r\n            // 外圆\r\n            silent: false,\r\n            type: 'gauge',\r\n            zlevel: 2,\r\n            startAngle: 0,\r\n            endAngle: 360,\r\n            clockwise: true,\r\n            radius: '75%',\r\n            splitNumber: 5,\r\n            avoidLabelOverlap: false,\r\n            axisLine: {\r\n              show: true\r\n              // lineStyle: {\r\n              //   color: [\r\n              //     [80 / 100, \"rgba(0, 211, 167, 0.3)\"],\r\n              //     [1, \"#f0f2f8\"],\r\n              //   ],\r\n              //   width: 16,\r\n              // },\r\n            },\r\n            itemStyle: {\r\n              color: 'rgba(255,255,255,0)'\r\n            },\r\n            progress: {\r\n              show: true\r\n            },\r\n            axisTick: {\r\n              show: true,\r\n              splitNumber: 1,\r\n              distance: -16,\r\n              lineStyle: {\r\n                color: '#ffffff',\r\n                width: 3\r\n              },\r\n              length: 20\r\n            }, // 刻度样式\r\n            splitLine: {\r\n              show: false\r\n            },\r\n            axisLabel: {\r\n              show: false\r\n            },\r\n            pointer: {\r\n              show: false\r\n            },\r\n            title: {\r\n              show: false\r\n            },\r\n            detail: {\r\n              show: false\r\n            }\r\n          },\r\n          {\r\n            // 外圆2\r\n            type: 'pie',\r\n            silent: true,\r\n            center: ['50%', '50%'],\r\n            radius: ['0%', '50%'],\r\n            avoidLabelOverlap: false,\r\n            zlevel: 3,\r\n            itemStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 1,\r\n                x2: 0,\r\n                y2: 0,\r\n                colorStops: [\r\n                  {\r\n                    offset: 0,\r\n                    color: 'rgba(0, 211, 167, 0.3)'\r\n                  },\r\n                  {\r\n                    offset: 1,\r\n                    color: 'rgba(57, 133, 238, 0)'\r\n                  }\r\n                ]\r\n              },\r\n              borderColor: 'rgba(0, 211, 167, 0.2)'\r\n            },\r\n            label: {\r\n              show: true,\r\n              position: 'center',\r\n              formatter: (pamars) => {\r\n                return `0%`\r\n              },\r\n              fontSize: 24,\r\n              color: '#3f4652'\r\n            },\r\n            labelLine: {\r\n              show: false\r\n            },\r\n            data: [1]\r\n          }\r\n        ]\r\n      },\r\n      // 报修处理人员完成排名\r\n      repairProcessingPersonnelCompleteRankingData: [],\r\n      // 获取设备故障率排行\r\n      equipmentFailureRateRanking: []\r\n    }\r\n  },\r\n  activated() {},\r\n  beforeDestroy() {\r\n    this.autoScroll(true)\r\n  },\r\n  mounted() {\r\n    this.initData()\r\n\r\n    this.autoScroll()\r\n  },\r\n  methods: {\r\n    // 打开弹框\r\n    openDialog(type, row, orderType) {\r\n      this.$refs.editDialog.handleOpen(type, row, orderType)\r\n    },\r\n    // 初始化加载数据\r\n    initData() {\r\n      // 待办报修\r\n      this.getWorkOrderManageList()\r\n      // 获取工单总览统计\r\n      this.getTimeoutStatistics()\r\n      // 获取工单响应超时统计\r\n      this.getWorkorderStatistics()\r\n      // 获取工单满意度统计\r\n      this.getSatisfactionStatistics()\r\n      // 获取处理人员完成排名\r\n      this.getProcessedRank()\r\n\r\n      // 获取各车间工单情况\r\n      this.getWorkShopCase()\r\n      // 获取各车间趋势\r\n      this.getWorkOrderTrend()\r\n      // 获取报修工单故障类型\r\n      this.getWorkOrderErrorType()\r\n\r\n      // 获取设备完好率\r\n      this.getEquipFailureRateRank()\r\n      // 获取设备故障率排行\r\n      this.getDeviceServiceabilityRate()\r\n    },\r\n    // 充值表单数据并加载\r\n    resetForm() {\r\n      this.yearMonthType = 'month'\r\n      this.yearMonthValue = dayjs(new Date()).format('YYYY-MM')\r\n      this.initData()\r\n    },\r\n    // 筛选条件\r\n    yearMonthRadioChange(e) {\r\n      if (e == 1) {\r\n        this.yearMonthType = 'year'\r\n        this.yearMonthValue = dayjs(new Date()).format('YYYY')\r\n      } else if (e == 2) {\r\n        this.yearMonthType = 'month'\r\n        this.yearMonthValue = dayjs(new Date()).format('YYYY-MM')\r\n      }\r\n      this.initData()\r\n    },\r\n    // 年 月 切换\r\n    yearMonthPickerChange() {\r\n      this.initData()\r\n    },\r\n    // 待办报修\r\n    async getWorkOrderManageList() {\r\n      const res = await GetWorkOrderManageList({\r\n        model: {\r\n          Date: [],\r\n          Order_Code: '',\r\n          Order_Name: '',\r\n          Create_Date: '',\r\n          Create_EDate: '',\r\n          State: '',\r\n          WorkOrder_Setup_Id: 'jsbx',\r\n          Maintain_Person: '',\r\n          WorkOrder_State: 0,\r\n          Type: 1,\r\n          type: 1\r\n        },\r\n        pageInfo: {\r\n          Page: 1,\r\n          PageSize: 10,\r\n          SortName: 'Create_Date',\r\n          SortOrder: 'DESC'\r\n        }\r\n      })\r\n      this.pendingRepairRequestData = res.Data.Data\r\n    },\r\n    // 获取工单满意度统计\r\n    async getSatisfactionStatistics() {\r\n      const res = await GetSatisfactionStatistics({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairSatisfactionConfig = res.Data\r\n      const average = res.Data.Avg || 0\r\n      let averageStr = ''\r\n      if (average >= 4) {\r\n        averageStr = '优'\r\n      } else if (average < 4 && average >= 3) {\r\n        averageStr = '良'\r\n      } else if (average < 3 && average >= 2) {\r\n        averageStr = '中'\r\n      } else if (average < 2) {\r\n        averageStr = '差'\r\n      }\r\n      this.repairSatisfactionOptions.series = [\r\n        {\r\n          name: '外部刻度',\r\n          type: 'gauge',\r\n          radius: '100%',\r\n          splitNumber: 20,\r\n          min: 0,\r\n          max: 100,\r\n          startAngle: 225,\r\n          endAngle: -45,\r\n          axisLine: {\r\n            roundCap: true,\r\n            lineStyle: {\r\n              width: 0,\r\n              opacity: 0\r\n            }\r\n          },\r\n          axisLabel: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            show: true,\r\n            length: 3,\r\n            lineStyle: {\r\n              color: '#a9afb8',\r\n              width: 1\r\n            }\r\n          },\r\n          detail: {\r\n            show: false\r\n          },\r\n          pointer: {\r\n            show: false\r\n          }\r\n        },\r\n        {\r\n          name: '内部刻度',\r\n          type: 'gauge',\r\n          radius: '80%',\r\n          splitNumber: 20,\r\n          min: 0,\r\n          max: 100,\r\n          startAngle: 225,\r\n          endAngle: -45,\r\n          title: {\r\n            show: true,\r\n            fontSize: 12,\r\n            color: '#505D6F',\r\n            offsetCenter: ['0', '-20%']\r\n          },\r\n          data: [\r\n            {\r\n              value: [],\r\n              name: '报修处理满意度'\r\n            }\r\n          ],\r\n          detail: {\r\n            valueAnimation: true,\r\n            formatter: () => {\r\n              return averageStr\r\n            },\r\n            fontSize: 14,\r\n            color: '#298DFF',\r\n            offsetCenter: [0, '10%']\r\n          },\r\n          axisLine: {\r\n            roundCap: true,\r\n            lineStyle: {\r\n              width: 20,\r\n              color: [\r\n                [\r\n                  (average * 20) / 100,\r\n                  {\r\n                    type: 'linear',\r\n                    x: 0,\r\n                    y: 1,\r\n                    x2: 0,\r\n                    y2: 0,\r\n                    colorStops: [\r\n                      {\r\n                        offset: 0,\r\n                        color: '#50FFE4'\r\n                      },\r\n                      {\r\n                        offset: 1,\r\n                        color: '#298DFF'\r\n                      }\r\n                    ]\r\n                  }\r\n                ],\r\n                [1, 'rgba(225,225,225,0.4)']\r\n              ]\r\n            }\r\n          },\r\n          axisLabel: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          },\r\n          pointer: {\r\n            show: false\r\n          }\r\n        }\r\n      ]\r\n    },\r\n    // 获取处理人员完成排名\r\n    async getProcessedRank() {\r\n      const res = await GetProcessedRank({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairProcessingPersonnelCompleteRankingData = res.Data\r\n    },\r\n    // 获取各车间工单情况\r\n    async getWorkShopCase() {\r\n      const res = await GetWorkShopCase({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      console.log(res, 'res')\r\n      const repairStatusEachWorkshopOptions = res.Data.map((item) => ({\r\n        name: item.Label,\r\n        value: item.Value,\r\n        percent: item.Rate\r\n      }))\r\n      this.repairStatusEachWorkshopOptions.series[0].data =\r\n        repairStatusEachWorkshopOptions\r\n      this.repairStatusEachWorkshopOptions.legend.formatter = function(name) {\r\n        const obj = repairStatusEachWorkshopOptions.find(\r\n          (item) => item.name == name\r\n        )\r\n        return `{labelMark|${obj.name}} {valueMark|${obj.value} 次}  {percentMark|${obj.percent} %}`\r\n      }\r\n    },\r\n    // 获取报修工单故障类型\r\n    async getWorkOrderErrorType() {\r\n      const res = await GetWorkOrderErrorType({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairFaultTypeOptions.series[0].data = res.Data.map((item) => ({\r\n        name: item.Label,\r\n        value: item.Value,\r\n        percent: item.Rate\r\n      }))\r\n    },\r\n    // 获取各车间趋势\r\n    async getWorkOrderTrend() {\r\n      const res = await GetWorkOrderTrend({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      let xAxisData = []\r\n      this.trendRepairReportsVariousWorkshopsOptions.series = res.Data.map(\r\n        (item) => {\r\n          xAxisData = item.ShopData.map((ele) => ele.Label)\r\n          if (item.ShopName == '报修数量') {\r\n            return {\r\n              name: item.ShopName,\r\n              type: 'line',\r\n              smooth: true,\r\n              symbol: 'none',\r\n              data: item.ShopData.map((ele) => ele.Value)\r\n            }\r\n          } else {\r\n            return {\r\n              name: item.ShopName,\r\n              type: 'bar',\r\n              stack: 'vehicle',\r\n              emphasis: {\r\n                focus: 'series'\r\n              },\r\n              data: item.ShopData.map((ele) => ele.Value)\r\n            }\r\n          }\r\n        }\r\n      )\r\n      this.trendRepairReportsVariousWorkshopsOptions.xAxis.data = xAxisData\r\n    },\r\n\r\n    // 获取设备完好率\r\n    async getDeviceServiceabilityRate() {\r\n      const res = await GetDeviceServiceabilityRate({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.equipmentIntegrityRate = res.Data\r\n      this.equipmentIntegrityRateOptions.series[0].axisLine.lineStyle = {\r\n        color: [\r\n          [res.Data.ServiceabilityRate / 100, 'rgba(0, 211, 167, 0.3)'],\r\n          [1, '#f0f2f8']\r\n        ],\r\n        width: 16\r\n      }\r\n      this.equipmentIntegrityRateOptions.series[1].label.formatter = function(\r\n        pamars\r\n      ) {\r\n        return `${res.Data.ServiceabilityRate}%`\r\n      }\r\n    },\r\n    // 获取设备故障率排行\r\n    async getEquipFailureRateRank() {\r\n      const res = await GetEquipFailureRateRank({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.equipmentFailureRateRanking = res.Data\r\n    },\r\n\r\n    // 获取工单总览统计\r\n    async getTimeoutStatistics() {\r\n      const res = await GetTimeoutStatistics({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairResponseConfig = res.Data\r\n      this.repairResponseOptions.xAxis[0].data = res.Data.List.map(\r\n        (item) => item.Date\r\n      )\r\n      this.repairResponseOptions.series[0].data = res.Data.List.map(\r\n        (item) => item.Timely\r\n      )\r\n      this.repairResponseOptions.series[1].data = res.Data.List.map(\r\n        (item) => item.Timeout\r\n      )\r\n      this.repairResponseOptions.series[2].data = res.Data.List.map(\r\n        (item) => item.Percent\r\n      )\r\n    },\r\n    // 获取工单响应超时统计\r\n    async getWorkorderStatistics() {\r\n      const res = await GetWorkorderStatistics({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairOverview = res.Data\r\n    },\r\n    // 获取等待时长\r\n    getWaitingTime(date) {\r\n      const startDate = new Date(date)\r\n      var endDate = new Date() // 获取当前时间\r\n      var difference = Math.abs(endDate - startDate)\r\n      var days = Math.floor(difference / (1000 * 60 * 60 * 24))\r\n      var hours = Math.floor(\r\n        (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)\r\n      )\r\n      var minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))\r\n      var formattedDifference = ''\r\n\r\n      if (days > 0) {\r\n        formattedDifference += days + '天'\r\n      }\r\n      if (hours > 0) {\r\n        formattedDifference += hours + '小时'\r\n      }\r\n      if (minutes > 0 || (days === 0 && hours === 0)) {\r\n        formattedDifference += minutes + '分钟'\r\n      }\r\n      return formattedDifference\r\n    },\r\n    // 获取状态样式\r\n    getStatusStyle(row) {\r\n      return this.pendingRepairRequestStatus.find(\r\n        (item) => item.value == row.State\r\n      )\r\n    },\r\n    // 获取日期时间\r\n    getStartTime(type) {\r\n      if (type == 1) {\r\n        return dayjs(this.yearMonthValue).format('YYYY')\r\n      } else if (type == 2) {\r\n        return dayjs(this.yearMonthValue).format('YYYY-MM')\r\n      }\r\n    },\r\n    // 设置表格自动滚动\r\n    autoScroll(stop) {\r\n      const table = this.$refs.scroll_Table\r\n      // 拿到表格中承载数据的div元素\r\n      const divData = table.$refs.bodyWrapper\r\n      // 拿到元素后，对元素进行定时增加距离顶部距离，实现滚动效果(此配置为每100毫秒移动1像素)\r\n      if (stop) {\r\n        // 再通过事件监听，监听到 组件销毁 后，再执行关闭计时器。\r\n        window.clearInterval(this.scrolltimer)\r\n      } else {\r\n        this.scrolltimer = window.setInterval(() => {\r\n          // 元素自增距离顶部1像素\r\n          divData.scrollTop += 2\r\n          // 判断元素是否滚动到底部(可视高度+距离顶部=整个高度)\r\n          if (\r\n            divData.clientHeight + divData.scrollTop ==\r\n            divData.scrollHeight\r\n          ) {\r\n            // 重置table距离顶部距离\r\n            divData.scrollTop = 0\r\n            // 重置table距离顶部距离。值=(滚动到底部时，距离顶部的大小) - 整个高度/2\r\n            // divData.scrollTop = divData.scrollTop - divData.scrollHeight / 2\r\n          }\r\n        }, 120) // 滚动速度\r\n      }\r\n    },\r\n\r\n    // 设置表格颜色\r\n    repairProcessingPersonnelCompleteRankingDataClassName({ row, rowIndex }) {\r\n      if (this.isEvenOrOdd(rowIndex + 1)) {\r\n        return 'row-one'\r\n      } else {\r\n        return 'row-two'\r\n      }\r\n    },\r\n    // 设置表格颜色\r\n    pendingRepairRequestDataClassName({ row, rowIndex }) {\r\n      if (this.isEvenOrOdd(rowIndex + 1)) {\r\n        return 'row-one'\r\n      } else {\r\n        return 'row-two'\r\n      }\r\n    },\r\n    //  判断是否是偶数行 还是奇数行\r\n    isEvenOrOdd(number) {\r\n      if (number % 2 === 0) {\r\n        return true\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    // 查看更多\r\n    lookMoreDetail() {\r\n      // let Url = this.jumpUrlList.find(\r\n      //   (item) => item.ModuleCode == ModuleCode\r\n      // ).Url;\r\n      const Platform = 'digitalfactory'\r\n      const ModuleId = localStorage.getItem('ModuleId')\r\n      const ModuleCode = localStorage.getItem('ModuleCode')\r\n      // 获取本月的第一天\r\n      const startOfMonth = dayjs().startOf('month')\r\n      // 获取本月的最后一天\r\n      const endOfMonth = dayjs().endOf('month')\r\n      // 输出结果\r\n      // Create_Date:\"2024-07-19\"\r\n      // Create_EDate:\"2024-08-14\"\r\n      this.$qiankun.switchMicroAppFn(\r\n        Platform,\r\n        'gzt',\r\n        '7622d042-b114-46a0-b1ba-b5621622f058',\r\n        `/business/maintenanceAndUpkeep/workOrderManagement?State=0&ActiveName=first&isJump=true`\r\n      )\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.workOrderStatistics_repair {\r\n  // padding: 10px 15px;\r\n  // height: calc(100vh - 90px);\r\n  overflow-y: auto;\r\n  .header {\r\n    display: flex;\r\n    // align-items: center;\r\n    justify-content: space-between;\r\n    height: 22px;\r\n    > span {\r\n      font-weight: bold;\r\n    }\r\n    .title_content {\r\n      display: flex;\r\n      font-weight: bold;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      span {\r\n        margin-right: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .search_content {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    .label {\r\n      margin-right: 10px;\r\n    }\r\n    .radio {\r\n      margin-right: 10px;\r\n    }\r\n    .picker {\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .repairSatisfaction_content {\r\n    height: 220px;\r\n    width: 100%;\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .repairSatisfactionlists {\r\n      display: flex;\r\n      flex-direction: column;\r\n      .repairSatisfactionlist {\r\n        padding: 2px 0px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        .label {\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          margin-right: 10px;\r\n        }\r\n        .value {\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .repairOverview_content {\r\n    height: 220px;\r\n    .bottom {\r\n      margin-top: 20px;\r\n      display: grid;\r\n      width: 100%;\r\n      grid-template-columns: repeat(3, calc(33% - 10px));\r\n      grid-gap: 20px; /* 设置间距 */\r\n      .main {\r\n        border-radius: 8px 8px 8px 8px;\r\n        padding: 10px 15px;\r\n        background: #fafdff;\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        justify-content: center;\r\n        .left {\r\n          width: 70px;\r\n          height: 70px;\r\n        }\r\n        .right {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          .text {\r\n            font-weight: bold;\r\n            font-size: 24px;\r\n            margin-top: 10px;\r\n          }\r\n          .value {\r\n            font-weight: 400;\r\n            font-size: 14px;\r\n            color: #666666;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .top {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      justify-content: space-around;\r\n      background: #fafdff;\r\n      border-radius: 8px 8px 8px 8px;\r\n      padding: 10px 15px;\r\n      .main {\r\n        display: flex;\r\n        flex-direction: row;\r\n        .left {\r\n          width: 80px;\r\n          height: 80px;\r\n        }\r\n        .right {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          .text {\r\n            font-weight: bold;\r\n            font-size: 24px;\r\n          }\r\n          .value {\r\n            font-weight: 400;\r\n            font-size: 14px;\r\n            color: #666666;\r\n            margin-top: 10px;\r\n            display: flex;\r\n            flex-direction: row;\r\n            align-items: center;\r\n            > span {\r\n              margin-right: 10px;\r\n              display: flex;\r\n              flex-direction: row;\r\n              align-items: center;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .maintenanceWorkOrderProcessingStatus_content {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 300px;\r\n    .left {\r\n      width: 50%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      padding: 0px 18px;\r\n      .title {\r\n        color: rgba(34, 40, 52, 0.85);\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        padding: 18px 0px;\r\n      }\r\n      .left_content {\r\n        background-color: #fafdff;\r\n        padding: 10px 15px;\r\n      }\r\n    }\r\n    .right {\r\n      width: 50%;\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      padding: 0px 18px;\r\n      .title {\r\n        color: rgba(34, 40, 52, 0.85);\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        padding: 18px 0px;\r\n      }\r\n      .right_content {\r\n        background-color: #fafdff;\r\n        padding: 10px 15px;\r\n      }\r\n    }\r\n    .item {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      margin-bottom: 20px;\r\n      .top {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-bottom: 6px;\r\n        font-size: 14px;\r\n        .left {\r\n          color: #333333;\r\n          font-size: 14px;\r\n        }\r\n        .right_color_one {\r\n          color: #298dff;\r\n        }\r\n        .right_color_two {\r\n          color: #00d3a7;\r\n        }\r\n        .right {\r\n          display: flex;\r\n          flex-direction: row;\r\n          .one {\r\n            margin-right: 14px;\r\n          }\r\n          .two {\r\n            width: 36px;\r\n            display: block;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .productionEquipmentLoadRateRanking_content {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: column;\r\n    height: 220px;\r\n    overflow-y: scroll;\r\n    scrollbar-width: none;\r\n    // scrollbar-color: transparent transparent;\r\n    .item {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      margin-bottom: 20px;\r\n      .top {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-bottom: 6px;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .equipmentIntegrityRate_content {\r\n    height: 220px;\r\n    display: flex;\r\n    flex-direction: row;\r\n    justify-content: space-between;\r\n    .equipmentIntegrityRatelists {\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: center;\r\n      width: 40%;\r\n      .equipmentIntegrityRatelist {\r\n        padding: 2px 0px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        margin-right: 20px;\r\n        .label {\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          margin-right: 10px;\r\n        }\r\n        .value {\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .repairResponse_content {\r\n    height: 300px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    .repairResponselists {\r\n      margin-top: -40px;\r\n      background: #fafdff;\r\n      border-radius: 8px 8px 8px 8px;\r\n      padding: 10px 30px;\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      justify-content: space-around;\r\n      width: 60%;\r\n      margin-bottom: 10px;\r\n      .repairResponselist {\r\n        padding: 2px 0px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        margin-right: 20px;\r\n        .label {\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          margin-right: 10px;\r\n        }\r\n        .value {\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .equipmentStartupStatus_content {\r\n    height: 220px;\r\n  }\r\n  .equipmentFailureTrend_content {\r\n    height: 320px;\r\n    .tablenumber {\r\n      width: 30px;\r\n      height: 23px;\r\n      background-size: 100%;\r\n      background-repeat: no-repeat;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      > span {\r\n        margin-top: 10px;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n  .repairStatusEachWorkshop_content {\r\n    height: 300px;\r\n    .right {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      .right_num {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  ::v-deep .el-card__header {\r\n    border-bottom: none !important;\r\n  }\r\n  ::v-deep .el-progress__text {\r\n    font-size: 18px !important;\r\n    color: #666666 !important;\r\n  }\r\n  ::v-deep.el-table .row-one {\r\n    background: rgba(41, 141, 255, 0.03) !important;\r\n  }\r\n\r\n  ::v-deep .el-table .row-two {\r\n    background: rgba(255, 255, 255, 1) !important;\r\n  }\r\n\r\n  // ::v-deep .el-radio-button__inner {\r\n  //   background-color: #ffffff;\r\n  //   // padding: 6px 32px;\r\n  //   height: 32px;\r\n  //   // line-height: 32px;\r\n  //   width: 80px;\r\n  //   font-size: 14px;\r\n  // }\r\n}\r\n</style>\r\n"]}]}