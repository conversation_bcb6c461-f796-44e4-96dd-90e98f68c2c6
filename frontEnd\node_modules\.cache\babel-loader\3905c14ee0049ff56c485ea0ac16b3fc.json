{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\equipmentManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\equipmentManagement\\index.vue", "mtime": 1755674552432}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "getGridByCode", "GetEquipmentList", "MonitoreImportTemplate", "ExportMonitoreEquipment", "LookVideo", "DelEquipment", "MonitoreEquipmentInfo", "ExportEquipmentList", "DialogForm", "WatchVideoDialog", "DeviceInfoDialog", "ImportFile", "downloadFile", "GetDictionaryTreeDetailListByCode", "GetParkArea", "GetTreeAddress", "components", "mixins", "data", "_this", "ruleForm", "EquipmentName", "EquipmentNumber", "InstallSite", "EquipmentType", "customForm", "formItems", "key", "label", "type", "otherOptions", "clearable", "change", "e", "console", "log", "options", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "text", "onclick", "handleDownTemplate", "handleImport", "handleExport", "handleDelete", "handleEdit", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "tableData", "operateOptions", "align", "tableActionsWidth", "tableActions", "actionLabel", "index", "row", "handleDeviceInfo", "Id", "handleLookVideo", "dialogVisible", "dialogTitle", "currentComponent", "componentsConfig", "Data", "componentsFuns", "open", "close", "fetchData", "multipleSelection", "Park_Area", "created", "_this2", "then", "res", "mounted", "_this3", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getDictionaryDetailListByCode", "sent", "stop", "methods", "_this4", "_objectSpread", "Page", "PageSize", "IsSucceed", "TotalCount", "id", "_this5", "length", "$message", "warning", "map", "item", "join", "$confirm", "_ref", "_callee2", "_", "_callee2$", "_context2", "success", "error", "Message", "_x", "apply", "arguments", "catch", "undefined", "_defineProperty", "Monitore_Equipment_Number", "Monitore_Equipment_Name", "Monitore_Equipment_SN_Number", "Monitore_Equipment_Type", "Pisition", "Park_area", "Purpose_Catetory", "Scene", "Site", "submitForm", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "_this6", "Mainstream_Code", "Substream_Code", "Url", "_this7", "code", "_this8"], "sources": ["src/views/business/safetyManagement/equipmentManagement/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 equipmentManagement\">\r\n    <custom-layout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"submitForm\"\r\n          @resetForm=\"fetchData\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </custom-layout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport getGridByCode from '../mixins/index'\r\nimport {\r\n  GetEquipmentList,\r\n  MonitoreImportTemplate,\r\n  ExportMonitoreEquipment,\r\n  LookVideo,\r\n  DelEquipment,\r\n  MonitoreEquipmentInfo,\r\n  ExportEquipmentList\r\n} from '@/api/business/safetyManagement'\r\nimport DialogForm from './components/dialogForm.vue'\r\nimport WatchVideoDialog from './components/watchVideoDialog.vue'\r\nimport DeviceInfoDialog from './components/deviceInfoDialog.vue'\r\nimport ImportFile from './components/importFile.vue'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport { GetDictionaryTreeDetailListByCode } from '@/api/sys'\r\nimport {\r\n  GetParkArea,\r\n  GetTreeAddress\r\n} from '@/api/business/energyManagement.js'\r\n\r\nexport default {\r\n  components: {\r\n    CustomLayout,\r\n    CustomTable,\r\n    CustomForm\r\n  },\r\n  mixins: [getGridByCode],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        EquipmentName: '',\r\n        EquipmentNumber: '',\r\n        InstallSite: '',\r\n        EquipmentType: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'EquipmentName',\r\n            label: '设备名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentNumber',\r\n            label: '设备编码',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'InstallSite',\r\n            label: '设备部署位置',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentType',\r\n            label: '设备类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '下载模板',\r\n              onclick: () => {\r\n                this.handleDownTemplate()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导入',\r\n              onclick: () => {\r\n                this.handleImport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导出',\r\n              onclick: () => {\r\n                this.handleExport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量删除',\r\n              onclick: () => {\r\n                this.handleDelete('batch')\r\n              }\r\n            },\r\n            {\r\n              text: '新增',\r\n              type: 'primary',\r\n              onclick: () => {\r\n                this.handleEdit()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [20, 40, 60, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 1000,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center'\r\n        },\r\n        tableActionsWidth: 220,\r\n        tableActions: [\r\n          {\r\n            actionLabel: '监控链接',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDeviceInfo(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete('single', row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '查看视频',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleLookVideo(row.Id)\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '新增',\r\n      currentComponent: null,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n        },\r\n        fetchData: () => {\r\n          this.fetchData()\r\n        }\r\n      },\r\n      multipleSelection: [],\r\n      Park_Area: ''\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchData()\r\n    this.getGridByCode('equipmentManagement')\r\n    GetParkArea().then((res) => {\r\n      this.Park_Area = res.Data\r\n    })\r\n  },\r\n  async mounted() {\r\n    this.customForm.formItems[3].options =\r\n      await this.getDictionaryDetailListByCode()\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      GetEquipmentList({\r\n        ...this.ruleForm,\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n          this.customTableConfig.tableData = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    handleDelete(type, id) {\r\n      if (type == 'batch') {\r\n        if (this.multipleSelection.length == 0) {\r\n          this.$message.warning('请选择数据!')\r\n          return\r\n        } else {\r\n          id = this.multipleSelection.map((item) => item.Id).join(',')\r\n        }\r\n      }\r\n      this.$confirm('确认删除？', {\r\n        type: 'warning'\r\n      })\r\n        .then(async(_) => {\r\n          await DelEquipment({ id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success('删除成功!')\r\n              this.fetchData()\r\n            } else {\r\n              this.$message.error(res.Message)\r\n            }\r\n          })\r\n        })\r\n        .catch((_) => { })\r\n    },\r\n    handleEdit(index, row, type = 'add') {\r\n      this.currentComponent = DialogForm\r\n      if (type === 'add') {\r\n        this.dialogTitle = '新增'\r\n        this.componentsConfig.Data = {\r\n          Monitore_Equipment_Number: '',\r\n          Monitore_Equipment_Name: '',\r\n          Monitore_Equipment_SN_Number: '',\r\n          Monitore_Equipment_Type: '',\r\n          Pisition: '',\r\n          Monitore_Equipment_Name: '',\r\n          Park_Area: this.Park_Area,\r\n          Site: '',\r\n          Address: '',\r\n          Brand: '',\r\n          Version: '',\r\n          Equipment_Purpose_Catetory: ''\r\n        }\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        row.Park_area = [row.Purpose_Catetory, row.Scene, row.Site]\r\n        this.componentsConfig.Data = { ...row, Park_Area: this.Park_Area }\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    submitForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData()\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.fetchData()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.customTableConfig.currentPage = val\r\n      this.fetchData()\r\n    },\r\n    handleSelectionChange(data) {\r\n      console.log(data)\r\n      this.multipleSelection = data\r\n    },\r\n    handleLookVideo(id) {\r\n      this.currentComponent = WatchVideoDialog\r\n      this.dialogTitle = '查看视频'\r\n      this.dialogVisible = true\r\n      this.componentsConfig.Data = id\r\n    },\r\n    handleDeviceInfo(id) {\r\n      this.currentComponent = DeviceInfoDialog\r\n      this.dialogTitle = '监控链接'\r\n      this.dialogVisible = true\r\n      MonitoreEquipmentInfo({ id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.componentsConfig.Data = {\r\n            Mainstream_Code: res.Data.Mainstream_Code,\r\n            Substream_Code: res.Data.Substream_Code,\r\n            Url: res.Data.Url\r\n          }\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleDownTemplate() {\r\n      MonitoreImportTemplate({ code: 'equipmentManagement' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data, '安防监控设备管理导入模板')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    // handleExport() {\r\n    //   let id = \"\";\r\n    //   if (this.multipleSelection.length == 0) {\r\n    //     this.$message.warning(\"请选择数据!\");\r\n    //     return;\r\n    //   } else {\r\n    //     id = this.multipleSelection.map((item) => item.Id).join(\",\");\r\n    //   }\r\n    //   ExportMonitoreEquipment({\r\n    //     code: \"equipmentManagement\",\r\n    //     id,\r\n    //   }).then((res) => {\r\n    //     if (res.IsSucceed) {\r\n    //       this.$message.success(\"导出成功\");\r\n    //       downloadFile(res.Data, \"安防监控设备管理数据\");\r\n    //     } else {\r\n    //       this.$message.error(res.Message);\r\n    //     }\r\n    //   });\r\n    // },\r\n    handleExport() {\r\n      const Id = this.multipleSelection.map((item) => item.Id).join(',')\r\n      ExportEquipmentList({\r\n        ...this.ruleForm,\r\n        Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('导出成功')\r\n          downloadFile(res.Data, '安防监控设备管理数据')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleImport() {\r\n      this.currentComponent = ImportFile\r\n      this.dialogTitle = '批量导入'\r\n      this.dialogVisible = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang='scss'>\r\n.equipmentManagement {\r\n  // height: calc(100vh - 90px);\r\n  // overflow: hidden;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,OAAAC,aAAA;AACA,SACAC,gBAAA,EACAC,sBAAA,EACAC,uBAAA,EACAC,SAAA,EACAC,YAAA,EACAC,qBAAA,EACAC,mBAAA,QACA;AACA,OAAAC,UAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,UAAA;AACA,SAAAC,YAAA;AACA,SAAAC,iCAAA;AACA,SACAC,WAAA,EACAC,cAAA,QACA;AAEA;EACAC,UAAA;IACAnB,YAAA,EAAAA,YAAA;IACAC,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA;EACA;EACAkB,MAAA,GAAAjB,aAAA;EACAkB,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,QAAA;QACAC,aAAA;QACAC,eAAA;QACAC,WAAA;QACAC,aAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAO,OAAA;UACAN,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,EACA;QACAI,KAAA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAA;cACA1B,KAAA,CAAA2B,kBAAA;YACA;UACA,GACA;YACAF,IAAA;YACAC,OAAA,WAAAA,QAAA;cACA1B,KAAA,CAAA4B,YAAA;YACA;UACA,GACA;YACAH,IAAA;YACAC,OAAA,WAAAA,QAAA;cACA1B,KAAA,CAAA6B,YAAA;YACA;UACA,GACA;YACAJ,IAAA;YACAC,OAAA,WAAAA,QAAA;cACA1B,KAAA,CAAA8B,YAAA;YACA;UACA,GACA;YACAL,IAAA;YACAf,IAAA;YACAgB,OAAA,WAAAA,QAAA;cACA1B,KAAA,CAAA+B,UAAA;YACA;UACA;QAEA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA;QACAC,SAAA;QACAC,cAAA;UACAC,KAAA;QACA;QACAC,iBAAA;QACAC,YAAA,GACA;UACAC,WAAA;UACA/B,YAAA;YACAD,IAAA;UACA;UACAgB,OAAA,WAAAA,QAAAiB,KAAA,EAAAC,GAAA;YACA5C,KAAA,CAAA6C,gBAAA,CAAAD,GAAA,CAAAE,EAAA;UACA;QACA,GACA;UACAJ,WAAA;UACA/B,YAAA;YACAD,IAAA;UACA;UACAgB,OAAA,WAAAA,QAAAiB,KAAA,EAAAC,GAAA;YACA5C,KAAA,CAAA+B,UAAA,CAAAY,KAAA,EAAAC,GAAA;UACA;QACA,GACA;UACAF,WAAA;UACA/B,YAAA;YACAD,IAAA;UACA;UACAgB,OAAA,WAAAA,QAAAiB,KAAA,EAAAC,GAAA;YACA5C,KAAA,CAAA8B,YAAA,WAAAc,GAAA,CAAAE,EAAA;UACA;QACA,GACA;UACAJ,WAAA;UACA/B,YAAA;YACAD,IAAA;UACA;UACAgB,OAAA,WAAAA,QAAAiB,KAAA,EAAAC,GAAA;YACA5C,KAAA,CAAA+C,eAAA,CAAAH,GAAA,CAAAE,EAAA;UACA;QACA;MAEA;MACAE,aAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,gBAAA;QACAC,IAAA;MACA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAtD,KAAA,CAAAgD,aAAA;QACA;QACAO,KAAA,WAAAA,MAAA;UACAvD,KAAA,CAAAgD,aAAA;QACA;QACAQ,SAAA,WAAAA,UAAA;UACAxD,KAAA,CAAAwD,SAAA;QACA;MACA;MACAC,iBAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,KAAAJ,SAAA;IACA,KAAA3E,aAAA;IACAc,WAAA,GAAAkE,IAAA,WAAAC,GAAA;MACAF,MAAA,CAAAF,SAAA,GAAAI,GAAA,CAAAV,IAAA;IACA;EACA;EACAW,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OAEAT,MAAA,CAAAU,6BAAA;UAAA;YADAV,MAAA,CAAA1D,UAAA,CAAAC,SAAA,IAAAU,OAAA,GAAAsD,QAAA,CAAAI,IAAA;UAAA;UAAA;YAAA,OAAAJ,QAAA,CAAAK,IAAA;QAAA;MAAA,GAAAR,OAAA;IAAA;EAEA;EACAS,OAAA;IACArB,SAAA,WAAAA,UAAA;MAAA,IAAAsB,MAAA;MACAhG,gBAAA,CAAAiG,aAAA,CAAAA,aAAA,KACA,KAAA9E,QAAA;QACA+E,IAAA,OAAA1D,iBAAA,CAAAW,WAAA;QACAgD,QAAA,OAAA3D,iBAAA,CAAAY;MAAA,EACA,EAAA2B,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAoB,SAAA;UACAJ,MAAA,CAAAxD,iBAAA,CAAAa,KAAA,GAAA2B,GAAA,CAAAV,IAAA,CAAA+B,UAAA;UACAL,MAAA,CAAAxD,iBAAA,CAAAe,SAAA,GAAAyB,GAAA,CAAAV,IAAA,CAAAA,IAAA;QACA;MACA;IACA;IACAtB,YAAA,WAAAA,aAAApB,IAAA,EAAA0E,EAAA;MAAA,IAAAC,MAAA;MACA,IAAA3E,IAAA;QACA,SAAA+C,iBAAA,CAAA6B,MAAA;UACA,KAAAC,QAAA,CAAAC,OAAA;UACA;QACA;UACAJ,EAAA,QAAA3B,iBAAA,CAAAgC,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAA5C,EAAA;UAAA,GAAA6C,IAAA;QACA;MACA;MACA,KAAAC,QAAA;QACAlF,IAAA;MACA,GACAmD,IAAA;QAAA,IAAAgC,IAAA,GAAA5B,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA2B,SAAAC,CAAA;UAAA,OAAA7B,mBAAA,GAAAG,IAAA,UAAA2B,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAzB,IAAA,GAAAyB,SAAA,CAAAxB,IAAA;cAAA;gBAAAwB,SAAA,CAAAxB,IAAA;gBAAA,OACAvF,YAAA;kBAAAkG,EAAA,EAAAA;gBAAA,GAAAvB,IAAA,WAAAC,GAAA;kBACA,IAAAA,GAAA,CAAAoB,SAAA;oBACAG,MAAA,CAAAE,QAAA,CAAAW,OAAA;oBACAb,MAAA,CAAA7B,SAAA;kBACA;oBACA6B,MAAA,CAAAE,QAAA,CAAAY,KAAA,CAAArC,GAAA,CAAAsC,OAAA;kBACA;gBACA;cAAA;cAAA;gBAAA,OAAAH,SAAA,CAAArB,IAAA;YAAA;UAAA,GAAAkB,QAAA;QAAA,CACA;QAAA,iBAAAO,EAAA;UAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;QAAA;MAAA,KACAC,KAAA,WAAAT,CAAA;IACA;IACAhE,UAAA,WAAAA,WAAAY,KAAA,EAAAC,GAAA;MAAA,IAAAlC,IAAA,GAAA6F,SAAA,CAAAjB,MAAA,QAAAiB,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,KAAArD,gBAAA,GAAA7D,UAAA;MACA,IAAAqB,IAAA;QACA,KAAAuC,WAAA;QACA,KAAAE,gBAAA,CAAAC,IAAA,GAAAsD,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA;UACAC,yBAAA;UACAC,uBAAA;UACAC,4BAAA;UACAC,uBAAA;UACAC,QAAA;QAAA,8BACA,kBACA,KAAArD,SAAA,WACA,gBACA,cACA,gBACA,mCACA,GACA;MACA,WAAAhD,IAAA;QACA,KAAAuC,WAAA;QACAL,GAAA,CAAAoE,SAAA,IAAApE,GAAA,CAAAqE,gBAAA,EAAArE,GAAA,CAAAsE,KAAA,EAAAtE,GAAA,CAAAuE,IAAA;QACA,KAAAhE,gBAAA,CAAAC,IAAA,GAAA2B,aAAA,CAAAA,aAAA,KAAAnC,GAAA;UAAAc,SAAA,OAAAA;QAAA;MACA;MACA,KAAAV,aAAA;IACA;IACAoE,UAAA,WAAAA,WAAArH,IAAA;MACA,KAAAuB,iBAAA,CAAAW,WAAA;MACA,KAAAuB,SAAA;IACA;IACA6D,gBAAA,WAAAA,iBAAAC,GAAA;MACAvG,OAAA,CAAAC,GAAA,iBAAAuG,MAAA,CAAAD,GAAA;MACA,KAAAhG,iBAAA,CAAAY,QAAA,GAAAoF,GAAA;MACA,KAAA9D,SAAA;IACA;IACAgE,mBAAA,WAAAA,oBAAAF,GAAA;MACA,KAAAhG,iBAAA,CAAAW,WAAA,GAAAqF,GAAA;MACA,KAAA9D,SAAA;IACA;IACAiE,qBAAA,WAAAA,sBAAA1H,IAAA;MACAgB,OAAA,CAAAC,GAAA,CAAAjB,IAAA;MACA,KAAA0D,iBAAA,GAAA1D,IAAA;IACA;IACAgD,eAAA,WAAAA,gBAAAqC,EAAA;MACA,KAAAlC,gBAAA,GAAA5D,gBAAA;MACA,KAAA2D,WAAA;MACA,KAAAD,aAAA;MACA,KAAAG,gBAAA,CAAAC,IAAA,GAAAgC,EAAA;IACA;IACAvC,gBAAA,WAAAA,iBAAAuC,EAAA;MAAA,IAAAsC,MAAA;MACA,KAAAxE,gBAAA,GAAA3D,gBAAA;MACA,KAAA0D,WAAA;MACA,KAAAD,aAAA;MACA7D,qBAAA;QAAAiG,EAAA,EAAAA;MAAA,GAAAvB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAoB,SAAA;UACAwC,MAAA,CAAAvE,gBAAA,CAAAC,IAAA;YACAuE,eAAA,EAAA7D,GAAA,CAAAV,IAAA,CAAAuE,eAAA;YACAC,cAAA,EAAA9D,GAAA,CAAAV,IAAA,CAAAwE,cAAA;YACAC,GAAA,EAAA/D,GAAA,CAAAV,IAAA,CAAAyE;UACA;QACA;UACAH,MAAA,CAAAnC,QAAA,CAAAY,KAAA,CAAArC,GAAA,CAAAsC,OAAA;QACA;MACA;IACA;IACAzE,kBAAA,WAAAA,mBAAA;MAAA,IAAAmG,MAAA;MACA/I,sBAAA;QAAAgJ,IAAA;MAAA,GAAAlE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAoB,SAAA;UACAzF,YAAA,CAAAqE,GAAA,CAAAV,IAAA;QACA;UACA0E,MAAA,CAAAvC,QAAA,CAAAY,KAAA,CAAArC,GAAA,CAAAsC,OAAA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAvE,YAAA,WAAAA,aAAA;MAAA,IAAAmG,MAAA;MACA,IAAAlF,EAAA,QAAAW,iBAAA,CAAAgC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA5C,EAAA;MAAA,GAAA6C,IAAA;MACAvG,mBAAA,CAAA2F,aAAA,CAAAA,aAAA,KACA,KAAA9E,QAAA;QACA6C,EAAA,EAAAA;MAAA,EACA,EAAAe,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAoB,SAAA;UACA8C,MAAA,CAAAzC,QAAA,CAAAW,OAAA;UACAzG,YAAA,CAAAqE,GAAA,CAAAV,IAAA;QACA;UACA4E,MAAA,CAAAzC,QAAA,CAAAY,KAAA,CAAArC,GAAA,CAAAsC,OAAA;QACA;MACA;IACA;IACAxE,YAAA,WAAAA,aAAA;MACA,KAAAsB,gBAAA,GAAA1D,UAAA;MACA,KAAAyD,WAAA;MACA,KAAAD,aAAA;IACA;EACA;AACA", "ignoreList": []}]}