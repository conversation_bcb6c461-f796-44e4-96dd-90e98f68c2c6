<template>
  <div class="screenContent app-container abs100">
    <el-card class="box-card h100">
      <div class="text-center">
        <el-button-group class="player-btn-group">
          <el-button
            v-for="list in playerBtnGroup"
            :key="list.num"
            type="primary"
            :class="{ active: playerLength == list.num }"
            @click.prevent="setPlayerLength(list.num)"
          >{{ list.name }}</el-button>
        </el-button-group>
      </div>
      <el-container class="customContainer">
        <el-aside class="aside" width="400px">
          <el-input v-model="filterText" placeholder="输入设备名称搜索" />
          <el-tree
            ref="tree"
            class="filter-tree"
            :data="treeData"
            :props="defaultProps"
            highlight-current
            node-key="Id"
            :filter-node-method="filterNode"
            :default-expanded-keys="defaultExpandedKeys"
            @node-click="treeNodeClick"
          />
        </el-aside>
        <el-main>
          <el-row class="video-show">
            <el-col
              v-for="(player, index) in players"
              :key="index"
              :span="colSpan"
              class="video"
              @click="clickPlayer(player, index, $event)"
              @touchend="clickPlayer(player, index, $event)"
            >
              <LivePlayer
                v-loading="player.bLoading"
                :video-url="player.url"
                :water-mark="player.osd"
                :smart="player.bSmart"
                :poster="player.poster"
                :controls="player.bControls && !loopPlaying"
                live
                muted
                stretch
                :loading.sync="player.bLoading"
                element-loading-text="加载中..."
                element-loading-background="#000"
                @fullscreen="onFullscreenChange(player, index, $event)"
                @error="onError(player, index, $event)"
                @play="onPlay(player, index, $event)"
                @message="$message"
              />
              <div
                v-if="bVideoTitle && player.title"
                class="video-title"
                :title="player.title"
              >
                {{ player.title }}
              </div>
              <div
                v-show="player.url && player.bCloseShow && !loopPlaying"
                class="video-close"
                @click="closeVideo(index, true)"
              >
                关闭
              </div>
            </el-col>
          </el-row>
        </el-main>
      </el-container>
    </el-card>
  </div>
</template>

<script>
import LivePlayer from '@liveqing/liveplayer'
import { GetEquipmentTree, LookVideo } from '@/api/business/safetyManagement'

export default {
  components: {
    LivePlayer
  },
  data() {
    return {
      q: '',
      players: [],
      playerIdx: 0,
      colSpan: 12,
      playerLength: 1,
      loadedData: false,
      localData: {
        num1: {},
        num4: {},
        num9: {}
      },
      channelListDlgTitle: '',
      protocol: '',
      showTree: true,
      showGroupTree: false, // lazy load group tree
      showTip: false,
      treeLoading: false,
      groupTreeLoading: false,
      queryDevTreeLoading: false,
      queryGroupTreeLoading: false,
      defExpandDevs: [],
      devLevelFilter: false,
      groupLevelFilter: false,
      fullscreenFlag: false,
      contextMenuTarget: null,
      contextMenuVisible: false,
      contextMenuNodeData: null,
      treeProps: {

      },
      bSmartStream: false,
      bVideoTitle: false,
      level: 0,
      bPlayerFullscreen: false, // any player is fullscreen
      outHevcTipIdx: -1, // idx of player is out hevc stuck
      activeTab: 'dev',
      filterText: '',
      treeData: [],
      defaultProps: {
        children: 'Children',
        label: 'Name'
      },
      defaultExpandedKeys: []
    }
  },
  computed: {
    playerBtnGroup() {
      var list = [{
        num: 1,
        name: '单屏'
      }, {
        num: 4,
        name: '四分屏'
      }, {
        num: 9,
        name: '九分屏'
      }]
      return list
    },
    playing() {
      var player = this.players[this.playerIdx] || {}
      return !!player.url
    },
    treeEmptyText() {
      return this.treeLoading ? '加载中...' : '暂无数据'
    },
    showQueryDevTree() {
      if (!this.q) return false
      if (this.activeTab === 'dev' && this.devLevelFilter) {
        return true
      }
      return false
    },
    showQueryGroupTree() {
      if (!this.q) return false
      if (this.activeTab === 'group' && this.groupLevelFilter) {
        return true
      }
      return false
    }
  },
  watch: {
    bSmartStream: function(newVal, oldVal) {
      for (const idx in this.players) {
        const player = this.players[idx]
        if (!player) continue
        const _url = player.url
        if (!_url) continue
        player.url = ''
        player.bSmart = newVal
        this.$nextTick(() => {
          player.url = _url
        })
      }
    },
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.setPlayerLength(this.playerLength)
    this.contextMenuTarget = document.querySelector('#tab-tree-wrapper')
    this.getEquipmentTree()
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (to.query.protocol) {
        vm.protocol = to.query.protocol
      }
    })
  },
  beforeRouteUpdate(to, from, next) {
    this.clearVideos()
    if (to.query.protocol) {
      this.protocol = to.query.protocol
    }
    next()
  },
  beforeRouteLeave(to, from, next) {
    this.clearVideos()
    next()
  },
  methods: {
    clearVideos() {
      this.outHevcTipIdx = -1
      for (var idx in this.players) {
        this.closeVideo(idx)
      }
    },
    play(index, channel, next) {
      console.log(channel)
      var i = 0
      var player = null
      for (var _player of this.players) {
        if (index === i) {
          player = _player
          break
        }
        i++
      }
      if (!player) {
        this.$message({
          type: 'error',
          message: '当前播放窗口已被占满！'
        })
        return
      }
      if (player.bLoading) return
      player.bLoading = true
      if (next) {
        this.setPlayerIdx(index + 1)
      }
      if (channel.Node) {
        player.node = channel.Node
        this.$set(player.node, 'playing', true)
        delete channel.Node
      }
      LookVideo({ id: channel.ID }).then(res => {
        if (res.IsSucceed) {
          const videoUrl = res.Data
          player.bSmart = this.playerLength > 1 && this.bSmartStream
          player.url = videoUrl
          player.code = channel.ID
          player.protocol = 'HLS'
          player.title = channel.ID
          if (player.node) {
            this.$delete(player.node, 'playing')
            var play = player.node.play || []
            if (videoUrl) {
              play.push(index)
            }
            if (play.length) {
              this.$set(player.node, 'play', play)
            }
          }
          if (!this.loadedData) {
            this.localData['num' + this.playerLength] = {}
          }
          this.localData['num' + this.playerLength]['c' + index] = JSON.stringify(channel)
          this.setLocalData()
        } else {
          this.closeVideo(this.playerIdx)
          this.$message.error(res.Message)
        }
      })
    },
    closeVideo(idx, manual = false) {
      var player = this.players[idx]
      if (!player) return
      if (player.closeTimer) {
        clearTimeout(player.closeTimer)
        player.closeTimer = 0
      }
      if (this.outHevcTipIdx === idx) {
        this.outHevcTipIdx = -1
      }
      if (player.node) {
        var play = player.node.play || []
        play = play.filter(val => val !== idx)
        if (play.length) {
          this.$set(player.node, 'play', play)
        } else {
          this.$delete(player.node, 'play')
          this.$delete(player.node, 'playing')
        }
        delete player.node
      }
      player.mediaInfo = null
      player.bCloseShow = false
      player.bControls = false
      player.bLoading = false
      player.bSmart = false
      player.bFullscreen = false
      player.poster = ''
      player.title = ''
      player.osd = ''
      player.url = ''
      player.ptzType = 0
      player.serial = ''
      player.code = ''
      if (manual) {
        delete this.localData['num' + this.playerLength]['c' + idx]
        this.setLocalData()
      }
    },
    setPlayerLength(len) {
      if (len === this.players.length) return
      this.clearVideos()
      this.players = []
      this.playerLength = len
      len === 1 ? this.colSpan = 24 : len === 4 ? this.colSpan = 12 : this.colSpan = 8
      this.loadedData = false
      this.playerIdx = 0
      for (var i = 0; i < len; i++) {
        this.players.push({
          serial: '',
          code: '',
          url: '',
          ptzType: 0,
          protocol: '',
          poster: '',
          title: '',
          osd: '',
          bLoading: false,
          bCloseShow: false,
          bControls: false,
          bSmart: false,
          bFullscreen: false,
          closeTimer: 0,
          closeInterval: 0,
          mediaInfo: null
        })
      }
    },
    setPlayerIdx(idx) {
      var newIdx = idx % this.players.length
      this.playerIdx = newIdx
    },
    onError(player, idx, e) {
      if (e === 'MediaError' && player.mediaInfo && String(player.mediaInfo.videoCodec).startsWith('hvc')) {
        this.outHevcTipIdx = idx
        console.log('提示: 正在播放 H265 FLV 直出流, 确保浏览器版本较新, 并且开启硬件加速')
      }
    },
    onPlay(player, idx, t) {
      if (this.outHevcTipIdx === idx && t >= 1) {
        this.outHevcTipIdx = -1
      }
    },
    onFullscreenChange(player, idx, bFullscreen) {
      if (player) {
        player.bFullscreen = bFullscreen
        this.bPlayerFullscreen = bFullscreen
        if (bFullscreen) {
          $(`#dev-tree-right .video-show .video:eq(${idx}) .ptz-block-fs`).draggable({
            handle: '.ptz-center',
            cancel: '.ptz-cell',
            containment: `#dev-tree-right .video-show .video:eq(${idx}) .video-js`,
            delay: 100
          })
        } else {
          $(`#dev-tree-right .video-show .video:eq(${idx}) .ptz-block-fs`).draggable('destroy')
        }
      }
    },
    fullscreen() {
      // if (this.isMobile()) {
      //   this.$message({
      //     type: "error",
      //     message: "请在电脑浏览器上使用该功能"
      //   });
      //   return;
      // }
      this.$fullscreen.enter(this.$el.querySelector(`.video-show > div`), {
        wrap: false,
        callback: f => {
          this.fullscreenFlag = f
        }
      })
    },
    treeNodeClick(data, node) {
      console.log(node.isLeaf && (node.data.ParentId ?? '') !== '')
      if (node.isLeaf && (node.data.ParentId ?? '') !== '') {
        this.contextMenuNodeData = null
        this.contextMenuNode = null
        if (node && !node.playing) {
          if (!node.clickCnt || node.clickCnt > 1) {
            node.clickCnt = 1
          } else {
            node.clickCnt++
          }
          var player = this.players[this.playerIdx] || {}
          if (player.bLoading) return
          this.closeVideo(this.playerIdx)
          this.$nextTick(() => {
            this.play(this.playerIdx, {
              ID: data.Id,
              Name: data.Name,
              Node: node
            }, true)
          })
        }
      }
    },
    async getEquipmentTree() {
      const res = await GetEquipmentTree()
      console.log('res', res)
      if (res.IsSucceed) {
        this.treeData = res.Data
        if ((res.Data ?? []).length > 0) {
          this.$nextTick(() => {
            this.defaultExpandedKeys = [res.Data[0]?.Id, res.Data[0]?.Children[0]?.Id, res.Data[0]?.Children[0]?.Children[0]?.Id]
            this.play(this.playerIdx, {
              ID: res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Id,
              Name: res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Name,
              Node: this.$refs.tree.getNode(res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Id)
            }, true)
            this.$refs.tree.setCurrentKey(res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Id)
          })
        }
      } else {
        this.$message.error(res.Message)
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.Name.indexOf(value) !== -1
    }
  }
}
</script>
<style lang="scss" scoped>
.screenContent {
  .text-center {
    text-align: center;
  }
  .player-btn-group {
    .el-button--primary {
      color: #567;
      background: #fff;
      border: 1px solid #dcdfe6 !important;
    }
    .active {
      background-color: #298dff;
      border: 1px solid #298dff !important;
      color: #fff;
    }
  }
  #screen-sticky > #screen-sticky-bottom {
    display: none;
  }
  #screen-sticky-wrapper.sticky > #screen-sticky > #screen-sticky-bottom {
    display: block;
  }
  .customContainer {
    height: 100%;
    .aside {
      display: flex;
      flex-direction: column;
      .filter-tree {
        flex: 1;
        overflow-y: auto;
        padding: 20px 0;
      }
    }
  }
  .video-show {
    .video {
      border: 1px solid #fff;
    }
  }
}
</style>
