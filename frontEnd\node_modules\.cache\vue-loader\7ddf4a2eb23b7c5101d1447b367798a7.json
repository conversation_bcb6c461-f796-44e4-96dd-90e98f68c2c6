{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\lampManagement\\operationLog\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\lampManagement\\operationLog\\index.vue", "mtime": 1755674552428}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/lampManagement/operationLog", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        >\r\n          <!-- <template #customBtn=\"{slotScope}\"><el-button type=\"text\" @click=\"handelStart(slotScope)\">{{ slotScope.Status == 1 ? '停用' :'启用' }}</el-button></template> -->\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport {\r\n  GetOperateLogPageList,\r\n  ExportExcelAsync,\r\n  ClearOpearteLog\r\n} from '@/api/business/lampManagement'\r\nexport default {\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        EquipmentName: '',\r\n        OperateType: '',\r\n        StartTime: null,\r\n        EndTime: null,\r\n        ProjectName: '',\r\n        Date: []\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'EquipmentName',\r\n            label: '设备查询',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'OperateType',\r\n            label: '操作类型',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [\r\n              {\r\n                label: '查看图纸',\r\n                value: '查看图纸'\r\n              },\r\n              {\r\n                label: '查看联系人',\r\n                value: '查看联系人'\r\n              },\r\n              {\r\n                label: '连接联系人',\r\n                value: '连接联系人'\r\n              },\r\n              {\r\n                label: '评价联系人',\r\n                value: '评价联系人'\r\n              }\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Date',\r\n            label: '时间范围',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              type: 'datetimerange',\r\n              rangeSeparator: '至',\r\n              startPlaceholder: '开始日期',\r\n              endPlaceholder: '结束日期',\r\n              clearable: true,\r\n              valueFormat: 'yyyy-MM-dd HH:mm'\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n              if (e && e.length !== 0) {\r\n                this.ruleForm.StartTime = e[0]\r\n                this.ruleForm.EndTime = e[1]\r\n              } else {\r\n                this.ruleForm.StartTime = null\r\n                this.ruleForm.EndTime = null\r\n              }\r\n            }\r\n          },\r\n          {\r\n            key: 'ProjectName',\r\n            label: '项目名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'DrawingName',\r\n            label: '图纸',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n\r\n            {\r\n              key: 'batch',\r\n              disabled: false, // 是否禁用\r\n              text: '导出',\r\n              onclick: (item) => {\r\n                this.ExportData()\r\n              }\r\n            },\r\n            {\r\n              text: '清空',\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: false, // 是否禁用\r\n              icon: '', //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (index, row) => {\r\n                this.handleDelete(index, row)\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          {\r\n            label: '日志时间',\r\n            key: 'CreateDate'\r\n          },\r\n          {\r\n            label: '终端设备',\r\n            key: 'EquipmentName'\r\n          },\r\n          {\r\n            label: '设备ID',\r\n            key: 'EquipmentId'\r\n          },\r\n          {\r\n            label: '操作类型',\r\n            key: 'OperateType'\r\n          },\r\n          {\r\n            label: '操作人员',\r\n            key: 'CreateUserName'\r\n          },\r\n          {\r\n            label: 'IP地址',\r\n            key: 'IPAddress'\r\n          },\r\n          {\r\n            label: '项目名称',\r\n            key: 'ProjectName'\r\n          },\r\n          {\r\n            label: '图纸',\r\n            key: 'DrawingName'\r\n          },\r\n          {\r\n            label: '服务提供人',\r\n            key: 'ServiceUserName'\r\n          },\r\n          {\r\n            label: '内容',\r\n            key: 'Content'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.onFresh()\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.fetchData()\r\n    },\r\n    async fetchData() {\r\n      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null\r\n        this.ruleForm.EndTime = null\r\n      }\r\n      await GetOperateLogPageList({\r\n        ...this.ruleForm,\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.tableData = res.Data.Data\r\n          this.customTableConfig.total = res.Data.Total\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 导出\r\n    ExportData() {\r\n      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null\r\n        this.ruleForm.EndTime = null\r\n      }\r\n      ExportExcelAsync({ ...this.ruleForm,\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize }).then((res) => {\r\n        const url = window.URL.createObjectURL(new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }))\r\n        const link = document.createElement('a')\r\n        link.style.display = 'none'\r\n        link.href = url\r\n        // 文件名\r\n        link.setAttribute('download', '操作日志.xlsx')\r\n        document.body.appendChild(link)\r\n        link.click()\r\n      })\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row)\r\n      this.$confirm('该操作将清空所有数据，是否继续？', {\r\n        type: 'warning'\r\n      })\r\n        .then((_) => {\r\n          ClearOpearteLog().then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n              this.customTableConfig.pageSize = 1\r\n              this.onFresh()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch((_) => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.onFresh()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.onFresh()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      console.log(selection)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.layout {\r\n  height: 100%;\r\n  width: 100%;\r\n  position: absolute;\r\n  ::v-deep {\r\n    .CustomLayout {\r\n      .layoutTable {\r\n        height: 0;\r\n        .CustomTable {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          .table {\r\n            flex: 1;\r\n            height: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            .el-table {\r\n              flex: 1;\r\n              height: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}