{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlEquipmentManagement\\index.vue?vue&type=style&index=0&id=0b886316&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlEquipmentManagement\\index.vue", "mtime": 1755674552408}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5tdDIwIHsNCiAgbWFyZ2luLXRvcDogMTBweDsNCn0NCi5sYXlvdXR7DQogIGhlaWdodDogY2FsYygxMDB2aCAtIDkwcHgpOw0KICBvdmVyZmxvdzogYXV0bzsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAidA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/accessControl/accessControlEquipmentManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport DialogForm from './dialogForm.vue'\r\nimport DeviceConnect from './deviceConnect.vue'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport getGridByCode from '../../safetyManagement/mixins/index'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport {\r\n  GetEquipmentlList,\r\n  DelEquipment,\r\n  ExportEntrancePersonnel,\r\n  SyncEquipment\r\n} from '@/api/business/hazardousChemicals'\r\nimport { GetParkArea } from '@/api/business/energyManagement.js'\r\n\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  mixins: [getGridByCode, addRouterPage],\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        Entrance_Equipment_Name: '',\r\n        Entrance_Equipment_Type: '',\r\n        Position: '',\r\n        Platform_Name: '',\r\n        Status: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Entrance_Equipment_Name',\r\n            label: '设备名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Entrance_Equipment_Type',\r\n            label: '设备类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position',\r\n            label: '安装位置',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Platform_Name',\r\n            label: '平台名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          /*  {\r\n             key: 'Position',\r\n             label: '应用场景',\r\n             type: 'input',\r\n             otherOptions: {\r\n               clearable: true\r\n             },\r\n             change: (e) => {\r\n               console.log(e)\r\n             }\r\n           },\r\n           {\r\n             key: 'Position',\r\n             label: '所属区域',\r\n             type: 'input',\r\n             otherOptions: {\r\n               clearable: true\r\n             },\r\n             change: (e) => {\r\n               console.log(e)\r\n             }\r\n           }, */\r\n          {\r\n            key: 'Status',\r\n            label: '状态',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [\r\n              { label: '在线', value: '在线' },\r\n              { label: '离线', value: '离线' }\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              key: 'synchronous',\r\n              text: '设备同步',\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: false, // 是否禁用\r\n              icon: '', //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handelSyncEquipment()\r\n              }\r\n            },\r\n            // {\r\n            //   text: '新增',\r\n            //   round: false, // 是否圆角\r\n            //   plain: false, // 是否朴素\r\n            //   circle: false, // 是否圆形\r\n            //   loading: false, // 是否加载中\r\n            //   disabled: true, // 是否禁用\r\n            //   icon: '', //  图标\r\n            //   autofocus: false, // 是否聚焦\r\n            //   type: 'primary', // primary / success / warning / danger / info / text\r\n            //   size: 'small', // medium / small / mini\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleCreate()\r\n            //   }\r\n            // },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        loading: false,\r\n        operateOptions: {\r\n          width: '240px',\r\n          align: 'center'\r\n        },\r\n        tableActionsWidth: 180,\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text',\r\n              disabled: false // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text',\r\n              disabled: false // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row)\r\n            }\r\n          }\r\n          // {\r\n          //   actionLabel: '设备连接',\r\n          //   otherOptions: {\r\n          //     type: 'text',\r\n          //     disabled: true // 是否禁用\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleConent(row)\r\n          //   }\r\n          // }\r\n          /* {\r\n            actionLabel: '通行授权',\r\n            otherOptions: {\r\n              type: 'text',\r\n              disabled: true, // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleConfig(row)\r\n            }\r\n          }, */\r\n        ]\r\n      },\r\n      Park_Area: '',\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/add',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'ConfigureAuthorizationList',\r\n          meta: { title: `配置授权名单` }\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  async created() {\r\n    this.init()\r\n    this.customForm.formItems[1].options = await this.getDictionaryDetailListByCode()\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getEquipmentList()\r\n    },\r\n    init() {\r\n      this.getGridByCodePic('AccessControlEquipmentManagement', 'Allow_Pass_Visitors', false)\r\n      GetParkArea().then(res => {\r\n        this.Park_Area = res.Data\r\n      })\r\n      this.getEquipmentList()\r\n    },\r\n    async getEquipmentList() {\r\n      this.customTableConfig.loading = true\r\n      const res = await GetEquipmentlList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      }).finally(() => {\r\n        this.customTableConfig.loading = false\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.dialogVisible = true\r\n      this.currentComponent = DialogForm\r\n      this.componentsConfig.Data = {\r\n        Entrance_Equipment_Number: '',\r\n        Entrance_Equipment_Name: '',\r\n        Entrance_Equipment_Type: '',\r\n        Allow_Pass_Visitors: false,\r\n        Recognition_Way: [],\r\n        Park_area: [],\r\n        Address: '',\r\n        Position: '',\r\n        Platform_Name: '',\r\n        Platform_Contact_Way: '',\r\n        Engineer: '',\r\n        Engineer_Contact_Way: '',\r\n        Equipment_Purpose_Catetory: '',\r\n        Park_Area: this.Park_Area\r\n      }\r\n    },\r\n    // 同步设备\r\n    handelSyncEquipment() {\r\n      this.$confirm('此操作将进行设备同步, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'synchronous').loading = true\r\n        SyncEquipment({}).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '同步成功!'\r\n            })\r\n            this.getEquipmentList()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        }).finally(() => {\r\n          this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'synchronous').loading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消同步'\r\n        })\r\n      })\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row)\r\n      console.log(this)\r\n      this.$confirm('确认删除？', {\r\n        type: 'warning'\r\n      })\r\n        .then(async (_) => {\r\n          const res = await DelEquipment({\r\n            id: row.Id\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.$message.success('操作成功')\r\n            this.getEquipmentList()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n        .catch((_) => { })\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      this.currentComponent = DialogForm\r\n      let isShowBtn = true\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        isShowBtn = false\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        isShowBtn = true\r\n      }\r\n      this.dialogVisible = true\r\n      const Park_area = (row.Scene ?? '') == '' ? [row.Purpose_Catetory] : (row.Site ?? '') == '' ? [row.Purpose_Catetory, row.Scene] : [row.Purpose_Catetory, row.Scene, row.Site]\r\n      // row.Allow_Pass_Visitors = row.Allow_Pass_Visitors.toString() == 'false' ? '不允许' : '允许'\r\n      row.Pass_Visitors = row.Allow_Pass_Visitors.toString() == 'false' ? '不允许' : '允许'\r\n      this.componentsConfig.Data = { ...row, Recognition_Way: (row.Recognition_Way ?? '').split(','), Park_Area: this.Park_Area, Park_area, isShowBtn }\r\n    },\r\n    async handleExport() {\r\n      if (this.tableSelection.length == 0) {\r\n        this.$message.warning('请选择数据在导出')\r\n        return\r\n      }\r\n      const res = await ExportEntrancePersonnel({\r\n        id: this.tableSelection.map((item) => item.Id).toString(),\r\n        code: 'AccessControlEquipmentManagement'\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '门禁设备管理数据')\r\n      } else {\r\n        this.$message(res.Message)\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.getEquipmentList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.getEquipmentList()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    },\r\n    handleConent(row) {\r\n      this.dialogVisible = true\r\n      this.dialogTitle = '确认设备连接'\r\n      this.currentComponent = DeviceConnect\r\n      this.componentsConfig.Data = { ...row }\r\n    },\r\n    handleConfig(row) {\r\n      this.$router.push({ name: 'ConfigureAuthorizationList', query: { pg_redirect: this.$route.name, id: row.Id } })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}