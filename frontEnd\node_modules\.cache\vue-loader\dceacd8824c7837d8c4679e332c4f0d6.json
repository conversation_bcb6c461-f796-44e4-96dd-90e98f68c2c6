{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\internalVehicleManagement\\index.vue?vue&type=style&index=0&id=bf57d8cc&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\internalVehicleManagement\\index.vue", "mtime": 1755506574531}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCkBpbXBvcnQgIkAvdmlld3MvYnVzaW5lc3MvdmVoaWNsZUJhcnJpZXIvaW5kZXguc2NzcyI7DQoNCi5pbWd3YXBwZXIgew0KICB3aWR0aDogMTAwcHg7DQogIGhlaWdodDogMTAwcHg7DQp9DQouZW1wdHktaW1nIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAigBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/vehicleBarrier/pJVehicleBarrier/internalVehicleManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        >\r\n          <template #formSlot=\"{ slotScope }\">\r\n            <el-cascader\r\n              v-if=\"slotScope.key == 'DeptId'\"\r\n              style=\"width: 100%\"\r\n              ref=\"departmentPersonnelCascader\"\r\n              v-model=\"departmentPersonnelValue\"\r\n              :options=\"departmentPersonnelOptions\"\r\n              :props=\"{\r\n                expandTrigger: 'hover',\r\n                checkStrictly: true,\r\n              }\"\r\n              :clearable=\"true\"\r\n              @change=\"departmentPersonnelHandleChange\"\r\n            ></el-cascader>\r\n          </template>\r\n        </CustomForm>\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"600px\"\r\n      @closed=\"closedDialog\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"dialogRef\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport {\r\n  VehiclesGetVehicleList,\r\n  VehiclesDelVehicle,\r\n  VehiclesExportData,\r\n  VehiclesGetDeptList,\r\n  GetDictionaryDetailListByCode,\r\n  VehiclesDownloadTemplate,\r\n  VehiclesImportDataStream,\r\n} from \"@/api/business/vehicleBarrier.js\";\r\nimport baseInfo from \"./dialog/baseInfo.vue\";\r\nimport importDialog from \"@/views/business/vehicleBarrier/components/import.vue\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/pJVehicleBarrier/mixins/export\";\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nexport default {\r\n  Name: \"internalVehicleManagement\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n    baseInfo,\r\n    importDialog,\r\n  },\r\n  mixins: [exportInfo, addRouterPage],\r\n  data() {\r\n    return {\r\n      // 部门人员\r\n      departmentPersonnelValue: [],\r\n      departmentPersonnelOptions: [],\r\n\r\n      ruleForm: {\r\n        Number: \"\",\r\n        UserName: \"\",\r\n        UserPhone: \"\",\r\n        DeptId: \"\",\r\n        VehicleType: \"\",\r\n      },\r\n      componentsConfig: {\r\n        interfaceName: VehiclesImportDataStream,\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogTitle: \"\",\r\n      dialogVisible: false,\r\n      currentComponent: null,\r\n      //\r\n      PassImg: \"\", // 图片\r\n      vehicleTypeOption: [], // 车辆类型\r\n      tableSelection: [],\r\n      selectIds: [],\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Number\", // 字段ID\r\n            label: \"车牌号码\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"UserName\",\r\n            label: \"车主姓名\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"UserPhone\",\r\n            label: \"联系方式\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"DeptId\",\r\n            label: \"所属部�?,\r\n            type: \"slot\",\r\n          },\r\n          // {\r\n          //   key: \"DeptId\",\r\n          //   label: \"所属部�?,\r\n          //   type: \"select\",\r\n          //   options: [],\r\n          //   otherOptions: {\r\n          //     clearable: true,\r\n          //   },\r\n          //   change: (e) => {},\r\n          // },\r\n          {\r\n            key: \"VehicleType\",\r\n            label: \"车辆类型\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n        ],\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"新增\",\r\n              type: \"primary\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n            {\r\n              text: \"下载模板\",\r\n              onclick: (item) => {\r\n                this.ExportData(\r\n                  [],\r\n                  \"内部车辆管理模板\",\r\n                  VehiclesDownloadTemplate\r\n                );\r\n              },\r\n            },\r\n            {\r\n              text: \"批量导入\",\r\n              onclick: (item) => {\r\n                this.currentComponent = \"importDialog\";\r\n                this.dialogVisible = true;\r\n                this.dialogTitle = \"批量导入\";\r\n                this.componentsConfig = {\r\n                  interfaceName: VehiclesImportDataStream,\r\n                };\r\n              },\r\n            },\r\n\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                this.ExportData(\r\n                  this.ruleForm,\r\n                  \"内部车辆管理\",\r\n                  VehiclesExportData\r\n                );\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [20, 50, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: \"100%\",\r\n        tableActionsWidth: \"160\",\r\n        tableColumns: [\r\n          {\r\n            label: \"车牌号码\",\r\n            key: \"Number\",\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: \"车主姓名\",\r\n            key: \"UserName\",\r\n          },\r\n          {\r\n            label: \"车主联系方式\",\r\n            key: \"UserPhone\",\r\n          },\r\n          {\r\n            label: \"所属部�?,\r\n            key: \"DeptName\",\r\n          },\r\n\r\n          {\r\n            label: \"车辆类型\",\r\n            key: \"VehicleTypeName\",\r\n          },\r\n          {\r\n            label: \"更新�?,\r\n            key: \"ModifyUserName\",\r\n          },\r\n          {\r\n            label: \"更新时间\",\r\n            key: \"ModifyDate\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.$router.push({\r\n                name: \"InternalVehicleManagementView\",\r\n                query: { pg_redirect: this.$route.name, Id: row.Id },\r\n              });\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, \"edit\");\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/view\",\r\n          hidden: true,\r\n          component: () => import(\"./dialog/view.vue\"),\r\n          meta: { title: \"内部车辆管理详情\" },\r\n          name: \"InternalVehicleManagementView\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    // 测试\r\n    this.init();\r\n    //\r\n    this.getDictionaryDetailListByCode();\r\n\r\n    this.vehiclesGetDeptList();\r\n  },\r\n  methods: {\r\n    // 车辆类型\r\n    async getDictionaryDetailListByCode() {\r\n      await GetDictionaryDetailListByCode({\r\n        dictionaryCode: \"VehiclesType\",\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customForm.formItems.find(\r\n            (item) => item.key == \"VehicleType\"\r\n          ).options = res.Data.map((item) => ({\r\n            label: item.Display_Name,\r\n            value: item.Value,\r\n          }));\r\n        } else {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: res.Message,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 部门人员change\r\n    departmentPersonnelHandleChange(values) {\r\n      console.log(values, \"values\");\r\n      this.ruleForm.DeptId = values[values.length - 1];\r\n      // let userInfo =\r\n      //   this.$refs[\"departmentPersonnelCascader\"].getCheckedNodes()[0].data;\r\n      // this.ruleForm.UserPhone = userInfo.UserPhone;\r\n      // this.ruleForm.UserDeptName = userInfo.UserDeptName;\r\n      // this.ruleForm.UserDept = userInfo.UserDeptId;\r\n      // this.ruleForm.UserId = userInfo.Id;\r\n      // this.ruleForm.UserName = userInfo.Name;\r\n    },\r\n\r\n    // 部门\r\n    async vehiclesGetDeptList() {\r\n      await VehiclesGetDeptList({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          console.log(res, \"res\");\r\n          this.departmentPersonnelOptions = this.setCascadeData([res.Data]);\r\n          // this.customForm.formItems.find(\r\n          //   (item) => item.key == \"DeptId\"\r\n          // ).options = res.Data.map((item) => ({\r\n          //   label: item.DeptName,\r\n          //   value: item.DeptId,\r\n          // }));\r\n        } else {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: res.Message,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 递归\r\n    setCascadeData(data) {\r\n      return data.map((item) => {\r\n        //   label: item.DeptName,\r\n        //   value: item.DeptId,\r\n        let newItem = { ...item, label: item.DeptName, value: item.DeptId };\r\n        if (newItem.Child && newItem.Child.length > 0) {\r\n          newItem.children = this.setCascadeData(newItem.Child);\r\n        } else {\r\n          delete newItem.Child;\r\n        }\r\n        return newItem;\r\n      });\r\n    },\r\n\r\n    // v2 版本导出\r\n    async handleAllExport() {\r\n      const res = await VehiclesExportData({\r\n        // Id: this.tableSelection.map((item) => item.Id).toString(),\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        downloadFile(res.Data, \"21\");\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.departmentPersonnelValue = [];\r\n      this.ruleForm.DeptId = \"\";\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n    },\r\n    async init() {\r\n      await this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      const res = await VehiclesGetVehicleList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.Total;\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.currentComponent = \"baseInfo\";\r\n      this.dialogTitle = \"新增\";\r\n      this.dialogVisible = true;\r\n      this.componentsConfig = {\r\n        type: \"add\",\r\n      };\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row);\r\n      console.log(this);\r\n      this.$confirm(\"确认删除?\", {\r\n        type: \"warning\",\r\n      })\r\n        .then(async (_) => {\r\n          const res = await VehiclesDelVehicle({\r\n            Id: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: \"删除成功\",\r\n              type: \"success\",\r\n            });\r\n            this.init();\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: \"error\",\r\n            });\r\n          }\r\n        })\r\n        .catch((_) => {\r\n          this.$message({\r\n            type: \"info\",\r\n            message: \"已取消删�?,\r\n          });\r\n        });\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type);\r\n      this.currentComponent = \"baseInfo\";\r\n      if (type === \"edit\") {\r\n        this.dialogTitle = \"编辑\";\r\n      }\r\n      this.componentsConfig = {\r\n        row,\r\n        type,\r\n      };\r\n      this.dialogVisible = true;\r\n    },\r\n    // 关闭弹窗\r\n    closedDialog() {\r\n      this.dialogVisible = false;\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.onFresh();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.onFresh();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      // const Ids = [];\r\n      this.tableSelection = selection;\r\n      // this.tableSelection.forEach((item) => {\r\n      //   Ids.push(item.Id);\r\n      // });\r\n      // this.selectIds = Ids;\r\n    },\r\n    handleview(row) {\r\n      this.dialogVisible = true;\r\n      this.PassImg = row.PassImg;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n\r\n.imgwapper {\r\n  width: 100px;\r\n  height: 100px;\r\n}\r\n.empty-img {\r\n  text-align: center;\r\n}\r\n</style>\r\n"]}]}