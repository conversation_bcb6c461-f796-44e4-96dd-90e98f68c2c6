{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\trafficRecords\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\trafficRecords\\index.vue", "mtime": 1755674552437}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "VBPassRecordGetDropList", "VBPassRecordGetPassRecordList", "VBPassRecordGetPassRecordDetail", "VBPassRecordExportData", "exportInfo", "addRouterPage", "dayjs", "Name", "components", "mixins", "data", "_this", "ruleForm", "Number", "StartTime", "EndTime", "AccessType", "UserName", "PassType", "PassMode", "Date", "dialogVisible", "PassImg", "vehicleTypeOption", "tableSelection", "selectIds", "customForm", "formItems", "key", "label", "type", "otherOptions", "clearable", "input", "e", "change", "rangeSeparator", "startPlaceholder", "endPlaceholder", "valueFormat", "length", "options", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "disabled", "text", "onclick", "item", "console", "log", "ExportData", "pageSizeOptions", "currentPage", "pageSize", "total", "height", "tableColumns", "fixed", "tableData", "tableActions", "actionLabel", "index", "row", "$router", "push", "name", "query", "pg_redirect", "$route", "Id", "addPageArray", "path", "hidden", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "meta", "title", "created", "vBPassRecordGetDropList", "mounted", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "JumpParams", "wrap", "_callee$", "_context", "prev", "next", "$qiankun", "getMicroAppJumpParamsFn", "isJump", "format", "onFresh", "stop", "<PERSON><PERSON><PERSON><PERSON>", "setMicroAppJumpParamsFn", "methods", "_this3", "_callee2", "res", "_callee2$", "_context2", "sent", "IsSucceed", "find", "v", "Data", "List", "map", "Key", "value", "Value", "searchForm", "resetForm", "fetchData", "init", "_this4", "_callee3", "_callee3$", "_context3", "_this5", "_callee4", "_callee4$", "_context4", "_objectSpread", "Page", "PageSize", "Total", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "selection", "Ids", "for<PERSON>ach", "handleview"], "sources": ["src/views/business/vehicleBarrier/pJVehicleBarrier/trafficRecords/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"查看\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"600px\"\r\n    >\r\n      <el-image v-if=\"PassImg\" :src=\"PassImg\" class=\"imgwapper\" />\r\n      <div v-else class=\"empty-img\">暂无图片</div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport {\r\n  VBPassRecordGetDropList,\r\n  VBPassRecordGetPassRecordList,\r\n  VBPassRecordGetPassRecordDetail,\r\n  VBPassRecordExportData,\r\n} from \"@/api/business/vehicleBarrier.js\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/mixins/export.js\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nimport dayjs from \"dayjs\";\r\nexport default {\r\n  Name: \"vehiclePeerRecord\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  mixins: [exportInfo, addRouterPage],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        Number: \"\",\r\n        StartTime: null,\r\n        EndTime: null,\r\n        AccessType: \"\",\r\n        UserName: \"\",\r\n        PassType: \"\",\r\n        PassMode: \"\",\r\n        Date: [],\r\n      },\r\n      dialogVisible: false,\r\n      PassImg: \"\", // 图片\r\n      vehicleTypeOption: [], // 车辆类型\r\n      tableSelection: [],\r\n      selectIds: [],\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Number\", // 字段ID\r\n            label: \"车牌号码\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"Date\",\r\n            label: \"通行时间\",\r\n            type: \"datePicker\",\r\n            otherOptions: {\r\n              type: \"datetimerange\",\r\n              rangeSeparator: \"至\",\r\n              startPlaceholder: \"开始日期\",\r\n              endPlaceholder: \"结束日期\",\r\n              clearable: true,\r\n              valueFormat: \"yyyy-MM-dd HH:mm\",\r\n            },\r\n            change: (e) => {\r\n              // this.ruleForm.StartTime = e[0];\r\n              // this.ruleForm.EndTime = e[1];\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.StartTime = e[0];\r\n                this.ruleForm.EndTime = e[1];\r\n              } else {\r\n                this.ruleForm.StartTime = null;\r\n                this.ruleForm.EndTime = null;\r\n              }\r\n            },\r\n          },\r\n          {\r\n            key: \"AccessType\",\r\n            label: \"访问类型\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"UserName\",\r\n            label: \"车主姓名\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"PassType\",\r\n            label: \"过车方向\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"PassMode\",\r\n            label: \"通行方式\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n        ],\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData(\r\n                  this.ruleForm,\r\n                  \"车辆通行记录\",\r\n                  VBPassRecordExportData\r\n                );\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: \"100%\",\r\n        tableColumns: [\r\n          // {\r\n          //   width: 50,\r\n          //   otherOptions: {\r\n          //     type: \"selection\",\r\n          //     align: \"center\",\r\n          //   },\r\n          // },\r\n          {\r\n            label: \"通行时间\",\r\n            key: \"PassTime\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"车牌号码\",\r\n            key: \"Number\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"车辆类型\",\r\n            key: \"VehicleType\",\r\n          },\r\n          {\r\n            label: \"车主姓名\",\r\n            key: \"UserName\",\r\n          },\r\n          {\r\n            label: \"车主联系方式\",\r\n            key: \"UserPhone\",\r\n          },\r\n          {\r\n            label: \"访问类型\",\r\n            key: \"AccessType\",\r\n          },\r\n          {\r\n            label: \"过车方向\",\r\n            key: \"PassType\",\r\n          },\r\n          {\r\n            label: \"出入口\",\r\n            key: \"EntranceName\",\r\n          },\r\n          {\r\n            label: \"通行方式\",\r\n            key: \"PassMode\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.$router.push({\r\n                name: \"trafficRecordsView\",\r\n                query: { pg_redirect: this.$route.name, Id: row.Id },\r\n              });\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/view\",\r\n          hidden: true,\r\n          component: () => import(\"./dialog/view.vue\"),\r\n          meta: { title: \"内部车辆管理详情\" },\r\n          name: \"trafficRecordsView\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.vBPassRecordGetDropList();\r\n  },\r\n  async mounted() {\r\n    // 跳转设置默认参数\r\n    let JumpParams = this.$qiankun.getMicroAppJumpParamsFn();\r\n    if (JumpParams.isJump == \"true\") {\r\n      let StartTime = dayjs(Number(JumpParams.StartTime)).format(\r\n        \"YYYY-MM-DD HH:mm\"\r\n      );\r\n      let EndTime = dayjs(Number(JumpParams.EndTime)).format(\r\n        \"YYYY-MM-DD HH:mm\"\r\n      );\r\n      this.ruleForm.PassType = JumpParams.PassType\r\n      this.ruleForm.Date = [StartTime, EndTime];\r\n      this.ruleForm.StartTime = StartTime;\r\n      this.ruleForm.EndTime = EndTime;\r\n    }\r\n    this.onFresh()\r\n  },\r\n  beforeDestroy() {\r\n    this.$qiankun.setMicroAppJumpParamsFn();\r\n    this.ruleForm.StartTime = null;\r\n    this.ruleForm.EndTime = null;\r\n  },\r\n  methods: {\r\n    async vBPassRecordGetDropList() {\r\n      let res = await VBPassRecordGetDropList({});\r\n      if (res.IsSucceed) {\r\n        this.customForm.formItems.find((v) => v.key == \"AccessType\").options =\r\n          res.Data.find((v) => v.Name == \"AccessType\").List.map((item) => ({\r\n            label: item.Key,\r\n            value: item.Value,\r\n          }));\r\n\r\n        this.customForm.formItems.find((v) => v.key == \"PassType\").options =\r\n          res.Data.find((v) => v.Name == \"PassType\").List.map((item) => ({\r\n            label: item.Key,\r\n            value: item.Value,\r\n          }));\r\n\r\n        this.customForm.formItems.find((v) => v.key == \"PassMode\").options =\r\n          res.Data.find((v) => v.Name == \"PassMode\").List.map((item) => ({\r\n            label: item.Key,\r\n            value: item.Value,\r\n          }));\r\n      }\r\n    },\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n    },\r\n    async init() {\r\n      await this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      if (this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null;\r\n        this.ruleForm.EndTime = null;\r\n      }\r\n      const res = await VBPassRecordGetPassRecordList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.Total;\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.onFresh();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.onFresh();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      const Ids = [];\r\n      this.tableSelection = selection;\r\n      this.tableSelection.forEach((item) => {\r\n        Ids.push(item.Id);\r\n      });\r\n      console.log(Ids);\r\n      this.selectIds = Ids;\r\n      console.log(this.tableSelection);\r\n    },\r\n    handleview(row) {\r\n      this.dialogVisible = true;\r\n      this.PassImg = row.PassImg;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n\r\n.imgwapper {\r\n  width: 100px;\r\n  height: 100px;\r\n}\r\n.empty-img {\r\n  text-align: center;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,SACAC,uBAAA,EACAC,6BAAA,EACAC,+BAAA,EACAC,sBAAA,QACA;AACA,OAAAC,UAAA;AACA,OAAAC,aAAA;AACA,OAAAC,KAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAV,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAY,MAAA,GAAAL,UAAA,EAAAC,aAAA;EACAK,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,QAAA;QACAC,MAAA;QACAC,SAAA;QACAC,OAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,IAAA;MACA;MACAC,aAAA;MACAC,OAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,cAAA;MACAC,SAAA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAAA;UACAC,YAAA;YACA;YACAC,SAAA;UACA;UACAC,KAAA,WAAAA,MAAAC,CAAA;UACAC,MAAA,WAAAA,OAAA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAD,IAAA;YACAM,cAAA;YACAC,gBAAA;YACAC,cAAA;YACAN,SAAA;YACAO,WAAA;UACA;UACAJ,MAAA,WAAAA,OAAAD,CAAA;YACA;YACA;YACA,IAAAA,CAAA,IAAAA,CAAA,CAAAM,MAAA;cACA7B,KAAA,CAAAC,QAAA,CAAAE,SAAA,GAAAoB,CAAA;cACAvB,KAAA,CAAAC,QAAA,CAAAG,OAAA,GAAAmB,CAAA;YACA;cACAvB,KAAA,CAAAC,QAAA,CAAAE,SAAA;cACAH,KAAA,CAAAC,QAAA,CAAAG,OAAA;YACA;UACA;QACA,GACA;UACAa,GAAA;UACAC,KAAA;UACAC,IAAA;UACAW,OAAA;UACAV,YAAA;YACAC,SAAA;UACA;UACAG,MAAA,WAAAA,OAAAD,CAAA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,KAAA,WAAAA,MAAAC,CAAA;UACAC,MAAA,WAAAA,OAAA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAW,OAAA;UACAV,YAAA;YACAC,SAAA;UACA;UACAG,MAAA,WAAAA,OAAAD,CAAA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAW,OAAA;UACAV,YAAA;YACAC,SAAA;UACA;UACAG,MAAA,WAAAA,OAAAD,CAAA;QACA,EACA;QACAQ,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAnB,GAAA;YACAoB,QAAA;YAAA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAAC,IAAA;cACAC,OAAA,CAAAC,GAAA,CAAAF,IAAA;cACAxC,KAAA,CAAA2C,UAAA,CACA3C,KAAA,CAAAC,QAAA,EACA,UACAT,sBACA;YACA;UACA;QAEA;QACA;QACAoD,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;QACAC,YAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA/B,KAAA;UACAD,GAAA;UACAG,YAAA;YACA8B,KAAA;UACA;QACA,GACA;UACAhC,KAAA;UACAD,GAAA;UACAG,YAAA;YACA8B,KAAA;UACA;QACA,GACA;UACAhC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,EACA;QACAkC,SAAA;QACAC,YAAA,GACA;UACAC,WAAA;UACAjC,YAAA;YACAD,IAAA;UACA;UACAoB,OAAA,WAAAA,QAAAe,KAAA,EAAAC,GAAA;YACAvD,KAAA,CAAAwD,OAAA,CAAAC,IAAA;cACAC,IAAA;cACAC,KAAA;gBAAAC,WAAA,EAAA5D,KAAA,CAAA6D,MAAA,CAAAH,IAAA;gBAAAI,EAAA,EAAAP,GAAA,CAAAO;cAAA;YACA;UACA;QACA;MAEA;MACAC,YAAA,GACA;QACAC,IAAA,OAAAH,MAAA,CAAAG,IAAA;QACAC,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAC,IAAA;UAAAC,KAAA;QAAA;QACAf,IAAA;MACA;IAEA;EACA;EACAgB,OAAA,WAAAA,QAAA;IACA,KAAAC,uBAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,UAAA,EAAA/E,SAAA,EAAAC,OAAA;MAAA,OAAA2E,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACA;YACAL,UAAA,GAAAL,MAAA,CAAAW,QAAA,CAAAC,uBAAA;YACA,IAAAP,UAAA,CAAAQ,MAAA;cACAvF,SAAA,GAAAR,KAAA,CAAAO,MAAA,CAAAgF,UAAA,CAAA/E,SAAA,GAAAwF,MAAA,CACA,kBACA;cACAvF,OAAA,GAAAT,KAAA,CAAAO,MAAA,CAAAgF,UAAA,CAAA9E,OAAA,GAAAuF,MAAA,CACA,kBACA;cACAd,MAAA,CAAA5E,QAAA,CAAAM,QAAA,GAAA2E,UAAA,CAAA3E,QAAA;cACAsE,MAAA,CAAA5E,QAAA,CAAAQ,IAAA,IAAAN,SAAA,EAAAC,OAAA;cACAyE,MAAA,CAAA5E,QAAA,CAAAE,SAAA,GAAAA,SAAA;cACA0E,MAAA,CAAA5E,QAAA,CAAAG,OAAA,GAAAA,OAAA;YACA;YACAyE,MAAA,CAAAe,OAAA;UAAA;UAAA;YAAA,OAAAP,QAAA,CAAAQ,IAAA;QAAA;MAAA,GAAAZ,OAAA;IAAA;EACA;EACAa,aAAA,WAAAA,cAAA;IACA,KAAAN,QAAA,CAAAO,uBAAA;IACA,KAAA9F,QAAA,CAAAE,SAAA;IACA,KAAAF,QAAA,CAAAG,OAAA;EACA;EACA4F,OAAA;IACArB,uBAAA,WAAAA,wBAAA;MAAA,IAAAsB,MAAA;MAAA,OAAAnB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAkB,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAApB,mBAAA,GAAAI,IAAA,UAAAiB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAf,IAAA,GAAAe,SAAA,CAAAd,IAAA;YAAA;cAAAc,SAAA,CAAAd,IAAA;cAAA,OACAlG,uBAAA;YAAA;cAAA8G,GAAA,GAAAE,SAAA,CAAAC,IAAA;cACA,IAAAH,GAAA,CAAAI,SAAA;gBACAN,MAAA,CAAAlF,UAAA,CAAAC,SAAA,CAAAwF,IAAA,WAAAC,CAAA;kBAAA,OAAAA,CAAA,CAAAxF,GAAA;gBAAA,GAAAa,OAAA,GACAqE,GAAA,CAAAO,IAAA,CAAAF,IAAA,WAAAC,CAAA;kBAAA,OAAAA,CAAA,CAAA7G,IAAA;gBAAA,GAAA+G,IAAA,CAAAC,GAAA,WAAApE,IAAA;kBAAA;oBACAtB,KAAA,EAAAsB,IAAA,CAAAqE,GAAA;oBACAC,KAAA,EAAAtE,IAAA,CAAAuE;kBACA;gBAAA;gBAEAd,MAAA,CAAAlF,UAAA,CAAAC,SAAA,CAAAwF,IAAA,WAAAC,CAAA;kBAAA,OAAAA,CAAA,CAAAxF,GAAA;gBAAA,GAAAa,OAAA,GACAqE,GAAA,CAAAO,IAAA,CAAAF,IAAA,WAAAC,CAAA;kBAAA,OAAAA,CAAA,CAAA7G,IAAA;gBAAA,GAAA+G,IAAA,CAAAC,GAAA,WAAApE,IAAA;kBAAA;oBACAtB,KAAA,EAAAsB,IAAA,CAAAqE,GAAA;oBACAC,KAAA,EAAAtE,IAAA,CAAAuE;kBACA;gBAAA;gBAEAd,MAAA,CAAAlF,UAAA,CAAAC,SAAA,CAAAwF,IAAA,WAAAC,CAAA;kBAAA,OAAAA,CAAA,CAAAxF,GAAA;gBAAA,GAAAa,OAAA,GACAqE,GAAA,CAAAO,IAAA,CAAAF,IAAA,WAAAC,CAAA;kBAAA,OAAAA,CAAA,CAAA7G,IAAA;gBAAA,GAAA+G,IAAA,CAAAC,GAAA,WAAApE,IAAA;kBAAA;oBACAtB,KAAA,EAAAsB,IAAA,CAAAqE,GAAA;oBACAC,KAAA,EAAAtE,IAAA,CAAAuE;kBACA;gBAAA;cACA;YAAA;YAAA;cAAA,OAAAV,SAAA,CAAAR,IAAA;UAAA;QAAA,GAAAK,QAAA;MAAA;IACA;IACAc,UAAA,WAAAA,WAAAjH,IAAA;MACA,KAAAmC,iBAAA,CAAAW,WAAA;MACAJ,OAAA,CAAAC,GAAA,CAAA3C,IAAA;MACA,KAAA6F,OAAA;IACA;IACAqB,SAAA,WAAAA,UAAA;MACA,KAAArB,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAAsB,SAAA;IACA;IACAC,IAAA,WAAAA,KAAA;MAAA,IAAAC,MAAA;MAAA,OAAAtC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAqC,SAAA;QAAA,OAAAtC,mBAAA,GAAAI,IAAA,UAAAmC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjC,IAAA,GAAAiC,SAAA,CAAAhC,IAAA;YAAA;cAAAgC,SAAA,CAAAhC,IAAA;cAAA,OACA6B,MAAA,CAAAF,SAAA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAA1B,IAAA;UAAA;QAAA,GAAAwB,QAAA;MAAA;IACA;IACAH,SAAA,WAAAA,UAAA;MAAA,IAAAM,MAAA;MAAA,OAAA1C,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAyC,SAAA;QAAA,IAAAtB,GAAA;QAAA,OAAApB,mBAAA,GAAAI,IAAA,UAAAuC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArC,IAAA,GAAAqC,SAAA,CAAApC,IAAA;YAAA;cACA,IAAAiC,MAAA,CAAAvH,QAAA,CAAAQ,IAAA,CAAAoB,MAAA;gBACA2F,MAAA,CAAAvH,QAAA,CAAAE,SAAA;gBACAqH,MAAA,CAAAvH,QAAA,CAAAG,OAAA;cACA;cAAAuH,SAAA,CAAApC,IAAA;cAAA,OACAjG,6BAAA,CAAAsI,aAAA;gBACAC,IAAA,EAAAL,MAAA,CAAAtF,iBAAA,CAAAW,WAAA;gBACAiF,QAAA,EAAAN,MAAA,CAAAtF,iBAAA,CAAAY;cAAA,GACA0E,MAAA,CAAAvH,QAAA,CACA;YAAA;cAJAkG,GAAA,GAAAwB,SAAA,CAAArB,IAAA;cAKA,IAAAH,GAAA,CAAAI,SAAA;gBACAiB,MAAA,CAAAtF,iBAAA,CAAAiB,SAAA,GAAAgD,GAAA,CAAAO,IAAA,CAAAA,IAAA;gBACAc,MAAA,CAAAtF,iBAAA,CAAAa,KAAA,GAAAoD,GAAA,CAAAO,IAAA,CAAAqB,KAAA;cACA;YAAA;YAAA;cAAA,OAAAJ,SAAA,CAAA9B,IAAA;UAAA;QAAA,GAAA4B,QAAA;MAAA;IACA;IACAO,gBAAA,WAAAA,iBAAAC,GAAA;MACAxF,OAAA,CAAAC,GAAA,iBAAAwF,MAAA,CAAAD,GAAA;MACA,KAAA/F,iBAAA,CAAAY,QAAA,GAAAmF,GAAA;MACA,KAAArC,OAAA;IACA;IACAuC,mBAAA,WAAAA,oBAAAF,GAAA;MACAxF,OAAA,CAAAC,GAAA,wBAAAwF,MAAA,CAAAD,GAAA;MACA,KAAA/F,iBAAA,CAAAW,WAAA,GAAAoF,GAAA;MACA,KAAArC,OAAA;IACA;IACAwC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,IAAAC,GAAA;MACA,KAAAzH,cAAA,GAAAwH,SAAA;MACA,KAAAxH,cAAA,CAAA0H,OAAA,WAAA/F,IAAA;QACA8F,GAAA,CAAA7E,IAAA,CAAAjB,IAAA,CAAAsB,EAAA;MACA;MACArB,OAAA,CAAAC,GAAA,CAAA4F,GAAA;MACA,KAAAxH,SAAA,GAAAwH,GAAA;MACA7F,OAAA,CAAAC,GAAA,MAAA7B,cAAA;IACA;IACA2H,UAAA,WAAAA,WAAAjF,GAAA;MACA,KAAA7C,aAAA;MACA,KAAAC,OAAA,GAAA4C,GAAA,CAAA5C,OAAA;IACA;EACA;AACA", "ignoreList": []}]}