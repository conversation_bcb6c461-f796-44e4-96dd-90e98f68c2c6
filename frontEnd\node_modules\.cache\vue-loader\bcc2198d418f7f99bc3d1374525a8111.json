{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlEquipmentManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlEquipmentManagement\\index.vue", "mtime": 1755674552408}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk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file": "index.vue", "sourceRoot": "src/views/business/accessControl/accessControlEquipmentManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport DialogForm from './dialogForm.vue'\r\nimport DeviceConnect from './deviceConnect.vue'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport getGridByCode from '../../safetyManagement/mixins/index'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport {\r\n  GetEquipmentlList,\r\n  DelEquipment,\r\n  ExportEntrancePersonnel,\r\n  SyncEquipment\r\n} from '@/api/business/hazardousChemicals'\r\nimport { GetParkArea } from '@/api/business/energyManagement.js'\r\n\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  mixins: [getGridByCode, addRouterPage],\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        Entrance_Equipment_Name: '',\r\n        Entrance_Equipment_Type: '',\r\n        Position: '',\r\n        Platform_Name: '',\r\n        Status: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Entrance_Equipment_Name',\r\n            label: '设备名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Entrance_Equipment_Type',\r\n            label: '设备类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position',\r\n            label: '安装位置',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Platform_Name',\r\n            label: '平台名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          /*  {\r\n             key: 'Position',\r\n             label: '应用场景',\r\n             type: 'input',\r\n             otherOptions: {\r\n               clearable: true\r\n             },\r\n             change: (e) => {\r\n               console.log(e)\r\n             }\r\n           },\r\n           {\r\n             key: 'Position',\r\n             label: '所属区域',\r\n             type: 'input',\r\n             otherOptions: {\r\n               clearable: true\r\n             },\r\n             change: (e) => {\r\n               console.log(e)\r\n             }\r\n           }, */\r\n          {\r\n            key: 'Status',\r\n            label: '状态',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [\r\n              { label: '在线', value: '在线' },\r\n              { label: '离线', value: '离线' }\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              key: 'synchronous',\r\n              text: '设备同步',\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: false, // 是否禁用\r\n              icon: '', //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handelSyncEquipment()\r\n              }\r\n            },\r\n            // {\r\n            //   text: '新增',\r\n            //   round: false, // 是否圆角\r\n            //   plain: false, // 是否朴素\r\n            //   circle: false, // 是否圆形\r\n            //   loading: false, // 是否加载中\r\n            //   disabled: true, // 是否禁用\r\n            //   icon: '', //  图标\r\n            //   autofocus: false, // 是否聚焦\r\n            //   type: 'primary', // primary / success / warning / danger / info / text\r\n            //   size: 'small', // medium / small / mini\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleCreate()\r\n            //   }\r\n            // },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        loading: false,\r\n        operateOptions: {\r\n          width: '240px',\r\n          align: 'center'\r\n        },\r\n        tableActionsWidth: 180,\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text',\r\n              disabled: false // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text',\r\n              disabled: false // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row)\r\n            }\r\n          }\r\n          // {\r\n          //   actionLabel: '设备连接',\r\n          //   otherOptions: {\r\n          //     type: 'text',\r\n          //     disabled: true // 是否禁用\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleConent(row)\r\n          //   }\r\n          // }\r\n          /* {\r\n            actionLabel: '通行授权',\r\n            otherOptions: {\r\n              type: 'text',\r\n              disabled: true, // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleConfig(row)\r\n            }\r\n          }, */\r\n        ]\r\n      },\r\n      Park_Area: '',\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/add',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'ConfigureAuthorizationList',\r\n          meta: { title: `配置授权名单` }\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  async created() {\r\n    this.init()\r\n    this.customForm.formItems[1].options = await this.getDictionaryDetailListByCode()\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getEquipmentList()\r\n    },\r\n    init() {\r\n      this.getGridByCodePic('AccessControlEquipmentManagement', 'Allow_Pass_Visitors', false)\r\n      GetParkArea().then(res => {\r\n        this.Park_Area = res.Data\r\n      })\r\n      this.getEquipmentList()\r\n    },\r\n    async getEquipmentList() {\r\n      this.customTableConfig.loading = true\r\n      const res = await GetEquipmentlList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      }).finally(() => {\r\n        this.customTableConfig.loading = false\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.dialogVisible = true\r\n      this.currentComponent = DialogForm\r\n      this.componentsConfig.Data = {\r\n        Entrance_Equipment_Number: '',\r\n        Entrance_Equipment_Name: '',\r\n        Entrance_Equipment_Type: '',\r\n        Allow_Pass_Visitors: false,\r\n        Recognition_Way: [],\r\n        Park_area: [],\r\n        Address: '',\r\n        Position: '',\r\n        Platform_Name: '',\r\n        Platform_Contact_Way: '',\r\n        Engineer: '',\r\n        Engineer_Contact_Way: '',\r\n        Equipment_Purpose_Catetory: '',\r\n        Park_Area: this.Park_Area\r\n      }\r\n    },\r\n    // 同步设备\r\n    handelSyncEquipment() {\r\n      this.$confirm('此操作将进行设备同步, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'synchronous').loading = true\r\n        SyncEquipment({}).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '同步成功!'\r\n            })\r\n            this.getEquipmentList()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        }).finally(() => {\r\n          this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'synchronous').loading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消同步'\r\n        })\r\n      })\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row)\r\n      console.log(this)\r\n      this.$confirm('确认删除？', {\r\n        type: 'warning'\r\n      })\r\n        .then(async (_) => {\r\n          const res = await DelEquipment({\r\n            id: row.Id\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.$message.success('操作成功')\r\n            this.getEquipmentList()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n        .catch((_) => { })\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      this.currentComponent = DialogForm\r\n      let isShowBtn = true\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        isShowBtn = false\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        isShowBtn = true\r\n      }\r\n      this.dialogVisible = true\r\n      const Park_area = (row.Scene ?? '') == '' ? [row.Purpose_Catetory] : (row.Site ?? '') == '' ? [row.Purpose_Catetory, row.Scene] : [row.Purpose_Catetory, row.Scene, row.Site]\r\n      // row.Allow_Pass_Visitors = row.Allow_Pass_Visitors.toString() == 'false' ? '不允许' : '允许'\r\n      row.Pass_Visitors = row.Allow_Pass_Visitors.toString() == 'false' ? '不允许' : '允许'\r\n      this.componentsConfig.Data = { ...row, Recognition_Way: (row.Recognition_Way ?? '').split(','), Park_Area: this.Park_Area, Park_area, isShowBtn }\r\n    },\r\n    async handleExport() {\r\n      if (this.tableSelection.length == 0) {\r\n        this.$message.warning('请选择数据在导出')\r\n        return\r\n      }\r\n      const res = await ExportEntrancePersonnel({\r\n        id: this.tableSelection.map((item) => item.Id).toString(),\r\n        code: 'AccessControlEquipmentManagement'\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '门禁设备管理数据')\r\n      } else {\r\n        this.$message(res.Message)\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.getEquipmentList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.getEquipmentList()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    },\r\n    handleConent(row) {\r\n      this.dialogVisible = true\r\n      this.dialogTitle = '确认设备连接'\r\n      this.currentComponent = DeviceConnect\r\n      this.componentsConfig.Data = { ...row }\r\n    },\r\n    handleConfig(row) {\r\n      this.$router.push({ name: 'ConfigureAuthorizationList', query: { pg_redirect: this.$route.name, id: row.Id } })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}