<template>
  <div class="pJEnergyAnalysisBox">
    <div class="customTabs">
      <div
        v-for="(item, index) in energyTypeList"
        :key="index"
        :class="[energyType === item.name ? 'activeTab' : '']"
        @click="handelEnergyTab(item)"
      >
        {{ item.name }}
      </div>
    </div>
    {{ yearMonthValue }}
    <div class="searchBox">
      <div style="display: flex">
        <el-radio-group
          v-model="yearMonthRadio"
          class="radio"
          @change="yearMonthRadioChange"
        >
          <el-radio-button :label="1">年</el-radio-button>
          <el-radio-button :label="2">月</el-radio-button>
          <el-radio-button :label="4">日</el-radio-button>
        </el-radio-group>
        <div class="divider" />
        <el-date-picker
          v-if="yearMonthRadio == 1"
          v-model="yearMonthValue"
          class="picker"
          :clearable="false"
          value-format="yyyy"
          type="year"
          @change="pickChange"
        />
        <el-date-picker
          v-else-if="yearMonthRadio == 2"
          v-model="yearMonthValue"
          class="picker"
          :clearable="false"
          value-format="yyyy-MM"
          type="month"
          @change="pickChange"
        />
        <el-date-picker
          v-else
          v-model="yearMonthValue"
          class="picker"
          :clearable="false"
          value-format="yyyy-MM-dd"
          type="date"
          @change="pickChange"
        />
        <el-button @click="reset">重置</el-button>
      </div>
    </div>
    <div class="wapper2">
      <component
        :is="currentComponent"
        ref="content"
        :components-config="{
          DateType: yearMonthRadio,
          StartTime: yearMonthValue,
          randomInteger: isFlag,
        }"
      />
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import electricity from './eleNew/index'
import gas from './gas/index'
import water from './water/index'

export default {
  components: {
    electricity,
    gas,
    water
  },
  data() {
    return {
      energyTypeList: [
        {
          name: '电'
        },
        {
          name: '水'
        },
        {
          name: '气'
        }
      ],
      energyType: '电',
      yearMonthRadio: 2,
      yearMonthValue: dayjs().subtract(0, 'month').format('YYYY-MM'),
      isFlag: false,
      currentComponent: electricity
    }
  },
  created() {},
  mounted() {},
  provide() {
    return {
      DateType: () => this.yearMonthRadio,
      StartTime: () => this.yearMonthValue,
      EndTime: () => this.yearMonthValue,
      randomInteger: () => this.isFlag
    }
  },
  methods: {
    handelEnergyTab(item) {
      this.energyType = item.name
      this.isFlag = !this.isFlag
      if (this.energyType === '水') {
        this.currentComponent = 'water'
      } else if (this.energyType === '电') {
        this.currentComponent = 'electricity'
      } else if (this.energyType === '气') {
        this.currentComponent = 'gas'
      }
    },
    yearMonthRadioChange(val) {
      if (val === 1) {
        this.yearMonthValue = dayjs().format('YYYY')
      } else if (val === 2) {
        this.yearMonthValue = dayjs().subtract(0, 'month').format('YYYY-MM')
      } else {
        this.yearMonthValue = dayjs().subtract(0, 'month').format('YYYY-MM-DD')
      }
      this.isFlag = !this.isFlag
    },
    reset() {
      this.isFlag = !this.isFlag
      this.yearMonthRadio = 2
      this.yearMonthValue = dayjs().subtract(0, 'month').format('YYYY-MM')
    },
    pickChange() {
      this.isFlag = !this.isFlag
    },
    handleClick() {
      this.$refs.configDialog.handleOpen()
    },
    refreshData() {
      this.isFlag = !this.isFlag
    }
  }
}
</script>
<style scoped lang='scss'>
.pJEnergyAnalysisBox {
  padding: 20px;
  .customTabs {
    height: 64px;
    background: #fff;
    border-radius: 4px;
    display: flex;
    margin-bottom: 16px;
    font-size: 18px;
    color: #999;
    > div {
      width: 140px;
      height: 64px;
      line-height: 64px;
      text-align: center;
      cursor: pointer;
    }
    .activeTab {
      font-weight: 600;
      color: #298dff;
      border-bottom: 2px solid #298dff;
    }
  }
  .searchBox {
    margin-bottom: 16px;
    background-color: #fff;
    border-radius: 4px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    .radio {
      margin-right: 10px;
    }
    .picker {
      margin-right: 10px;
    }
    // ::v-deep .el-radio-button__inner {
    //   background-color: #ffffff;
    //   height: 32px;
    //   width: 80px;
    //   font-size: 14px;
    // }
  }
  .divider {
    width: 1px;
    height: 32px;
    margin: 0 32px;
    background: #eee;
  }
}
</style>
