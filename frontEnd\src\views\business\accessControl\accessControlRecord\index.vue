<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'

import dialogFormLook from './dialogFormLook.vue'

import { downloadFile } from '@/utils/downloadFile'
// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'
// import CustomButton from '@/businessComponents/CustomButton/index.vue'
// import * as moment from 'moment'
import dayjs from 'dayjs'

import {
  GetRole,
  GetTrafficRecordList,
  ExportEntranceTrafficRecord,
  GetDictionaryDetailListByCode
} from '@/api/business/accessControl'

export default {
  name: '',
  components: {
    CustomTable,
    // CustomButton,
    // CustomTitle,
    CustomForm,
    CustomLayout
  },
  data() {
    return {
      currentComponent: dialogFormLook,
      componentsConfig: {},
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      tableSelection: [],

      ruleForm: {
        P_Name: '',
        BeginTime: '',
        EndTime: '',
        EntranceType: '',
        EquipmentName: '',
        Position: '',
        Position_Name: '',
        Traffic_Way: '',
        P_Type: ''
      },
      customForm: {
        formItems: [
          {
            key: 'P_Name', // 字段ID
            label: '姓名', // Form的label
            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              clearable: true
            },
            // width: '240px',
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'BeginTime',
            label: '通行开始时间',
            type: 'datePicker',
            otherOptions: {
              placeholder: ''
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'EndTime',
            label: '通行结束时间',
            type: 'datePicker',
            otherOptions: {
              placeholder: ''
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'EntranceType', // 字段ID
            label: '门禁类型', // Form的label
            type: 'select',
            options: [],
            otherOptions: {
              placeholder: ''
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'EquipmentName', // 字段ID
            label: '门禁设备', // Form的label
            type: 'input',
            otherOptions: {
              placeholder: ''
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Position', // 字段ID
            label: '安装位置', // Form的label
            type: 'input',
            otherOptions: {
              placeholder: ''
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Position_Name', // 字段ID
            label: '岗位类型', // Form的label
            type: 'select',
            otherOptions: {
              placeholder: ''
            },
            options: [],
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Traffic_Way', // 字段ID
            label: '通行方式', // Form的label
            type: 'select',

            options: [],
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'P_Type', // 字段ID
            label: '人员类型', // Form的label
            type: 'select',

            options: [],
            change: (e) => {
              console.log(e)
            }
          }
        ],
        rules: {},
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            // {
            //   text: '新增',
            //   round: false, // 是否圆角
            //   plain: false, // 是否朴素
            //   circle: false, // 是否圆形
            //   loading: false, // 是否加载中
            //   disabled: false, // 是否禁用
            //   icon: '', //  图标
            //   autofocus: false, // 是否聚焦
            //   type: 'primary', // primary / success / warning / danger / info / text
            //   size: 'small', // medium / small / mini
            //   onclick: (item) => {
            //     console.log(item)
            //     this.handleCreate()
            //   }
            // },
            {
              text: '批量导出',
              onclick: (item) => {
                console.log(item)
                this.handleExport()
              }
            }
            // {
            //   text: '批量导出',
            //   onclick: (item) => {
            //     console.log(item)
            //     this.handleAllExport()
            //   }
            // }
          ]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            width: 50,
            otherOptions: {
              type: 'selection',
              align: 'center'
            }
          },
          // {
          //   width: 60,
          //   label: '序号',
          //   otherOptions: {
          //     type: 'index',
          //     align: 'center'
          //   } // key
          // },
          {
            label: '姓名',
            key: 'P_Name'
          },
          // {
          //   label: '图片',
          //   key: 'Name'
          // },
          {
            label: '性别',
            key: 'P_Sex'
          },
          {
            label: '联系方式',
            key: 'Contact_Way'
          },
          {
            label: '人员类型',
            key: 'P_Type'
          },
          {
            label: '岗位类型',
            key: 'Position_Name'
          },
          {
            label: '门禁类型',
            key: 'Entrance_Equipment_Type'
          },
          {
            label: '门禁名称',
            key: 'Entrance_Equipment_Name'
          },
          {
            label: '门禁位置',
            key: 'Position'
          },
          {
            label: '通行时间',
            key: 'Traffic_Time'
          },
          {
            label: '通行方式',
            key: 'Traffic_Way'
          }
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: '查看详情',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'view')
            }
          }
          // {
          //   actionLabel: '编辑',
          //   otherOptions: {
          //     type: 'text'
          //   },
          //   onclick: (index, row) => {
          //     this.handleEdit(index, row, 'edit')
          //   }
          // },
          // {
          //   actionLabel: '删除',
          //   otherOptions: {
          //     type: 'text'
          //   },
          //   onclick: (index, row) => {
          //     this.handleDelete(index, row)
          //   }
          // }
        ]
      }
    }
  },
  computed: {},
  async created() {
    // 门禁类型
    this.customForm.formItems.find(
      (item) => item.key === 'EntranceType'
    ).options = await this.initDeviceType('Entrance_Type')
    // 岗位类型
    this.customForm.formItems.find(
      (item) => item.key === 'Position_Name'
    ).options = await this.initGetRole('Entrance_Type')

    console.log(await this.initDeviceType('Position_Type'), '1221212222222')
    // 通行方式
    this.customForm.formItems.find(
      (item) => item.key === 'Traffic_Way'
    ).options = await this.initDeviceType('Traffic_Way')
    // 人员类型
    this.customForm.formItems.find((item) => item.key === 'P_Type').options =
      await this.initDeviceType('P_Type')
    this.init()
  },
  methods: {
    async initDeviceType(code) {
      const res = await GetDictionaryDetailListByCode({
        dictionaryCode: code
      })
      const options = res.Data.map((item, index) => ({
        label: item.Display_Name,
        value: item.Value
      }))
      return options
    },
    // 岗位类型
    async initGetRole(code) {
      const res = await GetRole({})
      const options = res.Data.map((item, index) => ({
        label: item.Display_Name,
        value: item.Value
      }))
      return options
    },

    searchForm(data) {
      console.log(data)
      this.customTableConfig.currentPage = 1
      this.onFresh()
    },
    resetForm() {
      this.onFresh()
    },
    onFresh() {
      this.GetTrafficRecordList()
    },
    init() {
      this.GetTrafficRecordList()
    },
    async GetTrafficRecordList() {
      const res = await GetTrafficRecordList({
        ParameterJson: [
          {
            Key: 'string',
            Value: [null],
            Type: 'string',
            Filter_Type: 'string'
          }
        ],
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,

        Search: '',
        SortName: '',
        SortOrder: '',
        TotalCount: 0,
        P_Name: '',

        EntranceType: '',
        EquipmentName: '',
        Position: '',
        Position_Name: '',
        Traffic_Way: '',
        P_Type: '',
        ...this.ruleForm,
        BeginTime: this.ruleForm.EndTime
          ? dayjs(this.ruleForm.BeginTime).format('YYYY-MM-DD')
          : '',
        EndTime: this.ruleForm.EndTime
          ? dayjs(this.ruleForm.EndTime).format('YYYY-MM-DD')
          : ''
      })
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data.map((item) => ({
          ...item,
          Traffic_Time: dayjs(item.Traffic_Time).format('YYYY-MM-DD HH:mm:ss')
        }))
        console.log(res)
        this.customTableConfig.total = res.Data.TotalCount
      } else {
        this.$message.error(res.Message)
      }
    },
    handleCreate() {
      this.dialogTitle = '新增'
      this.dialogVisible = true
    },
    // handleDelete(index, row) {
    //   console.log(index, row)
    //   console.log(this)
    //   this.$confirm('确认删除？', {
    //     type: 'warning'
    //   })
    //     .then(async(_) => {
    //       const res = await DeleteEquipment({
    //         IDs: [row.ID]
    //       })
    //       if (res.IsSucceed) {
    //         this.init()
    //       }
    //     })
    //     .catch((_) => {})
    // },
    handleEdit(index, row, type) {
      console.log(index, row, type)
      if (type === 'view') {
        this.dialogTitle = '查看'
        this.componentsConfig = {
          ID: row.Id,
          title: '查看',
          row: row
        }
      }
      this.dialogVisible = true
    },
    async handleExport() {
      if (this.tableSelection.length == 0) {
        this.$message.warning('请选择数据在导出')
        return
      }
      const res = await ExportEntranceTrafficRecord({
        id: this.tableSelection.map((item) => item.Id).join(','),
        code: 'accessControlRecord'
        // ...this.ruleForm
      })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '21')
      } else {
        this.$message.error(res.Message)
      }
    },
    // async handleAllExport() {
    //   const res = await ExportEntranceTrafficRecord({
    //     Content: '',
    //     EqtType: '',
    //     Position: '',
    //     IsAll: true,
    //     Ids: [],
    //     ...this.ruleForm
    //   })
    //   if (res.IsSucceed) {
    //     console.log(res)
    //     downloadFile(res.Data, '21')
    //   }
    // },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.init()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.init()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
    }
  }
}
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
.layout{
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
