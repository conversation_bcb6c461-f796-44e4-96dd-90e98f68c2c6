<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        >
          <template #customBtn="{slotScope}"><el-button v-if="slotScope.Status == 1" type="text" @click="handelClose(slotScope)">关闭</el-button></template>
        </CustomTable>
      </template>
    </CustomLayout>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import { GetModule, GetTypesByModule, GetWarnPageList, GetWarningStatus, GetWarningModule, GetWarningType, CloseWarning } from '@/api/business/myWorkBench'
export default {
  name: 'MyTasks',
  components: {
    CustomTable,
    CustomForm,
    CustomLayout
  },
  data() {
    return {
      ruleForm: {
        Name: '',
        OperateType: '',
        StartTime: null,
        EndTime: null,
        Module: '',
        Type: '',
        Date: [],
        Status: null
      },
      customForm: {
        formItems: [
          {
            key: 'Name',
            label: '告警名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            width: '240px',
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Module',
            label: '业务模块',
            type: 'select',
            otherOptions: {
              clearable: true
            },
            options: [],
            change: (e) => {
              this.getTypesByModule(e)
            }
          },
          {
            key: 'Type',
            label: '告警类型',
            type: 'select',
            otherOptions: {
              disabled: true,
              clearable: true
            },
            options: [
              {
                label: '全部',
                value: ''
              }
            ],
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Status',
            label: '状态',
            type: 'select',
            otherOptions: {
              clearable: true
            },
            options: [],
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Date',
            label: '告警时间',
            type: 'datePicker',
            otherOptions: {
              type: 'datetimerange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              clearable: true,
              valueFormat: 'yyyy-MM-dd HH:mm'
            },
            change: (e) => {
              console.log(e)
              if (e && e.length !== 0) {
                this.ruleForm.StartTime = e[0]
                this.ruleForm.EndTime = e[1]
              } else {
                this.ruleForm.StartTime = null
                this.ruleForm.EndTime = null
              }
            }
          }
        ],
        rules: {},
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: []
        },
        // 表格
        loading: false,
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        height: '100%',
        tableColumns: [
          {
            label: '告警时间',
            key: 'AlarmTime'
          },
          {
            label: '状态',
            key: 'Status',
            render: (row) => {
              const h = this.$createElement
              return h('div', {}, [
                h(
                  'span',
                  { class: row.Status == 1 ? 'warning' : row.Status == 2 ? 'close' : row.Status == 3 ? 'handel' : '' },
                  ''
                ),
                h('span', {}, `${row.Status == 1 ? '告警中' : row.Status == 2 ? '已关闭' : row.Status == 3 ? '已处理' : ''}`)
              ])
            }
          },
          {
            label: '告警类型',
            key: 'Type'
          },
          {
            label: '告警名称',
            key: 'Name'
          },
          {
            label: '来源',
            key: 'Source'
          },
          {
            label: '业务模块',
            key: 'Module'
          },
          {
            label: '处理内容',
            key: 'Content'
          }
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: '查看详情',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.getDetail(row)
            }
          }
        ]
      }
    }
  },
  mounted() {
    this.getModule()
    this.getWarningStatus()
    this.onFresh()
  },
  methods: {
    // 获取业务模块下拉
    getModule() {
      GetModule({}).then((res) => {
        if (res.IsSucceed) {
          const data = res.Data || []
          const arr = []
          data.forEach((item) => {
            const obj = {
              label: item,
              value: item
            }
            arr.push(obj)
          })
          arr.unshift({
            label: '全部',
            value: ''
          })
          console.log(arr)
          this.customForm.formItems.find(
            (v) => v.key == 'Module'
          ).options = arr
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    // 获取告警类型
    getTypesByModule(value) {
      this.ruleForm.Type = ''
      if (value) {
        GetTypesByModule({ Type: 2, Module: value }).then((res) => {
          if (res.IsSucceed) {
            const data = res.Data || []
            const arr = []
            data.forEach((item) => {
              const obj = {
                label: item,
                value: item
              }
              arr.push(obj)
            })
            arr.unshift({
              label: '全部',
              value: ''
            })
            console.log(arr)
            this.customForm.formItems.find(
              (v) => v.key == 'Type'
            ).otherOptions.disabled = false
            this.customForm.formItems.find(
              (v) => v.key == 'Type'
            ).options = arr
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        })
      } else {
        this.customForm.formItems.find(
          (v) => v.key == 'Type'
        ).otherOptions.disabled = true
        this.customForm.formItems.find((v) => v.key == 'Type').options =
          []
      }
    },
    // 获取任务状态
    getWarningStatus() {
      GetWarningStatus({}).then((res) => {
        if (res.IsSucceed) {
          const data = res.Data || []
          const arr = []
          data.forEach((item) => {
            const obj = {
              label: item.Name,
              value: item.Value
            }
            arr.push(obj)
          })
          arr.unshift({
            label: '全部',
            value: null
          })
          console.log(arr)
          this.customForm.formItems.find(
            (v) => v.key == 'Status'
          ).options = arr
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    searchForm(data) {
      console.log(data)
      this.onFresh()
    },
    resetForm() {
      this.customForm.formItems.find(
        (v) => v.key == 'Type'
      ).otherOptions.disabled = true
      this.customForm.formItems.find(
        (v) => v.key == 'Type'
      ).options = [{
        label: '全部',
        value: ''
      }]
      this.onFresh()
    },
    onFresh() {
      this.fetchData()
    },
    async fetchData() {
      this.customTableConfig.loading = true
      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {
        this.ruleForm.StartTime = null
        this.ruleForm.EndTime = null
      }
      await GetWarnPageList({
        ...this.ruleForm,
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize
      }).then((res) => {
        if (res.IsSucceed) {
          this.customTableConfig.tableData = res.Data.Data
          this.customTableConfig.total = res.Data.TotalCount
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      }).finally(() => {
        this.customTableConfig.loading = false
      })
    },

    // 关闭
    handelClose(row) {
      this.$confirm('是否关闭?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        CloseWarning({ Id: row.Id }).then((res) => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '关闭成功!'
            })
            this.onFresh()
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 查看详情
    getDetail(item) {
      console.log(item)
      if (item.Url) {
        let platform = '' // 子应用
        if (item.ModuleName == '后台设置') {
          platform = 'management'
        } else {
          platform = 'digitalfactory'
        }
        this.$qiankun.switchMicroAppFn(
          platform,
          item.ModuleCode,
          item.ModuleId,
          item.Url
        )
      } else {
        const platform = 'digitalfactory'
        const code = 'szgc'
        const id = '97b119f9-e634-4d95-87b0-df2433dc7893'
        let url = ''
        if (item.Module == '能耗管理') {
          url = '/business/energy/alarmDetail'
        } else if (item.Module == '车辆道闸') {
          url = '/bussiness/vehicle/alarm-info'
        } else if (item.Module == '门禁管理') {
          url = '/business/AccessControlAlarmDetails'
        } else if (item.Module == '安防管理') {
          url = '/business/equipmentAlarm'
        } else if (item.Module == '危化品管理') {
          url = '/business/hazchem/alarmInformation'
        } else if (item.Module == '环境管理') {
          url = '/business/environment/alarmInformation'
        } else if (item.Module == '访客管理') {
          url = '/business/energy/alarmDetail'
          console.log('访客管理')
        }
        this.$qiankun.switchMicroAppFn(platform, code, id, url)
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.onFresh()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.onFresh()
    }
  }
}
</script>

<style scoped lang="scss">
* {
  box-sizing: border-box;
}

.layout {
  height: 100%;
  width: 100%;
  position: absolute;
  ::v-deep {
    .CustomLayout {
      .layoutTable {
        height: 0;
        .CustomTable {
          height: 100%;
          display: flex;
          flex-direction: column;
          .table {
            flex: 1;
            height: 0;
            display: flex;
            flex-direction: column;
            .el-table {
              flex: 1;
              height: 0;
            }
          }
        }
      }
    }
  }
}

.warning {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 5px;
  background-color: #fb6b7f;
}
.handel {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  margin-right: 5px;
  background-color: #368dff;
}
.close {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  margin-right: 5px;
  background-color: #37be6b;
}
</style>
