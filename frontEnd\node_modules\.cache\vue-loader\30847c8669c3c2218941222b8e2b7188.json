{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\vehiclesInThePark\\index.vue?vue&type=style&index=0&id=dc824dd2&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\vehiclesInThePark\\index.vue", "mtime": 1755506574538}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQpAaW1wb3J0ICJAL3ZpZXdzL2J1c2luZXNzL3ZlaGljbGVCYXJyaWVyL2luZGV4LnNjc3MiOw0KDQouaW1nd2FwcGVyIHsNCiAgd2lkdGg6IDEwMHB4Ow0KICBoZWlnaHQ6IDEwMHB4Ow0KfQ0KLmVtcHR5LWltZyB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCi5wcm9ncmVzc0JveCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYXJvdW5kOw0KICBwYWRkaW5nOiAyMHB4IDBweDsNCiAgLnBpZUNoYXJ0RG9tIHsNCiAgICB3aWR0aDogMTI2cHg7DQogICAgaGVpZ2h0OiAxMjZweDsNCiAgfQ0KICAucHJvZ3Jlc3NUd28gew0KICAgIGhlaWdodDogMTI2cHg7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICAgIC5sZWZ0IHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgLnRpdGxlIHsNCiAgICAgICAgZm9udC1zaXplOiAyMHB4Ow0KICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgfQ0KICAgICAgLmluZm8gew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgICAubnVtIHsNCiAgICAgICAgICBjb2xvcjogI2FhYTsNCiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAgICAgICB9DQogICAgICAgIC50ZXh0IHsNCiAgICAgICAgICBjb2xvcjogI2FhYTsNCiAgICAgICAgfQ0KICAgICAgICAuY29sb3JBY3RpdmUgew0KICAgICAgICAgIGNvbG9yOiAjMjk4ZGZmOw0KICAgICAgICB9DQogICAgICAgIC50ZXh0QWN0aXZlIHsNCiAgICAgICAgICBmb250LXNpemU6IDI4cHg7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkjBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/vehicleBarrier/pJVehicleBarrier/vehiclesInThePark", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout\r\n      :layoutConfig=\"{\r\n        isShowLayoutChart: true,\r\n      }\"\r\n    >\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutChart>\r\n        <div class=\"progressBox\">\r\n          <div class=\"progressTwo\">\r\n            <div class=\"left\">\r\n              <span class=\"title\">当前在园车辆总数</span>\r\n              <div class=\"info\" style=\"height: 60.5px\">\r\n                <span class=\"colorActive textActive\">{{\r\n                  inParkStatistics.Total\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"progressTwo\">\r\n            <div class=\"left\">\r\n              <span class=\"title\">访问类型统计</span>\r\n              <div class=\"info\">\r\n                <span class=\"num\">\r\n                  <span\r\n                    :class=\"[index == 0 ? 'colorActive textActive' : '']\"\r\n                    v-for=\"(item, index) in inParkStatistics.Access\"\r\n                    :key=\"index\"\r\n                    >{{ index == 0 ? \"\" : \"/\" }} {{ item.Value }}</span\r\n                  >\r\n                </span>\r\n                <span class=\"text\">\r\n                  <span\r\n                    :class=\"[index == 0 ? 'colorActive' : '']\"\r\n                    v-for=\"(item, index) in inParkStatistics.Access\"\r\n                    :key=\"index\"\r\n                    >{{ index == 0 ? \"\" : \"/\" }} {{ item.Key }}</span\r\n                  >\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <el-progress\r\n              class=\"right\"\r\n              type=\"circle\"\r\n              :stroke-width=\"20\"\r\n              :percentage=\"AccessPrecent.toFixed(2)\"\r\n            ></el-progress>\r\n          </div>\r\n          <div class=\"progressTwo\">\r\n            <div class=\"left\">\r\n              <span class=\"title\">入园超时统计</span>\r\n              <div class=\"info\">\r\n                <span class=\"num\">\r\n                  <span\r\n                    :class=\"[index == 0 ? 'colorActive textActive' : '']\"\r\n                    v-for=\"(item, index) in inParkStatistics.Timeout\"\r\n                    :key=\"index\"\r\n                    >{{ index == 0 ? \"\" : \"/\" }} {{ item.Value }}</span\r\n                  >\r\n                </span>\r\n                <span class=\"text\">\r\n                  <span\r\n                    :class=\"[index == 0 ? 'colorActive' : '']\"\r\n                    v-for=\"(item, index) in inParkStatistics.Timeout\"\r\n                    :key=\"index\"\r\n                    >{{ index == 0 ? \"\" : \"/\" }} {{ item.Key }}</span\r\n                  >\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <el-progress\r\n              class=\"right\"\r\n              type=\"circle\"\r\n              :stroke-width=\"20\"\r\n              :percentage=\"TimeoutPrecent.toFixed(2)\"\r\n            ></el-progress>\r\n          </div>\r\n          <div class=\"progressTwo\">\r\n            <div class=\"left\">\r\n              <span class=\"title\">车辆类型统计</span>\r\n              <div class=\"info\">\r\n                <span\r\n                  style=\"color: #298dff; margin-bottom: 10px\"\r\n                  v-for=\"(item, index) in inParkStatistics.VehiclesType\"\r\n                  :key=\"index\"\r\n                  >{{ item.name }}: {{ item.value }}\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <v-chart\r\n              ref=\"pieChartRef\"\r\n              class=\"pieChartDom\"\r\n              :option=\"pieOptionOption\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport {\r\n  VBInParkVehiclesGetInParkVehiclesList,\r\n  VBInParkVehiclesGetInParkStatistics,\r\n  VBInParkVehiclesVehicleLeaving,\r\n  VBInParkVehiclesExportData,\r\n  VBPassRecordGetDropList,\r\n} from \"@/api/business/vehicleBarrier.js\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/mixins/export.js\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\n\r\nimport VChart from \"vue-echarts\";\r\nimport { use } from \"echarts/core\";\r\nimport { CanvasRenderer } from \"echarts/renderers\";\r\nimport { PieChart } from \"echarts/charts\";\r\nimport {\r\n  GridComponent,\r\n  LegendComponent,\r\n  TooltipComponent,\r\n  TitleComponent,\r\n  DataZoomComponent,\r\n} from \"echarts/components\";\r\nuse([\r\n  CanvasRenderer,\r\n  PieChart,\r\n  DataZoomComponent,\r\n  GridComponent,\r\n  LegendComponent,\r\n  TitleComponent,\r\n  TooltipComponent,\r\n]);\r\n\r\nexport default {\r\n  Name: \"vehiclePeerRecord\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n    VChart,\r\n  },\r\n  mixins: [exportInfo, addRouterPage],\r\n  data() {\r\n    return {\r\n      pieOptionOption: {\r\n        tooltip: {\r\n          trigger: \"item\",\r\n        },\r\n        legend: {\r\n          show: false,\r\n        },\r\n        series: [\r\n          {\r\n            name: \"车辆类型统计\",\r\n            type: \"pie\",\r\n            radius: [\"60%\", \"90%\"],\r\n            avoidLabelOverlap: false,\r\n            label: {\r\n              show: false,\r\n              position: \"center\",\r\n            },\r\n            labelLine: {\r\n              show: false,\r\n            },\r\n            data: [],\r\n            // color:['#298DFF']\r\n          },\r\n        ],\r\n      },\r\n      ruleForm: {\r\n        Number: \"\",\r\n        Date: [],\r\n        StartTime: null,\r\n        EndTime: null,\r\n        AccessType: \"\",\r\n        UserName: \"\",\r\n        VehicleType: \"\",\r\n        Timeout: \"\",\r\n      },\r\n\r\n      tableSelection: [],\r\n\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Number\", // 字段ID\r\n            label: \"车牌号码\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"Date\",\r\n            label: \"通行时间\",\r\n            type: \"datePicker\",\r\n            otherOptions: {\r\n              type: \"datetimerange\",\r\n              rangeSeparator: \"�?,\r\n              startPlaceholder: \"开始日�?,\r\n              endPlaceholder: \"结束日期\",\r\n              clearable: true,\r\n              valueFormat: \"yyyy-MM-dd HH:mm\",\r\n            },\r\n            change: (e) => {\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.StartTime = e[0];\r\n                this.ruleForm.EndTime = e[1];\r\n              } else {\r\n                this.ruleForm.StartTime = null;\r\n                this.ruleForm.EndTime = null;\r\n              }\r\n            },\r\n          },\r\n          {\r\n            key: \"VehicleType\",\r\n            label: \"车辆类型\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"UserName\",\r\n            label: \"车主姓名\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"AccessType\",\r\n            label: \"访问类型\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"Timeout\",\r\n            label: \"是否超时\",\r\n            type: \"select\",\r\n            options: [\r\n              {\r\n                label: \"�?,\r\n                value: true,\r\n              },\r\n              {\r\n                label: \"�?,\r\n                value: false,\r\n              },\r\n            ],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n        ],\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData(\r\n                  this.ruleForm,\r\n                  \"在园车俩\",\r\n                  VBInParkVehiclesExportData\r\n                );\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: \"100%\",\r\n        tableColumns: [\r\n          // {\r\n          //   width: 50,\r\n          //   otherOptions: {\r\n          //     type: 'selection',\r\n          //     align: 'center'\r\n          //   }\r\n          // },\r\n          {\r\n            label: \"驶入时间\",\r\n            key: \"PassTime\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"车牌号码\",\r\n            key: \"Number\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"车辆类型\",\r\n            key: \"VehicleType\",\r\n          },\r\n          {\r\n            label: \"车主姓名\",\r\n            key: \"UserName\",\r\n          },\r\n          {\r\n            label: \"车主联系方式\",\r\n            key: \"UserPhone\",\r\n          },\r\n          {\r\n            label: \"访问类型\",\r\n            key: \"AccessType\",\r\n          },\r\n          {\r\n            label: \"出入�?,\r\n            key: \"EntranceName\",\r\n          },\r\n          {\r\n            label: \"入园时长\",\r\n            key: \"ParkTime\",\r\n          },\r\n          {\r\n            label: \"超时时长\",\r\n            key: \"Timeout\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActionsWidth: 120,\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.$router.push({\r\n                name: \"vehiclesInTheParkView\",\r\n                query: { pg_redirect: this.$route.name, Id: row.Id },\r\n              });\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"车辆出园\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleLeave(row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/view\",\r\n          hidden: true,\r\n          component: () => import(\"./dialog/view.vue\"),\r\n          meta: { title: \"在园车辆详情\" },\r\n          name: \"vehiclesInTheParkView\",\r\n        },\r\n      ],\r\n      inParkStatistics: {},\r\n\r\n      AccessPrecent: 0,\r\n      TimeoutPrecent: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.vBPassRecordGetDropList();\r\n    // this.init();\r\n    // this.vBInParkVehiclesGetInParkStatistics();\r\n  },\r\n  async mounted() {\r\n    // 跳转设置默认参数\r\n    let JumpParams = this.$qiankun.getMicroAppJumpParamsFn();\r\n    if (JumpParams.isJump == \"true\") {\r\n      this.ruleForm.Timeout = JumpParams.Timeout == \"true\";\r\n    }\r\n    this.onFresh();\r\n  },\r\n  beforeDestroy() {\r\n    this.$qiankun.setMicroAppJumpParamsFn();\r\n    this.ruleForm.Timeout = null;\r\n  },\r\n  methods: {\r\n    async vBInParkVehiclesGetInParkStatistics() {\r\n      if (this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null;\r\n        this.ruleForm.EndTime = null;\r\n      }\r\n      let res = await VBInParkVehiclesGetInParkStatistics({\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.inParkStatistics = res.Data;\r\n        let AccessTotal = res.Data.Access.reduce(\r\n          (accumulator, currentValue) =>\r\n            accumulator + parseInt(currentValue.Value),\r\n          0\r\n        );\r\n        let TimeoutTotal = res.Data.Timeout.reduce(\r\n          (accumulator, currentValue) =>\r\n            accumulator + parseInt(currentValue.Value),\r\n          0\r\n        );\r\n        // let VehiclesTypeTotal = res.Data.VehiclesType.map(\r\n        //   (obj) => obj.value\r\n        // ).reduce(\r\n        //   (accumulator, currentValue) => accumulator + currentValue.Value,\r\n        //   0\r\n        // );\r\n        this.inParkStatistics.Access = res.Data.Access;\r\n        this.inParkStatistics.Timeout = res.Data.Timeout;\r\n        this.inParkStatistics.VehiclesType = res.Data.VehiclesType.map(\r\n          (item) => ({\r\n            value: item.Value,\r\n            name: item.Key,\r\n          })\r\n        );\r\n        this.pieOptionOption.series[0].data =\r\n          this.inParkStatistics.VehiclesType;\r\n\r\n        this.AccessPrecent =\r\n          (Number(res.Data.Access[0].Value) / AccessTotal) * 100;\r\n        this.TimeoutPrecent =\r\n          (Number(res.Data.Timeout[0].Value) / TimeoutTotal) * 100;\r\n        if (AccessTotal == 0) {\r\n          this.AccessPrecent = 0;\r\n        }\r\n        if (TimeoutTotal == 0) {\r\n          this.TimeoutPrecent = 0;\r\n        }\r\n      }\r\n    },\r\n    async vBPassRecordGetDropList() {\r\n      let res = await VBPassRecordGetDropList({});\r\n      if (res.IsSucceed) {\r\n        this.customForm.formItems.find((v) => v.key == \"VehicleType\").options =\r\n          res.Data.find((v) => v.Name == \"VehicleType\").List.map((item) => ({\r\n            label: item.Key,\r\n            value: item.Value,\r\n          }));\r\n        this.customForm.formItems.find((v) => v.key == \"AccessType\").options =\r\n          res.Data.find((v) => v.Name == \"AccessType\").List.map((item) => ({\r\n            label: item.Key,\r\n            value: item.Value,\r\n          }));\r\n      }\r\n    },\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n      this.vBInParkVehiclesGetInParkStatistics();\r\n    },\r\n    async init() {\r\n      await this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      if (this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null;\r\n        this.ruleForm.EndTime = null;\r\n      }\r\n      const res = await VBInParkVehiclesGetInParkVehiclesList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.Total;\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.onFresh();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.onFresh();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleLeave(row) {\r\n      this.$confirm(\r\n        '<div><div>是否确认进行车辆出园</div><div style=\"font-size:12px;color:#aaa\">车辆出园操作将标识该车为出园状�?/div></div>',\r\n        \"操作确认\",\r\n        {\r\n          type: \"warning\",\r\n          dangerouslyUseHTMLString: true,\r\n        }\r\n      )\r\n        .then(async (_) => {\r\n          const res = await VBInParkVehiclesVehicleLeaving({\r\n            Id: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.$message.success(\"操作成功\");\r\n            this.init();\r\n          } else {\r\n            this.$message.error(res.Message);\r\n          }\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n\r\n.imgwapper {\r\n  width: 100px;\r\n  height: 100px;\r\n}\r\n.empty-img {\r\n  text-align: center;\r\n}\r\n.progressBox {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-around;\r\n  padding: 20px 0px;\r\n  .pieChartDom {\r\n    width: 126px;\r\n    height: 126px;\r\n  }\r\n  .progressTwo {\r\n    height: 126px;\r\n    display: flex;\r\n    flex-direction: row;\r\n    .left {\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      .title {\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n      }\r\n      .info {\r\n        display: flex;\r\n        flex-direction: column;\r\n        .num {\r\n          color: #aaa;\r\n          margin-bottom: 10px;\r\n        }\r\n        .text {\r\n          color: #aaa;\r\n        }\r\n        .colorActive {\r\n          color: #298dff;\r\n        }\r\n        .textActive {\r\n          font-size: 28px;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}