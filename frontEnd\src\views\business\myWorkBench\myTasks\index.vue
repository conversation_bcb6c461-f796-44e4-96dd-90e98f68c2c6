<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        >
          <!-- <template #customBtn="{slotScope}"><el-button type="text" @click="goHandelAlarm(slotScope)">去处理</el-button></template> -->
        </CustomTable>
      </template>
    </CustomLayout>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import {
  GetTaskPageList,
  GetTaskType,
  GetTaskStatus
} from '@/api/business/myWorkBench'
export default {
  name: 'MyTasks',
  components: {
    CustomTable,
    CustomForm,
    CustomLayout
  },
  data() {
    return {
      ruleForm: {
        Name: '',
        Type: '',
        StartTime: null,
        EndTime: null,
        Status: null,
        Date: []
      },
      taskTypeData: [],
      customForm: {
        formItems: [
          {
            key: 'Name',
            label: '任务名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            width: '240px',
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Type',
            label: '任务类型',
            type: 'select',
            otherOptions: {
              clearable: true
            },
            options: [

            ],
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Status',
            label: '任务状态',
            type: 'select',
            otherOptions: {
              clearable: true
            },
            options: [],
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Date',
            label: '开始时间',
            type: 'datePicker',
            otherOptions: {
              type: 'datetimerange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              clearable: true,
              valueFormat: 'yyyy-MM-dd HH:mm'
            },
            change: (e) => {
              console.log(e)
              if (e && e.length !== 0) {
                this.ruleForm.StartTime = e[0]
                this.ruleForm.EndTime = e[1]
              } else {
                this.ruleForm.StartTime = null
                this.ruleForm.EndTime = null
              }
            }
          }
        ],
        rules: {},
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        loading: false,
        buttonConfig: {
          buttonList: [

          ]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        height: '100%',
        tableColumns: [
          {
            label: '任务状态',
            key: 'StatusName'
          },
          {
            label: '任务类型',
            key: 'Type'
          },
          {
            label: '任务名称',
            key: 'Name'
          },
          {
            label: '来源',
            key: 'Source'
          },
          {
            label: '业务模块',
            key: 'Module'
          },
          {
            label: '任务开始时间',
            key: 'StartTime'
          },
          {
            label: '计划完成时间',
            key: 'EndTime'
          },
          {
            label: '实际完成时间',
            key: 'DoneTime'
          }
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: '去处理',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.goHandelAlarm(row)
            }
          }
        ]
      }
    }
  },
  mounted() {
    this.getTaskType()
    this.getTaskStatus()
    this.onFresh()
  },
  methods: {
    // 获取任务类型
    getTaskType() {
      GetTaskType({}).then((res) => {
        if (res.IsSucceed) {
          const arr = []
          const data = res.Data || null
          data.forEach((item) => {
            const obj = {
              label: item.Name,
              value: item.Value
            }
            arr.push(obj)
          })
          this.customForm.formItems.find((v) => v.key == 'Type').options = arr
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    // 获取任务类型
    getTaskStatus() {
      this.customTableConfig.loading = true
      GetTaskStatus({}).then((res) => {
        if (res.IsSucceed) {
          const arr = []
          const data = res.Data || null
          data.forEach((item) => {
            const obj = {
              label: item.Name,
              value: item.Value
            }
            arr.push(obj)
          })
          this.customForm.formItems.find((v) => v.key == 'Status').options = arr
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      }).finally(() => {
        this.customTableConfig.loading = false
      })
    },
    searchForm(data) {
      console.log(data)
      this.onFresh()
    },
    resetForm() {
      this.onFresh()
    },
    onFresh() {
      this.fetchData()
    },
    async fetchData() {
      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {
        this.ruleForm.StartTime = null
        this.ruleForm.EndTime = null
      }
      await GetTaskPageList({
        ...this.ruleForm,
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize
      }).then((res) => {
        if (res.IsSucceed) {
          this.customTableConfig.tableData = res.Data.Data
          this.customTableConfig.total = res.Data.TotalCount
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },

    // 去处理路由跳转
    goHandelAlarm(item) {
      // this.$router.push({ name: item.Code })
      console.log(item, 'item')
      if (item.Url) {
        let platform = '' // 子应用
        if (item.ModuleName == '后台设置') {
          platform = 'management'
        } else {
          platform = 'digitalfactory'
        }
        this.$qiankun.switchMicroAppFn(
          platform,
          item.ModuleCode,
          item.ModuleId,
          item.Url
        )
      } else {
        const platform = 'digitalfactory'
        const code = 'szgc'
        const id = '97b119f9-e634-4d95-87b0-df2433dc7893'
        let url = ''
        if (item.Module == '环境管理') {
          url = '/business/environment/alarmInformation'
        } else
        if (item.Module == '访客管理') {
          url = '/business/visitorList'
          console.log('访客管理')
        }
        this.$qiankun.switchMicroAppFn(platform, code, id, url)
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.onFresh()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.onFresh()
    }
  }
}
</script>

<style scoped lang="scss">
* {
  box-sizing: border-box;
}

.layout {
  height: 100%;
  width: 100%;
  position: absolute;
  ::v-deep {
    .CustomLayout {
      .layoutTable {
        height: 0;
        .CustomTable {
          height: 100%;
          display: flex;
          flex-direction: column;
          .table {
            flex: 1;
            height: 0;
            display: flex;
            flex-direction: column;
            .el-table {
              flex: 1;
              height: 0;
            }
          }
        }
      }
    }
  }
}
</style>
