{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\utils\\request.js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\utils\\request.js", "mtime": 1755674172207}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "axios", "Message", "store", "getToken", "baseUrl", "platformUrl", "qs", "keyMap", "Map", "getRequestKey", "config", "url", "method", "data", "params", "key", "stringify", "join", "checkRequest", "source", "has", "cancel", "set", "deleteRequestKey", "delete", "A<PERSON>os", "option", "service", "create", "baseURL", "timeout", "interceptors", "request", "use", "prototype", "$tokenStr", "headers", "getters", "token", "localStorage", "getItem", "Last_Working_Object_Id", "CancelToken", "cancelToken", "error", "console", "log", "Promise", "reject", "response", "res", "StatusCode", "dispatch", "then", "location", "reload", "message", "type", "duration", "isCancel", "resolve", "catch", "finally", "_"], "sources": ["D:/project/platform_framework_hlj/hljbimdigitalfactory/frontEnd/src/utils/request.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport axios from 'axios'\r\nimport { Message } from 'element-ui'\r\nimport store from '@/store'\r\nimport { getToken } from '@/utils/auth'\r\n// import Vue from 'vue'\r\nimport { baseUrl, platformUrl } from '@/utils/baseurl'\r\nimport qs from 'qs'\r\n\r\n// create an axios instance\r\n// Vue.prototype.$config = window.URLGLOBAL\r\n\r\n/* setTimeout(() => {\r\n  delete window.URLGLOBAL // 用完之后删除\r\n}, 500) */\r\n// function baseURL() {\r\n// const regexSYS = /\\/Platform\\//\r\n// const regexPRO = /\\/DF\\//\r\n// const baseUrl = regexSYS.test(data) ? window.URLGLOBAL.Platform : regexPRO.test(data) ? window.URLGLOBAL.PRO : window.URLGLOBAL.URL ?? ''\r\n// return baseUrl\r\n// return process.env.VUE_APP_BASE_API\r\n// }\r\n\r\n// 创建存储 key 的 集合\r\nconst keyMap = new Map()\r\n\r\n// 获取每一次请求的key\r\nfunction getRequestKey(config) {\r\n  const { url, method, data, params } = config\r\n  // 记得这里一定要处理 每次请求都掉会变化的参数(比如每个请求都携带了时间戳),否则二个请求的key不一样\r\n  const key = [url, method, qs.stringify(data), qs.stringify(params)].join('&')\r\n  return key\r\n}\r\n\r\n// 判断是否存在该key,不存在则插入\r\nfunction checkRequest(config, source) {\r\n  if (config.url === '/Platform/User/GetUserEntity' || config.url === '/Platform/Company/GetEntity') {\r\n    return\r\n  }\r\n  const key = getRequestKey(config)\r\n  if (keyMap.has(key)) {\r\n    source.cancel()\r\n  } else {\r\n    keyMap.set(key, config)\r\n  }\r\n}\r\n\r\n// 响应完成后,删除key\r\nfunction deleteRequestKey(config) {\r\n  const key = getRequestKey(config)\r\n  if (keyMap.has(key)) {\r\n    keyMap.delete(key)\r\n  }\r\n}\r\n\r\n// let baseUrlStr = baseUrl()\r\nfunction Axios(option) {\r\n  // if (option.url === '/Platform/User/GetUserEntity' || option.url === '/Platform/Company/GetEntity') {\r\n  //   baseUrlStr = platformUrl() // process.env.VUE_APP_PLATFORM_API\r\n  // } else {\r\n  //   baseUrlStr = baseUrl()\r\n  // }\r\n  const service = axios.create({\r\n    baseURL: baseUrl(), // url = base url + request url\r\n    // withCredentials: true, // send cookies when cross-domain requests\r\n    timeout: 60000 * 10 // request timeout\r\n  })\r\n\r\n  // request interceptor\r\n  service.interceptors.request.use(\r\n    config => {\r\n      // do something before request is sent\r\n      if (Vue.prototype.$tokenStr) {\r\n        config.headers['Authorization'] = Vue.prototype.$tokenStr\r\n      } else if (store.getters.token || getToken()) {\r\n        // let each request carry token\r\n        // ['X-Token'] is a custom headers key\r\n        // please modify it according to the actual situation\r\n        config.headers['Authorization'] = getToken()\r\n      }\r\n      if (localStorage.getItem('Last_Working_Object_Id')) {\r\n        config.headers.Last_Working_Object_Id = localStorage.getItem('Last_Working_Object_Id')\r\n      }\r\n\r\n      // 这里 是拦截的关键,使用 axios CancelToken, 每次请求都重新生成 source 和 cancelToken\r\n      const source = axios.CancelToken.source()\r\n      config.cancelToken = source.token\r\n      // 检查请求\r\n      checkRequest(config, source)\r\n      return config\r\n    },\r\n    error => {\r\n      // do something with request error\r\n      console.log(error) // for debug\r\n      return Promise.reject(error)\r\n    }\r\n  )\r\n\r\n  // response interceptor\r\n  service.interceptors.response.use(\r\n    /**\r\n     * If you want to get http information such as headers or status\r\n     * Please return  response => response\r\n     */\r\n\r\n    /**\r\n     * Determine the request status by custom code\r\n     * Here is just an example\r\n     * You can also judge the status by HTTP Status Code\r\n     */\r\n    response => {\r\n      const res = response.data\r\n      // if (res.StatusCode === 502 || res.StatusCode === 502 || res.StatusCode === 501 || res.StatusCode === 500) {\r\n      //   // 服务端返回false，同时需要获取数据\r\n      //   // to re-login\r\n      //   // MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {\r\n      //   //   confirmButtonText: 'Re-Login',\r\n      //   //   cancelButtonText: 'Cancel',\r\n      //   //   type: 'warning'\r\n      //   // }).then(() => {\r\n      //   //   store.dispatch('user/resetToken').then(() => {\r\n      //   //     location.reload()\r\n      //   //   })\r\n      //   // })\r\n      //   return res\r\n      // } else if (res.StatusCode === 200 || res.StatusCode === 502 || res.StatusCode === 502 || res.StatusCode === 501 || res.StatusCode === 500) {\r\n      //   // Message({\r\n      //   //   message: res.Message || 'Error',\r\n      //   //   type: 'error',\r\n      //   //   duration: 5 * 1000\r\n      //   // })\r\n      // } else\r\n      if (res.StatusCode === 401) {\r\n        store.dispatch('user/resetToken').then(() => {\r\n          location.reload()\r\n        })\r\n      } else if (res.StatusCode === 502) { // res.StatusCode === 501 ||\r\n        Message({\r\n          message: res.Message || 'Error',\r\n          type: 'error',\r\n          duration: 5 * 1000\r\n        })\r\n      } else {\r\n        if (res.Message !== '操作错误') {\r\n          return res\r\n        } else {\r\n          return null\r\n        }\r\n\r\n        // return Promise.reject(new Error(res.Message || ' '))\r\n      }\r\n      // else if (res.StatusCode === 200001) { // res.StatusCode === 501 ||\r\n      //   Message({\r\n      //     message: res.Message || 'Error',\r\n      //     type: 'error',\r\n      //     duration: 5 * 1000\r\n      //   })\r\n      // }\r\n    },\r\n    error => {\r\n      // 拦截掉重复请求的错误,中断promise执行\r\n      if (axios.isCancel(error)) {\r\n        return new Promise(() => { })\r\n      }\r\n\r\n      console.log('err' + error) // for debug\r\n      Message({\r\n        message: '请求超时',\r\n        type: 'error',\r\n        duration: 5 * 1000\r\n      })\r\n      return Promise.reject(error)\r\n    }\r\n  )\r\n  // 请求处理\r\n  return new Promise((resolve, reject) => {\r\n    service(option).then(res => {\r\n      resolve(res)\r\n      // 请求成功了但返回没有 data 内容放行到 外层的Promise 的 .catch\r\n      // if (res && res.Data != \"\") {\r\n      //   resolve(res);\r\n      // } else {\r\n      //   reject(res);\r\n      // }\r\n    }).catch(error => {\r\n      reject(error)\r\n    }).finally(_ => {\r\n      // 删除已完成的请求 key\r\n      deleteRequestKey(option)\r\n    })\r\n  })\r\n}\r\n\r\nexport default Axios\r\n"], "mappings": ";;;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,QAAQ,cAAc;AACvC;AACA,SAASC,OAAO,EAAEC,WAAW,QAAQ,iBAAiB;AACtD,OAAOC,EAAE,MAAM,IAAI;;AAEnB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAMC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;;AAExB;AACA,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC7B,IAAQC,GAAG,GAA2BD,MAAM,CAApCC,GAAG;IAAEC,MAAM,GAAmBF,MAAM,CAA/BE,MAAM;IAAEC,IAAI,GAAaH,MAAM,CAAvBG,IAAI;IAAEC,MAAM,GAAKJ,MAAM,CAAjBI,MAAM;EACjC;EACA,IAAMC,GAAG,GAAG,CAACJ,GAAG,EAAEC,MAAM,EAAEN,EAAE,CAACU,SAAS,CAACH,IAAI,CAAC,EAAEP,EAAE,CAACU,SAAS,CAACF,MAAM,CAAC,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC;EAC7E,OAAOF,GAAG;AACZ;;AAEA;AACA,SAASG,YAAYA,CAACR,MAAM,EAAES,MAAM,EAAE;EACpC,IAAIT,MAAM,CAACC,GAAG,KAAK,8BAA8B,IAAID,MAAM,CAACC,GAAG,KAAK,6BAA6B,EAAE;IACjG;EACF;EACA,IAAMI,GAAG,GAAGN,aAAa,CAACC,MAAM,CAAC;EACjC,IAAIH,MAAM,CAACa,GAAG,CAACL,GAAG,CAAC,EAAE;IACnBI,MAAM,CAACE,MAAM,CAAC,CAAC;EACjB,CAAC,MAAM;IACLd,MAAM,CAACe,GAAG,CAACP,GAAG,EAAEL,MAAM,CAAC;EACzB;AACF;;AAEA;AACA,SAASa,gBAAgBA,CAACb,MAAM,EAAE;EAChC,IAAMK,GAAG,GAAGN,aAAa,CAACC,MAAM,CAAC;EACjC,IAAIH,MAAM,CAACa,GAAG,CAACL,GAAG,CAAC,EAAE;IACnBR,MAAM,CAACiB,MAAM,CAACT,GAAG,CAAC;EACpB;AACF;;AAEA;AACA,SAASU,KAAKA,CAACC,MAAM,EAAE;EACrB;EACA;EACA;EACA;EACA;EACA,IAAMC,OAAO,GAAG3B,KAAK,CAAC4B,MAAM,CAAC;IAC3BC,OAAO,EAAEzB,OAAO,CAAC,CAAC;IAAE;IACpB;IACA0B,OAAO,EAAE,KAAK,GAAG,EAAE,CAAC;EACtB,CAAC,CAAC;;EAEF;EACAH,OAAO,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9B,UAAAvB,MAAM,EAAI;IACR;IACA,IAAIX,GAAG,CAACmC,SAAS,CAACC,SAAS,EAAE;MAC3BzB,MAAM,CAAC0B,OAAO,CAAC,eAAe,CAAC,GAAGrC,GAAG,CAACmC,SAAS,CAACC,SAAS;IAC3D,CAAC,MAAM,IAAIjC,KAAK,CAACmC,OAAO,CAACC,KAAK,IAAInC,QAAQ,CAAC,CAAC,EAAE;MAC5C;MACA;MACA;MACAO,MAAM,CAAC0B,OAAO,CAAC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,CAAC;IAC9C;IACA,IAAIoC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE;MAClD9B,MAAM,CAAC0B,OAAO,CAACK,sBAAsB,GAAGF,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IACxF;;IAEA;IACA,IAAMrB,MAAM,GAAGnB,KAAK,CAAC0C,WAAW,CAACvB,MAAM,CAAC,CAAC;IACzCT,MAAM,CAACiC,WAAW,GAAGxB,MAAM,CAACmB,KAAK;IACjC;IACApB,YAAY,CAACR,MAAM,EAAES,MAAM,CAAC;IAC5B,OAAOT,MAAM;EACf,CAAC,EACD,UAAAkC,KAAK,EAAI;IACP;IACAC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAC;IACnB,OAAOG,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;EAC9B,CACF,CAAC;;EAED;EACAjB,OAAO,CAACI,YAAY,CAACkB,QAAQ,CAAChB,GAAG;EAC/B;AACJ;AACA;AACA;;EAEI;AACJ;AACA;AACA;AACA;EACI,UAAAgB,QAAQ,EAAI;IACV,IAAMC,GAAG,GAAGD,QAAQ,CAACpC,IAAI;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIqC,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE;MAC1BjD,KAAK,CAACkD,QAAQ,CAAC,iBAAiB,CAAC,CAACC,IAAI,CAAC,YAAM;QAC3CC,QAAQ,CAACC,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIL,GAAG,CAACC,UAAU,KAAK,GAAG,EAAE;MAAE;MACnClD,OAAO,CAAC;QACNuD,OAAO,EAAEN,GAAG,CAACjD,OAAO,IAAI,OAAO;QAC/BwD,IAAI,EAAE,OAAO;QACbC,QAAQ,EAAE,CAAC,GAAG;MAChB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAIR,GAAG,CAACjD,OAAO,KAAK,MAAM,EAAE;QAC1B,OAAOiD,GAAG;MACZ,CAAC,MAAM;QACL,OAAO,IAAI;MACb;;MAEA;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,CAAC,EACD,UAAAN,KAAK,EAAI;IACP;IACA,IAAI5C,KAAK,CAAC2D,QAAQ,CAACf,KAAK,CAAC,EAAE;MACzB,OAAO,IAAIG,OAAO,CAAC,YAAM,CAAE,CAAC,CAAC;IAC/B;IAEAF,OAAO,CAACC,GAAG,CAAC,KAAK,GAAGF,KAAK,CAAC,EAAC;IAC3B3C,OAAO,CAAC;MACNuD,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,CAAC,GAAG;IAChB,CAAC,CAAC;IACF,OAAOX,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;EAC9B,CACF,CAAC;EACD;EACA,OAAO,IAAIG,OAAO,CAAC,UAACa,OAAO,EAAEZ,MAAM,EAAK;IACtCrB,OAAO,CAACD,MAAM,CAAC,CAAC2B,IAAI,CAAC,UAAAH,GAAG,EAAI;MAC1BU,OAAO,CAACV,GAAG,CAAC;MACZ;MACA;MACA;MACA;MACA;MACA;IACF,CAAC,CAAC,CAACW,KAAK,CAAC,UAAAjB,KAAK,EAAI;MAChBI,MAAM,CAACJ,KAAK,CAAC;IACf,CAAC,CAAC,CAACkB,OAAO,CAAC,UAAAC,CAAC,EAAI;MACd;MACAxC,gBAAgB,CAACG,MAAM,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,eAAeD,KAAK", "ignoreList": []}]}