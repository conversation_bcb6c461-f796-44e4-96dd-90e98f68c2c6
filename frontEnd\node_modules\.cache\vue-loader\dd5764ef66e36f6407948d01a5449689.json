{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\behaviorAnalysisAlarm\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\behaviorAnalysisAlarm\\index.vue", "mtime": 1755506574216}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/behaviorAnalysis/behaviorAnalysisAlarm", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport dialogForm from \"./dialogForm.vue\";\r\nimport dayjs from \"dayjs\";\r\n\r\nimport {\r\n  GetBehaviorWarningList,\r\n  GetBehaviorWarningEntity,\r\n  TriggerBehaviorWarning,\r\n} from \"@/api/business/behaviorAnalysis\";\r\nimport { GetDictionaryDetailListByCode } from \"@/api/sys\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: dialogForm,\r\n      componentsConfig: {\r\n        Data: {},\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"告警详情\",\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        DeviceName: \"\",\r\n        WarningType: \"\",\r\n        HandleStatus: \"\",\r\n        Date: [],\r\n        BeginWarningTime: null,\r\n        EndWarningTime: null,\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Date\", // 字段ID\r\n            label: \"告警时间\", // Form的label\r\n            type: \"datePicker\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              type: \"daterange\",\r\n              disabled: false,\r\n              placeholder: \"请输�?..\",\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.BeginWarningTime = dayjs(e[0]).format(\r\n                  \"YYYY-MM-DD\"\r\n                );\r\n                this.ruleForm.EndWarningTime = dayjs(e[1]).format(\"YYYY-MM-DD\");\r\n              } else {\r\n                this.ruleForm.BeginWarningTime = null;\r\n                this.ruleForm.EndWarningTime = null;\r\n              }\r\n            },\r\n          },\r\n          {\r\n            key: \"WarningType\",\r\n            label: \"告警类型\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n              this.GetTypesByModule();\r\n            },\r\n          },\r\n          {\r\n            key: \"DeviceName\",\r\n            label: \"告警设备\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"HandleStatus\",\r\n            label: \"状�?,\r\n            type: \"select\",\r\n            options: [\r\n              {\r\n                label: \"待广�?,\r\n                value: \"1\",\r\n              },\r\n              {\r\n                label: \"已广�?,\r\n                value: \"2\",\r\n              },\r\n              {\r\n                label: \"广播成功\",\r\n                value: \"3\",\r\n              },\r\n              {\r\n                label: \"广播失败\",\r\n                value: \"4\",\r\n              },\r\n              {\r\n                label: \"无需广播\",\r\n                value: \"5\",\r\n              },\r\n              {\r\n                label: \"无广播配�?,\r\n                value: \"6\",\r\n              },\r\n            ],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: []\r\n          //   {\r\n          //     text: \"批量关闭\",\r\n          //     onclick: (item) => {\r\n          //       console.log(item);\r\n          //       this.handleClose();\r\n          //     },\r\n          //   },\r\n          // ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          // {\r\n          //   otherOptions: {\r\n          //     type: \"selection\",\r\n          //     align: \"center\",\r\n          //     fixed: \"left\",\r\n          //   },\r\n          // },\r\n          {\r\n            label: \"告警时间\",\r\n            key: \"WarningTime\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: \"告警类型\",\r\n            key: \"WarningTypeDes\",\r\n            width: 140,\r\n            otherOptions: {\r\n              align: \"center\",\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: \"告警设备\",\r\n            key: \"DeviceName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"告警设备编码\",\r\n            key: \"DeviceCode\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"设备地址\",\r\n            key: \"Position\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"联动广播\",\r\n            key: \"BroadcastEquipmentCount\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"广播时间\",\r\n            key: \"BroadcastTime\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"状�?,\r\n            key: \"HandleStatus\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              if (row.HandleStatus == 1) {\r\n                return this.$createElement(\"span\", {}, \"待广�?);\r\n              } else if (row.HandleStatus == 2) {\r\n                return this.$createElement(\"span\", {}, \"已广�?);\r\n              } else if (row.HandleStatus == 3) {\r\n                return this.$createElement(\r\n                  \"span\",\r\n                  {\r\n                    style: {\r\n                      color: \"green\",\r\n                    },\r\n                  },\r\n                  \"广播成功\"\r\n                );\r\n              } else if (row.HandleStatus == 4) {\r\n                return this.$createElement(\r\n                  \"span\",\r\n                  {\r\n                    style: {\r\n                      color: \"red\",\r\n                    },\r\n                  },\r\n                  \"广播失败\"\r\n                );\r\n              } else if (row.HandleStatus == 5) {\r\n                return this.$createElement(\r\n                  \"span\",\r\n                  {},\r\n                  \"无需广播\"\r\n                );\r\n              } else if (row.HandleStatus == 6) {\r\n                return this.$createElement(\r\n                  \"span\",\r\n                  {},\r\n                  \"无广播配�?\r\n                );\r\n              }\r\n              return this.$createElement(\"span\", {}, \"\");\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: \"center\",\r\n          width: \"180\",\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看详情\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"重新广播\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleRebroadcast(row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init();\r\n\r\n    this.getDictionaryDetailListByCode();\r\n  },\r\n  // mixins: [getGridByCode],\r\n  methods: {\r\n    async handleClose() {\r\n      const res = await SetWarningStatus({\r\n        Status: \"2\",\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.$message.success(\"操作成功\");\r\n        this.onFresh();\r\n      }\r\n    },\r\n    async getDictionaryDetailListByCode() {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode: \"BehaviorWarningType\",\r\n      });\r\n      if (res.IsSucceed) {\r\n        let result = res.Data || [];\r\n        let warningType = result.map((item) => ({\r\n          value: item.Value,\r\n          label: item.Display_Name,\r\n        }));\r\n        this.customForm.formItems.find(\r\n          (item) => item.key == \"WarningType\"\r\n        ).options = warningType;\r\n      }\r\n    },\r\n\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1;\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.BeginWarningTime = null;\r\n      this.ruleForm.EndWarningTime = null;\r\n      this.ruleForm.Date = null;\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.GetBehaviorWarningList();\r\n    },\r\n\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.GetBehaviorWarningList();\r\n    },\r\n    async GetBehaviorWarningList() {\r\n      const res = await GetBehaviorWarningList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n\r\n    async handleRebroadcast(row) {\r\n      const res = await TriggerBehaviorWarning({\r\n        ID: row.Id,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.$message.success(\"操作成功\");\r\n        this.init();\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.GetBehaviorWarningList();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.GetBehaviorWarningList();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleEdit(row) {\r\n      this.dialogVisible = true;\r\n      this.componentsConfig.Data = row;\r\n      this.componentsConfig = {\r\n        type: \"edit\",\r\n        data: row,\r\n      };\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}