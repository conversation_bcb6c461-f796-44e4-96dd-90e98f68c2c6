{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\environmentalManagement\\monitoringArchives\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\environmentalManagement\\monitoringArchives\\index.vue", "mtime": 1755506574276}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/business/environmentalManagement/monitoringArchives", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        >\r\n          <template #Position>\r\n            <el-cascader\r\n              v-model=\"positionArr\"\r\n              :options=\"options\"\r\n              :placeholder=\"`请选择`\"\r\n              :props=\"props\"\r\n              style=\"margin-bottom: 18px; width: 100%\"\r\n            />\r\n          </template>\r\n        </CustomForm>\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport DialogForm from './dialogForm.vue'\r\nimport DialogType from './dialogType.vue'\r\nimport DialogFormLook from './dialogFormLook.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport {\r\n  GetEquipmentList,\r\n  DeleteEquipment,\r\n  ExportEnvironmentEquipment,\r\n  EnvironmentImportTemplate,\r\n  EnvironmentEquipmentImport,\r\n  ExportEquipmentList,\r\n} from '@/api/business/environmentalManagement'\r\nimport { deviceTypeMixins } from '../../mixins/deviceType.js'\r\n// import * as moment from 'moment'\r\nimport dayjs from 'dayjs'\r\nimport importDialog from '@/views/business/energyManagement/components/import.vue'\r\nimport exportInfo from '@/views/business/energyManagement/mixins/export.js'\r\nimport getGridByCode from '@/views/business/safetyManagement/mixins/index'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout,\r\n    importDialog\r\n  },\r\n  mixins: [deviceTypeMixins, exportInfo, getGridByCode],\r\n  data() {\r\n    return {\r\n      options: [],\r\n      props: {\r\n        label: 'Label',\r\n        children: 'Children',\r\n        value: 'Label',\r\n        checkStrictly: true\r\n      },\r\n      positionArr: [],\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        interfaceName: EnvironmentEquipmentImport\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Content', // 字段ID\r\n            label: '', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '输入设备编号或名称进行搜�?\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EqtType',\r\n            label: '设备类型',\r\n            type: 'select',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择设备类型'\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position', // 字段ID\r\n            label: '安装位置', // Form的label\r\n            type: 'slot', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              // placeholder: '请输入安装位�?\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '设备类型配置',\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleClick()\r\n              }\r\n            },\r\n            {\r\n              text: '新增',\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleCreate()\r\n              }\r\n            },\r\n            {\r\n              text: '下载模板',\r\n              disabled: false, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleDownTemplate()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导入',\r\n              disabled: false, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.currentComponent = 'importDialog'\r\n                this.dialogVisible = true\r\n                this.dialogTitle = '批量导入'\r\n              }\r\n            },\r\n            // {\r\n            //   text: '导出',\r\n            //   key: 'batch',\r\n            //   disabled: true,\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleExport()\r\n            //   }\r\n            // },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleAllExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            width: 60,\r\n            label: '序号',\r\n            otherOptions: {\r\n              type: 'index',\r\n              align: 'center'\r\n            } // key\r\n            // otherOptions: {\r\n            //   width: 180, // 宽度\r\n            //   fixed: 'left', // left, right\r\n            //   align: 'center' //\tleft/center/right\r\n            // }\r\n          },\r\n          {\r\n            label: '设备编号',\r\n            key: 'EId',\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n            // render: row => {\r\n            //   return (\r\n            //     <span>\r\n            //       {row.EId}\r\n            //     </span>\r\n            //   )\r\n            // }\r\n          },\r\n          {\r\n            label: '设备名称',\r\n            key: 'Name',\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: '品牌',\r\n            key: 'Brand'\r\n          },\r\n          {\r\n            label: '型号',\r\n            key: 'Type'\r\n          },\r\n          {\r\n            label: '设备类型',\r\n            key: 'EqtType'\r\n          },\r\n          {\r\n            label: '安装位置',\r\n            key: 'Position'\r\n          },\r\n          {\r\n            label: '安装日期',\r\n            key: 'Date'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 200\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init()\r\n  },\r\n  mounted() {\r\n    this.initDeviceType('EqtType', 'EnvironmentEqtType')\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.positionArr = []\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getEquipmentList()\r\n    },\r\n    async init() {\r\n      this.getEquipmentList()\r\n      this.options = await this.getTreeAddress()\r\n      console.log(this.options)\r\n    },\r\n    async getEquipmentList() {\r\n      this.customTableConfig.loading = true\r\n      if (this.positionArr.length > 0) {\r\n        this.ruleForm.Position = this.positionArr.join('/')\r\n      }\r\n      const res = await GetEquipmentList({\r\n        ParameterJson: [\r\n          {\r\n            Key: '',\r\n            Value: [null],\r\n            Type: '',\r\n            Filter_Type: ''\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n\r\n        SortName: '',\r\n        SortOrder: '',\r\n        Search: '',\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        ...this.ruleForm\r\n      })\r\n      this.customTableConfig.loading = false\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n          ...item,\r\n          Date: dayjs(item.Date).format('YYYY-MM-DD')\r\n        }))\r\n        console.log(res)\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.currentComponent = DialogForm\r\n      this.componentsConfig = {\r\n        disabled: false,\r\n        title: '新增'\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row)\r\n      console.log(this)\r\n      this.$confirm(\r\n        '该操作将在监测设备档案中删除该设备信息，请确认是否删除？',\r\n        '删除',\r\n        {\r\n          type: 'error'\r\n        }\r\n      )\r\n        .then(async (_) => {\r\n          const res = await DeleteEquipment({\r\n            IDs: [row.ID]\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.init()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n        .catch((_) => { })\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      if (type === 'view') {\r\n        this.currentComponent = DialogFormLook\r\n        this.dialogTitle = '查看'\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: true,\r\n          title: '查看'\r\n        }\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        this.currentComponent = DialogForm\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: false,\r\n          title: '编辑'\r\n        }\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    // async handleExport() {\r\n    //   const res = await ExportEnvironmentEquipment({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: false,\r\n    //     Ids: this.tableSelection.map((item) => item.ID),\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n\r\n    // v2 版本导出\r\n    async handleAllExport() {\r\n      const res = await ExportEquipmentList({\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        Id: this.tableSelection.map((item) => item.ID).toString(),\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    // 下载模板\r\n    handleDownTemplate() {\r\n      EnvironmentImportTemplate({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data, '环境监测设备档案导出')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n      // if (this.tableSelection.length > 0) {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = false\r\n      // } else {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = true\r\n      // }\r\n    },\r\n    handleClick() {\r\n      this.currentComponent = DialogType\r\n      this.componentsConfig = {\r\n        disabled: false,\r\n        title: '新增'\r\n      }\r\n      this.dialogVisible = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: calc(100vh - 100px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}