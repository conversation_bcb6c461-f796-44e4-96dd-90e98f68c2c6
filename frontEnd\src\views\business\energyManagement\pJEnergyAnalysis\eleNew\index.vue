<template>
  <div class="eleBox">
    <el-row :gutter="16">
      <el-col :span="Is_Photovoltaic ? 8 : 8">
        <electricity-usage :is-photovoltaic="Is_Photovoltaic" />
      </el-col>
      <el-col v-if="Is_Photovoltaic" :span="8">
        <photovoltaic />
      </el-col>
      <el-col :span="Is_Photovoltaic ? 8 : 16">
        <generation :is-photovoltaic="Is_Photovoltaic" />
      </el-col>
      <el-col :span="16">
        <useEleKJ />
      </el-col>
      <!-- <el-col :span="4">
        <ZGFactory />
      </el-col> -->
      <el-col :span="8">
        <electricityLoss />
      </el-col>
      <!-- <el-col :span="16">
        <useEleGX />
      </el-col> -->
      <el-col :span="24">
        <equipmentUsage />
      </el-col>
    </el-row>
    <!-- <elePic /> -->
  </div>
</template>

<script>
import electricityUsage from './components/electricityUsage'
import photovoltaic from './components/photovoltaic'
import generation from './components/generation'
import useEleKJ from './components/useEleKJ'
// import ZGFactory from './components/ZGFactory'
import electricityLoss from './components/electricityLoss'
// import useEleGX from './components/useEleGX'
import equipmentUsage from './components/equipmentUsage'
// import elePic from './components/elePic'
import { GetPreferenceSettingValue } from '@/api/sys/system-setting'

export default {
  components: {
    electricityUsage,
    photovoltaic,
    generation,
    useEleKJ,
    // ZGFactory,
    electricityLoss,
    // useEleGX,
    equipmentUsage
    // elePic
  },
  data() {
    return {
      Is_Photovoltaic: false
    }
  },
  created() {

  },
  mounted() {
    GetPreferenceSettingValue({ Code: 'Is_Photovoltaic' }).then((res) => {
      if (res.IsSucceed) {
        this.Is_Photovoltaic = res.Data === 'true'
      }
    })
  },
  methods: {

  }
}
</script>
<style scoped lang='scss'>

</style>
