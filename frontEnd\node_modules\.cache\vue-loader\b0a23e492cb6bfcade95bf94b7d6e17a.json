{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\vehiclePeerRecord\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\vehiclePeerRecord\\index.vue", "mtime": 1755674552439}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/vehicleBarrier/vehiclePeerRecord", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"查看\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"600px\"\r\n    >\r\n      <el-image v-if=\"PassImg\" :src=\"PassImg\" class=\"imgwapper\" />\r\n      <div v-else class=\"empty-img\">暂无图片</div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport {\r\n  GetPassRecordList,\r\n  ExportPassRecordData,\r\n} from \"@/api/business/vehicleBarrier.js\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/mixins/export.js\";\r\nimport { parseTime } from \"@/utils/index.js\";\r\nexport default {\r\n  Name: \"vehiclePeerRecord\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  mixins: [exportInfo],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        VehicleOwnerName: \"\",\r\n        VehicleOwnerPhone: \"\",\r\n        Number: \"\",\r\n        StartTime: null,\r\n        EndTime: null,\r\n        EquipmentDate: [],\r\n      },\r\n      dialogVisible: false,\r\n      PassImg: \"\", // 图片\r\n      vehicleTypeOption: [], // 车辆类型\r\n      tableSelection: [],\r\n      selectIds: [],\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"VehicleOwnerName\", // 字段ID\r\n            label: \"车主姓名\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"VehicleOwnerPhone\",\r\n            label: \"车主联系方式\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"Number\",\r\n            label: \"车牌\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          // {\r\n          //   key: 'Number',\r\n          //   label: '通行方式',\r\n          //   type: 'input',\r\n          //   otherOptions: {\r\n          //     clearable: true\r\n          //   }\r\n          // },\r\n          {\r\n            key: \"EquipmentDate\",\r\n            label: \"时间\",\r\n            type: \"datePicker\",\r\n            otherOptions: {\r\n              type: \"datetimerange\",\r\n              rangeSeparator: \"至\",\r\n              startPlaceholder: \"开始日期\",\r\n              endPlaceholder: \"结束日期\",\r\n              clearable: true,\r\n              valueFormat: \"yyyy-MM-dd HH:mm\",\r\n            },\r\n            change: (e) => {\r\n              this.ruleForm.StartTime = e[0];\r\n              this.ruleForm.EndTime = e[1];\r\n            },\r\n          },\r\n        ],\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n\r\n                this.ExportData(\r\n                  {\r\n                    ...this.ruleForm,\r\n                    Ids: this.selectIds.toString(),\r\n                  },\r\n                  \"车辆通行记录\",\r\n                  ExportPassRecordData\r\n                );\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: \"100%\",\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: \"selection\",\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"车牌号码\",\r\n            key: \"Number\",\r\n          },\r\n          {\r\n            label: \"车辆品牌\",\r\n            key: \"Brand\",\r\n          },\r\n          {\r\n            label: \"车主姓名\",\r\n            key: \"VehicleOwnerName\",\r\n          },\r\n          {\r\n            label: \"车主联系方式\",\r\n            key: \"VehicleOwnerPhone\",\r\n          },\r\n          {\r\n            label: \"车辆分类\",\r\n            key: \"TypeName\",\r\n          },\r\n          {\r\n            label: \"车辆属性\",\r\n            key: \"Attr\",\r\n          },\r\n          {\r\n            label: \"过车方向\",\r\n            key: \"PassTypeName\",\r\n          },\r\n          {\r\n            label: \"时间\",\r\n            key: \"PassTime\",\r\n            render: (row) => {\r\n              return (\r\n                <span>\r\n                  {row.PassTime\r\n                    ? parseTime(\r\n                        new Date(row.PassTime),\r\n                        \"{y}-{m}-{d} {h}:{i}:{s}\"\r\n                      )\r\n                    : null}\r\n                </span>\r\n              );\r\n            },\r\n          },\r\n          {\r\n            label: \"出入口\",\r\n            key: \"GatewayId\",\r\n          },\r\n          {\r\n            label: \"通行方式\",\r\n            key: \"PassModeName\",\r\n          },\r\n          {\r\n            label: \"停车场\",\r\n            key: \"ParkingName\",\r\n          },\r\n          {\r\n            label: \"停车时长\",\r\n            key: \"ParkingTime\",\r\n            // render: row => {\r\n            //   return (<span>{row.ParkingTime ? parseTime(new Date(row.ParkingTime), '{y}-{m}-{d} {h}:{i}:{s}') : ''}</span>)\r\n            // }\r\n          },\r\n          {\r\n            label: \"原因\",\r\n            key: \"Reason\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleview(row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    // 测试\r\n    this.init();\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n    },\r\n    async init() {\r\n      await this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      if (this.ruleForm.EquipmentDate.length == 0) {\r\n        this.ruleForm.StartTime = null;\r\n        this.ruleForm.EndTime = null;\r\n      }\r\n      const res = await GetPassRecordList({\r\n        ParameterJson: [\r\n          {\r\n            Key: \"\",\r\n            Value: [null],\r\n            Type: \"\",\r\n            Filter_Type: \"\",\r\n          },\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => {\r\n          item.PassTypeName =\r\n            item.PassType == 1 ? \"驶入\" : item.PassType == 2 ? \"驶出\" : \"-\";\r\n\r\n          switch (Number(item.PassMode)) {\r\n            case 0:\r\n              item.PassModeName = \"其他\";\r\n              break;\r\n            case 1:\r\n              item.PassModeName = \"客户端开闸放行\";\r\n              break;\r\n            case 2:\r\n              item.PassModeName = \"遥控器开闸放行\";\r\n              break;\r\n            case 3:\r\n              item.PassModeName = \"场内扫码支付放行\";\r\n              break;\r\n            case 4:\r\n              item.PassModeName = \"车道静态码支付放行 \";\r\n              break;\r\n            case 5:\r\n              item.PassModeName = \"无感支付放行\";\r\n              break;\r\n            case 6:\r\n              item.PassModeName = \"自动放行\";\r\n              break;\r\n            default:\r\n              item.PassModeName = \"-\";\r\n              break;\r\n          }\r\n\r\n          return item;\r\n        });\r\n        this.customTableConfig.total = res.Data.Total;\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.onFresh();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.onFresh();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      const Ids = [];\r\n      this.tableSelection = selection;\r\n      this.tableSelection.forEach((item) => {\r\n        Ids.push(item.Id);\r\n      });\r\n      console.log(Ids);\r\n      this.selectIds = Ids;\r\n      console.log(this.tableSelection);\r\n      // if (this.tableSelection.length > 0) {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = false\r\n      // } else {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = true\r\n      // }\r\n    },\r\n    handleview(row) {\r\n      this.dialogVisible = true;\r\n      this.PassImg = row.PassImg;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n\r\n.imgwapper {\r\n  width: 100px;\r\n  height: 100px;\r\n}\r\n.empty-img {\r\n  text-align: center;\r\n}\r\n</style>\r\n"]}]}