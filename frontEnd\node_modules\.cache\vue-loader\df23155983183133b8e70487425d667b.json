{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomLayout\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomLayout\\index.vue", "mtime": 1755505758775}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICAvLyBsYXlvdXRDb25maWcKICBwcm9wczogewogICAgbGF5b3V0Q29uZmlnOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogKCkgPT4geyB9CiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHt9CiAgfSwKICBjb21wdXRlZDogewogICAgaXNTaG93U2VhcmNoRm9ybSgpIHsKICAgICAgcmV0dXJuICh0aGlzLmxheW91dE9iaiAmJiB0aGlzLmxheW91dE9iai5pc1Nob3dTZWFyY2hGb3JtKSB8fCB0cnVlCiAgICB9LAogICAgaXNTaG93TGF5b3V0Q2hhcnQoKSB7CiAgICAgIHJldHVybiAodGhpcy5sYXlvdXRPYmogJiYgdGhpcy5sYXlvdXRPYmouaXNTaG93TGF5b3V0Q2hhcnQpIHx8IGZhbHNlCiAgICB9LAogICAgbGF5b3V0T2JqKCkgewogICAgICByZXR1cm4gdGhpcy5sYXlvdXRDb25maWcKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/businessComponents/CustomLayout", "sourcesContent": ["<template>\n  <div class=\"CustomLayout\">\n    <div v-if=\"isShowSearchForm\" class=\"searchForm\">\n      <slot name=\"searchForm\" />\n    </div>\n    <div v-if=\"isShowLayoutChart\" class=\"layoutChart\">\n      <slot name=\"layoutChart\" />\n    </div>\n    <div class=\"layoutTable\">\n      <slot name=\"layoutTable\" />\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  // layoutConfig\n  props: {\n    layoutConfig: {\n      type: Object,\n      default: () => { }\n    }\n  },\n  data() {\n    return {}\n  },\n  computed: {\n    isShowSearchForm() {\n      return (this.layoutObj && this.layoutObj.isShowSearchForm) || true\n    },\n    isShowLayoutChart() {\n      return (this.layoutObj && this.layoutObj.isShowLayoutChart) || false\n    },\n    layoutObj() {\n      return this.layoutConfig\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.CustomLayout {\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  height: 100%;\n  .searchForm {\n    // margin: 15px 15px 0px 15px;\n    padding: 10px 15px;\n    background-color: white;\n  }\n  .layoutChart {\n    margin: 15px 15px 0px 15px;\n    padding: 10px 15px;\n    background-color: white;\n  }\n  .layoutTable {\n    flex: 1;\n    margin: 10px 0 0 0;\n    padding: 10px 15px;\n    background-color: white;\n  }\n}\n</style>\n"]}]}