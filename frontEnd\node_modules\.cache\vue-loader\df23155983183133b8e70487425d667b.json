{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomLayout\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomLayout\\index.vue", "mtime": 1755506092623}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIC8vIGxheW91dENvbmZpZw0KICBwcm9wczogew0KICAgIGxheW91dENvbmZpZzogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4geyB9LA0KICAgIH0sDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHt9Ow0KICB9LA0KICBjb21wdXRlZDogew0KICAgIGlzU2hvd1NlYXJjaEZvcm0oKSB7DQogICAgICByZXR1cm4gKHRoaXMubGF5b3V0T2JqICYmIHRoaXMubGF5b3V0T2JqLmlzU2hvd1NlYXJjaEZvcm0pIHx8IHRydWU7DQogICAgfSwNCiAgICBpc1Nob3dMYXlvdXRDaGFydCgpIHsNCiAgICAgIHJldHVybiAodGhpcy5sYXlvdXRPYmogJiYgdGhpcy5sYXlvdXRPYmouaXNTaG93TGF5b3V0Q2hhcnQpIHx8IGZhbHNlOw0KICAgIH0sDQogICAgbGF5b3V0T2JqKCkgew0KICAgICAgcmV0dXJuIHRoaXMubGF5b3V0Q29uZmlnOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/businessComponents/CustomLayout", "sourcesContent": ["<template>\r\n  <div class=\"CustomLayout\">\r\n    <div class=\"searchForm\" v-if=\"isShowSearchForm\">\r\n      <slot name=\"searchForm\" />\r\n    </div>\r\n    <div class=\"layoutChart\" v-if=\"isShowLayoutChart\">\r\n      <slot name=\"layoutChart\" />\r\n    </div>\r\n    <div class=\"layoutTable\">\r\n      <slot name=\"layoutTable\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  // layoutConfig\r\n  props: {\r\n    layoutConfig: {\r\n      type: Object,\r\n      default: () => { },\r\n    },\r\n  },\r\n  data() {\r\n    return {};\r\n  },\r\n  computed: {\r\n    isShowSearchForm() {\r\n      return (this.layoutObj && this.layoutObj.isShowSearchForm) || true;\r\n    },\r\n    isShowLayoutChart() {\r\n      return (this.layoutObj && this.layoutObj.isShowLayoutChart) || false;\r\n    },\r\n    layoutObj() {\r\n      return this.layoutConfig;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.CustomLayout {\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n  .searchForm {\r\n    margin: 15px 15px 0px 15px;\r\n    padding: 10px 15px;\r\n    background-color: white;\r\n  }\r\n  .layoutChart {\r\n    margin: 15px 15px 0px 15px;\r\n    padding: 10px 15px;\r\n    background-color: white;\r\n  }\r\n  .layoutTable {\r\n    flex: 1;\r\n    margin: 10px 15px;\r\n    padding: 10px 15px;\r\n    background-color: white;\r\n  }\r\n}\r\n</style>\r\n"]}]}