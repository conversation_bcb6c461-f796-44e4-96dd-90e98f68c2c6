{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\vehiclePeerRecord\\index.vue?vue&type=style&index=0&id=02984bc3&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\vehiclePeerRecord\\index.vue", "mtime": 1755674552439}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQpAaW1wb3J0ICJAL3ZpZXdzL2J1c2luZXNzL3ZlaGljbGVCYXJyaWVyL2luZGV4LnNjc3MiOw0KDQouaW1nd2FwcGVyIHsNCiAgd2lkdGg6IDEwMHB4Ow0KICBoZWlnaHQ6IDEwMHB4Ow0KfQ0KLmVtcHR5LWltZyB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsWA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/vehicleBarrier/vehiclePeerRecord", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"查看\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"600px\"\r\n    >\r\n      <el-image v-if=\"PassImg\" :src=\"PassImg\" class=\"imgwapper\" />\r\n      <div v-else class=\"empty-img\">暂无图片</div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport {\r\n  GetPassRecordList,\r\n  ExportPassRecordData,\r\n} from \"@/api/business/vehicleBarrier.js\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/mixins/export.js\";\r\nimport { parseTime } from \"@/utils/index.js\";\r\nexport default {\r\n  Name: \"vehiclePeerRecord\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  mixins: [exportInfo],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        VehicleOwnerName: \"\",\r\n        VehicleOwnerPhone: \"\",\r\n        Number: \"\",\r\n        StartTime: null,\r\n        EndTime: null,\r\n        EquipmentDate: [],\r\n      },\r\n      dialogVisible: false,\r\n      PassImg: \"\", // 图片\r\n      vehicleTypeOption: [], // 车辆类型\r\n      tableSelection: [],\r\n      selectIds: [],\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"VehicleOwnerName\", // 字段ID\r\n            label: \"车主姓名\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"VehicleOwnerPhone\",\r\n            label: \"车主联系方式\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"Number\",\r\n            label: \"车牌\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          // {\r\n          //   key: 'Number',\r\n          //   label: '通行方式',\r\n          //   type: 'input',\r\n          //   otherOptions: {\r\n          //     clearable: true\r\n          //   }\r\n          // },\r\n          {\r\n            key: \"EquipmentDate\",\r\n            label: \"时间\",\r\n            type: \"datePicker\",\r\n            otherOptions: {\r\n              type: \"datetimerange\",\r\n              rangeSeparator: \"至\",\r\n              startPlaceholder: \"开始日期\",\r\n              endPlaceholder: \"结束日期\",\r\n              clearable: true,\r\n              valueFormat: \"yyyy-MM-dd HH:mm\",\r\n            },\r\n            change: (e) => {\r\n              this.ruleForm.StartTime = e[0];\r\n              this.ruleForm.EndTime = e[1];\r\n            },\r\n          },\r\n        ],\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n\r\n                this.ExportData(\r\n                  {\r\n                    ...this.ruleForm,\r\n                    Ids: this.selectIds.toString(),\r\n                  },\r\n                  \"车辆通行记录\",\r\n                  ExportPassRecordData\r\n                );\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: \"100%\",\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: \"selection\",\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"车牌号码\",\r\n            key: \"Number\",\r\n          },\r\n          {\r\n            label: \"车辆品牌\",\r\n            key: \"Brand\",\r\n          },\r\n          {\r\n            label: \"车主姓名\",\r\n            key: \"VehicleOwnerName\",\r\n          },\r\n          {\r\n            label: \"车主联系方式\",\r\n            key: \"VehicleOwnerPhone\",\r\n          },\r\n          {\r\n            label: \"车辆分类\",\r\n            key: \"TypeName\",\r\n          },\r\n          {\r\n            label: \"车辆属性\",\r\n            key: \"Attr\",\r\n          },\r\n          {\r\n            label: \"过车方向\",\r\n            key: \"PassTypeName\",\r\n          },\r\n          {\r\n            label: \"时间\",\r\n            key: \"PassTime\",\r\n            render: (row) => {\r\n              return (\r\n                <span>\r\n                  {row.PassTime\r\n                    ? parseTime(\r\n                        new Date(row.PassTime),\r\n                        \"{y}-{m}-{d} {h}:{i}:{s}\"\r\n                      )\r\n                    : null}\r\n                </span>\r\n              );\r\n            },\r\n          },\r\n          {\r\n            label: \"出入口\",\r\n            key: \"GatewayId\",\r\n          },\r\n          {\r\n            label: \"通行方式\",\r\n            key: \"PassModeName\",\r\n          },\r\n          {\r\n            label: \"停车场\",\r\n            key: \"ParkingName\",\r\n          },\r\n          {\r\n            label: \"停车时长\",\r\n            key: \"ParkingTime\",\r\n            // render: row => {\r\n            //   return (<span>{row.ParkingTime ? parseTime(new Date(row.ParkingTime), '{y}-{m}-{d} {h}:{i}:{s}') : ''}</span>)\r\n            // }\r\n          },\r\n          {\r\n            label: \"原因\",\r\n            key: \"Reason\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleview(row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    // 测试\r\n    this.init();\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n    },\r\n    async init() {\r\n      await this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      if (this.ruleForm.EquipmentDate.length == 0) {\r\n        this.ruleForm.StartTime = null;\r\n        this.ruleForm.EndTime = null;\r\n      }\r\n      const res = await GetPassRecordList({\r\n        ParameterJson: [\r\n          {\r\n            Key: \"\",\r\n            Value: [null],\r\n            Type: \"\",\r\n            Filter_Type: \"\",\r\n          },\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => {\r\n          item.PassTypeName =\r\n            item.PassType == 1 ? \"驶入\" : item.PassType == 2 ? \"驶出\" : \"-\";\r\n\r\n          switch (Number(item.PassMode)) {\r\n            case 0:\r\n              item.PassModeName = \"其他\";\r\n              break;\r\n            case 1:\r\n              item.PassModeName = \"客户端开闸放行\";\r\n              break;\r\n            case 2:\r\n              item.PassModeName = \"遥控器开闸放行\";\r\n              break;\r\n            case 3:\r\n              item.PassModeName = \"场内扫码支付放行\";\r\n              break;\r\n            case 4:\r\n              item.PassModeName = \"车道静态码支付放行 \";\r\n              break;\r\n            case 5:\r\n              item.PassModeName = \"无感支付放行\";\r\n              break;\r\n            case 6:\r\n              item.PassModeName = \"自动放行\";\r\n              break;\r\n            default:\r\n              item.PassModeName = \"-\";\r\n              break;\r\n          }\r\n\r\n          return item;\r\n        });\r\n        this.customTableConfig.total = res.Data.Total;\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.onFresh();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.onFresh();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      const Ids = [];\r\n      this.tableSelection = selection;\r\n      this.tableSelection.forEach((item) => {\r\n        Ids.push(item.Id);\r\n      });\r\n      console.log(Ids);\r\n      this.selectIds = Ids;\r\n      console.log(this.tableSelection);\r\n      // if (this.tableSelection.length > 0) {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = false\r\n      // } else {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = true\r\n      // }\r\n    },\r\n    handleview(row) {\r\n      this.dialogVisible = true;\r\n      this.PassImg = row.PassImg;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n\r\n.imgwapper {\r\n  width: 100px;\r\n  height: 100px;\r\n}\r\n.empty-img {\r\n  text-align: center;\r\n}\r\n</style>\r\n"]}]}