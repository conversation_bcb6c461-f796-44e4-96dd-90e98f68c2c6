<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <div class="tableNotice">
          <span>设备基础音量：0为最大，数值越大音量越小</span>
          <span>数据更新时间： {{ updateDate }}</span>
        </div>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";

import dayjs from "dayjs";
import {
  GetEquipmentListSZCJ,
  PostEquipmentDataList,
} from "@/api/business/smartBroadcasting";
import { GetDictionaryDetailListByCode } from "@/api/sys";
export default {
  name: "",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
  },
  data() {
    return {
      currentComponent: null,
      componentsConfig: {
        Data: {},
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.initData();
        },
      },
      dialogVisible: false,
      dialogTitle: "编辑",
      tableSelection: [],
      updateDate: "",
      ruleForm: {
        DeviceName: "",
        DeviceStatus: "",
        Date: [],
        BeginCreateDate: null,
        EndCreateDate: null,
      },
      customForm: {
        formItems: [
          {
            key: "DeviceName",
            label: "设备名称",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
          {
            key: "DeviceStatus",
            label: "设备状态",
            type: "select",
            options: [],
            otherOptions: {
              clearable: true,
            },
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
          {
            key: "Date", // 字段ID
            label: "创建时间", // Form的label
            type: "datePicker", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              type: "daterange",
              disabled: false,
              placeholder: "请输入...",
            },
            change: (e) => {
              // change事件
              console.log(e);
              if (e && e.length > 0) {
                this.ruleForm.BeginCreateDate = dayjs(e[0]).format(
                  "YYYY-MM-DD"
                );
                this.ruleForm.EndCreateDate = dayjs(e[1]).format("YYYY-MM-DD");
              }
            },
          },
        ],
        rules: {},
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "更新数据",
              onclick: (item) => {
                console.log(item);
                this.handleResetData();
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            label: "设备名称",
            key: "DeviceName",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "设备基础音量",
            key: "VolumeValue",
            width: 140,
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "ID",
            key: "DeviceId",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "设备状态",
            key: "DeviceStatusDes",
            otherOptions: {
              align: "center",
            },
            render: (row) => {
              if (row.DeviceStatus == "ONLINE") {
                return this.$createElement(
                  "span",
                  {
                    style: {
                      color: "green",
                    },
                  },
                  row.DeviceStatusDes
                );
              } else if (row.DeviceStatus == "OFFLINE") {
                return this.$createElement(
                  "span",
                  {
                    style: {
                      color: "red",
                    },
                  },
                  row.DeviceStatusDes
                );
              } else if (row.DeviceStatus == "MALFUNCTION") {
                return this.$createElement(
                  "span",
                  {
                    style: {
                      color: "red",
                    },
                  },
                  row.DeviceStatusDes
                );
              } else if (row.DeviceStatus == "ERFISTERING") {
                return this.$createElement("span", {}, row.DeviceStatusDes);
              } else if (row.DeviceStatus == "HISTORY") {
                return this.$createElement("span", {}, row.DeviceStatusDes);
              }
              return this.$createElement("span", {}, row.DeviceStatusDes);
            },
          },
          {
            label: "创建时间",
            key: "CreateDate",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "创建来源",
            key: "DeviceSource",
            otherOptions: {
              align: "center",
            },
          },
        ],
        tableData: [],
        operateOptions: {
          align: "center",
          width: "180",
        },
        tableActions: [],
      },
    };
  },
  computed: {},
  created() {
    this.initData();
    this.getDictionaryDetailListByCode();
  },
  methods: {
    async handleResetData() {
      const res = await PostEquipmentDataList({});
      console.log(res, "res");
      if (res.IsSucceed) {
        this.initData();
        this.$message({
          type: "success",
          message: "更新成功",
        });
      }
    },

    searchForm(data) {
      console.log(data);
      this.customTableConfig.currentPage = 1

      this.initData();
    },
    resetForm() {
      this.ruleForm.BeginCreateDate = null;
      this.ruleForm.EndCreateDate = null;
      this.ruleForm.Date = null;
      this.initData();
    },

    async getDictionaryDetailListByCode() {
      const res = await GetDictionaryDetailListByCode({
        dictionaryCode: "BroadcastEquipmentStatus",
      });
      if (res.IsSucceed) {
        let result = res.Data || [];
        let deviceStatus = result.map((item) => ({
          value: item.Value,
          label: item.Display_Name,
        }));
        console.log(deviceStatus, "deviceStatus");
        this.customForm.formItems.find(
          (item) => item.key == "DeviceStatus"
        ).options = deviceStatus;
      }
    },

    async initData() {
      const res = await GetEquipmentListSZCJ({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data;
        this.customTableConfig.total = res.Data.TotalCount;
        if (res.Data.Data.length > 0) {
          this.updateDate = res.Data.Data[0].UpdateDate || "";
        }
      } else {
        this.$message.error(res.Message);
      }
    },

    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.initData();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
    handleEdit(row) {
      this.dialogVisible = true;
      this.componentsConfig.Data = row;
    },
  },
};
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
.tableNotice {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  color: rgba(34, 40, 52, 0.65);
  font-size: 14px;
}
</style>
