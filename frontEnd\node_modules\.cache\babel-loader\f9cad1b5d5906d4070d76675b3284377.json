{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJsafetyManagement\\equipmentManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJsafetyManagement\\equipmentManagement\\index.vue", "mtime": 1755674552406}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "getGridByCode", "GetEquipmentListSZCJ", "MonitoreImportTemplate", "ExportMonitoreEquipment", "LookVideo", "DelEquipment", "MonitoreEquipmentInfo", "DialogForm", "WatchVideoDialog", "DeviceInfoDialog", "ImportFile", "downloadFile", "GetDictionaryTreeDetailListByCode", "GetParkArea", "GetTreeAddress", "components", "mixins", "data", "_this", "ruleForm", "EquipmentName", "EquipmentNumber", "InstallSite", "EquipmentType", "customForm", "formItems", "key", "label", "type", "otherOptions", "clearable", "change", "e", "console", "log", "options", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "text", "onclick", "handleDownTemplate", "handleImport", "handleExport", "handleDelete", "handleEdit", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "tableData", "operateOptions", "align", "tableActionsWidth", "tableActions", "actionLabel", "index", "row", "handleDeviceInfo", "Id", "handleLookVideo", "dialogVisible", "dialogTitle", "currentComponent", "componentsConfig", "Data", "componentsFuns", "open", "close", "fetchData", "multipleSelection", "Park_Area", "created", "_this2", "then", "res", "mounted", "_this3", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getDictionaryDetailListByCode", "sent", "stop", "methods", "_this4", "_objectSpread", "Page", "PageSize", "IsSucceed", "TotalCount", "id", "_this5", "length", "$message", "warning", "map", "item", "join", "$confirm", "_ref", "_callee2", "_", "_callee2$", "_context2", "success", "error", "Message", "_x", "apply", "arguments", "catch", "undefined", "_defineProperty", "Monitore_Equipment_Number", "Monitore_Equipment_Name", "Monitore_Equipment_SN_Number", "Monitore_Equipment_Type", "Pisition", "Park_area", "Purpose_Catetory", "Scene", "Site", "submitForm", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "_this6", "Mainstream_Code", "Substream_Code", "Url", "_this7", "code", "_this8"], "sources": ["src/views/business/SZCJsafetyManagement/equipmentManagement/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 equipmentManagement\">\r\n    <custom-layout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"submitForm\"\r\n          @resetForm=\"fetchData\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </custom-layout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport getGridByCode from '../mixins/index'\r\nimport { GetEquipmentListSZCJ, MonitoreImportTemplate, ExportMonitoreEquipment, LookVideo, DelEquipment, MonitoreEquipmentInfo } from '@/api/business/safetyManagement'\r\nimport DialogForm from './components/dialogForm.vue'\r\nimport WatchVideoDialog from './components/watchVideoDialog.vue'\r\nimport DeviceInfoDialog from './components/deviceInfoDialog.vue'\r\nimport ImportFile from './components/importFile.vue'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport { GetDictionaryTreeDetailListByCode } from '@/api/sys'\r\nimport { GetParkArea, GetTreeAddress } from '@/api/business/energyManagement.js'\r\n\r\nexport default {\r\n  components: {\r\n    CustomLayout,\r\n    CustomTable,\r\n    CustomForm\r\n  },\r\n  mixins: [getGridByCode],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        EquipmentName: '',\r\n        EquipmentNumber: '',\r\n        InstallSite: '',\r\n        EquipmentType: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'EquipmentName',\r\n            label: '设备名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentNumber',\r\n            label: '设备编码',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'InstallSite',\r\n            label: '设备部署位置',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentType',\r\n            label: '设备类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '下载模板',\r\n              onclick: () => {\r\n                this.handleDownTemplate()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导入',\r\n              onclick: () => {\r\n                this.handleImport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导出',\r\n              onclick: () => {\r\n                this.handleExport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量删除',\r\n              onclick: () => {\r\n                this.handleDelete('batch')\r\n              }\r\n            },\r\n            {\r\n              text: '新增',\r\n              type: 'primary',\r\n              onclick: () => {\r\n                this.handleEdit()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [20, 40, 60, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 1000,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center'\r\n        },\r\n        tableActionsWidth: 260,\r\n        tableActions: [\r\n          {\r\n            actionLabel: '监控链接',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDeviceInfo(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete('single', row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '查看视频',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleLookVideo(row.Id)\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '新增',\r\n      currentComponent: null,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n        },\r\n        fetchData: () => {\r\n          this.fetchData()\r\n        }\r\n      },\r\n      multipleSelection: [],\r\n      Park_Area: ''\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchData()\r\n    this.getGridByCode('equipmentManagement')\r\n    GetParkArea().then(res => {\r\n      this.Park_Area = res.Data\r\n    })\r\n  },\r\n  async mounted() {\r\n    this.customForm.formItems[3].options = await this.getDictionaryDetailListByCode()\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      GetEquipmentListSZCJ({\r\n        ...this.ruleForm, Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n          this.customTableConfig.tableData = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    handleDelete(type, id) {\r\n      if (type == 'batch') {\r\n        if (this.multipleSelection.length == 0) {\r\n          this.$message.warning('请选择数据!')\r\n          return\r\n        } else {\r\n          id = this.multipleSelection.map(item => item.Id).join(',')\r\n        }\r\n      }\r\n      this.$confirm('确认删除？', {\r\n        type: 'warning'\r\n      })\r\n        .then(async(_) => {\r\n          await DelEquipment({ id }).then(res => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success('删除成功!')\r\n              this.fetchData()\r\n            } else {\r\n              this.$message.error(res.Message)\r\n            }\r\n          })\r\n        })\r\n        .catch((_) => { })\r\n    },\r\n    handleEdit(index, row, type = 'add') {\r\n      this.currentComponent = DialogForm\r\n      if (type === 'add') {\r\n        this.dialogTitle = '新增'\r\n        this.componentsConfig.Data = {\r\n          Monitore_Equipment_Number: '',\r\n          Monitore_Equipment_Name: '',\r\n          Monitore_Equipment_SN_Number: '',\r\n          Monitore_Equipment_Type: '',\r\n          Pisition: '',\r\n          Monitore_Equipment_Name: '',\r\n          Park_Area: this.Park_Area,\r\n          Site: '',\r\n          Address: '',\r\n          Brand: '',\r\n          Version: '',\r\n          Equipment_Purpose_Catetory: ''\r\n        }\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        row.Park_area = [row.Purpose_Catetory, row.Scene, row.Site]\r\n        this.componentsConfig.Data = { ...row, Park_Area: this.Park_Area }\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    submitForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData()\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.fetchData()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.customTableConfig.currentPage = val\r\n      this.fetchData()\r\n    },\r\n    handleSelectionChange(data) {\r\n      console.log(data)\r\n      this.multipleSelection = data\r\n    },\r\n    handleLookVideo(id) {\r\n      this.currentComponent = WatchVideoDialog\r\n      this.dialogTitle = '查看视频'\r\n      this.dialogVisible = true\r\n      this.componentsConfig.Data = id\r\n    },\r\n    handleDeviceInfo(id) {\r\n      this.currentComponent = DeviceInfoDialog\r\n      this.dialogTitle = '监控链接'\r\n      this.dialogVisible = true\r\n      MonitoreEquipmentInfo({ id }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.componentsConfig.Data = {\r\n            Mainstream_Code: res.Data.Mainstream_Code,\r\n            Substream_Code: res.Data.Substream_Code,\r\n            Url: res.Data.Url\r\n          }\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleDownTemplate() {\r\n      MonitoreImportTemplate({ code: 'equipmentManagement' }).then(res => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data, '安防监控设备管理导入模板')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleExport() {\r\n      let id = ''\r\n      if (this.multipleSelection.length == 0) {\r\n        this.$message.warning('请选择数据!')\r\n        return\r\n      } else {\r\n        id = this.multipleSelection.map(item => item.Id).join(',')\r\n      }\r\n      ExportMonitoreEquipment({\r\n        code: 'equipmentManagement',\r\n        id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('导出成功')\r\n          downloadFile(res.Data, '安防监控设备管理数据')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleImport() {\r\n      this.currentComponent = ImportFile\r\n      this.dialogTitle = '批量导入'\r\n      this.dialogVisible = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang='scss'>\r\n.equipmentManagement {\r\n  // height: calc(100vh - 90px);\r\n  // overflow: hidden;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,OAAAC,aAAA;AACA,SAAAC,oBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,SAAA,EAAAC,YAAA,EAAAC,qBAAA;AACA,OAAAC,UAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,UAAA;AACA,SAAAC,YAAA;AACA,SAAAC,iCAAA;AACA,SAAAC,WAAA,EAAAC,cAAA;AAEA;EACAC,UAAA;IACAlB,YAAA,EAAAA,YAAA;IACAC,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA;EACA;EACAiB,MAAA,GAAAhB,aAAA;EACAiB,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,QAAA;QACAC,aAAA;QACAC,eAAA;QACAC,WAAA;QACAC,aAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAO,OAAA;UACAN,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,EACA;QACAI,KAAA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAA;cACA1B,KAAA,CAAA2B,kBAAA;YACA;UACA,GACA;YACAF,IAAA;YACAC,OAAA,WAAAA,QAAA;cACA1B,KAAA,CAAA4B,YAAA;YACA;UACA,GACA;YACAH,IAAA;YACAC,OAAA,WAAAA,QAAA;cACA1B,KAAA,CAAA6B,YAAA;YACA;UACA,GACA;YACAJ,IAAA;YACAC,OAAA,WAAAA,QAAA;cACA1B,KAAA,CAAA8B,YAAA;YACA;UACA,GACA;YACAL,IAAA;YACAf,IAAA;YACAgB,OAAA,WAAAA,QAAA;cACA1B,KAAA,CAAA+B,UAAA;YACA;UACA;QAEA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA;QACAC,SAAA;QACAC,cAAA;UACAC,KAAA;QACA;QACAC,iBAAA;QACAC,YAAA,GACA;UACAC,WAAA;UACA/B,YAAA;YACAD,IAAA;UACA;UACAgB,OAAA,WAAAA,QAAAiB,KAAA,EAAAC,GAAA;YACA5C,KAAA,CAAA6C,gBAAA,CAAAD,GAAA,CAAAE,EAAA;UACA;QACA,GACA;UACAJ,WAAA;UACA/B,YAAA;YACAD,IAAA;UACA;UACAgB,OAAA,WAAAA,QAAAiB,KAAA,EAAAC,GAAA;YACA5C,KAAA,CAAA+B,UAAA,CAAAY,KAAA,EAAAC,GAAA;UACA;QACA,GACA;UACAF,WAAA;UACA/B,YAAA;YACAD,IAAA;UACA;UACAgB,OAAA,WAAAA,QAAAiB,KAAA,EAAAC,GAAA;YACA5C,KAAA,CAAA8B,YAAA,WAAAc,GAAA,CAAAE,EAAA;UACA;QACA,GACA;UACAJ,WAAA;UACA/B,YAAA;YACAD,IAAA;UACA;UACAgB,OAAA,WAAAA,QAAAiB,KAAA,EAAAC,GAAA;YACA5C,KAAA,CAAA+C,eAAA,CAAAH,GAAA,CAAAE,EAAA;UACA;QACA;MAEA;MACAE,aAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,gBAAA;QACAC,IAAA;MACA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAtD,KAAA,CAAAgD,aAAA;QACA;QACAO,KAAA,WAAAA,MAAA;UACAvD,KAAA,CAAAgD,aAAA;QACA;QACAQ,SAAA,WAAAA,UAAA;UACAxD,KAAA,CAAAwD,SAAA;QACA;MACA;MACAC,iBAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,KAAAJ,SAAA;IACA,KAAA1E,aAAA;IACAa,WAAA,GAAAkE,IAAA,WAAAC,GAAA;MACAF,MAAA,CAAAF,SAAA,GAAAI,GAAA,CAAAV,IAAA;IACA;EACA;EACAW,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAU,6BAAA;UAAA;YAAAV,MAAA,CAAA1D,UAAA,CAAAC,SAAA,IAAAU,OAAA,GAAAsD,QAAA,CAAAI,IAAA;UAAA;UAAA;YAAA,OAAAJ,QAAA,CAAAK,IAAA;QAAA;MAAA,GAAAR,OAAA;IAAA;EACA;EACAS,OAAA;IACArB,SAAA,WAAAA,UAAA;MAAA,IAAAsB,MAAA;MACA/F,oBAAA,CAAAgG,aAAA,CAAAA,aAAA,KACA,KAAA9E,QAAA;QAAA+E,IAAA,OAAA1D,iBAAA,CAAAW,WAAA;QACAgD,QAAA,OAAA3D,iBAAA,CAAAY;MAAA,EACA,EAAA2B,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAoB,SAAA;UACAJ,MAAA,CAAAxD,iBAAA,CAAAa,KAAA,GAAA2B,GAAA,CAAAV,IAAA,CAAA+B,UAAA;UACAL,MAAA,CAAAxD,iBAAA,CAAAe,SAAA,GAAAyB,GAAA,CAAAV,IAAA,CAAAA,IAAA;QACA;MACA;IACA;IACAtB,YAAA,WAAAA,aAAApB,IAAA,EAAA0E,EAAA;MAAA,IAAAC,MAAA;MACA,IAAA3E,IAAA;QACA,SAAA+C,iBAAA,CAAA6B,MAAA;UACA,KAAAC,QAAA,CAAAC,OAAA;UACA;QACA;UACAJ,EAAA,QAAA3B,iBAAA,CAAAgC,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAA5C,EAAA;UAAA,GAAA6C,IAAA;QACA;MACA;MACA,KAAAC,QAAA;QACAlF,IAAA;MACA,GACAmD,IAAA;QAAA,IAAAgC,IAAA,GAAA5B,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA2B,SAAAC,CAAA;UAAA,OAAA7B,mBAAA,GAAAG,IAAA,UAAA2B,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAzB,IAAA,GAAAyB,SAAA,CAAAxB,IAAA;cAAA;gBAAAwB,SAAA,CAAAxB,IAAA;gBAAA,OACAtF,YAAA;kBAAAiG,EAAA,EAAAA;gBAAA,GAAAvB,IAAA,WAAAC,GAAA;kBACA,IAAAA,GAAA,CAAAoB,SAAA;oBACAG,MAAA,CAAAE,QAAA,CAAAW,OAAA;oBACAb,MAAA,CAAA7B,SAAA;kBACA;oBACA6B,MAAA,CAAAE,QAAA,CAAAY,KAAA,CAAArC,GAAA,CAAAsC,OAAA;kBACA;gBACA;cAAA;cAAA;gBAAA,OAAAH,SAAA,CAAArB,IAAA;YAAA;UAAA,GAAAkB,QAAA;QAAA,CACA;QAAA,iBAAAO,EAAA;UAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;QAAA;MAAA,KACAC,KAAA,WAAAT,CAAA;IACA;IACAhE,UAAA,WAAAA,WAAAY,KAAA,EAAAC,GAAA;MAAA,IAAAlC,IAAA,GAAA6F,SAAA,CAAAjB,MAAA,QAAAiB,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,KAAArD,gBAAA,GAAA7D,UAAA;MACA,IAAAqB,IAAA;QACA,KAAAuC,WAAA;QACA,KAAAE,gBAAA,CAAAC,IAAA,GAAAsD,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA;UACAC,yBAAA;UACAC,uBAAA;UACAC,4BAAA;UACAC,uBAAA;UACAC,QAAA;QAAA,8BACA,kBACA,KAAArD,SAAA,WACA,gBACA,cACA,gBACA,mCACA,GACA;MACA,WAAAhD,IAAA;QACA,KAAAuC,WAAA;QACAL,GAAA,CAAAoE,SAAA,IAAApE,GAAA,CAAAqE,gBAAA,EAAArE,GAAA,CAAAsE,KAAA,EAAAtE,GAAA,CAAAuE,IAAA;QACA,KAAAhE,gBAAA,CAAAC,IAAA,GAAA2B,aAAA,CAAAA,aAAA,KAAAnC,GAAA;UAAAc,SAAA,OAAAA;QAAA;MACA;MACA,KAAAV,aAAA;IACA;IACAoE,UAAA,WAAAA,WAAArH,IAAA;MACA,KAAAuB,iBAAA,CAAAW,WAAA;MACA,KAAAuB,SAAA;IACA;IACA6D,gBAAA,WAAAA,iBAAAC,GAAA;MACAvG,OAAA,CAAAC,GAAA,iBAAAuG,MAAA,CAAAD,GAAA;MACA,KAAAhG,iBAAA,CAAAY,QAAA,GAAAoF,GAAA;MACA,KAAA9D,SAAA;IACA;IACAgE,mBAAA,WAAAA,oBAAAF,GAAA;MACA,KAAAhG,iBAAA,CAAAW,WAAA,GAAAqF,GAAA;MACA,KAAA9D,SAAA;IACA;IACAiE,qBAAA,WAAAA,sBAAA1H,IAAA;MACAgB,OAAA,CAAAC,GAAA,CAAAjB,IAAA;MACA,KAAA0D,iBAAA,GAAA1D,IAAA;IACA;IACAgD,eAAA,WAAAA,gBAAAqC,EAAA;MACA,KAAAlC,gBAAA,GAAA5D,gBAAA;MACA,KAAA2D,WAAA;MACA,KAAAD,aAAA;MACA,KAAAG,gBAAA,CAAAC,IAAA,GAAAgC,EAAA;IACA;IACAvC,gBAAA,WAAAA,iBAAAuC,EAAA;MAAA,IAAAsC,MAAA;MACA,KAAAxE,gBAAA,GAAA3D,gBAAA;MACA,KAAA0D,WAAA;MACA,KAAAD,aAAA;MACA5D,qBAAA;QAAAgG,EAAA,EAAAA;MAAA,GAAAvB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAoB,SAAA;UACAwC,MAAA,CAAAvE,gBAAA,CAAAC,IAAA;YACAuE,eAAA,EAAA7D,GAAA,CAAAV,IAAA,CAAAuE,eAAA;YACAC,cAAA,EAAA9D,GAAA,CAAAV,IAAA,CAAAwE,cAAA;YACAC,GAAA,EAAA/D,GAAA,CAAAV,IAAA,CAAAyE;UACA;QACA;UACAH,MAAA,CAAAnC,QAAA,CAAAY,KAAA,CAAArC,GAAA,CAAAsC,OAAA;QACA;MACA;IACA;IACAzE,kBAAA,WAAAA,mBAAA;MAAA,IAAAmG,MAAA;MACA9I,sBAAA;QAAA+I,IAAA;MAAA,GAAAlE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAoB,SAAA;UACAzF,YAAA,CAAAqE,GAAA,CAAAV,IAAA;QACA;UACA0E,MAAA,CAAAvC,QAAA,CAAAY,KAAA,CAAArC,GAAA,CAAAsC,OAAA;QACA;MACA;IACA;IACAvE,YAAA,WAAAA,aAAA;MAAA,IAAAmG,MAAA;MACA,IAAA5C,EAAA;MACA,SAAA3B,iBAAA,CAAA6B,MAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA;MACA;QACAJ,EAAA,QAAA3B,iBAAA,CAAAgC,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAA5C,EAAA;QAAA,GAAA6C,IAAA;MACA;MACA1G,uBAAA;QACA8I,IAAA;QACA3C,EAAA,EAAAA;MACA,GAAAvB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAoB,SAAA;UACA8C,MAAA,CAAAzC,QAAA,CAAAW,OAAA;UACAzG,YAAA,CAAAqE,GAAA,CAAAV,IAAA;QACA;UACA4E,MAAA,CAAAzC,QAAA,CAAAY,KAAA,CAAArC,GAAA,CAAAsC,OAAA;QACA;MACA;IACA;IACAxE,YAAA,WAAAA,aAAA;MACA,KAAAsB,gBAAA,GAAA1D,UAAA;MACA,KAAAyD,WAAA;MACA,KAAAD,aAAA;IACA;EACA;AACA", "ignoreList": []}]}