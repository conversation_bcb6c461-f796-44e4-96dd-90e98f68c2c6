{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\intelligentControl\\components\\layoutCard.vue?vue&type=style&index=0&id=fd133c62&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\intelligentControl\\components\\layoutCard.vue", "mtime": 1755506761043}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoubGF5b3V0IHsKICBoZWlnaHQ6IDEzMHB4OwogIGZvbnQtZmFtaWx5OiBQaW5nRmFuZyBTQywgUGluZ0ZhbmcgU0M7CiAgZGlzcGxheTogZmxleDsKICAuY2FyZCB7CiAgICBmbGV4OiAxOwogICAgZmxvYXQ6IGxlZnQ7CiAgICBtYXJnaW46IDBweCAxMnB4OyAvKiDmt7vliqDpl7Tot50gKi8KICAgIGRpc3BsYXk6IGZsZXg7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsgLyog5rC05bmz5bGF5LitICovCiAgICBhbGlnbi1pdGVtczogY2VudGVyOyAvKiDlnoLnm7TlsYXkuK0gKi8KICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7IC8qIOiuvue9ruiDjOaZr+minOiJsiAqLwoKICAgIC5jYXJkbGVmdCB7CiAgICAgIGZsb2F0OiBsZWZ0OwogICAgfQogICAgLmNhcmRyaWdodCB7CiAgICAgIGZsb2F0OiBsZWZ0OwogICAgICBmb250LXN0eWxlOiBub3JtYWw7CiAgICAgIC50aXRsZSB7CiAgICAgICAgaGVpZ2h0OiAyNXB4OwogICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICAgIGZvbnQtc2l6ZTogMXJlbTsKICAgICAgICBjb2xvcjogIzMzMzMzMzsKICAgICAgICBsaW5lLWhlaWdodDogMjVweDsKICAgICAgfQogICAgICAubnVtIHsKICAgICAgICBoZWlnaHQ6IDMzcHg7CiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsKICAgICAgICBmb250LXNpemU6IDEuMnJlbTsKICAgICAgICBsaW5lLWhlaWdodDogMzNweDsKICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["layoutCard.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "layoutCard.vue", "sourceRoot": "src/views/business/intelligentControl/components", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <div\n      class=\"card\"\n      v-for=\"(item, index) in cardList\"\n      :key=\"index\"\n      :style=\"{\n        backgroundColor: item.backgroundcolor,\n        height: '100px',\n        borderRadius: '8px',\n        margin: item.backgroundcolor ? '' : 'auto'\n      }\"\n    >\n      <div class=\"cardleft\" :style=\"item.backgroundcolor ? 'margin-right: 16px;' : ''\">\n        <img :src=\"item.cardimg\" />\n      </div>\n      <div class=\"cardright\">\n        <div class=\"title\">{{ item.cardtitle }}</div>\n        <div class=\"num\" :style=\"{ color: item.numcolor }\" v-if=\"item.cardtitle\">\n          <count-to\n            :startVal=\"0\"\n            :endVal=\"item.cardNum\"\n            :duration=\"50\"\n            :decimals=\"item.unit === '件' ? 0 : 3\"\n          ></count-to>\n          <span>{{ item.unit }}</span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport countTo from 'vue-count-to'\nexport default {\n  components: { countTo },\n  props: {\n    cardList: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {}\n  },\n  computed: {}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.layout {\n  height: 130px;\n  font-family: PingFang SC, PingFang SC;\n  display: flex;\n  .card {\n    flex: 1;\n    float: left;\n    margin: 0px 12px; /* 添加间距 */\n    display: flex;\n    justify-content: center; /* 水平居中 */\n    align-items: center; /* 垂直居中 */\n    background-color: #ffffff; /* 设置背景颜色 */\n\n    .cardleft {\n      float: left;\n    }\n    .cardright {\n      float: left;\n      font-style: normal;\n      .title {\n        height: 25px;\n        font-weight: bold;\n        font-size: 1rem;\n        color: #333333;\n        line-height: 25px;\n      }\n      .num {\n        height: 33px;\n        font-weight: 600;\n        font-size: 1.2rem;\n        line-height: 33px;\n      }\n    }\n  }\n}\n</style>\n"]}]}