{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\intelligentControl\\components\\layoutCard.vue?vue&type=style&index=0&id=fd133c62&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\intelligentControl\\components\\layoutCard.vue", "mtime": 1755674552427}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmxheW91dCB7DQogIGhlaWdodDogMTMwcHg7DQogIGZvbnQtZmFtaWx5OiBQaW5nRmFuZyBTQywgUGluZ0ZhbmcgU0M7DQogIGRpc3BsYXk6IGZsZXg7DQogIC5jYXJkIHsNCiAgICBmbGV4OiAxOw0KICAgIGZsb2F0OiBsZWZ0Ow0KICAgIG1hcmdpbjogMHB4IDEycHg7IC8qIOa3u+WKoOmXtOi3nSAqLw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7IC8qIOawtOW5s+WxheS4rSAqLw0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7IC8qIOWeguebtOWxheS4rSAqLw0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7IC8qIOiuvue9ruiDjOaZr+minOiJsiAqLw0KDQogICAgLmNhcmRsZWZ0IHsNCiAgICAgIGZsb2F0OiBsZWZ0Ow0KICAgIH0NCiAgICAuY2FyZHJpZ2h0IHsNCiAgICAgIGZsb2F0OiBsZWZ0Ow0KICAgICAgZm9udC1zdHlsZTogbm9ybWFsOw0KICAgICAgLnRpdGxlIHsNCiAgICAgICAgaGVpZ2h0OiAyNXB4Ow0KICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgZm9udC1zaXplOiAxcmVtOw0KICAgICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDI1cHg7DQogICAgICB9DQogICAgICAubnVtIHsNCiAgICAgICAgaGVpZ2h0OiAzM3B4Ow0KICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgICBmb250LXNpemU6IDEuMnJlbTsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDMzcHg7DQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["layoutCard.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "layoutCard.vue", "sourceRoot": "src/views/business/intelligentControl/components", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div\r\n      class=\"card\"\r\n      v-for=\"(item, index) in cardList\"\r\n      :key=\"index\"\r\n      :style=\"{\r\n        backgroundColor: item.backgroundcolor,\r\n        height: '100px',\r\n        borderRadius: '8px',\r\n        margin: item.backgroundcolor ? '' : 'auto'\r\n      }\"\r\n    >\r\n      <div class=\"cardleft\" :style=\"item.backgroundcolor ? 'margin-right: 16px;' : ''\">\r\n        <img :src=\"item.cardimg\" />\r\n      </div>\r\n      <div class=\"cardright\">\r\n        <div class=\"title\">{{ item.cardtitle }}</div>\r\n        <div class=\"num\" :style=\"{ color: item.numcolor }\" v-if=\"item.cardtitle\">\r\n          <count-to\r\n            :startVal=\"0\"\r\n            :endVal=\"item.cardNum\"\r\n            :duration=\"50\"\r\n            :decimals=\"item.unit === '件' ? 0 : 3\"\r\n          ></count-to>\r\n          <span>{{ item.unit }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport countTo from 'vue-count-to'\r\nexport default {\r\n  components: { countTo },\r\n  props: {\r\n    cardList: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {}\r\n  },\r\n  computed: {}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: 130px;\r\n  font-family: PingFang SC, PingFang SC;\r\n  display: flex;\r\n  .card {\r\n    flex: 1;\r\n    float: left;\r\n    margin: 0px 12px; /* 添加间距 */\r\n    display: flex;\r\n    justify-content: center; /* 水平居中 */\r\n    align-items: center; /* 垂直居中 */\r\n    background-color: #ffffff; /* 设置背景颜色 */\r\n\r\n    .cardleft {\r\n      float: left;\r\n    }\r\n    .cardright {\r\n      float: left;\r\n      font-style: normal;\r\n      .title {\r\n        height: 25px;\r\n        font-weight: bold;\r\n        font-size: 1rem;\r\n        color: #333333;\r\n        line-height: 25px;\r\n      }\r\n      .num {\r\n        height: 33px;\r\n        font-weight: 600;\r\n        font-size: 1.2rem;\r\n        line-height: 33px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}