{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\hazardousChemicals\\alarmInformation\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\hazardousChemicals\\alarmInformation\\index.vue", "mtime": 1755506574331}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBDdXN0b21MYXlvdXQgZnJvbSAiQC9idXNpbmVzc0NvbXBvbmVudHMvQ3VzdG9tTGF5b3V0L2luZGV4LnZ1ZSI7CmltcG9ydCBDdXN0b21UYWJsZSBmcm9tICJAL2J1c2luZXNzQ29tcG9uZW50cy9DdXN0b21UYWJsZS9pbmRleC52dWUiOwppbXBvcnQgQ3VzdG9tRm9ybSBmcm9tICJAL2J1c2luZXNzQ29tcG9uZW50cy9DdXN0b21Gb3JtL2luZGV4LnZ1ZSI7CgppbXBvcnQgRGlhbG9nRm9ybSBmcm9tICIuL2RpYWxvZ0Zvcm0udnVlIjsKaW1wb3J0IERpYWxvZ0Zvcm1Mb29rIGZyb20gIi4vZGlhbG9nRm9ybUxvb2sudnVlIjsKCmltcG9ydCB7IGRvd25sb2FkRmlsZSB9IGZyb20gIkAvdXRpbHMvZG93bmxvYWRGaWxlIjsKLy8gaW1wb3J0IEN1c3RvbVRpdGxlIGZyb20gJ0AvYnVzaW5lc3NDb21wb25lbnRzL0N1c3RvbVRpdGxlL2luZGV4LnZ1ZScKLy8gaW1wb3J0IEN1c3RvbUJ1dHRvbiBmcm9tICdAL2J1c2luZXNzQ29tcG9uZW50cy9DdXN0b21CdXR0b24vaW5kZXgudnVlJwppbXBvcnQgeyBkZXZpY2VUeXBlTWl4aW5zIH0gZnJvbSAiLi4vLi4vbWl4aW5zL2RldmljZVR5cGUuanMiOwppbXBvcnQgewogIEdldFdhcm5pbmdMaXN0LAogIEdldFdhcm5pbmdUeXBlLAogIEV4cG9ydFdhcm5pbmcsCiAgVXBkYXRlV2FybmluZ1N0YXR1cywKfSBmcm9tICJAL2FwaS9idXNpbmVzcy9oYXphcmRvdXNDaGVtaWNhbHMiOwovLyBpbXBvcnQgKiBhcyBtb21lbnQgZnJvbSAnbW9tZW50JwppbXBvcnQgZGF5anMgZnJvbSAiZGF5anMiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICIiLAogIGNvbXBvbmVudHM6IHsKICAgIEN1c3RvbVRhYmxlLAogICAgLy8gQ3VzdG9tQnV0dG9uLAogICAgLy8gQ3VzdG9tVGl0bGUsCiAgICBDdXN0b21Gb3JtLAogICAgQ3VzdG9tTGF5b3V0LAogIH0sCiAgbWl4aW5zOiBbZGV2aWNlVHlwZU1peGluc10sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGN1cnJlbnRDb21wb25lbnQ6IERpYWxvZ0Zvcm0sCiAgICAgIGNvbXBvbmVudHNDb25maWc6IHt9LAogICAgICBjb21wb25lbnRzRnVuczogewogICAgICAgIG9wZW46ICgpID0+IHsKICAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgICAgfSwKICAgICAgICBjbG9zZTogKCkgPT4gewogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICB0aGlzLm9uRnJlc2goKTsKICAgICAgICB9LAogICAgICB9LAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nVGl0bGU6ICIiLAogICAgICB0YWJsZVNlbGVjdGlvbjogW10sCgogICAgICBydWxlRm9ybTogewogICAgICAgIENvbnRlbnQ6ICIiLAogICAgICAgIEVxdFR5cGU6ICIiLAogICAgICAgIFBvc2l0aW9uOiAiIiwKICAgICAgfSwKICAgICAgY3VzdG9tRm9ybTogewogICAgICAgIGZvcm1JdGVtczogWwogICAgICAgICAgewogICAgICAgICAgICBrZXk6ICJDb250ZW50IiwgLy8g5a2X5q61SUQKICAgICAgICAgICAgbGFiZWw6ICIiLCAvLyBGb3Jt55qEbGFiZWwKICAgICAgICAgICAgdHlwZTogImlucHV0IiwgLy8gaW5wdXQ65pmu6YCa6L6T5YWl5qGGLHRleHRhcmVhOuaWh+acrO+/vT9zZWxlY3Q65LiL5ouJ6YCJ5oup77+9P2RhdGVwaWNrZXI65pel5pyf6YCJ5oup77+9PwogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsKICAgICAgICAgICAgICAvLyDpmaTkuoZtb2RlbOS7peWklueahOWFtuS7lueahOWPguaVsCzlhbfkvZPor7flj4LogINlbGVtZW505paH5qGjCiAgICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlLAogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6L6T5YWl6K6+5aSH57yW5Y+35oiW5ZCN56ew6L+b6KGM5pCc77+9PywKICAgICAgICAgICAgfSwKICAgICAgICAgICAgd2lkdGg6ICIyNDBweCIsCiAgICAgICAgICAgIGNoYW5nZTogKGUpID0+IHsKICAgICAgICAgICAgICAvLyBjaGFuZ2Xkuovku7YKICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgICAgICAgfSwKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGtleTogIkVxdFR5cGUiLAogICAgICAgICAgICBsYWJlbDogIuiuvuWkh+exu+WeiyIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAoKICAgICAgICAgICAgb3RoZXJPcHRpb25zOiB7CiAgICAgICAgICAgICAgLy8g6Zmk5LqGbW9kZWzku6XlpJbnmoTlhbbku5bnmoTlj4LmlbAs5YW35L2T6K+35Y+C6ICDZWxlbWVudOaWh+ahowogICAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZSwKICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogIuivt+mAieaLqeiuvuWkh+exu+WeiyIsCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIG9wdGlvbnM6IFtdLAogICAgICAgICAgICBjaGFuZ2U6IChlKSA9PiB7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coZSk7CiAgICAgICAgICAgIH0sCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBrZXk6ICJXYXJuaW5nVHlwZSIsCiAgICAgICAgICAgIGxhYmVsOiAi5ZGK6K2m57G75Z6LIiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsCgogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsKICAgICAgICAgICAgICAvLyDpmaTkuoZtb2RlbOS7peWklueahOWFtuS7lueahOWPguaVsCzlhbfkvZPor7flj4LogINlbGVtZW505paH5qGjCiAgICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlLAogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6K+36YCJ5oup5ZGK6K2m57G75Z6LIiwKICAgICAgICAgICAgfSwKICAgICAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgICAgIGNoYW5nZTogKGUpID0+IHsKICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgICAgICAgfSwKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGtleTogIkhhbmRsZV9TdGF0dXMiLAogICAgICAgICAgICBsYWJlbDogIuWRiuitpueKtu+/vT8sCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsKICAgICAgICAgICAgICAvLyDpmaTkuoZtb2RlbOS7peWklueahOWFtuS7lueahOWPguaVsCzlhbfkvZPor7flj4LogINlbGVtZW505paH5qGjCiAgICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlLAogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6K+36YCJ5oup5ZGK6K2m54q277+9PywKICAgICAgICAgICAgfSwKICAgICAgICAgICAgb3B0aW9uczogWwogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIGxhYmVsOiAi5ZGK6K2m77+9PywKICAgICAgICAgICAgICAgIHZhbHVlOiAxLAogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgbGFiZWw6ICLlt7LlhbPvv70/LAogICAgICAgICAgICAgICAgdmFsdWU6IDIsCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAvLyB7CiAgICAgICAgICAgICAgLy8gICBsYWJlbDogJ+W3suWkhO+/vT8sCiAgICAgICAgICAgICAgLy8gICB2YWx1ZTogMwogICAgICAgICAgICAgIC8vIH0KICAgICAgICAgICAgXSwKICAgICAgICAgICAgY2hhbmdlOiAoZSkgPT4gewogICAgICAgICAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgICAgICAgICB9LAogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAga2V5OiAiUG9zaXRpb24iLCAvLyDlrZfmrrVJRAogICAgICAgICAgICBsYWJlbDogIuWuieijheS9jee9riIsIC8vIEZvcm3nmoRsYWJlbAogICAgICAgICAgICB0eXBlOiAiaW5wdXQiLCAvLyBpbnB1dDrmma7pgJrovpPlhaXmoYYsdGV4dGFyZWE65paH5pys77+9P3NlbGVjdDrkuIvmi4npgInmi6nvv70/ZGF0ZXBpY2tlcjrml6XmnJ/pgInmi6nvv70/CiAgICAgICAgICAgIG90aGVyT3B0aW9uczogewogICAgICAgICAgICAgIC8vIOmZpOS6hm1vZGVs5Lul5aSW55qE5YW25LuW55qE5Y+C5pWwLOWFt+S9k+ivt+WPguiAg2VsZW1lbnTmlofmoaMKICAgICAgICAgICAgICBjbGVhcmFibGU6IHRydWUsCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICLor7fovpPlhaXlronoo4XkvY3vv70/LAogICAgICAgICAgICB9LAogICAgICAgICAgICBjaGFuZ2U6IChlKSA9PiB7CiAgICAgICAgICAgICAgLy8gY2hhbmdl5LqL5Lu2CiAgICAgICAgICAgICAgY29uc29sZS5sb2coZSk7CiAgICAgICAgICAgIH0sCiAgICAgICAgICB9LAogICAgICAgIF0sCiAgICAgICAgcnVsZXM6IHsKICAgICAgICAgIC8vIOivt+WPgueFp2VsZW1lbnRGb3JtIHJ1bGVzCiAgICAgICAgfSwKICAgICAgICBjdXN0b21Gb3JtQnV0dG9uczogewogICAgICAgICAgc3VibWl0TmFtZTogIuafpeivoiIsCiAgICAgICAgICByZXNldE5hbWU6ICLph43nva4iLAogICAgICAgIH0sCiAgICAgIH0sCiAgICAgIGN1c3RvbVRhYmxlQ29uZmlnOiB7CiAgICAgICAgYnV0dG9uQ29uZmlnOiB7CiAgICAgICAgICBidXR0b25MaXN0OiBbCiAgICAgICAgICAgIC8vIHsKICAgICAgICAgICAgLy8gICB0ZXh0OiAn5paw5aKeJywKICAgICAgICAgICAgLy8gICByb3VuZDogZmFsc2UsIC8vIOaYr+WQpuWchuinkgogICAgICAgICAgICAvLyAgIHBsYWluOiBmYWxzZSwgLy8g5piv5ZCm5py057SgCiAgICAgICAgICAgIC8vICAgY2lyY2xlOiBmYWxzZSwgLy8g5piv5ZCm5ZyG5b2iCiAgICAgICAgICAgIC8vICAgbG9hZGluZzogZmFsc2UsIC8vIOaYr+WQpuWKoOi9ve+/vT8gICAgICAgICAgICAvLyAgIGRpc2FibGVkOiBmYWxzZSwgLy8g5piv5ZCm56aB55SoCiAgICAgICAgICAgIC8vICAgaWNvbjogJycsIC8vICDlm77moIcKICAgICAgICAgICAgLy8gICBhdXRvZm9jdXM6IGZhbHNlLCAvLyDmmK/lkKbogZrnhKYKICAgICAgICAgICAgLy8gICB0eXBlOiAncHJpbWFyeScsIC8vIHByaW1hcnkgLyBzdWNjZXNzIC8gd2FybmluZyAvIGRhbmdlciAvIGluZm8gLyB0ZXh0CiAgICAgICAgICAgIC8vICAgc2l6ZTogJ3NtYWxsJywgLy8gbWVkaXVtIC8gc21hbGwgLyBtaW5pCiAgICAgICAgICAgIC8vICAgb25jbGljazogKGl0ZW0pID0+IHsKICAgICAgICAgICAgLy8gICAgIGNvbnNvbGUubG9nKGl0ZW0pCiAgICAgICAgICAgIC8vICAgICB0aGlzLmhhbmRsZUNyZWF0ZSgpCiAgICAgICAgICAgIC8vICAgfQogICAgICAgICAgICAvLyB9LAogICAgICAgICAgICAvLyB7CiAgICAgICAgICAgIC8vICAgdGV4dDogJ+WvvOWHuicsCiAgICAgICAgICAgIC8vICAgb25jbGljazogKGl0ZW0pID0+IHsKICAgICAgICAgICAgLy8gICAgIGNvbnNvbGUubG9nKGl0ZW0pCiAgICAgICAgICAgIC8vICAgICB0aGlzLmhhbmRsZUV4cG9ydCgpCiAgICAgICAgICAgIC8vICAgfQogICAgICAgICAgICAvLyB9LAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgdGV4dDogIuaJuemHj+WvvOWHuiIsCiAgICAgICAgICAgICAgb25jbGljazogKGl0ZW0pID0+IHsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGl0ZW0pOwogICAgICAgICAgICAgICAgdGhpcy5oYW5kbGVBbGxFeHBvcnQoKTsKICAgICAgICAgICAgICB9LAogICAgICAgICAgICB9LAogICAgICAgICAgXSwKICAgICAgICB9LAogICAgICAgIC8vIOihqOagvAogICAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAgIHBhZ2VTaXplT3B0aW9uczogWzEwLCAyMCwgNTAsIDgwXSwKICAgICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgICBwYWdlU2l6ZTogMjAsCiAgICAgICAgdG90YWw6IDAsCiAgICAgICAgdGFibGVDb2x1bW5zOiBbCiAgICAgICAgICAvLyB7CiAgICAgICAgICAvLyAgIHdpZHRoOiA1MCwKICAgICAgICAgIC8vICAgb3RoZXJPcHRpb25zOiB7CiAgICAgICAgICAvLyAgICAgdHlwZTogJ3NlbGVjdGlvbicsCiAgICAgICAgICAvLyAgICAgYWxpZ246ICdjZW50ZXInCiAgICAgICAgICAvLyAgIH0KICAgICAgICAgIC8vIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHdpZHRoOiA2MCwKICAgICAgICAgICAgbGFiZWw6ICLluo/lj7ciLAogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsKICAgICAgICAgICAgICB0eXBlOiAiaW5kZXgiLAogICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwKICAgICAgICAgICAgfSwgLy8ga2V5CiAgICAgICAgICAgIC8vIG90aGVyT3B0aW9uczogewogICAgICAgICAgICAvLyAgIHdpZHRoOiAxODAsIC8vIOWuveW6pgogICAgICAgICAgICAvLyAgIGZpeGVkOiAnbGVmdCcsIC8vIGxlZnQsIHJpZ2h0CiAgICAgICAgICAgIC8vICAgYWxpZ246ICdjZW50ZXInIC8vCWxlZnQvY2VudGVyL3JpZ2h0CiAgICAgICAgICAgIC8vIH0KICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5ZGK6K2m5pe26Ze0IiwKICAgICAgICAgICAga2V5OiAiVGltZSIsCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogewogICAgICAgICAgICAgIGZpeGVkOiAibGVmdCIsCiAgICAgICAgICAgIH0sCiAgICAgICAgICB9LAogICAgICAgICAgLy8gewogICAgICAgICAgLy8gICBsYWJlbDogIuWRiuitpuS6i+S7tuWQjeensCIsCiAgICAgICAgICAvLyAgIGtleTogIkVxdE5hbWVUeXBlIiwKICAgICAgICAgIC8vICAgb3RoZXJPcHRpb25zOiB7CiAgICAgICAgICAvLyAgICAgZml4ZWQ6ICJsZWZ0IiwKICAgICAgICAgIC8vICAgfSwKICAgICAgICAgIC8vIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5ZGK6K2m57yW5Y+3IiwKICAgICAgICAgICAga2V5OiAiV0lkIiwKICAgICAgICAgICAgd2lkdGg6IDE2MCwKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5ZGK6K2m6K6+5aSH57yW5Y+3IiwKICAgICAgICAgICAga2V5OiAiRUlkIiwKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5ZGK6K2m5LqL5Lu25ZCN56ewIiwKICAgICAgICAgICAga2V5OiAiRW52RXZlbnROYW1lIiwKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5ZGK6K2m57G75Z6LIiwKICAgICAgICAgICAga2V5OiAiVHlwZSIsCiAgICAgICAgICAgIHdpZHRoOiA5MCwKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi6Kem5Y+R77+9PywKICAgICAgICAgICAga2V5OiAiVHJpZ2dlckl0ZW0iLAogICAgICAgICAgICB3aWR0aDogOTAsCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuinpuWPke+/vT8sCiAgICAgICAgICAgIGtleTogIldhcm5pbmdWYWx1ZSIsCiAgICAgICAgICAgIHdpZHRoOiA5MCwKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5a6J6KOF5L2N572uIiwKICAgICAgICAgICAga2V5OiAiUG9zaXRpb24iLAogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLlkYrorabnirbvv70/LAogICAgICAgICAgICBrZXk6ICJIYW5kbGVTdGF0dXNTdHIiLAogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLmk43kvZzvv70/LAogICAgICAgICAgICBrZXk6ICJIYW5kbGVyX1VzZXJOYW1lIiwKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5pON5L2c5pe26Ze0IiwKICAgICAgICAgICAga2V5OiAiSGFuZGxlX1RpbWUiLAogICAgICAgICAgfSwKICAgICAgICBdLAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgb3BlcmF0ZU9wdGlvbnM6IHsKICAgICAgICAgIHdpZHRoOiAyMDAsCiAgICAgICAgfSwKICAgICAgICB0YWJsZUFjdGlvbnM6IFsKICAgICAgICAgIC8vIHsKICAgICAgICAgIC8vICAgYWN0aW9uTGFiZWw6ICflhbPpl60nLAogICAgICAgICAgLy8gICBvdGhlck9wdGlvbnM6IHsKICAgICAgICAgIC8vICAgICB0eXBlOiAndGV4dCcKICAgICAgICAgIC8vICAgfSwKICAgICAgICAgIC8vICAgb25jbGljazogKGluZGV4LCByb3cpID0+IHsKICAgICAgICAgIC8vICAgICB0aGlzLmhhbmRlbENsb3NlKHJvdykKICAgICAgICAgIC8vICAgfQogICAgICAgICAgLy8gfSwKICAgICAgICAgIHsKICAgICAgICAgICAgYWN0aW9uTGFiZWw6ICLmn6XnnIsiLAogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsKICAgICAgICAgICAgICB0eXBlOiAidGV4dCIsCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIG9uY2xpY2s6IChpbmRleCwgcm93KSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5oYW5kbGVFZGl0KGluZGV4LCByb3csICJ2aWV3Iik7CiAgICAgICAgICAgIH0sCiAgICAgICAgICB9LAogICAgICAgICAgLy8gewogICAgICAgICAgLy8gICBhY3Rpb25MYWJlbDogJ+e8lui+kScsCiAgICAgICAgICAvLyAgIG90aGVyT3B0aW9uczogewogICAgICAgICAgLy8gICAgIHR5cGU6ICd0ZXh0JwogICAgICAgICAgLy8gICB9LAogICAgICAgICAgLy8gICBvbmNsaWNrOiAoaW5kZXgsIHJvdykgPT4gewogICAgICAgICAgLy8gICAgIHRoaXMuaGFuZGxlRWRpdChpbmRleCwgcm93LCAnZWRpdCcpCiAgICAgICAgICAvLyAgIH0KICAgICAgICAgIC8vIH0sCiAgICAgICAgICAvLyB7CiAgICAgICAgICAvLyAgIGFjdGlvbkxhYmVsOiAn5Yig6ZmkJywKICAgICAgICAgIC8vICAgb3RoZXJPcHRpb25zOiB7CiAgICAgICAgICAvLyAgICAgdHlwZTogJ3RleHQnCiAgICAgICAgICAvLyAgIH0sCiAgICAgICAgICAvLyAgIG9uY2xpY2s6IChpbmRleCwgcm93KSA9PiB7CiAgICAgICAgICAvLyAgICAgdGhpcy5oYW5kbGVEZWxldGUoaW5kZXgsIHJvdykKICAgICAgICAgIC8vICAgfQogICAgICAgICAgLy8gfQogICAgICAgIF0sCiAgICAgIH0sCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHt9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLmluaXQoKTsKICAgIHRoaXMuaW5pdERldmljZVR5cGUoIkVxdFR5cGUiLCAiSGF6Y2hlbUVxdFR5cGUiKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIHNlYXJjaEZvcm0oZGF0YSkgewogICAgICBjb25zb2xlLmxvZyhkYXRhKTsKICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5jdXJyZW50UGFnZSA9IDE7CiAgICAgIHRoaXMub25GcmVzaCgpOwogICAgfSwKICAgIHJlc2V0Rm9ybSgpIHsKICAgICAgdGhpcy5vbkZyZXNoKCk7CiAgICB9LAogICAgb25GcmVzaCgpIHsKICAgICAgdGhpcy5HZXRXYXJuaW5nTGlzdCgpOwogICAgfSwKICAgIGluaXQoKSB7CiAgICAgIHRoaXMuR2V0V2FybmluZ0xpc3QoKTsKICAgICAgdGhpcy5HZXRXYXJuaW5nVHlwZSgpOwogICAgfSwKICAgIGFzeW5jIEdldFdhcm5pbmdMaXN0KCkgewogICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmxvYWRpbmcgPSB0cnVlOwogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRXYXJuaW5nTGlzdCh7CiAgICAgICAgUGFyYW1ldGVySnNvbjogWwogICAgICAgICAgewogICAgICAgICAgICBLZXk6ICIiLAogICAgICAgICAgICBWYWx1ZTogW251bGxdLAogICAgICAgICAgICBUeXBlOiAiIiwKICAgICAgICAgICAgRmlsdGVyX1R5cGU6ICIiLAogICAgICAgICAgfSwKICAgICAgICBdLAogICAgICAgIFBhZ2U6IHRoaXMuY3VzdG9tVGFibGVDb25maWcuY3VycmVudFBhZ2UsCiAgICAgICAgUGFnZVNpemU6IHRoaXMuY3VzdG9tVGFibGVDb25maWcucGFnZVNpemUsCgogICAgICAgIFNvcnROYW1lOiAiIiwKICAgICAgICBTb3J0T3JkZXI6ICIiLAogICAgICAgIFNlYXJjaDogIiIsCiAgICAgICAgQ29udGVudDogIiIsCiAgICAgICAgRXF0VHlwZTogIiIsCiAgICAgICAgUG9zaXRpb246ICIiLAogICAgICAgIElzQWxsOiB0cnVlLAogICAgICAgIC4uLnRoaXMucnVsZUZvcm0sCiAgICAgIH0pOwogICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLnRhYmxlRGF0YSA9IHJlcy5EYXRhLkRhdGEubWFwKChpdGVtKSA9PiAoewogICAgICAgICAgLi4uaXRlbSwKICAgICAgICAgIFRpbWU6IGRheWpzKGl0ZW0uVGltZSkuZm9ybWF0KCJZWVlZLU1NLUREIEhIOm1tOnNzIiksCiAgICAgICAgfSkpOwogICAgICAgIGNvbnNvbGUubG9nKHJlcyk7CiAgICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy50b3RhbCA9IHJlcy5EYXRhLlRvdGFsQ291bnQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuTWVzc2FnZSk7CiAgICAgIH0KICAgIH0sCiAgICBhc3luYyBHZXRXYXJuaW5nVHlwZSgpIHsKICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0V2FybmluZ1R5cGUoe30pOwogICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgIGNvbnNvbGUubG9nKHJlcywgInJlcyIpOwogICAgICAgIHRoaXMuY3VzdG9tRm9ybS5mb3JtSXRlbXMuZmluZCgKICAgICAgICAgIChpdGVtLCBpbmRleCkgPT4gaXRlbS5rZXkgPT09ICJXYXJuaW5nVHlwZSIKICAgICAgICApLm9wdGlvbnMgPSByZXMuRGF0YS5tYXAoKGl0ZW0pID0+ICh7CiAgICAgICAgICBsYWJlbDogaXRlbS5UeXBlLAogICAgICAgICAgdmFsdWU6IGl0ZW0uVHlwZSwKICAgICAgICB9KSk7CiAgICAgICAgLy8gY29uc29sZS5sb2cocmVzKQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UpOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlQ3JlYXRlKCkgewogICAgICB0aGlzLmRpYWxvZ1RpdGxlID0gIuaWsOWiniI7CiAgICAgIHRoaXMuY29tcG9uZW50c0NvbmZpZyA9IHsKICAgICAgICBkaXNhYmxlZDogZmFsc2UsCiAgICAgICAgdGl0bGU6ICLmlrDlop4iLAogICAgICB9OwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKICAgIC8vIGhhbmRsZURlbGV0ZShpbmRleCwgcm93KSB7CiAgICAvLyAgIGNvbnNvbGUubG9nKGluZGV4LCByb3cpCiAgICAvLyAgIGNvbnNvbGUubG9nKHRoaXMpCiAgICAvLyAgIHRoaXMuJGNvbmZpcm0oJ+ivpeaTjeS9nOWwhuWcqOebkea1i+iuvuWkh+aho+ahiOS4reWIoOmZpOivpeiuvuWkh+S/oe+/vT/or7fnoa7orqTmmK/lkKbliKDvv70/JywgJ+WIoOmZpCcsIHsKICAgIC8vICAgICB0eXBlOiAnZXJyb3InCiAgICAvLyAgIH0pCiAgICAvLyAgICAgLnRoZW4oYXN5bmMoXykgPT4gewogICAgLy8gICAgICAgY29uc3QgcmVzID0gYXdhaXQgRGVsZXRlRXF1aXBtZW50KHsKICAgIC8vICAgICAgICAgSURzOiBbcm93LklEXQogICAgLy8gICAgICAgfSkKICAgIC8vICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAvLyAgICAgICAgIHRoaXMuaW5pdCgpCiAgICAvLyAgICAgICB9IGVsc2UgewogICAgLy8gICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKQogICAgLy8gICAgICAgfQogICAgLy8gICAgIH0pCiAgICAvLyAgICAgLmNhdGNoKChfKSA9PiB7fSkKICAgIC8vIH0sCiAgICBoYW5kbGVFZGl0KGluZGV4LCByb3csIHR5cGUpIHsKICAgICAgY29uc29sZS5sb2coaW5kZXgsIHJvdywgdHlwZSk7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIGlmICh0eXBlID09PSAidmlldyIpIHsKICAgICAgICB0aGlzLmRpYWxvZ1RpdGxlID0gIuafpeeciyI7CiAgICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gRGlhbG9nRm9ybTsKICAgICAgICB0aGlzLmNvbXBvbmVudHNDb25maWcgPSB7CiAgICAgICAgICBJRDogcm93LklELAogICAgICAgICAgZGlzYWJsZWQ6IHRydWUsCiAgICAgICAgICB0aXRsZTogIuafpeeciyIsCiAgICAgICAgICByb3c6IHJvdywKICAgICAgICB9OwogICAgICB9CiAgICAgIC8vIGVsc2UgaWYgKHR5cGUgPT09ICdlZGl0JykgewogICAgICAvLyAgIHRoaXMuZGlhbG9nVGl0bGUgPSAn57yW6L6RJwogICAgICAvLyAgIHRoaXMuY29tcG9uZW50c0NvbmZpZyA9IHsKICAgICAgLy8gICAgIElEOiByb3cuSUQsCiAgICAgIC8vICAgICBkaXNhYmxlZDogZmFsc2UsCiAgICAgIC8vICAgICB0aXRsZTogJ+e8lui+kScKICAgICAgLy8gICB9CiAgICAgIC8vIH0KICAgIH0sCiAgICAvLyBhc3luYyBoYW5kbGVFeHBvcnQoKSB7CiAgICAvLyAgIGNvbnNvbGUubG9nKHRoaXMucnVsZUZvcm0pCiAgICAvLyAgIGNvbnN0IHJlcyA9IGF3YWl0IEV4cG9ydFdhcm5pbmcoewogICAgLy8gICAgIENvbnRlbnQ6ICcnLAogICAgLy8gICAgIEVxdFR5cGU6ICcnLAogICAgLy8gICAgIFBvc2l0aW9uOiAnJywKICAgIC8vICAgICBJc0FsbDogZmFsc2UsCiAgICAvLyAgICAgSWRzOiB0aGlzLnRhYmxlU2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS5JRCksCiAgICAvLyAgICAgLi4udGhpcy5ydWxlRm9ybQogICAgLy8gICB9KQogICAgLy8gICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgLy8gICAgIGNvbnNvbGUubG9nKHJlcykKICAgIC8vICAgICBkb3dubG9hZEZpbGUocmVzLkRhdGEsICcyMScpCiAgICAvLyAgIH0gZWxzZSB7CiAgICAvLyAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuTWVzc2FnZSkKICAgIC8vICAgfQogICAgLy8gfSwKICAgIGFzeW5jIGhhbmRsZUFsbEV4cG9ydCgpIHsKICAgICAgY29uc3QgcmVzID0gYXdhaXQgRXhwb3J0V2FybmluZyh7CiAgICAgICAgQ29udGVudDogIiIsCiAgICAgICAgRXF0VHlwZTogIiIsCiAgICAgICAgUG9zaXRpb246ICIiLAogICAgICAgIElzQWxsOiB0cnVlLAogICAgICAgIElkczogW10sCiAgICAgICAgLi4udGhpcy5ydWxlRm9ybSwKICAgICAgfSk7CiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgY29uc29sZS5sb2cocmVzKTsKICAgICAgICBkb3dubG9hZEZpbGUocmVzLkRhdGEsICIyMSIpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UpOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlU2l6ZUNoYW5nZSh2YWwpIHsKICAgICAgY29uc29sZS5sb2coYOavj+mhtSAke3ZhbH0g5p2hYCk7CiAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcucGFnZVNpemUgPSB2YWw7CiAgICAgIHRoaXMuaW5pdCgpOwogICAgfSwKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIGNvbnNvbGUubG9nKGDlvZPliY3vv70/ICR7dmFsfWApOwogICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmN1cnJlbnRQYWdlID0gdmFsOwogICAgICB0aGlzLmluaXQoKTsKICAgIH0sCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMudGFibGVTZWxlY3Rpb24gPSBzZWxlY3Rpb247CiAgICB9LAogICAgaGFuZGVsQ2xvc2Uocm93KSB7CiAgICAgIGlmIChyb3cuSGFuZGxlU3RhdHVzU3RyID09ICLlhbPpl60iKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fli7/ph43lpI3mk43kvZwiKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBVcGRhdGVXYXJuaW5nU3RhdHVzKHsgaWQ6IHJvdy5JZCwgd2lkOiByb3cuV0lkLCBTdGF0dXNFbnVtOiAyIH0pLnRoZW4oCiAgICAgICAgICAocmVzKSA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLmluaXQoKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgICk7CiAgICAgIH0KICAgIH0sCiAgfSwKfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/business/hazardousChemicals/alarmInformation", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n          ><template #customBtn=\"{ slotScope }\"\n            ><el-button\n              v-if=\"slotScope.Handle_Status == 1\"\n              type=\"text\"\n              @click=\"handelClose(slotScope)\"\n              >关闭</el-button\n            ></template\n          ></CustomTable\n        >\n      </template>\n    </CustomLayout>\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <component\n        :is=\"currentComponent\"\n        v-if=\"dialogVisible\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n    /></el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\n\nimport DialogForm from \"./dialogForm.vue\";\nimport DialogFormLook from \"./dialogFormLook.vue\";\n\nimport { downloadFile } from \"@/utils/downloadFile\";\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\nimport { deviceTypeMixins } from \"../../mixins/deviceType.js\";\nimport {\n  GetWarningList,\n  GetWarningType,\n  ExportWarning,\n  UpdateWarningStatus,\n} from \"@/api/business/hazardousChemicals\";\n// import * as moment from 'moment'\nimport dayjs from \"dayjs\";\n\nexport default {\n  name: \"\",\n  components: {\n    CustomTable,\n    // CustomButton,\n    // CustomTitle,\n    CustomForm,\n    CustomLayout,\n  },\n  mixins: [deviceTypeMixins],\n  data() {\n    return {\n      currentComponent: DialogForm,\n      componentsConfig: {},\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true;\n        },\n        close: () => {\n          this.dialogVisible = false;\n          this.onFresh();\n        },\n      },\n      dialogVisible: false,\n      dialogTitle: \"\",\n      tableSelection: [],\n\n      ruleForm: {\n        Content: \"\",\n        EqtType: \"\",\n        Position: \"\",\n      },\n      customForm: {\n        formItems: [\n          {\n            key: \"Content\", // 字段ID\n            label: \"\", // Form的label\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"输入设备编号或名称进行搜�?,\n            },\n            width: \"240px\",\n            change: (e) => {\n              // change事件\n              console.log(e);\n            },\n          },\n          {\n            key: \"EqtType\",\n            label: \"设备类型\",\n            type: \"select\",\n\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"请选择设备类型\",\n            },\n            options: [],\n            change: (e) => {\n              console.log(e);\n            },\n          },\n          {\n            key: \"WarningType\",\n            label: \"告警类型\",\n            type: \"select\",\n\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"请选择告警类型\",\n            },\n            options: [],\n            change: (e) => {\n              console.log(e);\n            },\n          },\n          {\n            key: \"Handle_Status\",\n            label: \"告警状�?,\n            type: \"select\",\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"请选择告警状�?,\n            },\n            options: [\n              {\n                label: \"告警�?,\n                value: 1,\n              },\n              {\n                label: \"已关�?,\n                value: 2,\n              },\n              // {\n              //   label: '已处�?,\n              //   value: 3\n              // }\n            ],\n            change: (e) => {\n              console.log(e);\n            },\n          },\n          {\n            key: \"Position\", // 字段ID\n            label: \"安装位置\", // Form的label\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"请输入安装位�?,\n            },\n            change: (e) => {\n              // change事件\n              console.log(e);\n            },\n          },\n        ],\n        rules: {\n          // 请参照elementForm rules\n        },\n        customFormButtons: {\n          submitName: \"查询\",\n          resetName: \"重置\",\n        },\n      },\n      customTableConfig: {\n        buttonConfig: {\n          buttonList: [\n            // {\n            //   text: '新增',\n            //   round: false, // 是否圆角\n            //   plain: false, // 是否朴素\n            //   circle: false, // 是否圆形\n            //   loading: false, // 是否加载�?            //   disabled: false, // 是否禁用\n            //   icon: '', //  图标\n            //   autofocus: false, // 是否聚焦\n            //   type: 'primary', // primary / success / warning / danger / info / text\n            //   size: 'small', // medium / small / mini\n            //   onclick: (item) => {\n            //     console.log(item)\n            //     this.handleCreate()\n            //   }\n            // },\n            // {\n            //   text: '导出',\n            //   onclick: (item) => {\n            //     console.log(item)\n            //     this.handleExport()\n            //   }\n            // },\n            {\n              text: \"批量导出\",\n              onclick: (item) => {\n                console.log(item);\n                this.handleAllExport();\n              },\n            },\n          ],\n        },\n        // 表格\n        loading: false,\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        tableColumns: [\n          // {\n          //   width: 50,\n          //   otherOptions: {\n          //     type: 'selection',\n          //     align: 'center'\n          //   }\n          // },\n          {\n            width: 60,\n            label: \"序号\",\n            otherOptions: {\n              type: \"index\",\n              align: \"center\",\n            }, // key\n            // otherOptions: {\n            //   width: 180, // 宽度\n            //   fixed: 'left', // left, right\n            //   align: 'center' //\tleft/center/right\n            // }\n          },\n          {\n            label: \"告警时间\",\n            key: \"Time\",\n            otherOptions: {\n              fixed: \"left\",\n            },\n          },\n          // {\n          //   label: \"告警事件名称\",\n          //   key: \"EqtNameType\",\n          //   otherOptions: {\n          //     fixed: \"left\",\n          //   },\n          // },\n          {\n            label: \"告警编号\",\n            key: \"WId\",\n            width: 160,\n          },\n          {\n            label: \"告警设备编号\",\n            key: \"EId\",\n          },\n          {\n            label: \"告警事件名称\",\n            key: \"EnvEventName\",\n          },\n          {\n            label: \"告警类型\",\n            key: \"Type\",\n            width: 90,\n          },\n          {\n            label: \"触发�?,\n            key: \"TriggerItem\",\n            width: 90,\n          },\n          {\n            label: \"触发�?,\n            key: \"WarningValue\",\n            width: 90,\n          },\n          {\n            label: \"安装位置\",\n            key: \"Position\",\n          },\n          {\n            label: \"告警状�?,\n            key: \"HandleStatusStr\",\n          },\n          {\n            label: \"操作�?,\n            key: \"Handler_UserName\",\n          },\n          {\n            label: \"操作时间\",\n            key: \"Handle_Time\",\n          },\n        ],\n        tableData: [],\n        operateOptions: {\n          width: 200,\n        },\n        tableActions: [\n          // {\n          //   actionLabel: '关闭',\n          //   otherOptions: {\n          //     type: 'text'\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handelClose(row)\n          //   }\n          // },\n          {\n            actionLabel: \"查看\",\n            otherOptions: {\n              type: \"text\",\n            },\n            onclick: (index, row) => {\n              this.handleEdit(index, row, \"view\");\n            },\n          },\n          // {\n          //   actionLabel: '编辑',\n          //   otherOptions: {\n          //     type: 'text'\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handleEdit(index, row, 'edit')\n          //   }\n          // },\n          // {\n          //   actionLabel: '删除',\n          //   otherOptions: {\n          //     type: 'text'\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handleDelete(index, row)\n          //   }\n          // }\n        ],\n      },\n    };\n  },\n  computed: {},\n  mounted() {\n    this.init();\n    this.initDeviceType(\"EqtType\", \"HazchemEqtType\");\n  },\n  methods: {\n    searchForm(data) {\n      console.log(data);\n      this.customTableConfig.currentPage = 1;\n      this.onFresh();\n    },\n    resetForm() {\n      this.onFresh();\n    },\n    onFresh() {\n      this.GetWarningList();\n    },\n    init() {\n      this.GetWarningList();\n      this.GetWarningType();\n    },\n    async GetWarningList() {\n      this.customTableConfig.loading = true;\n      const res = await GetWarningList({\n        ParameterJson: [\n          {\n            Key: \"\",\n            Value: [null],\n            Type: \"\",\n            Filter_Type: \"\",\n          },\n        ],\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n\n        SortName: \"\",\n        SortOrder: \"\",\n        Search: \"\",\n        Content: \"\",\n        EqtType: \"\",\n        Position: \"\",\n        IsAll: true,\n        ...this.ruleForm,\n      });\n      this.customTableConfig.loading = false;\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\n          ...item,\n          Time: dayjs(item.Time).format(\"YYYY-MM-DD HH:mm:ss\"),\n        }));\n        console.log(res);\n        this.customTableConfig.total = res.Data.TotalCount;\n      } else {\n        this.$message.error(res.Message);\n      }\n    },\n    async GetWarningType() {\n      const res = await GetWarningType({});\n      if (res.IsSucceed) {\n        console.log(res, \"res\");\n        this.customForm.formItems.find(\n          (item, index) => item.key === \"WarningType\"\n        ).options = res.Data.map((item) => ({\n          label: item.Type,\n          value: item.Type,\n        }));\n        // console.log(res)\n      } else {\n        this.$message.error(res.Message);\n      }\n    },\n    handleCreate() {\n      this.dialogTitle = \"新增\";\n      this.componentsConfig = {\n        disabled: false,\n        title: \"新增\",\n      };\n      this.dialogVisible = true;\n    },\n    // handleDelete(index, row) {\n    //   console.log(index, row)\n    //   console.log(this)\n    //   this.$confirm('该操作将在监测设备档案中删除该设备信�?请确认是否删�?', '删除', {\n    //     type: 'error'\n    //   })\n    //     .then(async(_) => {\n    //       const res = await DeleteEquipment({\n    //         IDs: [row.ID]\n    //       })\n    //       if (res.IsSucceed) {\n    //         this.init()\n    //       } else {\n    //         this.$message.error(res.Message)\n    //       }\n    //     })\n    //     .catch((_) => {})\n    // },\n    handleEdit(index, row, type) {\n      console.log(index, row, type);\n      this.dialogVisible = true;\n      if (type === \"view\") {\n        this.dialogTitle = \"查看\";\n        this.currentComponent = DialogForm;\n        this.componentsConfig = {\n          ID: row.ID,\n          disabled: true,\n          title: \"查看\",\n          row: row,\n        };\n      }\n      // else if (type === 'edit') {\n      //   this.dialogTitle = '编辑'\n      //   this.componentsConfig = {\n      //     ID: row.ID,\n      //     disabled: false,\n      //     title: '编辑'\n      //   }\n      // }\n    },\n    // async handleExport() {\n    //   console.log(this.ruleForm)\n    //   const res = await ExportWarning({\n    //     Content: '',\n    //     EqtType: '',\n    //     Position: '',\n    //     IsAll: false,\n    //     Ids: this.tableSelection.map((item) => item.ID),\n    //     ...this.ruleForm\n    //   })\n    //   if (res.IsSucceed) {\n    //     console.log(res)\n    //     downloadFile(res.Data, '21')\n    //   } else {\n    //     this.$message.error(res.Message)\n    //   }\n    // },\n    async handleAllExport() {\n      const res = await ExportWarning({\n        Content: \"\",\n        EqtType: \"\",\n        Position: \"\",\n        IsAll: true,\n        Ids: [],\n        ...this.ruleForm,\n      });\n      if (res.IsSucceed) {\n        console.log(res);\n        downloadFile(res.Data, \"21\");\n      } else {\n        this.$message.error(res.Message);\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`);\n      this.customTableConfig.pageSize = val;\n      this.init();\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`);\n      this.customTableConfig.currentPage = val;\n      this.init();\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection;\n    },\n    handelClose(row) {\n      if (row.HandleStatusStr == \"关闭\") {\n        this.$message.warning(\"请勿重复操作\");\n      } else {\n        UpdateWarningStatus({ id: row.Id, wid: row.WId, StatusEnum: 2 }).then(\n          (res) => {\n            if (res.IsSucceed) {\n              this.$message.success(\"操作成功\");\n              this.init();\n            } else {\n              this.$message.error(res.Message);\n            }\n          }\n        );\n      }\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.layout {\n  height: calc(100vh - 90px);\n  overflow: auto;\n}\n</style>\n"]}]}