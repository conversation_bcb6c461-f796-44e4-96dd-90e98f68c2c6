<template>
  <div class="app-container abs100">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="报警播放音频设置" name="1">
          <component :is="currentComponent" />
        </el-tab-pane>
        <el-tab-pane label="报警广播区域设置" name="2">
          <component :is="currentComponent" />
        </el-tab-pane>
      </el-tabs>
  </div>
</template>

<script>
import broadcastAreaSettings from "./broadcastAreaSettings.vue";
import playAudioSettings from "./playAudioSettings.vue";

export default {
  name: "",
  data() {
    return {
      activeName: "1",
      currentComponent: playAudioSettings,
    };
  },
  computed: {},
  created() {},

  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
      if (tab.index == "0") {
        this.currentComponent = playAudioSettings;
      } else if (tab.index == "1") {
        this.currentComponent = broadcastAreaSettings;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.layout{
  margin: 15px 15px;
}
.mt20 {
  margin-top: 10px;
}
</style>
