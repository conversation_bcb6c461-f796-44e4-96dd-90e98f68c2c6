
<template>
  <div class="customForm">
    <el-form
      ref="ruleForm"
      :label-width="labelWidth"
      :model="formData"
      :inline="inline"
      :rules="rules"
    >
      <el-form-item
        v-for="(item, index) in customFormItemsArr"
        :key="index"
        :prop="item.key"
        :label="item.label"
      >
        <template v-if="item.type === 'input' || item.type === 'textarea'">
          <el-input
            v-model="formData[`${item.key}`]"
            :style="{ width: item.width ? item.width : '100%' }"
            :placeholder="
              !item.otherOptions || !item.otherOptions.placeholder
                ? `请输入${item.label}`
                : item.otherOptions.placeholder
            "
            v-bind="item.otherOptions"
            @change="
              (e) => {
                item.change && item.change(e);
              }
            "
            @input="
              (e) => {
                item.input && item.input(e);
              }
            "
          >
            <el-button
              v-if="item.showSlotAppend"
              slot="append"
              style="min-width: 64px"
            >{{ item.showSlotAppendText || "-" }}</el-button>
          </el-input>
        </template>
        <template v-if="item.type === 'inputNumber' ">
          <el-input-number
            v-model="formData[`${item.key}`]"
            :style="{ width: item.width ? item.width : '100%' }"
            :placeholder="
              !item.otherOptions || !item.otherOptions.placeholder
                ? `请输入${item.label}`
                : item.otherOptions.placeholder
            "
            v-bind="item.otherOptions"
            @change="
              (e) => {
                item.change && item.change(e);
              }
            "
            @input="
              (e) => {
                item.input && item.input(e);
              }
            "
          />
        </template>
        <template v-if="item.type === 'select'">
          <el-select
            v-model="formData[`${item.key}`]"
            :placeholder="`请选择${item.label}`"
            v-bind="item.otherOptions"
            :style="{ width: item.width ? item.width : '100%' }"
            @change="
              (e) => {
                item.change(e);
              }
            "
          >
            <el-option
              v-for="option in item.options"
              :key="option.value"
              :value="option.value"
              :label="option.label"
              :disabled="option.disabled"
            />
          </el-select>
        </template>
        <template v-if="item.type === 'switch'">
          <el-switch
            v-model="formData[`${item.key}`]"
            v-bind="item.otherOptions"
            @change="
              (e) => {
                item.change(e);
              }
            "
          />
        </template>
        <template v-if="item.type === 'checkbox'">
          <el-checkbox-group
            v-model="formData[`${item.key}`]"
            @change="
              (e) => {
                item.change(e);
              }
            "
          >
            <el-checkbox
              v-for="(v, o) in item.checkboxOptions"
              :key="o"
              :label="v.label"
              v-bind="v.otherOptions"
              @change="
                (e) => {
                  v.change(e);
                }
              "
            />
          </el-checkbox-group>
        </template>
        <template v-if="item.type === 'radio'">
          <el-radio-group
            v-model="formData[`${item.key}`]"
            @input="
              (e) => {
                item.change(e);
              }
            "
          >
            <el-radio
              v-for="(v, o) in item.radioOptions"
              :key="o"
              :label="v.label"
              v-bind="v.otherOptions"
              @input="
                (e) => {
                  v.change(e);
                }
              "
            />
          </el-radio-group>
        </template>
        <template v-if="item.type === 'radioBtn'">
          <el-radio-group
            v-model="formData[`${item.key}`]"
            @input="
              (e) => {
                item.change(e);
              }
            "
          >
            <el-radio-button
              v-for="(v, o) in item.radioOptions"
              :key="o"
              :label="v.label"
              v-bind="item.otherOptions"
              @input="
                (e) => {
                  v.change(e);
                }
              "
            />
          </el-radio-group>
        </template>
        <template v-if="item.type === 'datePicker'">
          <el-date-picker
            v-model="formData[`${item.key}`]"
            style="width: 100%"
            :placeholder="`请选择${item.label}`"
            v-bind="item.otherOptions"
            @change="
              (e) => {
                item.change(e);
              }
            "
          />
        </template>
        <template v-if="item.type === 'addressGroup'">
          <el-input
            v-model="formData[`${item.parkAreaKey}`]"
            style="width: 100%; margin-bottom: 18px"
            placeholder="请输入园区地址"
            readonly
            v-bind="item.otherInputOptions"
          />
          <el-cascader
            v-model="formData[`${item.key}`]"
            :options="item.options"
            :placeholder="`请选择${item.label}`"
            v-bind="item.otherOptions"
            style="margin-bottom: 18px; width: 100%"
            @change="
              (e) => {
                item.change(e);
              }
            "
          />
          <el-input
            v-model="formData[`${item.addressKey}`]"
            style="width: 100"
            placeholder="请输入详细地址"
            v-bind="item.otherInputOptions"
          />
        </template>
        <template v-if="item.type === 'cascader'">
          <el-cascader
            v-model="formData[`${item.key}`]"
            :options="item.options"
            :placeholder="`请选择${item.label}`"
            v-bind="item.otherOptions"
            @change="
              (e) => {
                item.change(e);
              }
            "
          />
        </template>
        <template v-if="item.type === 'slot'">
          <slot
            v-if="$scopedSlots[item.key]"
            :slot-scope="item"
            :name="item.key"
          />
          <slot v-else :slot-scope="item" name="formSlot" />
        </template>
      </el-form-item>
      <el-form-item
        :style="{
          'margin-left': customFormButtons.marginLeft,
        }"
      >
        <el-button
          v-if="
            customFormButtons.submitShow == undefined
              ? true
              : customFormButtons.submitShow
          "
          type="primary"
          @click="submitForm('ruleForm')"
        >{{
          customFormButtons.submitName ? customFormButtons.submitName : "提交"
        }}</el-button>
        <el-button
          v-if="
            customFormButtons.resetShow == undefined
              ? true
              : customFormButtons.resetShow
          "
          @click="resetForm('ruleForm')"
        >{{
          customFormButtons.resetName ? customFormButtons.resetName : "重置"
        }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    customFormItems: {
      type: Array,
      default: () => []
    },
    customFormButtons: {
      type: Object,
      default: () => ({})
    },
    labelWidth: {
      type: String,
      default: '120px'
    },
    rules: {
      type: Object,
      default: () => ({})
    },
    inline: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {
    formData: {
      get() {
        return this.value
      }
    },
    customFormItemsArr() {
      return this.customFormItems
    }
  },
  created() {},
  methods: {
    submitForm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('submitForm', this.formData)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
      this.$emit('resetForm')
    }
  }
}
</script>

  <style lang="scss" scoped>
.customForm {
  .el-form--inline {
    .el-form-item {
      margin-top: 5px;
      margin-bottom: 5px;
    }
  }

  .el-cascader--small {
    width: 100%;
  }
}
</style>
