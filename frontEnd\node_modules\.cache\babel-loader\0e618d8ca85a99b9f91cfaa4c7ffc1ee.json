{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\components\\electricityUsage.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\components\\electricityUsage.vue", "mtime": 1754619818617}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetElectricUsage", "components", "props", "isPhotovoltaic", "type", "Boolean", "default", "data", "loading", "list", "computed", "parentData", "DateType", "StartTime", "randomInteger", "watch", "handler", "nv", "ov", "getElectricUsage", "created", "mounted", "inject", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "IsSucceed", "Data", "stop"], "sources": ["src/views/business/energyManagement/pJEnergyAnalysis/eleNew/components/electricityUsage.vue"], "sourcesContent": ["<template>\n  <div\n    v-loading=\"loading\"\n    class=\"electricityUsage\"\n    element-loading-text=\"加载中...\"\n  >\n    <div class=\"title\">\n      <div class=\"left\">用电情况</div>\n      <div class=\"right\">不包含重钢工厂</div>\n    </div>\n    <div class=\"eleList\">\n      <div v-for=\"(item, index) in list\" :key=\"index\" class=\"eleItem\" :style=\"{height: '100px' }\">\n        <div class=\"left\">\n          <p style=\"margin-bottom: 12px\">{{ item.Key }}</p>\n          <p>\n            <b>{{ item.Value }}</b><span>度</span>\n            <template v-if=\"item.Key == '总用电'\">\n              功率因数\n              <span\n                style=\"margin-left: 16px; font-size: 16px; color: #666\"\n              >--</span></template>\n            <template v-else> {{ item.Percent }}% </template>\n          </p>\n        </div>\n        <img\n          v-if=\"item.Key == '总用电'\"\n          class=\"right\"\n          src=\"@/assets/Business/eleIcon1.png\"\n          alt=\"\"\n        >\n        <img\n          v-if=\"item.Key == '用电(市电)'\"\n          class=\"right\"\n          src=\"@/assets/Business/eleIcon2.png\"\n          alt=\"\"\n        >\n        <img\n          v-if=\"item.Key == '用电(光伏)'\"\n          class=\"right\"\n          src=\"@/assets/Business/eleIcon3.png\"\n          alt=\"\"\n        >\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetElectricUsage } from '@/api/business/energyManagement.js'\nexport default {\n  components: {\n\n  },\n  props: {\n    isPhotovoltaic: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      loading: true,\n      list: []\n    }\n  },\n  computed: {\n    parentData() {\n      return {\n        DateType: this.DateType(),\n        StartTime: this.StartTime(),\n        randomInteger: this.randomInteger()\n      }\n    }\n  },\n  watch: {\n    parentData: {\n      handler(nv, ov) {\n        this.getElectricUsage()\n      }\n    }\n  },\n  created() {\n    this.getElectricUsage()\n  },\n  mounted() {\n\n  },\n  inject: ['DateType', 'StartTime', 'randomInteger'],\n  methods: {\n    async getElectricUsage() {\n      this.loading = true\n      const res = await GetElectricUsage(this.parentData)\n      if (res.IsSucceed) {\n        this.list = res.Data\n      }\n      this.loading = false\n    }\n  }\n}\n</script>\n<style scoped lang='scss'>\n.electricityUsage {\n  height: 392px;\n  background: #fff;\n  border-radius: 4px;\n  width: 100%;\n  padding: 16px;\n  box-sizing: border-box;\n  margin-bottom: 16px;\n  .title {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 16px;\n    .left {\n      color: #666;\n      font-weight: bold;\n      font-size: 16px;\n    }\n    .right {\n      font-size: 12px;\n      color: #b8bec8;\n    }\n  }\n  .eleList {\n    .eleItem {\n      padding: 20px 25px;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      border: 1px solid rgba(41, 141, 255, 0.1);\n      margin-bottom: 16px;\n      background: linear-gradient(\n        90deg,\n        rgba(41, 141, 255, 0.05) 0%,\n        rgba(41, 141, 255, 0) 100%\n      );\n      &:last-child {\n        margin-bottom: 0;\n      }\n      .left {\n        font-size: 16px;\n        color: #1d2541;\n        span {\n          color: #999;\n          font-size: 14px;\n          margin: 0 16px 0 8px;\n        }\n        b {\n          color: #394f7f;\n          font-size: 28px;\n        }\n      }\n      .right {\n        height: 54px;\n        width: 54px;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA,SAAAA,gBAAA;AACA;EACAC,UAAA,GAEA;EACAC,KAAA;IACAC,cAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA;QACAC,QAAA,OAAAA,QAAA;QACAC,SAAA,OAAAA,SAAA;QACAC,aAAA,OAAAA,aAAA;MACA;IACA;EACA;EACAC,KAAA;IACAJ,UAAA;MACAK,OAAA,WAAAA,QAAAC,EAAA,EAAAC,EAAA;QACA,KAAAC,gBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,gBAAA;EACA;EACAE,OAAA,WAAAA,QAAA,GAEA;EACAC,MAAA;EACAC,OAAA;IACAJ,gBAAA,WAAAA,iBAAA;MAAA,IAAAK,KAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,KAAA,CAAAhB,OAAA;cAAAwB,QAAA,CAAAE,IAAA;cAAA,OACAlC,gBAAA,CAAAwB,KAAA,CAAAb,UAAA;YAAA;cAAAkB,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,GAAA,CAAAO,SAAA;gBACAZ,KAAA,CAAAf,IAAA,GAAAoB,GAAA,CAAAQ,IAAA;cACA;cACAb,KAAA,CAAAhB,OAAA;YAAA;YAAA;cAAA,OAAAwB,QAAA,CAAAM,IAAA;UAAA;QAAA,GAAAV,OAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}