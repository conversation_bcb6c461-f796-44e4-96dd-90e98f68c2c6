{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\maintenanceAndUpkeep\\workOrderManagement\\components\\immediate.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\maintenanceAndUpkeep\\workOrderManagement\\components\\immediate.vue", "mtime": 1755506574384}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBDdXN0b21MYXlvdXQgZnJvbSAnQC9idXNpbmVzc0NvbXBvbmVudHMvQ3VzdG9tTGF5b3V0L2luZGV4LnZ1ZScKaW1wb3J0IEN1c3RvbVRhYmxlIGZyb20gJ0AvYnVzaW5lc3NDb21wb25lbnRzL0N1c3RvbVRhYmxlL2luZGV4LnZ1ZScKLy8gaW1wb3J0IEF1dGhCdXR0b25zIGZyb20gIkAvbWl4aW5zL2F1dGgtYnV0dG9ucyI7CmltcG9ydCBlZGl0RGlhbG9nIGZyb20gJy4uL2VkaXREaWFsb2cudnVlJwppbXBvcnQgY2xvc2VSYXRlRGlhbG9nIGZyb20gJy4uL2Nsb3NlUmF0ZURpYWxvZy52dWUnCmltcG9ydCB7CiAgR2V0V29ya09yZGVyTWFuYWdlTGlzdCwKICBHZXRXb3JrT3JkZXJUeXBlLAogIEdldFBlcnNvbkxpc3QsCiAgRGVsZXRlQ29hdGluZ1JlcXVpciwKICBTZW5kV29ya09yZGVyUGVyc29uLAogIEdldEVxdWlwRHJvcExpc3QKfSBmcm9tICdAL2FwaS9idXNpbmVzcy9tYWludGVuYW5jZUFuZFVwa2VlcC5qcycKCmV4cG9ydCBkZWZhdWx0IHsKICBOYW1lOiAnJywKICBjb21wb25lbnRzOiB7CiAgICBDdXN0b21UYWJsZSwKICAgIEN1c3RvbUxheW91dCwKICAgIGVkaXREaWFsb2csCiAgICBjbG9zZVJhdGVEaWFsb2cKICB9LAogIHByb3BzOiB7CiAgICBmbGFnOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9LAogICAgcGVyc29uTGlzdDogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgIH0sCiAgICBlcXVpcE9wdGlvbnM6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdCiAgICB9LAogICAgYXV0aEJ1dHRvbnM6IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiAoKSA9PiB7fQogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHVzZXJJZDogJycsCiAgICAgIHF1ZXJ5OiB7CiAgICAgICAgRGF0ZTogW10sCiAgICAgICAgT3JkZXJfQ29kZTogJycsCiAgICAgICAgT3JkZXJfTmFtZTogJycsCiAgICAgICAgQ3JlYXRlX0RhdGU6ICcnLAogICAgICAgIENyZWF0ZV9FRGF0ZTogJycsCiAgICAgICAgU3RhdGU6ICcnLAogICAgICAgIFdvcmtPcmRlcl9TZXR1cF9JZDogJ2pzYngnLAogICAgICAgIE1haW50YWluX1BlcnNvbjogJycsCiAgICAgICAgV29ya09yZGVyX1N0YXRlOiBudWxsLAogICAgICAgIFR5cGU6IDEKICAgICAgfSwKICAgICAgdHlwZTogJycsCiAgICAgIHBpY2tlck9wdGlvbnM6IHsKICAgICAgICBzaG9ydGN1dHM6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgdGV4dDogJ+S7iuWkqScsCiAgICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7CiAgICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW25ldyBEYXRlKCksIG5ldyBEYXRlKCldKQogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0ZXh0OiAn77+9P++/vT8sCiAgICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7CiAgICAgICAgICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoKQogICAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoKQogICAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDcpCiAgICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3N0YXJ0LCBlbmRdKQogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0ZXh0OiAn77+9PzDvv70/LAogICAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKCkKICAgICAgICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCkKICAgICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiAzMCkKICAgICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pCiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHRleHQ6ICfmnKzmnIgnLAogICAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKQogICAgICAgICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKAogICAgICAgICAgICAgICAgdG9kYXkuZ2V0RnVsbFllYXIoKSwKICAgICAgICAgICAgICAgIHRvZGF5LmdldE1vbnRoKCkgKyAxLAogICAgICAgICAgICAgICAgMAogICAgICAgICAgICAgICkKICAgICAgICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKHRvZGF5LmdldEZ1bGxZZWFyKCksIHRvZGF5LmdldE1vbnRoKCksIDEpCiAgICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3N0YXJ0LCBlbmRdKQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgXQogICAgICB9LAogICAgICBzdGF0ZUxpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICBuYW1lOiAn5b6F5aSE77+9PywKICAgICAgICAgIGNvZGU6IDAKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIG5hbWU6ICflpITnkIbvv70/LAogICAgICAgICAgY29kZTogMQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbmFtZTogJ+W+heWkjeajgCcsCiAgICAgICAgICBjb2RlOiAyCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBuYW1lOiAn5b6F6K+E77+9PywKICAgICAgICAgIGNvZGU6IDMKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIG5hbWU6ICflpITnkIblrozmiJAnLAogICAgICAgICAgY29kZTogNAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbmFtZTogJ+W3suWFs++/vT8sCiAgICAgICAgICBjb2RlOiA1CiAgICAgICAgfQogICAgICBdLAogICAgICB3b3JrVHlwZUxpc3Q6IFsKICAgICAgICAvLyB7CiAgICAgICAgLy8gICBEaXNwbGF5X05hbWU6ICLlt7LlhbPvv70/LAogICAgICAgIC8vICAgVmFsdWU6IDUsCiAgICAgICAgLy8gfSwKICAgICAgXSwKICAgICAgY29tcG9uZW50c0Z1bnM6IHsKICAgICAgICBvcGVuOiAoKSA9PiB7CiAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICAgICAgfSwKICAgICAgICBjbG9zZTogKCkgPT4gewogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgICAgICAgIHRoaXMub25GcmVzaCgpCiAgICAgICAgfSwKICAgICAgICBjbG9zZUFuZEZyZXNoOiAoKSA9PiB7CiAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQogICAgICAgICAgdGhpcy5vbkZyZXNoKCkKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBkaWFsb2dUaXRsZTogJycsCiAgICAgIHRhYmxlU2VsZWN0aW9uOiBbXSwKICAgICAgc2VsZWN0SWRzOiBbXSwKICAgICAgY3VzdG9tVGFibGVDb25maWc6IHsKICAgICAgICAvLyDooajmoLwKICAgICAgICBwYWdlU2l6ZU9wdGlvbnM6IFsxMCwgMjAsIDUwLCA4MF0sCiAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgcGFnZVNpemU6IDIwLAogICAgICAgIHRvdGFsOiAwLAogICAgICAgIGhlaWdodDogJzEwMHZoJywKICAgICAgICB0YWJsZUNvbHVtbnM6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgd2lkdGg6IDUwLAogICAgICAgICAgICBsYWJlbDogJ+W6j+WPtycsCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogewogICAgICAgICAgICAgIHR5cGU6ICdpbmRleCcsCiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInCiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAn5Y+R6LW35pe26Ze0JywKICAgICAgICAgICAga2V5OiAnQ3JlYXRlX0RhdGUnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogJ+W3peWNleWQjeensCcsCiAgICAgICAgICAgIGtleTogJ09yZGVyX05hbWUnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogJ+W3peWNleexu+WeiycsCiAgICAgICAgICAgIGtleTogJ09yZGVyX1R5cGUnLAogICAgICAgICAgICByZW5kZXI6IChyb3cpID0+IHsKICAgICAgICAgICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgKICAgICAgICAgICAgICAgICdzcGFuJywKICAgICAgICAgICAgICAgIHt9LAogICAgICAgICAgICAgICAgcm93Lk9yZGVyX1R5cGUgPT09IG51bGwKICAgICAgICAgICAgICAgICAgPyAnLScKICAgICAgICAgICAgICAgICAgOiByb3cuT3JkZXJfVHlwZSA9PT0gJ2pzYngnCiAgICAgICAgICAgICAgICAgICAgPyAn5Y2z5pe25oql5L+uJwogICAgICAgICAgICAgICAgICAgIDogJ+iuvuWkh+e7tOS/nScKICAgICAgICAgICAgICApCiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAn5bel5Y2V77+9PywKICAgICAgICAgICAga2V5OiAnT3JkZXJfQ29kZScKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAn5byA5aeL5aSE55CG5pe277+9PywKICAgICAgICAgICAga2V5OiAnU3RhcnRfVGltZScKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAn5aSE55CG5a6M5oiQ5pe26Ze0JywKICAgICAgICAgICAga2V5OiAnRW5kX1RpbWUnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogJ+WkhOeQhueUqOaXticsCiAgICAgICAgICAgIGtleTogJ1RpbWUnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogJ+aKpeS/rumDqOmXqCcsCiAgICAgICAgICAgIGtleTogJ0RlcGFydF9OYW1lJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICfmiqXkv67vv70/LAogICAgICAgICAgICBrZXk6ICdXYXJyYW50eV9QZXJzb24nCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogJ+e7tOS/ru+/vT8sCiAgICAgICAgICAgIGtleTogJ01haW50YWluX1BlcnNvbicKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAn5bel5Y2V54q277+9PywKICAgICAgICAgICAga2V5OiAnU3RhdGUnLAogICAgICAgICAgICByZW5kZXI6IChyb3cpID0+IHsKICAgICAgICAgICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgKICAgICAgICAgICAgICAgICdzcGFuJywKICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgc3R5bGU6IHsKICAgICAgICAgICAgICAgICAgICBjb2xvcjoKICAgICAgICAgICAgICAgICAgICAgIHJvdy5TdGF0ZSA9PT0gJzAnCiAgICAgICAgICAgICAgICAgICAgICAgID8gJyNGRjVFN0MnCiAgICAgICAgICAgICAgICAgICAgICAgIDogcm93LlN0YXRlID09PSAnMScKICAgICAgICAgICAgICAgICAgICAgICAgICA/ICcjMjk4REZGJwogICAgICAgICAgICAgICAgICAgICAgICAgIDogcm93LlN0YXRlID09PSAnMicKICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJyNGRjkwMkMnCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHJvdy5TdGF0ZSA9PT0gJzMnCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJyMyOThERkYnCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogcm93LlN0YXRlID09PSAnNCcKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICcjMDBEM0E3JwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJyMzMzMzMzMnCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICByb3cuU3RhdGUgPT09ICcwJwogICAgICAgICAgICAgICAgICA/ICflvoXlpITvv70/CiAgICAgICAgICAgICAgICAgIDogcm93LlN0YXRlID09PSAnMScKICAgICAgICAgICAgICAgICAgICA/ICflpITnkIbvv70/CiAgICAgICAgICAgICAgICAgICAgOiByb3cuU3RhdGUgPT09ICcyJwogICAgICAgICAgICAgICAgICAgICAgPyAn5b6F5aSN5qOAJwogICAgICAgICAgICAgICAgICAgICAgOiByb3cuU3RhdGUgPT09ICczJwogICAgICAgICAgICAgICAgICAgICAgICA/ICflvoXor4Tvv70/CiAgICAgICAgICAgICAgICAgICAgICAgIDogcm93LlN0YXRlID09PSAnNCcKICAgICAgICAgICAgICAgICAgICAgICAgICA/ICflpITnkIblrozmiJAnCiAgICAgICAgICAgICAgICAgICAgICAgICAgOiAn5bey5YWz77+9PwogICAgICAgICAgICAgICkKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIF0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUFjdGlvbnNXaWR0aDogMjIwLAogICAgICAgIHRhYmxlQWN0aW9uczogWwogICAgICAgICAgewogICAgICAgICAgICBhY3Rpb25MYWJlbDogJycsCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogewogICAgICAgICAgICAgIHR5cGU6ICd0ZXh0JwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgXSwKICAgICAgICBidXR0b25Db25maWc6IHsKICAgICAgICAgIGJ1dHRvbkxpc3Q6IFtdCiAgICAgICAgfSwKICAgICAgICBvcGVyYXRlT3B0aW9uczogewogICAgICAgICAgd2lkdGg6IDMwMCAvLyDmk43kvZzmoI/lrr3vv70/ICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9LAogIGNvbXB1dGVkOiB7fSwKICAvLyBtaXhpbnM6IFtBdXRoQnV0dG9uc10sCiAgd2F0Y2g6IHsKICAgIC8vICdBdXRoQnV0dG9ucy5idXR0b25zJzp7CiAgICAvLyAgIGhhbmRsZXIodmFsLG9sZHZhbCl7CiAgICAvLyAgICAgY29uc29sZS5sb2coJ2RkZHNzJyx2YWwsb2xkdmFsKTsKICAgIC8vICAgICAgIHRoaXMuc2hvdz10cnVlCiAgICAvLyAgICAgfQoKICAgIC8vICAgfQogICAgLy8gfQogICAgZmxhZzogewogICAgICBoYW5kbGVyKHZhbCkgewogICAgICAgIGNvbnNvbGUubG9nKCdhYWFhJywgdmFsKQogICAgICAgIHRoaXMuaW5pdERhdGEoKQogICAgICB9CiAgICB9CiAgfSwKICBjcmVhdGVkKCkge30sCiAgbW91bnRlZCgpIHsKICAgIC8vIOi3s+i9rOiuvue9rum7mOiupOWPguaVsAogICAgLy8gY29uc3QgSnVtcFBhcmFtcyA9IHRoaXMuJHFpYW5rdW4uZ2V0TWljcm9BcHBKdW1wUGFyYW1zRm4oKQogICAgLy8gY29uc29sZS5sb2coSnVtcFBhcmFtcy5DcmVhdGVfRGF0ZSwgJ+i3s+i9rOWPguaVsC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tJykKICAgIC8vIGlmIChKdW1wUGFyYW1zLmlzSnVtcCA9PSAndHJ1ZScpIHsKICAgIC8vICAgdGhpcy5xdWVyeS5TdGF0ZSA9IE51bWJlcihKdW1wUGFyYW1zLlN0YXRlKQogICAgLy8gICAvLyB0aGlzLnF1ZXJ5LkNyZWF0ZV9EYXRlID0gSnVtcFBhcmFtcy5DcmVhdGVfRGF0ZTsKICAgIC8vICAgLy8gdGhpcy5xdWVyeS5DcmVhdGVfRURhdGUgPSBKdW1wUGFyYW1zLkNyZWF0ZV9FRGF0ZTsKICAgIC8vICAgLy8gdGhpcy5xdWVyeS5EYXRlID0gW0p1bXBQYXJhbXMuQ3JlYXRlX0RhdGUsIEp1bXBQYXJhbXMuQ3JlYXRlX0VEYXRlXTsKICAgIC8vIH0KICAgIHRoaXMuaW5pdERhdGEoKQogIH0sCiAgYmVmb3JlRGVzdHJveSgpIHsKICAgIHRoaXMuJHFpYW5rdW4uc2V0TWljcm9BcHBKdW1wUGFyYW1zRm4oKQogICAgdGhpcy5xdWVyeS5TdGF0ZSA9IG51bGwKICAgIC8vIHRoaXMucXVlcnkuQ3JlYXRlX0RhdGUgPSBudWxsOwogICAgLy8gdGhpcy5xdWVyeS5DcmVhdGVfRURhdGUgPSBudWxsOwogICAgLy8gdGhpcy5xdWVyeS5EYXRlID0gW107CiAgfSwKICBtZXRob2RzOiB7CiAgICBhc3luYyBpbml0RGF0YSgpIHsKICAgICAgLy8gbGV0IHJlcyA9IGF3YWl0IEdldFdvcmtPcmRlclR5cGUoeyBDb2RlOiAiV29ya09yZGVyVHlwZSIgfSk7CiAgICAgIC8vIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgIC8vICAgdGhpcy53b3JrVHlwZUxpc3QgPSByZXMuRGF0YTsKICAgICAgLy8gfQoKICAgICAgdGhpcy51c2VySWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnVXNlcklkJykKICAgICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5LnR5cGUgPT09ICdteScpIHsKICAgICAgICB0aGlzLnF1ZXJ5LnR5cGUgPSAwCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5xdWVyeS50eXBlID0gMQogICAgICB9CiAgICAgIGF3YWl0IHRoaXMuaW5pdCgpCiAgICB9LAogICAgb3BlbkFkZCgpIHsKICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9ICfmlrDlop4nCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuJHJlZnMuZGlhbG9nUmVmLmluaXQoMCwge30sICdhZGQnKQogICAgICB9KQogICAgfSwKICAgIHNlYXJjaEZvcm0oKSB7CiAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcuY3VycmVudFBhZ2UgPSAxCiAgICAgIHRoaXMub25GcmVzaCgpCiAgICB9LAogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMucXVlcnkgPSB7CiAgICAgICAgRGF0ZTogW10sCiAgICAgICAgT3JkZXJfQ29kZTogJycsCiAgICAgICAgT3JkZXJfTmFtZTogJycsCiAgICAgICAgQ3JlYXRlX0RhdGU6ICcnLAogICAgICAgIENyZWF0ZV9FRGF0ZTogJycsCiAgICAgICAgU3RhdGU6ICcnLAogICAgICAgIFdvcmtPcmRlcl9TZXR1cF9JZDogJ2pzYngnLAogICAgICAgIE1haW50YWluX1BlcnNvbjogJycsCiAgICAgICAgV29ya09yZGVyX1N0YXRlOiB0aGlzLnF1ZXJ5LldvcmtPcmRlcl9TdGF0ZQogICAgICB9CiAgICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS50eXBlID09PSAnbXknKSB7CiAgICAgICAgdGhpcy5xdWVyeS50eXBlID0gMAogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnkudHlwZSA9IDEKICAgICAgfQogICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmN1cnJlbnRQYWdlID0gMQogICAgICB0aGlzLm9uRnJlc2goKQogICAgfSwKICAgIHJlc2V0Rm9ybSgpIHsKICAgICAgdGhpcy5vbkZyZXNoKCkKICAgIH0sCiAgICBvbkZyZXNoKCkgewogICAgICB0aGlzLmZldGNoRGF0YSgpCiAgICB9LAogICAgaW5pdCgpIHsKICAgICAgdGhpcy5mZXRjaERhdGEoKQogICAgfSwKICAgIGdldFR5cGVMaXN0KCkgewogICAgICBjb25zb2xlLmxvZygncmVzLkRhdGFyZXMuRGF0YXJlcy5EYXRhcmVzLkRhdGEtLS0tLS0tLS0tLS0tLS0tLS0tJykKICAgICAgLy8gR2V0V29ya09yZGVyVHlwZSh7IENvZGU6ICJXb3JrT3JkZXJUeXBlIiB9KS50aGVuKChyZXMpID0+IHsKICAgICAgLy8gICBjb25zb2xlLmxvZygKICAgICAgLy8gICAgIHJlcy5EYXRhLAogICAgICAvLyAgICAgInJlcy5EYXRhcmVzLkRhdGFyZXMuRGF0YXJlcy5EYXRhLS0tLS0tLS0tLS0tLS0tLS0tLSIKICAgICAgLy8gICApOwogICAgICAvLyAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgIC8vICAgICB0aGlzLnR5cGVMaXN0ID0gcmVzLkRhdGE7CiAgICAgIC8vICAgfQogICAgICAvLyB9KTsKICAgIH0sCiAgICBhc3luYyBmZXRjaERhdGEoKSB7CiAgICAgIGNvbnNvbGUubG9nKDEyMykKICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0V29ya09yZGVyTWFuYWdlTGlzdCh7CiAgICAgICAgbW9kZWw6IHRoaXMucXVlcnksCiAgICAgICAgcGFnZUluZm86IHsKICAgICAgICAgIFBhZ2U6IHRoaXMuY3VzdG9tVGFibGVDb25maWcuY3VycmVudFBhZ2UsCiAgICAgICAgICBQYWdlU2l6ZTogdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5wYWdlU2l6ZSwKICAgICAgICAgIFNvcnROYW1lOiAnQ3JlYXRlX0RhdGUnLAogICAgICAgICAgU29ydE9yZGVyOiAnREVTQycKICAgICAgICB9CiAgICAgIH0pCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy50YWJsZURhdGEgPSByZXMuRGF0YS5EYXRhCiAgICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy50b3RhbCA9IHJlcy5EYXRhLlRvdGFsQ291bnQKICAgICAgfQogICAgfSwKICAgIGhhbmRsZUNyZWF0ZSgpIHsKICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9ICfmlrDlop4nCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuJHJlZnMuZGlhbG9nUmVmLmluaXQoMCwge30sICdkaXNwYXRjaCcpCiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlRGVsZXRlKGluZGV4LCByb3cpIHsKICAgICAgdGhpcy4kY29uZmlybSgn6K+356Gu6K6k77yM5piv5ZCm5Yig6Zmk6K+l5pWw77+9PycsIHsKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICBEZWxldGVDb2F0aW5nUmVxdWlyKHsgSWQ6IHJvdy5JZCB9KS50aGVuKChyZXMpID0+IHsKICAgICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8nLAogICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICB0aGlzLmluaXQoKQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoXykgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdpbmZvJywKICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoO+/vT8KICAgICAgICAgIH0pCiAgICAgICAgfSkKICAgIH0sCiAgICAvLyDmiZPlvIDmlrDlop7nvJbovpHlvLnnqpcKICAgIGFzeW5jIG9wZW5EaWFsb2codHlwZSwgcm93LCBvcmRlclR5cGUpIHsKICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0V29ya09yZGVyVHlwZSh7IENvZGU6ICdXb3JrT3JkZXJUeXBlJyB9KQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgIHRoaXMud29ya1R5cGVMaXN0ID0gcmVzLkRhdGEKICAgICAgfQogICAgICB0aGlzLiRyZWZzLmVkaXREaWFsb2cuaGFuZGxlT3Blbih0eXBlLCByb3csIG9yZGVyVHlwZSwgdGhpcy53b3JrVHlwZUxpc3QpCiAgICB9LAogICAgLy8g5omT5byA5YWz6Zet5bel5Y2V5by556qX5oiW6K+E5Lu35by577+9PyAgICBvcGVuQ2xvc2VSYXRlKHJvdywgdHlwZSkgewogICAgICB0aGlzLiRyZWZzLmNsb3NlUmF0ZURpYWxvZy5oYW5kbGVPcGVuKHR5cGUsIHJvdykKICAgIH0sCiAgICAvLyDmjqXljZUKICAgIHJlY2VpdmluZ09yZGVycyhyb3cpIHsKICAgICAgU2VuZFdvcmtPcmRlclBlcnNvbih7IElkOiByb3cuSWQgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5o6l5Y2V5oiQ5YqfJykKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuTWVzc2FnZSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlU2l6ZUNoYW5nZSh2YWwpIHsKICAgICAgY29uc29sZS5sb2coYOavj+mhtSAke3ZhbH0g5p2hYCkKICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5wYWdlU2l6ZSA9IHZhbAogICAgICB0aGlzLm9uRnJlc2goKQogICAgfSwKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIGNvbnNvbGUubG9nKGDlvZPliY3vv70/ICR7dmFsfWApCiAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcuY3VycmVudFBhZ2UgPSB2YWwKICAgICAgdGhpcy5vbkZyZXNoKCkKICAgIH0sCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIGNvbnN0IElkcyA9IFtdCiAgICAgIHRoaXMudGFibGVTZWxlY3Rpb24gPSBzZWxlY3Rpb24KICAgICAgdGhpcy50YWJsZVNlbGVjdGlvbi5mb3JFYWNoKChpdGVtKSA9PiB7CiAgICAgICAgSWRzLnB1c2goaXRlbS5JZCkKICAgICAgfSkKICAgICAgY29uc29sZS5sb2coSWRzKQogICAgICB0aGlzLnNlbGVjdElkcyA9IElkcwogICAgICBjb25zb2xlLmxvZyh0aGlzLnRhYmxlU2VsZWN0aW9uKQogICAgfSwKCiAgICBjaGFuZ2VEYXRlKCkgewogICAgICB0aGlzLnF1ZXJ5LkNyZWF0ZV9EYXRlID0gdGhpcy5xdWVyeS5EYXRlID8gdGhpcy5xdWVyeS5EYXRlWzBdIDogbnVsbAogICAgICB0aGlzLnF1ZXJ5LkNyZWF0ZV9FRGF0ZSA9IHRoaXMucXVlcnkuRGF0ZSA/IHRoaXMucXVlcnkuRGF0ZVsxXSA6IG51bGwKICAgIH0sCiAgICBnZXRCdG5BdXRoKGNvZGUpIHsKICAgICAgLy8gY29uc29sZS5sb2coY29kZSx0aGlzLkF1dGhCdXR0b25zLHRoaXMuQXV0aEJ1dHRvbnMuYnV0dG9ucy5maW5kKGl0ZW09Pml0ZW0uQ29kZT09PWNvZGUpKTsKICAgICAgcmV0dXJuIHRoaXMuYXV0aEJ1dHRvbnMuYnV0dG9ucy5maW5kKChpdGVtKSA9PiBpdGVtLkNvZGUgPT09IGNvZGUpCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["immediate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi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file": "immediate.vue", "sourceRoot": "src/views/business/maintenanceAndUpkeep/workOrderManagement/components", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <div class=\"toolbox\">\n          <!-- <div>\n              <el-button @click=\"openDialog('add')\" type=\"primary\">�?�?/el-button>\n            </div> -->\n          <div>\n            <el-form inline>\n              <el-form-item label=\"工单�?\" style=\"margin-bottom: 10px\">\n                <el-input\n                  v-model=\"query.Order_Code\"\n                  clearable\n                  style=\"width: 150px\"\n                />\n              </el-form-item>\n              <el-form-item label=\"工单名称:\" style=\"margin-bottom: 10px\">\n                <el-input\n                  v-model=\"query.Order_Name\"\n                  clearable\n                  style=\"width: 150px\"\n                />\n              </el-form-item>\n              <el-form-item label=\"发起时间:\" style=\"margin-bottom: 10px\">\n                <el-date-picker\n                  v-model=\"query.Date\"\n                  align=\"right\"\n                  type=\"daterange\"\n                  placeholder=\"选择日期\"\n                  style=\"width: 300px\"\n                  value-format=\"yyyy-MM-dd\"\n                  :picker-options=\"pickerOptions\"\n                  @change=\"changeDate\"\n                />\n              </el-form-item>\n              <el-form-item label=\"工单状�?\" style=\"margin-bottom: 10px\">\n                <el-select\n                  v-model=\"query.State\"\n                  clearable\n                  filterable\n                  style=\"width: 120px\"\n                >\n                  <el-option\n                    v-for=\"item in stateList\"\n                    :key=\"item.code\"\n                    :label=\"item.name\"\n                    :value=\"item.code\"\n                  />\n                </el-select>\n              </el-form-item>\n              <!-- <el-form-item label=\"工单类型:\" style=\"margin-bottom: 10px\">\n                <el-select\n                  v-model=\"query.WorkOrder_Setup_Id\"\n                  clearable\n                  filterable\n                  style=\"width: 120px\"\n                >\n                  <el-option\n                    v-for=\"item in workTypeList\"\n                    :key=\"item.Value\"\n                    :label=\"item.Display_Name\"\n                    :value=\"item.Value\"\n                  />\n                </el-select>\n              </el-form-item> -->\n              <el-form-item label=\"维修�?\" style=\"margin-bottom: 10px\">\n                <el-select\n                  v-model=\"query.Maintain_Person\"\n                  clearable\n                  filterable\n                  style=\"width: 150px\"\n                >\n                  <el-option\n                    v-for=\"item in personList\"\n                    :key=\"item.Id\"\n                    :label=\"item.Name\"\n                    :value=\"item.Id\"\n                  />\n                </el-select>\n              </el-form-item>\n              <el-form-item label=\"设备查询:\" style=\"margin-bottom: 10px\">\n                <el-select\n                  v-model=\"query.EquipId\"\n                  filterable\n                  clearable\n                  placeholder=\"请输入设�?\n                >\n                  <el-option\n                    v-for=\"item in equipOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  />\n                </el-select>\n              </el-form-item>\n              <el-button @click=\"reset()\">�?�?/el-button>\n              <el-button type=\"primary\" @click=\"searchForm()\">�?�?/el-button>\n            </el-form>\n          </div>\n        </div>\n      </template>\n      <template v-slot:layoutTable>\n        <div class=\"toolbox\">\n          <div>\n            <el-radio-group\n              v-model=\"query.WorkOrder_State\"\n              class=\"typeline\"\n              @input=\"searchForm\"\n            >\n              <el-radio-button :label=\"null\">全部</el-radio-button>\n              <el-radio-button :label=\"0\">待处�?/el-radio-button>\n              <el-radio-button :label=\"1\">处理�?/el-radio-button>\n              <el-radio-button :label=\"2\">已处�?/el-radio-button>\n            </el-radio-group>\n          </div>\n          <div>\n            <el-button\n              type=\"primary\"\n              @click=\"openDialog('add')\"\n            >�?�?/el-button>\n          </div>\n        </div>\n        <CustomTable\n          style=\"height: calc(100vh - 350px)\"\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        >\n          <template #customBtn=\"{ slotScope }\">\n            <template v-if=\"slotScope.State === '0' && slotScope.Is_Anth\">\n              <el-button\n                v-if=\"getBtnAuth('dispatch')\"\n                type=\"text\"\n                @click=\"openDialog('dispatch', slotScope, slotScope.Order_Type)\"\n              >派工</el-button>\n              <el-button\n                v-if=\"getBtnAuth('dispatch-myorder')\"\n                type=\"text\"\n                code=\"dispatch-myorder\"\n                @click=\"openDialog('dispatch', slotScope, slotScope.Order_Type)\"\n              >派工</el-button>\n              <el-button\n                v-if=\"getBtnAuth('receive')\"\n                type=\"text\"\n                @click=\"receivingOrders(slotScope)\"\n              >接单</el-button>\n              <el-button\n                v-if=\"getBtnAuth('receive-myorder')\"\n                type=\"text\"\n                code=\"receive-myorder\"\n                @click=\"receivingOrders(slotScope)\"\n              >接单</el-button>\n            </template>\n            <el-button\n              v-if=\"getBtnAuth('detail')\"\n              type=\"text\"\n              @click=\"openDialog('detail', slotScope, slotScope.Order_Type)\"\n            >查看详情</el-button>\n            <el-button\n              v-if=\"getBtnAuth('detail-myorder')\"\n              type=\"text\"\n              @click=\"openDialog('detail', slotScope, slotScope.Order_Type)\"\n            >查看详情</el-button>\n            <template\n              v-if=\"\n                slotScope.State === '1' &&\n                  slotScope.Maintain_Person_Id === userId &&\n                  slotScope.Order_Type === 'jsbx'\n              \"\n            >\n              <el-button\n                v-if=\"getBtnAuth('handle')\"\n                type=\"text\"\n                @click=\"openDialog('handle', slotScope, slotScope.Order_Type)\"\n              >工单处理</el-button>\n              <el-button\n                v-if=\"getBtnAuth('handle-myorder')\"\n                type=\"text\"\n                @click=\"openDialog('handle', slotScope, slotScope.Order_Type)\"\n              >工单处理</el-button>\n            </template>\n            <template\n              v-if=\"\n                slotScope.State === '2' &&\n                  slotScope.Is_Anth &&\n                  slotScope.Order_Type === 'jsbx'\n              \"\n            >\n              <el-button\n                v-if=\"getBtnAuth('recheck')\"\n                type=\"text\"\n                @click=\"openDialog('recheck', slotScope, slotScope.Order_Type)\"\n              >工单复检</el-button>\n              <el-button\n                v-if=\"getBtnAuth('recheck-myorder')\"\n                type=\"text\"\n                @click=\"openDialog('recheck', slotScope, slotScope.Order_Type)\"\n              >工单复检</el-button>\n            </template>\n            <template\n              v-if=\"\n                slotScope.State === '3' &&\n                  slotScope.Order_Type === 'jsbx' &&\n                  slotScope.Create_UserId === userId\n              \"\n            >\n              <el-button\n                v-if=\"getBtnAuth('rate')\"\n                type=\"text\"\n                @click=\"openCloseRate(slotScope, 'rate')\"\n              >工单评价</el-button>\n              <el-button\n                v-if=\"getBtnAuth('rate-myorder')\"\n                type=\"text\"\n                @click=\"openCloseRate(slotScope, 'rate')\"\n              >工单评价</el-button>\n            </template>\n            <template v-if=\"slotScope.State === '0' || slotScope.State === '1'\">\n              <el-button\n                v-if=\"getBtnAuth('close')\"\n                type=\"text\"\n                @click=\"openCloseRate(slotScope, 'close')\"\n              >关闭</el-button>\n              <el-button\n                v-if=\"getBtnAuth('close-myorder')\"\n                type=\"text\"\n                @click=\"openCloseRate(slotScope, 'close')\"\n              >关闭</el-button>\n            </template>\n          </template>\n        </CustomTable>\n      </template>\n    </CustomLayout>\n    <editDialog ref=\"editDialog\" @refresh=\"fetchData\" />\n    <closeRateDialog ref=\"closeRateDialog\" @refresh=\"fetchData\" />\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\n// import AuthButtons from \"@/mixins/auth-buttons\";\nimport editDialog from '../editDialog.vue'\nimport closeRateDialog from '../closeRateDialog.vue'\nimport {\n  GetWorkOrderManageList,\n  GetWorkOrderType,\n  GetPersonList,\n  DeleteCoatingRequir,\n  SendWorkOrderPerson,\n  GetEquipDropList\n} from '@/api/business/maintenanceAndUpkeep.js'\n\nexport default {\n  Name: '',\n  components: {\n    CustomTable,\n    CustomLayout,\n    editDialog,\n    closeRateDialog\n  },\n  props: {\n    flag: {\n      type: Boolean,\n      default: false\n    },\n    personList: {\n      type: Array,\n      default: () => []\n    },\n    equipOptions: {\n      type: Array,\n      default: () => []\n    },\n    authButtons: {\n      type: Object,\n      default: () => {}\n    }\n  },\n  data() {\n    return {\n      userId: '',\n      query: {\n        Date: [],\n        Order_Code: '',\n        Order_Name: '',\n        Create_Date: '',\n        Create_EDate: '',\n        State: '',\n        WorkOrder_Setup_Id: 'jsbx',\n        Maintain_Person: '',\n        WorkOrder_State: null,\n        Type: 1\n      },\n      type: '',\n      pickerOptions: {\n        shortcuts: [\n          {\n            text: '今天',\n            onClick(picker) {\n              picker.$emit('pick', [new Date(), new Date()])\n            }\n          },\n          {\n            text: '�?�?,\n            onClick(picker) {\n              const end = new Date()\n              const start = new Date()\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\n              picker.$emit('pick', [start, end])\n            }\n          },\n          {\n            text: '�?0�?,\n            onClick(picker) {\n              const end = new Date()\n              const start = new Date()\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\n              picker.$emit('pick', [start, end])\n            }\n          },\n          {\n            text: '本月',\n            onClick(picker) {\n              const today = new Date()\n              const end = new Date(\n                today.getFullYear(),\n                today.getMonth() + 1,\n                0\n              )\n              const start = new Date(today.getFullYear(), today.getMonth(), 1)\n              picker.$emit('pick', [start, end])\n            }\n          }\n        ]\n      },\n      stateList: [\n        {\n          name: '待处�?,\n          code: 0\n        },\n        {\n          name: '处理�?,\n          code: 1\n        },\n        {\n          name: '待复检',\n          code: 2\n        },\n        {\n          name: '待评�?,\n          code: 3\n        },\n        {\n          name: '处理完成',\n          code: 4\n        },\n        {\n          name: '已关�?,\n          code: 5\n        }\n      ],\n      workTypeList: [\n        // {\n        //   Display_Name: \"已关�?,\n        //   Value: 5,\n        // },\n      ],\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true\n        },\n        close: () => {\n          this.dialogVisible = false\n          this.onFresh()\n        },\n        closeAndFresh: () => {\n          this.dialogVisible = false\n          this.onFresh()\n        }\n      },\n      dialogVisible: false,\n      dialogTitle: '',\n      tableSelection: [],\n      selectIds: [],\n      customTableConfig: {\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        height: '100vh',\n        tableColumns: [\n          {\n            width: 50,\n            label: '序号',\n            otherOptions: {\n              type: 'index',\n              align: 'center'\n            }\n          },\n          {\n            label: '发起时间',\n            key: 'Create_Date'\n          },\n          {\n            label: '工单名称',\n            key: 'Order_Name'\n          },\n          {\n            label: '工单类型',\n            key: 'Order_Type',\n            render: (row) => {\n              return this.$createElement(\n                'span',\n                {},\n                row.Order_Type === null\n                  ? '-'\n                  : row.Order_Type === 'jsbx'\n                    ? '即时报修'\n                    : '设备维保'\n              )\n            }\n          },\n          {\n            label: '工单�?,\n            key: 'Order_Code'\n          },\n          {\n            label: '开始处理时�?,\n            key: 'Start_Time'\n          },\n          {\n            label: '处理完成时间',\n            key: 'End_Time'\n          },\n          {\n            label: '处理用时',\n            key: 'Time'\n          },\n          {\n            label: '报修部门',\n            key: 'Depart_Name'\n          },\n          {\n            label: '报修�?,\n            key: 'Warranty_Person'\n          },\n          {\n            label: '维修�?,\n            key: 'Maintain_Person'\n          },\n          {\n            label: '工单状�?,\n            key: 'State',\n            render: (row) => {\n              return this.$createElement(\n                'span',\n                {\n                  style: {\n                    color:\n                      row.State === '0'\n                        ? '#FF5E7C'\n                        : row.State === '1'\n                          ? '#298DFF'\n                          : row.State === '2'\n                            ? '#FF902C'\n                            : row.State === '3'\n                              ? '#298DFF'\n                              : row.State === '4'\n                                ? '#00D3A7'\n                                : '#333333'\n                  }\n                },\n                row.State === '0'\n                  ? '待处�?\n                  : row.State === '1'\n                    ? '处理�?\n                    : row.State === '2'\n                      ? '待复检'\n                      : row.State === '3'\n                        ? '待评�?\n                        : row.State === '4'\n                          ? '处理完成'\n                          : '已关�?\n              )\n            }\n          }\n        ],\n        tableData: [],\n        tableActionsWidth: 220,\n        tableActions: [\n          {\n            actionLabel: '',\n            otherOptions: {\n              type: 'text'\n            }\n          }\n        ],\n        buttonConfig: {\n          buttonList: []\n        },\n        operateOptions: {\n          width: 300 // 操作栏宽�?        }\n      }\n    }\n  },\n  computed: {},\n  // mixins: [AuthButtons],\n  watch: {\n    // 'AuthButtons.buttons':{\n    //   handler(val,oldval){\n    //     console.log('dddss',val,oldval);\n    //       this.show=true\n    //     }\n\n    //   }\n    // }\n    flag: {\n      handler(val) {\n        console.log('aaaa', val)\n        this.initData()\n      }\n    }\n  },\n  created() {},\n  mounted() {\n    // 跳转设置默认参数\n    // const JumpParams = this.$qiankun.getMicroAppJumpParamsFn()\n    // console.log(JumpParams.Create_Date, '跳转参数-----------------------')\n    // if (JumpParams.isJump == 'true') {\n    //   this.query.State = Number(JumpParams.State)\n    //   // this.query.Create_Date = JumpParams.Create_Date;\n    //   // this.query.Create_EDate = JumpParams.Create_EDate;\n    //   // this.query.Date = [JumpParams.Create_Date, JumpParams.Create_EDate];\n    // }\n    this.initData()\n  },\n  beforeDestroy() {\n    this.$qiankun.setMicroAppJumpParamsFn()\n    this.query.State = null\n    // this.query.Create_Date = null;\n    // this.query.Create_EDate = null;\n    // this.query.Date = [];\n  },\n  methods: {\n    async initData() {\n      // let res = await GetWorkOrderType({ Code: \"WorkOrderType\" });\n      // if (res.IsSucceed) {\n      //   this.workTypeList = res.Data;\n      // }\n\n      this.userId = localStorage.getItem('UserId')\n      if (this.$route.query.type === 'my') {\n        this.query.type = 0\n      } else {\n        this.query.type = 1\n      }\n      await this.init()\n    },\n    openAdd() {\n      this.dialogTitle = '新增'\n      this.dialogVisible = true\n      this.$nextTick(() => {\n        this.$refs.dialogRef.init(0, {}, 'add')\n      })\n    },\n    searchForm() {\n      this.customTableConfig.currentPage = 1\n      this.onFresh()\n    },\n    reset() {\n      this.query = {\n        Date: [],\n        Order_Code: '',\n        Order_Name: '',\n        Create_Date: '',\n        Create_EDate: '',\n        State: '',\n        WorkOrder_Setup_Id: 'jsbx',\n        Maintain_Person: '',\n        WorkOrder_State: this.query.WorkOrder_State\n      }\n      if (this.$route.query.type === 'my') {\n        this.query.type = 0\n      } else {\n        this.query.type = 1\n      }\n      this.customTableConfig.currentPage = 1\n      this.onFresh()\n    },\n    resetForm() {\n      this.onFresh()\n    },\n    onFresh() {\n      this.fetchData()\n    },\n    init() {\n      this.fetchData()\n    },\n    getTypeList() {\n      console.log('res.Datares.Datares.Datares.Data-------------------')\n      // GetWorkOrderType({ Code: \"WorkOrderType\" }).then((res) => {\n      //   console.log(\n      //     res.Data,\n      //     \"res.Datares.Datares.Datares.Data-------------------\"\n      //   );\n      //   if (res.IsSucceed) {\n      //     this.typeList = res.Data;\n      //   }\n      // });\n    },\n    async fetchData() {\n      console.log(123)\n      const res = await GetWorkOrderManageList({\n        model: this.query,\n        pageInfo: {\n          Page: this.customTableConfig.currentPage,\n          PageSize: this.customTableConfig.pageSize,\n          SortName: 'Create_Date',\n          SortOrder: 'DESC'\n        }\n      })\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data\n        this.customTableConfig.total = res.Data.TotalCount\n      }\n    },\n    handleCreate() {\n      this.dialogTitle = '新增'\n      this.dialogVisible = true\n      this.$nextTick(() => {\n        this.$refs.dialogRef.init(0, {}, 'dispatch')\n      })\n    },\n    handleDelete(index, row) {\n      this.$confirm('请确认，是否删除该数�?', {\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteCoatingRequir({ Id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                message: '删除成功',\n                type: 'success'\n              })\n              this.init()\n            } else {\n              this.$message({\n                message: res.Message,\n                type: 'error'\n              })\n            }\n          })\n        })\n        .catch((_) => {\n          this.$message({\n            type: 'info',\n            message: '已取消删�?\n          })\n        })\n    },\n    // 打开新增编辑弹窗\n    async openDialog(type, row, orderType) {\n      const res = await GetWorkOrderType({ Code: 'WorkOrderType' })\n      if (res.IsSucceed) {\n        this.workTypeList = res.Data\n      }\n      this.$refs.editDialog.handleOpen(type, row, orderType, this.workTypeList)\n    },\n    // 打开关闭工单弹窗或评价弹�?    openCloseRate(row, type) {\n      this.$refs.closeRateDialog.handleOpen(type, row)\n    },\n    // 接单\n    receivingOrders(row) {\n      SendWorkOrderPerson({ Id: row.Id }).then((res) => {\n        if (res.IsSucceed) {\n          this.fetchData()\n          this.$message.success('接单成功')\n        } else {\n          this.$message.error(res.Message)\n        }\n      })\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.onFresh()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`)\n      this.customTableConfig.currentPage = val\n      this.onFresh()\n    },\n    handleSelectionChange(selection) {\n      const Ids = []\n      this.tableSelection = selection\n      this.tableSelection.forEach((item) => {\n        Ids.push(item.Id)\n      })\n      console.log(Ids)\n      this.selectIds = Ids\n      console.log(this.tableSelection)\n    },\n\n    changeDate() {\n      this.query.Create_Date = this.query.Date ? this.query.Date[0] : null\n      this.query.Create_EDate = this.query.Date ? this.query.Date[1] : null\n    },\n    getBtnAuth(code) {\n      // console.log(code,this.AuthButtons,this.AuthButtons.buttons.find(item=>item.Code===code));\n      return this.authButtons.buttons.find((item) => item.Code === code)\n    }\n  }\n}\n</script>\n\n  <style lang=\"scss\" scoped>\n@import \"@/views/business/vehicleBarrier/index.scss\";\n.toolbox {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 5px;\n  ::v-deep .el-form-item {\n    margin-bottom: 0px;\n  }\n}\n.typeline {\n  ::v-deep .el-radio-button__inner {\n    border-radius: 2px;\n  }\n  ::v-deep .is-active {\n    .el-radio-button__inner {\n      background-color: #ffffff;\n      color: #298dff;\n    }\n  }\n}\n</style>\n\n"]}]}