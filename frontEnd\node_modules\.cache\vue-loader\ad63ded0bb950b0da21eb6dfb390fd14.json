{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\maintenanceAndUpkeep\\workOrderManagement\\components\\immediate.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\maintenanceAndUpkeep\\workOrderManagement\\components\\immediate.vue", "mtime": 1755674552429}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQ3VzdG9tTGF5b3V0IGZyb20gJ0AvYnVzaW5lc3NDb21wb25lbnRzL0N1c3RvbUxheW91dC9pbmRleC52dWUnDQppbXBvcnQgQ3VzdG9tVGFibGUgZnJvbSAnQC9idXNpbmVzc0NvbXBvbmVudHMvQ3VzdG9tVGFibGUvaW5kZXgudnVlJw0KLy8gaW1wb3J0IEF1dGhCdXR0b25zIGZyb20gIkAvbWl4aW5zL2F1dGgtYnV0dG9ucyI7DQppbXBvcnQgZWRpdERpYWxvZyBmcm9tICcuLi9lZGl0RGlhbG9nLnZ1ZScNCmltcG9ydCBjbG9zZVJhdGVEaWFsb2cgZnJvbSAnLi4vY2xvc2VSYXRlRGlhbG9nLnZ1ZScNCmltcG9ydCB7DQogIEdldFdvcmtPcmRlck1hbmFnZUxpc3QsDQogIEdldFdvcmtPcmRlclR5cGUsDQogIEdldFBlcnNvbkxpc3QsDQogIERlbGV0ZUNvYXRpbmdSZXF1aXIsDQogIFNlbmRXb3JrT3JkZXJQZXJzb24sDQogIEdldEVxdWlwRHJvcExpc3QNCn0gZnJvbSAnQC9hcGkvYnVzaW5lc3MvbWFpbnRlbmFuY2VBbmRVcGtlZXAuanMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgTmFtZTogJycsDQogIGNvbXBvbmVudHM6IHsNCiAgICBDdXN0b21UYWJsZSwNCiAgICBDdXN0b21MYXlvdXQsDQogICAgZWRpdERpYWxvZywNCiAgICBjbG9zZVJhdGVEaWFsb2cNCiAgfSwNCiAgcHJvcHM6IHsNCiAgICBmbGFnOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIHBlcnNvbkxpc3Q6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgZGVmYXVsdDogKCkgPT4gW10NCiAgICB9LA0KICAgIGVxdWlwT3B0aW9uczogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgYXV0aEJ1dHRvbnM6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IHt9DQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB1c2VySWQ6ICcnLA0KICAgICAgcXVlcnk6IHsNCiAgICAgICAgRGF0ZTogW10sDQogICAgICAgIE9yZGVyX0NvZGU6ICcnLA0KICAgICAgICBPcmRlcl9OYW1lOiAnJywNCiAgICAgICAgQ3JlYXRlX0RhdGU6ICcnLA0KICAgICAgICBDcmVhdGVfRURhdGU6ICcnLA0KICAgICAgICBTdGF0ZTogJycsDQogICAgICAgIFdvcmtPcmRlcl9TZXR1cF9JZDogJ2pzYngnLA0KICAgICAgICBNYWludGFpbl9QZXJzb246ICcnLA0KICAgICAgICBXb3JrT3JkZXJfU3RhdGU6IG51bGwsDQogICAgICAgIFR5cGU6IDENCiAgICAgIH0sDQogICAgICB0eXBlOiAnJywNCiAgICAgIHBpY2tlck9wdGlvbnM6IHsNCiAgICAgICAgc2hvcnRjdXRzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdGV4dDogJ+S7iuWkqScsDQogICAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbbmV3IERhdGUoKSwgbmV3IERhdGUoKV0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0ZXh0OiAn6L+RN+WkqScsDQogICAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpDQogICAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoKQ0KICAgICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiA3KQ0KICAgICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0ZXh0OiAn6L+RMzDlpKknLA0KICAgICAgICAgICAgb25DbGljayhwaWNrZXIpIHsNCiAgICAgICAgICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoKQ0KICAgICAgICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCkNCiAgICAgICAgICAgICAgc3RhcnQuc2V0VGltZShzdGFydC5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0ICogMzApDQogICAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIFtzdGFydCwgZW5kXSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRleHQ6ICfmnKzmnIgnLA0KICAgICAgICAgICAgb25DbGljayhwaWNrZXIpIHsNCiAgICAgICAgICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpDQogICAgICAgICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKA0KICAgICAgICAgICAgICAgIHRvZGF5LmdldEZ1bGxZZWFyKCksDQogICAgICAgICAgICAgICAgdG9kYXkuZ2V0TW9udGgoKSArIDEsDQogICAgICAgICAgICAgICAgMA0KICAgICAgICAgICAgICApDQogICAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUodG9kYXkuZ2V0RnVsbFllYXIoKSwgdG9kYXkuZ2V0TW9udGgoKSwgMSkNCiAgICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3N0YXJ0LCBlbmRdKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIHN0YXRlTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+W+heWkhOeQhicsDQogICAgICAgICAgY29kZTogMA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+WkhOeQhuS4rScsDQogICAgICAgICAgY29kZTogMQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+W+heWkjeajgCcsDQogICAgICAgICAgY29kZTogMg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+W+heivhOS7tycsDQogICAgICAgICAgY29kZTogMw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+WkhOeQhuWujOaIkCcsDQogICAgICAgICAgY29kZTogNA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+W3suWFs+mXrScsDQogICAgICAgICAgY29kZTogNQ0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgd29ya1R5cGVMaXN0OiBbDQogICAgICAgIC8vIHsNCiAgICAgICAgLy8gICBEaXNwbGF5X05hbWU6ICLlt7LlhbPpl60iLA0KICAgICAgICAvLyAgIFZhbHVlOiA1LA0KICAgICAgICAvLyB9LA0KICAgICAgXSwNCiAgICAgIGNvbXBvbmVudHNGdW5zOiB7DQogICAgICAgIG9wZW46ICgpID0+IHsNCiAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICAgIH0sDQogICAgICAgIGNsb3NlOiAoKSA9PiB7DQogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgICB0aGlzLm9uRnJlc2goKQ0KICAgICAgICB9LA0KICAgICAgICBjbG9zZUFuZEZyZXNoOiAoKSA9PiB7DQogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgICB0aGlzLm9uRnJlc2goKQ0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBkaWFsb2dUaXRsZTogJycsDQogICAgICB0YWJsZVNlbGVjdGlvbjogW10sDQogICAgICBzZWxlY3RJZHM6IFtdLA0KICAgICAgY3VzdG9tVGFibGVDb25maWc6IHsNCiAgICAgICAgLy8g6KGo5qC8DQogICAgICAgIHBhZ2VTaXplT3B0aW9uczogWzEwLCAyMCwgNTAsIDgwXSwNCiAgICAgICAgY3VycmVudFBhZ2U6IDEsDQogICAgICAgIHBhZ2VTaXplOiAyMCwNCiAgICAgICAgdG90YWw6IDAsDQogICAgICAgIGhlaWdodDogJzEwMCUnLA0KICAgICAgICB0YWJsZUNvbHVtbnM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICB3aWR0aDogNTAsDQogICAgICAgICAgICBsYWJlbDogJ+W6j+WPtycsDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgdHlwZTogJ2luZGV4JywNCiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+WPkei1t+aXtumXtCcsDQogICAgICAgICAgICBrZXk6ICdDcmVhdGVfRGF0ZScNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAn5bel5Y2V5ZCN56ewJywNCiAgICAgICAgICAgIGtleTogJ09yZGVyX05hbWUnDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+W3peWNleexu+WeiycsDQogICAgICAgICAgICBrZXk6ICdPcmRlcl9UeXBlJywNCiAgICAgICAgICAgIHJlbmRlcjogKHJvdykgPT4gew0KICAgICAgICAgICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgNCiAgICAgICAgICAgICAgICAnc3BhbicsDQogICAgICAgICAgICAgICAge30sDQogICAgICAgICAgICAgICAgcm93Lk9yZGVyX1R5cGUgPT09IG51bGwNCiAgICAgICAgICAgICAgICAgID8gJy0nDQogICAgICAgICAgICAgICAgICA6IHJvdy5PcmRlcl9UeXBlID09PSAnanNieCcNCiAgICAgICAgICAgICAgICAgICAgPyAn5Y2z5pe25oql5L+uJw0KICAgICAgICAgICAgICAgICAgICA6ICforr7lpIfnu7Tkv50nDQogICAgICAgICAgICAgICkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAn5bel5Y2V5Y+3JywNCiAgICAgICAgICAgIGtleTogJ09yZGVyX0NvZGUnDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+W8gOWni+WkhOeQhuaXtumXtCcsDQogICAgICAgICAgICBrZXk6ICdTdGFydF9UaW1lJw0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICflpITnkIblrozmiJDml7bpl7QnLA0KICAgICAgICAgICAga2V5OiAnRW5kX1RpbWUnDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+WkhOeQhueUqOaXticsDQogICAgICAgICAgICBrZXk6ICdUaW1lJw0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICfmiqXkv67pg6jpl6gnLA0KICAgICAgICAgICAga2V5OiAnRGVwYXJ0X05hbWUnDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+aKpeS/ruaWuScsDQogICAgICAgICAgICBrZXk6ICdXYXJyYW50eV9QZXJzb24nDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+e7tOS/ruS6uicsDQogICAgICAgICAgICBrZXk6ICdNYWludGFpbl9QZXJzb24nDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+W3peWNleeKtuaAgScsDQogICAgICAgICAgICBrZXk6ICdTdGF0ZScsDQogICAgICAgICAgICByZW5kZXI6IChyb3cpID0+IHsNCiAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuJGNyZWF0ZUVsZW1lbnQoDQogICAgICAgICAgICAgICAgJ3NwYW4nLA0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgIHN0eWxlOiB7DQogICAgICAgICAgICAgICAgICAgIGNvbG9yOg0KICAgICAgICAgICAgICAgICAgICAgIHJvdy5TdGF0ZSA9PT0gJzAnDQogICAgICAgICAgICAgICAgICAgICAgICA/ICcjRkY1RTdDJw0KICAgICAgICAgICAgICAgICAgICAgICAgOiByb3cuU3RhdGUgPT09ICcxJw0KICAgICAgICAgICAgICAgICAgICAgICAgICA/ICcjMjk4REZGJw0KICAgICAgICAgICAgICAgICAgICAgICAgICA6IHJvdy5TdGF0ZSA9PT0gJzInDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnI0ZGOTAyQycNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHJvdy5TdGF0ZSA9PT0gJzMnDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICcjMjk4REZGJw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiByb3cuU3RhdGUgPT09ICc0Jw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICcjMDBEM0E3Jw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICcjMzMzMzMzJw0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgcm93LlN0YXRlID09PSAnMCcNCiAgICAgICAgICAgICAgICAgID8gJ+W+heWkhOeQhicNCiAgICAgICAgICAgICAgICAgIDogcm93LlN0YXRlID09PSAnMScNCiAgICAgICAgICAgICAgICAgICAgPyAn5aSE55CG5LitJw0KICAgICAgICAgICAgICAgICAgICA6IHJvdy5TdGF0ZSA9PT0gJzInDQogICAgICAgICAgICAgICAgICAgICAgPyAn5b6F5aSN5qOAJw0KICAgICAgICAgICAgICAgICAgICAgIDogcm93LlN0YXRlID09PSAnMycNCiAgICAgICAgICAgICAgICAgICAgICAgID8gJ+W+heivhOS7tycNCiAgICAgICAgICAgICAgICAgICAgICAgIDogcm93LlN0YXRlID09PSAnNCcNCiAgICAgICAgICAgICAgICAgICAgICAgICAgPyAn5aSE55CG5a6M5oiQJw0KICAgICAgICAgICAgICAgICAgICAgICAgICA6ICflt7LlhbPpl60nDQogICAgICAgICAgICAgICkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIF0sDQogICAgICAgIHRhYmxlRGF0YTogW10sDQogICAgICAgIHRhYmxlQWN0aW9uc1dpZHRoOiAyMjAsDQogICAgICAgIHRhYmxlQWN0aW9uczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGFjdGlvbkxhYmVsOiAnJywNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICB0eXBlOiAndGV4dCcNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIF0sDQogICAgICAgIGJ1dHRvbkNvbmZpZzogew0KICAgICAgICAgIGJ1dHRvbkxpc3Q6IFtdDQogICAgICAgIH0sDQogICAgICAgIG9wZXJhdGVPcHRpb25zOiB7DQogICAgICAgICAgd2lkdGg6IDMwMCAvLyDmk43kvZzmoI/lrr3luqYNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHt9LA0KICAvLyBtaXhpbnM6IFtBdXRoQnV0dG9uc10sDQogIHdhdGNoOiB7DQogICAgLy8gJ0F1dGhCdXR0b25zLmJ1dHRvbnMnOnsNCiAgICAvLyAgIGhhbmRsZXIodmFsLG9sZHZhbCl7DQogICAgLy8gICAgIGNvbnNvbGUubG9nKCdkZGRzcycsdmFsLG9sZHZhbCk7DQogICAgLy8gICAgICAgdGhpcy5zaG93PXRydWUNCiAgICAvLyAgICAgfQ0KDQogICAgLy8gICB9DQogICAgLy8gfQ0KICAgIGZsYWc6IHsNCiAgICAgIGhhbmRsZXIodmFsKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCdhYWFhJywgdmFsKQ0KICAgICAgICB0aGlzLmluaXREYXRhKCkNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7fSwNCiAgbW91bnRlZCgpIHsNCiAgICAvLyDot7Povazorr7nva7pu5jorqTlj4LmlbANCiAgICAvLyBjb25zdCBKdW1wUGFyYW1zID0gdGhpcy4kcWlhbmt1bi5nZXRNaWNyb0FwcEp1bXBQYXJhbXNGbigpDQogICAgLy8gY29uc29sZS5sb2coSnVtcFBhcmFtcy5DcmVhdGVfRGF0ZSwgJ+i3s+i9rOWPguaVsC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tJykNCiAgICAvLyBpZiAoSnVtcFBhcmFtcy5pc0p1bXAgPT0gJ3RydWUnKSB7DQogICAgLy8gICB0aGlzLnF1ZXJ5LlN0YXRlID0gTnVtYmVyKEp1bXBQYXJhbXMuU3RhdGUpDQogICAgLy8gICAvLyB0aGlzLnF1ZXJ5LkNyZWF0ZV9EYXRlID0gSnVtcFBhcmFtcy5DcmVhdGVfRGF0ZTsNCiAgICAvLyAgIC8vIHRoaXMucXVlcnkuQ3JlYXRlX0VEYXRlID0gSnVtcFBhcmFtcy5DcmVhdGVfRURhdGU7DQogICAgLy8gICAvLyB0aGlzLnF1ZXJ5LkRhdGUgPSBbSnVtcFBhcmFtcy5DcmVhdGVfRGF0ZSwgSnVtcFBhcmFtcy5DcmVhdGVfRURhdGVdOw0KICAgIC8vIH0NCiAgICB0aGlzLmluaXREYXRhKCkNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICB0aGlzLiRxaWFua3VuLnNldE1pY3JvQXBwSnVtcFBhcmFtc0ZuKCkNCiAgICB0aGlzLnF1ZXJ5LlN0YXRlID0gbnVsbA0KICAgIC8vIHRoaXMucXVlcnkuQ3JlYXRlX0RhdGUgPSBudWxsOw0KICAgIC8vIHRoaXMucXVlcnkuQ3JlYXRlX0VEYXRlID0gbnVsbDsNCiAgICAvLyB0aGlzLnF1ZXJ5LkRhdGUgPSBbXTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFzeW5jIGluaXREYXRhKCkgew0KICAgICAgLy8gbGV0IHJlcyA9IGF3YWl0IEdldFdvcmtPcmRlclR5cGUoeyBDb2RlOiAiV29ya09yZGVyVHlwZSIgfSk7DQogICAgICAvLyBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgLy8gICB0aGlzLndvcmtUeXBlTGlzdCA9IHJlcy5EYXRhOw0KICAgICAgLy8gfQ0KDQogICAgICB0aGlzLnVzZXJJZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdVc2VySWQnKQ0KICAgICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5LnR5cGUgPT09ICdteScpIHsNCiAgICAgICAgdGhpcy5xdWVyeS50eXBlID0gMA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5xdWVyeS50eXBlID0gMQ0KICAgICAgfQ0KICAgICAgYXdhaXQgdGhpcy5pbml0KCkNCiAgICB9LA0KICAgIG9wZW5BZGQoKSB7DQogICAgICB0aGlzLmRpYWxvZ1RpdGxlID0gJ+aWsOWinicNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy4kcmVmcy5kaWFsb2dSZWYuaW5pdCgwLCB7fSwgJ2FkZCcpDQogICAgICB9KQ0KICAgIH0sDQogICAgc2VhcmNoRm9ybSgpIHsNCiAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcuY3VycmVudFBhZ2UgPSAxDQogICAgICB0aGlzLm9uRnJlc2goKQ0KICAgIH0sDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLnF1ZXJ5ID0gew0KICAgICAgICBEYXRlOiBbXSwNCiAgICAgICAgT3JkZXJfQ29kZTogJycsDQogICAgICAgIE9yZGVyX05hbWU6ICcnLA0KICAgICAgICBDcmVhdGVfRGF0ZTogJycsDQogICAgICAgIENyZWF0ZV9FRGF0ZTogJycsDQogICAgICAgIFN0YXRlOiAnJywNCiAgICAgICAgV29ya09yZGVyX1NldHVwX0lkOiAnanNieCcsDQogICAgICAgIE1haW50YWluX1BlcnNvbjogJycsDQogICAgICAgIFdvcmtPcmRlcl9TdGF0ZTogdGhpcy5xdWVyeS5Xb3JrT3JkZXJfU3RhdGUNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS50eXBlID09PSAnbXknKSB7DQogICAgICAgIHRoaXMucXVlcnkudHlwZSA9IDANCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucXVlcnkudHlwZSA9IDENCiAgICAgIH0NCiAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcuY3VycmVudFBhZ2UgPSAxDQogICAgICB0aGlzLm9uRnJlc2goKQ0KICAgIH0sDQogICAgcmVzZXRGb3JtKCkgew0KICAgICAgdGhpcy5vbkZyZXNoKCkNCiAgICB9LA0KICAgIG9uRnJlc2goKSB7DQogICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgfSwNCiAgICBpbml0KCkgew0KICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgIH0sDQogICAgZ2V0VHlwZUxpc3QoKSB7DQogICAgICBjb25zb2xlLmxvZygncmVzLkRhdGFyZXMuRGF0YXJlcy5EYXRhcmVzLkRhdGEtLS0tLS0tLS0tLS0tLS0tLS0tJykNCiAgICAgIC8vIEdldFdvcmtPcmRlclR5cGUoeyBDb2RlOiAiV29ya09yZGVyVHlwZSIgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAvLyAgIGNvbnNvbGUubG9nKA0KICAgICAgLy8gICAgIHJlcy5EYXRhLA0KICAgICAgLy8gICAgICJyZXMuRGF0YXJlcy5EYXRhcmVzLkRhdGFyZXMuRGF0YS0tLS0tLS0tLS0tLS0tLS0tLS0iDQogICAgICAvLyAgICk7DQogICAgICAvLyAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAvLyAgICAgdGhpcy50eXBlTGlzdCA9IHJlcy5EYXRhOw0KICAgICAgLy8gICB9DQogICAgICAvLyB9KTsNCiAgICB9LA0KICAgIGFzeW5jIGZldGNoRGF0YSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKDEyMykNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldFdvcmtPcmRlck1hbmFnZUxpc3Qoew0KICAgICAgICBtb2RlbDogdGhpcy5xdWVyeSwNCiAgICAgICAgcGFnZUluZm86IHsNCiAgICAgICAgICBQYWdlOiB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmN1cnJlbnRQYWdlLA0KICAgICAgICAgIFBhZ2VTaXplOiB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLnBhZ2VTaXplLA0KICAgICAgICAgIFNvcnROYW1lOiAnQ3JlYXRlX0RhdGUnLA0KICAgICAgICAgIFNvcnRPcmRlcjogJ0RFU0MnDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLnRhYmxlRGF0YSA9IHJlcy5EYXRhLkRhdGENCiAgICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy50b3RhbCA9IHJlcy5EYXRhLlRvdGFsQ291bnQNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUNyZWF0ZSgpIHsNCiAgICAgIHRoaXMuZGlhbG9nVGl0bGUgPSAn5paw5aKeJw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLiRyZWZzLmRpYWxvZ1JlZi5pbml0KDAsIHt9LCAnZGlzcGF0Y2gnKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZURlbGV0ZShpbmRleCwgcm93KSB7DQogICAgICB0aGlzLiRjb25maXJtKCfor7fnoa7orqTvvIzmmK/lkKbliKDpmaTor6XmlbDmja4/Jywgew0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICBEZWxldGVDb2F0aW5nUmVxdWlyKHsgSWQ6IHJvdy5JZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8nLA0KICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICB0aGlzLmluaXQoKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoXykgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcNCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgIH0sDQogICAgLy8g5omT5byA5paw5aKe57yW6L6R5by556qXDQogICAgYXN5bmMgb3BlbkRpYWxvZyh0eXBlLCByb3csIG9yZGVyVHlwZSkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0V29ya09yZGVyVHlwZSh7IENvZGU6ICdXb3JrT3JkZXJUeXBlJyB9KQ0KICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgdGhpcy53b3JrVHlwZUxpc3QgPSByZXMuRGF0YQ0KICAgICAgfQ0KICAgICAgdGhpcy4kcmVmcy5lZGl0RGlhbG9nLmhhbmRsZU9wZW4odHlwZSwgcm93LCBvcmRlclR5cGUsIHRoaXMud29ya1R5cGVMaXN0KQ0KICAgIH0sDQogICAgLy8g5omT5byA5YWz6Zet5bel5Y2V5by556qX5oiW6K+E5Lu35by556qXDQogICAgb3BlbkNsb3NlUmF0ZShyb3csIHR5cGUpIHsNCiAgICAgIHRoaXMuJHJlZnMuY2xvc2VSYXRlRGlhbG9nLmhhbmRsZU9wZW4odHlwZSwgcm93KQ0KICAgIH0sDQogICAgLy8g5o6l5Y2VDQogICAgcmVjZWl2aW5nT3JkZXJzKHJvdykgew0KICAgICAgU2VuZFdvcmtPcmRlclBlcnNvbih7IElkOiByb3cuSWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5o6l5Y2V5oiQ5YqfJykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlU2l6ZUNoYW5nZSh2YWwpIHsNCiAgICAgIGNvbnNvbGUubG9nKGDmr4/pobUgJHt2YWx9IOadoWApDQogICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLnBhZ2VTaXplID0gdmFsDQogICAgICB0aGlzLm9uRnJlc2goKQ0KICAgIH0sDQogICAgaGFuZGxlQ3VycmVudENoYW5nZSh2YWwpIHsNCiAgICAgIGNvbnNvbGUubG9nKGDlvZPliY3pobU6ICR7dmFsfWApDQogICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmN1cnJlbnRQYWdlID0gdmFsDQogICAgICB0aGlzLm9uRnJlc2goKQ0KICAgIH0sDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgY29uc3QgSWRzID0gW10NCiAgICAgIHRoaXMudGFibGVTZWxlY3Rpb24gPSBzZWxlY3Rpb24NCiAgICAgIHRoaXMudGFibGVTZWxlY3Rpb24uZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICBJZHMucHVzaChpdGVtLklkKQ0KICAgICAgfSkNCiAgICAgIGNvbnNvbGUubG9nKElkcykNCiAgICAgIHRoaXMuc2VsZWN0SWRzID0gSWRzDQogICAgICBjb25zb2xlLmxvZyh0aGlzLnRhYmxlU2VsZWN0aW9uKQ0KICAgIH0sDQoNCiAgICBjaGFuZ2VEYXRlKCkgew0KICAgICAgdGhpcy5xdWVyeS5DcmVhdGVfRGF0ZSA9IHRoaXMucXVlcnkuRGF0ZSA/IHRoaXMucXVlcnkuRGF0ZVswXSA6IG51bGwNCiAgICAgIHRoaXMucXVlcnkuQ3JlYXRlX0VEYXRlID0gdGhpcy5xdWVyeS5EYXRlID8gdGhpcy5xdWVyeS5EYXRlWzFdIDogbnVsbA0KICAgIH0sDQogICAgZ2V0QnRuQXV0aChjb2RlKSB7DQogICAgICAvLyBjb25zb2xlLmxvZyhjb2RlLHRoaXMuQXV0aEJ1dHRvbnMsdGhpcy5BdXRoQnV0dG9ucy5idXR0b25zLmZpbmQoaXRlbT0+aXRlbS5Db2RlPT09Y29kZSkpOw0KICAgICAgcmV0dXJuIHRoaXMuYXV0aEJ1dHRvbnMuYnV0dG9ucy5maW5kKChpdGVtKSA9PiBpdGVtLkNvZGUgPT09IGNvZGUpDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["immediate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi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file": "immediate.vue", "sourceRoot": "src/views/business/maintenanceAndUpkeep/workOrderManagement/components", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <div class=\"toolbox\">\r\n          <!-- <div>\r\n              <el-button @click=\"openDialog('add')\" type=\"primary\">新 增</el-button>\r\n            </div> -->\r\n          <div>\r\n            <el-form inline>\r\n              <el-form-item label=\"工单号:\" style=\"margin-bottom: 10px\">\r\n                <el-input\r\n                  v-model=\"query.Order_Code\"\r\n                  clearable\r\n                  style=\"width: 150px\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"工单名称:\" style=\"margin-bottom: 10px\">\r\n                <el-input\r\n                  v-model=\"query.Order_Name\"\r\n                  clearable\r\n                  style=\"width: 150px\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"发起时间:\" style=\"margin-bottom: 10px\">\r\n                <el-date-picker\r\n                  v-model=\"query.Date\"\r\n                  align=\"right\"\r\n                  type=\"daterange\"\r\n                  placeholder=\"选择日期\"\r\n                  style=\"width: 300px\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  :picker-options=\"pickerOptions\"\r\n                  @change=\"changeDate\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"工单状态:\" style=\"margin-bottom: 10px\">\r\n                <el-select\r\n                  v-model=\"query.State\"\r\n                  clearable\r\n                  filterable\r\n                  style=\"width: 120px\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in stateList\"\r\n                    :key=\"item.code\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.code\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n              <!-- <el-form-item label=\"工单类型:\" style=\"margin-bottom: 10px\">\r\n                <el-select\r\n                  v-model=\"query.WorkOrder_Setup_Id\"\r\n                  clearable\r\n                  filterable\r\n                  style=\"width: 120px\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in workTypeList\"\r\n                    :key=\"item.Value\"\r\n                    :label=\"item.Display_Name\"\r\n                    :value=\"item.Value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item> -->\r\n              <el-form-item label=\"维修人:\" style=\"margin-bottom: 10px\">\r\n                <el-select\r\n                  v-model=\"query.Maintain_Person\"\r\n                  clearable\r\n                  filterable\r\n                  style=\"width: 150px\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in personList\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item label=\"设备查询:\" style=\"margin-bottom: 10px\">\r\n                <el-select\r\n                  v-model=\"query.EquipId\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请输入设备\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in equipOptions\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"item.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-button @click=\"reset()\">重 置</el-button>\r\n              <el-button type=\"primary\" @click=\"searchForm()\">查 询</el-button>\r\n            </el-form>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <div class=\"toolbox\">\r\n          <div>\r\n            <el-radio-group\r\n              v-model=\"query.WorkOrder_State\"\r\n              class=\"typeline\"\r\n              @input=\"searchForm\"\r\n            >\r\n              <el-radio-button :label=\"null\">全部</el-radio-button>\r\n              <el-radio-button :label=\"0\">待处理</el-radio-button>\r\n              <el-radio-button :label=\"1\">处理中</el-radio-button>\r\n              <el-radio-button :label=\"2\">已处理</el-radio-button>\r\n            </el-radio-group>\r\n          </div>\r\n          <div>\r\n            <el-button\r\n              type=\"primary\"\r\n              @click=\"openDialog('add')\"\r\n            >新 增</el-button>\r\n          </div>\r\n        </div>\r\n        <CustomTable\r\n          style=\"height: calc(100vh - 350px)\"\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        >\r\n          <template #customBtn=\"{ slotScope }\">\r\n            <template v-if=\"slotScope.State === '0' && slotScope.Is_Anth\">\r\n              <el-button\r\n                v-if=\"getBtnAuth('dispatch')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('dispatch', slotScope, slotScope.Order_Type)\"\r\n              >派工</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('dispatch-myorder')\"\r\n                type=\"text\"\r\n                code=\"dispatch-myorder\"\r\n                @click=\"openDialog('dispatch', slotScope, slotScope.Order_Type)\"\r\n              >派工</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('receive')\"\r\n                type=\"text\"\r\n                @click=\"receivingOrders(slotScope)\"\r\n              >接单</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('receive-myorder')\"\r\n                type=\"text\"\r\n                code=\"receive-myorder\"\r\n                @click=\"receivingOrders(slotScope)\"\r\n              >接单</el-button>\r\n            </template>\r\n            <el-button\r\n              v-if=\"getBtnAuth('detail')\"\r\n              type=\"text\"\r\n              @click=\"openDialog('detail', slotScope, slotScope.Order_Type)\"\r\n            >查看详情</el-button>\r\n            <el-button\r\n              v-if=\"getBtnAuth('detail-myorder')\"\r\n              type=\"text\"\r\n              @click=\"openDialog('detail', slotScope, slotScope.Order_Type)\"\r\n            >查看详情</el-button>\r\n            <template\r\n              v-if=\"\r\n                slotScope.State === '1' &&\r\n                  slotScope.Maintain_Person_Id === userId &&\r\n                  slotScope.Order_Type === 'jsbx'\r\n              \"\r\n            >\r\n              <el-button\r\n                v-if=\"getBtnAuth('handle')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('handle', slotScope, slotScope.Order_Type)\"\r\n              >工单处理</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('handle-myorder')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('handle', slotScope, slotScope.Order_Type)\"\r\n              >工单处理</el-button>\r\n            </template>\r\n            <template\r\n              v-if=\"\r\n                slotScope.State === '2' &&\r\n                  slotScope.Is_Anth &&\r\n                  slotScope.Order_Type === 'jsbx'\r\n              \"\r\n            >\r\n              <el-button\r\n                v-if=\"getBtnAuth('recheck')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('recheck', slotScope, slotScope.Order_Type)\"\r\n              >工单复检</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('recheck-myorder')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('recheck', slotScope, slotScope.Order_Type)\"\r\n              >工单复检</el-button>\r\n            </template>\r\n            <template\r\n              v-if=\"\r\n                slotScope.State === '3' &&\r\n                  slotScope.Order_Type === 'jsbx' &&\r\n                  slotScope.Create_UserId === userId\r\n              \"\r\n            >\r\n              <el-button\r\n                v-if=\"getBtnAuth('rate')\"\r\n                type=\"text\"\r\n                @click=\"openCloseRate(slotScope, 'rate')\"\r\n              >工单评价</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('rate-myorder')\"\r\n                type=\"text\"\r\n                @click=\"openCloseRate(slotScope, 'rate')\"\r\n              >工单评价</el-button>\r\n            </template>\r\n            <template v-if=\"slotScope.State === '0' || slotScope.State === '1'\">\r\n              <el-button\r\n                v-if=\"getBtnAuth('close')\"\r\n                type=\"text\"\r\n                @click=\"openCloseRate(slotScope, 'close')\"\r\n              >关闭</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('close-myorder')\"\r\n                type=\"text\"\r\n                @click=\"openCloseRate(slotScope, 'close')\"\r\n              >关闭</el-button>\r\n            </template>\r\n          </template>\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n    <editDialog ref=\"editDialog\" @refresh=\"fetchData\" />\r\n    <closeRateDialog ref=\"closeRateDialog\" @refresh=\"fetchData\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\n// import AuthButtons from \"@/mixins/auth-buttons\";\r\nimport editDialog from '../editDialog.vue'\r\nimport closeRateDialog from '../closeRateDialog.vue'\r\nimport {\r\n  GetWorkOrderManageList,\r\n  GetWorkOrderType,\r\n  GetPersonList,\r\n  DeleteCoatingRequir,\r\n  SendWorkOrderPerson,\r\n  GetEquipDropList\r\n} from '@/api/business/maintenanceAndUpkeep.js'\r\n\r\nexport default {\r\n  Name: '',\r\n  components: {\r\n    CustomTable,\r\n    CustomLayout,\r\n    editDialog,\r\n    closeRateDialog\r\n  },\r\n  props: {\r\n    flag: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    personList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    equipOptions: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    authButtons: {\r\n      type: Object,\r\n      default: () => {}\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      userId: '',\r\n      query: {\r\n        Date: [],\r\n        Order_Code: '',\r\n        Order_Name: '',\r\n        Create_Date: '',\r\n        Create_EDate: '',\r\n        State: '',\r\n        WorkOrder_Setup_Id: 'jsbx',\r\n        Maintain_Person: '',\r\n        WorkOrder_State: null,\r\n        Type: 1\r\n      },\r\n      type: '',\r\n      pickerOptions: {\r\n        shortcuts: [\r\n          {\r\n            text: '今天',\r\n            onClick(picker) {\r\n              picker.$emit('pick', [new Date(), new Date()])\r\n            }\r\n          },\r\n          {\r\n            text: '近7天',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '近30天',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '本月',\r\n            onClick(picker) {\r\n              const today = new Date()\r\n              const end = new Date(\r\n                today.getFullYear(),\r\n                today.getMonth() + 1,\r\n                0\r\n              )\r\n              const start = new Date(today.getFullYear(), today.getMonth(), 1)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      stateList: [\r\n        {\r\n          name: '待处理',\r\n          code: 0\r\n        },\r\n        {\r\n          name: '处理中',\r\n          code: 1\r\n        },\r\n        {\r\n          name: '待复检',\r\n          code: 2\r\n        },\r\n        {\r\n          name: '待评价',\r\n          code: 3\r\n        },\r\n        {\r\n          name: '处理完成',\r\n          code: 4\r\n        },\r\n        {\r\n          name: '已关闭',\r\n          code: 5\r\n        }\r\n      ],\r\n      workTypeList: [\r\n        // {\r\n        //   Display_Name: \"已关闭\",\r\n        //   Value: 5,\r\n        // },\r\n      ],\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        },\r\n        closeAndFresh: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n      selectIds: [],\r\n      customTableConfig: {\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            label: '序号',\r\n            otherOptions: {\r\n              type: 'index',\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '发起时间',\r\n            key: 'Create_Date'\r\n          },\r\n          {\r\n            label: '工单名称',\r\n            key: 'Order_Name'\r\n          },\r\n          {\r\n            label: '工单类型',\r\n            key: 'Order_Type',\r\n            render: (row) => {\r\n              return this.$createElement(\r\n                'span',\r\n                {},\r\n                row.Order_Type === null\r\n                  ? '-'\r\n                  : row.Order_Type === 'jsbx'\r\n                    ? '即时报修'\r\n                    : '设备维保'\r\n              )\r\n            }\r\n          },\r\n          {\r\n            label: '工单号',\r\n            key: 'Order_Code'\r\n          },\r\n          {\r\n            label: '开始处理时间',\r\n            key: 'Start_Time'\r\n          },\r\n          {\r\n            label: '处理完成时间',\r\n            key: 'End_Time'\r\n          },\r\n          {\r\n            label: '处理用时',\r\n            key: 'Time'\r\n          },\r\n          {\r\n            label: '报修部门',\r\n            key: 'Depart_Name'\r\n          },\r\n          {\r\n            label: '报修方',\r\n            key: 'Warranty_Person'\r\n          },\r\n          {\r\n            label: '维修人',\r\n            key: 'Maintain_Person'\r\n          },\r\n          {\r\n            label: '工单状态',\r\n            key: 'State',\r\n            render: (row) => {\r\n              return this.$createElement(\r\n                'span',\r\n                {\r\n                  style: {\r\n                    color:\r\n                      row.State === '0'\r\n                        ? '#FF5E7C'\r\n                        : row.State === '1'\r\n                          ? '#298DFF'\r\n                          : row.State === '2'\r\n                            ? '#FF902C'\r\n                            : row.State === '3'\r\n                              ? '#298DFF'\r\n                              : row.State === '4'\r\n                                ? '#00D3A7'\r\n                                : '#333333'\r\n                  }\r\n                },\r\n                row.State === '0'\r\n                  ? '待处理'\r\n                  : row.State === '1'\r\n                    ? '处理中'\r\n                    : row.State === '2'\r\n                      ? '待复检'\r\n                      : row.State === '3'\r\n                        ? '待评价'\r\n                        : row.State === '4'\r\n                          ? '处理完成'\r\n                          : '已关闭'\r\n              )\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActionsWidth: 220,\r\n        tableActions: [\r\n          {\r\n            actionLabel: '',\r\n            otherOptions: {\r\n              type: 'text'\r\n            }\r\n          }\r\n        ],\r\n        buttonConfig: {\r\n          buttonList: []\r\n        },\r\n        operateOptions: {\r\n          width: 300 // 操作栏宽度\r\n        }\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  // mixins: [AuthButtons],\r\n  watch: {\r\n    // 'AuthButtons.buttons':{\r\n    //   handler(val,oldval){\r\n    //     console.log('dddss',val,oldval);\r\n    //       this.show=true\r\n    //     }\r\n\r\n    //   }\r\n    // }\r\n    flag: {\r\n      handler(val) {\r\n        console.log('aaaa', val)\r\n        this.initData()\r\n      }\r\n    }\r\n  },\r\n  created() {},\r\n  mounted() {\r\n    // 跳转设置默认参数\r\n    // const JumpParams = this.$qiankun.getMicroAppJumpParamsFn()\r\n    // console.log(JumpParams.Create_Date, '跳转参数-----------------------')\r\n    // if (JumpParams.isJump == 'true') {\r\n    //   this.query.State = Number(JumpParams.State)\r\n    //   // this.query.Create_Date = JumpParams.Create_Date;\r\n    //   // this.query.Create_EDate = JumpParams.Create_EDate;\r\n    //   // this.query.Date = [JumpParams.Create_Date, JumpParams.Create_EDate];\r\n    // }\r\n    this.initData()\r\n  },\r\n  beforeDestroy() {\r\n    this.$qiankun.setMicroAppJumpParamsFn()\r\n    this.query.State = null\r\n    // this.query.Create_Date = null;\r\n    // this.query.Create_EDate = null;\r\n    // this.query.Date = [];\r\n  },\r\n  methods: {\r\n    async initData() {\r\n      // let res = await GetWorkOrderType({ Code: \"WorkOrderType\" });\r\n      // if (res.IsSucceed) {\r\n      //   this.workTypeList = res.Data;\r\n      // }\r\n\r\n      this.userId = localStorage.getItem('UserId')\r\n      if (this.$route.query.type === 'my') {\r\n        this.query.type = 0\r\n      } else {\r\n        this.query.type = 1\r\n      }\r\n      await this.init()\r\n    },\r\n    openAdd() {\r\n      this.dialogTitle = '新增'\r\n      this.dialogVisible = true\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.init(0, {}, 'add')\r\n      })\r\n    },\r\n    searchForm() {\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    reset() {\r\n      this.query = {\r\n        Date: [],\r\n        Order_Code: '',\r\n        Order_Name: '',\r\n        Create_Date: '',\r\n        Create_EDate: '',\r\n        State: '',\r\n        WorkOrder_Setup_Id: 'jsbx',\r\n        Maintain_Person: '',\r\n        WorkOrder_State: this.query.WorkOrder_State\r\n      }\r\n      if (this.$route.query.type === 'my') {\r\n        this.query.type = 0\r\n      } else {\r\n        this.query.type = 1\r\n      }\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.fetchData()\r\n    },\r\n    init() {\r\n      this.fetchData()\r\n    },\r\n    getTypeList() {\r\n      console.log('res.Datares.Datares.Datares.Data-------------------')\r\n      // GetWorkOrderType({ Code: \"WorkOrderType\" }).then((res) => {\r\n      //   console.log(\r\n      //     res.Data,\r\n      //     \"res.Datares.Datares.Datares.Data-------------------\"\r\n      //   );\r\n      //   if (res.IsSucceed) {\r\n      //     this.typeList = res.Data;\r\n      //   }\r\n      // });\r\n    },\r\n    async fetchData() {\r\n      console.log(123)\r\n      const res = await GetWorkOrderManageList({\r\n        model: this.query,\r\n        pageInfo: {\r\n          Page: this.customTableConfig.currentPage,\r\n          PageSize: this.customTableConfig.pageSize,\r\n          SortName: 'Create_Date',\r\n          SortOrder: 'DESC'\r\n        }\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.dialogVisible = true\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.init(0, {}, 'dispatch')\r\n      })\r\n    },\r\n    handleDelete(index, row) {\r\n      this.$confirm('请确认，是否删除该数据?', {\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeleteCoatingRequir({ Id: row.Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n              this.init()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch((_) => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    // 打开新增编辑弹窗\r\n    async openDialog(type, row, orderType) {\r\n      const res = await GetWorkOrderType({ Code: 'WorkOrderType' })\r\n      if (res.IsSucceed) {\r\n        this.workTypeList = res.Data\r\n      }\r\n      this.$refs.editDialog.handleOpen(type, row, orderType, this.workTypeList)\r\n    },\r\n    // 打开关闭工单弹窗或评价弹窗\r\n    openCloseRate(row, type) {\r\n      this.$refs.closeRateDialog.handleOpen(type, row)\r\n    },\r\n    // 接单\r\n    receivingOrders(row) {\r\n      SendWorkOrderPerson({ Id: row.Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.fetchData()\r\n          this.$message.success('接单成功')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.onFresh()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.onFresh()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      const Ids = []\r\n      this.tableSelection = selection\r\n      this.tableSelection.forEach((item) => {\r\n        Ids.push(item.Id)\r\n      })\r\n      console.log(Ids)\r\n      this.selectIds = Ids\r\n      console.log(this.tableSelection)\r\n    },\r\n\r\n    changeDate() {\r\n      this.query.Create_Date = this.query.Date ? this.query.Date[0] : null\r\n      this.query.Create_EDate = this.query.Date ? this.query.Date[1] : null\r\n    },\r\n    getBtnAuth(code) {\r\n      // console.log(code,this.AuthButtons,this.AuthButtons.buttons.find(item=>item.Code===code));\r\n      return this.authButtons.buttons.find((item) => item.Code === code)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n  <style lang=\"scss\" scoped>\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n.toolbox {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 5px;\r\n  ::v-deep .el-form-item {\r\n    margin-bottom: 0px;\r\n  }\r\n}\r\n.typeline {\r\n  ::v-deep .el-radio-button__inner {\r\n    border-radius: 2px;\r\n  }\r\n  ::v-deep .is-active {\r\n    .el-radio-button__inner {\r\n      background-color: #ffffff;\r\n      color: #298dff;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}