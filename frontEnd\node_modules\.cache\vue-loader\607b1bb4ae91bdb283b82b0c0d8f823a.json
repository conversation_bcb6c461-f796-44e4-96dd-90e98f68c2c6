{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\environmentalManagement\\alarmInformation\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\environmentalManagement\\alarmInformation\\index.vue", "mtime": 1755506574265}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/business/environmentalManagement/alarmInformation", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n          ><template #customBtn=\"{ slotScope }\"\n            ><el-button\n              v-if=\"slotScope.Handle_Status == 1\"\n              type=\"text\"\n              @click=\"handelClose(slotScope)\"\n              >关闭</el-button\n            ></template\n          ></CustomTable\n        >\n      </template>\n    </CustomLayout>\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <component\n        :is=\"currentComponent\"\n        v-if=\"dialogVisible\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n    /></el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\n\nimport DialogForm from './dialogForm.vue'\nimport DialogFormLook from \"./dialogFormLook.vue\";\n\nimport { downloadFile } from \"@/utils/downloadFile\";\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\nimport { deviceTypeMixins } from \"../../mixins/deviceType.js\";\nimport {\n  GetWarningList,\n  GetWarningType,\n  ExportWarning,\n  UpdateWarningStatus,\n} from \"@/api/business/environmentalManagement\";\n// import * as moment from 'moment'\nimport dayjs from \"dayjs\";\nexport default {\n  name: \"\",\n  components: {\n    CustomTable,\n    // CustomButton,\n    // CustomTitle,\n    CustomForm,\n    CustomLayout,\n  },\n  mixins: [deviceTypeMixins],\n  data() {\n    return {\n      currentComponent: DialogForm,\n      componentsConfig: {},\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true;\n        },\n        close: () => {\n          this.dialogVisible = false;\n          this.onFresh();\n        },\n      },\n      dialogVisible: false,\n      dialogTitle: \"\",\n      tableSelection: [],\n\n      ruleForm: {\n        Content: \"\",\n        EqtType: \"\",\n        Position: \"\",\n      },\n      customForm: {\n        formItems: [\n          {\n            key: \"Content\", // 字段ID\n            label: \"\", // Form的label\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"输入设备编号或名称进行搜�?,\n            },\n            width: \"240px\",\n            change: (e) => {\n              // change事件\n              console.log(e);\n            },\n          },\n          {\n            key: \"EqtType\",\n            label: \"设备类型\",\n            type: \"select\",\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"请选择设备类型\",\n            },\n            options: [],\n            change: (e) => {\n              console.log(e);\n            },\n          },\n          {\n            key: \"WarningType\",\n            label: \"告警类型\",\n            type: \"select\",\n\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"请选择告警类型\",\n            },\n            options: [],\n            change: (e) => {\n              console.log(e);\n            },\n          },\n          {\n            key: \"Handle_Status\",\n            label: \"告警状�?,\n            type: \"select\",\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"请选择告警状�?,\n            },\n            options: [\n              {\n                label: \"告警�?,\n                value: 1,\n              },\n              {\n                label: \"已关�?,\n                value: 2,\n              },\n              // {\n              //   label: \"已处�?,\n              //   value: 3,\n              // },\n            ],\n            change: (e) => {\n              console.log(e);\n            },\n          },\n          {\n            key: \"Position\", // 字段ID\n            label: \"安装位置\", // Form的label\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"请输入安装位�?,\n            },\n            change: (e) => {\n              // change事件\n              console.log(e);\n            },\n          },\n        ],\n        rules: {\n          // 请参照elementForm rules\n        },\n        customFormButtons: {\n          submitName: \"查询\",\n          resetName: \"重置\",\n        },\n      },\n      customTableConfig: {\n        buttonConfig: {\n          buttonList: [\n            // {\n            //   text: '新增',\n            //   round: false, // 是否圆角\n            //   plain: false, // 是否朴素\n            //   circle: false, // 是否圆形\n            //   loading: false, // 是否加载�?            //   disabled: false, // 是否禁用\n            //   icon: '', //  图标\n            //   autofocus: false, // 是否聚焦\n            //   type: 'primary', // primary / success / warning / danger / info / text\n            //   size: 'small', // medium / small / mini\n            //   onclick: (item) => {\n            //     console.log(item)\n            //     this.handleCreate()\n            //   }\n            // },\n            // {\n            //   text: '导出',\n            //   onclick: (item) => {\n            //     console.log(item)\n            //     this.handleExport()\n            //   }\n            // },\n            {\n              text: \"批量导出\",\n              onclick: (item) => {\n                console.log(item);\n                this.handleAllExport();\n              },\n            },\n          ],\n        },\n        // 表格\n        loading: false,\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        tableColumns: [\n          // {\n          //   width: 50,\n          //   otherOptions: {\n          //     type: 'selection',\n          //     align: 'center'\n          //   }\n          // },\n          {\n            width: 60,\n            label: \"序号\",\n            otherOptions: {\n              type: \"index\",\n              align: \"center\",\n            }, // key\n            // otherOptions: {\n            //   width: 180, // 宽度\n            //   fixed: 'left', // left, right\n            //   align: 'center' //\tleft/center/right\n            // }\n          },\n          {\n            label: \"告警时间\",\n            key: \"Time\",\n            otherOptions: {\n              fixed: 'left'\n            },\n          },\n          {\n            label: \"告警编号\",\n            key: \"WId\",\n            width: 160,\n          },\n          {\n            label: \"告警事件名称\",\n            key: \"EnvEventName\",\n            otherOptions: {\n              fixed: 'left'\n            },\n          },\n          {\n            label: \"告警设备编号\",\n            key: \"EId\",\n          },\n          // {\n          //   label: '告警事件名称',\n          //   key: 'EqtName',\n          // },\n          {\n            label: \"告警类型\",\n            key: \"Type\",\n            width: 90,\n          },\n          {\n            label: \"触发�?,\n            key: \"TriggerItem\",\n            width: 90,\n          },\n          {\n            label: \"触发�?,\n            key: \"WarningValue\",\n            width: 90,\n          },\n          {\n            label: \"安装位置\",\n            key: \"Position\",\n          },\n          {\n            label: \"告警状�?,\n            key: \"HandleStatusStr\",\n          },\n          {\n            label: \"操作�?,\n            key: \"Handler_UserName\",\n          },\n          {\n            label: \"操作时间\",\n            key: \"Handle_Time\",\n          },\n        ],\n        tableData: [],\n        operateOptions: {\n          width: 200,\n        },\n        tableActions: [\n          // {\n          //   actionLabel: '关闭',\n          //   otherOptions: {\n          //     type: 'text'\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handelClose(row)\n          //   }\n          // },\n          {\n            actionLabel: \"查看\",\n            otherOptions: {\n              type: \"text\",\n            },\n            onclick: (index, row) => {\n              this.handleEdit(index, row, \"view\");\n            },\n          },\n          // {\n          //   actionLabel: '编辑',\n          //   otherOptions: {\n          //     type: 'text'\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handleEdit(index, row, 'edit')\n          //   }\n          // },\n          // {\n          //   actionLabel: '删除',\n          //   otherOptions: {\n          //     type: 'text'\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handleDelete(index, row)\n          //   }\n          // }\n        ],\n      },\n    };\n  },\n  computed: {},\n  mounted() {\n    this.init();\n    this.initDeviceType(\"EqtType\", \"EnvironmentEqtType\");\n  },\n  methods: {\n    searchForm(data) {\n      console.log(data);\n      this.customTableConfig.currentPage = 1;\n      this.onFresh();\n    },\n    resetForm() {\n      this.onFresh();\n    },\n    onFresh() {\n      this.GetWarningList();\n    },\n    init() {\n      this.GetWarningList();\n      this.GetWarningType();\n    },\n    async GetWarningList() {\n      this.customTableConfig.loading = true;\n      const res = await GetWarningList({\n        ParameterJson: [\n          {\n            Key: \"\",\n            Value: [null],\n            Type: \"\",\n            Filter_Type: \"\",\n          },\n        ],\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n\n        SortName: \"\",\n        SortOrder: \"\",\n        Search: \"\",\n        Content: \"\",\n        EqtType: \"\",\n        Position: \"\",\n        IsAll: true,\n        ...this.ruleForm,\n      });\n      this.customTableConfig.loading = false;\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\n          ...item,\n          Time: dayjs(item.Time).format(\"YYYY-MM-DD HH:mm:ss\"),\n          EqtNameType: `${item.EqtName}${item.Type}`, // 2023-9-23 erwin add EqtNameType\n        }));\n        console.log(res);\n        this.customTableConfig.total = res.Data.TotalCount;\n      } else {\n        this.$message.error(res.Message);\n      }\n    },\n    async GetWarningType() {\n      const res = await GetWarningType({});\n      if (res.IsSucceed) {\n        console.log(res, \"res\");\n        this.customForm.formItems.find(\n          (item, index) => item.key === \"WarningType\"\n        ).options = [\n          {\n            label: \"全部\",\n            value: \"\",\n          },\n          ...res.Data.map((item) => ({\n            label: item.Type,\n            value: item.Type,\n          })),\n        ];\n        // console.log(res)\n      } else {\n        this.$message.error(res.Message);\n      }\n    },\n    handleCreate() {\n      this.dialogTitle = \"新增\";\n      this.componentsConfig = {\n        disabled: false,\n        title: \"新增\",\n      };\n      this.dialogVisible = true;\n    },\n    // handleDelete(index, row) {\n    //   console.log(index, row)\n    //   console.log(this)\n    //   this.$confirm('该操作将在监测设备档案中删除该设备信�?请确认是否删�?', '删除', {\n    //     type: 'error'\n    //   })\n    //     .then(async(_) => {\n    //       const res = await DeleteEquipment({\n    //         IDs: [row.ID]\n    //       })\n    //       if (res.IsSucceed) {\n    //         this.init()\n    //       } else {\n    //         this.$message.error(res.Message)\n    //       }\n    //     })\n    //     .catch((_) => {})\n    // },\n    handleEdit(index, row, type) {\n      console.log(index, row, type);\n      this.dialogVisible = true;\n      if (type === \"view\") {\n        this.dialogTitle = \"查看\";\n        this.currentComponent = DialogForm;\n        this.componentsConfig = {\n          ID: row.ID,\n          disabled: true,\n          title: \"查看\",\n          row: row,\n        };\n      }\n      // else if (type === 'edit') {\n      //   this.dialogTitle = '编辑'\n      //   this.componentsConfig = {\n      //     ID: row.ID,\n      //     disabled: false,\n      //     title: '编辑'\n      //   }\n      // }\n    },\n    // async handleExport() {\n    //   console.log(this.ruleForm)\n    //   const res = await ExportWarning({\n    //     Content: '',\n    //     EqtType: '',\n    //     Position: '',\n    //     IsAll: false,\n    //     Ids: this.tableSelection.map((item) => item.ID),\n    //     ...this.ruleForm\n    //   })\n    //   if (res.IsSucceed) {\n    //     console.log(res)\n    //     downloadFile(res.Data, '21')\n    //   } else {\n    //     this.$message.error(res.Message)\n    //   }\n    // },\n    async handleAllExport() {\n      const res = await ExportWarning({\n        Content: \"\",\n        EqtType: \"\",\n        Position: \"\",\n        IsAll: true,\n        Ids: [],\n        ...this.ruleForm,\n      });\n      if (res.IsSucceed) {\n        console.log(res);\n        downloadFile(res.Data, \"21\");\n      } else {\n        this.$message.error(res.Message);\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`);\n      this.customTableConfig.pageSize = val;\n      this.init();\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`);\n      this.customTableConfig.currentPage = val;\n      this.init();\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection;\n    },\n    handelClose(row) {\n      if (row.HandleStatusStr == \"关闭\") {\n        this.$message.warning(\"请勿重复操作\");\n      } else {\n        UpdateWarningStatus({ id: row.Id, wid: row.WId, StatusEnum: 2 }).then(\n          (res) => {\n            if (res.IsSucceed) {\n              this.$message.success(\"操作成功\");\n              this.init();\n            } else {\n              this.$message.error(res.Message);\n            }\n          }\n        );\n      }\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.layout{\n  height: calc(100vh - 90px);\n  overflow: auto;\n}\n</style>\n"]}]}