{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\layout\\components\\AppMain.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\layout\\components\\AppMain.vue", "mtime": 1755502545452}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQXBwTWFpbicsCiAgY29tcG9uZW50czoge30sCiAgY29tcHV0ZWQ6IHsKICAgIGNhY2hlZFZpZXdzOiBmdW5jdGlvbiBjYWNoZWRWaWV3cygpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnRhZ3NWaWV3LmNhY2hlZFZpZXdzOwogICAgfSwKICAgIGtleTogZnVuY3Rpb24ga2V5KCkgewogICAgICByZXR1cm4gdGhpcy4kcm91dGUucGF0aDsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["name", "components", "computed", "cachedViews", "$store", "state", "tagsView", "key", "$route", "path"], "sources": ["src/layout/components/AppMain.vue"], "sourcesContent": ["<template>\r\n  <section class=\"app-main\">\r\n    <!-- <transition name=\"fade-transform\" mode=\"out-in\">\r\n      <keep-alive :max=\"10\">\r\n        <router-view :key=\"key\" v-if=\"cachedViews.includes(name)\" />\r\n      </keep-alive>\r\n    </transition>\r\n    <transition name=\"fade-transform\" mode=\"out-in\">\r\n      <router-view :key=\"key\"  v-if=\"!cachedViews.includes(name)\" />\r\n    </transition> -->\r\n    <transition name=\"fade-transform\" mode=\"out-in\">\r\n      <keep-alive :include=\"cachedViews\">\r\n        <router-view :key=\"key\" />\r\n      </keep-alive>\r\n    </transition>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'AppMain',\r\n  components: {},\r\n  computed: {\r\n    cachedViews() {\r\n      return this.$store.state.tagsView.cachedViews\r\n    },\r\n    key() {\r\n      return this.$route.path\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-main {\r\n  /* 50= navbar  50  */\r\n  min-height: calc(100vh - 50px - 42px);\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: hidden;\r\n  background: #f4f5f7;\r\n}\r\n\r\n.fixed-header + .app-main {\r\n  padding-top: 50px;\r\n}\r\n\r\n.hasTagsView {\r\n  .app-main {\r\n    /* 84 = navbar + tags-view = 50 + 34 */\r\n    min-height: calc(100vh - 90px);\r\n  }\r\n\r\n  .fixed-header + .app-main {\r\n    padding-top: 84px;\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n// fix css style bug in open el-dialog\r\n.el-popup-parent--hidden {\r\n  .fixed-header {\r\n    padding-right: 15px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAmBA;EACAA,IAAA;EACAC,UAAA;EACAC,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,WAAA;IACA;IACAI,GAAA,WAAAA,IAAA;MACA,YAAAC,MAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}