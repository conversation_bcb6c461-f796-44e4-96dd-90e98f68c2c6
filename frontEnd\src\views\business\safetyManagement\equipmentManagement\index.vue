<template>
  <div class="app-container abs100 equipmentManagement">
    <custom-layout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="submitForm"
          @resetForm="fetchData"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </custom-layout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      />
    </el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import getGridByCode from '../mixins/index'
import {
  GetEquipmentList,
  MonitoreImportTemplate,
  ExportMonitoreEquipment,
  LookVideo,
  DelEquipment,
  MonitoreEquipmentInfo,
  ExportEquipmentList
} from '@/api/business/safetyManagement'
import DialogForm from './components/dialogForm.vue'
import WatchVideoDialog from './components/watchVideoDialog.vue'
import DeviceInfoDialog from './components/deviceInfoDialog.vue'
import ImportFile from './components/importFile.vue'
import { downloadFile } from '@/utils/downloadFile'
import { GetDictionaryTreeDetailListByCode } from '@/api/sys'
import {
  GetParkArea,
  GetTreeAddress
} from '@/api/business/energyManagement.js'

export default {
  components: {
    CustomLayout,
    CustomTable,
    CustomForm
  },
  mixins: [getGridByCode],
  data() {
    return {
      ruleForm: {
        EquipmentName: '',
        EquipmentNumber: '',
        InstallSite: '',
        EquipmentType: ''
      },
      customForm: {
        formItems: [
          {
            key: 'EquipmentName',
            label: '设备名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'EquipmentNumber',
            label: '设备编码',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'InstallSite',
            label: '设备部署位置',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'EquipmentType',
            label: '设备类型',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          }
        ],
        rules: {},
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: '下载模板',
              onclick: () => {
                this.handleDownTemplate()
              }
            },
            {
              text: '批量导入',
              onclick: () => {
                this.handleImport()
              }
            },
            {
              text: '批量导出',
              onclick: () => {
                this.handleExport()
              }
            },
            {
              text: '批量删除',
              onclick: () => {
                this.handleDelete('batch')
              }
            },
            {
              text: '新增',
              type: 'primary',
              onclick: () => {
                this.handleEdit()
              }
            }
          ]
        },
        // 表格
        pageSizeOptions: [20, 40, 60, 80, 100],
        currentPage: 1,
        pageSize: 20,
        total: 1000,
        tableColumns: [],
        tableData: [],
        operateOptions: {
          align: 'center'
        },
        tableActionsWidth: 220,
        tableActions: [
          {
            actionLabel: '监控链接',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleDeviceInfo(row.Id)
            }
          },
          {
            actionLabel: '编辑',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'edit')
            }
          },
          {
            actionLabel: '删除',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleDelete('single', row.Id)
            }
          },
          {
            actionLabel: '查看视频',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleLookVideo(row.Id)
            }
          }
        ]
      },
      dialogVisible: false,
      dialogTitle: '新增',
      currentComponent: null,
      componentsConfig: {
        Data: {}
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
        },
        fetchData: () => {
          this.fetchData()
        }
      },
      multipleSelection: [],
      Park_Area: ''
    }
  },
  created() {
    this.fetchData()
    this.getGridByCode('equipmentManagement')
    GetParkArea().then((res) => {
      this.Park_Area = res.Data
    })
  },
  async mounted() {
    this.customForm.formItems[3].options =
      await this.getDictionaryDetailListByCode()
  },
  methods: {
    fetchData() {
      GetEquipmentList({
        ...this.ruleForm,
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize
      }).then((res) => {
        if (res.IsSucceed) {
          this.customTableConfig.total = res.Data.TotalCount
          this.customTableConfig.tableData = res.Data.Data
        }
      })
    },
    handleDelete(type, id) {
      if (type == 'batch') {
        if (this.multipleSelection.length == 0) {
          this.$message.warning('请选择数据!')
          return
        } else {
          id = this.multipleSelection.map((item) => item.Id).join(',')
        }
      }
      this.$confirm('确认删除？', {
        type: 'warning'
      })
        .then(async(_) => {
          await DelEquipment({ id }).then((res) => {
            if (res.IsSucceed) {
              this.$message.success('删除成功!')
              this.fetchData()
            } else {
              this.$message.error(res.Message)
            }
          })
        })
        .catch((_) => { })
    },
    handleEdit(index, row, type = 'add') {
      this.currentComponent = DialogForm
      if (type === 'add') {
        this.dialogTitle = '新增'
        this.componentsConfig.Data = {
          Monitore_Equipment_Number: '',
          Monitore_Equipment_Name: '',
          Monitore_Equipment_SN_Number: '',
          Monitore_Equipment_Type: '',
          Pisition: '',
          Monitore_Equipment_Name: '',
          Park_Area: this.Park_Area,
          Site: '',
          Address: '',
          Brand: '',
          Version: '',
          Equipment_Purpose_Catetory: ''
        }
      } else if (type === 'edit') {
        this.dialogTitle = '编辑'
        row.Park_area = [row.Purpose_Catetory, row.Scene, row.Site]
        this.componentsConfig.Data = { ...row, Park_Area: this.Park_Area }
      }
      this.dialogVisible = true
    },
    submitForm(data) {
      this.customTableConfig.currentPage = 1
      this.fetchData()
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.customTableConfig.currentPage = val
      this.fetchData()
    },
    handleSelectionChange(data) {
      console.log(data)
      this.multipleSelection = data
    },
    handleLookVideo(id) {
      this.currentComponent = WatchVideoDialog
      this.dialogTitle = '查看视频'
      this.dialogVisible = true
      this.componentsConfig.Data = id
    },
    handleDeviceInfo(id) {
      this.currentComponent = DeviceInfoDialog
      this.dialogTitle = '监控链接'
      this.dialogVisible = true
      MonitoreEquipmentInfo({ id }).then((res) => {
        if (res.IsSucceed) {
          this.componentsConfig.Data = {
            Mainstream_Code: res.Data.Mainstream_Code,
            Substream_Code: res.Data.Substream_Code,
            Url: res.Data.Url
          }
        } else {
          this.$message.error(res.Message)
        }
      })
    },
    handleDownTemplate() {
      MonitoreImportTemplate({ code: 'equipmentManagement' }).then((res) => {
        if (res.IsSucceed) {
          downloadFile(res.Data, '安防监控设备管理导入模板')
        } else {
          this.$message.error(res.Message)
        }
      })
    },
    // handleExport() {
    //   let id = "";
    //   if (this.multipleSelection.length == 0) {
    //     this.$message.warning("请选择数据!");
    //     return;
    //   } else {
    //     id = this.multipleSelection.map((item) => item.Id).join(",");
    //   }
    //   ExportMonitoreEquipment({
    //     code: "equipmentManagement",
    //     id,
    //   }).then((res) => {
    //     if (res.IsSucceed) {
    //       this.$message.success("导出成功");
    //       downloadFile(res.Data, "安防监控设备管理数据");
    //     } else {
    //       this.$message.error(res.Message);
    //     }
    //   });
    // },
    handleExport() {
      const Id = this.multipleSelection.map((item) => item.Id).join(',')
      ExportEquipmentList({
        ...this.ruleForm,
        Id
      }).then((res) => {
        if (res.IsSucceed) {
          this.$message.success('导出成功')
          downloadFile(res.Data, '安防监控设备管理数据')
        } else {
          this.$message.error(res.Message)
        }
      })
    },
    handleImport() {
      this.currentComponent = ImportFile
      this.dialogTitle = '批量导入'
      this.dialogVisible = true
    }
  }
}
</script>
<style scoped lang='scss'>
.equipmentManagement {
  // height: calc(100vh - 90px);
  // overflow: hidden;
}
</style>
