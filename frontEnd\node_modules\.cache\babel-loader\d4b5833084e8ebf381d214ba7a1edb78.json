{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\alarmLinkageSettings\\broadcastAreaSettings.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\alarmLinkageSettings\\broadcastAreaSettings.vue", "mtime": 1755674552412}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "broadcastAreaSettingsDialogForm", "dayjs", "GetAreaSettingList", "BatchEditAreaSetting", "GetAreaEntity", "DeleteAreaSetting", "name", "components", "data", "_this", "currentComponent", "componentsConfig", "Data", "componentsFuns", "open", "dialogVisible", "close", "onFresh", "dialogTitle", "tableSelection", "customTableConfig", "buttonConfig", "buttonList", "text", "type", "onclick", "item", "console", "log", "handleCreate", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "label", "key", "otherOptions", "align", "width", "render", "row", "EquipmentNameArr", "$createElement", "style", "color", "join", "tableData", "operateOptions", "tableActions", "actionLabel", "index", "handleEdit", "handleDelete", "computed", "created", "init", "methods", "handleClose", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "SetWarningStatus", "Status", "Ids", "map", "Id", "sent", "IsSucceed", "$message", "success", "stop", "_this3", "_callee2", "_callee2$", "_context2", "Page", "PageSize", "TotalCount", "error", "Message", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "selection", "_this4", "$confirm", "then", "_ref", "_callee3", "_", "_callee3$", "_context3", "PurposeCatetory", "Scene", "Site", "_x", "apply", "arguments", "catch"], "sources": ["src/views/business/behaviorAnalysis/alarmLinkageSettings/broadcastAreaSettings.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <el-card class=\"box-card\">\r\n      <CustomTable\r\n        :custom-table-config=\"customTableConfig\"\r\n        @handleSizeChange=\"handleSizeChange\"\r\n        @handleCurrentChange=\"handleCurrentChange\"\r\n        @handleSelectionChange=\"handleSelectionChange\"\r\n      />\r\n    </el-card>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\n// import getGridByCode from \"../../safetyManagement/mixins/index\";\r\nimport broadcastAreaSettingsDialogForm from \"./broadcastAreaSettingsDialogForm.vue\";\r\n\r\nimport dayjs from \"dayjs\";\r\nimport {\r\n  GetAreaSettingList,\r\n  BatchEditAreaSetting,\r\n  GetAreaEntity,\r\n  DeleteAreaSetting,\r\n} from \"@/api/business/behaviorAnalysis\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: null,\r\n      componentsConfig: {\r\n        Data: {},\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"编辑\",\r\n      tableSelection: [],\r\n\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"新增\",\r\n              type: \"primary\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: \"报警位置\",\r\n            key: \"Position\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n              width: \"360\",\r\n            },\r\n          },\r\n          {\r\n            label: \"广播设备名称\",\r\n            key: \"EquipmentNameArr\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              if (row.EquipmentNameArr == \"广播失效\") {\r\n                return this.$createElement(\r\n                  \"span\",\r\n                  {\r\n                    style: {\r\n                      color: \"red\",\r\n                    },\r\n                  },\r\n                  row.EquipmentNameArr\r\n                );\r\n              }\r\n              return this.$createElement(\r\n                \"span\",\r\n                {},\r\n                row.EquipmentNameArr.join(\",\")\r\n              );\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: \"center\",\r\n          width: \"180\",\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"修改\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    async handleClose() {\r\n      const res = await SetWarningStatus({\r\n        Status: \"2\",\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.$message.success(\"操作成功\");\r\n        this.onFresh();\r\n      }\r\n    },\r\n\r\n    onFresh() {\r\n      this.GetAreaSettingList();\r\n    },\r\n\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.GetAreaSettingList();\r\n    },\r\n    async GetAreaSettingList() {\r\n      const res = await GetAreaSettingList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.GetAreaSettingList();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.GetAreaSettingList();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = \"新增告警设置\";\r\n      this.componentsConfig = {\r\n        type: \"add\",\r\n      };\r\n      this.dialogVisible = true;\r\n      this.currentComponent = broadcastAreaSettingsDialogForm;\r\n    },\r\n    handleEdit(row) {\r\n      this.dialogVisible = true;\r\n      this.dialogTitle = \"编辑告警设置\";\r\n      this.currentComponent = broadcastAreaSettingsDialogForm;\r\n      this.componentsConfig = {\r\n        type: \"edit\",\r\n        data: row,\r\n      };\r\n    },\r\n    handleDelete(index, row) {\r\n      this.$confirm(\"请确认是否删除？\", \"删除\", {\r\n        type: \"error\",\r\n      })\r\n        .then(async (_) => {\r\n          const res = await DeleteAreaSetting({\r\n            PurposeCatetory: row.PurposeCatetory,\r\n            Scene: row.Scene,\r\n            Site: row.Site,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.init();\r\n            this.$message.success(\"删除成功\");\r\n          } else {\r\n            this.$message.error(res.Message);\r\n          }\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.box-card{\r\n  height: calc(100vh - 190px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA;AACA,OAAAC,+BAAA;AAEA,OAAAC,KAAA;AACA,SACAC,kBAAA,IAAAA,mBAAA,EACAC,oBAAA,EACAC,aAAA,EACAC,iBAAA,QACA;AACA;EACAC,IAAA;EACAC,UAAA;IACAT,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAW,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,gBAAA;MACAC,gBAAA;QACAC,IAAA;MACA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAL,KAAA,CAAAM,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAP,KAAA,CAAAM,aAAA;UACAN,KAAA,CAAAQ,OAAA;QACA;MACA;MACAF,aAAA;MACAG,WAAA;MACAC,cAAA;MAEAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAC,IAAA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAAC,IAAA;cACAC,OAAA,CAAAC,GAAA,CAAAF,IAAA;cACAjB,KAAA,CAAAoB,YAAA;YACA;UACA;QAEA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA,GACA;UACAC,KAAA;UACAC,GAAA;UACAC,YAAA;YACAC,KAAA;YACAC,KAAA;UACA;QACA,GACA;UACAJ,KAAA;UACAC,GAAA;UACAC,YAAA;YACAC,KAAA;UACA;UACAE,MAAA,WAAAA,OAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,gBAAA;cACA,OAAAjC,KAAA,CAAAkC,cAAA,CACA,QACA;gBACAC,KAAA;kBACAC,KAAA;gBACA;cACA,GACAJ,GAAA,CAAAC,gBACA;YACA;YACA,OAAAjC,KAAA,CAAAkC,cAAA,CACA,QACA,IACAF,GAAA,CAAAC,gBAAA,CAAAI,IAAA,KACA;UACA;QACA,EACA;QACAC,SAAA;QACAC,cAAA;UACAV,KAAA;UACAC,KAAA;QACA;QACAU,YAAA,GACA;UACAC,WAAA;UACAb,YAAA;YACAb,IAAA;UACA;UACAC,OAAA,WAAAA,QAAA0B,KAAA,EAAAV,GAAA;YACAhC,KAAA,CAAA2C,UAAA,CAAAX,GAAA;UACA;QACA,GACA;UACAS,WAAA;UACAb,YAAA;YACAb,IAAA;UACA;UACAC,OAAA,WAAAA,QAAA0B,KAAA,EAAAV,GAAA;YACAhC,KAAA,CAAA4C,YAAA,CAAAF,KAAA,EAAAV,GAAA;UACA;QACA;MAEA;IACA;EACA;EACAa,QAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAC,gBAAA;gBACAC,MAAA;gBACAC,GAAA,EAAAb,MAAA,CAAAxC,cAAA,CAAAsD,GAAA,WAAA/C,IAAA;kBAAA,OAAAA,IAAA,CAAAgD,EAAA;gBAAA;cACA;YAAA;cAHAV,GAAA,GAAAG,QAAA,CAAAQ,IAAA;cAIA,IAAAX,GAAA,CAAAY,SAAA;gBACAjB,MAAA,CAAAkB,QAAA,CAAAC,OAAA;gBACAnB,MAAA,CAAA1C,OAAA;cACA;YAAA;YAAA;cAAA,OAAAkD,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA;IACA;IAEA9C,OAAA,WAAAA,QAAA;MACA,KAAAf,kBAAA;IACA;IAEAsD,IAAA,WAAAA,KAAA;MACA;MACA,KAAAtD,kBAAA;IACA;IACAA,kBAAA,WAAAA,mBAAA;MAAA,IAAA8E,MAAA;MAAA,OAAApB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAmB,SAAA;QAAA,IAAAjB,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAiB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAf,IAAA,GAAAe,SAAA,CAAAd,IAAA;YAAA;cAAAc,SAAA,CAAAd,IAAA;cAAA,OACAnE,mBAAA;gBACAkF,IAAA,EAAAJ,MAAA,CAAA5D,iBAAA,CAAAW,WAAA;gBACAsD,QAAA,EAAAL,MAAA,CAAA5D,iBAAA,CAAAY;cACA;YAAA;cAHAgC,GAAA,GAAAmB,SAAA,CAAAR,IAAA;cAIA,IAAAX,GAAA,CAAAY,SAAA;gBACAI,MAAA,CAAA5D,iBAAA,CAAA2B,SAAA,GAAAiB,GAAA,CAAApD,IAAA,CAAAA,IAAA;gBACAoE,MAAA,CAAA5D,iBAAA,CAAAa,KAAA,GAAA+B,GAAA,CAAApD,IAAA,CAAA0E,UAAA;cACA;gBACAN,MAAA,CAAAH,QAAA,CAAAU,KAAA,CAAAvB,GAAA,CAAAwB,OAAA;cACA;YAAA;YAAA;cAAA,OAAAL,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IAEAQ,gBAAA,WAAAA,iBAAAC,GAAA;MACA/D,OAAA,CAAAC,GAAA,iBAAA+D,MAAA,CAAAD,GAAA;MACA,KAAAtE,iBAAA,CAAAY,QAAA,GAAA0D,GAAA;MACA,KAAAxF,kBAAA;IACA;IACA0F,mBAAA,WAAAA,oBAAAF,GAAA;MACA/D,OAAA,CAAAC,GAAA,wBAAA+D,MAAA,CAAAD,GAAA;MACA,KAAAtE,iBAAA,CAAAW,WAAA,GAAA2D,GAAA;MACA,KAAAxF,kBAAA;IACA;IACA2F,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA3E,cAAA,GAAA2E,SAAA;IACA;IACAjE,YAAA,WAAAA,aAAA;MACA,KAAAX,WAAA;MACA,KAAAP,gBAAA;QACAa,IAAA;MACA;MACA,KAAAT,aAAA;MACA,KAAAL,gBAAA,GAAAV,+BAAA;IACA;IACAoD,UAAA,WAAAA,WAAAX,GAAA;MACA,KAAA1B,aAAA;MACA,KAAAG,WAAA;MACA,KAAAR,gBAAA,GAAAV,+BAAA;MACA,KAAAW,gBAAA;QACAa,IAAA;QACAhB,IAAA,EAAAiC;MACA;IACA;IACAY,YAAA,WAAAA,aAAAF,KAAA,EAAAV,GAAA;MAAA,IAAAsD,MAAA;MACA,KAAAC,QAAA;QACAxE,IAAA;MACA,GACAyE,IAAA;QAAA,IAAAC,IAAA,GAAAtC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAqC,SAAAC,CAAA;UAAA,IAAApC,GAAA;UAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAoC,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAlC,IAAA,GAAAkC,SAAA,CAAAjC,IAAA;cAAA;gBAAAiC,SAAA,CAAAjC,IAAA;gBAAA,OACAhE,iBAAA;kBACAkG,eAAA,EAAA9D,GAAA,CAAA8D,eAAA;kBACAC,KAAA,EAAA/D,GAAA,CAAA+D,KAAA;kBACAC,IAAA,EAAAhE,GAAA,CAAAgE;gBACA;cAAA;gBAJAzC,GAAA,GAAAsC,SAAA,CAAA3B,IAAA;gBAKA,IAAAX,GAAA,CAAAY,SAAA;kBACAmB,MAAA,CAAAvC,IAAA;kBACAuC,MAAA,CAAAlB,QAAA,CAAAC,OAAA;gBACA;kBACAiB,MAAA,CAAAlB,QAAA,CAAAU,KAAA,CAAAvB,GAAA,CAAAwB,OAAA;gBACA;cAAA;cAAA;gBAAA,OAAAc,SAAA,CAAAvB,IAAA;YAAA;UAAA,GAAAoB,QAAA;QAAA,CACA;QAAA,iBAAAO,EAAA;UAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;QAAA;MAAA,KACAC,KAAA,WAAAT,CAAA;IACA;EACA;AACA", "ignoreList": []}]}