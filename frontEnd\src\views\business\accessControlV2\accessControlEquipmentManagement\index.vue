<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import DialogForm from './dialogForm.vue'
import ImportFile from './importFile.vue'
import { downloadFile } from '@/utils/downloadFile'
import getGridByCode from '../../safetyManagement/mixins/index'
import addRouterPage from '@/mixins/add-router-page'
import { GetParkArea } from '@/api/business/energyManagement.js'
import { GetEquipmentPageList, DelEntranceEquipment, MJSBExportEntranceEquipmentList, EntranceEquipmentImportTemplate } from '@/api/business/accessControl.js'

export default {
  name: '',
  components: {
    CustomTable,
    CustomForm,
    CustomLayout
  },
  mixins: [getGridByCode, addRouterPage],
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {
        Data: {}
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      tableSelection: [],
      ruleForm: {
        EquipmentName: '',
        EquipmentType: '',
        Position: '',
        Status: '在线'
      },
      customForm: {
        formItems: [
          {
            key: 'EquipmentName',
            label: '设备名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'EquipmentType',
            label: '设备类型',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Position',
            label: '安装位置',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Status',
            label: '状态',
            type: 'select',
            otherOptions: {
              clearable: true
            },
            options: [],
            change: (e) => {
              console.log(e)
            }
          }
        ],
        rules: {
        },
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: '新增',
              type: 'primary', // primary / success / warning / danger / info / text
              size: 'small', // medium / small / mini
              onclick: (item) => {
                console.log(item)
                this.handleCreate()
              }
            },
            {
              text: '导入模板下载',
              onclick: (item) => {
                this.handleDown()
              }
            },
            {
              text: '批量导入',
              onclick: (item) => {
                this.handleImport()
              }
            },
            {
              text: '批量导出',
              onclick: (item) => {
                console.log(item)
                this.handleExport()
              }
            },
          ]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [],
        tableData: [],
        loading: false,
        operateOptions: {
          width: '240px',
          align: 'center'
        },
        tableActionsWidth: 180,
        tableActions: [
          {
            actionLabel: '查看',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleClick(row)
            }
          },
          {
            actionLabel: '编辑',
            otherOptions: {
              type: 'text',
              disabled: false // 是否禁用
            },
            onclick: (index, row) => {
              this.handleEdit(index, row)
            }
          },
          {
            actionLabel: '删除',
            otherOptions: {
              type: 'text',
              disabled: false // 是否禁用
            },
            onclick: (index, row) => {
              this.handleDelete(index, row)
            }
          }
        ]
      },
      Park_Area: '',
      addPageArray: [
        {
          path: this.$route.path + '/detail',
          hidden: true,
          component: () => import('./detail.vue'),
          name: 'accessControlEquipmentManagementDetail',
          meta: { title: `门禁设备详情` }
        }
      ]
    }
  },
  async created() {
    this.init()
    this.customForm.formItems.find(
      (item) => item.key === 'EquipmentType'
    ).options = await this.getDictionaryDetailListByCode('Entrance_Type')
    this.customForm.formItems.find(
      (item) => item.key === 'Status'
    ).options = await this.getDictionaryDetailListByCode('EquipmentStatus')
  },
  methods: {
    searchForm(data) {
      this.customTableConfig.currentPage = 1
      console.log(data)
      this.onFresh()
    },
    resetForm() {
      this.onFresh()
    },
    onFresh() {
      this.getEquipmentList()
    },
    init() {
      this.getGridByCode('AccessControlEquipmentManagement')
      this.getEquipmentList()
    },
    async getEquipmentList() {
      this.customTableConfig.loading = true
      const res = await GetEquipmentPageList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm
      }).finally(() => {
        this.customTableConfig.loading = false
      })
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data
        this.customTableConfig.total = res.Data.TotalCount
      }
    },
    handleCreate() {
      this.dialogTitle = '新增'
      this.dialogVisible = true
      this.currentComponent = DialogForm
      this.componentsConfig.Data = { type: 'isAdd' }
    },
    // 同步设备
    handelSyncEquipment() {
      this.$confirm('此操作将进行设备同步, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'synchronous').loading = true
        SyncEquipment({}).then((res) => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '同步成功!'
            })
            this.getEquipmentList()
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        }).finally(() => {
          this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'synchronous').loading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消同步'
        })
      })
    },
    handleDelete(index, row) {
      console.log(index, row)
      console.log(this)
      this.$confirm('确认删除？', {
        type: 'warning'
      })
        .then(async (_) => {
          const res = await DelEntranceEquipment({
            id: row.Id
          })
          if (res.IsSucceed) {
            this.$message.success('操作成功')
            this.getEquipmentList()
          } else {
            this.$message.error(res.Message)
          }
        })
        .catch((_) => { })
    },
    handleEdit(index, row) {
      console.log(index, row)
      this.currentComponent = DialogForm
      this.dialogTitle = '编辑'
      this.dialogVisible = true
      const Park_area = (row.Scene ?? '') == '' ? [row.Purpose_Catetory] : (row.Site ?? '') == '' ? [row.Purpose_Catetory, row.Scene] : [row.Purpose_Catetory, row.Scene, row.Site]
      this.componentsConfig.Data = { ...row, Park_area, type: 'isEdit' }
    },
    async handleExport() {
      const res = await MJSBExportEntranceEquipmentList({ ...this.ruleForm })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '门禁设备管理数据')
      } else {
        this.$message(res.Message)
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.getEquipmentList()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.getEquipmentList()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
    },
    handleConent(row) {
      this.dialogVisible = true
      this.dialogTitle = '确认设备连接'
      this.currentComponent = DeviceConnect
      this.componentsConfig.Data = { ...row }
    },
    handleClick(row) {
      this.$router.push({ name: 'accessControlEquipmentManagementDetail', query: { pg_redirect: this.$route.name, row: JSON.stringify(row) } })
    },
    async handleDown() {
      let res = await EntranceEquipmentImportTemplate()
      if (res.IsSucceed) {
        downloadFile(res.Data, '门禁设备导入模板')
      } else {
        this.$message(res.Message)
      }
    },
    handleImport() {
      this.dialogTitle = '文件导入'
      this.dialogVisible = true
      this.currentComponent = ImportFile
    }
  }
}
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
.layout{
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
