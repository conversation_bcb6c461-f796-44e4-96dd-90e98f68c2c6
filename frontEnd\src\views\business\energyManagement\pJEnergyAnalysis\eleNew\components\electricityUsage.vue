<template>
  <div
    v-loading="loading"
    class="electricityUsage"
    element-loading-text="加载中..."
  >
    <div class="title">
      <div class="left">用电情况</div>
      <div class="right">不包含重钢工厂</div>
    </div>
    <div class="eleList">
      <div v-for="(item, index) in list" :key="index" class="eleItem" :style="{height: isPhotovoltaic ? '97px' : '155px' }">
        <div class="left">
          <p style="margin-bottom: 12px">{{ item.Key }}</p>
          <p>
            <b>{{ item.Value }}</b><span>度</span>
            <template v-if="item.Key == '总用电'">
              功率因数
              <span
                style="margin-left: 16px; font-size: 16px; color: #666"
              >--</span></template>
            <template v-else> {{ item.Percent }}% </template>
          </p>
        </div>
        <img
          v-if="item.Key == '总用电'"
          class="right"
          src="@/assets/Business/eleIcon1.png"
          alt=""
        >
        <img
          v-if="item.Key == '用电(市电)'"
          class="right"
          src="@/assets/Business/eleIcon2.png"
          alt=""
        >
        <img
          v-if="item.Key == '用电(光伏)'"
          class="right"
          src="@/assets/Business/eleIcon3.png"
          alt=""
        >
      </div>
    </div>
  </div>
</template>

<script>
import { GetElectricUsage } from '@/api/business/energyManagement.js'
export default {
  components: {

  },
  props: {
    isPhotovoltaic: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: true,
      list: []
    }
  },
  computed: {
    parentData() {
      return {
        DateType: this.DateType(),
        StartTime: this.StartTime(),
        randomInteger: this.randomInteger()
      }
    }
  },
  watch: {
    parentData: {
      handler(nv, ov) {
        this.getElectricUsage()
      }
    }
  },
  created() {
    this.getElectricUsage()
  },
  mounted() {

  },
  inject: ['DateType', 'StartTime', 'randomInteger'],
  methods: {
    async getElectricUsage() {
      this.loading = true
      const res = await GetElectricUsage(this.parentData)
      if (res.IsSucceed) {
        this.list = res.Data
      }
      this.loading = false
    }
  }
}
</script>
<style scoped lang='scss'>
.electricityUsage {
  height: 392px;
  background: #fff;
  border-radius: 4px;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  margin-bottom: 16px;
  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .left {
      color: #666;
      font-weight: bold;
      font-size: 16px;
    }
    .right {
      font-size: 12px;
      color: #b8bec8;
    }
  }
  .eleList {
    .eleItem {
      padding: 20px 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border: 1px solid rgba(41, 141, 255, 0.1);
      margin-bottom: 16px;
      background: linear-gradient(
        90deg,
        rgba(41, 141, 255, 0.05) 0%,
        rgba(41, 141, 255, 0) 100%
      );
      &:last-child {
        margin-bottom: 0;
      }
      .left {
        font-size: 16px;
        color: #1d2541;
        span {
          color: #999;
          font-size: 14px;
          margin: 0 16px 0 8px;
        }
        b {
          color: #394f7f;
          font-size: 28px;
        }
      }
      .right {
        height: 54px;
        width: 54px;
      }
    }
  }
}
</style>
