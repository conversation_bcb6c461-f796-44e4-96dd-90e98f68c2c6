{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlPersonnelManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlPersonnelManagement\\index.vue", "mtime": 1755674552408}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAu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file": "index.vue", "sourceRoot": "src/views/business/accessControl/accessControlPersonnelManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        >\r\n          <template #customBtn=\"{ slotScope }\">\r\n            <el-button type=\"text\" @click=\"handleEnable(slotScope)\">{{\r\n              slotScope.Status == \"0\" ? \"禁用\" : \"启用\"\r\n            }}</el-button></template>\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport DialogForm from './dialogForm.vue'\r\nimport DialogFormLook from './dialogFormLook.vue'\r\nimport DialogFormImport from './dialogFormImport.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport {\r\n  GetPersonnelList,\r\n  SubPersonnel,\r\n  EntrancePersonnelInfo,\r\n  UpdateStatus,\r\n  DelPersonnel,\r\n  PersonnelImportTemplate,\r\n  EntrancePersonnelImport,\r\n  ExportEntrancePersonnel,\r\n  GetRole,\r\n  GetDepartment,\r\n  GetCompany,\r\n  GetDictionaryDetailListByCode\r\n} from '@/api/business/accessControl'\r\nimport { GetOssUrl } from '@/api/sys/index'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        P_Name: '',\r\n        Contact_Way: '',\r\n        P_Type: '',\r\n        Position_Name: '',\r\n        P_Unit: '',\r\n        P_Department: ''\r\n        // P_Workshop: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'P_Name', // 字段ID\r\n            label: '姓名', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Contact_Way', // 字段ID\r\n            label: '联系方式', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'P_Type',\r\n            label: '人员类型',\r\n            type: 'select',\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position_Name',\r\n            label: '岗位名称',\r\n            type: 'select',\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'P_Unit',\r\n            label: '所属单位',\r\n            type: 'select',\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'P_Department',\r\n            label: '所属部门',\r\n            type: 'select',\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n          // {\r\n          //   key: 'P_Workshop',\r\n          //   label: '所属车间',\r\n          //   type: 'select',\r\n          //   options: [\r\n          //   ],\r\n          //   change: (e) => {\r\n          //     console.log(e)\r\n          //   }\r\n          // }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '新增',\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: false, // 是否禁用\r\n              icon: '', //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleCreate()\r\n              }\r\n            },\r\n            {\r\n              text: '导入模板下载',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleTemDownload()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导入',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleImport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        operateOptions: {\r\n          width: 200\r\n        },\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          // {\r\n          //   width: 60,\r\n          //   label: '序号',\r\n          //   otherOptions: {\r\n          //     type: 'index',\r\n          //     align: 'center'\r\n          //   } // key\r\n          // },\r\n          {\r\n            label: '员工工号',\r\n            key: 'P_Number'\r\n          },\r\n          {\r\n            label: '姓名',\r\n            key: 'P_Name'\r\n          },\r\n          {\r\n            label: '性别',\r\n            key: 'P_Sex'\r\n          },\r\n          {\r\n            label: '联系方式',\r\n            key: 'Contact_Way'\r\n          },\r\n          {\r\n            label: '人脸照片',\r\n            key: 'Face_picture',\r\n            otherOptions: {\r\n              align: 'center'\r\n            },\r\n            render: (row) => {\r\n              if (row.Face_Picture_Url) {\r\n                return this.$createElement('el-image', {\r\n                  style: {\r\n                    width: '40px',\r\n                    height: '40px'\r\n                  },\r\n                  attrs: {\r\n                    fit: 'cover',\r\n                    src: row.Face_Picture_Url,\r\n                    previewSrcList: [row.Face_Picture_Url]\r\n                  }\r\n                })\r\n              }\r\n            }\r\n          },\r\n          {\r\n            label: '人员类型',\r\n            key: 'P_Type'\r\n          },\r\n          {\r\n            label: '岗位名称',\r\n            key: 'Position_Name'\r\n          },\r\n          {\r\n            label: '所属部门',\r\n            key: 'P_Department'\r\n          },\r\n          // {\r\n          //   label: '所属班组',\r\n          //   key: 'P_Group'\r\n          // },\r\n          // {\r\n          //   label: '所属车间',\r\n          //   key: 'P_Workshop'\r\n          // },\r\n          {\r\n            label: '所属单位',\r\n            key: 'P_Unit'\r\n          },\r\n          {\r\n            label: '状态',\r\n            key: 'Status',\r\n            render: (row) => {\r\n              const text = row.Status === '0' ? '启用' : '禁用'\r\n              return this.$createElement('span', {}, text)\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '修改',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          // {\r\n          //   actionLabel: '启用',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleEnable(index, row, 'edit')\r\n          //   }\r\n          // },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  async created() {\r\n    // 岗位类型\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'Position_Name'\r\n    ).options = await this.initGetRole('Entrance_Type')\r\n    // 所属部门\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'P_Department'\r\n    ).options = await this.initGetDepartment('Entrance_Type')\r\n    // 所属单位\r\n    this.customForm.formItems.find((item) => item.key === 'P_Unit').options =\r\n      await this.initGetCompany('Entrance_Type')\r\n    // 人员类型\r\n    this.customForm.formItems.find((item) => item.key === 'P_Type').options =\r\n      await this.initDeviceType('P_Type')\r\n    this.init()\r\n  },\r\n  methods: {\r\n    // 人员类型\r\n    async initDeviceType(code) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode: code\r\n      })\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    // 岗位类型\r\n    async initGetRole(code) {\r\n      const res = await GetRole({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    // 所属部门\r\n    async initGetDepartment(code) {\r\n      const res = await GetDepartment({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    // 所属单位\r\n    async initGetCompany(code) {\r\n      const res = await GetCompany({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getPersonnelList()\r\n    },\r\n    init() {\r\n      this.getPersonnelList()\r\n    },\r\n    async getPersonnelList() {\r\n      const res = await GetPersonnelList({\r\n        ParameterJson: [\r\n          {\r\n            Key: '',\r\n            Value: [null],\r\n            Type: '',\r\n            Filter_Type: ''\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        Search: '',\r\n        SortName: '',\r\n        SortOrder: '',\r\n        P_Name: '',\r\n        Contact_Way: '',\r\n        P_Type: '',\r\n        Position_Name: '',\r\n        P_Unit: '',\r\n        P_Department: '',\r\n        P_Workshop: '',\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        console.log(res)\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n        this.handelImage(res.Data.Data)\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    async handelImage(data) {\r\n      const promises = data.map(async(v) => {\r\n        let Face_Picture_Url = ''\r\n        if (v.Face_Picture) {\r\n          const imageRes = await GetOssUrl({ url: v.Face_Picture })\r\n          Face_Picture_Url = imageRes.Data\r\n        }\r\n        return (function() {\r\n          const Face_PictureUrl = Face_Picture_Url\r\n          // 使用闭包来捕获变量的当前值\r\n          v.Face_Picture_Url = v.Face_Picture ? Face_PictureUrl : ''\r\n          return v\r\n        })()\r\n      })\r\n\r\n      Promise.all(promises)\r\n        .then((data) => {\r\n          this.customTableConfig.tableData = data\r\n          console.log(data)\r\n        })\r\n        .catch((error) => {\r\n          console.error(error)\r\n        })\r\n    },\r\n\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.componentsConfig = {\r\n        disabled: false,\r\n        title: '新增'\r\n      }\r\n      this.dialogVisible = true\r\n      this.currentComponent = DialogForm\r\n    },\r\n    // 启动 停用\r\n    async handleEnable(row) {\r\n      const text = row.Status === '0' ? '禁用' : '启用'\r\n      const textStatus = row.Status === '0' ? '1' : '0'\r\n      this.$confirm(`确认${text}？`, {\r\n        type: 'warning'\r\n      })\r\n        .then(async(_) => {\r\n          const res = await UpdateStatus({\r\n            id: row.Id,\r\n            status: textStatus\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.init()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n        .catch((_) => {})\r\n    },\r\n    handleDelete(index, row) {\r\n      this.$confirm('确认删除？', {\r\n        type: 'warning'\r\n      })\r\n        .then(async(_) => {\r\n          const res = await DelPersonnel({\r\n            id: row.Id\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.init()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n        .catch((_) => {})\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.currentComponent = DialogFormLook\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: true,\r\n          title: '查看',\r\n          row: row\r\n        }\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        this.currentComponent = DialogForm\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: true,\r\n          title: '编辑',\r\n          row: row\r\n        }\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    async handleTemDownload() {\r\n      const res = await PersonnelImportTemplate({\r\n        code: 'accessControlPersonnelManagement'\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n        this.$message({\r\n          type: 'success',\r\n          message: '下载成功!'\r\n        })\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    async handleExport() {\r\n      if (this.tableSelection.length == 0) {\r\n        this.$message.warning('请选择数据在导出')\r\n        return\r\n      }\r\n      const res = await ExportEntrancePersonnel({\r\n        id: this.tableSelection.map((item) => item.Id).join(','),\r\n        code: 'accessControlPersonnelManagement',\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    async handleImport() {\r\n      this.dialogTitle = '批量导入'\r\n      this.currentComponent = DialogFormImport\r\n      this.componentsConfig = {\r\n        disabled: true,\r\n        title: '批量导入'\r\n      }\r\n      this.dialogVisible = true\r\n      // const res = await ExportEntrancePersonnel({\r\n      //   id: this.tableSelection.map((item) => item.Id),\r\n      //   ...this.ruleForm\r\n      // })\r\n      // if (res.IsSucceed) {\r\n      //   console.log(res)\r\n      //   downloadFile(res.Data, '21')\r\n      // }\r\n    },\r\n    async handleAllExport() {\r\n      const res = await ExportEntrancePersonnel({\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        Ids: [],\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}