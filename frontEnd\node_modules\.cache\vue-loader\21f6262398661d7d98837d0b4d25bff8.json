{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\equipmentAlarm\\index.vue?vue&type=style&index=0&id=606b996a&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\equipmentAlarm\\index.vue", "mtime": 1755674552432}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5lcXVpcG1lbnRBbGFybXsNCiAgLy8gaGVpZ2h0OiBjYWxjKDEwMHZoIC0gOTBweCk7DQogIC8vIG92ZXJmbG93OiBoaWRkZW47DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqWA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/safetyManagement/equipmentAlarm", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 equipmentAlarm\">\r\n    <custom-layout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"submitForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        >\r\n          <template #customBtn=\"{ slotScope }\">\r\n            <el-button\r\n              v-if=\"slotScope.Handle_Status != 2\"\r\n              type=\"text\"\r\n              @click=\"handleChange(slotScope)\"\r\n            >关闭</el-button>\r\n          </template>\r\n        </CustomTable>\r\n      </template>\r\n    </custom-layout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport getGridByCode from '../mixins/index'\r\nimport {\r\n  GetWarningList,\r\n  ExportWaringManage,\r\n  WaringManageInfo,\r\n  UpdateWarningStatus,\r\n  ExportWaringList\r\n} from '@/api/business/safetyManagement'\r\nimport DialogForm from './components/dialogForm.vue'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n\r\nexport default {\r\n  components: {\r\n    CustomLayout,\r\n    CustomTable,\r\n    CustomForm\r\n  },\r\n  mixins: [getGridByCode],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        EquipmentName: '',\r\n        EquipmentDate: ['', ''],\r\n        BeginTime: '',\r\n        EndTime: '',\r\n        Status: '',\r\n        EquipmentType: '',\r\n        Handler: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'EquipmentName',\r\n            label: '设备名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentDate',\r\n            label: '触发告警时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              type: 'daterange',\r\n              rangeSeparator: '至',\r\n              startPlaceholder: '开始日期',\r\n              endPlaceholder: '结束日期',\r\n              clearable: true,\r\n              valueFormat: 'yyyy-MM-dd'\r\n            },\r\n            change: (e) => {\r\n              this.ruleForm.BeginTime = (e ?? [])[0]\r\n              this.ruleForm.EndTime = (e ?? [])[1]\r\n            }\r\n          },\r\n          // {\r\n          //   key: 'EquipmentType',\r\n          //   label: '设备类型',\r\n          //   type: 'select',\r\n          //   options: [],\r\n          //   otherOptions: {\r\n          //     clearable: true\r\n          //   },\r\n          //   change: (e) => {\r\n          //     console.log(e)\r\n          //   }\r\n          // },\r\n          {\r\n            key: 'Handle_Status',\r\n            label: '告警状态',\r\n            type: 'select',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择告警状态'\r\n            },\r\n            options: [\r\n              {\r\n                label: '告警中',\r\n                value: 1\r\n              },\r\n              {\r\n                label: '已关闭',\r\n                value: 2\r\n              }\r\n              // {\r\n              //   label: '已处理',\r\n              //   value: 3\r\n              // }\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Handler',\r\n            label: '处理人',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '批量导出',\r\n              onclick: () => {\r\n                this.handleExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [20, 40, 60, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 1000,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center'\r\n        },\r\n        tableActions: [\r\n          /* {\r\n            actionLabel: '关闭',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleChange(row)\r\n            }\r\n          }, */\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleWatch(row.Id)\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '查看详情',\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n        }\r\n      },\r\n      multipleSelection: []\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchData()\r\n    this.getGridByCode('equipmentAlarm')\r\n  },\r\n  async mounted() {\r\n    // this.customForm.formItems[2].options = await this.getDictionaryDetailListByCode()\r\n  },\r\n  methods: {\r\n    fetchData(data = { Page: 1, PageSize: 20 }) {\r\n      const Data = {\r\n        ...this.ruleForm,\r\n        ...{\r\n          Page: this.customTableConfig.currentPage,\r\n          PageSize: this.customTableConfig.pageSize\r\n        }\r\n      }\r\n      delete Data.EquipmentDate\r\n      GetWarningList(Data).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n          this.customTableConfig.tableData = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    resetForm() {\r\n      this.ruleForm = {\r\n        EquipmentName: '',\r\n        EquipmentDate: ['', ''],\r\n        BeginTime: '',\r\n        EndTime: '',\r\n        Status: '',\r\n        EquipmentType: '',\r\n        Handler: ''\r\n      }\r\n      this.fetchData()\r\n    },\r\n    submitForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData()\r\n    },\r\n    handleSizeChange(val) {\r\n      this.customTableConfig.pageSize = val\r\n      this.fetchData({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: val\r\n      })\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.customTableConfig.currentPage = val\r\n      this.fetchData({ Page: val, PageSize: this.customTableConfig.pageSize })\r\n    },\r\n    handleSelectionChange(data) {\r\n      console.log(data)\r\n      this.multipleSelection = data\r\n    },\r\n    // handleExport() {\r\n    //   let id = ''\r\n    //   if (this.multipleSelection.length == 0) {\r\n    //     this.$message.warning('请选择数据!')\r\n    //     return\r\n    //   } else {\r\n    //     id = this.multipleSelection.map(item => item.Id).join(',')\r\n    //   }\r\n    //   ExportWaringManage({\r\n    //     code: 'equipmentAlarm',\r\n    //     id\r\n    //   }).then(res => {\r\n    //     if (res.IsSucceed) {\r\n    //       this.$message.success('导出成功')\r\n    //       downloadFile(res.Data, '安防告警信息管理数据')\r\n    //     } else {\r\n    //       this.$message.error(res.Message)\r\n    //     }\r\n    //   })\r\n    // },\r\n    // v2 版本导出\r\n    handleExport() {\r\n      const Id = this.multipleSelection.map((item) => item.Id).join(',')\r\n      ExportWaringList({\r\n        ...this.ruleForm,\r\n        Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('导出成功')\r\n          downloadFile(res.Data, '安防告警信息管理数据')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleWatch(id) {\r\n      WaringManageInfo({ id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          // 数据\r\n          this.dialogVisible = true\r\n          this.componentsConfig.Data = res.Data\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleChange(row) {\r\n      console.log(row)\r\n      if (row.HandleStatusStr == '关闭') {\r\n        this.$message.warning('请勿重复操作')\r\n      } else {\r\n        this.$confirm('此操作将关闭该告警, 是否继续?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n          .then(() => {\r\n            UpdateWarningStatus({\r\n              id: row.Id,\r\n              wid: row.WId,\r\n              StatusEnum: 2\r\n            }).then((res) => {\r\n              if (res.IsSucceed) {\r\n                this.$message.success('操作成功')\r\n                this.fetchData()\r\n              } else {\r\n                this.$message.error(res.Message)\r\n              }\r\n            })\r\n          })\r\n          .catch(() => {\r\n            this.$message({\r\n              type: 'info',\r\n              message: '已取消'\r\n            })\r\n          })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang='scss'>\r\n.equipmentAlarm{\r\n  // height: calc(100vh - 90px);\r\n  // overflow: hidden;\r\n}\r\n</style>\r\n"]}]}