<template>
  <div class="app-container abs100 workOrderStatistics_repair">
    <el-row :gutter="12">
      <el-col :span="24">
        <el-card shadow="hover">
          <div class="search_content">
            <span class="label">选择维度</span>
            <el-radio-group
              v-model="yearMonthRadio"
              class="radio"
              @change="yearMonthRadioChange"
            >
              <el-radio-button label="1">年</el-radio-button>
              <el-radio-button label="2">月</el-radio-button>
            </el-radio-group>
            <el-date-picker
              v-model="yearMonthValue"
              class="picker"
              :editable="false"
              :clearable="false"
              :type="yearMonthType"
              @change="yearMonthPickerChange"
            />
            <el-button @click="resetForm">重置</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="12" style="margin-top: 10px">
      <el-col :span="10">
        <el-card shadow="hover">
          <div slot="header" class="header">
            <div class="title_content">
              <span>报修总览</span>
              <el-popover
                placement="top-start"
                title="说明"
                width="420"
                trigger="hover"
              >
                <div>
                  <span>待处理：工单中心工单状态为待处理的所有工单数量</span><br>
                  <span>处理中：工单中心工单状态为处理中，待复检的所有工单数量</span><br>
                  <span>已处理：工单中心工单状态为待评价，处理完成，已关闭的所有工单数量</span>
                </div>
                <img
                  slot="reference"
                  style="width: 16px; height: 16px"
                  src="@/assets/question.png"
                  alt=""
                >
              </el-popover>
            </div>
          </div>
          <div class="repairOverview_content">
            <div class="top">
              <div class="main">
                <img
                  class="left"
                  src="@/assets/totalUmberSorkOrders.png"
                  alt=""
                >
                <div class="right">
                  <span class="text" style="color: #298dff">{{
                    repairOverview.Total
                  }}</span>
                  <span class="value">报修总数</span>
                </div>
              </div>
              <div class="main">
                <img class="left" src="@/assets/repairTime.png" alt="">
                <div class="right">
                  <span class="text" style="color: #ffae2c">{{
                    repairOverview.FixTime
                  }}</span>
                  <div class="value">
                    <span>平均修复时间</span>
                    <el-popover
                      placement="top-start"
                      title="说明"
                      trigger="hover"
                    >
                      <span>平均修复时间=工单总处理时长/工单个数</span>
                      <img
                        slot="reference"
                        style="width: 16px; height: 16px"
                        src="@/assets/question.png"
                        alt=""
                      >
                    </el-popover>
                  </div>
                </div>
              </div>
            </div>
            <div class="bottom">
              <div class="main">
                <img class="left" src="@/assets/pendingProcessing.png" alt="">
                <div class="right">
                  <span class="value">待处理</span>
                  <span class="text" style="color: #ff5e7c">{{
                    repairOverview.Pending
                  }}</span>
                </div>
              </div>
              <div class="main">
                <img class="left" src="@/assets/processing.png" alt="">
                <div class="right">
                  <span class="value">处理中</span>
                  <span class="text" style="color: #298dff">{{
                    repairOverview.Processing
                  }}</span>
                </div>
              </div>
              <div class="main">
                <img class="left" src="@/assets/processed.png" alt="">
                <div class="right">
                  <span class="value">已处理</span>
                  <span class="text" style="color: #00d3a7">{{
                    repairOverview.Processed
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="14">
        <el-card shadow="hover">
          <div slot="header" class="header">
            <span>待办报修</span>
            <el-button
              type="text"
              @click="lookMoreDetail('WorkOrderManagement')"
            >查看更多 <i class="el-icon-arrow-right" /></el-button>
          </div>
          <div style="margin-top: -20px">
            <el-table
              ref="scroll_Table"
              :data="pendingRepairRequestData"
              style="width: 100%"
              height="240"
              :highlight-current-row="false"
              :row-class-name="pendingRepairRequestDataClassName"
              @mouseenter.native="autoScroll(true)"
              @mouseleave.native="autoScroll(false)"
            >
              <el-table-column prop="Order_Name" label="报修名称" />
              <el-table-column prop="State" label="报修状态">
                <template slot-scope="scope">
                  <span
                    :style="{
                      color: getStatusStyle(scope.row).color,
                    }"
                  >
                    {{ getStatusStyle(scope.row).text }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="Warranty_Person" label="提交人员" />
              <el-table-column prop="Create_Date" label="创建时间" />
              <el-table-column label="等待时长">
                <template slot-scope="scope">
                  <span> {{ getWaitingTime(scope.row.Create_Date) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="ErrPercent" label="操作" width="100">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click="
                      openDialog('detail', scope.row, scope.row.Order_Type)
                    "
                  >查看详情</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="12" style="margin-top: 10px">
      <el-col :span="9">
        <el-card shadow="hover">
          <div slot="header" class="header">
            <span>报修故障类型</span>
          </div>
          <div class="equipmentStartupStatus_content">
            <v-chart
              ref="repairFaultTypeRef"
              class="repairFaultType"
              :option="repairFaultTypeOptions"
              :autoresize="true"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="5">
        <el-card shadow="hover">
          <div slot="header" class="header">
            <div class="title_content">
              <span>设备完好率</span>
              <el-popover
                placement="top-start"
                title="说明"
                width="420"
                trigger="hover"
              >
                <div>
                  <span>1.设备维修完好率=月完好天数/月总天数</span><br>
                  <span>2.月完好天数：当天无未处理完成的工单即为完好，每天24：00进行当天工单状态统计</span>
                </div>
                <img
                  slot="reference"
                  style="width: 16px; height: 16px"
                  src="@/assets/question.png"
                  alt=""
                >
              </el-popover>
            </div>
          </div>
          <div class="equipmentIntegrityRate_content">
            <div style="width: 60%; height: 100%">
              <v-chart
                ref="equipmentIntegrityRateRef"
                class="equipmentIntegrityRate"
                :option="equipmentIntegrityRateOptions"
                :autoresize="true"
              />
            </div>
            <div class="equipmentIntegrityRatelists">
              <div class="equipmentIntegrityRatelist">
                <span class="label">完好率</span>
                <span
                  class="value"
                  style="color: #00d3a7"
                >{{ equipmentIntegrityRate.ServiceabilityRate }}%</span>
              </div>
              <div class="equipmentIntegrityRatelist">
                <span class="label">统计天数</span>
                <span class="value" style="color: #298dff">{{
                  equipmentIntegrityRate.StatisticsDays
                }}</span>
              </div>
              <div class="equipmentIntegrityRatelist">
                <span class="label">完好天数</span>
                <span class="value" style="color: #298dff">{{
                  equipmentIntegrityRate.ServiceabilityDays
                }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="5">
        <el-card shadow="hover">
          <div slot="header" class="header">
            <div class="title_content">
              <span>设备故障率排行</span>
              <el-popover
                placement="top-start"
                title="说明"
                width="420"
                trigger="hover"
              >
                <div>
                  <span>代表单位时间内设备的故障次数多少，计算方式如下： 故障率=
                    累计故障次数/设备开机总时间h×100%</span>
                </div>
                <img
                  slot="reference"
                  style="width: 16px; height: 16px"
                  src="@/assets/question.png"
                  alt=""
                >
              </el-popover>
            </div>
          </div>
          <div class="productionEquipmentLoadRateRanking_content">
            <div
              v-for="(item, index) in equipmentFailureRateRanking"
              :key="index"
              class="item"
            >
              <div class="top">
                <span>{{ item.EquipName }}</span>
                <span>{{ item.FailureRate }}</span>
              </div>
              <el-progress
                class="bottom"
                :percentage="item.FailureRate"
                :show-text="false"
              />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="5">
        <el-card shadow="hover">
          <div slot="header" class="header">
            <span>报修满意度</span>
          </div>
          <div class="repairSatisfaction_content">
            <div style="width: 60%; height: 100%">
              <v-chart
                ref="repairSatisfactionRef"
                class="repairSatisfaction"
                :option="repairSatisfactionOptions"
                :autoresize="true"
              />
            </div>
            <div class="repairSatisfactionlists">
              <div class="repairSatisfactionlist">
                <span class="label">处理报修总数</span>
                <span class="value" style="color: #298dff">{{
                  repairSatisfactionConfig.Total
                }}</span>
              </div>
              <div class="repairSatisfactionlist" style="margin-top: 20px">
                <span class="label">最高满意度</span>
                <span
                  class="value"
                  style="color: #00d3a7"
                >{{ repairSatisfactionConfig.Max }} 分</span>
              </div>
              <div class="repairSatisfactionlist">
                <span class="label">最低满意度</span>
                <span
                  class="value"
                  style="color: #ff902c"
                >{{ repairSatisfactionConfig.Min }} 分</span>
              </div>
              <div class="repairSatisfactionlist">
                <span class="label">综合满意度</span>
                <span
                  class="value"
                  style="color: #298dff"
                >{{ repairSatisfactionConfig.Avg }} 分</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="12" style="margin-top: 10px">
      <el-col :span="14">
        <el-card shadow="hover">
          <div slot="header" class="header">
            <div class="title_content">
              <span>报修响应</span>
              <el-popover
                placement="top-start"
                title="说明"
                width="420"
                trigger="hover"
              >
                <div>
                  <span>1.响应及时率：计算工单创建时间到接单时间的时长，该时间越短则代表处理人员的响应速度越快。</span><br>
                  <span>2.响应超时：接单响应时间超过 1小时
                    的工单计算为响应超时。</span>
                </div>
                <img
                  slot="reference"
                  style="width: 16px; height: 16px"
                  src="@/assets/question.png"
                  alt=""
                >
              </el-popover>
            </div>
          </div>
          <div class="repairResponse_content">
            <div class="repairResponselists">
              <div class="repairResponselist">
                <span class="label">报修总数</span>
                <span class="value" style="color: #298dff">{{
                  repairResponseConfig.Total
                }}</span>
              </div>
              <div class="repairResponselist">
                <span class="label">响应及时</span>
                <span class="value" style="color: #298dff">{{
                  repairResponseConfig.Timely
                }}</span>
              </div>
              <div class="repairResponselist">
                <span class="label">响应超时</span>
                <span class="value" style="color: #ff902c">{{
                  repairResponseConfig.Timeout
                }}</span>
              </div>
            </div>
            <v-chart
              ref="repairResponseRef"
              class="repairResponse"
              :option="repairResponseOptions"
              :autoresize="true"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="10">
        <el-card shadow="hover">
          <div slot="header" class="header">
            <span>报修处理人员完成排名</span>
          </div>
          <div class="equipmentFailureTrend_content" style="margin-top: -20px">
            <el-table
              :data="repairProcessingPersonnelCompleteRankingData"
              style="width: 100%"
              height="475"
              :highlight-current-row="false"
              :row-class-name="
                repairProcessingPersonnelCompleteRankingDataClassName
              "
            >
              <!-- ref="scroll_Table"
              @mouseenter.native="autoScroll(true)"
              @mouseleave.native="autoScroll(false)" -->
              <el-table-column label="排名" width="100">
                <template slot-scope="scope">
                  <div
                    v-if="scope.$index < 3"
                    class="tablenumber"
                    :style="{
                      backgroundImage:
                        'url(' +
                        require(`../../../../../assets/no_${
                          scope.$index + 1
                        }.png`) +
                        ')',
                    }"
                  >
                    <span> {{ scope.$index + 1 }}</span>
                  </div>
                  <div v-else class="tablenumber">
                    <span> {{ scope.$index + 1 }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="Name" label="姓名" width="100" />
              <el-table-column prop="Count" sortable label="数量" width="100" />
              <el-table-column prop="Duration" sortable label="用时" />
              <el-table-column
                prop="Timely"
                sortable
                label="响应及时率"
                width="120"
              />
              <el-table-column
                prop="Satisfaction"
                sortable
                label="综合满意度"
                width="120"
              />
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="12" style="margin-top: 10px">
      <el-col :span="9">
        <el-card shadow="hover">
          <div slot="header" class="header">
            <span>各车间报修情况</span>
          </div>
          <div class="maintenanceWorkOrderProcessingStatus_content">
            <v-chart
              ref="repairStatusEachWorkshopRef"
              class="repairStatusEachWorkshop"
              :option="repairStatusEachWorkshopOptions"
              :autoresize="true"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="15">
        <el-card shadow="hover">
          <div slot="header" class="header">
            <span>各车间报修趋势</span>
          </div>
          <div class="repairStatusEachWorkshop_content">
            <v-chart
              ref="trendRepairReportsVariousWorkshopsRef"
              class="trendRepairReportsVariousWorkshops"
              :option="trendRepairReportsVariousWorkshopsOptions"
              :autoresize="true"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <editDialog ref="editDialog" @refresh="initData" />
  </div>
</template>

<script>
import {
  GetWorkOrderManageList,
  GetWorkorderStatistics,
  GetTimeoutStatistics,
  GetSatisfactionStatistics,
  GetProcessedRank,
  GetWorkShopCase,
  GetWorkOrderTrend,
  GetWorkOrderErrorType,
  GetEquipFailureRateRank,
  GetDeviceServiceabilityRate
} from '@/api/business/equipmentManagement'
import dayjs from 'dayjs'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart, LineChart, PieChart, GaugeChart } from 'echarts/charts'

import {
  GridComponent,
  LegendComponent,
  TooltipComponent,
  TitleComponent,
  DataZoomComponent,
  ToolboxComponent
} from 'echarts/components'

import editDialog from '@/views/business/maintenanceAndUpkeep/workOrderManagement/editDialog.vue'
use([
  CanvasRenderer,
  BarChart,
  LineChart,
  PieChart,
  GaugeChart,
  DataZoomComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  ToolboxComponent
])
export default {
  name: 'EquipmentAnalysis',
  components: {
    VChart,
    editDialog
  },
  mixins: [],
  data() {
    return {
      // 查看更多跳转列表
      jumpUrlList: [],
      // 待办报修
      pendingRepairRequestStatus: [
        {
          text: '待处理',
          value: '0',
          color: '#FF5E7C'
        },
        {
          text: '处理中',
          value: '1',
          color: '#298DFF'
        },
        {
          text: '待复检',
          value: '2',
          color: '#FF902C'
        },
        {
          text: '待评价',
          value: '3',
          color: '#298DFF'
        },
        {
          text: '处理完成',
          value: '4',
          color: '#00D3A7'
        },
        {
          text: '已关闭',
          value: '5',
          color: '#333333'
        }
      ],
      pendingRepairRequestData: [],
      repairOverview: {},
      yearMonthRadio: '2',
      yearMonthType: 'month',
      yearMonthValue: dayjs(new Date()).format('YYYY-MM'),
      scrolltimer: '', // 自动滚动的定时任务
      // 各车间报修趋势
      trendRepairReportsVariousWorkshopsOptions: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          top: '0',
          right: '0',
          itemWidth: 16,
          itemHeight: 8,
          itemGap: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        color: ['#4EBF8B', '#66CBF0', '#298DFF', '#FF902C'],
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        yAxis: [
          {
            type: 'value',
            position: 'left',
            logBase: 10
          }
        ],
        series: [
          // {
          //   name: "一车间",
          //   type: "bar",
          //   barWidth: 20,
          //   stack: "vehicle",
          //   emphasis: {
          //     focus: "series",
          //   },
          //   data: [],
          // },
          // {
          //   name: "二车间",
          //   type: "bar",
          //   stack: "vehicle",
          //   emphasis: {
          //     focus: "series",
          //   },
          //   data: [],
          // },
          // {
          //   name: "配送中心",
          //   type: "bar",
          //   stack: "vehicle",
          //   emphasis: {
          //     focus: "series",
          //   },
          //   data: [],
          // },
          // {
          //   name: "响应及时率",
          //   type: "line",
          //   smooth: true,
          //   symbol: "none",
          //   yAxisIndex: 1,
          //   tooltip: {
          //     valueFormatter: function (value) {
          //       return value + " %";
          //     },
          //   },
          //   data: [],
          // },
        ]
      },
      // 各车间报修情况
      repairStatusEachWorkshopOptions: {
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return ` ${params.name} ${params.percent}%  `
          }
        },
        color: ['#4EBF8B', '#298DFF', '#51A1FD'],
        legend: {
          orient: 'vertical',
          right: '0',
          bottom: 'center',
          itemWidth: 12,
          itemHeight: 6,
          textStyle: {
            color: 'rgba(34, 40, 52, 0.65)'
          },
          textStyle: {
            rich: {
              labelMark: {
                width: 60,
                color: '#222834'
              },
              valueMark: {
                width: 40,
                color: '#66CBF0'
              },
              percentMark: {
                width: 40,
                color: '#298DFF'
              }
            }
          }
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            right: 150,
            data: [],
            labelLine: {
              // 设置延长线的长度
              normal: {
                length: 5, // 设置延长线的长度
                length2: 10, // 设置第二段延长线的长度
                lineStyle: {
                  color: 'rgba(194, 203, 226, 1)'
                }
              }
            },
            label: {
              normal: {
                // formatter: '{d}%, {c} \n\n',
                formatter: ' {c|{b}}  {per|{d}%} \n{hr|}\n{a|}', // 这里最后另一行设置了一个空数据是为了能让延长线与hr线对接起来
                padding: [0, -4], // 取消hr线跟延长线之间的间隙
                rich: {
                  a: {
                    color: '#999',
                    lineHeight: 20, // 设置最后一行空数据高度，为了能让延长线与hr线对接起来
                    align: 'center'
                  },
                  hr: {
                    // 设置hr是为了让中间线能够自适应长度
                    borderColor: 'rgba(194, 203, 226, 1)', // hr的颜色为auto时候会主动显示颜色的
                    width: '105%',
                    borderWidth: 0.5,
                    height: 0.5
                  },
                  per: {
                    // 用百分比数据来调整下数字位置，显的好看些。如果不设置，formatter最后一行的空数据就不需要
                    padding: [4, 0],
                    // color: "rgba(194, 203, 226, 1)",
                    color: function(params) {
                      console.log(params, 'params-------------')
                      // 通过数据项的颜色来设置文字颜色
                      return params.color
                    }
                  }
                }
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      },
      // 报修故障类型
      repairFaultTypeOptions: {
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return ` ${params.name} ${params.percent}%  `
          }
        },
        color: ['#4EBF8B', '#298DFF', '#51A1FD'],
        legend: {
          orient: 'vertical',
          right: '0',
          bottom: 'center',
          itemWidth: 12,
          itemHeight: 6,
          textStyle: {
            color: 'rgba(34, 40, 52, 0.65)'
          },
          formatter: function(name) {
            return `${name}`
          }
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            // right: 100,
            data: [],
            labelLine: {
              // 设置延长线的长度
              normal: {
                length: 5, // 设置延长线的长度
                length2: 10, // 设置第二段延长线的长度
                lineStyle: {
                  color: 'rgba(194, 203, 226, 1)'
                }
              }
            },
            label: {
              normal: {
                // formatter: '{d}%, {c} \n\n',
                formatter: ' {c|{b}}  {per|{d}%} \n{hr|}\n{a|}', // 这里最后另一行设置了一个空数据是为了能让延长线与hr线对接起来
                padding: [0, -4], // 取消hr线跟延长线之间的间隙
                rich: {
                  a: {
                    color: '#999',
                    lineHeight: 20, // 设置最后一行空数据高度，为了能让延长线与hr线对接起来
                    align: 'center'
                  },
                  hr: {
                    // 设置hr是为了让中间线能够自适应长度
                    borderColor: 'rgba(194, 203, 226, 1)', // hr的颜色为auto时候会主动显示颜色的
                    width: '105%',
                    borderWidth: 0.5,
                    height: 0.5
                  },
                  per: {
                    // 用百分比数据来调整下数字位置，显的好看些。如果不设置，formatter最后一行的空数据就不需要
                    padding: [4, 0]
                  }
                }
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      },
      // 报修响应
      repairResponseConfig: {},
      repairResponseOptions: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          top: '0',
          right: '0',
          itemWidth: 16,
          itemHeight: 8,
          itemGap: 10
        },
        color: ['#298DFF', '#FF902C', '#00D3A7'],
        xAxis: [
          {
            type: 'category',
            data: [],
            axisPointer: {
              type: 'shadow'
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            type: 'value'
            // min: 0,
            // max: 250,
            // interval: 50,
            // axisLabel: {
            //   formatter: "{value} ml",
            // },
          },
          {
            type: 'value',
            // min: 0,
            // max: 25,
            // interval: 5,
            axisLabel: {
              formatter: '{value} %'
            }
          }
        ],
        series: [
          {
            name: '响应及时数',
            type: 'bar',
            barWidth: 20,
            stack: 'vehicle',
            tooltip: {
              valueFormatter: function(value) {
                return value
              }
            },
            data: []
          },
          {
            name: '响应超时数',
            type: 'bar',
            barWidth: 20,
            stack: 'vehicle',
            tooltip: {
              valueFormatter: function(value) {
                return value
              }
            },
            data: []
          },
          {
            name: '响应及时率',
            type: 'line',
            smooth: true,
            symbol: 'none',
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: function(value) {
                return value + ' %'
              }
            },
            data: []
          }
        ]
      },
      // 报修满意度
      repairSatisfactionConfig: {},
      repairSatisfactionOptions: {
        series: []
      },
      // 设备完好率
      equipmentIntegrityRate: {},
      equipmentIntegrityRateOptions: {
        tooltip: {
          show: false
        },
        series: [
          {
            // 外圆
            silent: false,
            type: 'gauge',
            zlevel: 2,
            startAngle: 0,
            endAngle: 360,
            clockwise: true,
            radius: '75%',
            splitNumber: 5,
            avoidLabelOverlap: false,
            axisLine: {
              show: true
              // lineStyle: {
              //   color: [
              //     [80 / 100, "rgba(0, 211, 167, 0.3)"],
              //     [1, "#f0f2f8"],
              //   ],
              //   width: 16,
              // },
            },
            itemStyle: {
              color: 'rgba(255,255,255,0)'
            },
            progress: {
              show: true
            },
            axisTick: {
              show: true,
              splitNumber: 1,
              distance: -16,
              lineStyle: {
                color: '#ffffff',
                width: 3
              },
              length: 20
            }, // 刻度样式
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            pointer: {
              show: false
            },
            title: {
              show: false
            },
            detail: {
              show: false
            }
          },
          {
            // 外圆2
            type: 'pie',
            silent: true,
            center: ['50%', '50%'],
            radius: ['0%', '50%'],
            avoidLabelOverlap: false,
            zlevel: 3,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(0, 211, 167, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(57, 133, 238, 0)'
                  }
                ]
              },
              borderColor: 'rgba(0, 211, 167, 0.2)'
            },
            label: {
              show: true,
              position: 'center',
              formatter: (pamars) => {
                return `0%`
              },
              fontSize: 24,
              color: '#3f4652'
            },
            labelLine: {
              show: false
            },
            data: [1]
          }
        ]
      },
      // 报修处理人员完成排名
      repairProcessingPersonnelCompleteRankingData: [],
      // 获取设备故障率排行
      equipmentFailureRateRanking: []
    }
  },
  activated() {},
  beforeDestroy() {
    this.autoScroll(true)
  },
  mounted() {
    this.initData()

    this.autoScroll()
  },
  methods: {
    // 打开弹框
    openDialog(type, row, orderType) {
      this.$refs.editDialog.handleOpen(type, row, orderType)
    },
    // 初始化加载数据
    initData() {
      // 待办报修
      this.getWorkOrderManageList()
      // 获取工单总览统计
      this.getTimeoutStatistics()
      // 获取工单响应超时统计
      this.getWorkorderStatistics()
      // 获取工单满意度统计
      this.getSatisfactionStatistics()
      // 获取处理人员完成排名
      this.getProcessedRank()

      // 获取各车间工单情况
      this.getWorkShopCase()
      // 获取各车间趋势
      this.getWorkOrderTrend()
      // 获取报修工单故障类型
      this.getWorkOrderErrorType()

      // 获取设备完好率
      this.getEquipFailureRateRank()
      // 获取设备故障率排行
      this.getDeviceServiceabilityRate()
    },
    // 充值表单数据并加载
    resetForm() {
      this.yearMonthType = 'month'
      this.yearMonthValue = dayjs(new Date()).format('YYYY-MM')
      this.initData()
    },
    // 筛选条件
    yearMonthRadioChange(e) {
      if (e == 1) {
        this.yearMonthType = 'year'
        this.yearMonthValue = dayjs(new Date()).format('YYYY')
      } else if (e == 2) {
        this.yearMonthType = 'month'
        this.yearMonthValue = dayjs(new Date()).format('YYYY-MM')
      }
      this.initData()
    },
    // 年 月 切换
    yearMonthPickerChange() {
      this.initData()
    },
    // 待办报修
    async getWorkOrderManageList() {
      const res = await GetWorkOrderManageList({
        model: {
          Date: [],
          Order_Code: '',
          Order_Name: '',
          Create_Date: '',
          Create_EDate: '',
          State: '',
          WorkOrder_Setup_Id: 'jsbx',
          Maintain_Person: '',
          WorkOrder_State: 0,
          Type: 1,
          type: 1
        },
        pageInfo: {
          Page: 1,
          PageSize: 10,
          SortName: 'Create_Date',
          SortOrder: 'DESC'
        }
      })
      this.pendingRepairRequestData = res.Data.Data
    },
    // 获取工单满意度统计
    async getSatisfactionStatistics() {
      const res = await GetSatisfactionStatistics({
        WorkOrderType: 'jsbx',
        DateType: this.yearMonthRadio,
        StartTime: this.getStartTime(this.yearMonthRadio)
      })
      this.repairSatisfactionConfig = res.Data
      const average = res.Data.Avg || 0
      let averageStr = ''
      if (average >= 4) {
        averageStr = '优'
      } else if (average < 4 && average >= 3) {
        averageStr = '良'
      } else if (average < 3 && average >= 2) {
        averageStr = '中'
      } else if (average < 2) {
        averageStr = '差'
      }
      this.repairSatisfactionOptions.series = [
        {
          name: '外部刻度',
          type: 'gauge',
          radius: '100%',
          splitNumber: 20,
          min: 0,
          max: 100,
          startAngle: 225,
          endAngle: -45,
          axisLine: {
            roundCap: true,
            lineStyle: {
              width: 0,
              opacity: 0
            }
          },
          axisLabel: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            length: 3,
            lineStyle: {
              color: '#a9afb8',
              width: 1
            }
          },
          detail: {
            show: false
          },
          pointer: {
            show: false
          }
        },
        {
          name: '内部刻度',
          type: 'gauge',
          radius: '80%',
          splitNumber: 20,
          min: 0,
          max: 100,
          startAngle: 225,
          endAngle: -45,
          title: {
            show: true,
            fontSize: 12,
            color: '#505D6F',
            offsetCenter: ['0', '-20%']
          },
          data: [
            {
              value: [],
              name: '报修处理满意度'
            }
          ],
          detail: {
            valueAnimation: true,
            formatter: () => {
              return averageStr
            },
            fontSize: 14,
            color: '#298DFF',
            offsetCenter: [0, '10%']
          },
          axisLine: {
            roundCap: true,
            lineStyle: {
              width: 20,
              color: [
                [
                  (average * 20) / 100,
                  {
                    type: 'linear',
                    x: 0,
                    y: 1,
                    x2: 0,
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0,
                        color: '#50FFE4'
                      },
                      {
                        offset: 1,
                        color: '#298DFF'
                      }
                    ]
                  }
                ],
                [1, 'rgba(225,225,225,0.4)']
              ]
            }
          },
          axisLabel: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          pointer: {
            show: false
          }
        }
      ]
    },
    // 获取处理人员完成排名
    async getProcessedRank() {
      const res = await GetProcessedRank({
        WorkOrderType: 'jsbx',
        DateType: this.yearMonthRadio,
        StartTime: this.getStartTime(this.yearMonthRadio)
      })
      this.repairProcessingPersonnelCompleteRankingData = res.Data
    },
    // 获取各车间工单情况
    async getWorkShopCase() {
      const res = await GetWorkShopCase({
        WorkOrderType: 'jsbx',
        DateType: this.yearMonthRadio,
        StartTime: this.getStartTime(this.yearMonthRadio)
      })
      console.log(res, 'res')
      const repairStatusEachWorkshopOptions = res.Data.map((item) => ({
        name: item.Label,
        value: item.Value,
        percent: item.Rate
      }))
      this.repairStatusEachWorkshopOptions.series[0].data =
        repairStatusEachWorkshopOptions
      this.repairStatusEachWorkshopOptions.legend.formatter = function(name) {
        const obj = repairStatusEachWorkshopOptions.find(
          (item) => item.name == name
        )
        return `{labelMark|${obj.name}} {valueMark|${obj.value} 次}  {percentMark|${obj.percent} %}`
      }
    },
    // 获取报修工单故障类型
    async getWorkOrderErrorType() {
      const res = await GetWorkOrderErrorType({
        WorkOrderType: 'jsbx',
        DateType: this.yearMonthRadio,
        StartTime: this.getStartTime(this.yearMonthRadio)
      })
      this.repairFaultTypeOptions.series[0].data = res.Data.map((item) => ({
        name: item.Label,
        value: item.Value,
        percent: item.Rate
      }))
    },
    // 获取各车间趋势
    async getWorkOrderTrend() {
      const res = await GetWorkOrderTrend({
        WorkOrderType: 'jsbx',
        DateType: this.yearMonthRadio,
        StartTime: this.getStartTime(this.yearMonthRadio)
      })
      let xAxisData = []
      this.trendRepairReportsVariousWorkshopsOptions.series = res.Data.map(
        (item) => {
          xAxisData = item.ShopData.map((ele) => ele.Label)
          if (item.ShopName == '报修数量') {
            return {
              name: item.ShopName,
              type: 'line',
              smooth: true,
              symbol: 'none',
              data: item.ShopData.map((ele) => ele.Value)
            }
          } else {
            return {
              name: item.ShopName,
              type: 'bar',
              stack: 'vehicle',
              emphasis: {
                focus: 'series'
              },
              data: item.ShopData.map((ele) => ele.Value)
            }
          }
        }
      )
      this.trendRepairReportsVariousWorkshopsOptions.xAxis.data = xAxisData
    },

    // 获取设备完好率
    async getDeviceServiceabilityRate() {
      const res = await GetDeviceServiceabilityRate({
        WorkOrderType: 'jsbx',
        DateType: this.yearMonthRadio,
        StartTime: this.getStartTime(this.yearMonthRadio)
      })
      this.equipmentIntegrityRate = res.Data
      this.equipmentIntegrityRateOptions.series[0].axisLine.lineStyle = {
        color: [
          [res.Data.ServiceabilityRate / 100, 'rgba(0, 211, 167, 0.3)'],
          [1, '#f0f2f8']
        ],
        width: 16
      }
      this.equipmentIntegrityRateOptions.series[1].label.formatter = function(
        pamars
      ) {
        return `${res.Data.ServiceabilityRate}%`
      }
    },
    // 获取设备故障率排行
    async getEquipFailureRateRank() {
      const res = await GetEquipFailureRateRank({
        WorkOrderType: 'jsbx',
        DateType: this.yearMonthRadio,
        StartTime: this.getStartTime(this.yearMonthRadio)
      })
      this.equipmentFailureRateRanking = res.Data
    },

    // 获取工单总览统计
    async getTimeoutStatistics() {
      const res = await GetTimeoutStatistics({
        WorkOrderType: 'jsbx',
        DateType: this.yearMonthRadio,
        StartTime: this.getStartTime(this.yearMonthRadio)
      })
      this.repairResponseConfig = res.Data
      this.repairResponseOptions.xAxis[0].data = res.Data.List.map(
        (item) => item.Date
      )
      this.repairResponseOptions.series[0].data = res.Data.List.map(
        (item) => item.Timely
      )
      this.repairResponseOptions.series[1].data = res.Data.List.map(
        (item) => item.Timeout
      )
      this.repairResponseOptions.series[2].data = res.Data.List.map(
        (item) => item.Percent
      )
    },
    // 获取工单响应超时统计
    async getWorkorderStatistics() {
      const res = await GetWorkorderStatistics({
        WorkOrderType: 'jsbx',
        DateType: this.yearMonthRadio,
        StartTime: this.getStartTime(this.yearMonthRadio)
      })
      this.repairOverview = res.Data
    },
    // 获取等待时长
    getWaitingTime(date) {
      const startDate = new Date(date)
      var endDate = new Date() // 获取当前时间
      var difference = Math.abs(endDate - startDate)
      var days = Math.floor(difference / (1000 * 60 * 60 * 24))
      var hours = Math.floor(
        (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      )
      var minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
      var formattedDifference = ''

      if (days > 0) {
        formattedDifference += days + '天'
      }
      if (hours > 0) {
        formattedDifference += hours + '小时'
      }
      if (minutes > 0 || (days === 0 && hours === 0)) {
        formattedDifference += minutes + '分钟'
      }
      return formattedDifference
    },
    // 获取状态样式
    getStatusStyle(row) {
      return this.pendingRepairRequestStatus.find(
        (item) => item.value == row.State
      )
    },
    // 获取日期时间
    getStartTime(type) {
      if (type == 1) {
        return dayjs(this.yearMonthValue).format('YYYY')
      } else if (type == 2) {
        return dayjs(this.yearMonthValue).format('YYYY-MM')
      }
    },
    // 设置表格自动滚动
    autoScroll(stop) {
      const table = this.$refs.scroll_Table
      // 拿到表格中承载数据的div元素
      const divData = table.$refs.bodyWrapper
      // 拿到元素后，对元素进行定时增加距离顶部距离，实现滚动效果(此配置为每100毫秒移动1像素)
      if (stop) {
        // 再通过事件监听，监听到 组件销毁 后，再执行关闭计时器。
        window.clearInterval(this.scrolltimer)
      } else {
        this.scrolltimer = window.setInterval(() => {
          // 元素自增距离顶部1像素
          divData.scrollTop += 2
          // 判断元素是否滚动到底部(可视高度+距离顶部=整个高度)
          if (
            divData.clientHeight + divData.scrollTop ==
            divData.scrollHeight
          ) {
            // 重置table距离顶部距离
            divData.scrollTop = 0
            // 重置table距离顶部距离。值=(滚动到底部时，距离顶部的大小) - 整个高度/2
            // divData.scrollTop = divData.scrollTop - divData.scrollHeight / 2
          }
        }, 120) // 滚动速度
      }
    },

    // 设置表格颜色
    repairProcessingPersonnelCompleteRankingDataClassName({ row, rowIndex }) {
      if (this.isEvenOrOdd(rowIndex + 1)) {
        return 'row-one'
      } else {
        return 'row-two'
      }
    },
    // 设置表格颜色
    pendingRepairRequestDataClassName({ row, rowIndex }) {
      if (this.isEvenOrOdd(rowIndex + 1)) {
        return 'row-one'
      } else {
        return 'row-two'
      }
    },
    //  判断是否是偶数行 还是奇数行
    isEvenOrOdd(number) {
      if (number % 2 === 0) {
        return true
      } else {
        return false
      }
    },
    // 查看更多
    lookMoreDetail() {
      // let Url = this.jumpUrlList.find(
      //   (item) => item.ModuleCode == ModuleCode
      // ).Url;
      const Platform = 'digitalfactory'
      const ModuleId = localStorage.getItem('ModuleId')
      const ModuleCode = localStorage.getItem('ModuleCode')
      // 获取本月的第一天
      const startOfMonth = dayjs().startOf('month')
      // 获取本月的最后一天
      const endOfMonth = dayjs().endOf('month')
      // 输出结果
      // Create_Date:"2024-07-19"
      // Create_EDate:"2024-08-14"
      this.$qiankun.switchMicroAppFn(
        Platform,
        'gzt',
        '7622d042-b114-46a0-b1ba-b5621622f058',
        `/business/maintenanceAndUpkeep/workOrderManagement?State=0&ActiveName=first&isJump=true`
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.workOrderStatistics_repair {
  // padding: 10px 15px;
  // height: calc(100vh - 90px);
  overflow-y: auto;
  .header {
    display: flex;
    // align-items: center;
    justify-content: space-between;
    height: 22px;
    > span {
      font-weight: bold;
    }
    .title_content {
      display: flex;
      font-weight: bold;
      flex-direction: row;
      align-items: center;
      span {
        margin-right: 10px;
      }
    }
  }

  .search_content {
    display: flex;
    flex-direction: row;
    align-items: center;
    .label {
      margin-right: 10px;
    }
    .radio {
      margin-right: 10px;
    }
    .picker {
      margin-right: 10px;
    }
  }

  .repairSatisfaction_content {
    height: 220px;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    .repairSatisfactionlists {
      display: flex;
      flex-direction: column;
      .repairSatisfactionlist {
        padding: 2px 0px;
        display: flex;
        flex-direction: row;
        align-items: center;
        .label {
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          margin-right: 10px;
        }
        .value {
          font-weight: bold;
          font-size: 18px;
        }
      }
    }
  }

  .repairOverview_content {
    height: 220px;
    .bottom {
      margin-top: 20px;
      display: grid;
      width: 100%;
      grid-template-columns: repeat(3, calc(33% - 10px));
      grid-gap: 20px; /* 设置间距 */
      .main {
        border-radius: 8px 8px 8px 8px;
        padding: 10px 15px;
        background: #fafdff;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        .left {
          width: 70px;
          height: 70px;
        }
        .right {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .text {
            font-weight: bold;
            font-size: 24px;
            margin-top: 10px;
          }
          .value {
            font-weight: 400;
            font-size: 14px;
            color: #666666;
          }
        }
      }
    }
    .top {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-around;
      background: #fafdff;
      border-radius: 8px 8px 8px 8px;
      padding: 10px 15px;
      .main {
        display: flex;
        flex-direction: row;
        .left {
          width: 80px;
          height: 80px;
        }
        .right {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .text {
            font-weight: bold;
            font-size: 24px;
          }
          .value {
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            margin-top: 10px;
            display: flex;
            flex-direction: row;
            align-items: center;
            > span {
              margin-right: 10px;
              display: flex;
              flex-direction: row;
              align-items: center;
            }
          }
        }
      }
    }
  }

  .maintenanceWorkOrderProcessingStatus_content {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 300px;
    .left {
      width: 50%;
      display: flex;
      flex-direction: column;
      padding: 0px 18px;
      .title {
        color: rgba(34, 40, 52, 0.85);
        font-size: 18px;
        font-weight: bold;
        padding: 18px 0px;
      }
      .left_content {
        background-color: #fafdff;
        padding: 10px 15px;
      }
    }
    .right {
      width: 50%;
      display: flex;
      flex-direction: column;

      padding: 0px 18px;
      .title {
        color: rgba(34, 40, 52, 0.85);
        font-size: 18px;
        font-weight: bold;
        padding: 18px 0px;
      }
      .right_content {
        background-color: #fafdff;
        padding: 10px 15px;
      }
    }
    .item {
      width: 100%;
      display: flex;
      flex-direction: column;
      margin-bottom: 20px;
      .top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6px;
        font-size: 14px;
        .left {
          color: #333333;
          font-size: 14px;
        }
        .right_color_one {
          color: #298dff;
        }
        .right_color_two {
          color: #00d3a7;
        }
        .right {
          display: flex;
          flex-direction: row;
          .one {
            margin-right: 14px;
          }
          .two {
            width: 36px;
            display: block;
          }
        }
      }
    }
  }
  .productionEquipmentLoadRateRanking_content {
    display: flex;
    align-items: center;
    flex-direction: column;
    height: 220px;
    overflow-y: scroll;
    scrollbar-width: none;
    // scrollbar-color: transparent transparent;
    .item {
      width: 100%;
      display: flex;
      flex-direction: column;
      margin-bottom: 20px;
      .top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6px;
        font-size: 14px;
      }
    }
  }

  .equipmentIntegrityRate_content {
    height: 220px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .equipmentIntegrityRatelists {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 40%;
      .equipmentIntegrityRatelist {
        padding: 2px 0px;
        display: flex;
        flex-direction: row;
        margin-right: 20px;
        .label {
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          margin-right: 10px;
        }
        .value {
          font-weight: bold;
          font-size: 18px;
        }
      }
    }
  }

  .repairResponse_content {
    height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .repairResponselists {
      margin-top: -40px;
      background: #fafdff;
      border-radius: 8px 8px 8px 8px;
      padding: 10px 30px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-around;
      width: 60%;
      margin-bottom: 10px;
      .repairResponselist {
        padding: 2px 0px;
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-right: 20px;
        .label {
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          margin-right: 10px;
        }
        .value {
          font-weight: bold;
          font-size: 18px;
        }
      }
    }
  }
  .equipmentStartupStatus_content {
    height: 220px;
  }
  .equipmentFailureTrend_content {
    height: 320px;
    .tablenumber {
      width: 30px;
      height: 23px;
      background-size: 100%;
      background-repeat: no-repeat;
      display: flex;
      align-items: center;
      justify-content: center;
      > span {
        margin-top: 10px;
        font-weight: 500;
      }
    }
  }
  .repairStatusEachWorkshop_content {
    height: 300px;
    .right {
      display: flex;
      flex-direction: row;
      align-items: center;
      .right_num {
        display: flex;
        flex-direction: row;
        align-items: center;
      }
    }
  }

  ::v-deep .el-card__header {
    border-bottom: none !important;
  }
  ::v-deep .el-progress__text {
    font-size: 18px !important;
    color: #666666 !important;
  }
  ::v-deep.el-table .row-one {
    background: rgba(41, 141, 255, 0.03) !important;
  }

  ::v-deep .el-table .row-two {
    background: rgba(255, 255, 255, 1) !important;
  }

  // ::v-deep .el-radio-button__inner {
  //   background-color: #ffffff;
  //   // padding: 6px 32px;
  //   height: 32px;
  //   // line-height: 32px;
  //   width: 80px;
  //   font-size: 14px;
  // }
}
</style>
