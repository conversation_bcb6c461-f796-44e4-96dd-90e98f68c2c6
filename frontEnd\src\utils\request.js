import Vue from 'vue'
import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
// import Vue from 'vue'
import { baseUrl, platformUrl } from '@/utils/baseurl'
import qs from 'qs'

// create an axios instance
// Vue.prototype.$config = window.URLGLOBAL

/* setTimeout(() => {
  delete window.URLGLOBAL // 用完之后删除
}, 500) */
// function baseURL() {
// const regexSYS = /\/Platform\//
// const regexPRO = /\/DF\//
// const baseUrl = regexSYS.test(data) ? window.URLGLOBAL.Platform : regexPRO.test(data) ? window.URLGLOBAL.PRO : window.URLGLOBAL.URL ?? ''
// return baseUrl
// return process.env.VUE_APP_BASE_API
// }

// 创建存储 key 的 集合
const keyMap = new Map()

// 获取每一次请求的key
function getRequestKey(config) {
  const { url, method, data, params } = config
  // 记得这里一定要处理 每次请求都掉会变化的参数(比如每个请求都携带了时间戳),否则二个请求的key不一样
  const key = [url, method, qs.stringify(data), qs.stringify(params)].join('&')
  return key
}

// 判断是否存在该key,不存在则插入
function checkRequest(config, source) {
  if (config.url === '/Platform/User/GetUserEntity' || config.url === '/Platform/Company/GetEntity') {
    return
  }
  const key = getRequestKey(config)
  if (keyMap.has(key)) {
    source.cancel()
  } else {
    keyMap.set(key, config)
  }
}

// 响应完成后,删除key
function deleteRequestKey(config) {
  const key = getRequestKey(config)
  if (keyMap.has(key)) {
    keyMap.delete(key)
  }
}

let baseUrlStr = baseUrl()
function Axios(option) {
  if (option.url === '/Platform/Role/RoleAuthorization') {
    baseUrlStr = platformUrl() // process.env.VUE_APP_PLATFORM_API
  } else {
    baseUrlStr = baseUrl()
  }
  const service = axios.create({
    baseURL: baseUrlStr, // url = base url + request url
    // withCredentials: true, // send cookies when cross-domain requests
    timeout: 60000 * 10 // request timeout
  })

  // request interceptor
  service.interceptors.request.use(
    config => {
      // do something before request is sent
      if (Vue.prototype.$tokenStr) {
        config.headers['Authorization'] = Vue.prototype.$tokenStr
      } else if (store.getters.token || getToken()) {
        // let each request carry token
        // ['X-Token'] is a custom headers key
        // please modify it according to the actual situation
        config.headers['Authorization'] = getToken()
      }
      if (localStorage.getItem('Last_Working_Object_Id')) {
        config.headers.Last_Working_Object_Id = localStorage.getItem('Last_Working_Object_Id')
      }

      // 这里 是拦截的关键,使用 axios CancelToken, 每次请求都重新生成 source 和 cancelToken
      const source = axios.CancelToken.source()
      config.cancelToken = source.token
      // 检查请求
      checkRequest(config, source)
      return config
    },
    error => {
      // do something with request error
      console.log(error) // for debug
      return Promise.reject(error)
    }
  )

  // response interceptor
  service.interceptors.response.use(
    /**
     * If you want to get http information such as headers or status
     * Please return  response => response
     */

    /**
     * Determine the request status by custom code
     * Here is just an example
     * You can also judge the status by HTTP Status Code
     */
    response => {
      const res = response.data
      // if (res.StatusCode === 502 || res.StatusCode === 502 || res.StatusCode === 501 || res.StatusCode === 500) {
      //   // 服务端返回false，同时需要获取数据
      //   // to re-login
      //   // MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {
      //   //   confirmButtonText: 'Re-Login',
      //   //   cancelButtonText: 'Cancel',
      //   //   type: 'warning'
      //   // }).then(() => {
      //   //   store.dispatch('user/resetToken').then(() => {
      //   //     location.reload()
      //   //   })
      //   // })
      //   return res
      // } else if (res.StatusCode === 200 || res.StatusCode === 502 || res.StatusCode === 502 || res.StatusCode === 501 || res.StatusCode === 500) {
      //   // Message({
      //   //   message: res.Message || 'Error',
      //   //   type: 'error',
      //   //   duration: 5 * 1000
      //   // })
      // } else
      if (res.StatusCode === 401) {
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      } else if (res.StatusCode === 502) { // res.StatusCode === 501 ||
        Message({
          message: res.Message || 'Error',
          type: 'error',
          duration: 5 * 1000
        })
      } else {
        if (res.Message !== '操作错误') {
          return res
        } else {
          return null
        }

        // return Promise.reject(new Error(res.Message || ' '))
      }
      // else if (res.StatusCode === 200001) { // res.StatusCode === 501 ||
      //   Message({
      //     message: res.Message || 'Error',
      //     type: 'error',
      //     duration: 5 * 1000
      //   })
      // }
    },
    error => {
      // 拦截掉重复请求的错误,中断promise执行
      if (axios.isCancel(error)) {
        return new Promise(() => { })
      }

      console.log('err' + error) // for debug
      Message({
        message: '请求超时',
        type: 'error',
        duration: 5 * 1000
      })
      return Promise.reject(error)
    }
  )
  // 请求处理
  return new Promise((resolve, reject) => {
    service(option).then(res => {
      resolve(res)
      // 请求成功了但返回没有 data 内容放行到 外层的Promise 的 .catch
      // if (res && res.Data != "") {
      //   resolve(res);
      // } else {
      //   reject(res);
      // }
    }).catch(error => {
      reject(error)
    }).finally(_ => {
      // 删除已完成的请求 key
      deleteRequestKey(option)
    })
  })
}

export default Axios
