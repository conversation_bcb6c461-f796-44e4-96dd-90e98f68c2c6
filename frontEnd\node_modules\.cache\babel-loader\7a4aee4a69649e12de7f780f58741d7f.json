{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\alarmNotification\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\alarmNotification\\index.vue", "mtime": 1755674552429}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "GetModule", "GetTypesByModule", "GetWarnPageList", "GetWarningStatus", "GetWarningModule", "GetWarningType", "CloseWarning", "name", "components", "data", "_this", "ruleForm", "Name", "OperateType", "StartTime", "EndTime", "<PERSON><PERSON><PERSON>", "Type", "Date", "Status", "customForm", "formItems", "key", "label", "type", "otherOptions", "clearable", "width", "change", "e", "console", "log", "options", "getTypesByModule", "disabled", "value", "rangeSeparator", "startPlaceholder", "endPlaceholder", "valueFormat", "length", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "loading", "pageSizeOptions", "currentPage", "pageSize", "total", "height", "tableColumns", "render", "row", "h", "$createElement", "class", "concat", "tableData", "tableActions", "actionLabel", "onclick", "index", "getDetail", "mounted", "getModule", "getWarningStatus", "onFresh", "methods", "_this2", "then", "res", "IsSucceed", "Data", "arr", "for<PERSON>ach", "item", "obj", "push", "unshift", "find", "v", "$message", "message", "Message", "_this3", "_this4", "Value", "searchForm", "resetForm", "fetchData", "_this5", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "_objectSpread", "Page", "PageSize", "TotalCount", "finally", "stop", "handelClose", "_this6", "$confirm", "confirmButtonText", "cancelButtonText", "Id", "catch", "Url", "platform", "ModuleName", "$qiankun", "switchMicroAppFn", "ModuleCode", "ModuleId", "code", "id", "url", "handleSizeChange", "val", "handleCurrentChange"], "sources": ["src/views/business/myWorkBench/alarmNotification/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n        >\r\n          <template #customBtn=\"{slotScope}\"><el-button v-if=\"slotScope.Status == 1\" type=\"text\" @click=\"handelClose(slotScope)\">关闭</el-button></template>\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport { GetModule, GetTypesByModule, GetWarnPageList, GetWarningStatus, GetWarningModule, GetWarningType, CloseWarning } from '@/api/business/myWorkBench'\r\nexport default {\r\n  name: 'MyTasks',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        Name: '',\r\n        OperateType: '',\r\n        StartTime: null,\r\n        EndTime: null,\r\n        Module: '',\r\n        Type: '',\r\n        Date: [],\r\n        Status: null\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Name',\r\n            label: '告警名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Module',\r\n            label: '业务模块',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              this.getTypesByModule(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Type',\r\n            label: '告警类型',\r\n            type: 'select',\r\n            otherOptions: {\r\n              disabled: true,\r\n              clearable: true\r\n            },\r\n            options: [\r\n              {\r\n                label: '全部',\r\n                value: ''\r\n              }\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Status',\r\n            label: '状态',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Date',\r\n            label: '告警时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              type: 'datetimerange',\r\n              rangeSeparator: '至',\r\n              startPlaceholder: '开始日期',\r\n              endPlaceholder: '结束日期',\r\n              clearable: true,\r\n              valueFormat: 'yyyy-MM-dd HH:mm'\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n              if (e && e.length !== 0) {\r\n                this.ruleForm.StartTime = e[0]\r\n                this.ruleForm.EndTime = e[1]\r\n              } else {\r\n                this.ruleForm.StartTime = null\r\n                this.ruleForm.EndTime = null\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: []\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          {\r\n            label: '告警时间',\r\n            key: 'AlarmTime'\r\n          },\r\n          {\r\n            label: '状态',\r\n            key: 'Status',\r\n            render: (row) => {\r\n              const h = this.$createElement\r\n              return h('div', {}, [\r\n                h(\r\n                  'span',\r\n                  { class: row.Status == 1 ? 'warning' : row.Status == 2 ? 'close' : row.Status == 3 ? 'handel' : '' },\r\n                  ''\r\n                ),\r\n                h('span', {}, `${row.Status == 1 ? '告警中' : row.Status == 2 ? '已关闭' : row.Status == 3 ? '已处理' : ''}`)\r\n              ])\r\n            }\r\n          },\r\n          {\r\n            label: '告警类型',\r\n            key: 'Type'\r\n          },\r\n          {\r\n            label: '告警名称',\r\n            key: 'Name'\r\n          },\r\n          {\r\n            label: '来源',\r\n            key: 'Source'\r\n          },\r\n          {\r\n            label: '业务模块',\r\n            key: 'Module'\r\n          },\r\n          {\r\n            label: '处理内容',\r\n            key: 'Content'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.getDetail(row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getModule()\r\n    this.getWarningStatus()\r\n    this.onFresh()\r\n  },\r\n  methods: {\r\n    // 获取业务模块下拉\r\n    getModule() {\r\n      GetModule({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data || []\r\n          const arr = []\r\n          data.forEach((item) => {\r\n            const obj = {\r\n              label: item,\r\n              value: item\r\n            }\r\n            arr.push(obj)\r\n          })\r\n          arr.unshift({\r\n            label: '全部',\r\n            value: ''\r\n          })\r\n          console.log(arr)\r\n          this.customForm.formItems.find(\r\n            (v) => v.key == 'Module'\r\n          ).options = arr\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 获取告警类型\r\n    getTypesByModule(value) {\r\n      this.ruleForm.Type = ''\r\n      if (value) {\r\n        GetTypesByModule({ Type: 2, Module: value }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            const data = res.Data || []\r\n            const arr = []\r\n            data.forEach((item) => {\r\n              const obj = {\r\n                label: item,\r\n                value: item\r\n              }\r\n              arr.push(obj)\r\n            })\r\n            arr.unshift({\r\n              label: '全部',\r\n              value: ''\r\n            })\r\n            console.log(arr)\r\n            this.customForm.formItems.find(\r\n              (v) => v.key == 'Type'\r\n            ).otherOptions.disabled = false\r\n            this.customForm.formItems.find(\r\n              (v) => v.key == 'Type'\r\n            ).options = arr\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      } else {\r\n        this.customForm.formItems.find(\r\n          (v) => v.key == 'Type'\r\n        ).otherOptions.disabled = true\r\n        this.customForm.formItems.find((v) => v.key == 'Type').options =\r\n          []\r\n      }\r\n    },\r\n    // 获取任务状态\r\n    getWarningStatus() {\r\n      GetWarningStatus({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data || []\r\n          const arr = []\r\n          data.forEach((item) => {\r\n            const obj = {\r\n              label: item.Name,\r\n              value: item.Value\r\n            }\r\n            arr.push(obj)\r\n          })\r\n          arr.unshift({\r\n            label: '全部',\r\n            value: null\r\n          })\r\n          console.log(arr)\r\n          this.customForm.formItems.find(\r\n            (v) => v.key == 'Status'\r\n          ).options = arr\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.customForm.formItems.find(\r\n        (v) => v.key == 'Type'\r\n      ).otherOptions.disabled = true\r\n      this.customForm.formItems.find(\r\n        (v) => v.key == 'Type'\r\n      ).options = [{\r\n        label: '全部',\r\n        value: ''\r\n      }]\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.fetchData()\r\n    },\r\n    async fetchData() {\r\n      this.customTableConfig.loading = true\r\n      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null\r\n        this.ruleForm.EndTime = null\r\n      }\r\n      await GetWarnPageList({\r\n        ...this.ruleForm,\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.tableData = res.Data.Data\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.customTableConfig.loading = false\r\n      })\r\n    },\r\n\r\n    // 关闭\r\n    handelClose(row) {\r\n      this.$confirm('是否关闭?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        CloseWarning({ Id: row.Id }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '关闭成功!'\r\n            })\r\n            this.onFresh()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    // 查看详情\r\n    getDetail(item) {\r\n      console.log(item)\r\n      if (item.Url) {\r\n        let platform = '' // 子应用\r\n        if (item.ModuleName == '后台设置') {\r\n          platform = 'management'\r\n        } else {\r\n          platform = 'digitalfactory'\r\n        }\r\n        this.$qiankun.switchMicroAppFn(\r\n          platform,\r\n          item.ModuleCode,\r\n          item.ModuleId,\r\n          item.Url\r\n        )\r\n      } else {\r\n        const platform = 'digitalfactory'\r\n        const code = 'szgc'\r\n        const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\r\n        let url = ''\r\n        if (item.Module == '能耗管理') {\r\n          url = '/business/energy/alarmDetail'\r\n        } else if (item.Module == '车辆道闸') {\r\n          url = '/bussiness/vehicle/alarm-info'\r\n        } else if (item.Module == '门禁管理') {\r\n          url = '/business/AccessControlAlarmDetails'\r\n        } else if (item.Module == '安防管理') {\r\n          url = '/business/equipmentAlarm'\r\n        } else if (item.Module == '危化品管理') {\r\n          url = '/business/hazchem/alarmInformation'\r\n        } else if (item.Module == '环境管理') {\r\n          url = '/business/environment/alarmInformation'\r\n        } else if (item.Module == '访客管理') {\r\n          url = '/business/energy/alarmDetail'\r\n          console.log('访客管理')\r\n        }\r\n        this.$qiankun.switchMicroAppFn(platform, code, id, url)\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.onFresh()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.onFresh()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.layout {\r\n  height: 100%;\r\n  width: 100%;\r\n  position: absolute;\r\n  ::v-deep {\r\n    .CustomLayout {\r\n      .layoutTable {\r\n        height: 0;\r\n        .CustomTable {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          .table {\r\n            flex: 1;\r\n            height: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            .el-table {\r\n              flex: 1;\r\n              height: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.warning {\r\n  display: inline-block;\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 4px;\r\n  margin-right: 5px;\r\n  background-color: #fb6b7f;\r\n}\r\n.handel {\r\n  display: inline-block;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 5px;\r\n  margin-right: 5px;\r\n  background-color: #368dff;\r\n}\r\n.close {\r\n  display: inline-block;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 5px;\r\n  margin-right: 5px;\r\n  background-color: #37be6b;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,SAAAC,SAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,cAAA,EAAAC,YAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAV,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAY,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,QAAA;QACAC,IAAA;QACAC,WAAA;QACAC,SAAA;QACAC,OAAA;QACAC,MAAA;QACAC,IAAA;QACAC,IAAA;QACAC,MAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,KAAA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAM,OAAA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAnB,KAAA,CAAAuB,gBAAA,CAAAJ,CAAA;UACA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAS,QAAA;YACAR,SAAA;UACA;UACAM,OAAA,GACA;YACAT,KAAA;YACAY,KAAA;UACA,EACA;UACAP,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAM,OAAA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAD,IAAA;YACAY,cAAA;YACAC,gBAAA;YACAC,cAAA;YACAZ,SAAA;YACAa,WAAA;UACA;UACAX,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;YACA,IAAAA,CAAA,IAAAA,CAAA,CAAAW,MAAA;cACA9B,KAAA,CAAAC,QAAA,CAAAG,SAAA,GAAAe,CAAA;cACAnB,KAAA,CAAAC,QAAA,CAAAI,OAAA,GAAAc,CAAA;YACA;cACAnB,KAAA,CAAAC,QAAA,CAAAG,SAAA;cACAJ,KAAA,CAAAC,QAAA,CAAAI,OAAA;YACA;UACA;QACA,EACA;QACA0B,KAAA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA;QACA;QACA;QACAC,OAAA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;QACAC,YAAA,GACA;UACA/B,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;UACAiC,MAAA,WAAAA,OAAAC,GAAA;YACA,IAAAC,CAAA,GAAA/C,KAAA,CAAAgD,cAAA;YACA,OAAAD,CAAA,aACAA,CAAA,CACA,QACA;cAAAE,KAAA,EAAAH,GAAA,CAAArC,MAAA,oBAAAqC,GAAA,CAAArC,MAAA,kBAAAqC,GAAA,CAAArC,MAAA;YAAA,GACA,EACA,GACAsC,CAAA,gBAAAG,MAAA,CAAAJ,GAAA,CAAArC,MAAA,gBAAAqC,GAAA,CAAArC,MAAA,gBAAAqC,GAAA,CAAArC,MAAA,qBACA;UACA;QACA,GACA;UACAI,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,EACA;QACAuC,SAAA;QACAC,YAAA,GACA;UACAC,WAAA;UACAtC,YAAA;YACAD,IAAA;UACA;UACAwC,OAAA,WAAAA,QAAAC,KAAA,EAAAT,GAAA;YACA9C,KAAA,CAAAwD,SAAA,CAAAV,GAAA;UACA;QACA;MAEA;IACA;EACA;EACAW,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAH,SAAA,WAAAA,UAAA;MAAA,IAAAI,MAAA;MACAxE,SAAA,KAAAyE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAlE,IAAA,GAAAiE,GAAA,CAAAE,IAAA;UACA,IAAAC,GAAA;UACApE,IAAA,CAAAqE,OAAA,WAAAC,IAAA;YACA,IAAAC,GAAA;cACAzD,KAAA,EAAAwD,IAAA;cACA5C,KAAA,EAAA4C;YACA;YACAF,GAAA,CAAAI,IAAA,CAAAD,GAAA;UACA;UACAH,GAAA,CAAAK,OAAA;YACA3D,KAAA;YACAY,KAAA;UACA;UACAL,OAAA,CAAAC,GAAA,CAAA8C,GAAA;UACAL,MAAA,CAAApD,UAAA,CAAAC,SAAA,CAAA8D,IAAA,CACA,UAAAC,CAAA;YAAA,OAAAA,CAAA,CAAA9D,GAAA;UAAA,CACA,EAAAU,OAAA,GAAA6C,GAAA;QACA;UACAL,MAAA,CAAAa,QAAA;YACA7D,IAAA;YACA8D,OAAA,EAAAZ,GAAA,CAAAa;UACA;QACA;MACA;IACA;IACA;IACAtD,gBAAA,WAAAA,iBAAAE,KAAA;MAAA,IAAAqD,MAAA;MACA,KAAA7E,QAAA,CAAAM,IAAA;MACA,IAAAkB,KAAA;QACAlC,gBAAA;UAAAgB,IAAA;UAAAD,MAAA,EAAAmB;QAAA,GAAAsC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACA,IAAAlE,IAAA,GAAAiE,GAAA,CAAAE,IAAA;YACA,IAAAC,GAAA;YACApE,IAAA,CAAAqE,OAAA,WAAAC,IAAA;cACA,IAAAC,GAAA;gBACAzD,KAAA,EAAAwD,IAAA;gBACA5C,KAAA,EAAA4C;cACA;cACAF,GAAA,CAAAI,IAAA,CAAAD,GAAA;YACA;YACAH,GAAA,CAAAK,OAAA;cACA3D,KAAA;cACAY,KAAA;YACA;YACAL,OAAA,CAAAC,GAAA,CAAA8C,GAAA;YACAW,MAAA,CAAApE,UAAA,CAAAC,SAAA,CAAA8D,IAAA,CACA,UAAAC,CAAA;cAAA,OAAAA,CAAA,CAAA9D,GAAA;YAAA,CACA,EAAAG,YAAA,CAAAS,QAAA;YACAsD,MAAA,CAAApE,UAAA,CAAAC,SAAA,CAAA8D,IAAA,CACA,UAAAC,CAAA;cAAA,OAAAA,CAAA,CAAA9D,GAAA;YAAA,CACA,EAAAU,OAAA,GAAA6C,GAAA;UACA;YACAW,MAAA,CAAAH,QAAA;cACA7D,IAAA;cACA8D,OAAA,EAAAZ,GAAA,CAAAa;YACA;UACA;QACA;MACA;QACA,KAAAnE,UAAA,CAAAC,SAAA,CAAA8D,IAAA,CACA,UAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA9D,GAAA;QAAA,CACA,EAAAG,YAAA,CAAAS,QAAA;QACA,KAAAd,UAAA,CAAAC,SAAA,CAAA8D,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA9D,GAAA;QAAA,GAAAU,OAAA,GACA;MACA;IACA;IACA;IACAqC,gBAAA,WAAAA,iBAAA;MAAA,IAAAoB,MAAA;MACAtF,gBAAA,KAAAsE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAlE,IAAA,GAAAiE,GAAA,CAAAE,IAAA;UACA,IAAAC,GAAA;UACApE,IAAA,CAAAqE,OAAA,WAAAC,IAAA;YACA,IAAAC,GAAA;cACAzD,KAAA,EAAAwD,IAAA,CAAAnE,IAAA;cACAuB,KAAA,EAAA4C,IAAA,CAAAW;YACA;YACAb,GAAA,CAAAI,IAAA,CAAAD,GAAA;UACA;UACAH,GAAA,CAAAK,OAAA;YACA3D,KAAA;YACAY,KAAA;UACA;UACAL,OAAA,CAAAC,GAAA,CAAA8C,GAAA;UACAY,MAAA,CAAArE,UAAA,CAAAC,SAAA,CAAA8D,IAAA,CACA,UAAAC,CAAA;YAAA,OAAAA,CAAA,CAAA9D,GAAA;UAAA,CACA,EAAAU,OAAA,GAAA6C,GAAA;QACA;UACAY,MAAA,CAAAJ,QAAA;YACA7D,IAAA;YACA8D,OAAA,EAAAZ,GAAA,CAAAa;UACA;QACA;MACA;IACA;IACAI,UAAA,WAAAA,WAAAlF,IAAA;MACAqB,OAAA,CAAAC,GAAA,CAAAtB,IAAA;MACA,KAAA6D,OAAA;IACA;IACAsB,SAAA,WAAAA,UAAA;MACA,KAAAxE,UAAA,CAAAC,SAAA,CAAA8D,IAAA,CACA,UAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA9D,GAAA;MAAA,CACA,EAAAG,YAAA,CAAAS,QAAA;MACA,KAAAd,UAAA,CAAAC,SAAA,CAAA8D,IAAA,CACA,UAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA9D,GAAA;MAAA,CACA,EAAAU,OAAA;QACAT,KAAA;QACAY,KAAA;MACA;MACA,KAAAmC,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAAuB,SAAA;IACA;IACAA,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAT,MAAA,CAAAjD,iBAAA,CAAAG,OAAA;cACA,KAAA8C,MAAA,CAAAnF,QAAA,CAAAO,IAAA,IAAA4E,MAAA,CAAAnF,QAAA,CAAAO,IAAA,CAAAsB,MAAA;gBACAsD,MAAA,CAAAnF,QAAA,CAAAG,SAAA;gBACAgF,MAAA,CAAAnF,QAAA,CAAAI,OAAA;cACA;cAAAsF,QAAA,CAAAE,IAAA;cAAA,OACArG,eAAA,CAAAsG,aAAA,CAAAA,aAAA,KACAV,MAAA,CAAAnF,QAAA;gBACA8F,IAAA,EAAAX,MAAA,CAAAjD,iBAAA,CAAAK,WAAA;gBACAwD,QAAA,EAAAZ,MAAA,CAAAjD,iBAAA,CAAAM;cAAA,EACA,EAAAsB,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAmB,MAAA,CAAAjD,iBAAA,CAAAgB,SAAA,GAAAa,GAAA,CAAAE,IAAA,CAAAA,IAAA;kBACAkB,MAAA,CAAAjD,iBAAA,CAAAO,KAAA,GAAAsB,GAAA,CAAAE,IAAA,CAAA+B,UAAA;gBACA;kBACAb,MAAA,CAAAT,QAAA;oBACA7D,IAAA;oBACA8D,OAAA,EAAAZ,GAAA,CAAAa;kBACA;gBACA;cACA,GAAAqB,OAAA;gBACAd,MAAA,CAAAjD,iBAAA,CAAAG,OAAA;cACA;YAAA;YAAA;cAAA,OAAAqD,QAAA,CAAAQ,IAAA;UAAA;QAAA,GAAAX,OAAA;MAAA;IACA;IAEA;IACAY,WAAA,WAAAA,YAAAtD,GAAA;MAAA,IAAAuD,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA1F,IAAA;MACA,GAAAiD,IAAA;QACAnE,YAAA;UAAA6G,EAAA,EAAA3D,GAAA,CAAA2D;QAAA,GAAA1C,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAoC,MAAA,CAAA1B,QAAA;cACA7D,IAAA;cACA8D,OAAA;YACA;YACAyB,MAAA,CAAAzC,OAAA;UACA;YACAyC,MAAA,CAAA1B,QAAA;cACA7D,IAAA;cACA8D,OAAA,EAAAZ,GAAA,CAAAa;YACA;UACA;QACA;MACA,GAAA6B,KAAA;QACAL,MAAA,CAAA1B,QAAA;UACA7D,IAAA;UACA8D,OAAA;QACA;MACA;IACA;IACA;IACApB,SAAA,WAAAA,UAAAa,IAAA;MACAjD,OAAA,CAAAC,GAAA,CAAAgD,IAAA;MACA,IAAAA,IAAA,CAAAsC,GAAA;QACA,IAAAC,QAAA;QACA,IAAAvC,IAAA,CAAAwC,UAAA;UACAD,QAAA;QACA;UACAA,QAAA;QACA;QACA,KAAAE,QAAA,CAAAC,gBAAA,CACAH,QAAA,EACAvC,IAAA,CAAA2C,UAAA,EACA3C,IAAA,CAAA4C,QAAA,EACA5C,IAAA,CAAAsC,GACA;MACA;QACA,IAAAC,SAAA;QACA,IAAAM,IAAA;QACA,IAAAC,EAAA;QACA,IAAAC,GAAA;QACA,IAAA/C,IAAA,CAAA/D,MAAA;UACA8G,GAAA;QACA,WAAA/C,IAAA,CAAA/D,MAAA;UACA8G,GAAA;QACA,WAAA/C,IAAA,CAAA/D,MAAA;UACA8G,GAAA;QACA,WAAA/C,IAAA,CAAA/D,MAAA;UACA8G,GAAA;QACA,WAAA/C,IAAA,CAAA/D,MAAA;UACA8G,GAAA;QACA,WAAA/C,IAAA,CAAA/D,MAAA;UACA8G,GAAA;QACA,WAAA/C,IAAA,CAAA/D,MAAA;UACA8G,GAAA;UACAhG,OAAA,CAAAC,GAAA;QACA;QACA,KAAAyF,QAAA,CAAAC,gBAAA,CAAAH,SAAA,EAAAM,IAAA,EAAAC,EAAA,EAAAC,GAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,GAAA;MACAlG,OAAA,CAAAC,GAAA,iBAAA6B,MAAA,CAAAoE,GAAA;MACA,KAAAnF,iBAAA,CAAAM,QAAA,GAAA6E,GAAA;MACA,KAAA1D,OAAA;IACA;IACA2D,mBAAA,WAAAA,oBAAAD,GAAA;MACAlG,OAAA,CAAAC,GAAA,wBAAA6B,MAAA,CAAAoE,GAAA;MACA,KAAAnF,iBAAA,CAAAK,WAAA,GAAA8E,GAAA;MACA,KAAA1D,OAAA;IACA;EACA;AACA", "ignoreList": []}]}