{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\components\\electricityUsage.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\components\\electricityUsage.vue", "mtime": 1754619818617}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldEVsZWN0cmljVXNhZ2UgfSBmcm9tICdAL2FwaS9idXNpbmVzcy9lbmVyZ3lNYW5hZ2VtZW50LmpzJwpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogewoKICB9LAogIHByb3BzOiB7CiAgICBpc1Bob3Rvdm9sdGFpYzogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIGxpc3Q6IFtdCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgcGFyZW50RGF0YSgpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBEYXRlVHlwZTogdGhpcy5EYXRlVHlwZSgpLAogICAgICAgIFN0YXJ0VGltZTogdGhpcy5TdGFydFRpbWUoKSwKICAgICAgICByYW5kb21JbnRlZ2VyOiB0aGlzLnJhbmRvbUludGVnZXIoKQogICAgICB9CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgcGFyZW50RGF0YTogewogICAgICBoYW5kbGVyKG52LCBvdikgewogICAgICAgIHRoaXMuZ2V0RWxlY3RyaWNVc2FnZSgpCiAgICAgIH0KICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldEVsZWN0cmljVXNhZ2UoKQogIH0sCiAgbW91bnRlZCgpIHsKCiAgfSwKICBpbmplY3Q6IFsnRGF0ZVR5cGUnLCAnU3RhcnRUaW1lJywgJ3JhbmRvbUludGVnZXInXSwKICBtZXRob2RzOiB7CiAgICBhc3luYyBnZXRFbGVjdHJpY1VzYWdlKCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldEVsZWN0cmljVXNhZ2UodGhpcy5wYXJlbnREYXRhKQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgIHRoaXMubGlzdCA9IHJlcy5EYXRhCiAgICAgIH0KICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["electricityUsage.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "electricityUsage.vue", "sourceRoot": "src/views/business/energyManagement/pJEnergyAnalysis/eleNew/components", "sourcesContent": ["<template>\n  <div\n    v-loading=\"loading\"\n    class=\"electricityUsage\"\n    element-loading-text=\"加载中...\"\n  >\n    <div class=\"title\">\n      <div class=\"left\">用电情况</div>\n      <div class=\"right\">不包含重钢工厂</div>\n    </div>\n    <div class=\"eleList\">\n      <div v-for=\"(item, index) in list\" :key=\"index\" class=\"eleItem\" :style=\"{height: '100px' }\">\n        <div class=\"left\">\n          <p style=\"margin-bottom: 12px\">{{ item.Key }}</p>\n          <p>\n            <b>{{ item.Value }}</b><span>度</span>\n            <template v-if=\"item.Key == '总用电'\">\n              功率因数\n              <span\n                style=\"margin-left: 16px; font-size: 16px; color: #666\"\n              >--</span></template>\n            <template v-else> {{ item.Percent }}% </template>\n          </p>\n        </div>\n        <img\n          v-if=\"item.Key == '总用电'\"\n          class=\"right\"\n          src=\"@/assets/Business/eleIcon1.png\"\n          alt=\"\"\n        >\n        <img\n          v-if=\"item.Key == '用电(市电)'\"\n          class=\"right\"\n          src=\"@/assets/Business/eleIcon2.png\"\n          alt=\"\"\n        >\n        <img\n          v-if=\"item.Key == '用电(光伏)'\"\n          class=\"right\"\n          src=\"@/assets/Business/eleIcon3.png\"\n          alt=\"\"\n        >\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetElectricUsage } from '@/api/business/energyManagement.js'\nexport default {\n  components: {\n\n  },\n  props: {\n    isPhotovoltaic: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      loading: true,\n      list: []\n    }\n  },\n  computed: {\n    parentData() {\n      return {\n        DateType: this.DateType(),\n        StartTime: this.StartTime(),\n        randomInteger: this.randomInteger()\n      }\n    }\n  },\n  watch: {\n    parentData: {\n      handler(nv, ov) {\n        this.getElectricUsage()\n      }\n    }\n  },\n  created() {\n    this.getElectricUsage()\n  },\n  mounted() {\n\n  },\n  inject: ['DateType', 'StartTime', 'randomInteger'],\n  methods: {\n    async getElectricUsage() {\n      this.loading = true\n      const res = await GetElectricUsage(this.parentData)\n      if (res.IsSucceed) {\n        this.list = res.Data\n      }\n      this.loading = false\n    }\n  }\n}\n</script>\n<style scoped lang='scss'>\n.electricityUsage {\n  height: 392px;\n  background: #fff;\n  border-radius: 4px;\n  width: 100%;\n  padding: 16px;\n  box-sizing: border-box;\n  margin-bottom: 16px;\n  .title {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 16px;\n    .left {\n      color: #666;\n      font-weight: bold;\n      font-size: 16px;\n    }\n    .right {\n      font-size: 12px;\n      color: #b8bec8;\n    }\n  }\n  .eleList {\n    .eleItem {\n      padding: 20px 25px;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      border: 1px solid rgba(41, 141, 255, 0.1);\n      margin-bottom: 16px;\n      background: linear-gradient(\n        90deg,\n        rgba(41, 141, 255, 0.05) 0%,\n        rgba(41, 141, 255, 0) 100%\n      );\n      &:last-child {\n        margin-bottom: 0;\n      }\n      .left {\n        font-size: 16px;\n        color: #1d2541;\n        span {\n          color: #999;\n          font-size: 14px;\n          margin: 0 16px 0 8px;\n        }\n        b {\n          color: #394f7f;\n          font-size: 28px;\n        }\n      }\n      .right {\n        height: 54px;\n        width: 54px;\n      }\n    }\n  }\n}\n</style>\n"]}]}