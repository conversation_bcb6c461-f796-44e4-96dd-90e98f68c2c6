{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\alarmLinkageSettings\\broadcastTimeSettings.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\alarmLinkageSettings\\broadcastTimeSettings.vue", "mtime": 1755674552413}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNwbGljZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaXRlcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLml0ZXJhdG9yLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldEJyb1RpbWVSYW5nZUxpc3QsIEJhdGNoRWRpdEJyb1RpbWVSYW5nZSB9IGZyb20gJ0AvYXBpL2J1c2luZXNzL2JlaGF2aW9yQW5hbHlzaXMnOwppbXBvcnQgZGF5anMgZnJvbSAnZGF5anMnOwpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHRpbWVEYXRhOiBbXSwKICAgICAgSXNFZGl0OiBmYWxzZSwKICAgICAgZm9ybVJ1bGVzOiB7CiAgICAgICAgdGltZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+Whq+WGmeaXtumXtCcsCiAgICAgICAgICB0cmlnZ2VyOiAnY2hhbmdlJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBidG5sb2FkaW5nOiBmYWxzZQogICAgfTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB0aGlzLmZldGNoRGF0YSgpOwogIH0sCiAgbWV0aG9kczogewogICAgLy8g5paw5aKeCiAgICBoYW5kZWxhZGQ6IGZ1bmN0aW9uIGhhbmRlbGFkZCgpIHsKICAgICAgdGhpcy50aW1lRGF0YS5wdXNoKHsKICAgICAgICBJZDogJycsCiAgICAgICAgU3RhcnRUaW1lOiAnJywKICAgICAgICBFbmRUaW1lOiAnJywKICAgICAgICB0aW1lOiBudWxsLAogICAgICAgIElzRGVsZXRlZDogZmFsc2UKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Yig6ZmkCiAgICBoYW5kZWxkZWxldGU6IGZ1bmN0aW9uIGhhbmRlbGRlbGV0ZShpdGVtLCBpZHgpIHsKICAgICAgaWYgKGl0ZW0uSWQpIHsKICAgICAgICB0aGlzLnRpbWVEYXRhW2lkeF0uSXNEZWxldGVkID0gdHJ1ZTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnRpbWVEYXRhLnNwbGljZShpZHgsIDEpOwogICAgICB9CiAgICB9LAogICAgLy8g5pe26Ze0CiAgICBjaGFuZ2VUaW1lOiBmdW5jdGlvbiBjaGFuZ2VUaW1lKGluZGV4LCBlKSB7CiAgICAgIGNvbnNvbGUubG9nKGluZGV4LCBlKTsKICAgICAgdmFyIGRheSA9IGRheWpzKCkuZm9ybWF0KCdZWVlZLU1NLUREJyk7CiAgICAgIHRoaXMudGltZURhdGFbaW5kZXhdLlN0YXJ0VGltZSA9IGRheSArICcgJyArIGVbMF07CiAgICAgIHRoaXMudGltZURhdGFbaW5kZXhdLkVuZFRpbWUgPSBkYXkgKyAnICcgKyBlWzFdOwogICAgICBjb25zb2xlLmxvZyh0aGlzLnRpbWVEYXRhW2luZGV4XSk7CiAgICB9LAogICAgLy8g6I635Y+W5pWw5o2uCiAgICBmZXRjaERhdGE6IGZ1bmN0aW9uIGZldGNoRGF0YSgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgLy8gdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICBHZXRCcm9UaW1lUmFuZ2VMaXN0KHsKICAgICAgICBwYWdlOiAxLAogICAgICAgIFBhZ2VTaXplOiAtMQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgaWYgKHJlcy5EYXRhLkRhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgICBfdGhpcy50aW1lRGF0YSA9IHJlcy5EYXRhLkRhdGEubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgaXRlbS50aW1lID0gW2l0ZW0uU3RhcnRUaW1lLCBpdGVtLkVuZFRpbWVdOwogICAgICAgICAgICAgIHJldHVybiBpdGVtOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIF90aGlzLnRpbWVEYXRhID0gW107CiAgICAgICAgICAgIF90aGlzLmhhbmRlbGFkZCgpOwogICAgICAgICAgfQogICAgICAgICAgY29uc29sZS5sb2coX3RoaXMudGltZURhdGEsICcgdGhpcy50aW1lRGF0YScpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuTWVzc2FnZSk7CiAgICAgICAgfQogICAgICB9KS5maW5hbGx5KGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOaPkOS6pAogICAgaGFuZGVsU3VibWl0OiBmdW5jdGlvbiBoYW5kZWxTdWJtaXQoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB2YXIgdmFsaWRhdGVQcm9taXNlcyA9IHRoaXMuJHJlZnMuZm9ybVJlZi5tYXAoZnVuY3Rpb24gKGl0ZW0sIGluZGV4KSB7CiAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHsKICAgICAgICAgIGl0ZW0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgICAgIHJlc29sdmUoKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICByZWplY3QoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICBQcm9taXNlLmFsbCh2YWxpZGF0ZVByb21pc2VzKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczIuc2F2ZUluZm8oKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgc2F2ZUluZm86IGZ1bmN0aW9uIHNhdmVJbmZvKCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdGhpcy5idG5sb2FkaW5nID0gdHJ1ZTsKICAgICAgY29uc29sZS5sb2coJzExMScpOwogICAgICBCYXRjaEVkaXRCcm9UaW1lUmFuZ2UodGhpcy50aW1lRGF0YSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIF90aGlzMy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgbWVzc2FnZTogJ+S/neWtmOaIkOWKnycKICAgICAgICAgIH0pOwogICAgICAgICAgX3RoaXMzLmZldGNoRGF0YSgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UpOwogICAgICAgIH0KICAgICAgfSkuZmluYWxseShmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMzLmJ0bmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+W5raICiAgICBoYW5kZWxDYW5jZWw6IGZ1bmN0aW9uIGhhbmRlbENhbmNlbCgpIHsKICAgICAgdGhpcy50aW1lRGF0YSA9IFtdOwogICAgICB0aGlzLklzRWRpdCA9ICF0aGlzLklzRWRpdDsKICAgICAgdGhpcy5mZXRjaERhdGEoKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["GetBroTimeRangeList", "BatchEditBroTimeRange", "dayjs", "data", "timeData", "IsEdit", "formRules", "time", "required", "message", "trigger", "loading", "btnloading", "mounted", "fetchData", "methods", "<PERSON><PERSON><PERSON>", "push", "Id", "StartTime", "EndTime", "IsDeleted", "handeldelete", "item", "idx", "splice", "changeTime", "index", "e", "console", "log", "day", "format", "_this", "page", "PageSize", "then", "res", "IsSucceed", "Data", "length", "map", "$message", "error", "Message", "finally", "handelSubmit", "_this2", "validatePromises", "$refs", "formRef", "Promise", "resolve", "reject", "validate", "valid", "all", "saveInfo", "catch", "_this3", "type", "handelCancel"], "sources": ["src/views/business/behaviorAnalysis/alarmLinkageSettings/broadcastTimeSettings.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <el-card class=\"box-card\">\r\n      <div\r\n        v-loading=\"loading\"\r\n        class=\"main-wapper\"\r\n        :style=\"{\r\n          maxHeight: !IsEdit\r\n            ? 'calc(100% - 30px - 14px)'\r\n            : 'calc(100% - 14px - 30px - 30px)',\r\n        }\"\r\n      >\r\n        <div v-for=\"(item, idx) in timeData\" :key=\"idx\">\r\n          <!-- <el-form v-if=\"!item.IsDeleted\" ref=\"formRef\" :model=\"item\" inline label-width=\"120px\" :rules=\"idx !== timeData.findIndex(item => !item.IsDeleted) ? formRules : {}\" :hide-required-asterisk=\"true\"> -->\r\n          <el-form\r\n            v-if=\"!item.IsDeleted\"\r\n            ref=\"formRef\"\r\n            :model=\"item\"\r\n            inline\r\n            label-width=\"120px\"\r\n            :rules=\"formRules\"\r\n            :hide-required-asterisk=\"true\"\r\n          >\r\n            <el-form-item\r\n              :label=\"\r\n                idx == timeData.findIndex((item) => !item.IsDeleted)\r\n                  ? '提交广播时间'\r\n                  : ' '\r\n              \"\r\n              prop=\"time\"\r\n            >\r\n              <el-time-picker\r\n                v-model=\"item.time\"\r\n                is-range\r\n                :disabled=\"!IsEdit\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始时间\"\r\n                end-placeholder=\"结束时间\"\r\n                placeholder=\"选择时间范围\"\r\n                value-format=\"HH:mm:ss\"\r\n                format=\"HH:mm:ss\"\r\n                @change=\"changeTime(idx, $event)\"\r\n              />\r\n            </el-form-item>\r\n            <el-button\r\n              v-if=\"IsEdit\"\r\n              type=\"text\"\r\n              style=\"color: #f56c6c\"\r\n              @click=\"handeldelete(item, idx)\"\r\n              >删除</el-button\r\n            >\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div v-if=\"IsEdit\">\r\n        <el-button\r\n          type=\"text\"\r\n          style=\"font-size: 12px; margin-left: 120px\"\r\n          @click=\"handeladd()\"\r\n          >+新增</el-button\r\n        >\r\n      </div>\r\n      <div class=\"prompt\">\r\n        系统将于以上设置时间段进行行为分析告警信息广播播放，其他时间识别行为分析告警但不进行广播播放\r\n      </div>\r\n      <div class=\"cs-btn\">\r\n        <el-button v-if=\"!IsEdit\" type=\"primary\" @click=\"IsEdit = !IsEdit\"\r\n          >编辑</el-button\r\n        >\r\n        <el-button\r\n          v-if=\"IsEdit\"\r\n          type=\"primary\"\r\n          :loading=\"btnloading\"\r\n          @click=\"handelSubmit\"\r\n          >确认</el-button\r\n        >\r\n        <el-button v-if=\"IsEdit\" @click=\"handelCancel\">取消</el-button>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetBroTimeRangeList, BatchEditBroTimeRange } from '@/api/business/behaviorAnalysis'\r\nimport dayjs from 'dayjs'\r\nexport default {\r\n  data() {\r\n    return {\r\n      timeData: [],\r\n      IsEdit: false,\r\n      formRules: { time: [{ required: true, message: '请填写时间', trigger: 'change' }] },\r\n      loading: false,\r\n      btnloading: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    // 新增\r\n    handeladd() {\r\n      this.timeData.push({\r\n        Id: '',\r\n        StartTime: '',\r\n        EndTime: '',\r\n        time: null,\r\n        IsDeleted: false\r\n      })\r\n    },\r\n    // 删除\r\n    handeldelete(item, idx) {\r\n      if (item.Id) {\r\n        this.timeData[idx].IsDeleted = true\r\n      } else {\r\n        this.timeData.splice(idx, 1)\r\n      }\r\n    },\r\n    // 时间\r\n    changeTime(index, e) {\r\n      console.log(index, e)\r\n      const day = dayjs().format('YYYY-MM-DD')\r\n      this.timeData[index].StartTime = day + ' ' + e[0]\r\n      this.timeData[index].EndTime = day + ' ' + e[1]\r\n      console.log(this.timeData[index])\r\n    },\r\n    // 获取数据\r\n    fetchData() {\r\n      // this.loading = true\r\n      GetBroTimeRangeList({ page: 1, PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          if (res.Data.Data.length > 0) {\r\n            this.timeData = res.Data.Data.map((item) => {\r\n              item.time = [item.StartTime, item.EndTime]\r\n              return item\r\n            })\r\n          } else {\r\n            this.timeData = []\r\n            this.handeladd()\r\n          }\r\n          console.log(this.timeData, ' this.timeData')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 提交\r\n    handelSubmit() {\r\n      const validatePromises = this.$refs.formRef.map((item, index) => {\r\n        return new Promise((resolve, reject) => {\r\n          item.validate((valid) => {\r\n            if (valid) {\r\n              resolve()\r\n            } else {\r\n              reject()\r\n            }\r\n          })\r\n        })\r\n      })\r\n\r\n      Promise.all(validatePromises)\r\n        .then(() => {\r\n          this.saveInfo()\r\n        })\r\n        .catch(() => {\r\n          return false\r\n        })\r\n    },\r\n\r\n    saveInfo() {\r\n      this.btnloading = true\r\n      console.log('111')\r\n      BatchEditBroTimeRange(this.timeData).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '保存成功'\r\n          })\r\n          this.fetchData()\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.btnloading = false\r\n      })\r\n    },\r\n    // 取消\r\n    handelCancel() {\r\n      this.timeData = []\r\n      this.IsEdit = !this.IsEdit\r\n      this.fetchData()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.box-card div:not(:first-child) {\r\n  .el-form {\r\n    .el-form-item {\r\n      ::v-deep.el-form-item__label {\r\n        // color: transparent !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.layout {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .box-card {\r\n    height: calc(100vh - 190px);\r\n    overflow: auto;\r\n    max-height: 100%;\r\n    ::v-deep .el-card__body {\r\n      max-height: 100%;\r\n    }\r\n    .main-wapper {\r\n      // max-height: calc(100% - 34px - 30px);\r\n      overflow: auto;\r\n    }\r\n    .prompt {\r\n      color: #a0a0a0;\r\n      font-size: 12px;\r\n      margin: 10px 0;\r\n      margin-left: 120px;\r\n    }\r\n\r\n    .cs-btn {\r\n      margin-left: 120px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmFA,SAAAA,mBAAA,EAAAC,qBAAA;AACA,OAAAC,KAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,MAAA;MACAC,SAAA;QAAAC,IAAA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAAA;MACAC,OAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAAZ,QAAA,CAAAa,IAAA;QACAC,EAAA;QACAC,SAAA;QACAC,OAAA;QACAb,IAAA;QACAc,SAAA;MACA;IACA;IACA;IACAC,YAAA,WAAAA,aAAAC,IAAA,EAAAC,GAAA;MACA,IAAAD,IAAA,CAAAL,EAAA;QACA,KAAAd,QAAA,CAAAoB,GAAA,EAAAH,SAAA;MACA;QACA,KAAAjB,QAAA,CAAAqB,MAAA,CAAAD,GAAA;MACA;IACA;IACA;IACAE,UAAA,WAAAA,WAAAC,KAAA,EAAAC,CAAA;MACAC,OAAA,CAAAC,GAAA,CAAAH,KAAA,EAAAC,CAAA;MACA,IAAAG,GAAA,GAAA7B,KAAA,GAAA8B,MAAA;MACA,KAAA5B,QAAA,CAAAuB,KAAA,EAAAR,SAAA,GAAAY,GAAA,SAAAH,CAAA;MACA,KAAAxB,QAAA,CAAAuB,KAAA,EAAAP,OAAA,GAAAW,GAAA,SAAAH,CAAA;MACAC,OAAA,CAAAC,GAAA,MAAA1B,QAAA,CAAAuB,KAAA;IACA;IACA;IACAb,SAAA,WAAAA,UAAA;MAAA,IAAAmB,KAAA;MACA;MACAjC,mBAAA;QAAAkC,IAAA;QAAAC,QAAA;MAAA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAD,GAAA,CAAAE,IAAA,CAAAA,IAAA,CAAAC,MAAA;YACAP,KAAA,CAAA7B,QAAA,GAAAiC,GAAA,CAAAE,IAAA,CAAAA,IAAA,CAAAE,GAAA,WAAAlB,IAAA;cACAA,IAAA,CAAAhB,IAAA,IAAAgB,IAAA,CAAAJ,SAAA,EAAAI,IAAA,CAAAH,OAAA;cACA,OAAAG,IAAA;YACA;UACA;YACAU,KAAA,CAAA7B,QAAA;YACA6B,KAAA,CAAAjB,SAAA;UACA;UACAa,OAAA,CAAAC,GAAA,CAAAG,KAAA,CAAA7B,QAAA;QACA;UACA6B,KAAA,CAAAS,QAAA,CAAAC,KAAA,CAAAN,GAAA,CAAAO,OAAA;QACA;MACA,GAAAC,OAAA;QACAZ,KAAA,CAAAtB,OAAA;MACA;IACA;IACA;IACAmC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,gBAAA,QAAAC,KAAA,CAAAC,OAAA,CAAAT,GAAA,WAAAlB,IAAA,EAAAI,KAAA;QACA,WAAAwB,OAAA,WAAAC,OAAA,EAAAC,MAAA;UACA9B,IAAA,CAAA+B,QAAA,WAAAC,KAAA;YACA,IAAAA,KAAA;cACAH,OAAA;YACA;cACAC,MAAA;YACA;UACA;QACA;MACA;MAEAF,OAAA,CAAAK,GAAA,CAAAR,gBAAA,EACAZ,IAAA;QACAW,MAAA,CAAAU,QAAA;MACA,GACAC,KAAA;QACA;MACA;IACA;IAEAD,QAAA,WAAAA,SAAA;MAAA,IAAAE,MAAA;MACA,KAAA/C,UAAA;MACAiB,OAAA,CAAAC,GAAA;MACA7B,qBAAA,MAAAG,QAAA,EAAAgC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAqB,MAAA,CAAAjB,QAAA;YACAkB,IAAA;YACAnD,OAAA;UACA;UACAkD,MAAA,CAAA7C,SAAA;QACA;UACA6C,MAAA,CAAAjB,QAAA,CAAAC,KAAA,CAAAN,GAAA,CAAAO,OAAA;QACA;MACA,GAAAC,OAAA;QACAc,MAAA,CAAA/C,UAAA;MACA;IACA;IACA;IACAiD,YAAA,WAAAA,aAAA;MACA,KAAAzD,QAAA;MACA,KAAAC,MAAA,SAAAA,MAAA;MACA,KAAAS,SAAA;IACA;EACA;AACA", "ignoreList": []}]}