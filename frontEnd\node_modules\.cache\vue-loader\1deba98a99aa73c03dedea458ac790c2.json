{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\assessmentManagement\\assessmentResults\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\assessmentManagement\\assessmentResults\\index.vue", "mtime": 1755674552411}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/assessmentManagement/assessmentResults", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport dialogView from \"./dialog/view.vue\";\r\n// import { downloadFile } from \"@/utils/downloadFile\";\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport {\r\n  GetPageList,\r\n  GetDetail,\r\n  Distribute,\r\n  ExportData,\r\n} from \"@/api/business/processDocIssuance\";\r\nimport { GetDictionaryDetailListByCode } from \"@/api/sys\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/mixins/export.js\";\r\n// import * as moment from 'moment'\r\n// import dayjs from \"dayjs\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  mixins: [exportInfo],\r\n  // mixins: [deviceTypeMixins, otherMixin],\r\n  data() {\r\n    return {\r\n      currentComponent: dialogView,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"\",\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        EquipCode: \"\",\r\n\r\n        Status: \"\",\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Status\", // 字段ID\r\n            label: \"考核角色\", // Form的label\r\n            type: \"select\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            options: [\r\n              {\r\n                label: \"下发中\",\r\n                value: \"1\",\r\n              },\r\n              {\r\n                label: \"下发成功\",\r\n                value: \"2\",\r\n              },\r\n              {\r\n                label: \"下发失败\",\r\n                value: \"3\",\r\n              },\r\n            ],\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"EquipCode\",\r\n            label: \"考核月\",\r\n            type: \"datePicker\",\r\n            otherOptions: {\r\n              type: \"month\",\r\n              clearable: true,\r\n              valueFormat: \"yyyy-MM-dd\",\r\n            },\r\n            change: (e) => {\r\n              // if (e.length > 0) {\r\n              //   this.ruleForm.CreateStartTime = e[0] + \" 00:00:00\";\r\n              //   this.ruleForm.CreateEndTime = e[1] + \" 23:59:59\";\r\n              // } else {\r\n              //   this.ruleForm.CreateStartTime = null;\r\n              //   this.ruleForm.CreateEndTime = null;\r\n              // }\r\n            },\r\n          },\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"添加配置\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.dialogVisible = true;\r\n                this.dialogTitle = \"添加配置\";\r\n                // this.handleAllExport();\r\n                // this.ExportData(this.ruleForm, \"工艺文件下发\", ExportData);\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: \"考核角色\",\r\n            key: \"DistributeDate\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"考核月\",\r\n            key: \"FileName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"得分\",\r\n            key: \"EquipName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 200,\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"下载明细\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {},\r\n          },\r\n        ],\r\n      },\r\n      deceiveTypeList: [],\r\n    };\r\n  },\r\n  computed: {},\r\n  mounted() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.ExcuteStartTime = null;\r\n      this.ruleForm.ExcuteEndTime = null;\r\n      this.ruleForm.CreateStartTime = null;\r\n      this.ruleForm.CreateEndTime = null;\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.GetDataList();\r\n    },\r\n    init() {\r\n      this.GetDataList();\r\n    },\r\n    async GetDataList() {\r\n      this.customTableConfig.loading = true;\r\n      let res = await GetPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      this.customTableConfig.loading = false;\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.Total;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    async getDictionaryDetailListByCode(dictionaryCode = \"deviceType\", Value) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode,\r\n      });\r\n      if (res.IsSucceed) {\r\n        const options = [{ label: \"全部\", value: \"\" }];\r\n        res.Data.map((item) => {\r\n          options.push({\r\n            label: item.Display_Name,\r\n            value: item[Value],\r\n            ...item,\r\n          });\r\n        });\r\n        return options;\r\n      }\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type);\r\n      this.dialogVisible = true;\r\n      // if (type === \"view\") {\r\n      //   this.dialogTitle = \"查看\";\r\n      //   this.currentComponent = null;\r\n      //   this.componentsConfig = {\r\n      //     ID: row.Id,\r\n      //     disabled: true,\r\n      //     title: \"查看\",\r\n      //     ...row,\r\n      //   };\r\n      // }\r\n      // else if (type === 'edit') {\r\n      //   this.dialogTitle = '编辑'\r\n      //   this.componentsConfig = {\r\n      //     ID: row.ID,\r\n      //     disabled: false,\r\n      //     title: '编辑'\r\n      //   }\r\n      // }\r\n    },\r\n    // async handleExport() {\r\n    //   console.log(this.ruleForm)\r\n    //   const res = await ExportDataList({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: false,\r\n    //     Ids: this.tableSelection.map((item) => item.Id),\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n    // async handleAllExport() {\r\n    //   const res = await ExportDataList({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: true,\r\n    //     Ids: [],\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n    // v2 版本导出\r\n    // async handleAllExport() {\r\n    //   const res = await ExportData({\r\n    //     ...this.ruleForm,\r\n    //   });\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res);\r\n    //     downloadFile(res.Data, \"21\");\r\n    //   } else {\r\n    //     this.$message.error(res.Message);\r\n    //   }\r\n    // },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.init();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.init();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}