{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\processDocIssuance\\processDocIssuanceList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\processDocIssuance\\processDocIssuanceList\\index.vue", "mtime": 1755674552431}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk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file": "index.vue", "sourceRoot": "src/views/business/processDocIssuance/processDocIssuanceList", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <!-- <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog> -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\n\r\n// import { downloadFile } from \"@/utils/downloadFile\";\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport {\r\n  GetPageList,\r\n  GetDetail,\r\n  Distribute,\r\n  ExportData,\r\n} from \"@/api/business/processDocIssuance\";\r\nimport { GetDictionaryDetailListByCode } from \"@/api/sys\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/mixins/export.js\";\r\n// import * as moment from 'moment'\r\n// import dayjs from \"dayjs\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  mixins: [addRouterPage, exportInfo],\r\n  // mixins: [deviceTypeMixins, otherMixin],\r\n  data() {\r\n    return {\r\n      currentComponent: null,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"\",\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        ExcuteStartTime: null,\r\n        ExcuteEndTime: null,\r\n        ExcuteDate: [],\r\n        FileName: \"\",\r\n        EquipName: \"\",\r\n        EquipCode: \"\",\r\n        CreateTime: [],\r\n        CreateStartTime: null,\r\n        CreateEndTime: null,\r\n        Status: \"\",\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"ExcuteDate\",\r\n            label: \"下发时间\",\r\n            type: \"datePicker\",\r\n            otherOptions: {\r\n              type: \"daterange\",\r\n              rangeSeparator: \"至\",\r\n              startPlaceholder: \"开始日期\",\r\n              endPlaceholder: \"结束日期\",\r\n              clearable: true,\r\n              valueFormat: \"yyyy-MM-dd\",\r\n            },\r\n            change: (e) => {\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.ExcuteStartTime = e[0] + ' 00:00:00';\r\n                this.ruleForm.ExcuteEndTime = e[1] + ' 23:59:59';\r\n              } else {\r\n                this.ruleForm.ExcuteStartTime = null;\r\n                this.ruleForm.ExcuteEndTime = null;\r\n              }\r\n            },\r\n          },\r\n          {\r\n            key: \"FileName\", // 字段ID\r\n            label: \"文件名称\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            width: \"240px\",\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"EquipName\", // 字段ID\r\n            label: \"下发设备\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"EquipCode\",\r\n            label: \"设备编码\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"CreateTime\",\r\n            label: \"创建时间\",\r\n            type: \"datePicker\",\r\n            otherOptions: {\r\n              type: \"daterange\",\r\n              rangeSeparator: \"至\",\r\n              startPlaceholder: \"开始日期\",\r\n              endPlaceholder: \"结束日期\",\r\n              clearable: true,\r\n              valueFormat: \"yyyy-MM-dd\",\r\n            },\r\n            change: (e) => {\r\n              if (e.length > 0) {\r\n                this.ruleForm.CreateStartTime = e[0] + ' 00:00:00';\r\n                this.ruleForm.CreateEndTime = e[1] + ' 23:59:59';\r\n              } else {\r\n                this.ruleForm.CreateStartTime = null;\r\n                this.ruleForm.CreateEndTime = null;\r\n              }\r\n            },\r\n          },\r\n          {\r\n            key: \"Status\", // 字段ID\r\n            label: \"下发状态\", // Form的label\r\n            type: \"select\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            options: [\r\n              {\r\n                label: \"下发中\",\r\n                value: \"1\",\r\n              },\r\n              {\r\n                label: \"下发成功\",\r\n                value: \"2\",\r\n              },\r\n              {\r\n                label: \"下发失败\",\r\n                value: \"3\",\r\n              },\r\n            ],\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                // this.handleAllExport();\r\n                this.ExportData(this.ruleForm, \"工艺文件下发\", ExportData);\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: \"下发时间\",\r\n            key: \"DistributeDate\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"文件名称\",\r\n            key: \"FileName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"下发设备\",\r\n            key: \"EquipName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"设备编码\",\r\n            key: \"EquipCode\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"设备品牌\",\r\n            key: \"Brand\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"创建来源\",\r\n            key: \"CreateSource\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"创建时间\",\r\n            key: \"CreateDate\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"下发状态\",\r\n            key: \"StatusName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 200,\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看详情\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              // this.handleEdit(index, row, \"view\");\r\n              this.$router.push({\r\n                name: \"processDocIssuanceListView\",\r\n                query: { pg_redirect: this.$route.name, Id: row.Id },\r\n              });\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"重新下发\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              // this.handleEdit(index, row, \"edit\");\r\n              this.$confirm(\"是否确认重新下发该数据？\", \"操作确认\", {\r\n                type: \"warning\",\r\n                dangerouslyUseHTMLString: true,\r\n              })\r\n                .then(async (_) => {\r\n                  const res = await Distribute({\r\n                    Id: row.Id,\r\n                  });\r\n                  if (res.IsSucceed) {\r\n                    this.$message.success(\"操作成功\");\r\n                    this.init();\r\n                  } else {\r\n                    this.$message.error(res.Message);\r\n                  }\r\n                })\r\n                .catch((_) => {});\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      deceiveTypeList: [],\r\n\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/view\",\r\n          hidden: true,\r\n          component: () => import(\"./dialog/view.vue\"),\r\n          meta: { title: \"工艺文件下发详情\" },\r\n          name: \"processDocIssuanceListView\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  computed: {},\r\n  mounted() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.ExcuteStartTime = null;\r\n      this.ruleForm.ExcuteEndTime = null;\r\n      this.ruleForm.CreateStartTime = null;\r\n      this.ruleForm.CreateEndTime = null;\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.GetDataList();\r\n    },\r\n    init() {\r\n      this.GetDataList();\r\n    },\r\n    async GetDataList() {\r\n      this.customTableConfig.loading = true;\r\n      let res = await GetPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      this.customTableConfig.loading = false;\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.Total;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    async getDictionaryDetailListByCode(dictionaryCode = \"deviceType\", Value) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode,\r\n      });\r\n      if (res.IsSucceed) {\r\n        const options = [{ label: \"全部\", value: \"\" }];\r\n        res.Data.map((item) => {\r\n          options.push({\r\n            label: item.Display_Name,\r\n            value: item[Value],\r\n            ...item,\r\n          });\r\n        });\r\n        return options;\r\n      }\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type);\r\n      this.dialogVisible = true;\r\n      if (type === \"view\") {\r\n        this.dialogTitle = \"查看\";\r\n        this.currentComponent = null;\r\n        this.componentsConfig = {\r\n          ID: row.Id,\r\n          disabled: true,\r\n          title: \"查看\",\r\n          ...row,\r\n        };\r\n      }\r\n      // else if (type === 'edit') {\r\n      //   this.dialogTitle = '编辑'\r\n      //   this.componentsConfig = {\r\n      //     ID: row.ID,\r\n      //     disabled: false,\r\n      //     title: '编辑'\r\n      //   }\r\n      // }\r\n    },\r\n    // async handleExport() {\r\n    //   console.log(this.ruleForm)\r\n    //   const res = await ExportDataList({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: false,\r\n    //     Ids: this.tableSelection.map((item) => item.Id),\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n    // async handleAllExport() {\r\n    //   const res = await ExportDataList({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: true,\r\n    //     Ids: [],\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n    // v2 版本导出\r\n    // async handleAllExport() {\r\n    //   const res = await ExportData({\r\n    //     ...this.ruleForm,\r\n    //   });\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res);\r\n    //     downloadFile(res.Data, \"21\");\r\n    //   } else {\r\n    //     this.$message.error(res.Message);\r\n    //   }\r\n    // },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.init();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.init();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}