{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\taskCenter\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\taskCenter\\index.vue", "mtime": 1755674552423}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/eventManagement/taskCenter", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport getGridByCode from '../../safetyManagement/mixins/index'\r\nimport DialogForm from './dialogForm.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport dayjs from 'dayjs'\r\n\r\nimport {\r\n  GetTaskPageList,\r\n  UpdateTask,\r\n  GetTypesByModule\r\n} from '@/api/business/eventManagement'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  mixins: [getGridByCode],\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '编辑',\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        TaskType: '',\r\n        TaskName: '',\r\n        Status: '0',\r\n        TaskBeg: null,\r\n        TaskEnd: null\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'TaskType',\r\n            label: '任务类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'TaskName',\r\n            label: '任务名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Status',\r\n            label: '任务状态',\r\n            type: 'select',\r\n            options: [\r\n              {\r\n                label: '全部',\r\n                value: '0'\r\n              },\r\n              {\r\n                label: '未完成',\r\n                value: '1'\r\n              },\r\n              {\r\n                label: '已完成 ',\r\n                value: '2'\r\n              },\r\n              {\r\n                label: '已超期',\r\n                value: '3'\r\n              },\r\n              {\r\n                label: '超期完成',\r\n                value: '4'\r\n              }\r\n            ],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Date', // 字段ID\r\n            label: '任务开始时间', // Form的label\r\n            type: 'datePicker', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              type: 'daterange',\r\n              disabled: false,\r\n              placeholder: '请输入...'\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.TaskBeg = dayjs(e[0]).format('YYYY-MM-DD')\r\n                this.ruleForm.TaskEnd = dayjs(e[1]).format('YYYY-MM-DD')\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: []\r\n          //   {\r\n          //     text: \"导出\",\r\n          //     onclick: (item) => {\r\n          //       console.log(item);\r\n          //       this.handleExport();\r\n          //     },\r\n          //   },\r\n          // ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center',\r\n              fixed: 'left'\r\n            }\r\n          },\r\n          {\r\n            label: '任务开始时间',\r\n            key: 'BegTime',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '计划完成时间',\r\n            key: 'EndTime',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '实际完成时间',\r\n            key: 'DoneTime',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '任务状态',\r\n            key: 'StatusDisplay',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '负责人',\r\n            key: 'ActualReceiveUsersNames',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '任务名称',\r\n            key: 'Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n\r\n          {\r\n            label: '通知方式',\r\n            key: 'MessageNoticeModeDisplay',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '任务类型',\r\n            key: 'TaskType',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '来源',\r\n            key: 'SourceName',\r\n            width: 180,\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '业务模块',\r\n            key: 'Module',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '操作人',\r\n            key: 'ModifyUserName',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '备注',\r\n            key: 'Remark',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center',\r\n          width: '180'\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              // this.handleEdit(row);\r\n              const platform = 'digitalfactory'\r\n              const code = 'szgc'\r\n              const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\r\n              let url = ''\r\n              // if (row.Module == \"能耗管理\") {\r\n              //   url = \"/business/energy/alarmDetail\";\r\n              // } else if (row.Module == \"车辆道闸\") {\r\n              //   url = \"/bussiness/vehicle/alarm-info\";\r\n              // } else if (row.Module == \"门禁管理\") {\r\n              //   url = \"/business/AccessControlAlarmDetails\";\r\n              // } else if (row.Module == \"安防管理\") {\r\n              //   url = \"/business/equipmentAlarm\";\r\n              // } else if (row.Module == \"危化品管理\") {\r\n              //   url = \"/business/hazchem/alarmInformation\";\r\n              // } else\r\n              if (row.Module == '环境管理') {\r\n                url = '/business/environment/alarmInformation'\r\n              } else if (row.Module == '访客管理') {\r\n                url = '/business/visitorList'\r\n                console.log('访客管理')\r\n              }\r\n              this.$qiankun.switchMicroAppFn(platform, code, id, url)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init()\r\n    this.GetTypesByModule()\r\n  },\r\n  methods: {\r\n    async GetTypesByModule() {\r\n      const res = await GetTypesByModule({\r\n        Type: '3',\r\n        Module: ''\r\n      })\r\n      console.log(res, 'res')\r\n      if (res.IsSucceed) {\r\n        const result = res.Data || []\r\n        const typeList = result.map((item) => ({\r\n          value: item,\r\n          label: item\r\n        }))\r\n        this.customForm.formItems.find(\r\n          (item) => item.key == 'TaskType'\r\n        ).options = typeList\r\n      }\r\n    },\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.TaskBeg = null\r\n      this.ruleForm.TaskEnd = null\r\n      this.ruleForm.Date = null\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getTaskPageList()\r\n    },\r\n\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.getTaskPageList()\r\n    },\r\n    async getTaskPageList() {\r\n      const res = await GetTaskPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n        // if (this.customTableConfig.tableData.length > 0) {\r\n        //   this.customTableConfig.tableData.map((v) => {\r\n        //     v.Warning_First_Time =\r\n        //       (v.Warning_First_Time ?? \"\") != \"\"\r\n        //         ? dayjs(v.Warning_First_Time).format(\"YYYY-MM-DD HH:mm:ss\")\r\n        //         : \"\";\r\n        //     v.Warning_Last_Time =\r\n        //       (v.Warning_Last_Time ?? \"\") != \"\"\r\n        //         ? dayjs(v.Warning_Last_Time).format(\"YYYY-MM-DD HH:mm:ss\")\r\n        //         : \"\";\r\n        //   });\r\n        // }\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    async handleExport() {\r\n      const res = await ExportEntranceWarning({\r\n        id: this.tableSelection.map((item) => item.Id).toString(),\r\n        code: 'AccessControlAlarmDetails1'\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '告警明细数据')\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.getTaskPageList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.getTaskPageList()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    },\r\n    handleEdit(row) {\r\n      this.dialogVisible = true\r\n      this.componentsConfig.Data = row\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  width: 100%;\r\n}\r\n</style>\r\n"]}]}