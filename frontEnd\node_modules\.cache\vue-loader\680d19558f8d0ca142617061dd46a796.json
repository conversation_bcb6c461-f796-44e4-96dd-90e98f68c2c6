{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\taskCenter\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\taskCenter\\index.vue", "mtime": 1755506574324}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/eventManagement/taskCenter", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        />\n      </template>\n    </CustomLayout>\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <component\n        :is=\"currentComponent\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n      /></el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\nimport getGridByCode from '../../safetyManagement/mixins/index'\nimport DialogForm from './dialogForm.vue'\n\nimport { downloadFile } from '@/utils/downloadFile'\nimport dayjs from 'dayjs'\n\nimport {\n  GetTaskPageList,\n  UpdateTask,\n  GetTypesByModule\n} from '@/api/business/eventManagement'\nexport default {\n  name: '',\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout\n  },\n  mixins: [getGridByCode],\n  data() {\n    return {\n      currentComponent: DialogForm,\n      componentsConfig: {\n        Data: {}\n      },\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true\n        },\n        close: () => {\n          this.dialogVisible = false\n          this.onFresh()\n        }\n      },\n      dialogVisible: false,\n      dialogTitle: '编辑',\n      tableSelection: [],\n      ruleForm: {\n        TaskType: '',\n        TaskName: '',\n        Status: '0',\n        TaskBeg: null,\n        TaskEnd: null\n      },\n      customForm: {\n        formItems: [\n          {\n            key: 'TaskType',\n            label: '任务类型',\n            type: 'select',\n            options: [],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'TaskName',\n            label: '任务名称',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'Status',\n            label: '任务状�?,\n            type: 'select',\n            options: [\n              {\n                label: '全部',\n                value: '0'\n              },\n              {\n                label: '未完�?,\n                value: '1'\n              },\n              {\n                label: '已完�?',\n                value: '2'\n              },\n              {\n                label: '已超�?,\n                value: '3'\n              },\n              {\n                label: '超期完成',\n                value: '4'\n              }\n            ],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'Date', // 字段ID\n            label: '任务开始时�?, // Form的label\n            type: 'datePicker', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              type: 'daterange',\n              disabled: false,\n              placeholder: '请输�?..'\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n              if (e && e.length > 0) {\n                this.ruleForm.TaskBeg = dayjs(e[0]).format('YYYY-MM-DD')\n                this.ruleForm.TaskEnd = dayjs(e[1]).format('YYYY-MM-DD')\n              }\n            }\n          }\n        ],\n        rules: {},\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        buttonConfig: {\n          buttonList: []\n          //   {\n          //     text: \"导出\",\n          //     onclick: (item) => {\n          //       console.log(item);\n          //       this.handleExport();\n          //     },\n          //   },\n          // ],\n        },\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        tableColumns: [\n          {\n            otherOptions: {\n              type: 'selection',\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '任务开始时�?,\n            key: 'BegTime',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '计划完成时间',\n            key: 'EndTime',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '实际完成时间',\n            key: 'DoneTime',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '任务状�?,\n            key: 'StatusDisplay',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '负责�?,\n            key: 'ActualReceiveUsersNames',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '任务名称',\n            key: 'Name',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n\n          {\n            label: '通知方式',\n            key: 'MessageNoticeModeDisplay',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '任务类型',\n            key: 'TaskType',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '来源',\n            key: 'SourceName',\n            width: 180,\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '业务模块',\n            key: 'Module',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '操作�?,\n            key: 'ModifyUserName',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '备注',\n            key: 'Remark',\n            otherOptions: {\n              align: 'center'\n            }\n          }\n        ],\n        tableData: [],\n        operateOptions: {\n          align: 'center',\n          width: '180'\n        },\n        tableActions: [\n          {\n            actionLabel: '查看详情',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              // this.handleEdit(row);\n              const platform = 'digitalfactory'\n              const code = 'szgc'\n              const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\n              let url = ''\n              // if (row.Module == \"能耗管�?) {\n              //   url = \"/business/energy/alarmDetail\";\n              // } else if (row.Module == \"车辆道闸\") {\n              //   url = \"/bussiness/vehicle/alarm-info\";\n              // } else if (row.Module == \"门禁管理\") {\n              //   url = \"/business/AccessControlAlarmDetails\";\n              // } else if (row.Module == \"安防管理\") {\n              //   url = \"/business/equipmentAlarm\";\n              // } else if (row.Module == \"危化品管�?) {\n              //   url = \"/business/hazchem/alarmInformation\";\n              // } else\n              if (row.Module == '环境管理') {\n                url = '/business/environment/alarmInformation'\n              } else if (row.Module == '访客管理') {\n                url = '/business/visitorList'\n                console.log('访客管理')\n              }\n              this.$qiankun.switchMicroAppFn(platform, code, id, url)\n            }\n          },\n          {\n            actionLabel: '编辑',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.handleEdit(row)\n            }\n          }\n        ]\n      }\n    }\n  },\n  computed: {},\n  created() {\n    this.init()\n    this.GetTypesByModule()\n  },\n  methods: {\n    async GetTypesByModule() {\n      const res = await GetTypesByModule({\n        Type: '3',\n        Module: ''\n      })\n      console.log(res, 'res')\n      if (res.IsSucceed) {\n        const result = res.Data || []\n        const typeList = result.map((item) => ({\n          value: item,\n          label: item\n        }))\n        this.customForm.formItems.find(\n          (item) => item.key == 'TaskType'\n        ).options = typeList\n      }\n    },\n    searchForm(data) {\n      console.log(data)\n      this.customTableConfig.currentPage = 1\n      this.onFresh()\n    },\n    resetForm() {\n      this.ruleForm.TaskBeg = null\n      this.ruleForm.TaskEnd = null\n      this.ruleForm.Date = null\n      this.onFresh()\n    },\n    onFresh() {\n      this.getTaskPageList()\n    },\n\n    init() {\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\n      this.getTaskPageList()\n    },\n    async getTaskPageList() {\n      const res = await GetTaskPageList({\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n        ...this.ruleForm\n      })\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data\n        this.customTableConfig.total = res.Data.TotalCount\n        // if (this.customTableConfig.tableData.length > 0) {\n        //   this.customTableConfig.tableData.map((v) => {\n        //     v.Warning_First_Time =\n        //       (v.Warning_First_Time ?? \"\") != \"\"\n        //         ? dayjs(v.Warning_First_Time).format(\"YYYY-MM-DD HH:mm:ss\")\n        //         : \"\";\n        //     v.Warning_Last_Time =\n        //       (v.Warning_Last_Time ?? \"\") != \"\"\n        //         ? dayjs(v.Warning_Last_Time).format(\"YYYY-MM-DD HH:mm:ss\")\n        //         : \"\";\n        //   });\n        // }\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n    async handleExport() {\n      const res = await ExportEntranceWarning({\n        id: this.tableSelection.map((item) => item.Id).toString(),\n        code: 'AccessControlAlarmDetails1'\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        downloadFile(res.Data, '告警明细数据')\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.getTaskPageList()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`)\n      this.customTableConfig.currentPage = val\n      this.getTaskPageList()\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection\n    },\n    handleEdit(row) {\n      this.dialogVisible = true\n      this.componentsConfig.Data = row\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.layout {\n  height: calc(100vh - 90px);\n  width: 100%;\n}\n</style>\n"]}]}