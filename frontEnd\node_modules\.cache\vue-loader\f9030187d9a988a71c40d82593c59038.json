{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControlV2\\statisticalAnalysisOfAccessControl\\index.vue?vue&type=style&index=0&id=031fbc6d&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControlV2\\statisticalAnalysisOfAccessControl\\index.vue", "mtime": 1755674552410}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hbmFseXNpc0JveCB7DQogIC8vIHBhZGRpbmc6IDEwcHggMTVweDsNCiAgLy8gaGVpZ2h0OiBjYWxjKDEwMHZoIC0gOTBweCk7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/accessControlV2/statisticalAnalysisOfAccessControl", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 analysisBox\">\r\n    <top :params-obj=\"crjr\" />\r\n    <middle :params-arr=\"paramsArr\" />\r\n    <bottom :params-obj=\"rllqs\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Top from './components/top.vue'\r\nimport Middle from './components/middle.vue'\r\nimport Bottom from './components/bottom.vue'\r\nimport { GetJumpUrl } from '@/api/business/accessControlV2.js'\r\nexport default {\r\n  components: {\r\n    Top,\r\n    Middle,\r\n    Bottom\r\n  },\r\n  data() {\r\n    return {\r\n      crjr: {},\r\n      paramsArr: [],\r\n      rllqs: {}\r\n    }\r\n  },\r\n  created() {\r\n    this.getJumpUrl()\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n    async getJumpUrl() {\r\n      const res = await GetJumpUrl()\r\n      if (res.IsSucceed) {\r\n        res.Data.map(i => {\r\n          if (i.ViewMore == '最新出入记录') {\r\n            this.crjr = i\r\n          } else if (i.ViewMore == '园区人流量趋势') {\r\n            this.rllqs = i\r\n          } else {\r\n            this.paramsArr.push(i)\r\n          }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang='scss'>\r\n.analysisBox {\r\n  // padding: 10px 15px;\r\n  // height: calc(100vh - 90px);\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n"]}]}