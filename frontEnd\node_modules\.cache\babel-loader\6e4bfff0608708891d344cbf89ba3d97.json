{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomLayout\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomLayout\\index.vue", "mtime": 1755505758775}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICAvLyBsYXlvdXRDb25maWcKICBwcm9wczogewogICAgbGF5b3V0Q29uZmlnOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7fQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7fTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICBpc1Nob3dTZWFyY2hGb3JtOiBmdW5jdGlvbiBpc1Nob3dTZWFyY2hGb3JtKCkgewogICAgICByZXR1cm4gdGhpcy5sYXlvdXRPYmogJiYgdGhpcy5sYXlvdXRPYmouaXNTaG93U2VhcmNoRm9ybSB8fCB0cnVlOwogICAgfSwKICAgIGlzU2hvd0xheW91dENoYXJ0OiBmdW5jdGlvbiBpc1Nob3dMYXlvdXRDaGFydCgpIHsKICAgICAgcmV0dXJuIHRoaXMubGF5b3V0T2JqICYmIHRoaXMubGF5b3V0T2JqLmlzU2hvd0xheW91dENoYXJ0IHx8IGZhbHNlOwogICAgfSwKICAgIGxheW91dE9iajogZnVuY3Rpb24gbGF5b3V0T2JqKCkgewogICAgICByZXR1cm4gdGhpcy5sYXlvdXRDb25maWc7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["props", "layoutConfig", "type", "Object", "default", "data", "computed", "isShowSearchForm", "layout<PERSON><PERSON>j", "isShowLayoutChart"], "sources": ["src/businessComponents/CustomLayout/index.vue"], "sourcesContent": ["<template>\n  <div class=\"CustomLayout\">\n    <div v-if=\"isShowSearchForm\" class=\"searchForm\">\n      <slot name=\"searchForm\" />\n    </div>\n    <div v-if=\"isShowLayoutChart\" class=\"layoutChart\">\n      <slot name=\"layoutChart\" />\n    </div>\n    <div class=\"layoutTable\">\n      <slot name=\"layoutTable\" />\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  // layoutConfig\n  props: {\n    layoutConfig: {\n      type: Object,\n      default: () => { }\n    }\n  },\n  data() {\n    return {}\n  },\n  computed: {\n    isShowSearchForm() {\n      return (this.layoutObj && this.layoutObj.isShowSearchForm) || true\n    },\n    isShowLayoutChart() {\n      return (this.layoutObj && this.layoutObj.isShowLayoutChart) || false\n    },\n    layoutObj() {\n      return this.layoutConfig\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.CustomLayout {\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  height: 100%;\n  .searchForm {\n    // margin: 15px 15px 0px 15px;\n    padding: 10px 15px;\n    background-color: white;\n  }\n  .layoutChart {\n    margin: 15px 15px 0px 15px;\n    padding: 10px 15px;\n    background-color: white;\n  }\n  .layoutTable {\n    flex: 1;\n    margin: 10px 0 0 0;\n    padding: 10px 15px;\n    background-color: white;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;AAeA;EACA;EACAA,KAAA;IACAC,YAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;EACA;EACAC,QAAA;IACAC,gBAAA,WAAAA,iBAAA;MACA,YAAAC,SAAA,SAAAA,SAAA,CAAAD,gBAAA;IACA;IACAE,iBAAA,WAAAA,kBAAA;MACA,YAAAD,SAAA,SAAAA,SAAA,CAAAC,iBAAA;IACA;IACAD,SAAA,WAAAA,UAAA;MACA,YAAAP,YAAA;IACA;EACA;AACA", "ignoreList": []}]}