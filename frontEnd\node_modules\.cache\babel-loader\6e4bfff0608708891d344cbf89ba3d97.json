{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomLayout\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomLayout\\index.vue", "mtime": 1755674552400}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICAvLyBsYXlvdXRDb25maWcKICBwcm9wczogewogICAgbGF5b3V0Q29uZmlnOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7fQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7fTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICBpc1Nob3dTZWFyY2hGb3JtOiBmdW5jdGlvbiBpc1Nob3dTZWFyY2hGb3JtKCkgewogICAgICByZXR1cm4gdGhpcy5sYXlvdXRPYmogJiYgdGhpcy5sYXlvdXRPYmouaXNTaG93U2VhcmNoRm9ybSB8fCB0cnVlOwogICAgfSwKICAgIGlzU2hvd0xheW91dENoYXJ0OiBmdW5jdGlvbiBpc1Nob3dMYXlvdXRDaGFydCgpIHsKICAgICAgcmV0dXJuIHRoaXMubGF5b3V0T2JqICYmIHRoaXMubGF5b3V0T2JqLmlzU2hvd0xheW91dENoYXJ0IHx8IGZhbHNlOwogICAgfSwKICAgIGxheW91dE9iajogZnVuY3Rpb24gbGF5b3V0T2JqKCkgewogICAgICByZXR1cm4gdGhpcy5sYXlvdXRDb25maWc7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["props", "layoutConfig", "type", "Object", "default", "data", "computed", "isShowSearchForm", "layout<PERSON><PERSON>j", "isShowLayoutChart"], "sources": ["src/businessComponents/CustomLayout/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"CustomLayout\">\r\n    <div v-if=\"isShowSearchForm\" class=\"searchForm\">\r\n      <slot name=\"searchForm\" />\r\n    </div>\r\n    <div v-if=\"isShowLayoutChart\" class=\"layoutChart\">\r\n      <slot name=\"layoutChart\" />\r\n    </div>\r\n    <div class=\"layoutTable\">\r\n      <slot name=\"layoutTable\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  // layoutConfig\r\n  props: {\r\n    layoutConfig: {\r\n      type: Object,\r\n      default: () => { }\r\n    }\r\n  },\r\n  data() {\r\n    return {}\r\n  },\r\n  computed: {\r\n    isShowSearchForm() {\r\n      return (this.layoutObj && this.layoutObj.isShowSearchForm) || true\r\n    },\r\n    isShowLayoutChart() {\r\n      return (this.layoutObj && this.layoutObj.isShowLayoutChart) || false\r\n    },\r\n    layoutObj() {\r\n      return this.layoutConfig\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.CustomLayout {\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n  .searchForm {\r\n    margin: 0 0 10px 0;\r\n    padding: 10px 15px;\r\n    background-color: white;\r\n  }\r\n  .layoutChart {\r\n    margin: 15px 15px 0px 15px;\r\n    padding: 10px 15px;\r\n    background-color: white;\r\n  }\r\n  .layoutTable {\r\n    flex: 1;\r\n    // margin: 10px 15px;\r\n    padding: 10px 15px;\r\n    background-color: white;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;AAeA;EACA;EACAA,KAAA;IACAC,YAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;EACA;EACAC,QAAA;IACAC,gBAAA,WAAAA,iBAAA;MACA,YAAAC,SAAA,SAAAA,SAAA,CAAAD,gBAAA;IACA;IACAE,iBAAA,WAAAA,kBAAA;MACA,YAAAD,SAAA,SAAAA,SAAA,CAAAC,iBAAA;IACA;IACAD,SAAA,WAAAA,UAAA;MACA,YAAAP,YAAA;IACA;EACA;AACA", "ignoreList": []}]}