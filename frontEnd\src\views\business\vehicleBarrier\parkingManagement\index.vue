<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
          ><template #customBtn="{ slotScope }"
            ><el-button
              type="text"
              :disabled="true"
              @click="handelStart(slotScope)"
              >{{ slotScope.Status == 1 ? "停用" : "启用" }}</el-button
            ></template
          ></CustomTable
        >
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @closed="closedDialog"
    >
      <component
        :is="currentComponent"
        ref="dialogRef"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";

import baseInfo from "./dialog/baseInfo.vue";
import trafficRegulations from "./dialog/trafficRegulations";
import importDialog from "@/views/business/vehicleBarrier/components/import.vue";

import exportInfo from "@/views/business/vehicleBarrier/mixins/export.js";
import {
  GetPageList,
  DelPacking,
  SetPackingStatus,
  ExportData,
  ImportDataStream,
} from "@/api/business/vehicleBarrier.js";

export default {
  Name: "",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
    trafficRegulations,
    baseInfo,
    importDialog,
  },
  mixins: [exportInfo],
  data() {
    return {
      currentComponent: baseInfo,
      componentsConfig: {
        interfaceName: ImportDataStream,
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogVisible: false,
      dialogTitle: "",
      tableSelection: [],
      selectIds: [],
      ruleForm: {
        Name: "",
        Position: "",
        Status: null, // 0禁用 1启用
      },
      customForm: {
        formItems: [
          {
            key: "Name", // 字段ID
            label: "停车场名称", // Form的label
            type: "input", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            // placeholder: '请输入输入停车场名称',
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
            },
            width: "240px",
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
          {
            key: "Position",
            label: "停车场地址",
            type: "input",
            // placeholder: '请输入停车场地址',
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
            },
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "Status", // 字段ID
            label: "状态", // Form的label
            type: "select", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            // placeholder: '请选择状态',
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
            },
            options: [
              {
                label: "启用",
                value: 1,
              },
              {
                label: "停用",
                value: 0,
              },
            ],
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "新增",
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: true, // 是否禁用
              icon: "", //  图标
              autofocus: false, // 是否聚焦
              type: "primary", // primary / success / warning / danger / info / text
              size: "small", // medium / small / mini
              onclick: (item) => {
                console.log(item);
                this.handleCreate();
              },
            },
            {
              text: "下载模板",
              disabled: true, // 是否禁用
              onclick: (item) => {
                console.log(item);
                this.ExportData({}, "停车场管理模板", ExportData);
              },
            },
            {
              text: "批量导入",
              disabled: true, // 是否禁用
              onclick: (item) => {
                console.log(item);
                this.currentComponent = "importDialog";
                this.dialogVisible = true;
                this.dialogTitle = "批量导入";
              },
            },
            {
              key: "batch",
              disabled: false, // 是否禁用
              text: "批量导出",
              onclick: (item) => {
                console.log(item);
                this.ExportData(
                  {
                    ...this.ruleForm,
                    Ids: this.selectIds.toString(),
                  },
                  "停车场管理",
                  ExportData
                );
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        height: "100%",
        tableActionsWidth: 280,
        tableColumns: [
          {
            width: 50,
            otherOptions: {
              type: "selection",
              align: "center",
            },
          },
          {
            label: "停车场名称",
            key: "Name",
          },
          {
            label: "停车场位置",
            key: "PositionInfo",
            width: 240,
          },
          {
            label: "停车场编码",
            key: "Code",
          },
          {
            label: "停车位置总数量",
            key: "TotalSpace",
            width: 120,
          },
          {
            label: "固定停车位数量",
            key: "FixedSpace",
            width: 120,
          },
          {
            label: "临时停车位数量",
            key: "TempSpace",
            width: 140,
          },
          {
            label: "出入口数量",
            key: "TotalGateway",
          },
          {
            label: "出口数量",
            key: "ExitNum",
          },
          {
            label: "入口数量",
            key: "EntranceNum",
          },
          {
            label: "状态",
            key: "StatusName",
          },
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: "编辑",
            otherOptions: {
              type: "text",
              disabled: true, // 是否禁用
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, "edit");
            },
          },
          {
            actionLabel: "删除",
            otherOptions: {
              type: "text",
              disabled: true, // 是否禁用
            },
            onclick: (index, row) => {
              this.handleDelete(index, row);
            },
          },
          {
            actionLabel: "查看详情",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, "view");
            },
          },
          {
            actionLabel: "配置通行规则",
            otherOptions: {
              type: "text",
              disabled: true, // 是否禁用
            },
            onclick: (index, row) => {
              this.configRules(index, row, "view");
            },
          },
        ],
        operateOptions: {
          width: 300, // 操作栏宽度
        },
      },
    };
  },
  computed: {},
  created() {
    this.init();
  },
  methods: {
    searchForm(data) {
      this.customTableConfig.currentPage = 1;
      console.log(data);
      this.onFresh();
    },
    resetForm() {
      this.onFresh();
    },
    onFresh() {
      this.fetchData();
    },
    init() {
      this.fetchData();
    },
    async fetchData() {
      const res = await GetPageList({
        ParameterJson: [
          {
            Key: "",
            Value: [null],
            Type: "",
            Filter_Type: "",
          },
        ],
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data.map((v) => {
          v.StatusName = v.Status == 1 ? "启用" : "停用";

          return v;
        });
        console.log(res);
        this.customTableConfig.total = res.Data.Total;
      }
    },
    handleCreate() {
      this.currentComponent = "baseInfo";
      this.dialogTitle = "新增";
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.dialogRef.init(0, {}, "add");
      });
    },
    handleDelete(index, row) {
      console.log(index, row);
      console.log(this);
      this.$confirm("确认删除?", {
        type: "warning",
      })
        .then(async (_) => {
          const res = await DelPacking({
            Id: row.Id,
          });
          if (res.IsSucceed) {
            this.$message({
              message: "删除成功",
              type: "success",
            });
            this.init();
          } else {
            this.$message({
              message: res.Message,
              type: "error",
            });
          }
        })
        .catch((_) => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleEdit(index, row, type) {
      console.log(index, row, type);
      this.currentComponent = "baseInfo";
      if (type === "view") {
        this.dialogTitle = "查看";
      } else if (type === "edit") {
        this.dialogTitle = "编辑";
      }
      this.$nextTick(() => {
        this.$refs.dialogRef.init(index, row, type);
      });

      this.dialogVisible = true;
    },
    configRules(index, row, type) {
      this.dialogTitle = "配置通行规则";
      this.currentComponent = "trafficRegulations";
      this.$nextTick(() => {
        this.$refs.dialogRef.init(row.Id);
      });
      this.dialogVisible = true;
    },
    // 关闭弹窗
    closedDialog() {
      this.$refs.dialogRef.closeClearForm();
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.onFresh();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.onFresh();
    },
    handleSelectionChange(selection) {
      const Ids = [];
      this.tableSelection = selection;
      this.tableSelection.forEach((item) => {
        Ids.push(item.Id);
      });
      console.log(Ids);
      this.selectIds = Ids;
      // console.log(this.tableSelection)
      // if (this.tableSelection.length > 0) {
      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = false
      // } else {
      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = true
      // }
    },
    // 启动 停用
    handelStart(slotScope) {
      console.log(slotScope);
      SetPackingStatus({ Id: slotScope.Id }).then((res) => {
        if (res.IsSucceed) {
          this.$message({
            message: `${slotScope.Status == 1 ? "停用" : "启用"}成功`,
            type: "success",
          });
          this.onFresh();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/views/business/vehicleBarrier/index.scss";
.mt20 {
  margin-top: 10px;
}
</style>
