{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\alarmNotification\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\alarmNotification\\index.vue", "mtime": 1755674552429}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/business/myWorkBench/alarmNotification", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n        >\r\n          <template #customBtn=\"{slotScope}\"><el-button v-if=\"slotScope.Status == 1\" type=\"text\" @click=\"handelClose(slotScope)\">关闭</el-button></template>\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport { GetModule, GetTypesByModule, GetWarnPageList, GetWarningStatus, GetWarningModule, GetWarningType, CloseWarning } from '@/api/business/myWorkBench'\r\nexport default {\r\n  name: 'MyTasks',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        Name: '',\r\n        OperateType: '',\r\n        StartTime: null,\r\n        EndTime: null,\r\n        Module: '',\r\n        Type: '',\r\n        Date: [],\r\n        Status: null\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Name',\r\n            label: '告警名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Module',\r\n            label: '业务模块',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              this.getTypesByModule(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Type',\r\n            label: '告警类型',\r\n            type: 'select',\r\n            otherOptions: {\r\n              disabled: true,\r\n              clearable: true\r\n            },\r\n            options: [\r\n              {\r\n                label: '全部',\r\n                value: ''\r\n              }\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Status',\r\n            label: '状态',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Date',\r\n            label: '告警时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              type: 'datetimerange',\r\n              rangeSeparator: '至',\r\n              startPlaceholder: '开始日期',\r\n              endPlaceholder: '结束日期',\r\n              clearable: true,\r\n              valueFormat: 'yyyy-MM-dd HH:mm'\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n              if (e && e.length !== 0) {\r\n                this.ruleForm.StartTime = e[0]\r\n                this.ruleForm.EndTime = e[1]\r\n              } else {\r\n                this.ruleForm.StartTime = null\r\n                this.ruleForm.EndTime = null\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: []\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          {\r\n            label: '告警时间',\r\n            key: 'AlarmTime'\r\n          },\r\n          {\r\n            label: '状态',\r\n            key: 'Status',\r\n            render: (row) => {\r\n              const h = this.$createElement\r\n              return h('div', {}, [\r\n                h(\r\n                  'span',\r\n                  { class: row.Status == 1 ? 'warning' : row.Status == 2 ? 'close' : row.Status == 3 ? 'handel' : '' },\r\n                  ''\r\n                ),\r\n                h('span', {}, `${row.Status == 1 ? '告警中' : row.Status == 2 ? '已关闭' : row.Status == 3 ? '已处理' : ''}`)\r\n              ])\r\n            }\r\n          },\r\n          {\r\n            label: '告警类型',\r\n            key: 'Type'\r\n          },\r\n          {\r\n            label: '告警名称',\r\n            key: 'Name'\r\n          },\r\n          {\r\n            label: '来源',\r\n            key: 'Source'\r\n          },\r\n          {\r\n            label: '业务模块',\r\n            key: 'Module'\r\n          },\r\n          {\r\n            label: '处理内容',\r\n            key: 'Content'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.getDetail(row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getModule()\r\n    this.getWarningStatus()\r\n    this.onFresh()\r\n  },\r\n  methods: {\r\n    // 获取业务模块下拉\r\n    getModule() {\r\n      GetModule({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data || []\r\n          const arr = []\r\n          data.forEach((item) => {\r\n            const obj = {\r\n              label: item,\r\n              value: item\r\n            }\r\n            arr.push(obj)\r\n          })\r\n          arr.unshift({\r\n            label: '全部',\r\n            value: ''\r\n          })\r\n          console.log(arr)\r\n          this.customForm.formItems.find(\r\n            (v) => v.key == 'Module'\r\n          ).options = arr\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 获取告警类型\r\n    getTypesByModule(value) {\r\n      this.ruleForm.Type = ''\r\n      if (value) {\r\n        GetTypesByModule({ Type: 2, Module: value }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            const data = res.Data || []\r\n            const arr = []\r\n            data.forEach((item) => {\r\n              const obj = {\r\n                label: item,\r\n                value: item\r\n              }\r\n              arr.push(obj)\r\n            })\r\n            arr.unshift({\r\n              label: '全部',\r\n              value: ''\r\n            })\r\n            console.log(arr)\r\n            this.customForm.formItems.find(\r\n              (v) => v.key == 'Type'\r\n            ).otherOptions.disabled = false\r\n            this.customForm.formItems.find(\r\n              (v) => v.key == 'Type'\r\n            ).options = arr\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      } else {\r\n        this.customForm.formItems.find(\r\n          (v) => v.key == 'Type'\r\n        ).otherOptions.disabled = true\r\n        this.customForm.formItems.find((v) => v.key == 'Type').options =\r\n          []\r\n      }\r\n    },\r\n    // 获取任务状态\r\n    getWarningStatus() {\r\n      GetWarningStatus({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data || []\r\n          const arr = []\r\n          data.forEach((item) => {\r\n            const obj = {\r\n              label: item.Name,\r\n              value: item.Value\r\n            }\r\n            arr.push(obj)\r\n          })\r\n          arr.unshift({\r\n            label: '全部',\r\n            value: null\r\n          })\r\n          console.log(arr)\r\n          this.customForm.formItems.find(\r\n            (v) => v.key == 'Status'\r\n          ).options = arr\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.customForm.formItems.find(\r\n        (v) => v.key == 'Type'\r\n      ).otherOptions.disabled = true\r\n      this.customForm.formItems.find(\r\n        (v) => v.key == 'Type'\r\n      ).options = [{\r\n        label: '全部',\r\n        value: ''\r\n      }]\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.fetchData()\r\n    },\r\n    async fetchData() {\r\n      this.customTableConfig.loading = true\r\n      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null\r\n        this.ruleForm.EndTime = null\r\n      }\r\n      await GetWarnPageList({\r\n        ...this.ruleForm,\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.tableData = res.Data.Data\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.customTableConfig.loading = false\r\n      })\r\n    },\r\n\r\n    // 关闭\r\n    handelClose(row) {\r\n      this.$confirm('是否关闭?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        CloseWarning({ Id: row.Id }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '关闭成功!'\r\n            })\r\n            this.onFresh()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    // 查看详情\r\n    getDetail(item) {\r\n      console.log(item)\r\n      if (item.Url) {\r\n        let platform = '' // 子应用\r\n        if (item.ModuleName == '后台设置') {\r\n          platform = 'management'\r\n        } else {\r\n          platform = 'digitalfactory'\r\n        }\r\n        this.$qiankun.switchMicroAppFn(\r\n          platform,\r\n          item.ModuleCode,\r\n          item.ModuleId,\r\n          item.Url\r\n        )\r\n      } else {\r\n        const platform = 'digitalfactory'\r\n        const code = 'szgc'\r\n        const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\r\n        let url = ''\r\n        if (item.Module == '能耗管理') {\r\n          url = '/business/energy/alarmDetail'\r\n        } else if (item.Module == '车辆道闸') {\r\n          url = '/bussiness/vehicle/alarm-info'\r\n        } else if (item.Module == '门禁管理') {\r\n          url = '/business/AccessControlAlarmDetails'\r\n        } else if (item.Module == '安防管理') {\r\n          url = '/business/equipmentAlarm'\r\n        } else if (item.Module == '危化品管理') {\r\n          url = '/business/hazchem/alarmInformation'\r\n        } else if (item.Module == '环境管理') {\r\n          url = '/business/environment/alarmInformation'\r\n        } else if (item.Module == '访客管理') {\r\n          url = '/business/energy/alarmDetail'\r\n          console.log('访客管理')\r\n        }\r\n        this.$qiankun.switchMicroAppFn(platform, code, id, url)\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.onFresh()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.onFresh()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.layout {\r\n  height: 100%;\r\n  width: 100%;\r\n  position: absolute;\r\n  ::v-deep {\r\n    .CustomLayout {\r\n      .layoutTable {\r\n        height: 0;\r\n        .CustomTable {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          .table {\r\n            flex: 1;\r\n            height: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            .el-table {\r\n              flex: 1;\r\n              height: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.warning {\r\n  display: inline-block;\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 4px;\r\n  margin-right: 5px;\r\n  background-color: #fb6b7f;\r\n}\r\n.handel {\r\n  display: inline-block;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 5px;\r\n  margin-right: 5px;\r\n  background-color: #368dff;\r\n}\r\n.close {\r\n  display: inline-block;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 5px;\r\n  margin-right: 5px;\r\n  background-color: #37be6b;\r\n}\r\n</style>\r\n"]}]}