{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\alarmNotification\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\alarmNotification\\index.vue", "mtime": 1755506574393}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/business/myWorkBench/alarmNotification", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n        >\n          <template #customBtn=\"{slotScope}\"><el-button v-if=\"slotScope.Status == 1\" type=\"text\" @click=\"handelClose(slotScope)\">关闭</el-button></template>\n        </CustomTable>\n      </template>\n    </CustomLayout>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\nimport { GetModule, GetTypesByModule, GetWarnPageList, GetWarningStatus, GetWarningModule, GetWarningType, CloseWarning } from '@/api/business/myWorkBench'\nexport default {\n  name: 'MyTasks',\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout\n  },\n  data() {\n    return {\n      ruleForm: {\n        Name: '',\n        OperateType: '',\n        StartTime: null,\n        EndTime: null,\n        Module: '',\n        Type: '',\n        Date: [],\n        Status: null\n      },\n      customForm: {\n        formItems: [\n          {\n            key: 'Name',\n            label: '告警名称',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            width: '240px',\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'Module',\n            label: '业务模块',\n            type: 'select',\n            otherOptions: {\n              clearable: true\n            },\n            options: [],\n            change: (e) => {\n              this.getTypesByModule(e)\n            }\n          },\n          {\n            key: 'Type',\n            label: '告警类型',\n            type: 'select',\n            otherOptions: {\n              disabled: true,\n              clearable: true\n            },\n            options: [\n              {\n                label: '全部',\n                value: ''\n              }\n            ],\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'Status',\n            label: '状�?,\n            type: 'select',\n            otherOptions: {\n              clearable: true\n            },\n            options: [],\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'Date',\n            label: '告警时间',\n            type: 'datePicker',\n            otherOptions: {\n              type: 'datetimerange',\n              rangeSeparator: '�?,\n              startPlaceholder: '开始日�?,\n              endPlaceholder: '结束日期',\n              clearable: true,\n              valueFormat: 'yyyy-MM-dd HH:mm'\n            },\n            change: (e) => {\n              console.log(e)\n              if (e && e.length !== 0) {\n                this.ruleForm.StartTime = e[0]\n                this.ruleForm.EndTime = e[1]\n              } else {\n                this.ruleForm.StartTime = null\n                this.ruleForm.EndTime = null\n              }\n            }\n          }\n        ],\n        rules: {},\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        buttonConfig: {\n          buttonList: []\n        },\n        // 表格\n        loading: false,\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        height: '100%',\n        tableColumns: [\n          {\n            label: '告警时间',\n            key: 'AlarmTime'\n          },\n          {\n            label: '状�?,\n            key: 'Status',\n            render: (row) => {\n              const h = this.$createElement\n              return h('div', {}, [\n                h(\n                  'span',\n                  { class: row.Status == 1 ? 'warning' : row.Status == 2 ? 'close' : row.Status == 3 ? 'handel' : '' },\n                  ''\n                ),\n                h('span', {}, `${row.Status == 1 ? '告警�? : row.Status == 2 ? '已关�? : row.Status == 3 ? '已处�? : ''}`)\n              ])\n            }\n          },\n          {\n            label: '告警类型',\n            key: 'Type'\n          },\n          {\n            label: '告警名称',\n            key: 'Name'\n          },\n          {\n            label: '来源',\n            key: 'Source'\n          },\n          {\n            label: '业务模块',\n            key: 'Module'\n          },\n          {\n            label: '处理内容',\n            key: 'Content'\n          }\n        ],\n        tableData: [],\n        tableActions: [\n          {\n            actionLabel: '查看详情',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.getDetail(row)\n            }\n          }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.getModule()\n    this.getWarningStatus()\n    this.onFresh()\n  },\n  methods: {\n    // 获取业务模块下拉\n    getModule() {\n      GetModule({}).then((res) => {\n        if (res.IsSucceed) {\n          const data = res.Data || []\n          const arr = []\n          data.forEach((item) => {\n            const obj = {\n              label: item,\n              value: item\n            }\n            arr.push(obj)\n          })\n          arr.unshift({\n            label: '全部',\n            value: ''\n          })\n          console.log(arr)\n          this.customForm.formItems.find(\n            (v) => v.key == 'Module'\n          ).options = arr\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    // 获取告警类型\n    getTypesByModule(value) {\n      this.ruleForm.Type = ''\n      if (value) {\n        GetTypesByModule({ Type: 2, Module: value }).then((res) => {\n          if (res.IsSucceed) {\n            const data = res.Data || []\n            const arr = []\n            data.forEach((item) => {\n              const obj = {\n                label: item,\n                value: item\n              }\n              arr.push(obj)\n            })\n            arr.unshift({\n              label: '全部',\n              value: ''\n            })\n            console.log(arr)\n            this.customForm.formItems.find(\n              (v) => v.key == 'Type'\n            ).otherOptions.disabled = false\n            this.customForm.formItems.find(\n              (v) => v.key == 'Type'\n            ).options = arr\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        })\n      } else {\n        this.customForm.formItems.find(\n          (v) => v.key == 'Type'\n        ).otherOptions.disabled = true\n        this.customForm.formItems.find((v) => v.key == 'Type').options =\n          []\n      }\n    },\n    // 获取任务状�?    getWarningStatus() {\n      GetWarningStatus({}).then((res) => {\n        if (res.IsSucceed) {\n          const data = res.Data || []\n          const arr = []\n          data.forEach((item) => {\n            const obj = {\n              label: item.Name,\n              value: item.Value\n            }\n            arr.push(obj)\n          })\n          arr.unshift({\n            label: '全部',\n            value: null\n          })\n          console.log(arr)\n          this.customForm.formItems.find(\n            (v) => v.key == 'Status'\n          ).options = arr\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    searchForm(data) {\n      console.log(data)\n      this.onFresh()\n    },\n    resetForm() {\n      this.customForm.formItems.find(\n        (v) => v.key == 'Type'\n      ).otherOptions.disabled = true\n      this.customForm.formItems.find(\n        (v) => v.key == 'Type'\n      ).options = [{\n        label: '全部',\n        value: ''\n      }]\n      this.onFresh()\n    },\n    onFresh() {\n      this.fetchData()\n    },\n    async fetchData() {\n      this.customTableConfig.loading = true\n      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {\n        this.ruleForm.StartTime = null\n        this.ruleForm.EndTime = null\n      }\n      await GetWarnPageList({\n        ...this.ruleForm,\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.customTableConfig.tableData = res.Data.Data\n          this.customTableConfig.total = res.Data.TotalCount\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      }).finally(() => {\n        this.customTableConfig.loading = false\n      })\n    },\n\n    // 关闭\n    handelClose(row) {\n      this.$confirm('是否关闭?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        CloseWarning({ Id: row.Id }).then((res) => {\n          if (res.IsSucceed) {\n            this.$message({\n              type: 'success',\n              message: '关闭成功!'\n            })\n            this.onFresh()\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删�?\n        })\n      })\n    },\n    // 查看详情\n    getDetail(item) {\n      console.log(item)\n      if (item.Url) {\n        let platform = '' // 子应�?        if (item.ModuleName == '后台设置') {\n          platform = 'management'\n        } else {\n          platform = 'digitalfactory'\n        }\n        this.$qiankun.switchMicroAppFn(\n          platform,\n          item.ModuleCode,\n          item.ModuleId,\n          item.Url\n        )\n      } else {\n        const platform = 'digitalfactory'\n        const code = 'szgc'\n        const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\n        let url = ''\n        if (item.Module == '能耗管�?) {\n          url = '/business/energy/alarmDetail'\n        } else if (item.Module == '车辆道闸') {\n          url = '/bussiness/vehicle/alarm-info'\n        } else if (item.Module == '门禁管理') {\n          url = '/business/AccessControlAlarmDetails'\n        } else if (item.Module == '安防管理') {\n          url = '/business/equipmentAlarm'\n        } else if (item.Module == '危化品管�?) {\n          url = '/business/hazchem/alarmInformation'\n        } else if (item.Module == '环境管理') {\n          url = '/business/environment/alarmInformation'\n        } else if (item.Module == '访客管理') {\n          url = '/business/energy/alarmDetail'\n          console.log('访客管理')\n        }\n        this.$qiankun.switchMicroAppFn(platform, code, id, url)\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.onFresh()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`)\n      this.customTableConfig.currentPage = val\n      this.onFresh()\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n* {\n  box-sizing: border-box;\n}\n\n.layout {\n  height: 100%;\n  width: 100%;\n  position: absolute;\n  ::v-deep {\n    .CustomLayout {\n      .layoutTable {\n        height: 0;\n        .CustomTable {\n          height: 100%;\n          display: flex;\n          flex-direction: column;\n          .table {\n            flex: 1;\n            height: 0;\n            display: flex;\n            flex-direction: column;\n            .el-table {\n              flex: 1;\n              height: 0;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n.warning {\n  display: inline-block;\n  width: 8px;\n  height: 8px;\n  border-radius: 4px;\n  margin-right: 5px;\n  background-color: #fb6b7f;\n}\n.handel {\n  display: inline-block;\n  width: 10px;\n  height: 10px;\n  border-radius: 5px;\n  margin-right: 5px;\n  background-color: #368dff;\n}\n.close {\n  display: inline-block;\n  width: 10px;\n  height: 10px;\n  border-radius: 5px;\n  margin-right: 5px;\n  background-color: #37be6b;\n}\n</style>\n"]}]}