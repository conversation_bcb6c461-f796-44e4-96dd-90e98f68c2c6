{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\smartPark\\leadershipCockpit\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\smartPark\\leadershipCockpit\\index.vue", "mtime": 1755506574434}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBwYXJzZVRpbWUgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IEN1c3RvbUxheW91dCBmcm9tICdAL2J1c2luZXNzQ29tcG9uZW50cy9DdXN0b21MYXlvdXQvaW5kZXgudnVlJw0KaW1wb3J0IEN1c3RvbVRhYmxlIGZyb20gJ0AvYnVzaW5lc3NDb21wb25lbnRzL0N1c3RvbVRhYmxlL2luZGV4LnZ1ZScNCmltcG9ydCBDdXN0b21Gb3JtIGZyb20gJ0AvYnVzaW5lc3NDb21wb25lbnRzL0N1c3RvbUZvcm0vaW5kZXgudnVlJw0KaW1wb3J0IERpYWxvZ0Zvcm0gZnJvbSAnLi9kaWFsb2dGb3JtLnZ1ZScNCmltcG9ydCBoaXN0b3J5IGZyb20gJy4vaGlzdG9yeS52dWUnDQppbXBvcnQgeyBjb21iaW5lVVJMIH0gZnJvbSAnQC91dGlscycNCmltcG9ydCB7DQogIEdldFBhZ2VMaXN0LA0KICBPcGVyYXRlRGF0YSwNCiAgRG93bmxvYWREYXRhQXN5bmMNCn0gZnJvbSAnQC9hcGkvYnVzaW5lc3Mvc21hcnRQYXJrJw0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnTGVhZGVyc2hpcENvY2twaXQnLA0KICBjb21wb25lbnRzOiB7DQogICAgQ3VzdG9tVGFibGUsDQogICAgQ3VzdG9tRm9ybSwNCiAgICBDdXN0b21MYXlvdXQsDQogICAgaGlzdG9yeSwNCiAgICBEaWFsb2dGb3JtDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGN1cnJlbnRDb21wb25lbnQ6IERpYWxvZ0Zvcm0sDQogICAgICBjb21wb25lbnRzQ29uZmlnOiB7fSwNCiAgICAgIGNvbXBvbmVudHNGdW5zOiB7DQogICAgICAgIG9wZW46ICgpID0+IHsNCiAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICAgIH0sDQogICAgICAgIGNsb3NlOiAoKSA9PiB7DQogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgICB0aGlzLm9uRnJlc2goKQ0KICAgICAgICB9LA0KDQogICAgICB9LA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBkaWFsb2dUaXRsZTogJycsDQogICAgICB0YWJsZVNlbGVjdGlvbjogW10sDQoNCiAgICAgIHJ1bGVGb3JtOiB7DQogICAgICAgIFN0YXR1czogbnVsbCwNCiAgICAgICAgU3RhcnREYXRlOiBudWxsLA0KICAgICAgICBFbmREYXRlOiBudWxsLA0KICAgICAgICBkYXRhOiBbXQ0KICAgICAgfSwNCiAgICAgIGN1c3RvbUZvcm06IHsNCiAgICAgICAgZm9ybUl0ZW1zOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiAnU3RhdHVzJywgLy8g5a2X5q61SUQNCiAgICAgICAgICAgIGxhYmVsOiAn54q277+9PywgLy8gRm9ybeeahGxhYmVsDQogICAgICAgICAgICB0eXBlOiAnc2VsZWN0JywgLy8gaW5wdXQ65pmu6YCa6L6T5YWl5qGGLHRleHRhcmVhOuaWh+acrO+/vT9zZWxlY3Q65LiL5ouJ6YCJ5oup77+9P2RhdGVwaWNrZXI65pel5pyf6YCJ5oup77+9PwogICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+mAieaLqScsDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgLy8g6Zmk5LqGbW9kZWzku6XlpJbnmoTlhbbku5bnmoTlj4LmlbAs5YW35L2T6K+35Y+C6ICDZWxlbWVudOaWh+ahow0KICAgICAgICAgICAgICBjbGVhcmFibGU6IHRydWUNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBvcHRpb25zOiBbDQogICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICB2YWx1ZTogMCwNCiAgICAgICAgICAgICAgICBsYWJlbDogJ+acquWvvO+/vT8NCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgIHZhbHVlOiAxLA0KICAgICAgICAgICAgICAgIGxhYmVsOiAn6I2J56i/Jw0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgdmFsdWU6IDIsDQogICAgICAgICAgICAgICAgbGFiZWw6ICfmraPlvI8nDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIF0sDQogICAgICAgICAgICB3aWR0aDogJzI0MHB4JywNCiAgICAgICAgICAgIGNoYW5nZTogKGUpID0+IHsNCiAgICAgICAgICAgICAgLy8gY2hhbmdl5LqL5Lu2DQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKGUpDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBrZXk6ICdkYXRhJywNCiAgICAgICAgICAgIGxhYmVsOiAn5pe26Ze0JywNCiAgICAgICAgICAgIHR5cGU6ICdkYXRlUGlja2VyJywNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICB0eXBlOiAnbW9udGhyYW5nZScsDQogICAgICAgICAgICAgIHJhbmdlU2VwYXJhdG9yOiAn77+9PywNCiAgICAgICAgICAgICAgc3RhcnRQbGFjZWhvbGRlcjogJ+W8gOWni+aXpe+/vT8sDQogICAgICAgICAgICAgIGVuZFBsYWNlaG9sZGVyOiAn57uT5p2f5pel5pyfJywNCiAgICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlLA0KICAgICAgICAgICAgICB2YWx1ZUZvcm1hdDogJ3l5eXktTU0nDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgY2hhbmdlOiAoZSkgPT4gew0KICAgICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLlN0YXJ0RGF0ZSA9IGVbMF0NCiAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5FbmREYXRlID0gZVsxXQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgXSwNCiAgICAgICAgcnVsZXM6IHsNCiAgICAgICAgICAvLyDor7flj4LnhadlbGVtZW50Rm9ybSBydWxlcw0KICAgICAgICB9LA0KICAgICAgICBjdXN0b21Gb3JtQnV0dG9uczogew0KICAgICAgICAgIHN1Ym1pdE5hbWU6ICfmn6Xor6InLA0KICAgICAgICAgIHJlc2V0TmFtZTogJ+mHjee9ricNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIGN1c3RvbVRhYmxlQ29uZmlnOiB7DQogICAgICAgIGJ1dHRvbkNvbmZpZzogew0KICAgICAgICAgIGJ1dHRvbkxpc3Q6W10NCiAgICAgICAgfSwNCiAgICAgICAgLy8g6KGo5qC8DQogICAgICAgIHBhZ2VTaXplT3B0aW9uczogWzEwLCAyMCwgNTAsIDgwXSwNCiAgICAgICAgY3VycmVudFBhZ2U6IDEsDQogICAgICAgIHBhZ2VTaXplOiAyMCwNCiAgICAgICAgdG90YWw6IDAsDQogICAgICAgIHRhYmxlQ29sdW1uczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHdpZHRoOiA2MCwNCiAgICAgICAgICAgIGxhYmVsOiAn5bqP5Y+3JywNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICB0eXBlOiAnaW5kZXgnLA0KICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAn5oql6KGo57uf6K6h5pyI5Lu9JywNCiAgICAgICAgICAgIGtleTogJ1RpdGxlJw0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICfnirbvv70/LA0KICAgICAgICAgICAga2V5OiAnU3RhdHVzTmFtZScNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAn5pON5L2c77+9PywNCiAgICAgICAgICAgIGtleTogJ09wZXJhdG9yJw0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICfmk43kvZzml7bpl7QnLA0KICAgICAgICAgICAga2V5OiAnT3BlcmF0ZVRpbWUnDQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICB0YWJsZURhdGE6IFtdLA0KICAgICAgICB0YWJsZUFjdGlvbnM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBhY3Rpb25MYWJlbDogJycsDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgdHlwZTogJ3RleHQnLA0KICAgICAgICAgICAgICBkaXNhYmxlZDogZmFsc2UNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBvbmNsaWNrOiAoaW5kZXgsIHJvdykgPT4gew0KICAgICAgICAgICAgICB0aGlzLmhhbmRsZVZpZXcoaW5kZXgsIHJvdykNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgXQ0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHt9LA0KICBjcmVhdGVkKCkgew0KICAgIC8vIHRoaXMuZ2V0QmFzZURhdGEoKQ0KICAgIHRoaXMuaW5pdCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBzZWFyY2hGb3JtKGRhdGEpIHsNCiAgICAgIGNvbnNvbGUubG9nKGRhdGEpDQogICAgICB0aGlzLm9uRnJlc2goKQ0KICAgIH0sDQogICAgcmVzZXRGb3JtKCkgew0KICAgICAgdGhpcy5vbkZyZXNoKCkNCiAgICB9LA0KICAgIG9uRnJlc2goKSB7DQogICAgICB0aGlzLmdldFBhZ2VMaXN0KCkNCiAgICB9LA0KICAgIGluaXQoKSB7DQogICAgICB0aGlzLmdldFBhZ2VMaXN0KCkNCiAgICB9LA0KICAgIGFzeW5jIGdldFBhZ2VMaXN0KCkgew0KICAgICAgaWYgKHRoaXMucnVsZUZvcm0uZGF0YS5sZW5ndGggPT0gMCkgew0KICAgICAgICB0aGlzLnJ1bGVGb3JtLlN0YXJ0RGF0ZSA9IG51bGwNCiAgICAgICAgdGhpcy5ydWxlRm9ybS5FbmREYXRlID0gbnVsbA0KICAgICAgfQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0UGFnZUxpc3Qoew0KICAgICAgICBQYWdlOiB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmN1cnJlbnRQYWdlLA0KICAgICAgICBQYWdlU2l6ZTogdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5wYWdlU2l6ZSwNCiAgICAgICAgLi4udGhpcy5ydWxlRm9ybQ0KICAgICAgfSkNCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIGNvbnNvbGUubG9nKHJlcykNCiAgICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy50YWJsZURhdGEgPSByZXMuRGF0YS5EYXRhDQogICAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcudG90YWwgPSByZXMuRGF0YS5Ub3RhbA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgdHlwZTogJ2Vycm9yJywNCiAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlQ3JlYXRlKHNsb3RTY29wZSkgew0KICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9ICflr7zlhaXmiqXooagnDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnRGlhbG9nRm9ybScNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy4kcmVmcy5jdXJyZW50Q29tcG9uZW50LmluaXQoc2xvdFNjb3BlKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIGhhbmRsZVZpZXcoaW5kZXgsIHJvdykgew0KICAgIC8vICAgLy8gY29uc29sZS5sb2coaW5kZXgsIHJvdykNCiAgICAvLyAgIC8vIOeOr+Wig+WIpOaWrQ0KICAgIC8vICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7DQogICAgLy8gICAgIGNvbnNvbGUubG9nKCflvIDlj5Hnjq/vv70/KQ0KICAgIC8vICAgICB3aW5kb3cub3BlbignaHR0cDovL3ducHpnYy1kZXYuYmltdGsuY29tL2NvY2twaXQnLCAnX2JsYW5rJykNCiAgICAvLyAgIH0gZWxzZSB7DQogICAgLy8gICAgIGNvbnNvbGUubG9nKCfnlJ/kuqfnjq/looMnKQ0KICAgIC8vICAgICB3aW5kb3cub3BlbignaHR0cDovL3ducHpnYy10ZXN0LmJpbXRrLmNvbS9jb2NrcGl0JywgJ19ibGFuaycpDQogICAgLy8gICB9DQogICAgLy8gfSwNCiAgICBhc3luYyBoYW5kbGVUZW1wbGF0ZURvd24ocm93KSB7DQogICAgICBpZiAocm93LlVybCkgew0KICAgICAgICBEb3dubG9hZERhdGFBc3luYyh7IHVybDogcm93LlVybCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBjb25zdCB1cmwgPSB3aW5kb3cuVVJMLmNyZWF0ZU9iamVjdFVSTCgNCiAgICAgICAgICAgIG5ldyBCbG9iKFtyZXNdLCB7DQogICAgICAgICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCcNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgKQ0KICAgICAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJykNCiAgICAgICAgICBsaW5rLnN0eWxlLmRpc3BsYXkgPSAnbm9uZScNCiAgICAgICAgICBsaW5rLmhyZWYgPSB1cmwNCiAgICAgICAgICAvLyDmlofku7bvv70/CiAgICAgICAgICBsaW5rLnNldEF0dHJpYnV0ZSgnZG93bmxvYWQnLCAn6am+6am26Iix5oql77+9P3hsc3gnKQ0KICAgICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluaykNCiAgICAgICAgICBsaW5rLmNsaWNrKCkNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdlcnJvcicsDQogICAgICAgICAgbWVzc2FnZTogJ+aaguaXoOaVsOaNricNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5pKk5ZueL+aPkOS6pA0KICAgIHdpdGhEcmF3T3JTdWJtaXQocm93LCB0eXBlKSB7DQogICAgICB0aGlzLiRjb25maXJtKGAke3R5cGUgPT09IDIgPyAn5pWw5o2u5pKk5Zue5Lya77yM5b2T5pyI5pWw5o2u5bCG5LiN5Lya5Zyo6am+6am26Iix5aSn5bGP5LiK5bGV56S677yM6K+356Gu6K6k77yM5piv5ZCm57un77+9PycgOiAn5o+Q5Lqk6YCJ5Lit55qE5pWw5o2u77yM5piv5ZCm57un57ut77+9P30gYCwgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgT3BlcmF0ZURhdGEoew0KICAgICAgICAgIElkOiByb3cuSWQsDQogICAgICAgICAgVHlwZTogdHlwZQ0KICAgICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBjb25zb2xlLmxvZyhyZXMpDQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsDQogICAgICAgICAgICAgIG1lc3NhZ2U6IGAke3R5cGUgPT09IDIgPyAn5pKk5Zue5oiQ5YqfIScgOiAn5o+Q5Lqk5oiQ5YqfISd9YA0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMub25GcmVzaCgpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InLA0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig77+9Pw0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5Y6G5Y+y6K6w5b2VDQogICAgaGFuZGVsSGlzdG9yeShyb3cpIHsNCiAgICAgIHRoaXMuZGlhbG9nVGl0bGUgPSAn5Y6G5Y+y6K6w5b2VJw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ2hpc3RvcnknDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnMuY3VycmVudENvbXBvbmVudC5pbml0KHJvdykNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIGhhbmRsZVNpemVDaGFuZ2UodmFsKSB7DQogICAgICBjb25zb2xlLmxvZyhg5q+P6aG1ICR7dmFsfSDmnaFgKQ0KICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5wYWdlU2l6ZSA9IHZhbA0KICAgICAgdGhpcy5pbml0KCkNCiAgICB9LA0KICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7DQogICAgICBjb25zb2xlLmxvZyhg5b2T5YmN77+9PyAke3ZhbH1gKQ0KICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5jdXJyZW50UGFnZSA9IHZhbA0KICAgICAgdGhpcy5pbml0KCkNCiAgICB9LA0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMudGFibGVTZWxlY3Rpb24gPSBzZWxlY3Rpb24NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/smartPark/leadershipCockpit", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        >\r\n\r\n          <template #customBtn=\"{slotScope}\">\r\n            <el-button v-if=\"slotScope.Status !== 2\" type=\"text\" @click=\"handleCreate(slotScope)\">导入表单</el-button>\r\n            <el-button v-if=\"slotScope.Status == 1\" type=\"text\" @click=\"withDrawOrSubmit(slotScope,1)\">提交</el-button>\r\n            <el-button v-if=\"slotScope.Status !== 0\" type=\"text\" @click=\"handleTemplateDown(slotScope)\">下载</el-button>\r\n            <el-button type=\"text\" @click=\"handelHistory(slotScope)\">历史记录</el-button>\r\n            <el-button v-if=\"slotScope.Status == 2\" type=\"text\" @click=\"withDrawOrSubmit(slotScope,2)\">撤回</el-button>\r\n          </template></CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\" top=\"6vh\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { parseTime } from '@/utils'\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport DialogForm from './dialogForm.vue'\r\nimport history from './history.vue'\r\nimport { combineURL } from '@/utils'\r\nimport {\r\n  GetPageList,\r\n  OperateData,\r\n  DownloadDataAsync\r\n} from '@/api/business/smartPark'\r\nexport default {\r\n  name: 'LeadershipCockpit',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n    history,\r\n    DialogForm\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        },\r\n\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Status: null,\r\n        StartDate: null,\r\n        EndDate: null,\r\n        data: []\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Status', // 字段ID\r\n            label: '状�?, // Form的label\r\n            type: 'select', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            placeholder: '请选择',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            options: [\r\n              {\r\n                value: 0,\r\n                label: '未导�?\r\n              },\r\n              {\r\n                value: 1,\r\n                label: '草稿'\r\n              },\r\n              {\r\n                value: 2,\r\n                label: '正式'\r\n              }\r\n            ],\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'data',\r\n            label: '时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              type: 'monthrange',\r\n              rangeSeparator: '�?,\r\n              startPlaceholder: '开始日�?,\r\n              endPlaceholder: '结束日期',\r\n              clearable: true,\r\n              valueFormat: 'yyyy-MM'\r\n            },\r\n            change: (e) => {\r\n              this.ruleForm.StartDate = e[0]\r\n              this.ruleForm.EndDate = e[1]\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList:[]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 60,\r\n            label: '序号',\r\n            otherOptions: {\r\n              type: 'index',\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '报表统计月份',\r\n            key: 'Title'\r\n          },\r\n          {\r\n            label: '状�?,\r\n            key: 'StatusName'\r\n          },\r\n          {\r\n            label: '操作�?,\r\n            key: 'Operator'\r\n          },\r\n          {\r\n            label: '操作时间',\r\n            key: 'OperateTime'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '',\r\n            otherOptions: {\r\n              type: 'text',\r\n              disabled: false\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleView(index, row)\r\n            }\r\n          }\r\n\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  created() {\r\n    // this.getBaseData()\r\n    this.init()\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getPageList()\r\n    },\r\n    init() {\r\n      this.getPageList()\r\n    },\r\n    async getPageList() {\r\n      if (this.ruleForm.data.length == 0) {\r\n        this.ruleForm.StartDate = null\r\n        this.ruleForm.EndDate = null\r\n      }\r\n      const res = await GetPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        this.customTableConfig.total = res.Data.Total\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    handleCreate(slotScope) {\r\n      this.dialogTitle = '导入报表'\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'DialogForm'\r\n      this.$nextTick(() => {\r\n        this.$refs.currentComponent.init(slotScope)\r\n      })\r\n    },\r\n    // handleView(index, row) {\r\n    //   // console.log(index, row)\r\n    //   // 环境判断\r\n    //   if (process.env.NODE_ENV === 'development') {\r\n    //     console.log('开发环�?)\r\n    //     window.open('http://wnpzgc-dev.bimtk.com/cockpit', '_blank')\r\n    //   } else {\r\n    //     console.log('生产环境')\r\n    //     window.open('http://wnpzgc-test.bimtk.com/cockpit', '_blank')\r\n    //   }\r\n    // },\r\n    async handleTemplateDown(row) {\r\n      if (row.Url) {\r\n        DownloadDataAsync({ url: row.Url }).then((res) => {\r\n          const url = window.URL.createObjectURL(\r\n            new Blob([res], {\r\n              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\r\n            })\r\n          )\r\n          const link = document.createElement('a')\r\n          link.style.display = 'none'\r\n          link.href = url\r\n          // 文件�?\n          link.setAttribute('download', '驾驶舱报�?xlsx')\r\n          document.body.appendChild(link)\r\n          link.click()\r\n        })\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: '暂无数据'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 撤回/提交\r\n    withDrawOrSubmit(row, type) {\r\n      this.$confirm(`${type === 2 ? '数据撤回会，当月数据将不会在驾驶舱大屏上展示，请确认，是否继�?' : '提交选中的数据，是否继续�?} `, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        OperateData({\r\n          Id: row.Id,\r\n          Type: type\r\n        }).then((res) => {\r\n          console.log(res)\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: `${type === 2 ? '撤回成功!' : '提交成功!'}`\r\n            })\r\n            this.onFresh()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删�?\r\n        })\r\n      })\r\n    },\r\n\r\n    // 历史记录\r\n    handelHistory(row) {\r\n      this.dialogTitle = '历史记录'\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'history'\r\n      this.$nextTick(() => {\r\n        this.$refs.currentComponent.init(row)\r\n      })\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n\r\n"]}]}