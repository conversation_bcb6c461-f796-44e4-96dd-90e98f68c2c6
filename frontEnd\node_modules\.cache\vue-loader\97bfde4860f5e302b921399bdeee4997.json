{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\indexNew.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\indexNew.vue", "mtime": 1755737285683}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["indexNew.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "indexNew.vue", "sourceRoot": "src/views/business/energyManagement/pJEnergyAnalysis", "sourcesContent": ["<template>\n  <div class=\"pJEnergyAnalysisBox\">\n    <div class=\"customTabs\">\n      <div\n        v-for=\"(item, index) in energyTypeList\"\n        :key=\"index\"\n        :class=\"[energyType === item.name ? 'activeTab' : '']\"\n        @click=\"handelEnergyTab(item)\"\n      >\n        {{ item.name }}\n      </div>\n    </div>\n    {{ yearMonthValue }}\n    <div class=\"searchBox\">\n      <div style=\"display: flex\">\n        <el-radio-group\n          v-model=\"yearMonthRadio\"\n          class=\"radio\"\n          @change=\"yearMonthRadioChange\"\n        >\n          <el-radio-button :label=\"1\">年</el-radio-button>\n          <el-radio-button :label=\"2\">月</el-radio-button>\n          <el-radio-button :label=\"4\">日</el-radio-button>\n        </el-radio-group>\n        <div class=\"divider\" />\n        <el-date-picker\n          v-if=\"yearMonthRadio == 1\"\n          v-model=\"yearMonthValue\"\n          class=\"picker\"\n          :clearable=\"false\"\n          value-format=\"yyyy\"\n          type=\"year\"\n          @change=\"pickChange\"\n        />\n        <el-date-picker\n          v-else-if=\"yearMonthRadio == 2\"\n          v-model=\"yearMonthValue\"\n          class=\"picker\"\n          :clearable=\"false\"\n          value-format=\"yyyy-MM\"\n          type=\"month\"\n          @change=\"pickChange\"\n        />\n        <el-date-picker\n          v-else\n          v-model=\"yearMonthValue\"\n          class=\"picker\"\n          :clearable=\"false\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"date\"\n          @change=\"pickChange\"\n        />\n        <el-button @click=\"reset\">重置</el-button>\n      </div>\n    </div>\n    <div class=\"wapper2\">\n      <component\n        :is=\"currentComponent\"\n        ref=\"content\"\n        :components-config=\"{\n          DateType: yearMonthRadio,\n          StartTime: yearMonthValue,\n          randomInteger: isFlag,\n        }\"\n      />\n    </div>\n  </div>\n</template>\n\n<script>\nimport dayjs from 'dayjs'\nimport electricity from './eleNew/index'\nimport gas from './gas/index'\nimport water from './water/index'\n\nexport default {\n  components: {\n    electricity,\n    gas,\n    water\n  },\n  data() {\n    return {\n      energyTypeList: [\n        {\n          name: '电'\n        },\n        {\n          name: '水'\n        },\n        {\n          name: '气'\n        }\n      ],\n      energyType: '电',\n      yearMonthRadio: 2,\n      yearMonthValue: dayjs().subtract(0, 'month').format('YYYY-MM'),\n      isFlag: false,\n      currentComponent: electricity\n    }\n  },\n  created() {},\n  mounted() {},\n  provide() {\n    return {\n      DateType: () => this.yearMonthRadio,\n      StartTime: () => this.yearMonthValue,\n      EndTime: () => this.yearMonthValue,\n      randomInteger: () => this.isFlag\n    }\n  },\n  methods: {\n    handelEnergyTab(item) {\n      this.energyType = item.name\n      this.isFlag = !this.isFlag\n      if (this.energyType === '水') {\n        this.currentComponent = 'water'\n      } else if (this.energyType === '电') {\n        this.currentComponent = 'electricity'\n      } else if (this.energyType === '气') {\n        this.currentComponent = 'gas'\n      }\n    },\n    yearMonthRadioChange(val) {\n      if (val === 1) {\n        this.yearMonthValue = dayjs().format('YYYY')\n      } else if (val === 2) {\n        this.yearMonthValue = dayjs().subtract(0, 'month').format('YYYY-MM')\n      } else {\n        this.yearMonthValue = dayjs().subtract(0, 'month').format('YYYY-MM-DD')\n      }\n      this.isFlag = !this.isFlag\n    },\n    reset() {\n      this.isFlag = !this.isFlag\n      this.yearMonthRadio = 2\n      this.yearMonthValue = dayjs().subtract(0, 'month').format('YYYY-MM')\n    },\n    pickChange() {\n      this.isFlag = !this.isFlag\n    },\n    handleClick() {\n      this.$refs.configDialog.handleOpen()\n    },\n    refreshData() {\n      this.isFlag = !this.isFlag\n    }\n  }\n}\n</script>\n<style scoped lang='scss'>\n.pJEnergyAnalysisBox {\n  padding: 20px;\n  .customTabs {\n    height: 64px;\n    background: #fff;\n    border-radius: 4px;\n    display: flex;\n    margin-bottom: 16px;\n    font-size: 18px;\n    color: #999;\n    > div {\n      width: 140px;\n      height: 64px;\n      line-height: 64px;\n      text-align: center;\n      cursor: pointer;\n    }\n    .activeTab {\n      font-weight: 600;\n      color: #298dff;\n      border-bottom: 2px solid #298dff;\n    }\n  }\n  .searchBox {\n    margin-bottom: 16px;\n    background-color: #fff;\n    border-radius: 4px;\n    padding: 16px;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    overflow: hidden;\n    .radio {\n      margin-right: 10px;\n    }\n    .picker {\n      margin-right: 10px;\n    }\n    // ::v-deep .el-radio-button__inner {\n    //   background-color: #ffffff;\n    //   height: 32px;\n    //   width: 80px;\n    //   font-size: 14px;\n    // }\n  }\n  .divider {\n    width: 1px;\n    height: 32px;\n    margin: 0 32px;\n    background: #eee;\n  }\n}\n</style>\n"]}]}