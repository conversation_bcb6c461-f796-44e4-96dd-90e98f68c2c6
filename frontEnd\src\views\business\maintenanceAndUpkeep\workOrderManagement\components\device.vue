<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <div class="toolbox">
          <!-- <div>
              <el-button @click="openDialog('add')" type="primary">新 增</el-button>
            </div> -->
          <div>
            <el-form inline>
              <el-form-item label="工单号:" style="margin-bottom: 10px">
                <el-input
                  v-model="query.Order_Code"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>
              <el-form-item label="工单名称:" style="margin-bottom: 10px">
                <el-input
                  v-model="query.Order_Name"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>
              <el-form-item label="发起时间:" style="margin-bottom: 10px">
                <el-date-picker
                  v-model="query.Date"
                  align="right"
                  type="daterange"
                  placeholder="选择日期"
                  style="width: 300px"
                  value-format="yyyy-MM-dd"
                  :picker-options="pickerOptions"
                  @change="changeDate"
                />
              </el-form-item>
              <el-form-item label="工单状态:" style="margin-bottom: 10px">
                <el-select
                  v-model="query.State"
                  clearable
                  filterable
                  style="width: 120px"
                >
                  <el-option
                    v-for="item in stateList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
              <!-- <el-form-item label="工单类型:" style="margin-bottom: 10px">
                  <el-select
                    v-model="query.WorkOrder_Setup_Id"
                    clearable
                    filterable
                    style="width: 120px"
                  >
                    <el-option
                      v-for="item in workTypeList"
                      :key="item.Value"
                      :label="item.Display_Name"
                      :value="item.Value"
                    />
                  </el-select>
                </el-form-item> -->
              <el-form-item label="维修人:" style="margin-bottom: 10px">
                <el-select
                  v-model="query.Maintain_Person"
                  clearable
                  filterable
                  style="width: 150px"
                >
                  <el-option
                    v-for="item in personList"
                    :key="item.Id"
                    :label="item.Name"
                    :value="item.Id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="设备查询:" style="margin-bottom: 10px">
                <el-select
                  v-model="query.EquipId"
                  filterable
                  clearable
                  placeholder="请输入设备"
                >
                  <el-option
                    v-for="item in equipOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-button @click="reset()">重 置</el-button>
              <el-button type="primary" @click="searchForm()">查 询</el-button>
            </el-form>
          </div>
        </div>
      </template>
      <template v-slot:layoutTable>
        <div class="toolbox">
          <div>
            <el-radio-group
              v-model="query.WorkOrder_State"
              class="typeline"
              @input="searchForm"
            >
              <el-radio-button :label="null">全部</el-radio-button>
              <el-radio-button :label="0">待处理</el-radio-button>
              <el-radio-button :label="1">处理中</el-radio-button>
              <el-radio-button :label="2">已处理</el-radio-button>
            </el-radio-group>
          </div>
          <div>
            <el-button
              type="primary"
              @click="openDialog('add')"
            >新 增</el-button>
          </div>
        </div>
        <CustomTable
          style="height: calc(100vh - 350px)"
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        >
          <template #customBtn="{ slotScope }">
            <template v-if="slotScope.State === '0' && slotScope.Is_Anth">
              <el-button
                v-if="getBtnAuth('dispatch')"
                type="text"
                @click="openDialog('dispatch', slotScope, slotScope.Order_Type)"
              >派工</el-button>
              <el-button
                v-if="getBtnAuth('dispatch-myorder')"
                type="text"
                code="dispatch-myorder"
                @click="openDialog('dispatch', slotScope, slotScope.Order_Type)"
              >派工</el-button>
              <el-button
                v-if="getBtnAuth('receive')"
                type="text"
                @click="receivingOrders(slotScope)"
              >接单</el-button>
              <el-button
                v-if="getBtnAuth('receive-myorder')"
                type="text"
                code="receive-myorder"
                @click="receivingOrders(slotScope)"
              >接单</el-button>
            </template>
            <el-button
              v-if="getBtnAuth('detail')"
              type="text"
              @click="openDialog('detail', slotScope, slotScope.Order_Type)"
            >查看详情</el-button>
            <el-button
              v-if="getBtnAuth('detail-myorder')"
              type="text"
              @click="openDialog('detail', slotScope, slotScope.Order_Type)"
            >查看详情</el-button>
            <template
              v-if="
                slotScope.State === '1' &&
                  slotScope.Maintain_Person_Id === userId &&
                  slotScope.Order_Type === 'jsbx'
              "
            >
              <el-button
                v-if="getBtnAuth('handle')"
                type="text"
                @click="openDialog('handle', slotScope, slotScope.Order_Type)"
              >工单处理</el-button>
              <el-button
                v-if="getBtnAuth('handle-myorder')"
                type="text"
                @click="openDialog('handle', slotScope, slotScope.Order_Type)"
              >工单处理</el-button>
            </template>
            <template
              v-if="
                slotScope.State === '2' &&
                  slotScope.Is_Anth &&
                  slotScope.Order_Type === 'jsbx'
              "
            >
              <el-button
                v-if="getBtnAuth('recheck')"
                type="text"
                @click="openDialog('recheck', slotScope, slotScope.Order_Type)"
              >工单复检</el-button>
              <el-button
                v-if="getBtnAuth('recheck-myorder')"
                type="text"
                @click="openDialog('recheck', slotScope, slotScope.Order_Type)"
              >工单复检</el-button>
            </template>
            <template
              v-if="
                slotScope.State === '3' &&
                  slotScope.Order_Type === 'jsbx' &&
                  slotScope.Create_UserId === userId
              "
            >
              <el-button
                v-if="getBtnAuth('rate')"
                type="text"
                @click="openCloseRate(slotScope, 'rate')"
              >工单评价</el-button>
              <el-button
                v-if="getBtnAuth('rate-myorder')"
                type="text"
                @click="openCloseRate(slotScope, 'rate')"
              >工单评价</el-button>
            </template>
            <template v-if="slotScope.State === '0' || slotScope.State === '1'">
              <el-button
                v-if="getBtnAuth('close')"
                type="text"
                @click="openCloseRate(slotScope, 'close')"
              >关闭</el-button>
              <el-button
                v-if="getBtnAuth('close-myorder')"
                type="text"
                @click="openCloseRate(slotScope, 'close')"
              >关闭</el-button>
            </template>
          </template>
        </CustomTable>
      </template>
    </CustomLayout>
    <editDialog ref="editDialog" @refresh="fetchData" />
    <closeRateDialog ref="closeRateDialog" @refresh="fetchData" />
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
// import AuthButtons from "@/mixins/auth-buttons";
import editDialog from '../editDialog.vue'
import closeRateDialog from '../closeRateDialog.vue'
import {
  GetWorkOrderManageList,
  GetWorkOrderType,
  GetPersonList,
  DeleteCoatingRequir,
  SendWorkOrderPerson,
  GetEquipDropList
} from '@/api/business/maintenanceAndUpkeep.js'

export default {
  Name: '',
  components: {
    CustomTable,
    CustomLayout,
    editDialog,
    closeRateDialog
  },
  props: {
    flag: {
      type: Boolean,
      default: false
    },
    personList: {
      type: Array,
      default: () => []
    },
    equipOptions: {
      type: Array,
      default: () => []
    },
    authButtons: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      userId: '',
      query: {
        Date: [],
        Order_Code: '',
        Order_Name: '',
        Create_Date: '',
        Create_EDate: '',
        State: '',
        WorkOrder_Setup_Id: 'sbwb',
        Maintain_Person: '',
        WorkOrder_State: null,
        Type: 1
      },
      type: '',
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              picker.$emit('pick', [new Date(), new Date()])
            }
          },
          {
            text: '近7天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '近30天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const today = new Date()
              const end = new Date(
                today.getFullYear(),
                today.getMonth() + 1,
                0
              )
              const start = new Date(today.getFullYear(), today.getMonth(), 1)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      stateList: [
        {
          name: '待处理',
          code: 0
        },
        {
          name: '处理中',
          code: 1
        },
        {
          name: '待复检',
          code: 2
        },
        {
          name: '待评价',
          code: 3
        },
        {
          name: '处理完成',
          code: 4
        },
        {
          name: '已关闭',
          code: 5
        }
      ],
      workTypeList: [
        // {
        //   Display_Name: "已关闭",
        //   Value: 5,
        // },
      ],
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        },
        closeAndFresh: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      tableSelection: [],
      selectIds: [],
      customTableConfig: {
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        height: '100%',
        tableColumns: [
          {
            width: 50,
            label: '序号',
            otherOptions: {
              type: 'index',
              align: 'center'
            }
          },
          {
            label: '发起时间',
            key: 'Create_Date'
          },
          {
            label: '工单名称',
            key: 'Order_Name'
          },
          {
            label: '工单类型',
            key: 'Order_Type',
            render: (row) => {
              return this.$createElement(
                'span',
                {},
                row.Order_Type === null
                  ? '-'
                  : row.Order_Type === 'jsbx'
                    ? '即时报修'
                    : '设备维保'
              )
            }
          },
          {
            label: '工单号',
            key: 'Order_Code'
          },
          {
            label: '开始处理时间',
            key: 'Start_Time'
          },
          {
            label: '处理完成时间',
            key: 'End_Time'
          },
          {
            label: '处理用时',
            key: 'Time'
          },
          {
            label: '报修部门',
            key: 'Depart_Name'
          },
          {
            label: '报修方',
            key: 'Warranty_Person'
          },
          {
            label: '维修人',
            key: 'Maintain_Person'
          },
          {
            label: '工单状态',
            key: 'State',
            render: (row) => {
              return this.$createElement(
                'span',
                {
                  style: {
                    color:
                      row.State === '0'
                        ? '#FF5E7C'
                        : row.State === '1'
                          ? '#298DFF'
                          : row.State === '2'
                            ? '#FF902C'
                            : row.State === '3'
                              ? '#298DFF'
                              : row.State === '4'
                                ? '#00D3A7'
                                : '#333333'
                  }
                },
                row.State === '0'
                  ? '待处理'
                  : row.State === '1'
                    ? '处理中'
                    : row.State === '2'
                      ? '待复检'
                      : row.State === '3'
                        ? '待评价'
                        : row.State === '4'
                          ? '处理完成'
                          : '已关闭'
              )
            }
          }
        ],
        tableData: [],
        tableActionsWidth: 220,
        tableActions: [
          {
            actionLabel: '',
            otherOptions: {
              type: 'text'
            }
          }
        ],
        buttonConfig: {
          buttonList: []
        },
        operateOptions: {
          width: 300 // 操作栏宽度
        }
      }
    }
  },
  computed: {},
  // mixins: [AuthButtons],
  watch: {
    //   'AuthButtons.buttons':{
    //     handler(val,oldval){
    //       console.log('dddss',val,oldval);
    //         this.show=true
    //       }

    //     }
    //   }
    flag: {
      handler(val) {
        console.log('dddss', val)
        this.initData()
      }
    }
  },
  created() {},
  mounted() {
    // 跳转设置默认参数
    // let JumpParams = this.$qiankun.getMicroAppJumpParamsFn();
    // console.log(JumpParams.Create_Date, "跳转参数-----------------------");
    // if (JumpParams.isJump == "true") {
    //   this.query.State = Number(JumpParams.State);
    //   // this.query.Create_Date = JumpParams.Create_Date;
    //   // this.query.Create_EDate = JumpParams.Create_EDate;
    //   // this.query.Date = [JumpParams.Create_Date, JumpParams.Create_EDate];
    // }
    this.initData()
  },
  beforeDestroy() {
    this.$qiankun.setMicroAppJumpParamsFn()
    this.query.State = null
    // this.query.Create_Date = null;
    // this.query.Create_EDate = null;
    // this.query.Date = [];
  },
  methods: {
    async initData() {
      // let res = await GetWorkOrderType({ Code: "WorkOrderType" });
      // console.log(res, "12121212");
      // if (res.IsSucceed) {
      //   this.workTypeList = res.Data;
      // }

      this.userId = localStorage.getItem('UserId')
      if (this.$route.query.type === 'my') {
        this.query.type = 0
      } else {
        this.query.type = 1
      }
      await this.init()
    },
    openAdd() {
      this.dialogTitle = '新增'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.dialogRef.init(0, {}, 'add')
      })
    },
    searchForm() {
      this.customTableConfig.currentPage = 1
      this.onFresh()
    },
    reset() {
      this.query = {
        Date: [],
        Order_Code: '',
        Order_Name: '',
        Create_Date: '',
        Create_EDate: '',
        State: '',
        WorkOrder_Setup_Id: 'sbwb',
        Maintain_Person: '',
        WorkOrder_State: this.query.WorkOrder_State
      }
      if (this.$route.query.type === 'my') {
        this.query.type = 0
      } else {
        this.query.type = 1
      }
      this.customTableConfig.currentPage = 1
      this.onFresh()
    },
    resetForm() {
      this.onFresh()
    },
    onFresh() {
      this.fetchData()
    },
    init() {
      this.fetchData()
    },
    getTypeList() {
      console.log('res.Datares.Datares.Datares.Data-------------------')
      // GetWorkOrderType({ Code: "WorkOrderType" }).then((res) => {
      //   console.log(
      //     res.Data,
      //     "res.Datares.Datares.Datares.Data-------------------"
      //   );
      //   if (res.IsSucceed) {
      //     this.typeList = res.Data;
      //   }
      // });
    },
    async fetchData() {
      const res = await GetWorkOrderManageList({
        model: this.query,
        pageInfo: {
          Page: this.customTableConfig.currentPage,
          PageSize: this.customTableConfig.pageSize,
          SortName: 'Create_Date',
          SortOrder: 'DESC'
        }
      })
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data
        this.customTableConfig.total = res.Data.TotalCount
      }
    },
    handleCreate() {
      this.dialogTitle = '新增'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.dialogRef.init(0, {}, 'dispatch')
      })
    },
    handleDelete(index, row) {
      this.$confirm('请确认，是否删除该数据?', {
        type: 'warning'
      })
        .then(() => {
          DeleteCoatingRequir({ Id: row.Id }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.init()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        })
        .catch((_) => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 打开新增编辑弹窗
    async openDialog(type, row, orderType) {
      const res = await GetWorkOrderType({ Code: 'WorkOrderType' })
      if (res.IsSucceed) {
        this.workTypeList = res.Data
      }
      this.$refs.editDialog.handleOpen(type, row, orderType, this.workTypeList)
    },
    // 打开关闭工单弹窗或评价弹窗
    openCloseRate(row, type) {
      this.$refs.closeRateDialog.handleOpen(type, row)
    },
    // 接单
    receivingOrders(row) {
      SendWorkOrderPerson({ Id: row.Id }).then((res) => {
        if (res.IsSucceed) {
          this.fetchData()
          this.$message.success('接单成功')
        } else {
          this.$message.error(res.Message)
        }
      })
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.onFresh()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.onFresh()
    },
    handleSelectionChange(selection) {
      const Ids = []
      this.tableSelection = selection
      this.tableSelection.forEach((item) => {
        Ids.push(item.Id)
      })
      console.log(Ids)
      this.selectIds = Ids
      console.log(this.tableSelection)
    },

    changeDate() {
      this.query.Create_Date = this.query.Date ? this.query.Date[0] : null
      this.query.Create_EDate = this.query.Date ? this.query.Date[1] : null
    },
    getBtnAuth(code) {
      // console.log(code,this.AuthButtons,this.AuthButtons.buttons.find(item=>item.Code===code));
      return this.authButtons.buttons.find((item) => item.Code === code)
    }
  }
}
</script>

  <style lang="scss" scoped>
@import "@/views/business/vehicleBarrier/index.scss";
.toolbox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  ::v-deep .el-form-item {
    margin-bottom: 0px;
  }
}
.typeline {
  ::v-deep .el-radio-button__inner {
    border-radius: 2px;
  }
  ::v-deep .is-active {
    .el-radio-button__inner {
      background-color: #ffffff;
      color: #298dff;
    }
  }
}
</style>

