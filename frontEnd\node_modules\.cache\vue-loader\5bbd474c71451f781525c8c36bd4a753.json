{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\hazardousChemicals\\monitorData\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\hazardousChemicals\\monitorData\\index.vue", "mtime": 1755674552426}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQ3VzdG9tTGF5b3V0IGZyb20gJ0AvYnVzaW5lc3NDb21wb25lbnRzL0N1c3RvbUxheW91dC9pbmRleC52dWUnDQppbXBvcnQgQ3VzdG9tVGFibGUgZnJvbSAnQC9idXNpbmVzc0NvbXBvbmVudHMvQ3VzdG9tVGFibGUvaW5kZXgudnVlJw0KaW1wb3J0IEN1c3RvbUZvcm0gZnJvbSAnQC9idXNpbmVzc0NvbXBvbmVudHMvQ3VzdG9tRm9ybS9pbmRleC52dWUnDQoNCmltcG9ydCBEaWFsb2dGb3JtTG9vayBmcm9tICcuL2RpYWxvZ0Zvcm1Mb29rLnZ1ZScNCg0KaW1wb3J0IHsgZG93bmxvYWRGaWxlIH0gZnJvbSAnQC91dGlscy9kb3dubG9hZEZpbGUnDQovLyBpbXBvcnQgQ3VzdG9tVGl0bGUgZnJvbSAnQC9idXNpbmVzc0NvbXBvbmVudHMvQ3VzdG9tVGl0bGUvaW5kZXgudnVlJw0KLy8gaW1wb3J0IEN1c3RvbUJ1dHRvbiBmcm9tICdAL2J1c2luZXNzQ29tcG9uZW50cy9DdXN0b21CdXR0b24vaW5kZXgudnVlJw0KDQppbXBvcnQgeyBHZXREYXRhTGlzdCwgRXhwb3J0RGF0YUxpc3QsIEdldEhhemNoZW1EVENMaXN0IH0gZnJvbSAnQC9hcGkvYnVzaW5lc3MvaGF6YXJkb3VzQ2hlbWljYWxzJw0KaW1wb3J0IHsgR2V0RGljdGlvbmFyeURldGFpbExpc3RCeUNvZGUgfSBmcm9tICdAL2FwaS9zeXMnDQoNCmltcG9ydCB7IGRldmljZVR5cGVNaXhpbnMgfSBmcm9tICcuLi8uLi9taXhpbnMvZGV2aWNlVHlwZS5qcycNCmltcG9ydCBvdGhlck1peGluIGZyb20gJy4uLy4uL21peGlucy9pbmRleC5qcycNCi8vIGltcG9ydCBtb21lbnQgZnJvbSAnbW9tZW50Jw0KaW1wb3J0IGRheWpzIGZyb20gJ2RheWpzJw0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnJywNCiAgY29tcG9uZW50czogew0KICAgIEN1c3RvbVRhYmxlLA0KICAgIC8vIEN1c3RvbUJ1dHRvbiwNCiAgICAvLyBDdXN0b21UaXRsZSwNCiAgICBDdXN0b21Gb3JtLA0KICAgIEN1c3RvbUxheW91dA0KICB9LA0KICBtaXhpbnM6IFtkZXZpY2VUeXBlTWl4aW5zLCBvdGhlck1peGluXSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgY3VycmVudENvbXBvbmVudDogRGlhbG9nRm9ybUxvb2ssDQogICAgICBjb21wb25lbnRzQ29uZmlnOiB7fSwNCiAgICAgIGNvbXBvbmVudHNGdW5zOiB7DQogICAgICAgIG9wZW46ICgpID0+IHsNCiAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICAgIH0sDQogICAgICAgIGNsb3NlOiAoKSA9PiB7DQogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgICB0aGlzLm9uRnJlc2goKQ0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBkaWFsb2dUaXRsZTogJycsDQogICAgICB0YWJsZVNlbGVjdGlvbjogW10sDQoNCiAgICAgIHJ1bGVGb3JtOiB7DQogICAgICAgIENvbnRlbnQ6ICcnLA0KICAgICAgICBFcXRUeXBlOiAnJywNCiAgICAgICAgUG9zaXRpb246ICcnLA0KICAgICAgICBEYXRhVHlwZTogJycNCiAgICAgIH0sDQogICAgICBjdXN0b21Gb3JtOiB7DQogICAgICAgIGZvcm1JdGVtczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGtleTogJ0NvbnRlbnQnLCAvLyDlrZfmrrVJRA0KICAgICAgICAgICAgbGFiZWw6ICcnLCAvLyBGb3Jt55qEbGFiZWwNCiAgICAgICAgICAgIHR5cGU6ICdpbnB1dCcsIC8vIGlucHV0OuaZrumAmui+k+WFpeahhix0ZXh0YXJlYTrmlofmnKzln58sc2VsZWN0OuS4i+aLiemAieaLqeWZqCxkYXRlcGlja2VyOuaXpeacn+mAieaLqeWZqA0KDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgLy8g6Zmk5LqGbW9kZWzku6XlpJbnmoTlhbbku5bnmoTlj4LmlbAs5YW35L2T6K+35Y+C6ICDZWxlbWVudOaWh+ahow0KICAgICAgICAgICAgICBjbGVhcmFibGU6IHRydWUsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6L6T5YWl6K6+5aSH57yW5Y+35oiW5ZCN56ew6L+b6KGM5pCc57SiJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHdpZHRoOiAnMjQwcHgnLA0KICAgICAgICAgICAgY2hhbmdlOiAoZSkgPT4gew0KICAgICAgICAgICAgICAvLyBjaGFuZ2Xkuovku7YNCiAgICAgICAgICAgICAgY29uc29sZS5sb2coZSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGtleTogJ0VxdFR5cGUnLA0KICAgICAgICAgICAgbGFiZWw6ICforr7lpIfnsbvlnosnLA0KICAgICAgICAgICAgdHlwZTogJ3NlbGVjdCcsDQoNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICAvLyDpmaTkuoZtb2RlbOS7peWklueahOWFtuS7lueahOWPguaVsCzlhbfkvZPor7flj4LogINlbGVtZW505paH5qGjDQogICAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZSwNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6norr7lpIfnsbvlnosnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgb3B0aW9uczogW10sDQogICAgICAgICAgICBjaGFuZ2U6IChlKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0uRGF0YVR5cGUgPSAnJw0KICAgICAgICAgICAgICBjb25zdCBJZCA9IHRoaXMuZGVjZWl2ZVR5cGVMaXN0LmZpbmQoKGl0ZW0pID0+IGl0ZW0udmFsdWUgPT09IGUpLklkDQogICAgICAgICAgICAgIHRoaXMuZ2V0RFRDTGlzdChHZXRIYXpjaGVtRFRDTGlzdCwgSWQsICdEYXRhVHlwZScsICdEYXRhSWQnKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiAnRGF0YVR5cGUnLA0KICAgICAgICAgICAgbGFiZWw6ICfmlbDmja7nsbvlnosnLA0KICAgICAgICAgICAgdHlwZTogJ3NlbGVjdCcsDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgLy8g6Zmk5LqGbW9kZWzku6XlpJbnmoTlhbbku5bnmoTlj4LmlbAs5YW35L2T6K+35Y+C6ICDZWxlbWVudOaWh+ahow0KICAgICAgICAgICAgICBjbGVhcmFibGU6IHRydWUsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36YCJ5oup6K6+5aSH57G75Z6LJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIG9wdGlvbnM6IFtdLA0KICAgICAgICAgICAgY2hhbmdlOiAoZSkgPT4gew0KICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiAnUG9zaXRpb24nLCAvLyDlrZfmrrVJRA0KICAgICAgICAgICAgbGFiZWw6ICflronoo4XkvY3nva4nLCAvLyBGb3Jt55qEbGFiZWwNCiAgICAgICAgICAgIHR5cGU6ICdpbnB1dCcsIC8vIGlucHV0OuaZrumAmui+k+WFpeahhix0ZXh0YXJlYTrmlofmnKzln58sc2VsZWN0OuS4i+aLiemAieaLqeWZqCxkYXRlcGlja2VyOuaXpeacn+mAieaLqeWZqA0KDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgLy8g6Zmk5LqGbW9kZWzku6XlpJbnmoTlhbbku5bnmoTlj4LmlbAs5YW35L2T6K+35Y+C6ICDZWxlbWVudOaWh+ahow0KICAgICAgICAgICAgICBjbGVhcmFibGU6IHRydWUsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5a6J6KOF5L2N572uJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGNoYW5nZTogKGUpID0+IHsNCiAgICAgICAgICAgICAgLy8gY2hhbmdl5LqL5Lu2DQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKGUpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICBydWxlczogew0KICAgICAgICAgIC8vIOivt+WPgueFp2VsZW1lbnRGb3JtIHJ1bGVzDQogICAgICAgIH0sDQogICAgICAgIGN1c3RvbUZvcm1CdXR0b25zOiB7DQogICAgICAgICAgc3VibWl0TmFtZTogJ+afpeivoicsDQogICAgICAgICAgcmVzZXROYW1lOiAn6YeN572uJw0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgY3VzdG9tVGFibGVDb25maWc6IHsNCiAgICAgICAgYnV0dG9uQ29uZmlnOiB7DQogICAgICAgICAgYnV0dG9uTGlzdDogWw0KICAgICAgICAgICAgLy8gew0KICAgICAgICAgICAgLy8gICB0ZXh0OiAn5paw5aKeJywNCiAgICAgICAgICAgIC8vICAgcm91bmQ6IGZhbHNlLCAvLyDmmK/lkKblnIbop5INCiAgICAgICAgICAgIC8vICAgcGxhaW46IGZhbHNlLCAvLyDmmK/lkKbmnLTntKANCiAgICAgICAgICAgIC8vICAgY2lyY2xlOiBmYWxzZSwgLy8g5piv5ZCm5ZyG5b2iDQogICAgICAgICAgICAvLyAgIGxvYWRpbmc6IGZhbHNlLCAvLyDmmK/lkKbliqDovb3kuK0NCiAgICAgICAgICAgIC8vICAgZGlzYWJsZWQ6IGZhbHNlLCAvLyDmmK/lkKbnpoHnlKgNCiAgICAgICAgICAgIC8vICAgaWNvbjogJycsIC8vICDlm77moIcNCiAgICAgICAgICAgIC8vICAgYXV0b2ZvY3VzOiBmYWxzZSwgLy8g5piv5ZCm6IGa54SmDQogICAgICAgICAgICAvLyAgIHR5cGU6ICdwcmltYXJ5JywgLy8gcHJpbWFyeSAvIHN1Y2Nlc3MgLyB3YXJuaW5nIC8gZGFuZ2VyIC8gaW5mbyAvIHRleHQNCiAgICAgICAgICAgIC8vICAgc2l6ZTogJ3NtYWxsJywgLy8gbWVkaXVtIC8gc21hbGwgLyBtaW5pDQogICAgICAgICAgICAvLyAgIG9uY2xpY2s6IChpdGVtKSA9PiB7DQogICAgICAgICAgICAvLyAgICAgY29uc29sZS5sb2coaXRlbSkNCiAgICAgICAgICAgIC8vICAgICB0aGlzLmhhbmRsZUNyZWF0ZSgpDQogICAgICAgICAgICAvLyAgIH0NCiAgICAgICAgICAgIC8vIH0sDQogICAgICAgICAgICAvLyB7DQogICAgICAgICAgICAvLyAgIHRleHQ6ICflr7zlh7onLA0KICAgICAgICAgICAgLy8gICBrZXk6ICdiYXRjaCcsDQogICAgICAgICAgICAvLyAgIGRpc2FibGVkOiB0cnVlLA0KICAgICAgICAgICAgLy8gICBvbmNsaWNrOiAoaXRlbSkgPT4gew0KICAgICAgICAgICAgLy8gICAgIGNvbnNvbGUubG9nKGl0ZW0pDQogICAgICAgICAgICAvLyAgICAgdGhpcy5oYW5kbGVFeHBvcnQoKQ0KICAgICAgICAgICAgLy8gICB9DQogICAgICAgICAgICAvLyB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICB0ZXh0OiAn5om56YeP5a+85Ye6JywNCiAgICAgICAgICAgICAgb25jbGljazogKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhpdGVtKQ0KICAgICAgICAgICAgICAgIHRoaXMuaGFuZGxlQWxsRXhwb3J0KCkNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfSwNCiAgICAgICAgLy8g6KGo5qC8DQogICAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICBwYWdlU2l6ZU9wdGlvbnM6IFsxMCwgMjAsIDUwLCA4MF0sDQogICAgICAgIGN1cnJlbnRQYWdlOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMjAsDQogICAgICAgIHRvdGFsOiAwLA0KICAgICAgICB0YWJsZUNvbHVtbnM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgd2lkdGg6IDIwLA0KICAgICAgICAgICAgICB0eXBlOiAnc2VsZWN0aW9uJywNCiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICB0YWJsZURhdGE6IFtdLA0KICAgICAgICBvcGVyYXRlT3B0aW9uczogew0KICAgICAgICAgIHdpZHRoOiAyMDANCiAgICAgICAgfSwNCiAgICAgICAgdGFibGVBY3Rpb25zOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgYWN0aW9uTGFiZWw6ICfmn6XnnIsnLA0KICAgICAgICAgICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgICAgIHR5cGU6ICd0ZXh0Jw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIG9uY2xpY2s6IChpbmRleCwgcm93KSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuaGFuZGxlRWRpdChpbmRleCwgcm93LCAndmlldycpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICAgIC8vIHsNCiAgICAgICAgICAvLyAgIGFjdGlvbkxhYmVsOiAn57yW6L6RJywNCiAgICAgICAgICAvLyAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgIC8vICAgICB0eXBlOiAndGV4dCcNCiAgICAgICAgICAvLyAgIH0sDQogICAgICAgICAgLy8gICBvbmNsaWNrOiAoaW5kZXgsIHJvdykgPT4gew0KICAgICAgICAgIC8vICAgICB0aGlzLmhhbmRsZUVkaXQoaW5kZXgsIHJvdywgJ2VkaXQnKQ0KICAgICAgICAgIC8vICAgfQ0KICAgICAgICAgIC8vIH0sDQogICAgICAgICAgLy8gew0KICAgICAgICAgIC8vICAgYWN0aW9uTGFiZWw6ICfliKDpmaQnLA0KICAgICAgICAgIC8vICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgLy8gICAgIHR5cGU6ICd0ZXh0Jw0KICAgICAgICAgIC8vICAgfSwNCiAgICAgICAgICAvLyAgIG9uY2xpY2s6IChpbmRleCwgcm93KSA9PiB7DQogICAgICAgICAgLy8gICAgIHRoaXMuaGFuZGxlRGVsZXRlKGluZGV4LCByb3cpDQogICAgICAgICAgLy8gICB9DQogICAgICAgICAgLy8gfQ0KICAgICAgICBdLA0KICAgICAgICBkZWNlaXZlVHlwZUxpc3Q6IFtdDQogICAgICB9DQogICAgfQ0KICB9LA0KICBjb21wdXRlZDoge30sDQogIGFzeW5jIG1vdW50ZWQoKSB7DQogICAgdGhpcy5pbml0KCkNCiAgICB0aGlzLmRlY2VpdmVUeXBlTGlzdCA9IHRoaXMuY3VzdG9tRm9ybS5mb3JtSXRlbXMuZmluZCgoaXRlbSkgPT4gaXRlbS5rZXkgPT09ICdFcXRUeXBlJykub3B0aW9ucyA9IGF3YWl0IHRoaXMuZ2V0RGljdGlvbmFyeURldGFpbExpc3RCeUNvZGUoJ0hhemNoZW1FcXRUeXBlJywgJ1ZhbHVlJykNCiAgICAvLyB0aGlzLmluaXREZXZpY2VUeXBlKCJFcXRUeXBlIiwgIkVudmlyb25tZW50RXF0VHlwZSIpOw0KICAgIHRoaXMuZ2V0RFRDTGlzdChHZXRIYXpjaGVtRFRDTGlzdCwgJycsICdEYXRhVHlwZScsICdEYXRhSWQnKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgc2VhcmNoRm9ybShkYXRhKSB7DQogICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmN1cnJlbnRQYWdlID0gMQ0KICAgICAgdGhpcy5vbkZyZXNoKCkNCiAgICB9LA0KICAgIHJlc2V0Rm9ybSgpIHsNCiAgICAgIHRoaXMuZ2V0RFRDTGlzdChHZXRIYXpjaGVtRFRDTGlzdCwgJycsICdEYXRhVHlwZScsICdEYXRhSWQnKQ0KICAgICAgdGhpcy5vbkZyZXNoKCkNCiAgICB9LA0KICAgIG9uRnJlc2goKSB7DQogICAgICB0aGlzLkdldERhdGFMaXN0KCkNCiAgICB9LA0KICAgIGluaXQoKSB7DQogICAgICB0aGlzLkdldERhdGFMaXN0KCkNCiAgICB9LA0KICAgIGFzeW5jIEdldERhdGFMaXN0KCkgew0KICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0RGF0YUxpc3Qoew0KICAgICAgICBQYXJhbWV0ZXJKc29uOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgS2V5OiAnJywNCiAgICAgICAgICAgIFZhbHVlOiBbbnVsbF0sDQogICAgICAgICAgICBUeXBlOiAnJywNCiAgICAgICAgICAgIEZpbHRlcl9UeXBlOiAnJw0KICAgICAgICAgIH0NCiAgICAgICAgXSwNCiAgICAgICAgUGFnZTogdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5jdXJyZW50UGFnZSwNCiAgICAgICAgUGFnZVNpemU6IHRoaXMuY3VzdG9tVGFibGVDb25maWcucGFnZVNpemUsDQogICAgICAgIFNvcnROYW1lOiAnJywNCiAgICAgICAgU29ydE9yZGVyOiAnJywNCiAgICAgICAgU2VhcmNoOiAnJywNCiAgICAgICAgQ29udGVudDogJycsDQogICAgICAgIEVxdFR5cGU6ICcnLA0KICAgICAgICBQb3NpdGlvbjogJycsDQogICAgICAgIElzQWxsOiB0cnVlLA0KICAgICAgICAuLi50aGlzLnJ1bGVGb3JtDQogICAgICB9KQ0KICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIGlmIChyZXMuRGF0YS5EYXRhICYmIHJlcy5EYXRhLkRhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcudGFibGVEYXRhID0gcmVzLkRhdGEuRGF0YS5tYXAoKGl0ZW0pID0+ICh7DQogICAgICAgICAgICAuLi5pdGVtLA0KICAgICAgICAgICAgVGltZTogZGF5anMoaXRlbS5UaW1lKS5mb3JtYXQoJ1lZWVktTU0tREQgSEg6bW06c3MnKQ0KICAgICAgICAgIH0pKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcudGFibGVEYXRhID0gW10NCiAgICAgICAgfQ0KICAgICAgICAvKiB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLnRhYmxlQ29sdW1ucyA9IFtdLmNvbmNhdCgNCiAgICAgICAgICBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIHdpZHRoOiA1MCwNCiAgICAgICAgICAgICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgICAgICAgdHlwZTogJ3NlbGVjdGlvbicsDQogICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIHdpZHRoOiA2MCwNCiAgICAgICAgICAgICAgbGFiZWw6ICfluo/lj7cnLA0KICAgICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgICB0eXBlOiAnaW5kZXgnLA0KICAgICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgXSwNCiAgICAgICAgICByZXMuRGF0YS5EYXRhLkhlYWRlcnMNCiAgICAgICAgKQ0KICAgICAgICBjb25zb2xlLmxvZyhyZXMuRGF0YS5EYXRhLkhlYWRlcnMsICd0YWJsZUNvbHVtbnMnKQ0KICAgICAgICBjb25zb2xlLmxvZyh0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLnRhYmxlQ29sdW1ucywgJ3RhYmxlQ29sdW1ucycpDQogICAgICAgICovDQogICAgICAgIC8qKiAyMDIzLTA5LTE4IGVyd2luIG1vZGlmeSAqLw0KICAgICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLnRhYmxlQ29sdW1ucyA9IFsNCiAgICAgICAgICAvLyB7DQogICAgICAgICAgLy8gICB3aWR0aDogNjAsDQogICAgICAgICAgLy8gICBsYWJlbDogIuW6j+WPtyIsDQogICAgICAgICAgLy8gICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAvLyAgICAgdHlwZTogImluZGV4IiwNCiAgICAgICAgICAvLyAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgIC8vICAgfSwNCiAgICAgICAgICAvLyB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAn6K6+5aSH57yW5Y+3JywNCiAgICAgICAgICAgIGtleTogJ0VJZCcsDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgZml4ZWQ6ICdsZWZ0Jw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAn6K6+5aSH5ZCN56ewJywNCiAgICAgICAgICAgIGtleTogJ05hbWUnLA0KICAgICAgICAgICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgICAgIGZpeGVkOiAnbGVmdCcNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+iuvuWkh+exu+WeiycsDQogICAgICAgICAgICBrZXk6ICdFcXRUeXBlJywNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICBmaXhlZDogJ2xlZnQnDQogICAgICAgICAgICB9LA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICflronoo4XkvY3nva4nLA0KICAgICAgICAgICAga2V5OiAnUG9zaXRpb24nDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+aVsOaNruabtOaWsOaXtumXtCcsDQogICAgICAgICAgICBrZXk6ICdUaW1lJw0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICfmlbDmja7nsbvlnosnLA0KICAgICAgICAgICAga2V5OiAnVHlwZURlcycNCiAgICAgICAgICAgIC8vIHJlbmRlcjogKHJvdykgPT4gew0KICAgICAgICAgICAgLy8gICByZXR1cm4gYCR7cm93LlR5cGVEZXN9KCR7cm93LlVuaXR9KWANCiAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAn5pWw5o2u5Y+C5pWwJywNCiAgICAgICAgICAgIGtleTogJ1ZhbHVlJw0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICfljZXkvY0nLA0KICAgICAgICAgICAga2V5OiAnVW5pdCcNCiAgICAgICAgICB9DQogICAgICAgIF0NCiAgICAgICAgLyoqIGVuZCAgKi8NCiAgICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy50b3RhbCA9IHJlcy5EYXRhLlRvdGFsQ291bnQNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UpDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBnZXREaWN0aW9uYXJ5RGV0YWlsTGlzdEJ5Q29kZShkaWN0aW9uYXJ5Q29kZSA9ICdkZXZpY2VUeXBlJywgVmFsdWUpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlDb2RlKHsNCiAgICAgICAgZGljdGlvbmFyeUNvZGUNCiAgICAgIH0pDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICBjb25zdCBvcHRpb25zID0gW3sgbGFiZWw6ICflhajpg6gnLCB2YWx1ZTogJycgfV0NCiAgICAgICAgcmVzLkRhdGEubWFwKChpdGVtKSA9PiB7DQogICAgICAgICAgb3B0aW9ucy5wdXNoKHsNCiAgICAgICAgICAgIGxhYmVsOiBpdGVtLkRpc3BsYXlfTmFtZSwNCiAgICAgICAgICAgIHZhbHVlOiBpdGVtW1ZhbHVlXSwNCiAgICAgICAgICAgIC4uLml0ZW0NCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgICAgICByZXR1cm4gb3B0aW9ucw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlRWRpdChpbmRleCwgcm93LCB0eXBlKSB7DQogICAgICBjb25zb2xlLmxvZyhpbmRleCwgcm93LCB0eXBlKQ0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgaWYgKHR5cGUgPT09ICd2aWV3Jykgew0KICAgICAgICB0aGlzLmRpYWxvZ1RpdGxlID0gJ+afpeeciycNCiAgICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gRGlhbG9nRm9ybUxvb2sNCiAgICAgICAgdGhpcy5jb21wb25lbnRzQ29uZmlnID0gew0KICAgICAgICAgIElEOiByb3cuSWQsDQogICAgICAgICAgZGlzYWJsZWQ6IHRydWUsDQogICAgICAgICAgdGl0bGU6ICfmn6XnnIsnLA0KICAgICAgICAgIC4uLnJvdw0KICAgICAgICB9DQogICAgICB9DQogICAgICAvLyBlbHNlIGlmICh0eXBlID09PSAnZWRpdCcpIHsNCiAgICAgIC8vICAgdGhpcy5kaWFsb2dUaXRsZSA9ICfnvJbovpEnDQogICAgICAvLyAgIHRoaXMuY29tcG9uZW50c0NvbmZpZyA9IHsNCiAgICAgIC8vICAgICBJRDogcm93LklELA0KICAgICAgLy8gICAgIGRpc2FibGVkOiBmYWxzZSwNCiAgICAgIC8vICAgICB0aXRsZTogJ+e8lui+kScNCiAgICAgIC8vICAgfQ0KICAgICAgLy8gfQ0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgY29uc29sZS5sb2codGhpcy5ydWxlRm9ybSkNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEV4cG9ydERhdGFMaXN0KHsNCiAgICAgICAgQ29udGVudDogJycsDQogICAgICAgIEVxdFR5cGU6ICcnLA0KICAgICAgICBQb3NpdGlvbjogJycsDQogICAgICAgIElzQWxsOiBmYWxzZSwNCiAgICAgICAgSWRzOiB0aGlzLnRhYmxlU2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS5JZCksDQogICAgICAgIC4uLnRoaXMucnVsZUZvcm0NCiAgICAgIH0pDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICBjb25zb2xlLmxvZyhyZXMpDQogICAgICAgIGRvd25sb2FkRmlsZShyZXMuRGF0YSwgJzIxJykNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UpDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBoYW5kbGVBbGxFeHBvcnQoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCBFeHBvcnREYXRhTGlzdCh7DQogICAgICAgIENvbnRlbnQ6ICcnLA0KICAgICAgICBFcXRUeXBlOiAnJywNCiAgICAgICAgUG9zaXRpb246ICcnLA0KICAgICAgICBJc0FsbDogdHJ1ZSwNCiAgICAgICAgSWRzOiBbXSwNCiAgICAgICAgLi4udGhpcy5ydWxlRm9ybQ0KICAgICAgfSkNCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIGNvbnNvbGUubG9nKHJlcykNCiAgICAgICAgZG93bmxvYWRGaWxlKHJlcy5EYXRhLCAnMjEnKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuTWVzc2FnZSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVNpemVDaGFuZ2UodmFsKSB7DQogICAgICBjb25zb2xlLmxvZyhg5q+P6aG1ICR7dmFsfSDmnaFgKQ0KICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5wYWdlU2l6ZSA9IHZhbA0KICAgICAgdGhpcy5pbml0KCkNCiAgICB9LA0KICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7DQogICAgICBjb25zb2xlLmxvZyhg5b2T5YmN6aG1OiAke3ZhbH1gKQ0KICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5jdXJyZW50UGFnZSA9IHZhbA0KICAgICAgdGhpcy5pbml0KCkNCiAgICB9LA0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMudGFibGVTZWxlY3Rpb24gPSBzZWxlY3Rpb24NCiAgICAgIGlmICh0aGlzLnRhYmxlU2VsZWN0aW9uLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5idXR0b25Db25maWcuYnV0dG9uTGlzdC5maW5kKA0KICAgICAgICAgICh2KSA9PiB2LmtleSA9PSAnYmF0Y2gnDQogICAgICAgICkuZGlzYWJsZWQgPSBmYWxzZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5idXR0b25Db25maWcuYnV0dG9uTGlzdC5maW5kKA0KICAgICAgICAgICh2KSA9PiB2LmtleSA9PSAnYmF0Y2gnDQogICAgICAgICkuZGlzYWJsZWQgPSB0cnVlDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk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file": "index.vue", "sourceRoot": "src/views/business/hazardousChemicals/monitorData", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport DialogFormLook from './dialogFormLook.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport { GetDataList, ExportDataList, GetHazchemDTCList } from '@/api/business/hazardousChemicals'\r\nimport { GetDictionaryDetailListByCode } from '@/api/sys'\r\n\r\nimport { deviceTypeMixins } from '../../mixins/deviceType.js'\r\nimport otherMixin from '../../mixins/index.js'\r\n// import moment from 'moment'\r\nimport dayjs from 'dayjs'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  mixins: [deviceTypeMixins, otherMixin],\r\n  data() {\r\n    return {\r\n      currentComponent: DialogFormLook,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        DataType: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Content', // 字段ID\r\n            label: '', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '输入设备编号或名称进行搜索'\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EqtType',\r\n            label: '设备类型',\r\n            type: 'select',\r\n\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择设备类型'\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              this.ruleForm.DataType = ''\r\n              const Id = this.deceiveTypeList.find((item) => item.value === e).Id\r\n              this.getDTCList(GetHazchemDTCList, Id, 'DataType', 'DataId')\r\n            }\r\n          },\r\n          {\r\n            key: 'DataType',\r\n            label: '数据类型',\r\n            type: 'select',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择设备类型'\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position', // 字段ID\r\n            label: '安装位置', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请输入安装位置'\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            // {\r\n            //   text: '新增',\r\n            //   round: false, // 是否圆角\r\n            //   plain: false, // 是否朴素\r\n            //   circle: false, // 是否圆形\r\n            //   loading: false, // 是否加载中\r\n            //   disabled: false, // 是否禁用\r\n            //   icon: '', //  图标\r\n            //   autofocus: false, // 是否聚焦\r\n            //   type: 'primary', // primary / success / warning / danger / info / text\r\n            //   size: 'small', // medium / small / mini\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleCreate()\r\n            //   }\r\n            // },\r\n            // {\r\n            //   text: '导出',\r\n            //   key: 'batch',\r\n            //   disabled: true,\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleExport()\r\n            //   }\r\n            // },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleAllExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            otherOptions: {\r\n              width: 20,\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 200\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          }\r\n          // {\r\n          //   actionLabel: '编辑',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleEdit(index, row, 'edit')\r\n          //   }\r\n          // },\r\n          // {\r\n          //   actionLabel: '删除',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleDelete(index, row)\r\n          //   }\r\n          // }\r\n        ],\r\n        deceiveTypeList: []\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  async mounted() {\r\n    this.init()\r\n    this.deceiveTypeList = this.customForm.formItems.find((item) => item.key === 'EqtType').options = await this.getDictionaryDetailListByCode('HazchemEqtType', 'Value')\r\n    // this.initDeviceType(\"EqtType\", \"EnvironmentEqtType\");\r\n    this.getDTCList(GetHazchemDTCList, '', 'DataType', 'DataId')\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.getDTCList(GetHazchemDTCList, '', 'DataType', 'DataId')\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.GetDataList()\r\n    },\r\n    init() {\r\n      this.GetDataList()\r\n    },\r\n    async GetDataList() {\r\n      this.customTableConfig.loading = true\r\n      const res = await GetDataList({\r\n        ParameterJson: [\r\n          {\r\n            Key: '',\r\n            Value: [null],\r\n            Type: '',\r\n            Filter_Type: ''\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        SortName: '',\r\n        SortOrder: '',\r\n        Search: '',\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        ...this.ruleForm\r\n      })\r\n      this.customTableConfig.loading = false\r\n      if (res.IsSucceed) {\r\n        if (res.Data.Data && res.Data.Data.length > 0) {\r\n          this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n            ...item,\r\n            Time: dayjs(item.Time).format('YYYY-MM-DD HH:mm:ss')\r\n          }))\r\n        } else {\r\n          this.customTableConfig.tableData = []\r\n        }\r\n        /* this.customTableConfig.tableColumns = [].concat(\r\n          [\r\n            {\r\n              width: 50,\r\n              otherOptions: {\r\n                type: 'selection',\r\n                align: 'center'\r\n              }\r\n            },\r\n            {\r\n              width: 60,\r\n              label: '序号',\r\n              otherOptions: {\r\n                type: 'index',\r\n                align: 'center'\r\n              }\r\n            }\r\n          ],\r\n          res.Data.Data.Headers\r\n        )\r\n        console.log(res.Data.Data.Headers, 'tableColumns')\r\n        console.log(this.customTableConfig.tableColumns, 'tableColumns')\r\n        */\r\n        /** 2023-09-18 erwin modify */\r\n        this.customTableConfig.tableColumns = [\r\n          // {\r\n          //   width: 60,\r\n          //   label: \"序号\",\r\n          //   otherOptions: {\r\n          //     type: \"index\",\r\n          //     align: \"center\",\r\n          //   },\r\n          // },\r\n          {\r\n            label: '设备编号',\r\n            key: 'EId',\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: '设备名称',\r\n            key: 'Name',\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: '设备类型',\r\n            key: 'EqtType',\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: '安装位置',\r\n            key: 'Position'\r\n          },\r\n          {\r\n            label: '数据更新时间',\r\n            key: 'Time'\r\n          },\r\n          {\r\n            label: '数据类型',\r\n            key: 'TypeDes'\r\n            // render: (row) => {\r\n            //   return `${row.TypeDes}(${row.Unit})`\r\n            // }\r\n          },\r\n          {\r\n            label: '数据参数',\r\n            key: 'Value'\r\n          },\r\n          {\r\n            label: '单位',\r\n            key: 'Unit'\r\n          }\r\n        ]\r\n        /** end  */\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    async getDictionaryDetailListByCode(dictionaryCode = 'deviceType', Value) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode\r\n      })\r\n      if (res.IsSucceed) {\r\n        const options = [{ label: '全部', value: '' }]\r\n        res.Data.map((item) => {\r\n          options.push({\r\n            label: item.Display_Name,\r\n            value: item[Value],\r\n            ...item\r\n          })\r\n        })\r\n        return options\r\n      }\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      this.dialogVisible = true\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.currentComponent = DialogFormLook\r\n        this.componentsConfig = {\r\n          ID: row.Id,\r\n          disabled: true,\r\n          title: '查看',\r\n          ...row\r\n        }\r\n      }\r\n      // else if (type === 'edit') {\r\n      //   this.dialogTitle = '编辑'\r\n      //   this.componentsConfig = {\r\n      //     ID: row.ID,\r\n      //     disabled: false,\r\n      //     title: '编辑'\r\n      //   }\r\n      // }\r\n    },\r\n    async handleExport() {\r\n      console.log(this.ruleForm)\r\n      const res = await ExportDataList({\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: false,\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    async handleAllExport() {\r\n      const res = await ExportDataList({\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        Ids: [],\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n      if (this.tableSelection.length > 0) {\r\n        this.customTableConfig.buttonConfig.buttonList.find(\r\n          (v) => v.key == 'batch'\r\n        ).disabled = false\r\n      } else {\r\n        this.customTableConfig.buttonConfig.buttonList.find(\r\n          (v) => v.key == 'batch'\r\n        ).disabled = true\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}