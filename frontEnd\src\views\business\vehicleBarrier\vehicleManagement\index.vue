<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
          ><template #customBtn="{ slotScope }"
            ><el-button type="text" @click="setVehicleStatus(slotScope)"
              >{{ slotScope.Status == 0 ? "移出" : "列入" }}黑名单</el-button
            ></template
          ></CustomTable
        >
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @closed="closedDialog"
    >
      <component
        :is="currentComponent"
        ref="dialogRef"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";

import baseInfo from "./dialog/baseInfo.vue";

import { downloadFile } from "@/utils/downloadFile";
import { parseTime } from "@/utils/index.js";
import { GetOssUrl } from "@/api/sys/index";

import importDialog from "@/views/business/vehicleBarrier/components/import.vue";
import exportInfo from "@/views/business/vehicleBarrier/mixins/export.js";
import {
  GetVehiclePageList,
  DelVehicle,
  SetVehicleStatus,
  ExportVehicleData,
  ImportVehicleData,
} from "@/api/business/vehicleBarrier.js";
export default {
  Name: "",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
    baseInfo,
    importDialog,
  },
  mixins: [exportInfo],
  data() {
    return {
      currentComponent: baseInfo,
      componentsConfig: {
        interfaceName: ImportVehicleData,
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogVisible: false,
      dialogTitle: "",
      tableSelection: [],
      selectIds: [],
      ruleForm: {
        VehicleOwnerName: "",
        VehicleOwnerPhone: "",
        Number: "",
      },
      customForm: {
        formItems: [
          {
            key: "VehicleOwnerName", // 字段ID
            label: "车主姓名", // Form的label
            type: "input", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
            },
          },
          {
            key: "VehicleOwnerPhone",
            label: "车主联系方式",
            type: "input",
            otherOptions: {
              clearable: true,
            },
          },
          {
            key: "Number",
            label: "车牌",
            type: "input",
            otherOptions: {
              clearable: true,
            },
          },
        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "新增",
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: true, // 是否禁用
              icon: "", //  图标
              autofocus: false, // 是否聚焦
              type: "primary", // primary / success / warning / danger / info / text
              size: "small", // medium / small / mini
              onclick: (item) => {
                console.log(item);
                this.handleCreate();
              },
            },
            {
              text: "下载模板",
              disabled: true, // 是否禁用
              onclick: (item) => {
                console.log(item);
                this.ExportData([], "车辆管理模板", ExportVehicleData);
              },
            },
            {
              text: "批量导入",
              disabled: true, // 是否禁用
              onclick: (item) => {
                console.log(item);
                this.currentComponent = "importDialog";
                this.dialogVisible = true;
                this.dialogTitle = "批量导入";
              },
            },
            {
              key: "batch",
              disabled: false, // 是否禁用
              text: "批量导出",
              onclick: (item) => {
                console.log(item);
                this.ExportData(
                  {
                    ...this.ruleForm,
                    Ids: this.selectIds.toString(),
                  },
                  "车辆管理",
                  ExportVehicleData
                );
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        height: "100%",
        tableColumns: [
          {
            width: 50,
            otherOptions: {
              type: "selection",
              align: "center",
            },
          },
          {
            label: "车牌号码",
            key: "Number",
          },
          {
            label: "车辆品牌",
            key: "Brand",
          },
          {
            label: "车辆型号",
            key: "VehicleModel",
          },
          {
            label: "车辆颜色",
            key: "VehicleColorImg",
            render: (row) => {
              if (row.VehicleColorImgUrl) {
                return this.$createElement("el-image", {
                  attrs: {
                    src: row.VehicleColorImgUrl,
                    previewSrcList: [
                      row.VehicleColorImgUrl,
                      row.VehicleImgUrl,
                      row.VehicleNumberImgUrl,
                    ],
                  },
                });
              }
            },
          },
          {
            label: "车辆照片",
            key: "VehicleImg",
            render: (row) => {
              if (row.VehicleImgUrl) {
                return this.$createElement("el-image", {
                  attrs: {
                    src: row.VehicleImgUrl,
                    previewSrcList: [
                      row.VehicleColorImgUrl,
                      row.VehicleImgUrl,
                      row.VehicleNumberImgUrl,
                    ],
                  },
                });
              }
            },
          },
          {
            label: "车牌照片",
            key: "VehicleNumberImg",
            render: (row) => {
              if (row.VehicleNumberImgUrl) {
                return this.$createElement("el-image", {
                  attrs: {
                    src: row.VehicleNumberImgUrl,
                    previewSrcList: [
                      row.VehicleColorImgUrl,
                      row.VehicleImgUrl,
                      row.VehicleNumberImgUrl,
                    ],
                  },
                });
              }
            },
          },
          {
            label: "车辆类型",
            key: "TypeName",
          },
          {
            label: "车主姓名",
            key: "VehicleOwnerName",
          },
          {
            label: "车主联系方式",
            key: "VehicleOwnerPhoneCollect",
            otherOptions: {
              showOverflowTooltip: true,
            },
          },
          {
            label: "停车场",
            key: "PackingName",
          },
          {
            // 车位编码
            label: "固定停车位",
            key: "FixedPackingSpace",
          },
          {
            label: "新增时间",
            key: "Create_Date",
          },
          {
            label: "新增操作人",
            key: "Create_UserName",
          },
        ],
        tableData: [],
        tableActionsWidth: 220,
        tableActions: [
          {
            actionLabel: "编辑",
            otherOptions: {
              type: "text",
              disabled: true, // 是否禁用
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, "edit");
            },
          },
          {
            actionLabel: "删除",
            otherOptions: {
              type: "text",
              disabled: true, // 是否禁用
            },
            onclick: (index, row) => {
              this.handleDelete(index, row);
            },
          },
          {
            actionLabel: "查看详情",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, "view");
            },
          },
          // {
          //   actionLabel: '列入黑名单',
          //   otherOptions: {
          //     type: 'text'
          //   },
          //   onclick: (index, row) => {
          //     this.setVehicleStatus(row)
          //   }
          // }
        ],
        operateOptions: {
          width: 300, // 操作栏宽度
        },
      },
    };
  },
  computed: {},
  created() {
    this.init();
  },
  methods: {
    searchForm(data) {
      this.customTableConfig.currentPage = 1;
      console.log(data);
      this.onFresh();
    },
    resetForm() {
      this.onFresh();
    },
    onFresh() {
      this.fetchData();
    },
    async init() {
      await this.fetchData();
    },
    async fetchData() {
      const res = await GetVehiclePageList({
        ParameterJson: [
          {
            Key: "",
            Value: [null],
            Type: "",
            Filter_Type: "",
          },
        ],
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });

      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data;
        this.customTableConfig.total = res.Data.Total;
        this.handelImage(res.Data.Data);
      }
    },
    handelImage(data) {
      const promises = data.map(async (v) => {
        let VehicleColorUrl = "";
        let VehicleUrl = "";
        let VehicleNumberUrl = "";
        const arr = [];

        if (v.VehicleColorImg) {
          const vehicleColorRes = await GetOssUrl({ url: v.VehicleColorImg });
          VehicleColorUrl = vehicleColorRes.Data;
        }

        if (v.VehicleImg) {
          const vehicleRes = await GetOssUrl({ url: v.VehicleImg });
          VehicleUrl = vehicleRes.Data;
        }

        if (v.VehicleNumberImg) {
          const vehicleNumberRes = await GetOssUrl({ url: v.VehicleNumberImg });
          VehicleNumberUrl = vehicleNumberRes.Data;
        }

        return (function () {
          // 使用闭包来捕获变量的当前值
          const capturedVehicleColorUrl = VehicleColorUrl;
          const capturedVehicleUrl = VehicleUrl;
          const capturedVehicleNumberUrl = VehicleNumberUrl;

          v.VehicleColorImgUrl = v.VehicleColorImg
            ? capturedVehicleColorUrl
            : "";
          v.VehicleImgUrl = v.VehicleImg ? capturedVehicleUrl : "";
          v.VehicleNumberImgUrl = v.VehicleNumberImg
            ? capturedVehicleNumberUrl
            : "";
          v.Create_Date = parseTime(
            new Date(v.Create_Date),
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          arr.push(
            v.VehicleOwnerPhone,
            v.VehicleOwnerPhone1,
            v.VehicleOwnerPhone2
          );
          v.VehicleOwnerPhoneCollect = arr.filter(Boolean).join(",");

          return v;
        })();
      });

      Promise.all(promises)
        .then((data) => {
          this.customTableConfig.tableData = data;
          console.log(this.tableData);
        })
        .catch((error) => {
          console.error(error);
        });
    },
    handleCreate() {
      this.currentComponent = "baseInfo";
      this.dialogTitle = "新增";
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.dialogRef.init(0, {}, "add");
      });
    },
    handleDelete(index, row) {
      console.log(index, row);
      console.log(this);
      this.$confirm("确认删除？", {
        type: "warning",
      })
        .then(async (_) => {
          const res = await DelVehicle({
            Id: row.Id,
          });
          if (res.IsSucceed) {
            this.$message({
              message: "删除成功",
              type: "success",
            });
            this.init();
          } else {
            this.$message({
              message: res.Message,
              type: "error",
            });
          }
        })
        .catch((_) => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleEdit(index, row, type) {
      console.log(index, row, type);
      this.currentComponent = "baseInfo";
      if (type === "view") {
        this.dialogTitle = "查看";
      } else if (type === "edit") {
        this.dialogTitle = "编辑";
      }
      this.$nextTick(() => {
        this.$refs.dialogRef.init(index, row, type);
      });

      this.dialogVisible = true;
    },
    // 列入黑白名单
    setVehicleStatus(row) {
      this.$confirm(
        `确认${row.Status == 0 ? "移出" : "列入"}黑名单?`,
        `确认${row.Status == 0 ? "移出" : "列入"}黑名单?`,
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          SetVehicleStatus({ id: row.Id }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                type: "success",
                message: "保存成功!",
              });
              this.init();
            } else {
              this.$message({
                message: res.Message,
                type: "error",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 关闭弹窗
    closedDialog() {
      this.$refs.dialogRef.closeClearForm();
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.onFresh();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.onFresh();
    },
    handleSelectionChange(selection) {
      const Ids = [];
      this.tableSelection = selection;
      this.tableSelection.forEach((item) => {
        Ids.push(item.Id);
      });
      console.log(Ids);
      this.selectIds = Ids;
      console.log(this.tableSelection);
      // if (this.tableSelection.length > 0) {
      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = false
      // } else {
      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = true
      // }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/views/business/vehicleBarrier/index.scss";
.mt20 {
  margin-top: 10px;
}
::v-deep {
  .el-dialog__body {
    padding: 0px 20px 5px;
  }
}
</style>
