{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\vehiclesInThePark\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\vehiclesInThePark\\index.vue", "mtime": 1755674552437}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQ3VzdG9tTGF5b3V0IGZyb20gIkAvYnVzaW5lc3NDb21wb25lbnRzL0N1c3RvbUxheW91dC9pbmRleC52dWUiOw0KaW1wb3J0IEN1c3RvbVRhYmxlIGZyb20gIkAvYnVzaW5lc3NDb21wb25lbnRzL0N1c3RvbVRhYmxlL2luZGV4LnZ1ZSI7DQppbXBvcnQgQ3VzdG9tRm9ybSBmcm9tICJAL2J1c2luZXNzQ29tcG9uZW50cy9DdXN0b21Gb3JtL2luZGV4LnZ1ZSI7DQppbXBvcnQgew0KICBWQkluUGFya1ZlaGljbGVzR2V0SW5QYXJrVmVoaWNsZXNMaXN0LA0KICBWQkluUGFya1ZlaGljbGVzR2V0SW5QYXJrU3RhdGlzdGljcywNCiAgVkJJblBhcmtWZWhpY2xlc1ZlaGljbGVMZWF2aW5nLA0KICBWQkluUGFya1ZlaGljbGVzRXhwb3J0RGF0YSwNCiAgVkJQYXNzUmVjb3JkR2V0RHJvcExpc3QsDQp9IGZyb20gIkAvYXBpL2J1c2luZXNzL3ZlaGljbGVCYXJyaWVyLmpzIjsNCmltcG9ydCBleHBvcnRJbmZvIGZyb20gIkAvdmlld3MvYnVzaW5lc3MvdmVoaWNsZUJhcnJpZXIvbWl4aW5zL2V4cG9ydC5qcyI7DQppbXBvcnQgYWRkUm91dGVyUGFnZSBmcm9tICJAL21peGlucy9hZGQtcm91dGVyLXBhZ2UiOw0KDQppbXBvcnQgVkNoYXJ0IGZyb20gInZ1ZS1lY2hhcnRzIjsNCmltcG9ydCB7IHVzZSB9IGZyb20gImVjaGFydHMvY29yZSI7DQppbXBvcnQgeyBDYW52YXNSZW5kZXJlciB9IGZyb20gImVjaGFydHMvcmVuZGVyZXJzIjsNCmltcG9ydCB7IFBpZUNoYXJ0IH0gZnJvbSAiZWNoYXJ0cy9jaGFydHMiOw0KaW1wb3J0IHsNCiAgR3JpZENvbXBvbmVudCwNCiAgTGVnZW5kQ29tcG9uZW50LA0KICBUb29sdGlwQ29tcG9uZW50LA0KICBUaXRsZUNvbXBvbmVudCwNCiAgRGF0YVpvb21Db21wb25lbnQsDQp9IGZyb20gImVjaGFydHMvY29tcG9uZW50cyI7DQp1c2UoWw0KICBDYW52YXNSZW5kZXJlciwNCiAgUGllQ2hhcnQsDQogIERhdGFab29tQ29tcG9uZW50LA0KICBHcmlkQ29tcG9uZW50LA0KICBMZWdlbmRDb21wb25lbnQsDQogIFRpdGxlQ29tcG9uZW50LA0KICBUb29sdGlwQ29tcG9uZW50LA0KXSk7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgTmFtZTogInZlaGljbGVQZWVyUmVjb3JkIiwNCiAgY29tcG9uZW50czogew0KICAgIEN1c3RvbVRhYmxlLA0KICAgIEN1c3RvbUZvcm0sDQogICAgQ3VzdG9tTGF5b3V0LA0KICAgIFZDaGFydCwNCiAgfSwNCiAgbWl4aW5zOiBbZXhwb3J0SW5mbywgYWRkUm91dGVyUGFnZV0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHBpZU9wdGlvbk9wdGlvbjogew0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogIml0ZW0iLA0KICAgICAgICB9LA0KICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICBzaG93OiBmYWxzZSwNCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogIui9pui+huexu+Wei+e7n+iuoSIsDQogICAgICAgICAgICB0eXBlOiAicGllIiwNCiAgICAgICAgICAgIHJhZGl1czogWyI2MCUiLCAiOTAlIl0sDQogICAgICAgICAgICBhdm9pZExhYmVsT3ZlcmxhcDogZmFsc2UsDQogICAgICAgICAgICBsYWJlbDogew0KICAgICAgICAgICAgICBzaG93OiBmYWxzZSwNCiAgICAgICAgICAgICAgcG9zaXRpb246ICJjZW50ZXIiLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGxhYmVsTGluZTogew0KICAgICAgICAgICAgICBzaG93OiBmYWxzZSwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgICAgIC8vIGNvbG9yOlsnIzI5OERGRiddDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgICBydWxlRm9ybTogew0KICAgICAgICBOdW1iZXI6ICIiLA0KICAgICAgICBEYXRlOiBbXSwNCiAgICAgICAgU3RhcnRUaW1lOiBudWxsLA0KICAgICAgICBFbmRUaW1lOiBudWxsLA0KICAgICAgICBBY2Nlc3NUeXBlOiAiIiwNCiAgICAgICAgVXNlck5hbWU6ICIiLA0KICAgICAgICBWZWhpY2xlVHlwZTogIiIsDQogICAgICAgIFRpbWVvdXQ6ICIiLA0KICAgICAgfSwNCg0KICAgICAgdGFibGVTZWxlY3Rpb246IFtdLA0KDQogICAgICBjdXN0b21Gb3JtOiB7DQogICAgICAgIGZvcm1JdGVtczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGtleTogIk51bWJlciIsIC8vIOWtl+autUlEDQogICAgICAgICAgICBsYWJlbDogIui9pueJjOWPt+eggSIsIC8vIEZvcm3nmoRsYWJlbA0KICAgICAgICAgICAgdHlwZTogImlucHV0IiwgLy8gaW5wdXQ65pmu6YCa6L6T5YWl5qGGLHRleHRhcmVhOuaWh+acrOWfnyxzZWxlY3Q65LiL5ouJ6YCJ5oup5ZmoLGRhdGVwaWNrZXI65pel5pyf6YCJ5oup5ZmoDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgLy8g6Zmk5LqGbW9kZWzku6XlpJbnmoTlhbbku5bnmoTlj4LmlbAs5YW35L2T6K+35Y+C6ICDZWxlbWVudOaWh+ahow0KICAgICAgICAgICAgICBjbGVhcmFibGU6IHRydWUsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgaW5wdXQ6IChlKSA9PiB7fSwNCiAgICAgICAgICAgIGNoYW5nZTogKCkgPT4ge30sDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBrZXk6ICJEYXRlIiwNCiAgICAgICAgICAgIGxhYmVsOiAi6YCa6KGM5pe26Ze0IiwNCiAgICAgICAgICAgIHR5cGU6ICJkYXRlUGlja2VyIiwNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICB0eXBlOiAiZGF0ZXRpbWVyYW5nZSIsDQogICAgICAgICAgICAgIHJhbmdlU2VwYXJhdG9yOiAi6IezIiwNCiAgICAgICAgICAgICAgc3RhcnRQbGFjZWhvbGRlcjogIuW8gOWni+aXpeacnyIsDQogICAgICAgICAgICAgIGVuZFBsYWNlaG9sZGVyOiAi57uT5p2f5pel5pyfIiwNCiAgICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlLA0KICAgICAgICAgICAgICB2YWx1ZUZvcm1hdDogInl5eXktTU0tZGQgSEg6bW0iLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGNoYW5nZTogKGUpID0+IHsNCiAgICAgICAgICAgICAgaWYgKGUgJiYgZS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5TdGFydFRpbWUgPSBlWzBdOw0KICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0uRW5kVGltZSA9IGVbMV07DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5TdGFydFRpbWUgPSBudWxsOw0KICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0uRW5kVGltZSA9IG51bGw7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBrZXk6ICJWZWhpY2xlVHlwZSIsDQogICAgICAgICAgICBsYWJlbDogIui9pui+huexu+WeiyIsDQogICAgICAgICAgICB0eXBlOiAic2VsZWN0IiwNCiAgICAgICAgICAgIG9wdGlvbnM6IFtdLA0KICAgICAgICAgICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZSwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBjaGFuZ2U6IChlKSA9PiB7fSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGtleTogIlVzZXJOYW1lIiwNCiAgICAgICAgICAgIGxhYmVsOiAi6L2m5Li75aeT5ZCNIiwNCiAgICAgICAgICAgIHR5cGU6ICJpbnB1dCIsDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGlucHV0OiAoZSkgPT4ge30sDQogICAgICAgICAgICBjaGFuZ2U6ICgpID0+IHt9LA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiAiQWNjZXNzVHlwZSIsDQogICAgICAgICAgICBsYWJlbDogIuiuv+mXruexu+WeiyIsDQogICAgICAgICAgICB0eXBlOiAic2VsZWN0IiwNCiAgICAgICAgICAgIG9wdGlvbnM6IFtdLA0KICAgICAgICAgICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZSwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBjaGFuZ2U6IChlKSA9PiB7fSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGtleTogIlRpbWVvdXQiLA0KICAgICAgICAgICAgbGFiZWw6ICLmmK/lkKbotoXml7YiLA0KICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsDQogICAgICAgICAgICBvcHRpb25zOiBbDQogICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICBsYWJlbDogIuaYryIsDQogICAgICAgICAgICAgICAgdmFsdWU6IHRydWUsDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICBsYWJlbDogIuWQpiIsDQogICAgICAgICAgICAgICAgdmFsdWU6IGZhbHNlLA0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgXSwNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICBjbGVhcmFibGU6IHRydWUsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgY2hhbmdlOiAoZSkgPT4ge30sDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY3VzdG9tRm9ybUJ1dHRvbnM6IHsNCiAgICAgICAgICBzdWJtaXROYW1lOiAi5p+l6K+iIiwNCiAgICAgICAgICByZXNldE5hbWU6ICLph43nva4iLA0KICAgICAgICB9LA0KICAgICAgfSwNCiAgICAgIGN1c3RvbVRhYmxlQ29uZmlnOiB7DQogICAgICAgIGJ1dHRvbkNvbmZpZzogew0KICAgICAgICAgIGJ1dHRvbkxpc3Q6IFsNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAiYmF0Y2giLA0KICAgICAgICAgICAgICBkaXNhYmxlZDogZmFsc2UsIC8vIOaYr+WQpuemgeeUqA0KICAgICAgICAgICAgICB0ZXh0OiAi5om56YeP5a+85Ye6IiwNCiAgICAgICAgICAgICAgb25jbGljazogKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhpdGVtKTsNCiAgICAgICAgICAgICAgICB0aGlzLkV4cG9ydERhdGEoDQogICAgICAgICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLA0KICAgICAgICAgICAgICAgICAgIuWcqOWbrei9puS/qSIsDQogICAgICAgICAgICAgICAgICBWQkluUGFya1ZlaGljbGVzRXhwb3J0RGF0YQ0KICAgICAgICAgICAgICAgICk7DQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICB9LA0KICAgICAgICAgIF0sDQogICAgICAgIH0sDQogICAgICAgIC8vIOihqOagvA0KICAgICAgICBwYWdlU2l6ZU9wdGlvbnM6IFsxMCwgMjAsIDUwLCA4MF0sDQogICAgICAgIGN1cnJlbnRQYWdlOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMjAsDQogICAgICAgIHRvdGFsOiAwLA0KICAgICAgICBoZWlnaHQ6ICIxMDAlIiwNCiAgICAgICAgdGFibGVDb2x1bW5zOiBbDQogICAgICAgICAgLy8gew0KICAgICAgICAgIC8vICAgd2lkdGg6IDUwLA0KICAgICAgICAgIC8vICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgLy8gICAgIHR5cGU6ICdzZWxlY3Rpb24nLA0KICAgICAgICAgIC8vICAgICBhbGlnbjogJ2NlbnRlcicNCiAgICAgICAgICAvLyAgIH0NCiAgICAgICAgICAvLyB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAi6am25YWl5pe26Ze0IiwNCiAgICAgICAgICAgIGtleTogIlBhc3NUaW1lIiwNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICBmaXhlZDogImxlZnQiLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAi6L2m54mM5Y+356CBIiwNCiAgICAgICAgICAgIGtleTogIk51bWJlciIsDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgZml4ZWQ6ICJsZWZ0IiwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogIui9pui+huexu+WeiyIsDQogICAgICAgICAgICBrZXk6ICJWZWhpY2xlVHlwZSIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogIui9puS4u+Wnk+WQjSIsDQogICAgICAgICAgICBrZXk6ICJVc2VyTmFtZSIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogIui9puS4u+iBlOezu+aWueW8jyIsDQogICAgICAgICAgICBrZXk6ICJVc2VyUGhvbmUiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICLorr/pl67nsbvlnosiLA0KICAgICAgICAgICAga2V5OiAiQWNjZXNzVHlwZSIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogIuWHuuWFpeWPoyIsDQogICAgICAgICAgICBrZXk6ICJFbnRyYW5jZU5hbWUiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICLlhaXlm63ml7bplb8iLA0KICAgICAgICAgICAga2V5OiAiUGFya1RpbWUiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICLotoXml7bml7bplb8iLA0KICAgICAgICAgICAga2V5OiAiVGltZW91dCIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgdGFibGVEYXRhOiBbXSwNCiAgICAgICAgdGFibGVBY3Rpb25zV2lkdGg6IDEyMCwNCiAgICAgICAgdGFibGVBY3Rpb25zOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgYWN0aW9uTGFiZWw6ICLmn6XnnIsiLA0KICAgICAgICAgICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgICAgIHR5cGU6ICJ0ZXh0IiwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBvbmNsaWNrOiAoaW5kZXgsIHJvdykgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgICAgICAgICAgbmFtZTogInZlaGljbGVzSW5UaGVQYXJrVmlldyIsDQogICAgICAgICAgICAgICAgcXVlcnk6IHsgcGdfcmVkaXJlY3Q6IHRoaXMuJHJvdXRlLm5hbWUsIElkOiByb3cuSWQgfSwNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgYWN0aW9uTGFiZWw6ICLovabovoblh7rlm60iLA0KICAgICAgICAgICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgICAgIHR5cGU6ICJ0ZXh0IiwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBvbmNsaWNrOiAoaW5kZXgsIHJvdykgPT4gew0KICAgICAgICAgICAgICB0aGlzLmhhbmRsZUxlYXZlKHJvdyk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICB9LA0KICAgICAgYWRkUGFnZUFycmF5OiBbDQogICAgICAgIHsNCiAgICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgIi92aWV3IiwNCiAgICAgICAgICBoaWRkZW46IHRydWUsDQogICAgICAgICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoIi4vZGlhbG9nL3ZpZXcudnVlIiksDQogICAgICAgICAgbWV0YTogeyB0aXRsZTogIuWcqOWbrei9pui+huivpuaDhSIgfSwNCiAgICAgICAgICBuYW1lOiAidmVoaWNsZXNJblRoZVBhcmtWaWV3IiwNCiAgICAgICAgfSwNCiAgICAgIF0sDQogICAgICBpblBhcmtTdGF0aXN0aWNzOiB7fSwNCg0KICAgICAgQWNjZXNzUHJlY2VudDogMCwNCiAgICAgIFRpbWVvdXRQcmVjZW50OiAwLA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy52QlBhc3NSZWNvcmRHZXREcm9wTGlzdCgpOw0KICAgIC8vIHRoaXMuaW5pdCgpOw0KICAgIC8vIHRoaXMudkJJblBhcmtWZWhpY2xlc0dldEluUGFya1N0YXRpc3RpY3MoKTsNCiAgfSwNCiAgYXN5bmMgbW91bnRlZCgpIHsNCiAgICAvLyDot7Povazorr7nva7pu5jorqTlj4LmlbANCiAgICBsZXQgSnVtcFBhcmFtcyA9IHRoaXMuJHFpYW5rdW4uZ2V0TWljcm9BcHBKdW1wUGFyYW1zRm4oKTsNCiAgICBpZiAoSnVtcFBhcmFtcy5pc0p1bXAgPT0gInRydWUiKSB7DQogICAgICB0aGlzLnJ1bGVGb3JtLlRpbWVvdXQgPSBKdW1wUGFyYW1zLlRpbWVvdXQgPT0gInRydWUiOw0KICAgIH0NCiAgICB0aGlzLm9uRnJlc2goKTsNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICB0aGlzLiRxaWFua3VuLnNldE1pY3JvQXBwSnVtcFBhcmFtc0ZuKCk7DQogICAgdGhpcy5ydWxlRm9ybS5UaW1lb3V0ID0gbnVsbDsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFzeW5jIHZCSW5QYXJrVmVoaWNsZXNHZXRJblBhcmtTdGF0aXN0aWNzKCkgew0KICAgICAgaWYgKHRoaXMucnVsZUZvcm0uRGF0ZS5sZW5ndGggPT0gMCkgew0KICAgICAgICB0aGlzLnJ1bGVGb3JtLlN0YXJ0VGltZSA9IG51bGw7DQogICAgICAgIHRoaXMucnVsZUZvcm0uRW5kVGltZSA9IG51bGw7DQogICAgICB9DQogICAgICBsZXQgcmVzID0gYXdhaXQgVkJJblBhcmtWZWhpY2xlc0dldEluUGFya1N0YXRpc3RpY3Moew0KICAgICAgICAuLi50aGlzLnJ1bGVGb3JtLA0KICAgICAgfSk7DQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICB0aGlzLmluUGFya1N0YXRpc3RpY3MgPSByZXMuRGF0YTsNCiAgICAgICAgbGV0IEFjY2Vzc1RvdGFsID0gcmVzLkRhdGEuQWNjZXNzLnJlZHVjZSgNCiAgICAgICAgICAoYWNjdW11bGF0b3IsIGN1cnJlbnRWYWx1ZSkgPT4NCiAgICAgICAgICAgIGFjY3VtdWxhdG9yICsgcGFyc2VJbnQoY3VycmVudFZhbHVlLlZhbHVlKSwNCiAgICAgICAgICAwDQogICAgICAgICk7DQogICAgICAgIGxldCBUaW1lb3V0VG90YWwgPSByZXMuRGF0YS5UaW1lb3V0LnJlZHVjZSgNCiAgICAgICAgICAoYWNjdW11bGF0b3IsIGN1cnJlbnRWYWx1ZSkgPT4NCiAgICAgICAgICAgIGFjY3VtdWxhdG9yICsgcGFyc2VJbnQoY3VycmVudFZhbHVlLlZhbHVlKSwNCiAgICAgICAgICAwDQogICAgICAgICk7DQogICAgICAgIC8vIGxldCBWZWhpY2xlc1R5cGVUb3RhbCA9IHJlcy5EYXRhLlZlaGljbGVzVHlwZS5tYXAoDQogICAgICAgIC8vICAgKG9iaikgPT4gb2JqLnZhbHVlDQogICAgICAgIC8vICkucmVkdWNlKA0KICAgICAgICAvLyAgIChhY2N1bXVsYXRvciwgY3VycmVudFZhbHVlKSA9PiBhY2N1bXVsYXRvciArIGN1cnJlbnRWYWx1ZS5WYWx1ZSwNCiAgICAgICAgLy8gICAwDQogICAgICAgIC8vICk7DQogICAgICAgIHRoaXMuaW5QYXJrU3RhdGlzdGljcy5BY2Nlc3MgPSByZXMuRGF0YS5BY2Nlc3M7DQogICAgICAgIHRoaXMuaW5QYXJrU3RhdGlzdGljcy5UaW1lb3V0ID0gcmVzLkRhdGEuVGltZW91dDsNCiAgICAgICAgdGhpcy5pblBhcmtTdGF0aXN0aWNzLlZlaGljbGVzVHlwZSA9IHJlcy5EYXRhLlZlaGljbGVzVHlwZS5tYXAoDQogICAgICAgICAgKGl0ZW0pID0+ICh7DQogICAgICAgICAgICB2YWx1ZTogaXRlbS5WYWx1ZSwNCiAgICAgICAgICAgIG5hbWU6IGl0ZW0uS2V5LA0KICAgICAgICAgIH0pDQogICAgICAgICk7DQogICAgICAgIHRoaXMucGllT3B0aW9uT3B0aW9uLnNlcmllc1swXS5kYXRhID0NCiAgICAgICAgICB0aGlzLmluUGFya1N0YXRpc3RpY3MuVmVoaWNsZXNUeXBlOw0KDQogICAgICAgIHRoaXMuQWNjZXNzUHJlY2VudCA9DQogICAgICAgICAgKE51bWJlcihyZXMuRGF0YS5BY2Nlc3NbMF0uVmFsdWUpIC8gQWNjZXNzVG90YWwpICogMTAwOw0KICAgICAgICB0aGlzLlRpbWVvdXRQcmVjZW50ID0NCiAgICAgICAgICAoTnVtYmVyKHJlcy5EYXRhLlRpbWVvdXRbMF0uVmFsdWUpIC8gVGltZW91dFRvdGFsKSAqIDEwMDsNCiAgICAgICAgaWYgKEFjY2Vzc1RvdGFsID09IDApIHsNCiAgICAgICAgICB0aGlzLkFjY2Vzc1ByZWNlbnQgPSAwOw0KICAgICAgICB9DQogICAgICAgIGlmIChUaW1lb3V0VG90YWwgPT0gMCkgew0KICAgICAgICAgIHRoaXMuVGltZW91dFByZWNlbnQgPSAwOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyB2QlBhc3NSZWNvcmRHZXREcm9wTGlzdCgpIHsNCiAgICAgIGxldCByZXMgPSBhd2FpdCBWQlBhc3NSZWNvcmRHZXREcm9wTGlzdCh7fSk7DQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICB0aGlzLmN1c3RvbUZvcm0uZm9ybUl0ZW1zLmZpbmQoKHYpID0+IHYua2V5ID09ICJWZWhpY2xlVHlwZSIpLm9wdGlvbnMgPQ0KICAgICAgICAgIHJlcy5EYXRhLmZpbmQoKHYpID0+IHYuTmFtZSA9PSAiVmVoaWNsZVR5cGUiKS5MaXN0Lm1hcCgoaXRlbSkgPT4gKHsNCiAgICAgICAgICAgIGxhYmVsOiBpdGVtLktleSwNCiAgICAgICAgICAgIHZhbHVlOiBpdGVtLlZhbHVlLA0KICAgICAgICAgIH0pKTsNCiAgICAgICAgdGhpcy5jdXN0b21Gb3JtLmZvcm1JdGVtcy5maW5kKCh2KSA9PiB2LmtleSA9PSAiQWNjZXNzVHlwZSIpLm9wdGlvbnMgPQ0KICAgICAgICAgIHJlcy5EYXRhLmZpbmQoKHYpID0+IHYuTmFtZSA9PSAiQWNjZXNzVHlwZSIpLkxpc3QubWFwKChpdGVtKSA9PiAoew0KICAgICAgICAgICAgbGFiZWw6IGl0ZW0uS2V5LA0KICAgICAgICAgICAgdmFsdWU6IGl0ZW0uVmFsdWUsDQogICAgICAgICAgfSkpOw0KICAgICAgfQ0KICAgIH0sDQogICAgc2VhcmNoRm9ybShkYXRhKSB7DQogICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmN1cnJlbnRQYWdlID0gMTsNCiAgICAgIGNvbnNvbGUubG9nKGRhdGEpOw0KICAgICAgdGhpcy5vbkZyZXNoKCk7DQogICAgfSwNCiAgICByZXNldEZvcm0oKSB7DQogICAgICB0aGlzLm9uRnJlc2goKTsNCiAgICB9LA0KICAgIG9uRnJlc2goKSB7DQogICAgICB0aGlzLmZldGNoRGF0YSgpOw0KICAgICAgdGhpcy52QkluUGFya1ZlaGljbGVzR2V0SW5QYXJrU3RhdGlzdGljcygpOw0KICAgIH0sDQogICAgYXN5bmMgaW5pdCgpIHsNCiAgICAgIGF3YWl0IHRoaXMuZmV0Y2hEYXRhKCk7DQogICAgfSwNCiAgICBhc3luYyBmZXRjaERhdGEoKSB7DQogICAgICBpZiAodGhpcy5ydWxlRm9ybS5EYXRlLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHRoaXMucnVsZUZvcm0uU3RhcnRUaW1lID0gbnVsbDsNCiAgICAgICAgdGhpcy5ydWxlRm9ybS5FbmRUaW1lID0gbnVsbDsNCiAgICAgIH0NCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IFZCSW5QYXJrVmVoaWNsZXNHZXRJblBhcmtWZWhpY2xlc0xpc3Qoew0KICAgICAgICBQYWdlOiB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmN1cnJlbnRQYWdlLA0KICAgICAgICBQYWdlU2l6ZTogdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5wYWdlU2l6ZSwNCiAgICAgICAgLi4udGhpcy5ydWxlRm9ybSwNCiAgICAgIH0pOw0KICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy50YWJsZURhdGEgPSByZXMuRGF0YS5EYXRhOw0KICAgICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLnRvdGFsID0gcmVzLkRhdGEuVG90YWw7DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVTaXplQ2hhbmdlKHZhbCkgew0KICAgICAgY29uc29sZS5sb2coYOavj+mhtSAke3ZhbH0g5p2hYCk7DQogICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLnBhZ2VTaXplID0gdmFsOw0KICAgICAgdGhpcy5vbkZyZXNoKCk7DQogICAgfSwNCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKHZhbCkgew0KICAgICAgY29uc29sZS5sb2coYOW9k+WJjemhtTogJHt2YWx9YCk7DQogICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmN1cnJlbnRQYWdlID0gdmFsOw0KICAgICAgdGhpcy5vbkZyZXNoKCk7DQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLnRhYmxlU2VsZWN0aW9uID0gc2VsZWN0aW9uOw0KICAgIH0sDQogICAgaGFuZGxlTGVhdmUocm93KSB7DQogICAgICB0aGlzLiRjb25maXJtKA0KICAgICAgICAnPGRpdj48ZGl2PuaYr+WQpuehruiupOi/m+ihjOi9pui+huWHuuWbrTwvZGl2PjxkaXYgc3R5bGU9ImZvbnQtc2l6ZToxMnB4O2NvbG9yOiNhYWEiPui9pui+huWHuuWbreaTjeS9nOWwhuagh+ivhuivpei9puS4uuWHuuWbreeKtuaAgTwvZGl2PjwvZGl2PicsDQogICAgICAgICLmk43kvZznoa7orqQiLA0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICAgIGRhbmdlcm91c2x5VXNlSFRNTFN0cmluZzogdHJ1ZSwNCiAgICAgICAgfQ0KICAgICAgKQ0KICAgICAgICAudGhlbihhc3luYyAoXykgPT4gew0KICAgICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IFZCSW5QYXJrVmVoaWNsZXNWZWhpY2xlTGVhdmluZyh7DQogICAgICAgICAgICBJZDogcm93LklkLA0KICAgICAgICAgIH0pOw0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOw0KICAgICAgICAgICAgdGhpcy5pbml0KCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChfKSA9PiB7fSk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy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file": "index.vue", "sourceRoot": "src/views/business/vehicleBarrier/pJVehicleBarrier/vehiclesInThePark", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout\r\n      :layoutConfig=\"{\r\n        isShowLayoutChart: true,\r\n      }\"\r\n    >\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutChart>\r\n        <div class=\"progressBox\">\r\n          <div class=\"progressTwo\">\r\n            <div class=\"left\">\r\n              <span class=\"title\">当前在园车辆总数</span>\r\n              <div class=\"info\" style=\"height: 60.5px\">\r\n                <span class=\"colorActive textActive\">{{\r\n                  inParkStatistics.Total\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"progressTwo\">\r\n            <div class=\"left\">\r\n              <span class=\"title\">访问类型统计</span>\r\n              <div class=\"info\">\r\n                <span class=\"num\">\r\n                  <span\r\n                    :class=\"[index == 0 ? 'colorActive textActive' : '']\"\r\n                    v-for=\"(item, index) in inParkStatistics.Access\"\r\n                    :key=\"index\"\r\n                    >{{ index == 0 ? \"\" : \"/\" }} {{ item.Value }}</span\r\n                  >\r\n                </span>\r\n                <span class=\"text\">\r\n                  <span\r\n                    :class=\"[index == 0 ? 'colorActive' : '']\"\r\n                    v-for=\"(item, index) in inParkStatistics.Access\"\r\n                    :key=\"index\"\r\n                    >{{ index == 0 ? \"\" : \"/\" }} {{ item.Key }}</span\r\n                  >\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <el-progress\r\n              class=\"right\"\r\n              type=\"circle\"\r\n              :stroke-width=\"20\"\r\n              :percentage=\"AccessPrecent.toFixed(2)\"\r\n            ></el-progress>\r\n          </div>\r\n          <div class=\"progressTwo\">\r\n            <div class=\"left\">\r\n              <span class=\"title\">入园超时统计</span>\r\n              <div class=\"info\">\r\n                <span class=\"num\">\r\n                  <span\r\n                    :class=\"[index == 0 ? 'colorActive textActive' : '']\"\r\n                    v-for=\"(item, index) in inParkStatistics.Timeout\"\r\n                    :key=\"index\"\r\n                    >{{ index == 0 ? \"\" : \"/\" }} {{ item.Value }}</span\r\n                  >\r\n                </span>\r\n                <span class=\"text\">\r\n                  <span\r\n                    :class=\"[index == 0 ? 'colorActive' : '']\"\r\n                    v-for=\"(item, index) in inParkStatistics.Timeout\"\r\n                    :key=\"index\"\r\n                    >{{ index == 0 ? \"\" : \"/\" }} {{ item.Key }}</span\r\n                  >\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <el-progress\r\n              class=\"right\"\r\n              type=\"circle\"\r\n              :stroke-width=\"20\"\r\n              :percentage=\"TimeoutPrecent.toFixed(2)\"\r\n            ></el-progress>\r\n          </div>\r\n          <div class=\"progressTwo\">\r\n            <div class=\"left\">\r\n              <span class=\"title\">车辆类型统计</span>\r\n              <div class=\"info\">\r\n                <span\r\n                  style=\"color: #298dff; margin-bottom: 10px\"\r\n                  v-for=\"(item, index) in inParkStatistics.VehiclesType\"\r\n                  :key=\"index\"\r\n                  >{{ item.name }}: {{ item.value }}\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <v-chart\r\n              ref=\"pieChartRef\"\r\n              class=\"pieChartDom\"\r\n              :option=\"pieOptionOption\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport {\r\n  VBInParkVehiclesGetInParkVehiclesList,\r\n  VBInParkVehiclesGetInParkStatistics,\r\n  VBInParkVehiclesVehicleLeaving,\r\n  VBInParkVehiclesExportData,\r\n  VBPassRecordGetDropList,\r\n} from \"@/api/business/vehicleBarrier.js\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/mixins/export.js\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\n\r\nimport VChart from \"vue-echarts\";\r\nimport { use } from \"echarts/core\";\r\nimport { CanvasRenderer } from \"echarts/renderers\";\r\nimport { PieChart } from \"echarts/charts\";\r\nimport {\r\n  GridComponent,\r\n  LegendComponent,\r\n  TooltipComponent,\r\n  TitleComponent,\r\n  DataZoomComponent,\r\n} from \"echarts/components\";\r\nuse([\r\n  CanvasRenderer,\r\n  PieChart,\r\n  DataZoomComponent,\r\n  GridComponent,\r\n  LegendComponent,\r\n  TitleComponent,\r\n  TooltipComponent,\r\n]);\r\n\r\nexport default {\r\n  Name: \"vehiclePeerRecord\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n    VChart,\r\n  },\r\n  mixins: [exportInfo, addRouterPage],\r\n  data() {\r\n    return {\r\n      pieOptionOption: {\r\n        tooltip: {\r\n          trigger: \"item\",\r\n        },\r\n        legend: {\r\n          show: false,\r\n        },\r\n        series: [\r\n          {\r\n            name: \"车辆类型统计\",\r\n            type: \"pie\",\r\n            radius: [\"60%\", \"90%\"],\r\n            avoidLabelOverlap: false,\r\n            label: {\r\n              show: false,\r\n              position: \"center\",\r\n            },\r\n            labelLine: {\r\n              show: false,\r\n            },\r\n            data: [],\r\n            // color:['#298DFF']\r\n          },\r\n        ],\r\n      },\r\n      ruleForm: {\r\n        Number: \"\",\r\n        Date: [],\r\n        StartTime: null,\r\n        EndTime: null,\r\n        AccessType: \"\",\r\n        UserName: \"\",\r\n        VehicleType: \"\",\r\n        Timeout: \"\",\r\n      },\r\n\r\n      tableSelection: [],\r\n\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Number\", // 字段ID\r\n            label: \"车牌号码\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"Date\",\r\n            label: \"通行时间\",\r\n            type: \"datePicker\",\r\n            otherOptions: {\r\n              type: \"datetimerange\",\r\n              rangeSeparator: \"至\",\r\n              startPlaceholder: \"开始日期\",\r\n              endPlaceholder: \"结束日期\",\r\n              clearable: true,\r\n              valueFormat: \"yyyy-MM-dd HH:mm\",\r\n            },\r\n            change: (e) => {\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.StartTime = e[0];\r\n                this.ruleForm.EndTime = e[1];\r\n              } else {\r\n                this.ruleForm.StartTime = null;\r\n                this.ruleForm.EndTime = null;\r\n              }\r\n            },\r\n          },\r\n          {\r\n            key: \"VehicleType\",\r\n            label: \"车辆类型\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"UserName\",\r\n            label: \"车主姓名\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"AccessType\",\r\n            label: \"访问类型\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n          {\r\n            key: \"Timeout\",\r\n            label: \"是否超时\",\r\n            type: \"select\",\r\n            options: [\r\n              {\r\n                label: \"是\",\r\n                value: true,\r\n              },\r\n              {\r\n                label: \"否\",\r\n                value: false,\r\n              },\r\n            ],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n        ],\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData(\r\n                  this.ruleForm,\r\n                  \"在园车俩\",\r\n                  VBInParkVehiclesExportData\r\n                );\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: \"100%\",\r\n        tableColumns: [\r\n          // {\r\n          //   width: 50,\r\n          //   otherOptions: {\r\n          //     type: 'selection',\r\n          //     align: 'center'\r\n          //   }\r\n          // },\r\n          {\r\n            label: \"驶入时间\",\r\n            key: \"PassTime\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"车牌号码\",\r\n            key: \"Number\",\r\n            otherOptions: {\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"车辆类型\",\r\n            key: \"VehicleType\",\r\n          },\r\n          {\r\n            label: \"车主姓名\",\r\n            key: \"UserName\",\r\n          },\r\n          {\r\n            label: \"车主联系方式\",\r\n            key: \"UserPhone\",\r\n          },\r\n          {\r\n            label: \"访问类型\",\r\n            key: \"AccessType\",\r\n          },\r\n          {\r\n            label: \"出入口\",\r\n            key: \"EntranceName\",\r\n          },\r\n          {\r\n            label: \"入园时长\",\r\n            key: \"ParkTime\",\r\n          },\r\n          {\r\n            label: \"超时时长\",\r\n            key: \"Timeout\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActionsWidth: 120,\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.$router.push({\r\n                name: \"vehiclesInTheParkView\",\r\n                query: { pg_redirect: this.$route.name, Id: row.Id },\r\n              });\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"车辆出园\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleLeave(row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/view\",\r\n          hidden: true,\r\n          component: () => import(\"./dialog/view.vue\"),\r\n          meta: { title: \"在园车辆详情\" },\r\n          name: \"vehiclesInTheParkView\",\r\n        },\r\n      ],\r\n      inParkStatistics: {},\r\n\r\n      AccessPrecent: 0,\r\n      TimeoutPrecent: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.vBPassRecordGetDropList();\r\n    // this.init();\r\n    // this.vBInParkVehiclesGetInParkStatistics();\r\n  },\r\n  async mounted() {\r\n    // 跳转设置默认参数\r\n    let JumpParams = this.$qiankun.getMicroAppJumpParamsFn();\r\n    if (JumpParams.isJump == \"true\") {\r\n      this.ruleForm.Timeout = JumpParams.Timeout == \"true\";\r\n    }\r\n    this.onFresh();\r\n  },\r\n  beforeDestroy() {\r\n    this.$qiankun.setMicroAppJumpParamsFn();\r\n    this.ruleForm.Timeout = null;\r\n  },\r\n  methods: {\r\n    async vBInParkVehiclesGetInParkStatistics() {\r\n      if (this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null;\r\n        this.ruleForm.EndTime = null;\r\n      }\r\n      let res = await VBInParkVehiclesGetInParkStatistics({\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.inParkStatistics = res.Data;\r\n        let AccessTotal = res.Data.Access.reduce(\r\n          (accumulator, currentValue) =>\r\n            accumulator + parseInt(currentValue.Value),\r\n          0\r\n        );\r\n        let TimeoutTotal = res.Data.Timeout.reduce(\r\n          (accumulator, currentValue) =>\r\n            accumulator + parseInt(currentValue.Value),\r\n          0\r\n        );\r\n        // let VehiclesTypeTotal = res.Data.VehiclesType.map(\r\n        //   (obj) => obj.value\r\n        // ).reduce(\r\n        //   (accumulator, currentValue) => accumulator + currentValue.Value,\r\n        //   0\r\n        // );\r\n        this.inParkStatistics.Access = res.Data.Access;\r\n        this.inParkStatistics.Timeout = res.Data.Timeout;\r\n        this.inParkStatistics.VehiclesType = res.Data.VehiclesType.map(\r\n          (item) => ({\r\n            value: item.Value,\r\n            name: item.Key,\r\n          })\r\n        );\r\n        this.pieOptionOption.series[0].data =\r\n          this.inParkStatistics.VehiclesType;\r\n\r\n        this.AccessPrecent =\r\n          (Number(res.Data.Access[0].Value) / AccessTotal) * 100;\r\n        this.TimeoutPrecent =\r\n          (Number(res.Data.Timeout[0].Value) / TimeoutTotal) * 100;\r\n        if (AccessTotal == 0) {\r\n          this.AccessPrecent = 0;\r\n        }\r\n        if (TimeoutTotal == 0) {\r\n          this.TimeoutPrecent = 0;\r\n        }\r\n      }\r\n    },\r\n    async vBPassRecordGetDropList() {\r\n      let res = await VBPassRecordGetDropList({});\r\n      if (res.IsSucceed) {\r\n        this.customForm.formItems.find((v) => v.key == \"VehicleType\").options =\r\n          res.Data.find((v) => v.Name == \"VehicleType\").List.map((item) => ({\r\n            label: item.Key,\r\n            value: item.Value,\r\n          }));\r\n        this.customForm.formItems.find((v) => v.key == \"AccessType\").options =\r\n          res.Data.find((v) => v.Name == \"AccessType\").List.map((item) => ({\r\n            label: item.Key,\r\n            value: item.Value,\r\n          }));\r\n      }\r\n    },\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n      this.vBInParkVehiclesGetInParkStatistics();\r\n    },\r\n    async init() {\r\n      await this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      if (this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null;\r\n        this.ruleForm.EndTime = null;\r\n      }\r\n      const res = await VBInParkVehiclesGetInParkVehiclesList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.Total;\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.onFresh();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.onFresh();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleLeave(row) {\r\n      this.$confirm(\r\n        '<div><div>是否确认进行车辆出园</div><div style=\"font-size:12px;color:#aaa\">车辆出园操作将标识该车为出园状态</div></div>',\r\n        \"操作确认\",\r\n        {\r\n          type: \"warning\",\r\n          dangerouslyUseHTMLString: true,\r\n        }\r\n      )\r\n        .then(async (_) => {\r\n          const res = await VBInParkVehiclesVehicleLeaving({\r\n            Id: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.$message.success(\"操作成功\");\r\n            this.init();\r\n          } else {\r\n            this.$message.error(res.Message);\r\n          }\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n\r\n.imgwapper {\r\n  width: 100px;\r\n  height: 100px;\r\n}\r\n.empty-img {\r\n  text-align: center;\r\n}\r\n.progressBox {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-around;\r\n  padding: 20px 0px;\r\n  .pieChartDom {\r\n    width: 126px;\r\n    height: 126px;\r\n  }\r\n  .progressTwo {\r\n    height: 126px;\r\n    display: flex;\r\n    flex-direction: row;\r\n    .left {\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      .title {\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n      }\r\n      .info {\r\n        display: flex;\r\n        flex-direction: column;\r\n        .num {\r\n          color: #aaa;\r\n          margin-bottom: 10px;\r\n        }\r\n        .text {\r\n          color: #aaa;\r\n        }\r\n        .colorActive {\r\n          color: #298dff;\r\n        }\r\n        .textActive {\r\n          font-size: 28px;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}