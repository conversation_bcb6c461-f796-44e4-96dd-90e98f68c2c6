{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\smartPark\\leadershipCockpit\\index.vue?vue&type=style&index=0&id=0da38ad8&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\smartPark\\leadershipCockpit\\index.vue", "mtime": 1755506574434}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLm10MjAgew0KICBtYXJnaW4tdG9wOiAxMHB4Ow0KfQ0KLmxheW91dHsNCiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gOTBweCk7DQogIG92ZXJmbG93OiBhdXRvOw0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqVA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/smartPark/leadershipCockpit", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        >\r\n\r\n          <template #customBtn=\"{slotScope}\">\r\n            <el-button v-if=\"slotScope.Status !== 2\" type=\"text\" @click=\"handleCreate(slotScope)\">导入表单</el-button>\r\n            <el-button v-if=\"slotScope.Status == 1\" type=\"text\" @click=\"withDrawOrSubmit(slotScope,1)\">提交</el-button>\r\n            <el-button v-if=\"slotScope.Status !== 0\" type=\"text\" @click=\"handleTemplateDown(slotScope)\">下载</el-button>\r\n            <el-button type=\"text\" @click=\"handelHistory(slotScope)\">历史记录</el-button>\r\n            <el-button v-if=\"slotScope.Status == 2\" type=\"text\" @click=\"withDrawOrSubmit(slotScope,2)\">撤回</el-button>\r\n          </template></CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\" top=\"6vh\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { parseTime } from '@/utils'\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport DialogForm from './dialogForm.vue'\r\nimport history from './history.vue'\r\nimport { combineURL } from '@/utils'\r\nimport {\r\n  GetPageList,\r\n  OperateData,\r\n  DownloadDataAsync\r\n} from '@/api/business/smartPark'\r\nexport default {\r\n  name: 'LeadershipCockpit',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n    history,\r\n    DialogForm\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        },\r\n\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Status: null,\r\n        StartDate: null,\r\n        EndDate: null,\r\n        data: []\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Status', // 字段ID\r\n            label: '状�?, // Form的label\r\n            type: 'select', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            placeholder: '请选择',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            options: [\r\n              {\r\n                value: 0,\r\n                label: '未导�?\r\n              },\r\n              {\r\n                value: 1,\r\n                label: '草稿'\r\n              },\r\n              {\r\n                value: 2,\r\n                label: '正式'\r\n              }\r\n            ],\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'data',\r\n            label: '时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              type: 'monthrange',\r\n              rangeSeparator: '�?,\r\n              startPlaceholder: '开始日�?,\r\n              endPlaceholder: '结束日期',\r\n              clearable: true,\r\n              valueFormat: 'yyyy-MM'\r\n            },\r\n            change: (e) => {\r\n              this.ruleForm.StartDate = e[0]\r\n              this.ruleForm.EndDate = e[1]\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList:[]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 60,\r\n            label: '序号',\r\n            otherOptions: {\r\n              type: 'index',\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '报表统计月份',\r\n            key: 'Title'\r\n          },\r\n          {\r\n            label: '状�?,\r\n            key: 'StatusName'\r\n          },\r\n          {\r\n            label: '操作�?,\r\n            key: 'Operator'\r\n          },\r\n          {\r\n            label: '操作时间',\r\n            key: 'OperateTime'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '',\r\n            otherOptions: {\r\n              type: 'text',\r\n              disabled: false\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleView(index, row)\r\n            }\r\n          }\r\n\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  created() {\r\n    // this.getBaseData()\r\n    this.init()\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getPageList()\r\n    },\r\n    init() {\r\n      this.getPageList()\r\n    },\r\n    async getPageList() {\r\n      if (this.ruleForm.data.length == 0) {\r\n        this.ruleForm.StartDate = null\r\n        this.ruleForm.EndDate = null\r\n      }\r\n      const res = await GetPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        this.customTableConfig.total = res.Data.Total\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    handleCreate(slotScope) {\r\n      this.dialogTitle = '导入报表'\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'DialogForm'\r\n      this.$nextTick(() => {\r\n        this.$refs.currentComponent.init(slotScope)\r\n      })\r\n    },\r\n    // handleView(index, row) {\r\n    //   // console.log(index, row)\r\n    //   // 环境判断\r\n    //   if (process.env.NODE_ENV === 'development') {\r\n    //     console.log('开发环�?)\r\n    //     window.open('http://wnpzgc-dev.bimtk.com/cockpit', '_blank')\r\n    //   } else {\r\n    //     console.log('生产环境')\r\n    //     window.open('http://wnpzgc-test.bimtk.com/cockpit', '_blank')\r\n    //   }\r\n    // },\r\n    async handleTemplateDown(row) {\r\n      if (row.Url) {\r\n        DownloadDataAsync({ url: row.Url }).then((res) => {\r\n          const url = window.URL.createObjectURL(\r\n            new Blob([res], {\r\n              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\r\n            })\r\n          )\r\n          const link = document.createElement('a')\r\n          link.style.display = 'none'\r\n          link.href = url\r\n          // 文件�?\n          link.setAttribute('download', '驾驶舱报�?xlsx')\r\n          document.body.appendChild(link)\r\n          link.click()\r\n        })\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: '暂无数据'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 撤回/提交\r\n    withDrawOrSubmit(row, type) {\r\n      this.$confirm(`${type === 2 ? '数据撤回会，当月数据将不会在驾驶舱大屏上展示，请确认，是否继�?' : '提交选中的数据，是否继续�?} `, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        OperateData({\r\n          Id: row.Id,\r\n          Type: type\r\n        }).then((res) => {\r\n          console.log(res)\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: `${type === 2 ? '撤回成功!' : '提交成功!'}`\r\n            })\r\n            this.onFresh()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删�?\r\n        })\r\n      })\r\n    },\r\n\r\n    // 历史记录\r\n    handelHistory(row) {\r\n      this.dialogTitle = '历史记录'\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'history'\r\n      this.$nextTick(() => {\r\n        this.$refs.currentComponent.init(row)\r\n      })\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n\r\n"]}]}