{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\pjEquipmentAssetList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\pjEquipmentAssetList\\index.vue", "mtime": 1755735448748}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/equipmentManagement/pjEquipmentAssetList", "sourcesContent": ["<template>\n  <div class=\"app-container abs100 pjEquipmentAssetList\">\n    <custom-layout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"submitForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          ref=\"table1\"\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        />\n      </template>\n    </custom-layout>\n\n    <el-dialog\n      v-dialogDrag\n      width=\"30%\"\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogVisible\"\n    >\n      <component\n        :is=\"currentComponent\"\n        ref=\"content\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport addRouterPage from '@/mixins/add-router-page'\nimport {\n  DeleteEquipmentAssetEntity,\n  ExportEquipmentAssetsList,\n  GetEquipmentAssetPageList,\n  AssetImportTemplatePJ,\n  AssetEquipmentImportPJ,\n  ExportEquipmentListPJ,\n  GetEquipmentAssetPageListPJ,\n  GetDictionaryDetailListByParentId,\n  ExportEquipCardInfo\n} from '@/api/business/eqptAsset'\nimport { GetGridByCode } from '@/api/sys'\nimport { timeFormat } from '@/filters'\nimport { getDictionary } from '@/utils/common'\nimport { downloadFile, downloadFileOnNewTag } from '@/utils/downloadFile'\nimport Print from './components/print.vue'\nimport importDialog from './components/import.vue'\nimport exportInfo from '@/views/business/energyManagement/mixins/export.js'\nexport default {\n  name: 'EquipmentAssetList',\n  components: { CustomTable, CustomLayout, CustomForm, Print, importDialog },\n  mixins: [addRouterPage, exportInfo],\n  data() {\n    return {\n      componentsConfig: {\n        interfaceName: AssetEquipmentImportPJ\n      },\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true\n        },\n        close: () => {\n          this.dialogVisible = false\n          this.fetchData()\n        }\n      },\n      addPageArray: [\n        {\n          path: this.$route.path + '/add',\n          hidden: true,\n          component: () => import('./add.vue'),\n          name: 'EquipmentAssetListAdd',\n          meta: { title: `新增` }\n        },\n        {\n          path: this.$route.path + '/edit',\n          hidden: true,\n          component: () => import('./add.vue'),\n          name: 'EquipmentAssetListEdit',\n          meta: { title: `编辑` }\n        },\n        {\n          path: this.$route.path + '/view',\n          hidden: true,\n          component: () => import('./add.vue'),\n          name: 'EquipmentAssetListView',\n          meta: { title: `查看` }\n        },\n        {\n          path: this.$route.path + '/dataAcquisition',\n          hidden: true,\n          component: () => import('./dataAcquisition.vue'),\n          name: 'DataAcquisition',\n          meta: { title: `查看数据` }\n        },\n        {\n          path: this.$route.path + '/equipmentData',\n          hidden: true,\n          component: () => import('./equipmentData.vue'),\n          name: 'PJEquipmentData',\n          meta: { title: `设备数采` }\n        }\n      ],\n      ruleForm: {\n        EquipmentName: '',\n        departName: '',\n        EquipmentType: '',\n        EquipmentItemType: ''\n      },\n      customForm: {\n        formItems: [\n          {\n            key: 'EquipmentName',\n            label: '设备名称',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'EquipmentType',\n            label: '设备类型',\n            type: 'select',\n            options: [],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              this.customForm.formItems.find(\n                (v) => v.key === 'EquipmentItemType'\n              ).options = []\n              this.ruleForm.EquipmentItemType = ''\n              GetDictionaryDetailListByParentId(e).then((res) => {\n                this.customForm.formItems.find(\n                  (v) => v.key === 'EquipmentItemType'\n                ).options = res.Data.map((v) => {\n                  return {\n                    label: v.Display_Name,\n                    value: v.Id\n                  }\n                })\n              })\n            }\n          },\n          {\n            key: 'EquipmentItemType',\n            label: '设备子类',\n            type: 'select',\n            options: [],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'departName',\n            label: '所属部门',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              console.log(e)\n            }\n          }\n        ],\n        rules: {},\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        // 表格\n        // rowKey: 'Id', // 唯一标识 用于保持选中状态\n        pageSizeOptions: [20, 40, 60, 80, 100],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        tableColumns: [\n          {\n            width: '55',\n            otherOptions: {\n              type: 'selection',\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '设备名称',\n            key: 'Display_Name',\n            otherOptions: {\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '设备SN',\n            key: 'Serial_Number',\n            otherOptions: {\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '设备编号',\n            key: 'Device_Number',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '品牌',\n            key: 'Brand',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '规格型号',\n            key: 'Spec',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '设备类型',\n            key: 'Type_Name',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '设备子类',\n            key: 'Type_Detail_Name',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '厂家名称',\n            key: 'Manufacturer',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '厂家联系方式',\n            key: 'Manufacturer_Contact_Info',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '经销商',\n            key: 'Dealer',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '经销商联系方式',\n            key: 'Dealer_Contact_Info',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '工程师',\n            key: 'Engineer',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '工程师联系方式',\n            key: 'Engineer_Contact_Info',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '所属部门',\n            key: 'Department',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '安装位置',\n            key: 'Position',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '安装时间',\n            key: 'Install_Date',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '设备管理员',\n            key: 'Administrator',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '设备管理员联系方式',\n            key: 'Administrator_Contact_Info',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '用途',\n            key: 'Usage',\n            otherOptions: {\n              align: 'center'\n            }\n          }\n        ],\n        tableData: [],\n        operateOptions: {\n          align: 'center',\n          width: '200px'\n        },\n        buttonConfig: {\n          buttonList: [\n            {\n              text: '新增',\n              round: false, // 是否圆角\n              plain: false, // 是否朴素\n              circle: false, // 是否圆形\n              loading: false, // 是否加载中\n              disabled: false, // 是否禁用\n              icon: '', //  图标\n              autofocus: false, // 是否聚焦\n              type: 'primary', // primary / success / warning / danger / info / text\n              size: 'small', // medium / small / mini\n              onclick: (item) => {\n                console.log(item)\n                this.handleCreate()\n              }\n            },\n            {\n              text: '下载模板',\n              disabled: false, // 是否禁用\n              onclick: (item) => {\n                console.log(item)\n                this.handleDownTemplate()\n              }\n            },\n            {\n              text: '批量导入',\n              disabled: false, // 是否禁用\n              onclick: (item) => {\n                console.log(item)\n                this.currentComponent = 'importDialog'\n                this.dialogVisible = true\n                this.dialogTitle = '批量导入'\n              }\n            },\n            {\n              text: '批量导出',\n              disabled: false,\n              onclick: () => {\n                this.handleExport()\n              }\n            },\n            {\n              text: '批量打印',\n              disabled: false,\n              loading: false,\n              onclick: () => {\n                this.handlePrint()\n              }\n            },\n            {\n              text: '确定',\n              type: 'primary',\n              disabled: false,\n              onclick: () => {\n                this.handleAssocia()\n              }\n            }\n          ]\n        },\n        tableActionsWidth: 180,\n        tableActions: [\n          {\n            actionLabel: '编辑',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.handleEdit(row.Id)\n            }\n          },\n          {\n            actionLabel: '删除',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.handleDelete(row.Id)\n            }\n          },\n          // {\n          //   actionLabel: \"打印二维码\",\n          //   otherOptions: {\n          //     type: \"text\",\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handlePrintQr(row.Id);\n          //   },\n          // },\n          {\n            actionLabel: '查看详情',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.handleInfo(row.Id)\n            }\n          }\n          // {\n          //   actionLabel: \"查看数据\",\n          //   otherOptions: {\n          //     type: \"text\",\n          //   },\n          //   onclick: (index, row) => {\n          //     this.viewData(row);\n          //   },\n          // },\n        ]\n      },\n      multipleSelection: [],\n      currentComponent: 'Print',\n      dialogVisible: false,\n      associa: false // 是否关联\n    }\n  },\n  watch: {\n    'multipleSelection.length': {\n      handler(newValue) {\n        this.customTableConfig.buttonConfig.buttonList.find(\n          (item) => item.text === '确定'\n        ).disabled = !newValue\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.associa = this.$route.query.associa === 'true'\n    if (this.associa) {\n      this.customTableConfig.buttonConfig.buttonList = this.customTableConfig.buttonConfig.buttonList.filter(v => v.text === '确定')\n    } else {\n      this.customTableConfig.buttonConfig.buttonList = this.customTableConfig.buttonConfig.buttonList.filter(v => v.text !== '确定')\n    }\n  },\n  mounted() {\n    // this.getGridByCode(\"EquipmentAssetList\");\n    this.fetchData()\n    getDictionary('deviceType').then((res) => {\n      const item = this.customForm.formItems.find(\n        (v) => v.key === 'EquipmentType'\n      )\n      console.log('res', res, item)\n      item.options = res.map((v) => {\n        return {\n          label: v.Display_Name,\n          value: v.Id\n        }\n      })\n    })\n  },\n  activated() {\n    this.fetchData()\n  },\n  methods: {\n    resetForm() {\n      this.ruleForm = {}\n      this.customForm.formItems.find(\n        (v) => v.key === 'EquipmentItemType'\n      ).options = []\n      this.fetchData()\n      this.$refs.table1.setClearSelection()\n    },\n    submitForm() {\n      this.customTableConfig.currentPage = 1\n      this.fetchData()\n      this.$refs.table1.setClearSelection()\n    },\n    fetchData() {\n      GetEquipmentAssetPageListPJ({\n        Display_Name: this.ruleForm.EquipmentName,\n        Device_Type_Id: this.ruleForm.EquipmentType,\n        Device_Type_Detail_Id: this.ruleForm.EquipmentItemType,\n        Department: this.ruleForm.departName,\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.customTableConfig.tableData = res.Data.Data.map((v) => {\n            v.Install_Date = timeFormat(v.Install_Date)\n            return v\n          })\n          this.customTableConfig.total = res.Data.TotalCount\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    handleCreate() {\n      // this.dialogTitle = '新增'\n      // this.dialogVisible = true\n      this.$router.push({\n        name: 'EquipmentAssetListAdd',\n        query: { pg_redirect: this.$route.name, type: 1 }\n      })\n    },\n    handleSizeChange(val) {\n      this.customTableConfig.pageSize = val\n      this.fetchData({\n        Page: this.customTableConfig.currentPage,\n        PageSize: val\n      })\n    },\n    handleCurrentChange(val) {\n      this.customTableConfig.currentPage = val\n      this.fetchData({ Page: val, PageSize: this.customTableConfig.pageSize })\n    },\n    handleSelectionChange(data) {\n      console.log(data)\n      this.multipleSelection = data\n    },\n    // handleExport() {\n    //   console.log('handleExport')\n    //   ExportEquipmentAssetsList({\n    //     ids: this.multipleSelection.map(v => v.Id)\n    //   }).then(res => {\n    //     if (res.IsSucceed) {\n    //       downloadFile(res.Data)\n    //     } else {\n    //       this.$message({\n    //         message: res.Message,\n    //         type: 'error'\n    //       })\n    //     }\n    //   })\n    // },\n    // v2 导出设备资产列表\n    handleExport() {\n      console.log('handleExport')\n      ExportEquipmentListPJ({\n        Id: this.multipleSelection.map((v) => v.Id).toString(),\n        Display_Name: this.ruleForm.EquipmentName,\n        Device_Type_Id: this.ruleForm.EquipmentType,\n        Department: this.ruleForm.departName,\n        Device_Type_Detail_Id: this.ruleForm.EquipmentItemType\n      }).then((res) => {\n        if (res.IsSucceed) {\n          downloadFile(res.Data)\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    async handlePrint() {\n      this.customTableConfig.buttonConfig.buttonList[4].loading = true\n      const res = await ExportEquipCardInfo({\n        Display_Name: this.ruleForm.EquipmentName,\n        Device_Type_Id: this.ruleForm.EquipmentType,\n        Device_Type_Detail_Id: this.ruleForm.EquipmentItemType,\n        Department: this.ruleForm.departName,\n        Ids: this.multipleSelection.map((v) => v.Id)\n      })\n      if (res.IsSucceed) {\n        downloadFileOnNewTag(res.Data)\n      } else {\n        this.$message({\n          message: res.Message,\n          type: 'error'\n        })\n      }\n      this.customTableConfig.buttonConfig.buttonList[4].loading = false\n    },\n    handleDelete(id) {\n      this.$confirm('是否删除该设备, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteEquipmentAssetEntity({ ids: [id] }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.fetchData()\n            } else {\n              this.$message({\n                message: res.Message,\n                type: 'error'\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    handleEdit(id) {\n      this.$router.push({\n        name: 'EquipmentAssetListEdit',\n        query: { pg_redirect: this.$route.name, id, type: 2 }\n      })\n    },\n    handlePrintQr(v) {\n      this.dialogVisible = true\n      this.dialogTitle = '设备二维码'\n      this.$nextTick((_) => {\n        this.$refs['content'].setCode(v)\n      })\n    },\n    handleInfo(id) {\n      this.$router.push({\n        name: 'EquipmentAssetListView',\n        query: { pg_redirect: this.$route.name, id, type: 3 }\n      })\n    },\n    // getGridByCode(code) {\n    //   GetGridByCode({ code }).then((res) => {\n    //     console.log(res.Data);\n    //     if (res.IsSucceed) {\n    //       const Grid = res.Data.Grid;\n    //       this.customTableConfig.tableColumns = res.Data?.ColumnList.map(\n    //         (item) => {\n    //           return Object.assign(\n    //             {},\n    //             {\n    //               key: item.Code,\n    //               label: item.Display_Name,\n    //               width: item.Width,\n    //               otherOptions: {\n    //                 align: item.Align ? item.Align : \"center\",\n    //                 sortable: item.Is_Sort,\n    //                 fixed: item.Is_Frozen === false ? false : \"left\",\n    //                 Digit_Number: item.Digit_Number,\n    //               },\n    //             }\n    //           );\n    //         }\n    //       );\n    //       if (Grid.Is_Select) {\n    //         this.customTableConfig.tableColumns.unshift({\n    //           otherOptions: {\n    //             type: \"selection\",\n    //             align: \"center\",\n    //           },\n    //         });\n    //       }\n    //       this.customTableConfig.pageSize = Number(Grid.Row_Number);\n    //     }\n    //   });\n    // },\n    // 查看数据\n    viewData(data) {\n      data.num = 1\n      data.historyRouter = this.$route.name\n      this.$router.push({\n        name: 'PJEquipmentData',\n        query: { pg_redirect: this.$route.name, data }\n      })\n\n      this.$store.dispatch('eqpt/changeEqptData', data)\n    },\n    // 下载模板\n    handleDownTemplate() {\n      AssetImportTemplatePJ({}).then((res) => {\n        if (res.IsSucceed) {\n          downloadFile(res.Data, '设备资产导入模板')\n        } else {\n          this.$message.error(res.Message)\n        }\n      })\n    },\n    // 处理关联设备数据 待开发\n    handleAssocia() {\n      console.log('multipleSelection', this.multipleSelection)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.pjEquipmentAssetList {\n  /* height: calc(100vh - 90px); */\n  overflow: hidden;\n}\n</style>\n"]}]}