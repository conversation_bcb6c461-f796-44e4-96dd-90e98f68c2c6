{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\workshopBulletinBoard\\ProductionConfiguration\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\workshopBulletinBoard\\ProductionConfiguration\\index.vue", "mtime": 1755506574564}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/business/workshopBulletinBoard/ProductionConfiguration", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        />\n      </template>\n    </CustomLayout>\n    <el-dialog\n      v-dialogDrag\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogVisible\"\n      custom-class=\"cs-el-dialog\"\n      top=\"10vh\"\n    >\n      <component\n        :is=\"currentComponent\"\n        ref=\"currentComponent\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n      />\n    </el-dialog>\n    <!-- 导入弹窗 -->\n    <!-- <dialogImport ref=\"dialogImport\" /> -->\n  </div>\n</template>\n\n<script>\nimport { parseTime } from \"@/utils\";\n// import { baseUrl } from '@/utils/baseurl'\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\nimport DialogForm from \"./components/dialogTable.vue\";\nimport dialogImport from \"./components/dialogImport.vue\";\n// import { downloadFile } from '@/utils/downloadFile'\nimport { GetPreferenceSettingValue } from \"@/api/sys/system-setting\";\nimport { GetGridByCode } from \"@/api/sys\";\nimport { RoleAuthorization } from \"@/api/user\";\nimport addRouterPage from \"@/mixins/add-router-page\";\nimport {\n  GetPageList,\n  DeleteEntity,\n} from \"@/api/business/productionConfiguration\";\n// import { divide } from 'xe-utils'\nexport default {\n  name: \"ProductionConfiguration\",\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout,\n  },\n  mixins: [addRouterPage],\n  data() {\n    return {\n      addPageArray: [\n        {\n          path: this.$route.path + \"/add\",\n          hidden: true,\n          component: () => import(\"./components/add.vue\"),\n          name: \"ProductionConfigurationAdd\",\n          meta: { title: `新增` },\n        },\n        {\n          path: this.$route.path + \"/edit\",\n          hidden: true,\n          component: () => import(\"./components/add.vue\"),\n          name: \"ProductionConfigurationEdit\",\n          meta: { title: `编辑` },\n        },\n        {\n          path: this.$route.path + \"/view\",\n          hidden: true,\n          component: () => import(\"./components/add.vue\"),\n          name: \"ProductionConfigurationView\",\n          meta: { title: `查看` },\n        },\n      ],\n      currentComponent: DialogForm,\n      componentsConfig: {},\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true;\n        },\n        close: () => {\n          this.dialogVisible = false;\n          this.onFresh();\n        },\n      },\n      dialogVisible: false,\n      dialogTitle: \"\",\n      tableSelection: [],\n\n      ruleForm: {\n        Board_Name: \"\",\n      },\n      customForm: {\n        formItems: [\n          {\n            key: \"Board_Name\", // 字段ID\n            label: \"看板名称\", // Form的label\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?            placeholder: \"请输入看�?,\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n            },\n            change: (e) => {\n              // change事件\n              console.log(e);\n            },\n          },\n        ],\n        rules: {\n          // 请参照elementForm rules\n        },\n        customFormButtons: {\n          submitName: \"查询\",\n          resetName: \"重置\",\n        },\n      },\n      customTableConfig: {\n        buttonConfig: {\n          buttonList: [\n            {\n              text: \"新增\",\n              round: false, // 是否圆角\n              plain: false, // 是否朴素\n              circle: false, // 是否圆形\n              loading: false, // 是否加载�?              disabled: false, // 是否禁用\n              icon: \"\", //  图标\n              autofocus: false, // 是否聚焦\n              type: \"primary\", // primary / success / warning / danger / info / text\n              size: \"small\", // medium / small / mini\n              onclick: (item) => {\n                console.log(item);\n                this.handleCreate();\n              },\n            },\n          ],\n        },\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        tableColumns: [\n          {\n            width: 60,\n            label: \"序号\",\n            otherOptions: {\n              type: \"index\",\n              align: \"center\",\n            },\n          },\n          //   {\n          //     label: '报表编码',\n          //     key: 'Code'\n          //   },\n          //   {\n          //     label: '报表名称',\n          //     key: 'Name'\n          //   },\n          //   {\n          //     label: '报表创建时间',\n          //     key: 'Create_Date'\n          //   },\n          //   {\n          //     label: '报表创建�?,\n          //     key: 'Create_UserName'\n          //   }\n        ],\n        tableData: [],\n        tableActionsWidth: 240,\n        tableActions: [\n          {\n            actionLabel: \"查看\",\n            otherOptions: {\n              type: \"text\",\n              disabled: false,\n            },\n            onclick: (index, row) => {\n              this.handleView2(index, row);\n            },\n          },\n          {\n            actionLabel: \"编辑\",\n            otherOptions: {\n              type: \"text\",\n              disabled: false,\n            },\n            onclick: (index, row) => {\n              this.handleEdit(index, row);\n            },\n          },\n          {\n            actionLabel: \"删除\",\n            otherOptions: {\n              type: \"text\",\n              disabled: false,\n            },\n            onclick: (index, row) => {\n              this.handleDelete(index, row);\n            },\n          },\n          {\n            actionLabel: \"复制链接\",\n            otherOptions: {\n              type: \"text\",\n              disabled: false,\n            },\n            onclick: (index, row) => {\n              this.handleCopy(index, row);\n            },\n          },\n          {\n            actionLabel: \"导入数据\",\n            otherOptions: {\n              type: \"text\",\n              disabled: false,\n            },\n            onclick: (index, row) => {\n              this.handleImport(index, row);\n            },\n          },\n        ],\n        otherOptions: {},\n      },\n      roleAuthorizationList: [],\n      Big_Screen_Url: \"\",\n    };\n  },\n  computed: {},\n  created() {\n    this.getBaseData();\n    this.init();\n  },\n  activated() {\n    this.init();\n  },\n  methods: {\n    getBaseData() {\n      // 获取表格配置\n      GetGridByCode({ code: \"production_configuration_list\" }).then((res) => {\n        if (res.IsSucceed) {\n          const data = res.Data.ColumnList.map((item) => {\n            const temp = {\n              label: item.Display_Name,\n              key: item.Code,\n            };\n            if (item.Code === \"Ids\") {\n              temp.render = (row) => {\n                return this.$createElement(\n                  \"el-button\",\n                  {\n                    attrs: {\n                      type: \"text\",\n                    },\n                    on: {\n                      click: (val) => {\n                        this.handleView(val, row);\n                      },\n                    },\n                  },\n                  \"查看\"\n                );\n              };\n            }\n            return temp;\n          });\n          this.customTableConfig.tableColumns.push(...data);\n        } else {\n          this.$message({\n            type: \"error\",\n            message: res.Message,\n          });\n        }\n      });\n      /**\n     *  menuType: 2, //1PC 2app\n        roleType: 3, //1菜单权限�?列权�?�?按钮权限\n     */\n      RoleAuthorization({\n        workObjId: localStorage.getItem(\"Last_Working_Object_Id\"),\n        menuId: this.$route.meta.Id,\n        menuType: 1,\n        roleType: 3,\n        sign: 10,\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.roleAuthorizationList = res.Data;\n          this.customTableConfig.tableActions[1].otherOptions.disabled =\n            !this.roleAuthorizationList.find(\n              (item) => item.display_name === \"编辑\"\n            ).is_enabled;\n          this.customTableConfig.tableActions[2].otherOptions.disabled =\n            !this.roleAuthorizationList.find(\n              (item) => item.display_name === \"删除\"\n            ).is_enabled;\n          this.customTableConfig.tableActions[4].otherOptions.disabled =\n            !this.roleAuthorizationList.find(\n              (item) => item.display_name === \"导入数据\"\n            ).is_enabled;\n        } else {\n          this.$message({\n            type: \"error\",\n            message: res.Message,\n          });\n        }\n      });\n\n      GetPreferenceSettingValue({\n        Code: \"Big_screen\",\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.Big_Screen_Url = res.Data;\n        }\n      });\n    },\n    searchForm(data) {\n      console.log(data);\n      this.onFresh();\n    },\n    resetForm() {\n      this.onFresh();\n    },\n    onFresh() {\n      this.getPageList();\n    },\n    init() {\n      this.getPageList();\n    },\n    async getPageList() {\n      const res = await GetPageList({\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n        ...this.ruleForm,\n      });\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data.map((item) => {\n          item.Modify_Date = item.Modify_Date\n            ? parseTime(new Date(item.Modify_Date), \"{y}-{m}-{d} {h}:{i}:{s}\")\n            : \"\";\n          item.Create_Date = item.Create_Date\n            ? parseTime(new Date(item.Create_Date), \"{y}-{m}-{d} {h}:{i}:{s}\")\n            : \"\";\n          // item.Is_Push = item.Is_Push ? '�? : '�?\n          return item;\n        });\n        this.customTableConfig.total = res.Data.TotalCount;\n      } else {\n        this.$message({\n          type: \"error\",\n          message: res.Message,\n        });\n      }\n    },\n    // 新增\n    handleCreate() {\n      //   this.dialogTitle = '新增'\n      //   this.dialogVisible = true\n      this.$router.push({\n        name: \"ProductionConfigurationAdd\",\n        query: { pg_redirect: this.$route.name, type: 1 },\n      });\n      // this.$qiankun.switchMicroAppFn(\n      //   \"project\",\n      //   \"szdn\",\n      //   \"1cf6f8ac-d9d0-4b18-9959-5e4ef37886f4\",\n      //   `/business/workshop/productionConfiguration/add?pg_redirect=${this.$route.name}&type=1`\n      // );\n    },\n    // 查看绑定设备明细\n    handleView(index, row) {\n      console.log(index, row);\n      this.currentComponent = DialogForm;\n      this.dialogTitle = \"查看绑定设备明细\";\n      this.dialogVisible = true;\n      this.$nextTick(() => {\n        this.$refs.currentComponent.initView(row.Equipment_Ids);\n      });\n    },\n    // 查看看板\n    handleView2(index, row) {\n      window.open(\n        `${this.Big_Screen_Url}/productionBoard/index?id=${\n          row.Id\n        }&tenant=${localStorage.getItem(\"tenant\")}`,\n        \"_blank\"\n      );\n      // window.open(\n      //   `${process.env.VUE_APP_SCREEN_URL}/productionBoard/index?id=${\n      //     row.Id\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`,\n      //   \"_blank\"\n      // );\n      // if (process.env.NODE_ENV === \"development\") {\n      //   window.open(\n      //     `http://localhost:5173/productionBoard/index?id=${\n      //       row.Id\n      //     }&tenant=${localStorage.getItem(\"tenant\")}`,\n      //     \"_blank\"\n      //   );\n      // } else {\n      //   window.open(\n      //     `http://wnpzgc-test.bimtk.com/productionBoard/index?id=${\n      //       row.Id\n      //     }&tenant=${localStorage.getItem(\"tenant\")}`,\n      //     \"_blank\"\n      //   );\n      // }\n    },\n    // 编辑\n    handleEdit(index, row) {\n      console.log(index, row);\n      this.$router.push({\n        name: \"ProductionConfigurationAdd\",\n        query: { pg_redirect: this.$route.name, type: 2, id: row.Id },\n      });\n    },\n    // 复制链接\n    handleCopy(index, row) {\n      // console.log(\n      //   process.env.VUE_APP_SCREEN_URL,\n      //   \"process.env.VUE_APP_SCREEN_URL\"\n      // );\n      const textareaEle = document.createElement(\"textarea\");\n      // if (process.env.NODE_ENV === \"development\") {\n      //   textareaEle.value = `http://localhost:5173/productionBoard/index?id=${\n      //     row.Id\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`;\n      // } else {\n      //   textareaEle.value = `http://wnpzgc-test.bimtk.com/productionBoard/index?id=${\n      //     row.Id\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`;\n      // }\n      textareaEle.value = `${this.Big_Screen_Url}/productionBoard/index?id=${\n        row.Id\n      }&tenant=${localStorage.getItem(\"tenant\")}`;\n\n      document.body.appendChild(textareaEle);\n      textareaEle.select();\n      document.execCommand(\"copy\");\n      document.body.removeChild(textareaEle);\n      this.$message({\n        type: \"success\",\n        message: \"已成功复制到截切�?,\n      });\n    },\n    // 导入数据\n    handleImport(index, row) {\n      console.log(index, row);\n      this.currentComponent = dialogImport;\n      this.dialogTitle = \"导入数据\";\n      this.dialogVisible = true;\n      this.$nextTick(() => {\n        this.$refs.currentComponent.init(row);\n      });\n    },\n    // 删除\n    handleDelete(index, row) {\n      console.log(index, row);\n      this.$confirm(\"确认删除�?, {\n        type: \"warning\",\n      })\n        .then(async (_) => {\n          const res = await DeleteEntity({\n            id: row.Id,\n          });\n          if (res.IsSucceed) {\n            this.$message({\n              type: \"success\",\n              message: \"删除成功\",\n            });\n            this.init();\n          } else {\n            this.$message({\n              type: \"error\",\n              message: res.Message,\n            });\n          }\n        })\n        .catch((_) => {});\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`);\n      this.customTableConfig.pageSize = val;\n      this.init();\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`);\n      this.customTableConfig.currentPage = val;\n      this.init();\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection;\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.mt20 {\n  margin-top: 10px;\n}\n::v-deep .el-table__fixed-body-wrapper {\n  top: 40px !important;\n}\n.layout {\n  height: calc(100vh - 90px);\n  overflow: auto;\n}\n::v-deep .cs-el-dialog {\n  min-height: 300px !important;\n}\n</style>\n"]}]}