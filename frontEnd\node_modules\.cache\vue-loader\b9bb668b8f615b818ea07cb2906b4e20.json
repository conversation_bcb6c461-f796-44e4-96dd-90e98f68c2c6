{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\workshopBulletinBoard\\ProductionConfiguration\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\workshopBulletinBoard\\ProductionConfiguration\\index.vue", "mtime": 1755674552440}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/business/workshopBulletinBoard/ProductionConfiguration", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      custom-class=\"cs-el-dialog\"\r\n      top=\"10vh\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n    <!-- 导入弹窗 -->\r\n    <!-- <dialogImport ref=\"dialogImport\" /> -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { parseTime } from \"@/utils\";\r\n// import { baseUrl } from '@/utils/baseurl'\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport DialogForm from \"./components/dialogTable.vue\";\r\nimport dialogImport from \"./components/dialogImport.vue\";\r\n// import { downloadFile } from '@/utils/downloadFile'\r\nimport { GetPreferenceSettingValue } from \"@/api/sys/system-setting\";\r\nimport { GetGridByCode } from \"@/api/sys\";\r\nimport { RoleAuthorization } from \"@/api/user\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nimport {\r\n  GetPageList,\r\n  DeleteEntity,\r\n} from \"@/api/business/productionConfiguration\";\r\n// import { divide } from 'xe-utils'\r\nexport default {\r\n  name: \"ProductionConfiguration\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/add\",\r\n          hidden: true,\r\n          component: () => import(\"./components/add.vue\"),\r\n          name: \"ProductionConfigurationAdd\",\r\n          meta: { title: `新增` },\r\n        },\r\n        {\r\n          path: this.$route.path + \"/edit\",\r\n          hidden: true,\r\n          component: () => import(\"./components/add.vue\"),\r\n          name: \"ProductionConfigurationEdit\",\r\n          meta: { title: `编辑` },\r\n        },\r\n        {\r\n          path: this.$route.path + \"/view\",\r\n          hidden: true,\r\n          component: () => import(\"./components/add.vue\"),\r\n          name: \"ProductionConfigurationView\",\r\n          meta: { title: `查看` },\r\n        },\r\n      ],\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"\",\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Board_Name: \"\",\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Board_Name\", // 字段ID\r\n            label: \"看板名称\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            placeholder: \"请输入看板\",\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"新增\",\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: false, // 是否禁用\r\n              icon: \"\", //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 60,\r\n            label: \"序号\",\r\n            otherOptions: {\r\n              type: \"index\",\r\n              align: \"center\",\r\n            },\r\n          },\r\n          //   {\r\n          //     label: '报表编码',\r\n          //     key: 'Code'\r\n          //   },\r\n          //   {\r\n          //     label: '报表名称',\r\n          //     key: 'Name'\r\n          //   },\r\n          //   {\r\n          //     label: '报表创建时间',\r\n          //     key: 'Create_Date'\r\n          //   },\r\n          //   {\r\n          //     label: '报表创建人',\r\n          //     key: 'Create_UserName'\r\n          //   }\r\n        ],\r\n        tableData: [],\r\n        tableActionsWidth: 240,\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleView2(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"复制链接\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleCopy(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"导入数据\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleImport(index, row);\r\n            },\r\n          },\r\n        ],\r\n        otherOptions: {},\r\n      },\r\n      roleAuthorizationList: [],\r\n      Big_Screen_Url: \"\",\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.getBaseData();\r\n    this.init();\r\n  },\r\n  activated() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    getBaseData() {\r\n      // 获取表格配置\r\n      GetGridByCode({ code: \"production_configuration_list\" }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data.ColumnList.map((item) => {\r\n            const temp = {\r\n              label: item.Display_Name,\r\n              key: item.Code,\r\n            };\r\n            if (item.Code === \"Ids\") {\r\n              temp.render = (row) => {\r\n                return this.$createElement(\r\n                  \"el-button\",\r\n                  {\r\n                    attrs: {\r\n                      type: \"text\",\r\n                    },\r\n                    on: {\r\n                      click: (val) => {\r\n                        this.handleView(val, row);\r\n                      },\r\n                    },\r\n                  },\r\n                  \"查看\"\r\n                );\r\n              };\r\n            }\r\n            return temp;\r\n          });\r\n          this.customTableConfig.tableColumns.push(...data);\r\n        } else {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: res.Message,\r\n          });\r\n        }\r\n      });\r\n      /**\r\n     *  menuType: 2, //1PC 2app\r\n        roleType: 3, //1菜单权限，2列权限 ，3按钮权限\r\n     */\r\n      RoleAuthorization({\r\n        workObjId: localStorage.getItem(\"Last_Working_Object_Id\"),\r\n        menuId: this.$route.meta.Id,\r\n        menuType: 1,\r\n        roleType: 3,\r\n        sign: 10,\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.roleAuthorizationList = res.Data;\r\n          this.customTableConfig.tableActions[1].otherOptions.disabled =\r\n            !this.roleAuthorizationList.find(\r\n              (item) => item.display_name === \"编辑\"\r\n            ).is_enabled;\r\n          this.customTableConfig.tableActions[2].otherOptions.disabled =\r\n            !this.roleAuthorizationList.find(\r\n              (item) => item.display_name === \"删除\"\r\n            ).is_enabled;\r\n          this.customTableConfig.tableActions[4].otherOptions.disabled =\r\n            !this.roleAuthorizationList.find(\r\n              (item) => item.display_name === \"导入数据\"\r\n            ).is_enabled;\r\n        } else {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: res.Message,\r\n          });\r\n        }\r\n      });\r\n\r\n      GetPreferenceSettingValue({\r\n        Code: \"Big_screen\",\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.Big_Screen_Url = res.Data;\r\n        }\r\n      });\r\n    },\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.getPageList();\r\n    },\r\n    init() {\r\n      this.getPageList();\r\n    },\r\n    async getPageList() {\r\n      const res = await GetPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => {\r\n          item.Modify_Date = item.Modify_Date\r\n            ? parseTime(new Date(item.Modify_Date), \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n            : \"\";\r\n          item.Create_Date = item.Create_Date\r\n            ? parseTime(new Date(item.Create_Date), \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n            : \"\";\r\n          // item.Is_Push = item.Is_Push ? '是' : '否'\r\n          return item;\r\n        });\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n      } else {\r\n        this.$message({\r\n          type: \"error\",\r\n          message: res.Message,\r\n        });\r\n      }\r\n    },\r\n    // 新增\r\n    handleCreate() {\r\n      //   this.dialogTitle = '新增'\r\n      //   this.dialogVisible = true\r\n      this.$router.push({\r\n        name: \"ProductionConfigurationAdd\",\r\n        query: { pg_redirect: this.$route.name, type: 1 },\r\n      });\r\n      // this.$qiankun.switchMicroAppFn(\r\n      //   \"project\",\r\n      //   \"szdn\",\r\n      //   \"1cf6f8ac-d9d0-4b18-9959-5e4ef37886f4\",\r\n      //   `/business/workshop/productionConfiguration/add?pg_redirect=${this.$route.name}&type=1`\r\n      // );\r\n    },\r\n    // 查看绑定设备明细\r\n    handleView(index, row) {\r\n      console.log(index, row);\r\n      this.currentComponent = DialogForm;\r\n      this.dialogTitle = \"查看绑定设备明细\";\r\n      this.dialogVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.currentComponent.initView(row.Equipment_Ids);\r\n      });\r\n    },\r\n    // 查看看板\r\n    handleView2(index, row) {\r\n      window.open(\r\n        `${this.Big_Screen_Url}/productionBoard/index?id=${\r\n          row.Id\r\n        }&tenant=${localStorage.getItem(\"tenant\")}`,\r\n        \"_blank\"\r\n      );\r\n      // window.open(\r\n      //   `${process.env.VUE_APP_SCREEN_URL}/productionBoard/index?id=${\r\n      //     row.Id\r\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`,\r\n      //   \"_blank\"\r\n      // );\r\n      // if (process.env.NODE_ENV === \"development\") {\r\n      //   window.open(\r\n      //     `http://localhost:5173/productionBoard/index?id=${\r\n      //       row.Id\r\n      //     }&tenant=${localStorage.getItem(\"tenant\")}`,\r\n      //     \"_blank\"\r\n      //   );\r\n      // } else {\r\n      //   window.open(\r\n      //     `http://wnpzgc-test.bimtk.com/productionBoard/index?id=${\r\n      //       row.Id\r\n      //     }&tenant=${localStorage.getItem(\"tenant\")}`,\r\n      //     \"_blank\"\r\n      //   );\r\n      // }\r\n    },\r\n    // 编辑\r\n    handleEdit(index, row) {\r\n      console.log(index, row);\r\n      this.$router.push({\r\n        name: \"ProductionConfigurationAdd\",\r\n        query: { pg_redirect: this.$route.name, type: 2, id: row.Id },\r\n      });\r\n    },\r\n    // 复制链接\r\n    handleCopy(index, row) {\r\n      // console.log(\r\n      //   process.env.VUE_APP_SCREEN_URL,\r\n      //   \"process.env.VUE_APP_SCREEN_URL\"\r\n      // );\r\n      const textareaEle = document.createElement(\"textarea\");\r\n      // if (process.env.NODE_ENV === \"development\") {\r\n      //   textareaEle.value = `http://localhost:5173/productionBoard/index?id=${\r\n      //     row.Id\r\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`;\r\n      // } else {\r\n      //   textareaEle.value = `http://wnpzgc-test.bimtk.com/productionBoard/index?id=${\r\n      //     row.Id\r\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`;\r\n      // }\r\n      textareaEle.value = `${this.Big_Screen_Url}/productionBoard/index?id=${\r\n        row.Id\r\n      }&tenant=${localStorage.getItem(\"tenant\")}`;\r\n\r\n      document.body.appendChild(textareaEle);\r\n      textareaEle.select();\r\n      document.execCommand(\"copy\");\r\n      document.body.removeChild(textareaEle);\r\n      this.$message({\r\n        type: \"success\",\r\n        message: \"已成功复制到截切板\",\r\n      });\r\n    },\r\n    // 导入数据\r\n    handleImport(index, row) {\r\n      console.log(index, row);\r\n      this.currentComponent = dialogImport;\r\n      this.dialogTitle = \"导入数据\";\r\n      this.dialogVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.currentComponent.init(row);\r\n      });\r\n    },\r\n    // 删除\r\n    handleDelete(index, row) {\r\n      console.log(index, row);\r\n      this.$confirm(\"确认删除？\", {\r\n        type: \"warning\",\r\n      })\r\n        .then(async (_) => {\r\n          const res = await DeleteEntity({\r\n            id: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: \"success\",\r\n              message: \"删除成功\",\r\n            });\r\n            this.init();\r\n          } else {\r\n            this.$message({\r\n              type: \"error\",\r\n              message: res.Message,\r\n            });\r\n          }\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.init();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.init();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n::v-deep .el-table__fixed-body-wrapper {\r\n  top: 40px !important;\r\n}\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n::v-deep .cs-el-dialog {\r\n  min-height: 300px !important;\r\n}\r\n</style>\r\n"]}]}