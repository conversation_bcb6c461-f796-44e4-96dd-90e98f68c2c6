{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\myTasks\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\myTasks\\index.vue", "mtime": 1755674552430}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "GetTaskPageList", "GetTaskType", "GetTaskStatus", "name", "components", "data", "_this", "ruleForm", "Name", "Type", "StartTime", "EndTime", "Status", "Date", "taskTypeData", "customForm", "formItems", "key", "label", "type", "otherOptions", "clearable", "width", "change", "e", "console", "log", "options", "rangeSeparator", "startPlaceholder", "endPlaceholder", "valueFormat", "length", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "loading", "buttonConfig", "buttonList", "pageSizeOptions", "currentPage", "pageSize", "total", "height", "tableColumns", "tableData", "tableActions", "actionLabel", "onclick", "index", "row", "goHandelAlarm", "mounted", "getTaskType", "getTaskStatus", "onFresh", "methods", "_this2", "then", "res", "IsSucceed", "arr", "Data", "for<PERSON>ach", "item", "obj", "value", "Value", "push", "find", "v", "$message", "message", "Message", "_this3", "finally", "searchForm", "resetForm", "fetchData", "_this4", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "_objectSpread", "Page", "PageSize", "TotalCount", "stop", "Url", "platform", "ModuleName", "$qiankun", "switchMicroAppFn", "ModuleCode", "ModuleId", "code", "id", "url", "<PERSON><PERSON><PERSON>", "handleSizeChange", "val", "concat", "handleCurrentChange"], "sources": ["src/views/business/myWorkBench/myTasks/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n        >\r\n          <!-- <template #customBtn=\"{slotScope}\"><el-button type=\"text\" @click=\"goHandelAlarm(slotScope)\">去处理</el-button></template> -->\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport {\r\n  GetTaskPageList,\r\n  GetTaskType,\r\n  GetTaskStatus\r\n} from '@/api/business/myWorkBench'\r\nexport default {\r\n  name: 'MyTasks',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        Name: '',\r\n        Type: '',\r\n        StartTime: null,\r\n        EndTime: null,\r\n        Status: null,\r\n        Date: []\r\n      },\r\n      taskTypeData: [],\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Name',\r\n            label: '任务名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Type',\r\n            label: '任务类型',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [\r\n\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Status',\r\n            label: '任务状态',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Date',\r\n            label: '开始时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              type: 'datetimerange',\r\n              rangeSeparator: '至',\r\n              startPlaceholder: '开始日期',\r\n              endPlaceholder: '结束日期',\r\n              clearable: true,\r\n              valueFormat: 'yyyy-MM-dd HH:mm'\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n              if (e && e.length !== 0) {\r\n                this.ruleForm.StartTime = e[0]\r\n                this.ruleForm.EndTime = e[1]\r\n              } else {\r\n                this.ruleForm.StartTime = null\r\n                this.ruleForm.EndTime = null\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        loading: false,\r\n        buttonConfig: {\r\n          buttonList: [\r\n\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          {\r\n            label: '任务状态',\r\n            key: 'StatusName'\r\n          },\r\n          {\r\n            label: '任务类型',\r\n            key: 'Type'\r\n          },\r\n          {\r\n            label: '任务名称',\r\n            key: 'Name'\r\n          },\r\n          {\r\n            label: '来源',\r\n            key: 'Source'\r\n          },\r\n          {\r\n            label: '业务模块',\r\n            key: 'Module'\r\n          },\r\n          {\r\n            label: '任务开始时间',\r\n            key: 'StartTime'\r\n          },\r\n          {\r\n            label: '计划完成时间',\r\n            key: 'EndTime'\r\n          },\r\n          {\r\n            label: '实际完成时间',\r\n            key: 'DoneTime'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '去处理',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.goHandelAlarm(row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getTaskType()\r\n    this.getTaskStatus()\r\n    this.onFresh()\r\n  },\r\n  methods: {\r\n    // 获取任务类型\r\n    getTaskType() {\r\n      GetTaskType({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const arr = []\r\n          const data = res.Data || null\r\n          data.forEach((item) => {\r\n            const obj = {\r\n              label: item.Name,\r\n              value: item.Value\r\n            }\r\n            arr.push(obj)\r\n          })\r\n          this.customForm.formItems.find((v) => v.key == 'Type').options = arr\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 获取任务类型\r\n    getTaskStatus() {\r\n      this.customTableConfig.loading = true\r\n      GetTaskStatus({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const arr = []\r\n          const data = res.Data || null\r\n          data.forEach((item) => {\r\n            const obj = {\r\n              label: item.Name,\r\n              value: item.Value\r\n            }\r\n            arr.push(obj)\r\n          })\r\n          this.customForm.formItems.find((v) => v.key == 'Status').options = arr\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.customTableConfig.loading = false\r\n      })\r\n    },\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.fetchData()\r\n    },\r\n    async fetchData() {\r\n      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null\r\n        this.ruleForm.EndTime = null\r\n      }\r\n      await GetTaskPageList({\r\n        ...this.ruleForm,\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.tableData = res.Data.Data\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 去处理路由跳转\r\n    goHandelAlarm(item) {\r\n      // this.$router.push({ name: item.Code })\r\n      console.log(item, 'item')\r\n      if (item.Url) {\r\n        let platform = '' // 子应用\r\n        if (item.ModuleName == '后台设置') {\r\n          platform = 'management'\r\n        } else {\r\n          platform = 'digitalfactory'\r\n        }\r\n        this.$qiankun.switchMicroAppFn(\r\n          platform,\r\n          item.ModuleCode,\r\n          item.ModuleId,\r\n          item.Url\r\n        )\r\n      } else {\r\n        const platform = 'digitalfactory'\r\n        const code = 'szgc'\r\n        const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\r\n        let url = ''\r\n        if (item.Module == '环境管理') {\r\n          url = '/business/environment/alarmInformation'\r\n        } else\r\n        if (item.Module == '访客管理') {\r\n          url = '/business/visitorList'\r\n          console.log('访客管理')\r\n        }\r\n        this.$qiankun.switchMicroAppFn(platform, code, id, url)\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.onFresh()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.onFresh()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.layout {\r\n  height: 100%;\r\n  width: 100%;\r\n  position: absolute;\r\n  ::v-deep {\r\n    .CustomLayout {\r\n      .layoutTable {\r\n        height: 0;\r\n        .CustomTable {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          .table {\r\n            flex: 1;\r\n            height: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            .el-table {\r\n              flex: 1;\r\n              height: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,SACAC,eAAA,EACAC,WAAA,EACAC,aAAA,QACA;AACA;EACAC,IAAA;EACAC,UAAA;IACAN,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAQ,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,QAAA;QACAC,IAAA;QACAC,IAAA;QACAC,SAAA;QACAC,OAAA;QACAC,MAAA;QACAC,IAAA;MACA;MACAC,YAAA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,KAAA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAM,OAAA,IAEA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAM,OAAA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAD,IAAA;YACAS,cAAA;YACAC,gBAAA;YACAC,cAAA;YACAT,SAAA;YACAU,WAAA;UACA;UACAR,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;YACA,IAAAA,CAAA,IAAAA,CAAA,CAAAQ,MAAA;cACA1B,KAAA,CAAAC,QAAA,CAAAG,SAAA,GAAAc,CAAA;cACAlB,KAAA,CAAAC,QAAA,CAAAI,OAAA,GAAAa,CAAA;YACA;cACAlB,KAAA,CAAAC,QAAA,CAAAG,SAAA;cACAJ,KAAA,CAAAC,QAAA,CAAAI,OAAA;YACA;UACA;QACA,EACA;QACAsB,KAAA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,OAAA;QACAC,YAAA;UACAC,UAAA;QAGA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;QACAC,YAAA,GACA;UACA5B,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,EACA;QACA8B,SAAA;QACAC,YAAA,GACA;UACAC,WAAA;UACA7B,YAAA;YACAD,IAAA;UACA;UACA+B,OAAA,WAAAA,QAAAC,KAAA,EAAAC,GAAA;YACA9C,KAAA,CAAA+C,aAAA,CAAAD,GAAA;UACA;QACA;MAEA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;IACA,KAAAC,aAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAH,WAAA,WAAAA,YAAA;MAAA,IAAAI,MAAA;MACA1D,WAAA,KAAA2D,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAC,GAAA;UACA,IAAA1D,IAAA,GAAAwD,GAAA,CAAAG,IAAA;UACA3D,IAAA,CAAA4D,OAAA,WAAAC,IAAA;YACA,IAAAC,GAAA;cACAjD,KAAA,EAAAgD,IAAA,CAAA1D,IAAA;cACA4D,KAAA,EAAAF,IAAA,CAAAG;YACA;YACAN,GAAA,CAAAO,IAAA,CAAAH,GAAA;UACA;UACAR,MAAA,CAAA5C,UAAA,CAAAC,SAAA,CAAAuD,IAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAvD,GAAA;UAAA,GAAAU,OAAA,GAAAoC,GAAA;QACA;UACAJ,MAAA,CAAAc,QAAA;YACAtD,IAAA;YACAuD,OAAA,EAAAb,GAAA,CAAAc;UACA;QACA;MACA;IACA;IACA;IACAnB,aAAA,WAAAA,cAAA;MAAA,IAAAoB,MAAA;MACA,KAAAvC,iBAAA,CAAAC,OAAA;MACApC,aAAA,KAAA0D,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAC,GAAA;UACA,IAAA1D,IAAA,GAAAwD,GAAA,CAAAG,IAAA;UACA3D,IAAA,CAAA4D,OAAA,WAAAC,IAAA;YACA,IAAAC,GAAA;cACAjD,KAAA,EAAAgD,IAAA,CAAA1D,IAAA;cACA4D,KAAA,EAAAF,IAAA,CAAAG;YACA;YACAN,GAAA,CAAAO,IAAA,CAAAH,GAAA;UACA;UACAS,MAAA,CAAA7D,UAAA,CAAAC,SAAA,CAAAuD,IAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAvD,GAAA;UAAA,GAAAU,OAAA,GAAAoC,GAAA;QACA;UACAa,MAAA,CAAAH,QAAA;YACAtD,IAAA;YACAuD,OAAA,EAAAb,GAAA,CAAAc;UACA;QACA;MACA,GAAAE,OAAA;QACAD,MAAA,CAAAvC,iBAAA,CAAAC,OAAA;MACA;IACA;IACAwC,UAAA,WAAAA,WAAAzE,IAAA;MACAoB,OAAA,CAAAC,GAAA,CAAArB,IAAA;MACA,KAAAoD,OAAA;IACA;IACAsB,SAAA,WAAAA,UAAA;MACA,KAAAtB,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAAuB,SAAA;IACA;IACAA,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACA,KAAAT,MAAA,CAAA1E,QAAA,CAAAM,IAAA,IAAAoE,MAAA,CAAA1E,QAAA,CAAAM,IAAA,CAAAmB,MAAA;gBACAiD,MAAA,CAAA1E,QAAA,CAAAG,SAAA;gBACAuE,MAAA,CAAA1E,QAAA,CAAAI,OAAA;cACA;cAAA6E,QAAA,CAAAE,IAAA;cAAA,OACA1F,eAAA,CAAA2F,aAAA,CAAAA,aAAA,KACAV,MAAA,CAAA1E,QAAA;gBACAqF,IAAA,EAAAX,MAAA,CAAA5C,iBAAA,CAAAK,WAAA;gBACAmD,QAAA,EAAAZ,MAAA,CAAA5C,iBAAA,CAAAM;cAAA,EACA,EAAAiB,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAmB,MAAA,CAAA5C,iBAAA,CAAAU,SAAA,GAAAc,GAAA,CAAAG,IAAA,CAAAA,IAAA;kBACAiB,MAAA,CAAA5C,iBAAA,CAAAO,KAAA,GAAAiB,GAAA,CAAAG,IAAA,CAAA8B,UAAA;gBACA;kBACAb,MAAA,CAAAR,QAAA;oBACAtD,IAAA;oBACAuD,OAAA,EAAAb,GAAA,CAAAc;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAa,QAAA,CAAAO,IAAA;UAAA;QAAA,GAAAV,OAAA;MAAA;IACA;IAEA;IACAhC,aAAA,WAAAA,cAAAa,IAAA;MACA;MACAzC,OAAA,CAAAC,GAAA,CAAAwC,IAAA;MACA,IAAAA,IAAA,CAAA8B,GAAA;QACA,IAAAC,QAAA;QACA,IAAA/B,IAAA,CAAAgC,UAAA;UACAD,QAAA;QACA;UACAA,QAAA;QACA;QACA,KAAAE,QAAA,CAAAC,gBAAA,CACAH,QAAA,EACA/B,IAAA,CAAAmC,UAAA,EACAnC,IAAA,CAAAoC,QAAA,EACApC,IAAA,CAAA8B,GACA;MACA;QACA,IAAAC,SAAA;QACA,IAAAM,IAAA;QACA,IAAAC,EAAA;QACA,IAAAC,GAAA;QACA,IAAAvC,IAAA,CAAAwC,MAAA;UACAD,GAAA;QACA,OACA,IAAAvC,IAAA,CAAAwC,MAAA;UACAD,GAAA;UACAhF,OAAA,CAAAC,GAAA;QACA;QACA,KAAAyE,QAAA,CAAAC,gBAAA,CAAAH,SAAA,EAAAM,IAAA,EAAAC,EAAA,EAAAC,GAAA;MACA;IACA;IACAE,gBAAA,WAAAA,iBAAAC,GAAA;MACAnF,OAAA,CAAAC,GAAA,iBAAAmF,MAAA,CAAAD,GAAA;MACA,KAAAvE,iBAAA,CAAAM,QAAA,GAAAiE,GAAA;MACA,KAAAnD,OAAA;IACA;IACAqD,mBAAA,WAAAA,oBAAAF,GAAA;MACAnF,OAAA,CAAAC,GAAA,wBAAAmF,MAAA,CAAAD,GAAA;MACA,KAAAvE,iBAAA,CAAAK,WAAA,GAAAkE,GAAA;MACA,KAAAnD,OAAA;IACA;EACA;AACA", "ignoreList": []}]}