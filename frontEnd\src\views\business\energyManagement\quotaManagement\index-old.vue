<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible" top="6vh" :destroy-on-close="true">
      <component
        :is="currentComponent"
        ref="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      /></el-dialog>
  </div>
</template>

<script>
import { parseTime } from '@/utils'
// import { baseUrl } from '@/utils/baseurl'
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import DialogForm from './dialogForm.vue'
// import { downloadFile } from '@/utils/downloadFile'
import { GetGridByCode } from '@/api/sys'
import {
  GetQuotaList,
  DeleteQuota,
  DeleteAllQuota
} from '@/api/business/energyManagement'
export default {
  name: 'QuotaManagement',
  components: {
    CustomTable,
    // CustomButton,
    // CustomTitle,
    CustomForm,
    CustomLayout
  },
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {},
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '21',
      tableSelection: [],

      ruleForm: {
        Content: ''
        // EqtType: '',
        // Position: ''
      },
      customForm: {
        formItems: [
          {
            key: 'Content', // 字段ID
            label: '点表编号或名称', // Form的label
            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            placeholder: '输入点表编号或名称进行搜索',
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true
            },
            width: '240px',
            change: (e) => {
              // change事件
              console.log(e)
            }
          }
        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: '新增',
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: false, // 是否禁用
              icon: '', //  图标
              autofocus: false, // 是否聚焦
              type: 'primary', // primary / success / warning / danger / info / text
              size: 'small', // medium / small / mini
              onclick: (item) => {
                console.log(item)
                this.handleCreate()
              }
            },
            {
              text: '全部删除',
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: false, // 是否禁用
              icon: '', //  图标
              autofocus: false, // 是否聚焦
              type: 'danger', // primary / success / warning / danger / info / text
              size: 'small', // medium / small / mini
              onclick: (item) => {
                console.log(item)
                this.handleAllDelete()
              }
            }
          ]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            width: 60,
            label: '序号',
            otherOptions: {
              type: 'index',
              align: 'center'
            } // key
          }
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: '查看',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'view')
            }
          },
          {
            actionLabel: '编辑',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'edit')
            }
          },
          {
            actionLabel: '删除',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleDelete(index, row)
            }
          }
        ]
      }
    }
  },
  computed: {},
  created() {
    this.getBaseData()
    this.init()
  },
  methods: {
    getBaseData() {
      // 获取表格配置
      GetGridByCode({ code: 'quota_management_list' }).then(res => {
        if (res.IsSucceed) {
          const data = res.Data.ColumnList.map(item => {
            return {
              label: item.Display_Name,
              key: item.Code
            }
          })
          this.customTableConfig.tableColumns.push(...data)
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    searchForm(data) {
      console.log(data)
      this.onFresh()
    },
    resetForm() {
      this.onFresh()
    },
    onFresh() {
      this.getQuotaList()
    },
    init() {
      this.getQuotaList()
    },
    async getQuotaList() {
      const res = await GetQuotaList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        console.log(res)
        this.customTableConfig.tableData = res.Data.Data.map(item => {
          item.Date = item.Date ? parseTime(new Date(item.Date), '{y}-{m}-{d}') : ''
          return item
        })
        this.customTableConfig.buttonConfig.buttonList[1].disabled = this.customTableConfig.tableData.length === 0
        this.customTableConfig.total = res.Data.TotalCount
      } else {
        this.$message({
          type: 'error',
          message: res.Message
        })
      }
    },
    handleCreate() {
      this.dialogTitle = '新增'
      this.dialogVisible = true
      this.componentsConfig = {}
      this.$nextTick(() => {
        this.$refs.currentComponent.init('add')
      })
    },
    // 全部删除
    handleAllDelete() {
      this.$confirm('该操作将删除全部能耗配额，是否确认删除?', {
        type: 'warning'
      })
        .then(async(_) => {
          const res = await DeleteAllQuota()
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.init()
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        })
        .catch((_) => {})
    },
    // 单个删除
    handleDelete(index, row) {
      console.log(index, row)
      this.$confirm('该操作将删除当前能耗配额，是否确认删除?', {
        type: 'warning'
      })
        .then(async(_) => {
          const res = await DeleteQuota({
            IDs: [row.Id]
          })
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.init()
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        })
        .catch((_) => {})
    },
    handleEdit(index, row, type) {
      console.log(index, row, type)
      if (type === 'view') {
        this.dialogTitle = '查看'
        this.componentsConfig = { ...row }
        this.$nextTick(() => {
          this.$refs.currentComponent.init(type)
        })
      } else if (type === 'edit') {
        this.dialogTitle = '编辑'
        this.componentsConfig = { ...row }
        this.$nextTick(() => {
          this.$refs.currentComponent.init(type)
        })
      }
      this.dialogVisible = true
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.init()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.init()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
      this.customTableConfig.buttonConfig.buttonList[1].disabled = selection.length === 0
    }
  }
}
</script>

  <style lang="scss" scoped>
  .mt20 {
    margin-top: 10px;
  }
  </style>

