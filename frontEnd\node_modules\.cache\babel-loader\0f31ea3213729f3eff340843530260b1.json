{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\purviewConfig\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\purviewConfig\\index.vue", "mtime": 1755674552409}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "getGridByCode", "GetList", "DelAuthGroup", "SaveAuthGroup", "ExportData", "addRouterPage", "downloadFile", "name", "components", "data", "_this", "dialogVisible", "dialogTitle", "tableSelection", "ruleForm", "Name", "addForm", "rules", "required", "message", "trigger", "customForm", "formItems", "key", "label", "type", "otherOptions", "clearable", "change", "e", "console", "log", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "text", "size", "onclick", "item", "handleCreate", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "width", "align", "tableData", "operateOptions", "tableActionsWidth", "tableActions", "actionLabel", "index", "row", "handleWatch", "handleEdit", "handleDele", "Id", "handleExport", "addPageArray", "path", "$route", "hidden", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "meta", "title", "created", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "init", "stop", "watch", "handler", "newValue", "oldValue", "deep", "mixins", "methods", "searchForm", "fetchData", "resetForm", "_this3", "_callee2", "res", "_callee2$", "_context2", "_objectSpread", "Page", "PageSize", "sent", "IsSucceed", "Data", "Total", "$message", "error", "Message", "_this4", "_callee3", "_callee3$", "_context3", "submitForm", "_this5", "$refs", "validate", "valid", "success", "resetAddForm", "resetFields", "_this6", "_callee4", "_callee4$", "_context4", "$router", "push", "query", "pg_redirect", "_this7", "_callee5", "_callee5$", "_context5", "_callee6", "url", "link", "_callee6$", "_context6", "window", "URL", "createObjectURL", "Blob", "document", "createElement", "style", "display", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "selection", "_this8", "$confirm", "confirmButtonText", "cancelButtonText", "catch"], "sources": ["src/views/business/accessControl/purviewConfig/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      width=\"30%\"\r\n      :visible.sync=\"dialogVisible\"\r\n      destroy-on-close\r\n    >\r\n      <el-form\r\n        :model=\"addForm\"\r\n        :rules=\"rules\"\r\n        ref=\"ruleForm\"\r\n        label-width=\"100px\"\r\n        class=\"demo-ruleForm\"\r\n      >\r\n        <el-form-item label=\"权限组名称\" prop=\"Name\">\r\n          <el-input v-model=\"addForm.Name\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n          <el-button @click=\"resetAddForm\">取消</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport getGridByCode from \"../../safetyManagement/mixins/index\";\r\nimport { GetList, DelAuthGroup, SaveAuthGroup, ExportData } from \"@/api/business/purviewConfig\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      dialogTitle: \"新增权限组\",\r\n      tableSelection: \"\",\r\n      ruleForm: {\r\n        Name: \"\",\r\n      },\r\n      addForm: {\r\n        Name: \"\",\r\n      },\r\n      rules: {\r\n        Name: [{ required: true, message: '请输入权限组名称', trigger: 'blur' }]\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Name\",\r\n            label: \"门禁组名称\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"新增\",\r\n              type: \"primary\",\r\n              size: \"small\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '门禁组',\r\n            key: 'P_Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备数量',\r\n            width: '50px',\r\n            key: 'num',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '可通行人员',\r\n            width: '50px',\r\n            key: 'P_Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '同步状态',\r\n            key: 'SyncRemark',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: \"240px\",\r\n          align: \"center\",\r\n        },\r\n        tableActionsWidth: 160,\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleWatch(row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDele(row.Id)\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"导出\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              console.log(index, row);\r\n              this.handleExport(row.Id);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/watchDevice\",\r\n          hidden: true,\r\n          component: () => import(\"./watchDevice.vue\"),\r\n          meta: { title: `门禁权限配置详情` },\r\n          name: \"purviewConfigWatchDevice\",\r\n        },\r\n        {\r\n          path: this.$route.path + \"/editDevice\",\r\n          hidden: true,\r\n          component: () => import(\"./editDevice.vue\"),\r\n          meta: { title: `门禁权限配置编辑` },\r\n          name: \"purviewConfigEditDevice\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  async created() {\r\n    this.init();\r\n  },\r\n  watch: {\r\n    'customTableConfig.tableData': {\r\n      handler(newValue, oldValue) {\r\n        this.customTableConfig.tableColumns = [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '门禁组',\r\n            key: 'Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '设备数量',\r\n\r\n            key: 'EquipCount',\r\n            otherOptions: {\r\n              align: 'center',\r\n              width:'250px',\r\n            }\r\n          },\r\n          {\r\n            label: '可通行人员',\r\n\r\n            key: 'UserCount',\r\n            otherOptions: {\r\n              align: 'center',\r\n              width:'250px',\r\n            }\r\n          },\r\n          {\r\n            label: '同步状态',\r\n            key: 'SyncRemark',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n        ]\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mixins: [getGridByCode, addRouterPage],\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData();\r\n    },\r\n    resetForm() {\r\n      this.fetchData();\r\n    },\r\n    init() {\r\n      this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      const res = await GetList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.Total;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    async handleCreate() {\r\n      this.ruleForm.Name = ''\r\n      this.dialogVisible = true\r\n    },\r\n    submitForm() {\r\n      this.$refs.ruleForm.validate((valid) => {\r\n        if (valid) {\r\n          SaveAuthGroup(this.addForm).then(res => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success('新增成功')\r\n              this.fetchData()\r\n              this.resetAddForm()\r\n            } else {\r\n              this.$message.error(res.Message)\r\n            }\r\n          })\r\n        } else {\r\n          console.log('error submit!!');\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    resetAddForm() {\r\n      this.$refs.ruleForm.resetFields();\r\n      this.dialogVisible = false\r\n    },\r\n    async handleWatch(row) {\r\n      this.$router.push({\r\n        name: \"purviewConfigWatchDevice\",\r\n        query: { pg_redirect: this.$route.name, Id: row.Id },\r\n      });\r\n    },\r\n    async handleEdit(row) {\r\n      this.$router.push({\r\n        name: \"purviewConfigEditDevice\",\r\n        query: { pg_redirect: this.$route.name, Id: row.Id },\r\n      });\r\n    },\r\n    async handleExport(Id) {\r\n      const res = await ExportData({ Id });\r\n      const url = window.URL.createObjectURL(\r\n        new Blob([res], {\r\n          type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\r\n        })\r\n      );\r\n      const link = document.createElement(\"a\");\r\n      link.style.display = \"none\";\r\n      link.href = url;\r\n      link.setAttribute(\"download\", \"门禁权限配置.xlsx\");\r\n      document.body.appendChild(link);\r\n      link.click();\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.fetchData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.fetchData();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleDele(Id) {\r\n      this.$confirm('是否确定删除该数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DelAuthGroup({ Id }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            });\r\n            this.fetchData()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n\r\n      }).catch(() => {\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,OAAAC,aAAA;AACA,SAAAC,OAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,UAAA;AACA,OAAAC,aAAA;AACA,SAAAC,YAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAV,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAY,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,aAAA;MACAC,WAAA;MACAC,cAAA;MACAC,QAAA;QACAC,IAAA;MACA;MACAC,OAAA;QACAD,IAAA;MACA;MACAE,KAAA;QACAF,IAAA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,EACA;QACAZ,KAAA;QACAe,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAC,IAAA;YACAb,IAAA;YACAc,IAAA;YACAC,OAAA,WAAAA,QAAAC,IAAA;cACAX,OAAA,CAAAC,GAAA,CAAAU,IAAA;cACA/B,KAAA,CAAAgC,YAAA;YACA;UACA;QAEA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA,GACA;UACAC,KAAA;UACAtB,YAAA;YACAD,IAAA;YACAwB,KAAA;UACA;QACA,GACA;UACAzB,KAAA;UACAD,GAAA;UACAG,YAAA;YACAuB,KAAA;UACA;QACA,GACA;UACAzB,KAAA;UACAwB,KAAA;UACAzB,GAAA;UACAG,YAAA;YACAuB,KAAA;UACA;QACA,GACA;UACAzB,KAAA;UACAwB,KAAA;UACAzB,GAAA;UACAG,YAAA;YACAuB,KAAA;UACA;QACA,GACA;UACAzB,KAAA;UACAD,GAAA;UACAG,YAAA;YACAuB,KAAA;UACA;QACA,EACA;QACAC,SAAA;QACAC,cAAA;UACAH,KAAA;UACAC,KAAA;QACA;QACAG,iBAAA;QACAC,YAAA,GACA;UACAC,WAAA;UACA5B,YAAA;YACAD,IAAA;UACA;UACAe,OAAA,WAAAA,QAAAe,KAAA,EAAAC,GAAA;YACA9C,KAAA,CAAA+C,WAAA,CAAAD,GAAA;UACA;QACA,GACA;UACAF,WAAA;UACA5B,YAAA;YACAD,IAAA;UACA;UACAe,OAAA,WAAAA,QAAAe,KAAA,EAAAC,GAAA;YACA9C,KAAA,CAAAgD,UAAA,CAAAF,GAAA;UACA;QACA,GACA;UACAF,WAAA;UACA5B,YAAA;YACAD,IAAA;UACA;UACAe,OAAA,WAAAA,QAAAe,KAAA,EAAAC,GAAA;YACA9C,KAAA,CAAAiD,UAAA,CAAAH,GAAA,CAAAI,EAAA;UACA;QACA,GACA;UACAN,WAAA;UACA5B,YAAA;YACAD,IAAA;UACA;UACAe,OAAA,WAAAA,QAAAe,KAAA,EAAAC,GAAA;YACA1B,OAAA,CAAAC,GAAA,CAAAwB,KAAA,EAAAC,GAAA;YACA9C,KAAA,CAAAmD,YAAA,CAAAL,GAAA,CAAAI,EAAA;UACA;QACA;MAEA;MACAE,YAAA,GACA;QACAC,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAC,IAAA;UAAAC,KAAA;QAAA;QACAlE,IAAA;MACA,GACA;QACAwD,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAC,IAAA;UAAAC,KAAA;QAAA;QACAlE,IAAA;MACA;IAEA;EACA;EACAmE,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAT,MAAA,CAAAU,IAAA;UAAA;UAAA;YAAA,OAAAH,QAAA,CAAAI,IAAA;QAAA;MAAA,GAAAP,OAAA;IAAA;EACA;EACAQ,KAAA;IACA;MACAC,OAAA,WAAAA,QAAAC,QAAA,EAAAC,QAAA;QACA,KAAAvD,iBAAA,CAAAY,YAAA,IACA;UACAC,KAAA;UACAtB,YAAA;YACAD,IAAA;YACAwB,KAAA;UACA;QACA,GACA;UACAzB,KAAA;UACAD,GAAA;UACAG,YAAA;YACAuB,KAAA;UACA;QACA,GACA;UACAzB,KAAA;UAEAD,GAAA;UACAG,YAAA;YACAuB,KAAA;YACAD,KAAA;UACA;QACA,GACA;UACAxB,KAAA;UAEAD,GAAA;UACAG,YAAA;YACAuB,KAAA;YACAD,KAAA;UACA;QACA,GACA;UACAxB,KAAA;UACAD,GAAA;UACAG,YAAA;YACAuB,KAAA;UACA;QACA,EACA;MACA;MACA0C,IAAA;IACA;EACA;EACAC,MAAA,GAAA5F,aAAA,EAAAK,aAAA;EACAwF,OAAA;IACAC,UAAA,WAAAA,WAAArF,IAAA;MACA,KAAA0B,iBAAA,CAAAS,WAAA;MACA,KAAAmD,SAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAAD,SAAA;IACA;IACAV,IAAA,WAAAA,KAAA;MACA,KAAAU,SAAA;IACA;IACAA,SAAA,WAAAA,UAAA;MAAA,IAAAE,MAAA;MAAA,OAAArB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAoB,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAAtB,mBAAA,GAAAG,IAAA,UAAAoB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlB,IAAA,GAAAkB,SAAA,CAAAjB,IAAA;YAAA;cAAAiB,SAAA,CAAAjB,IAAA;cAAA,OACAnF,OAAA,CAAAqG,aAAA;gBACAC,IAAA,EAAAN,MAAA,CAAA9D,iBAAA,CAAAS,WAAA;gBACA4D,QAAA,EAAAP,MAAA,CAAA9D,iBAAA,CAAAU;cAAA,GACAoD,MAAA,CAAAnF,QAAA,CACA;YAAA;cAJAqF,GAAA,GAAAE,SAAA,CAAAI,IAAA;cAKA,IAAAN,GAAA,CAAAO,SAAA;gBACAT,MAAA,CAAA9D,iBAAA,CAAAe,SAAA,GAAAiD,GAAA,CAAAQ,IAAA,CAAAA,IAAA;gBACAV,MAAA,CAAA9D,iBAAA,CAAAW,KAAA,GAAAqD,GAAA,CAAAQ,IAAA,CAAAC,KAAA;cACA;gBACAX,MAAA,CAAAY,QAAA,CAAAC,KAAA,CAAAX,GAAA,CAAAY,OAAA;cACA;YAAA;YAAA;cAAA,OAAAV,SAAA,CAAAf,IAAA;UAAA;QAAA,GAAAY,QAAA;MAAA;IACA;IACAxD,YAAA,WAAAA,aAAA;MAAA,IAAAsE,MAAA;MAAA,OAAApC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAmC,SAAA;QAAA,OAAApC,mBAAA,GAAAG,IAAA,UAAAkC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhC,IAAA,GAAAgC,SAAA,CAAA/B,IAAA;YAAA;cACA4B,MAAA,CAAAlG,QAAA,CAAAC,IAAA;cACAiG,MAAA,CAAArG,aAAA;YAAA;YAAA;cAAA,OAAAwG,SAAA,CAAA7B,IAAA;UAAA;QAAA,GAAA2B,QAAA;MAAA;IACA;IACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAxG,QAAA,CAAAyG,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACArH,aAAA,CAAAkH,MAAA,CAAArG,OAAA,EAAAqD,IAAA,WAAA8B,GAAA;YACA,IAAAA,GAAA,CAAAO,SAAA;cACAW,MAAA,CAAAR,QAAA,CAAAY,OAAA;cACAJ,MAAA,CAAAtB,SAAA;cACAsB,MAAA,CAAAK,YAAA;YACA;cACAL,MAAA,CAAAR,QAAA,CAAAC,KAAA,CAAAX,GAAA,CAAAY,OAAA;YACA;UACA;QACA;UACAjF,OAAA,CAAAC,GAAA;UACA;QACA;MACA;IACA;IACA2F,YAAA,WAAAA,aAAA;MACA,KAAAJ,KAAA,CAAAxG,QAAA,CAAA6G,WAAA;MACA,KAAAhH,aAAA;IACA;IACA8C,WAAA,WAAAA,YAAAD,GAAA;MAAA,IAAAoE,MAAA;MAAA,OAAAhD,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA+C,SAAA;QAAA,OAAAhD,mBAAA,GAAAG,IAAA,UAAA8C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5C,IAAA,GAAA4C,SAAA,CAAA3C,IAAA;YAAA;cACAwC,MAAA,CAAAI,OAAA,CAAAC,IAAA;gBACA1H,IAAA;gBACA2H,KAAA;kBAAAC,WAAA,EAAAP,MAAA,CAAA5D,MAAA,CAAAzD,IAAA;kBAAAqD,EAAA,EAAAJ,GAAA,CAAAI;gBAAA;cACA;YAAA;YAAA;cAAA,OAAAmE,SAAA,CAAAzC,IAAA;UAAA;QAAA,GAAAuC,QAAA;MAAA;IACA;IACAnE,UAAA,WAAAA,WAAAF,GAAA;MAAA,IAAA4E,MAAA;MAAA,OAAAxD,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAuD,SAAA;QAAA,OAAAxD,mBAAA,GAAAG,IAAA,UAAAsD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApD,IAAA,GAAAoD,SAAA,CAAAnD,IAAA;YAAA;cACAgD,MAAA,CAAAJ,OAAA,CAAAC,IAAA;gBACA1H,IAAA;gBACA2H,KAAA;kBAAAC,WAAA,EAAAC,MAAA,CAAApE,MAAA,CAAAzD,IAAA;kBAAAqD,EAAA,EAAAJ,GAAA,CAAAI;gBAAA;cACA;YAAA;YAAA;cAAA,OAAA2E,SAAA,CAAAjD,IAAA;UAAA;QAAA,GAAA+C,QAAA;MAAA;IACA;IACAxE,YAAA,WAAAA,aAAAD,EAAA;MAAA,OAAAgB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA0D,SAAA;QAAA,IAAArC,GAAA,EAAAsC,GAAA,EAAAC,IAAA;QAAA,OAAA7D,mBAAA,GAAAG,IAAA,UAAA2D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzD,IAAA,GAAAyD,SAAA,CAAAxD,IAAA;YAAA;cAAAwD,SAAA,CAAAxD,IAAA;cAAA,OACAhF,UAAA;gBAAAwD,EAAA,EAAAA;cAAA;YAAA;cAAAuC,GAAA,GAAAyC,SAAA,CAAAnC,IAAA;cACAgC,GAAA,GAAAI,MAAA,CAAAC,GAAA,CAAAC,eAAA,CACA,IAAAC,IAAA,EAAA7C,GAAA;gBACA1E,IAAA;cACA,EACA;cACAiH,IAAA,GAAAO,QAAA,CAAAC,aAAA;cACAR,IAAA,CAAAS,KAAA,CAAAC,OAAA;cACAV,IAAA,CAAAW,IAAA,GAAAZ,GAAA;cACAC,IAAA,CAAAY,YAAA;cACAL,QAAA,CAAAM,IAAA,CAAAC,WAAA,CAAAd,IAAA;cACAA,IAAA,CAAAe,KAAA;YAAA;YAAA;cAAA,OAAAb,SAAA,CAAAtD,IAAA;UAAA;QAAA,GAAAkD,QAAA;MAAA;IACA;IACAkB,gBAAA,WAAAA,iBAAAC,GAAA;MACA7H,OAAA,CAAAC,GAAA,iBAAA6H,MAAA,CAAAD,GAAA;MACA,KAAAxH,iBAAA,CAAAU,QAAA,GAAA8G,GAAA;MACA,KAAA5D,SAAA;IACA;IACA8D,mBAAA,WAAAA,oBAAAF,GAAA;MACA7H,OAAA,CAAAC,GAAA,wBAAA6H,MAAA,CAAAD,GAAA;MACA,KAAAxH,iBAAA,CAAAS,WAAA,GAAA+G,GAAA;MACA,KAAA5D,SAAA;IACA;IACA+D,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAlJ,cAAA,GAAAkJ,SAAA;IACA;IACApG,UAAA,WAAAA,WAAAC,EAAA;MAAA,IAAAoG,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA1I,IAAA;MACA,GAAA4C,IAAA;QACAnE,YAAA;UAAA0D,EAAA,EAAAA;QAAA,GAAAS,IAAA,WAAA8B,GAAA;UACA,IAAAA,GAAA,CAAAO,SAAA;YACAsD,MAAA,CAAAnD,QAAA;cACApF,IAAA;cACAN,OAAA;YACA;YACA6I,MAAA,CAAAjE,SAAA;UACA;YACAiE,MAAA,CAAAnD,QAAA,CAAAC,KAAA,CAAAX,GAAA,CAAAY,OAAA;UACA;QACA;MAEA,GAAAqD,KAAA,cACA;IACA;EACA;AACA", "ignoreList": []}]}