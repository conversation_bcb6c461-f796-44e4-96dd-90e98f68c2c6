<template>
  <div class="app-container abs100 equipmentAlarm">
    <custom-layout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="submitForm"
          @resetForm="fetchData"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        >
          <template #customBtn="{ slotScope }">
            <el-button
              v-if="slotScope.Handle_Status != 2"
              type="text"
              @click="handleChange(slotScope)"
            >关闭</el-button>
          </template>
        </CustomTable>
      </template>
    </custom-layout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      />
    </el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import getGridByCode from '../mixins/index'
import { GetWarningListSZCJ, ExportWaringManage, WaringManageInfo, UpdateWarningStatus } from '@/api/business/safetyManagement'
import DialogForm from './components/dialogForm.vue'
import { downloadFile } from '@/utils/downloadFile'

export default {
  components: {
    CustomLayout,
    CustomTable,
    CustomForm
  },
  mixins: [getGridByCode],
  data() {
    return {
      ruleForm: {
        EquipmentName: '',
        EquipmentDate: ['', ''],
        BeginTime: '',
        EndTime: '',
        Status: '',
        EquipmentType: '',
        Handler: ''
      },
      customForm: {
        formItems: [
          {
            key: 'EquipmentName',
            label: '设备名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'EquipmentDate',
            label: '触发告警时间',
            type: 'datePicker',
            otherOptions: {
              type: 'daterange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              clearable: true,
              valueFormat: 'yyyy-MM-dd'
            },
            change: (e) => {
              this.ruleForm.BeginTime = e[0]
              this.ruleForm.EndTime = e[1]
            }
          },
          {
            key: 'EquipmentType',
            label: '设备类型',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Handle_Status',
            label: '告警状态',
            type: 'select',
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: '请选择告警状态'
            },
            options: [
              {
                label: '告警中',
                value: 1
              },
              {
                label: '已关闭',
                value: 2
              }
              // {
              //   label: '已处理',
              //   value: 3
              // },
            ],
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Handler',
            label: '处理人',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          }
        ],
        rules: {},
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: '批量导出',
              onclick: () => {
                this.handleExport()
              }
            }
          ]
        },
        // 表格
        pageSizeOptions: [20, 40, 60, 80, 100],
        currentPage: 1,
        pageSize: 20,
        total: 1000,
        tableColumns: [],
        tableData: [],
        operateOptions: {
          align: 'center'
        },
        tableActionsWidth: 140,
        tableActions: [
          /* {
            actionLabel: '关闭',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleChange(row)
            }
          }, */
          {
            actionLabel: '查看详情',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleWatch(row.Id)
            }
          }
        ]
      },
      dialogVisible: false,
      dialogTitle: '查看详情',
      currentComponent: DialogForm,
      componentsConfig: {
        Data: {}
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
        }
      },
      multipleSelection: []
    }
  },
  created() {
    this.fetchData()
    this.getGridByCode('equipmentAlarm')
  },
  async mounted() {
    this.customForm.formItems[2].options = await this.getDictionaryDetailListByCode()
  },
  methods: {
    fetchData(data = { Page: 1, PageSize: 20 }) {
      const Data = { ...this.ruleForm, ...data }
      delete Data.EquipmentDate
      GetWarningListSZCJ(Data).then(res => {
        if (res.IsSucceed) {
          this.customTableConfig.total = res.Data.TotalCount
          this.customTableConfig.tableData = res.Data.Data
        }
      })
    },
    submitForm(data) {
      this.customTableConfig.currentPage = 1
      this.fetchData()
    },
    handleSizeChange(val) {
      this.customTableConfig.pageSize = val
      this.fetchData({ Page: this.customTableConfig.currentPage, PageSize: val })
    },
    handleCurrentChange(val) {
      this.customTableConfig.currentPage = val
      this.fetchData({ Page: val, PageSize: this.customTableConfig.pageSize })
    },
    handleSelectionChange(data) {
      console.log(data)
      this.multipleSelection = data
    },
    handleExport() {
      let id = ''
      if (this.multipleSelection.length == 0) {
        this.$message.warning('请选择数据!')
        return
      } else {
        id = this.multipleSelection.map(item => item.Id).join(',')
      }
      ExportWaringManage({
        code: 'equipmentAlarm',
        id
      }).then(res => {
        if (res.IsSucceed) {
          this.$message.success('导出成功')
          downloadFile(res.Data, '安防告警信息管理数据')
        } else {
          this.$message.error(res.Message)
        }
      })
    },
    handleWatch(id) {
      WaringManageInfo({ id }).then(res => {
        if (res.IsSucceed) {
          // 数据
          this.dialogVisible = true
          this.componentsConfig.Data = res.Data
        } else {
          this.$message.error(res.Message)
        }
      })
    },
    handleChange(row) {
      console.log(row)
      if (row.HandleStatusStr == '关闭') {
        this.$message.warning('请勿重复操作')
      } else {
        this.$confirm('此操作将关闭该告警, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          UpdateWarningStatus({ id: row.Id, wid: row.WId, StatusEnum: 2 }).then(res => {
            if (res.IsSucceed) {
              this.$message.success('操作成功')
              this.fetchData()
            } else {
              this.$message.error(res.Message)
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      }
    }
  }
}
</script>
<style scoped lang='scss'>
.equipmentAlarm{
  // height: calc(100vh - 90px);
  // overflow: hidden;
}
</style>
