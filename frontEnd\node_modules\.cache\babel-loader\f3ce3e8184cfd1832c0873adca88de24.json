{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\environmentalManagement\\alarmConfiguration\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\environmentalManagement\\alarmConfiguration\\index.vue", "mtime": 1755674552417}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "DialogForm", "DialogFormLook", "deviceTypeMixins", "GetQuotaList", "DeleteQuota", "DeleteAllQuota", "GetEnviromentDTCList", "dayjs", "name", "components", "mixins", "data", "_this", "currentComponent", "componentsConfig", "componentsFuns", "open", "dialogVisible", "close", "onFresh", "dialogTitle", "tableSelection", "ruleForm", "EquipmentTypeId", "AlarmType", "TriggerItem", "customForm", "formItems", "key", "label", "type", "options", "otherOptions", "clearable", "placeholder", "width", "change", "e", "find", "item", "disabled", "getEnviromentDTCList", "console", "log", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "text", "round", "plain", "circle", "loading", "icon", "autofocus", "size", "onclick", "handleCreate", "handleAllDelete", "pageSizeOptions", "currentPage", "pageSize", "total", "height", "tableColumns", "align", "fixed", "tableData", "operateOptions", "tableActions", "actionLabel", "index", "row", "handleEdit", "handleDelete", "computed", "mounted", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getDictionaryDetailListByCode", "v", "sent", "unshift", "value", "init", "stop", "methods", "searchForm", "resetForm", "_this3", "_callee2", "res", "_callee2$", "_context2", "_objectSpread", "Parameter<PERSON>son", "Key", "Value", "Type", "Filter_Type", "Page", "PageSize", "SortName", "SortOrder", "Search", "Content", "IsSucceed", "Data", "map", "Date", "format", "TotalCount", "$message", "error", "Message", "title", "_this4", "$confirm", "then", "_ref", "_callee3", "_", "_callee3$", "_context3", "IDs", "ID", "_x", "apply", "arguments", "catch", "_this5", "_ref2", "_callee4", "_callee4$", "_context4", "_x2", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "selection"], "sources": ["src/views/business/environmentalManagement/alarmConfiguration/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport DialogForm from './dialogForm.vue'\r\nimport DialogFormLook from './dialogFormLook.vue'\r\n\r\n// import { downloadFile } from '@/utils/downloadFile'\r\nimport deviceTypeMixins from '../../mixins/index.js'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport {\r\n  GetQuotaList,\r\n  DeleteQuota,\r\n  DeleteAllQuota,\r\n  GetEnviromentDTCList\r\n} from '@/api/business/environmentalManagement'\r\nimport dayjs from 'dayjs'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  mixins: [deviceTypeMixins],\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        EquipmentTypeId: '',\r\n        AlarmType: '',\r\n        TriggerItem: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'EquipmentTypeId', // 字段ID\r\n            label: '设备类型', // Form的label\r\n            type: 'select', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            options: [],\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请输入设备类型'\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              this.customForm.formItems.find((item) => item.key === 'TriggerItem').otherOptions.disabled = !e\r\n              this.ruleForm.TriggerItem = ''\r\n              this.getEnviromentDTCList(GetEnviromentDTCList, e)\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'TriggerItem',\r\n            label: '配置项',\r\n            type: 'select',\r\n\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n              disabled: true,\r\n              placeholder: '请选择...'\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'AlarmType',\r\n            label: '告警类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n              placeholder: '请选择...'\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '新增',\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: false, // 是否禁用\r\n              icon: '', //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleCreate()\r\n              }\r\n            },\r\n            {\r\n              text: '全部删除',\r\n              type: 'danger',\r\n              disabled: false,\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleAllDelete(item)\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          // {\r\n          //   width: 50,\r\n          //   otherOptions: {\r\n          //     type: 'selection',\r\n          //     align: 'center'\r\n          //   }\r\n          // },\r\n          {\r\n            width: 60,\r\n            label: '序号',\r\n            otherOptions: {\r\n              type: 'index',\r\n              align: 'center'\r\n            } // key\r\n            // otherOptions: {\r\n            //   width: 180, // 宽度\r\n            //   fixed: 'left', // left, right\r\n            //   align: 'center' //\tleft/center/right\r\n            // }\r\n          },\r\n          {\r\n            label: '设备类型',\r\n            key: 'EqtType',\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: '告警类型',\r\n            key: 'AlarmType'\r\n          },\r\n          {\r\n            label: '配置项',\r\n            key: 'TriggerItem'\r\n          },\r\n          {\r\n            label: '对比方式',\r\n            key: 'ContrastModeStr'\r\n          },\r\n          {\r\n            label: '阈值',\r\n            key: 'LimitValue'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 200\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  async mounted() {\r\n    this.customForm.formItems.find((v) => v.key === 'EquipmentTypeId').options = await this.getDictionaryDetailListByCode('EnvironmentEqtType', 'Id')\r\n    this.customForm.formItems.find((v) => v.key === 'AlarmType').options = await this.getDictionaryDetailListByCode('EnvironmentAlarmType', 'Value')\r\n    this.customForm.formItems.find((v) => v.key === 'EquipmentTypeId').options.unshift({ label: '全部', value: '' })\r\n    this.customForm.formItems.find((v) => v.key === 'AlarmType').options.unshift({ label: '全部', value: '' })\r\n    this.getEnviromentDTCList(GetEnviromentDTCList, '')\r\n    this.init()\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.customForm.formItems.find((item) => item.key === 'TriggerItem').otherOptions.disabled = !this.ruleForm.EquipmentTypeId\r\n      this.getEnviromentDTCList(GetEnviromentDTCList, '')\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.GetQuotaList()\r\n    },\r\n    init() {\r\n      this.GetQuotaList()\r\n    },\r\n    async GetQuotaList() {\r\n      this.customTableConfig.loading = true\r\n      const res = await GetQuotaList({\r\n        ParameterJson: [\r\n          {\r\n            Key: '',\r\n            Value: [null],\r\n            Type: '',\r\n            Filter_Type: ''\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n\r\n        SortName: '',\r\n        SortOrder: '',\r\n        Search: '',\r\n        Content: '',\r\n        ...this.ruleForm\r\n      })\r\n      this.customTableConfig.loading = false\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n          ...item,\r\n          Date: dayjs(item.Date).format('YYYY-MM-DD HH:mm:ss')\r\n        }))\r\n        console.log(res)\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n        this.customTableConfig.buttonConfig.buttonList.find(\r\n          (item) => item.text == '全部删除'\r\n        ).disabled = this.customTableConfig.total == 0\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.componentsConfig = {\r\n        disabled: false,\r\n        title: '新增'\r\n      }\r\n      this.dialogVisible = true\r\n      this.currentComponent = DialogForm\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row)\r\n      console.log(this)\r\n      this.$confirm('该操作将删除当前配置，是否确认删除？', '删除', {\r\n        type: 'error'\r\n      })\r\n        .then(async(_) => {\r\n          const res = await DeleteQuota({\r\n            IDs: [row.ID]\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.init()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n        .catch((_) => { })\r\n    },\r\n    handleAllDelete(index, row) {\r\n      console.log(index, row)\r\n      console.log(this)\r\n      this.$confirm('该操作将删除全部配置，是否确认删除？', '删除', {\r\n        type: 'error'\r\n      })\r\n        .then(async(_) => {\r\n          const res = await DeleteAllQuota({\r\n            // IDs: [row.ID]\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.init()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n        .catch((_) => { })\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      this.dialogVisible = true\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.currentComponent = DialogFormLook\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: true,\r\n          title: '查看'\r\n        }\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        this.currentComponent = DialogForm\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: false,\r\n          title: '编辑'\r\n        }\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: 100% !important;\r\n  width: 100%;\r\n  position: absolute;\r\n  ::v-deep {\r\n    .CustomLayout {\r\n      .layoutTable {\r\n        height: 0;\r\n        .CustomTable {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          .table {\r\n            flex: 1;\r\n            height: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            .el-table {\r\n              flex: 1;\r\n              height: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AAEA,OAAAC,UAAA;AACA,OAAAC,cAAA;;AAEA;AACA,OAAAC,gBAAA;AACA;AACA;;AAEA,SACAC,YAAA,IAAAA,aAAA,EACAC,WAAA,EACAC,cAAA,EACAC,oBAAA,QACA;AACA,OAAAC,KAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAX,WAAA,EAAAA,WAAA;IACA;IACA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAa,MAAA,GAAAR,gBAAA;EACAS,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,gBAAA,EAAAb,UAAA;MACAc,gBAAA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAJ,KAAA,CAAAK,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAN,KAAA,CAAAK,aAAA;UACAL,KAAA,CAAAO,OAAA;QACA;MACA;MACAF,aAAA;MACAG,WAAA;MACAC,cAAA;MAEAC,QAAA;QACAC,eAAA;QACAC,SAAA;QACAC,WAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAAA;UACAC,OAAA;UACAC,YAAA;YACA;YACAC,SAAA;YACAC,WAAA;UACA;UACAC,KAAA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAzB,KAAA,CAAAc,UAAA,CAAAC,SAAA,CAAAW,IAAA,WAAAC,IAAA;cAAA,OAAAA,IAAA,CAAAX,GAAA;YAAA,GAAAI,YAAA,CAAAQ,QAAA,IAAAH,CAAA;YACAzB,KAAA,CAAAU,QAAA,CAAAG,WAAA;YACAb,KAAA,CAAA6B,oBAAA,CAAAnC,oBAAA,EAAA+B,CAAA;YACAK,OAAA,CAAAC,GAAA,CAAAN,CAAA;UACA;QACA,GACA;UACAT,GAAA;UACAC,KAAA;UACAC,IAAA;UAEAC,OAAA;UACAC,YAAA;YACAC,SAAA;YACAO,QAAA;YACAN,WAAA;UACA;UACAE,MAAA,WAAAA,OAAAC,CAAA;YACAK,OAAA,CAAAC,GAAA,CAAAN,CAAA;UACA;QACA,GACA;UACAT,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,OAAA;UACAC,YAAA;YACAC,SAAA;YACAC,WAAA;UACA;UACAE,MAAA,WAAAA,OAAAC,CAAA;YACAK,OAAA,CAAAC,GAAA,CAAAN,CAAA;UACA;QACA,EAEA;QACAO,KAAA;UACA;QAAA,CACA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAC,IAAA;YACAC,KAAA;YAAA;YACAC,KAAA;YAAA;YACAC,MAAA;YAAA;YACAC,OAAA;YAAA;YACAf,QAAA;YAAA;YACAgB,IAAA;YAAA;YACAC,SAAA;YAAA;YACA3B,IAAA;YAAA;YACA4B,IAAA;YAAA;YACAC,OAAA,WAAAA,QAAApB,IAAA;cACAG,OAAA,CAAAC,GAAA,CAAAJ,IAAA;cACA3B,KAAA,CAAAgD,YAAA;YACA;UACA,GACA;YACAT,IAAA;YACArB,IAAA;YACAU,QAAA;YACAmB,OAAA,WAAAA,QAAApB,IAAA;cACAG,OAAA,CAAAC,GAAA,CAAAJ,IAAA;cACA3B,KAAA,CAAAiD,eAAA,CAAAtB,IAAA;YACA;UACA;QAEA;QACA;QACAgB,OAAA;QACAO,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;QACAC,YAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACAhC,KAAA;UACAN,KAAA;UACAG,YAAA;YACAF,IAAA;YACAsC,KAAA;UACA;UACA;UACA;UACA;UACA;UACA;QACA,GACA;UACAvC,KAAA;UACAD,GAAA;UACAI,YAAA;YACAqC,KAAA;UACA;QACA,GACA;UACAxC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,EACA;QACA0C,SAAA;QACAC,cAAA;UACApC,KAAA;QACA;QACAqC,YAAA,GACA;UACAC,WAAA;UACAzC,YAAA;YACAF,IAAA;UACA;UACA6B,OAAA,WAAAA,QAAAe,KAAA,EAAAC,GAAA;YACA/D,KAAA,CAAAgE,UAAA,CAAAF,KAAA,EAAAC,GAAA;UACA;QACA,GACA;UACAF,WAAA;UACAzC,YAAA;YACAF,IAAA;UACA;UACA6B,OAAA,WAAAA,QAAAe,KAAA,EAAAC,GAAA;YACA/D,KAAA,CAAAgE,UAAA,CAAAF,KAAA,EAAAC,GAAA;UACA;QACA,GACA;UACAF,WAAA;UACAzC,YAAA;YACAF,IAAA;UACA;UACA6B,OAAA,WAAAA,QAAAe,KAAA,EAAAC,GAAA;YACA/D,KAAA,CAAAiE,YAAA,CAAAH,KAAA,EAAAC,GAAA;UACA;QACA;MAEA;IACA;EACA;EACAG,QAAA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAU,6BAAA;UAAA;YAAAV,MAAA,CAAAtD,UAAA,CAAAC,SAAA,CAAAW,IAAA,WAAAqD,CAAA;cAAA,OAAAA,CAAA,CAAA/D,GAAA;YAAA,GAAAG,OAAA,GAAAwD,QAAA,CAAAK,IAAA;YAAAL,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAU,6BAAA;UAAA;YAAAV,MAAA,CAAAtD,UAAA,CAAAC,SAAA,CAAAW,IAAA,WAAAqD,CAAA;cAAA,OAAAA,CAAA,CAAA/D,GAAA;YAAA,GAAAG,OAAA,GAAAwD,QAAA,CAAAK,IAAA;YACAZ,MAAA,CAAAtD,UAAA,CAAAC,SAAA,CAAAW,IAAA,WAAAqD,CAAA;cAAA,OAAAA,CAAA,CAAA/D,GAAA;YAAA,GAAAG,OAAA,CAAA8D,OAAA;cAAAhE,KAAA;cAAAiE,KAAA;YAAA;YACAd,MAAA,CAAAtD,UAAA,CAAAC,SAAA,CAAAW,IAAA,WAAAqD,CAAA;cAAA,OAAAA,CAAA,CAAA/D,GAAA;YAAA,GAAAG,OAAA,CAAA8D,OAAA;cAAAhE,KAAA;cAAAiE,KAAA;YAAA;YACAd,MAAA,CAAAvC,oBAAA,CAAAnC,oBAAA;YACA0E,MAAA,CAAAe,IAAA;UAAA;UAAA;YAAA,OAAAR,QAAA,CAAAS,IAAA;QAAA;MAAA,GAAAZ,OAAA;IAAA;EACA;EACAa,OAAA;IACAC,UAAA,WAAAA,WAAAvF,IAAA;MACA+B,OAAA,CAAAC,GAAA,CAAAhC,IAAA;MACA,KAAAqC,iBAAA,CAAAe,WAAA;MACA,KAAA5C,OAAA;IACA;IACAgF,SAAA,WAAAA,UAAA;MACA,KAAAzE,UAAA,CAAAC,SAAA,CAAAW,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAX,GAAA;MAAA,GAAAI,YAAA,CAAAQ,QAAA,SAAAlB,QAAA,CAAAC,eAAA;MACA,KAAAkB,oBAAA,CAAAnC,oBAAA;MACA,KAAAa,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAAhB,YAAA;IACA;IACA4F,IAAA,WAAAA,KAAA;MACA,KAAA5F,YAAA;IACA;IACAA,YAAA,WAAAA,aAAA;MAAA,IAAAiG,MAAA;MAAA,OAAAnB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAkB,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAApB,mBAAA,GAAAG,IAAA,UAAAkB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhB,IAAA,GAAAgB,SAAA,CAAAf,IAAA;YAAA;cACAW,MAAA,CAAApD,iBAAA,CAAAO,OAAA;cAAAiD,SAAA,CAAAf,IAAA;cAAA,OACAtF,aAAA,CAAAsG,aAAA;gBACAC,aAAA,GACA;kBACAC,GAAA;kBACAC,KAAA;kBACAC,IAAA;kBACAC,WAAA;gBACA,EACA;gBACAC,IAAA,EAAAX,MAAA,CAAApD,iBAAA,CAAAe,WAAA;gBACAiD,QAAA,EAAAZ,MAAA,CAAApD,iBAAA,CAAAgB,QAAA;gBAEAiD,QAAA;gBACAC,SAAA;gBACAC,MAAA;gBACAC,OAAA;cAAA,GACAhB,MAAA,CAAA9E,QAAA,CACA;YAAA;cAjBAgF,GAAA,GAAAE,SAAA,CAAAZ,IAAA;cAkBAQ,MAAA,CAAApD,iBAAA,CAAAO,OAAA;cACA,IAAA+C,GAAA,CAAAe,SAAA;gBACAjB,MAAA,CAAApD,iBAAA,CAAAsB,SAAA,GAAAgC,GAAA,CAAAgB,IAAA,CAAAA,IAAA,CAAAC,GAAA,WAAAhF,IAAA;kBAAA,OAAAkE,aAAA,CAAAA,aAAA,KACAlE,IAAA;oBACAiF,IAAA,EAAAjH,KAAA,CAAAgC,IAAA,CAAAiF,IAAA,EAAAC,MAAA;kBAAA;gBAAA,CACA;gBACA/E,OAAA,CAAAC,GAAA,CAAA2D,GAAA;gBACAF,MAAA,CAAApD,iBAAA,CAAAiB,KAAA,GAAAqC,GAAA,CAAAgB,IAAA,CAAAI,UAAA;gBACAtB,MAAA,CAAApD,iBAAA,CAAAC,YAAA,CAAAC,UAAA,CAAAZ,IAAA,CACA,UAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAY,IAAA;gBAAA,CACA,EAAAX,QAAA,GAAA4D,MAAA,CAAApD,iBAAA,CAAAiB,KAAA;cACA;gBACAmC,MAAA,CAAAuB,QAAA,CAAAC,KAAA,CAAAtB,GAAA,CAAAuB,OAAA;cACA;YAAA;YAAA;cAAA,OAAArB,SAAA,CAAAR,IAAA;UAAA;QAAA,GAAAK,QAAA;MAAA;IACA;IACAzC,YAAA,WAAAA,aAAA;MACA,KAAAxC,WAAA;MACA,KAAAN,gBAAA;QACA0B,QAAA;QACAsF,KAAA;MACA;MACA,KAAA7G,aAAA;MACA,KAAAJ,gBAAA,GAAAb,UAAA;IACA;IACA6E,YAAA,WAAAA,aAAAH,KAAA,EAAAC,GAAA;MAAA,IAAAoD,MAAA;MACArF,OAAA,CAAAC,GAAA,CAAA+B,KAAA,EAAAC,GAAA;MACAjC,OAAA,CAAAC,GAAA;MACA,KAAAqF,QAAA;QACAlG,IAAA;MACA,GACAmG,IAAA;QAAA,IAAAC,IAAA,GAAAjD,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAgD,SAAAC,CAAA;UAAA,IAAA9B,GAAA;UAAA,OAAApB,mBAAA,GAAAG,IAAA,UAAAgD,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA9C,IAAA,GAAA8C,SAAA,CAAA7C,IAAA;cAAA;gBAAA6C,SAAA,CAAA7C,IAAA;gBAAA,OACArF,WAAA;kBACAmI,GAAA,GAAA5D,GAAA,CAAA6D,EAAA;gBACA;cAAA;gBAFAlC,GAAA,GAAAgC,SAAA,CAAA1C,IAAA;gBAGA,IAAAU,GAAA,CAAAe,SAAA;kBACAU,MAAA,CAAAhC,IAAA;gBACA;kBACAgC,MAAA,CAAAJ,QAAA,CAAAC,KAAA,CAAAtB,GAAA,CAAAuB,OAAA;gBACA;cAAA;cAAA;gBAAA,OAAAS,SAAA,CAAAtC,IAAA;YAAA;UAAA,GAAAmC,QAAA;QAAA,CACA;QAAA,iBAAAM,EAAA;UAAA,OAAAP,IAAA,CAAAQ,KAAA,OAAAC,SAAA;QAAA;MAAA,KACAC,KAAA,WAAAR,CAAA;IACA;IACAvE,eAAA,WAAAA,gBAAAa,KAAA,EAAAC,GAAA;MAAA,IAAAkE,MAAA;MACAnG,OAAA,CAAAC,GAAA,CAAA+B,KAAA,EAAAC,GAAA;MACAjC,OAAA,CAAAC,GAAA;MACA,KAAAqF,QAAA;QACAlG,IAAA;MACA,GACAmG,IAAA;QAAA,IAAAa,KAAA,GAAA7D,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA4D,SAAAX,CAAA;UAAA,IAAA9B,GAAA;UAAA,OAAApB,mBAAA,GAAAG,IAAA,UAAA2D,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAzD,IAAA,GAAAyD,SAAA,CAAAxD,IAAA;cAAA;gBAAAwD,SAAA,CAAAxD,IAAA;gBAAA,OACApF,cAAA;kBACA;gBAAA,CACA;cAAA;gBAFAiG,GAAA,GAAA2C,SAAA,CAAArD,IAAA;gBAGA,IAAAU,GAAA,CAAAe,SAAA;kBACAwB,MAAA,CAAA9C,IAAA;gBACA;kBACA8C,MAAA,CAAAlB,QAAA,CAAAC,KAAA,CAAAtB,GAAA,CAAAuB,OAAA;gBACA;cAAA;cAAA;gBAAA,OAAAoB,SAAA,CAAAjD,IAAA;YAAA;UAAA,GAAA+C,QAAA;QAAA,CACA;QAAA,iBAAAG,GAAA;UAAA,OAAAJ,KAAA,CAAAJ,KAAA,OAAAC,SAAA;QAAA;MAAA,KACAC,KAAA,WAAAR,CAAA;IACA;IACAxD,UAAA,WAAAA,WAAAF,KAAA,EAAAC,GAAA,EAAA7C,IAAA;MACAY,OAAA,CAAAC,GAAA,CAAA+B,KAAA,EAAAC,GAAA,EAAA7C,IAAA;MACA,KAAAb,aAAA;MACA,IAAAa,IAAA;QACA,KAAAV,WAAA;QACA,KAAAP,gBAAA,GAAAZ,cAAA;QACA,KAAAa,gBAAA;UACA0H,EAAA,EAAA7D,GAAA,CAAA6D,EAAA;UACAhG,QAAA;UACAsF,KAAA;QACA;MACA,WAAAhG,IAAA;QACA,KAAAV,WAAA;QACA,KAAAP,gBAAA,GAAAb,UAAA;QACA,KAAAc,gBAAA;UACA0H,EAAA,EAAA7D,GAAA,CAAA6D,EAAA;UACAhG,QAAA;UACAsF,KAAA;QACA;MACA;IACA;IAEAqB,gBAAA,WAAAA,iBAAAC,GAAA;MACA1G,OAAA,CAAAC,GAAA,iBAAA0G,MAAA,CAAAD,GAAA;MACA,KAAApG,iBAAA,CAAAgB,QAAA,GAAAoF,GAAA;MACA,KAAArD,IAAA;IACA;IACAuD,mBAAA,WAAAA,oBAAAF,GAAA;MACA1G,OAAA,CAAAC,GAAA,wBAAA0G,MAAA,CAAAD,GAAA;MACA,KAAApG,iBAAA,CAAAe,WAAA,GAAAqF,GAAA;MACA,KAAArD,IAAA;IACA;IACAwD,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAnI,cAAA,GAAAmI,SAAA;IACA;EACA;AACA", "ignoreList": []}]}