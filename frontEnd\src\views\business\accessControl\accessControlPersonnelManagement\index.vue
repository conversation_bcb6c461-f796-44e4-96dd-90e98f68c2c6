<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        >
          <template #customBtn="{ slotScope }">
            <el-button type="text" @click="handleEnable(slotScope)">{{
              slotScope.Status == "0" ? "禁用" : "启用"
            }}</el-button></template>
        </CustomTable>
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'

import DialogForm from './dialogForm.vue'
import DialogFormLook from './dialogFormLook.vue'
import DialogFormImport from './dialogFormImport.vue'

import { downloadFile } from '@/utils/downloadFile'
// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'
// import CustomButton from '@/businessComponents/CustomButton/index.vue'

import {
  GetPersonnelList,
  SubPersonnel,
  EntrancePersonnelInfo,
  UpdateStatus,
  DelPersonnel,
  PersonnelImportTemplate,
  EntrancePersonnelImport,
  ExportEntrancePersonnel,
  GetRole,
  GetDepartment,
  GetCompany,
  GetDictionaryDetailListByCode
} from '@/api/business/accessControl'
import { GetOssUrl } from '@/api/sys/index'
export default {
  name: '',
  components: {
    CustomTable,
    // CustomButton,
    // CustomTitle,
    CustomForm,
    CustomLayout
  },
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {},
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      tableSelection: [],

      ruleForm: {
        P_Name: '',
        Contact_Way: '',
        P_Type: '',
        Position_Name: '',
        P_Unit: '',
        P_Department: ''
        // P_Workshop: ''
      },
      customForm: {
        formItems: [
          {
            key: 'P_Name', // 字段ID
            label: '姓名', // Form的label
            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true
            },
            width: '240px',
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'Contact_Way', // 字段ID
            label: '联系方式', // Form的label
            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'P_Type',
            label: '人员类型',
            type: 'select',
            options: [],
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Position_Name',
            label: '岗位名称',
            type: 'select',
            options: [],
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'P_Unit',
            label: '所属单位',
            type: 'select',
            options: [],
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'P_Department',
            label: '所属部门',
            type: 'select',
            options: [],
            change: (e) => {
              console.log(e)
            }
          }
          // {
          //   key: 'P_Workshop',
          //   label: '所属车间',
          //   type: 'select',
          //   options: [
          //   ],
          //   change: (e) => {
          //     console.log(e)
          //   }
          // }
        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: '新增',
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: false, // 是否禁用
              icon: '', //  图标
              autofocus: false, // 是否聚焦
              type: 'primary', // primary / success / warning / danger / info / text
              size: 'small', // medium / small / mini
              onclick: (item) => {
                console.log(item)
                this.handleCreate()
              }
            },
            {
              text: '导入模板下载',
              onclick: (item) => {
                console.log(item)
                this.handleTemDownload()
              }
            },
            {
              text: '批量导入',
              onclick: (item) => {
                console.log(item)
                this.handleImport()
              }
            },
            {
              text: '批量导出',
              onclick: (item) => {
                console.log(item)
                this.handleExport()
              }
            }
          ]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        operateOptions: {
          width: 200
        },
        tableColumns: [
          {
            width: 50,
            otherOptions: {
              type: 'selection',
              align: 'center'
            }
          },
          // {
          //   width: 60,
          //   label: '序号',
          //   otherOptions: {
          //     type: 'index',
          //     align: 'center'
          //   } // key
          // },
          {
            label: '员工工号',
            key: 'P_Number'
          },
          {
            label: '姓名',
            key: 'P_Name'
          },
          {
            label: '性别',
            key: 'P_Sex'
          },
          {
            label: '联系方式',
            key: 'Contact_Way'
          },
          {
            label: '人脸照片',
            key: 'Face_picture',
            otherOptions: {
              align: 'center'
            },
            render: (row) => {
              if (row.Face_Picture_Url) {
                return this.$createElement('el-image', {
                  style: {
                    width: '40px',
                    height: '40px'
                  },
                  attrs: {
                    fit: 'cover',
                    src: row.Face_Picture_Url,
                    previewSrcList: [row.Face_Picture_Url]
                  }
                })
              }
            }
          },
          {
            label: '人员类型',
            key: 'P_Type'
          },
          {
            label: '岗位名称',
            key: 'Position_Name'
          },
          {
            label: '所属部门',
            key: 'P_Department'
          },
          // {
          //   label: '所属班组',
          //   key: 'P_Group'
          // },
          // {
          //   label: '所属车间',
          //   key: 'P_Workshop'
          // },
          {
            label: '所属单位',
            key: 'P_Unit'
          },
          {
            label: '状态',
            key: 'Status',
            render: (row) => {
              const text = row.Status === '0' ? '启用' : '禁用'
              return this.$createElement('span', {}, text)
            }
          }
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: '查看详情',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'view')
            }
          },
          {
            actionLabel: '修改',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'edit')
            }
          },
          // {
          //   actionLabel: '启用',
          //   otherOptions: {
          //     type: 'text'
          //   },
          //   onclick: (index, row) => {
          //     this.handleEnable(index, row, 'edit')
          //   }
          // },
          {
            actionLabel: '删除',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleDelete(index, row)
            }
          }
        ]
      }
    }
  },
  computed: {},
  async created() {
    // 岗位类型
    this.customForm.formItems.find(
      (item) => item.key === 'Position_Name'
    ).options = await this.initGetRole('Entrance_Type')
    // 所属部门
    this.customForm.formItems.find(
      (item) => item.key === 'P_Department'
    ).options = await this.initGetDepartment('Entrance_Type')
    // 所属单位
    this.customForm.formItems.find((item) => item.key === 'P_Unit').options =
      await this.initGetCompany('Entrance_Type')
    // 人员类型
    this.customForm.formItems.find((item) => item.key === 'P_Type').options =
      await this.initDeviceType('P_Type')
    this.init()
  },
  methods: {
    // 人员类型
    async initDeviceType(code) {
      const res = await GetDictionaryDetailListByCode({
        dictionaryCode: code
      })
      const options = res.Data.map((item, index) => ({
        label: item.Display_Name,
        value: item.Value
      }))
      return options
    },
    // 岗位类型
    async initGetRole(code) {
      const res = await GetRole({})
      const options = res.Data.map((item, index) => ({
        label: item.Display_Name,
        value: item.Value
      }))
      return options
    },
    // 所属部门
    async initGetDepartment(code) {
      const res = await GetDepartment({})
      const options = res.Data.map((item, index) => ({
        label: item.Display_Name,
        value: item.Value
      }))
      return options
    },
    // 所属单位
    async initGetCompany(code) {
      const res = await GetCompany({})
      const options = res.Data.map((item, index) => ({
        label: item.Display_Name,
        value: item.Value
      }))
      return options
    },
    searchForm(data) {
      this.customTableConfig.currentPage = 1
      console.log(data)
      this.onFresh()
    },
    resetForm() {
      this.onFresh()
    },
    onFresh() {
      this.getPersonnelList()
    },
    init() {
      this.getPersonnelList()
    },
    async getPersonnelList() {
      const res = await GetPersonnelList({
        ParameterJson: [
          {
            Key: '',
            Value: [null],
            Type: '',
            Filter_Type: ''
          }
        ],
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        Search: '',
        SortName: '',
        SortOrder: '',
        P_Name: '',
        Contact_Way: '',
        P_Type: '',
        Position_Name: '',
        P_Unit: '',
        P_Department: '',
        P_Workshop: '',
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data
        console.log(res)
        this.customTableConfig.total = res.Data.TotalCount
        this.handelImage(res.Data.Data)
      } else {
        this.$message({
          type: 'error',
          message: res.Message
        })
      }
    },
    async handelImage(data) {
      const promises = data.map(async(v) => {
        let Face_Picture_Url = ''
        if (v.Face_Picture) {
          const imageRes = await GetOssUrl({ url: v.Face_Picture })
          Face_Picture_Url = imageRes.Data
        }
        return (function() {
          const Face_PictureUrl = Face_Picture_Url
          // 使用闭包来捕获变量的当前值
          v.Face_Picture_Url = v.Face_Picture ? Face_PictureUrl : ''
          return v
        })()
      })

      Promise.all(promises)
        .then((data) => {
          this.customTableConfig.tableData = data
          console.log(data)
        })
        .catch((error) => {
          console.error(error)
        })
    },

    handleCreate() {
      this.dialogTitle = '新增'
      this.componentsConfig = {
        disabled: false,
        title: '新增'
      }
      this.dialogVisible = true
      this.currentComponent = DialogForm
    },
    // 启动 停用
    async handleEnable(row) {
      const text = row.Status === '0' ? '禁用' : '启用'
      const textStatus = row.Status === '0' ? '1' : '0'
      this.$confirm(`确认${text}？`, {
        type: 'warning'
      })
        .then(async(_) => {
          const res = await UpdateStatus({
            id: row.Id,
            status: textStatus
          })
          if (res.IsSucceed) {
            this.init()
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        })
        .catch((_) => {})
    },
    handleDelete(index, row) {
      this.$confirm('确认删除？', {
        type: 'warning'
      })
        .then(async(_) => {
          const res = await DelPersonnel({
            id: row.Id
          })
          if (res.IsSucceed) {
            this.init()
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        })
        .catch((_) => {})
    },
    handleEdit(index, row, type) {
      console.log(index, row, type)
      if (type === 'view') {
        this.dialogTitle = '查看'
        this.currentComponent = DialogFormLook
        this.componentsConfig = {
          ID: row.ID,
          disabled: true,
          title: '查看',
          row: row
        }
      } else if (type === 'edit') {
        this.dialogTitle = '编辑'
        this.currentComponent = DialogForm
        this.componentsConfig = {
          ID: row.ID,
          disabled: true,
          title: '编辑',
          row: row
        }
      }
      this.dialogVisible = true
    },
    async handleTemDownload() {
      const res = await PersonnelImportTemplate({
        code: 'accessControlPersonnelManagement'
      })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '21')
        this.$message({
          type: 'success',
          message: '下载成功!'
        })
      } else {
        this.$message({
          type: 'error',
          message: res.Message
        })
      }
    },
    async handleExport() {
      if (this.tableSelection.length == 0) {
        this.$message.warning('请选择数据在导出')
        return
      }
      const res = await ExportEntrancePersonnel({
        id: this.tableSelection.map((item) => item.Id).join(','),
        code: 'accessControlPersonnelManagement',
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '21')
      } else {
        this.$message({
          type: 'error',
          message: res.Message
        })
      }
    },
    async handleImport() {
      this.dialogTitle = '批量导入'
      this.currentComponent = DialogFormImport
      this.componentsConfig = {
        disabled: true,
        title: '批量导入'
      }
      this.dialogVisible = true
      // const res = await ExportEntrancePersonnel({
      //   id: this.tableSelection.map((item) => item.Id),
      //   ...this.ruleForm
      // })
      // if (res.IsSucceed) {
      //   console.log(res)
      //   downloadFile(res.Data, '21')
      // }
    },
    async handleAllExport() {
      const res = await ExportEntrancePersonnel({
        Content: '',
        EqtType: '',
        Position: '',
        IsAll: true,
        Ids: [],
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '21')
      } else {
        this.$message({
          type: 'error',
          message: res.Message
        })
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.init()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.init()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
    }
  }
}
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
.layout{
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
