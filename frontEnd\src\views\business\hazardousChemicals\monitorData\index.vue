<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'

import DialogFormLook from './dialogFormLook.vue'

import { downloadFile } from '@/utils/downloadFile'
// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'
// import CustomButton from '@/businessComponents/CustomButton/index.vue'

import { GetDataList, ExportDataList, GetHazchemDTCList } from '@/api/business/hazardousChemicals'
import { GetDictionaryDetailListByCode } from '@/api/sys'

import { deviceTypeMixins } from '../../mixins/deviceType.js'
import otherMixin from '../../mixins/index.js'
// import moment from 'moment'
import dayjs from 'dayjs'
export default {
  name: '',
  components: {
    CustomTable,
    // CustomButton,
    // CustomTitle,
    CustomForm,
    CustomLayout
  },
  mixins: [deviceTypeMixins, otherMixin],
  data() {
    return {
      currentComponent: DialogFormLook,
      componentsConfig: {},
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      tableSelection: [],

      ruleForm: {
        Content: '',
        EqtType: '',
        Position: '',
        DataType: ''
      },
      customForm: {
        formItems: [
          {
            key: 'Content', // 字段ID
            label: '', // Form的label
            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器

            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: '输入设备编号或名称进行搜索'
            },
            width: '240px',
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'EqtType',
            label: '设备类型',
            type: 'select',

            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: '请选择设备类型'
            },
            options: [],
            change: (e) => {
              this.ruleForm.DataType = ''
              const Id = this.deceiveTypeList.find((item) => item.value === e).Id
              this.getDTCList(GetHazchemDTCList, Id, 'DataType', 'DataId')
            }
          },
          {
            key: 'DataType',
            label: '数据类型',
            type: 'select',
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: '请选择设备类型'
            },
            options: [],
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Position', // 字段ID
            label: '安装位置', // Form的label
            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器

            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: '请输入安装位置'
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          }
        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            // {
            //   text: '新增',
            //   round: false, // 是否圆角
            //   plain: false, // 是否朴素
            //   circle: false, // 是否圆形
            //   loading: false, // 是否加载中
            //   disabled: false, // 是否禁用
            //   icon: '', //  图标
            //   autofocus: false, // 是否聚焦
            //   type: 'primary', // primary / success / warning / danger / info / text
            //   size: 'small', // medium / small / mini
            //   onclick: (item) => {
            //     console.log(item)
            //     this.handleCreate()
            //   }
            // },
            // {
            //   text: '导出',
            //   key: 'batch',
            //   disabled: true,
            //   onclick: (item) => {
            //     console.log(item)
            //     this.handleExport()
            //   }
            // },
            {
              text: '批量导出',
              onclick: (item) => {
                console.log(item)
                this.handleAllExport()
              }
            }
          ]
        },
        // 表格
        loading: false,
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            otherOptions: {
              width: 20,
              type: 'selection',
              align: 'center'
            }
          }
        ],
        tableData: [],
        operateOptions: {
          width: 200
        },
        tableActions: [
          {
            actionLabel: '查看',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'view')
            }
          }
          // {
          //   actionLabel: '编辑',
          //   otherOptions: {
          //     type: 'text'
          //   },
          //   onclick: (index, row) => {
          //     this.handleEdit(index, row, 'edit')
          //   }
          // },
          // {
          //   actionLabel: '删除',
          //   otherOptions: {
          //     type: 'text'
          //   },
          //   onclick: (index, row) => {
          //     this.handleDelete(index, row)
          //   }
          // }
        ],
        deceiveTypeList: []
      }
    }
  },
  computed: {},
  async mounted() {
    this.init()
    this.deceiveTypeList = this.customForm.formItems.find((item) => item.key === 'EqtType').options = await this.getDictionaryDetailListByCode('HazchemEqtType', 'Value')
    // this.initDeviceType("EqtType", "EnvironmentEqtType");
    this.getDTCList(GetHazchemDTCList, '', 'DataType', 'DataId')
  },
  methods: {
    searchForm(data) {
      this.customTableConfig.currentPage = 1
      this.onFresh()
    },
    resetForm() {
      this.getDTCList(GetHazchemDTCList, '', 'DataType', 'DataId')
      this.onFresh()
    },
    onFresh() {
      this.GetDataList()
    },
    init() {
      this.GetDataList()
    },
    async GetDataList() {
      this.customTableConfig.loading = true
      const res = await GetDataList({
        ParameterJson: [
          {
            Key: '',
            Value: [null],
            Type: '',
            Filter_Type: ''
          }
        ],
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        SortName: '',
        SortOrder: '',
        Search: '',
        Content: '',
        EqtType: '',
        Position: '',
        IsAll: true,
        ...this.ruleForm
      })
      this.customTableConfig.loading = false
      if (res.IsSucceed) {
        if (res.Data.Data && res.Data.Data.length > 0) {
          this.customTableConfig.tableData = res.Data.Data.map((item) => ({
            ...item,
            Time: dayjs(item.Time).format('YYYY-MM-DD HH:mm:ss')
          }))
        } else {
          this.customTableConfig.tableData = []
        }
        /* this.customTableConfig.tableColumns = [].concat(
          [
            {
              width: 50,
              otherOptions: {
                type: 'selection',
                align: 'center'
              }
            },
            {
              width: 60,
              label: '序号',
              otherOptions: {
                type: 'index',
                align: 'center'
              }
            }
          ],
          res.Data.Data.Headers
        )
        console.log(res.Data.Data.Headers, 'tableColumns')
        console.log(this.customTableConfig.tableColumns, 'tableColumns')
        */
        /** 2023-09-18 erwin modify */
        this.customTableConfig.tableColumns = [
          // {
          //   width: 60,
          //   label: "序号",
          //   otherOptions: {
          //     type: "index",
          //     align: "center",
          //   },
          // },
          {
            label: '设备编号',
            key: 'EId',
            otherOptions: {
              fixed: 'left'
            },
          },
          {
            label: '设备名称',
            key: 'Name',
            otherOptions: {
              fixed: 'left'
            },
          },
          {
            label: '设备类型',
            key: 'EqtType',
            otherOptions: {
              fixed: 'left'
            },
          },
          {
            label: '安装位置',
            key: 'Position'
          },
          {
            label: '数据更新时间',
            key: 'Time'
          },
          {
            label: '数据类型',
            key: 'TypeDes'
            // render: (row) => {
            //   return `${row.TypeDes}(${row.Unit})`
            // }
          },
          {
            label: '数据参数',
            key: 'Value'
          },
          {
            label: '单位',
            key: 'Unit'
          }
        ]
        /** end  */
        this.customTableConfig.total = res.Data.TotalCount
      } else {
        this.$message.error(res.Message)
      }
    },
    async getDictionaryDetailListByCode(dictionaryCode = 'deviceType', Value) {
      const res = await GetDictionaryDetailListByCode({
        dictionaryCode
      })
      if (res.IsSucceed) {
        const options = [{ label: '全部', value: '' }]
        res.Data.map((item) => {
          options.push({
            label: item.Display_Name,
            value: item[Value],
            ...item
          })
        })
        return options
      }
    },
    handleEdit(index, row, type) {
      console.log(index, row, type)
      this.dialogVisible = true
      if (type === 'view') {
        this.dialogTitle = '查看'
        this.currentComponent = DialogFormLook
        this.componentsConfig = {
          ID: row.Id,
          disabled: true,
          title: '查看',
          ...row
        }
      }
      // else if (type === 'edit') {
      //   this.dialogTitle = '编辑'
      //   this.componentsConfig = {
      //     ID: row.ID,
      //     disabled: false,
      //     title: '编辑'
      //   }
      // }
    },
    async handleExport() {
      console.log(this.ruleForm)
      const res = await ExportDataList({
        Content: '',
        EqtType: '',
        Position: '',
        IsAll: false,
        Ids: this.tableSelection.map((item) => item.Id),
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '21')
      } else {
        this.$message.error(res.Message)
      }
    },
    async handleAllExport() {
      const res = await ExportDataList({
        Content: '',
        EqtType: '',
        Position: '',
        IsAll: true,
        Ids: [],
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '21')
      } else {
        this.$message.error(res.Message)
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.init()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.init()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
      if (this.tableSelection.length > 0) {
        this.customTableConfig.buttonConfig.buttonList.find(
          (v) => v.key == 'batch'
        ).disabled = false
      } else {
        this.customTableConfig.buttonConfig.buttonList.find(
          (v) => v.key == 'batch'
        ).disabled = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.layout {
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
