<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";
import dialogForm from "./dialogForm.vue";
import dayjs from "dayjs";

import {
  GetBehaviorWarningListSZCJ,
  GetBehaviorWarningEntity,
  TriggerBehaviorWarning,
} from "@/api/business/behaviorAnalysis";
import { GetDictionaryDetailListByCode } from "@/api/sys";
export default {
  name: "",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
  },
  data() {
    return {
      currentComponent: dialogForm,
      componentsConfig: {
        Data: {},
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogVisible: false,
      dialogTitle: "告警详情",
      tableSelection: [],
      ruleForm: {
        DeviceName: "",
        WarningType: "",
        HandleStatus: "",
        Date: [],
        BeginWarningTime: null,
        EndWarningTime: null,
      },
      customForm: {
        formItems: [
          {
            key: "Date", // 字段ID
            label: "告警时间", // Form的label
            type: "datePicker", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              type: "daterange",
              disabled: false,
              placeholder: "请输入...",
            },
            change: (e) => {
              // change事件
              console.log(e);
              if (e && e.length > 0) {
                this.ruleForm.BeginWarningTime = dayjs(e[0]).format(
                  "YYYY-MM-DD"
                );
                this.ruleForm.EndWarningTime = dayjs(e[1]).format("YYYY-MM-DD");
              }
            },
          },
          {
            key: "WarningType",
            label: "告警类型",
            type: "select",
            options: [],
            otherOptions: {
              clearable: true,
            },
            change: (e) => {
              // change事件
              console.log(e);
              this.GetTypesByModule();
            },
          },
          {
            key: "DeviceName",
            label: "告警设备",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
          {
            key: "HandleStatus",
            label: "状态",
            type: "select",
            options: [
              {
                label: "待广播",
                value: "1",
              },
              {
                label: "已提交",
                value: "2",
              },
              {
                label: "提交成功",
                value: "3",
              },
              {
                label: "提交失败",
                value: "4",
              },
            ],
            otherOptions: {
              clearable: true,
            },
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
        ],
        rules: {},
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          // buttonList: [
          //   {
          //     text: "批量关闭",
          //     onclick: (item) => {
          //       console.log(item);
          //       this.handleClose();
          //     },
          //   },
          // ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          // {
          //   otherOptions: {
          //     type: "selection",
          //     align: "center",
          //     fixed: "left",
          //   },
          // },
          {
            label: "告警时间",
            key: "WarningTime",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "告警类型",
            key: "WarningTypeDes",
            width: 140,
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "告警设备",
            key: "DeviceName",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "告警设备编码",
            key: "DeviceCode",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "设备地址",
            key: "Position",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "联动广播",
            key: "BroadcastEquipmentCount",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "广播时间",
            key: "BroadcastTime",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "状态",
            key: "HandleStatus",
            otherOptions: {
              align: "center",
            },
            render: (row) => {
              if (row.HandleStatus == 1) {
                return this.$createElement("span", {}, "待广播");
              } else if (row.HandleStatus == 2) {
                return this.$createElement("span", {}, "已提交");
              } else if (row.HandleStatus == 3) {
                return this.$createElement(
                  "span",
                  {
                    style: {
                      color: "green",
                    },
                  },
                  "提交成功"
                );
              } else if (row.HandleStatus == 4) {
                return this.$createElement(
                  "span",
                  {
                    style: {
                      color: "red",
                    },
                  },
                  "提交失败"
                );
              }
              return this.$createElement("span", {}, "");
            },
          },
        ],
        tableData: [],
        operateOptions: {
          align: "center",
          width: "180",
        },
        tableActions: [
          {
            actionLabel: "查看详情",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleEdit(row);
            },
          },
          {
            actionLabel: "重新广播",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleRebroadcast(row);
            },
          },
        ],
      },
    };
  },
  computed: {},
  created() {
    this.init();

    this.getDictionaryDetailListByCode();
  },
  // mixins: [getGridByCode],
  methods: {
    async handleClose() {
      const res = await SetWarningStatus({
        Status: "2",
        Ids: this.tableSelection.map((item) => item.Id),
      });
      if (res.IsSucceed) {
        this.$message.success("操作成功");
        this.onFresh();
      }
    },
    async getDictionaryDetailListByCode() {
      const res = await GetDictionaryDetailListByCode({
        dictionaryCode: "BehaviorWarningType",
      });
      if (res.IsSucceed) {
        let result = res.Data || [];
        let warningType = result.map((item) => ({
          value: item.Value,
          label: item.Display_Name,
        }));
        this.customForm.formItems.find(
          (item) => item.key == "WarningType"
        ).options = warningType;
      }
    },

    searchForm(data) {
      console.log(data);
      this.customTableConfig.currentPage = 1
      this.onFresh();
    },
    resetForm() {
      this.ruleForm.BeginWarningTime = null;
      this.ruleForm.EndWarningTime = null;
      this.ruleForm.Date = null;
      this.onFresh();
    },
    onFresh() {
      this.GetBehaviorWarningList();
    },

    init() {
      // this.getGridByCode("AccessControlAlarmDetails1");
      this.GetBehaviorWarningList();
    },
    async GetBehaviorWarningList() {
      const res = await GetBehaviorWarningListSZCJ({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data;
        this.customTableConfig.total = res.Data.TotalCount;
      } else {
        this.$message.error(res.Message);
      }
    },

    async handleRebroadcast(row) {
      const res = await TriggerBehaviorWarning({
        ID: row.Id,
      });
      if (res.IsSucceed) {
        this.$message.success("操作成功");
        this.init();
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.GetBehaviorWarningList();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.GetBehaviorWarningList();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
    handleEdit(row) {
      this.dialogVisible = true;
      this.componentsConfig.Data = row;
      this.componentsConfig = {
        type: "edit",
        data: row,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
</style>
