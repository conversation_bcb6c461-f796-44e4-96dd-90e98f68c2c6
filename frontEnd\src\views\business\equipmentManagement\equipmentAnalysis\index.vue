<template>
  <div class="app-container abs100 equipmentAnalysis">
    <el-row :gutter="12">
      <el-col :span="24">
        <equipmentOperationStatus
          v-loading="deviceStatusListLoading"
          :component-data="{
            deviceStatusList: deviceStatusList,
          }"
        />
        <!-- <el-card shadow="never" v-loading="deviceStatusListLoading">
          <equipmentOperationStatus
            :componentData="{
              deviceStatusList: deviceStatusList,
            }"
          ></equipmentOperationStatus>
        </el-card> -->
      </el-col>

    </el-row>
    <el-row :gutter="12" style="margin-top: 10px">
      <el-col :span="24">
        <el-card v-loading="rgvDataListLoading" shadow="never">
          <div slot="header" class="header">
            <span>AGV电量实时监测</span>
          </div>
          <agvBatteryLevel
            style="margin-top: -20px"
            :component-data="{
              rgvDataList: rgvDataList,
            }"
          />
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="12" style="margin-top: 10px">
      <el-col :span="12">
        <el-card
          v-loading="equipmentAbnormalityRankingDataLoading"
          shadow="never"
        >
          <div slot="header" class="header">
            <span>设备异常情况排行</span>
          </div>
          <div style="margin-top: -20px">
            <el-table
              :data="equipmentAbnormalityRankingData"
              style="width: 100%"
              height="240"
              :highlight-current-row="false"
              :row-class-name="rowClassName"
              :header-row-class-name="HeaderRowClassName"
              :cell-class-name="cellClassName"
            >
              <el-table-column label="排名" width="60">
                <template slot-scope="scope">
                  <div
                    v-if="scope.$index < 3"
                    class="tablenumber"
                    :style="{
                      backgroundImage:
                        'url(' +
                        require(`@/assets/no_${scope.$index + 1}.png`) +
                        ')',
                    }"
                  >
                    <span> {{ scope.$index + 1 }}</span>
                  </div>
                  <div v-else class="tablenumber">
                    <span> {{ scope.$index + 1 }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="Name" label="设备名称" />
              <el-table-column prop="Area" label="所属车间" />
              <el-table-column
                prop="ErrorTime"
                label="设备异常时间"
                width="140"
              />
              <el-table-column
                prop="StartTime"
                label="设备开机时间"
                width="140"
              />
              <el-table-column prop="ErrPercent" label="异常率" width="100" />
            </el-table>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card v-loading="latestAlarmInformationDataLoading" shadow="never">
          <div slot="header" class="header">
            <span>最新告警消息</span>
          </div>
          <div style="margin-top: -20px">
            <el-table
              :data="latestAlarmInformationData"
              style="width: 100%"
              height="240"
              :show-header="true"
              :highlight-current-row="false"
              :row-class-name="rowClassName"
              :header-row-class-name="HeaderRowClassName"
              :cell-class-name="cellClassName"
            >
              <el-table-column prop="Time" label="告警时间" width="160">
                <template slot-scope="scope">
                  <span>{{ scope.row.Time || "-" }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="Name" label="告警设备">
                <template slot-scope="scope">
                  <span>{{ scope.row.Name || "-" }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="StatusDes" label="设备状态" width="90">
                <template slot-scope="scope">
                  <span>{{ scope.row.StatusDes || "-" }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="Brand" label="设备品牌" width="100">
                <template slot-scope="scope">
                  <span>{{ scope.row.Brand || "-" }}</span>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="Content" label="异常信息" width="90">
                <template slot-scope="scope">
                  <span>{{ scope.row.Content || "-" }}</span>
                </template>
              </el-table-column> -->
              <el-table-column label="" width="40">
                <template slot-scope="scope">
                  <el-popover placement="bottom-end" width="" trigger="hover">
                    <div class="popover_latestAlarmInformation">
                      <div class="item" style="padding: 4px 0px; display: flex">
                        <span
                          style="
                            font-weight: 500;
                            font-size: 14px;
                            color: #999999;
                            width: 74px;
                          "
                        >告警时间</span>
                        <span
                          style="
                            font-weight: 500;
                            font-size: 14px;
                            color: #333333;
                          "
                        >{{ scope.row.Time || "-" }}</span>
                      </div>
                      <div class="item" style="padding: 4px 0px; display: flex">
                        <span
                          style="
                            font-weight: 500;
                            font-size: 14px;
                            color: #999999;
                            width: 74px;
                          "
                        >告警设备</span>
                        <span
                          style="
                            font-weight: 500;
                            font-size: 14px;
                            color: #333333;
                          "
                        >{{ scope.row.Name || "-" }}</span>
                      </div>
                      <div class="item" style="padding: 4px 0px; display: flex">
                        <span
                          style="
                            font-weight: 500;
                            font-size: 14px;
                            color: #999999;
                            width: 74px;
                          "
                        >设备状态</span>
                        <span
                          style="
                            font-weight: 500;
                            font-size: 14px;
                            color: #333333;
                          "
                        >{{ scope.row.StatusDes || "-" }}</span>
                      </div>
                      <div class="item" style="padding: 4px 0px; display: flex">
                        <span
                          style="
                            font-weight: 500;
                            font-size: 14px;
                            color: #999999;
                            width: 74px;
                          "
                        >设备品牌</span>
                        <span
                          style="
                            font-weight: 500;
                            font-size: 14px;
                            color: #333333;
                          "
                        >{{ scope.row.Brand || "-" }}</span>
                      </div>
                      <div class="item" style="padding: 4px 0px; display: flex">
                        <span
                          style="
                            font-weight: 500;
                            font-size: 14px;
                            color: #999999;
                            width: 74px;
                          "
                        >异常信息</span>
                        <span
                          style="
                            font-weight: 500;
                            font-size: 14px;
                            color: #333333;
                          "
                        >{{ scope.row.Content || "-" }}</span>
                      </div>
                    </div>
                    <div
                      slot="reference"
                      style="
                        height: 100%;
                        display: flex;
                        justify-content: center;
                      "
                    >
                      <i class="el-icon-arrow-right" />
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="12" style="margin-top: 10px">
      <el-col :span="18">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-card v-loading="productionVolumeTrendLoading" shadow="never">
              <div slot="header" class="header">
                <span>能效（电）分析</span>
                <span
                  class="right"
                  @click="productionVolumeTrendClick"
                >更多<i class="el-icon-arrow-right" /></span>
              </div>
              <div class="chartCardConent">
                <div class="chartCardItem">
                  <el-select
                    v-model="productionVolumeTrendValue"
                    :clearable="true"
                    placeholder="请选择"
                    @change="productionVolumeTrendChange"
                  >
                    <el-option
                      v-for="item in productionVolumeTrendSelectOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <efficiencyAnalysis
                  :component-data="{
                    productionVolumeTrendOptions: productionVolumeTrendOptions,
                  }"
                />
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card v-loading="equipmentFailureTrendLoading" shadow="never">
              <div slot="header" class="header">
                <span>设备故障</span>
                <span
                  class="right"
                  @click="equipmentFailureTrendClick"
                >更多<i class="el-icon-arrow-right" /></span>
              </div>
              <div class="chartCardConent">
                <div class="chartCardItem">
                  <el-select
                    v-model="equipmentFailureTrendValue"
                    placeholder="请选择"
                    :clearable="true"
                    @change="equipmentFailureTrendChange"
                  >
                    <el-option
                      v-for="item in equipmentFailureTrendSelectOptions"
                      :key="item.Id"
                      :label="item.Display_Name"
                      :value="item.Id"
                    />
                  </el-select>
                </div>
                <equipmentFailure
                  :component-data="{
                    equipmentFailureTrendOptions: equipmentFailureTrendOptions,
                  }"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-top: 10px">
          <el-col :span="12">
            <el-card
              v-loading="rankProdEquipmentRateMonthLoading"
              shadow="never"
            >
              <div slot="header" class="header">
                <span>本月生产设备负载率排行
                  <el-tooltip
                    class="item"
                    content="负载率=设备运行时间/设备开机时间"
                    placement="top-start"
                  >
                    <img src="@/assets/tooltip.png" alt="">
                  </el-tooltip>
                </span>
                <span
                  class="unit"
                >单位：{{ rankProdEquipmentRateMonth.Unit }}</span>
              </div>
              <customProcess
                :component-data="{ list: rankProdEquipmentRateMonth.Charts }"
              />
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card
              v-loading="
                monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading
              "
              shadow="never"
            >
              <div slot="header" class="header">
                <span>本月焊丝使用效率
                  <el-tooltip
                    class="item"
                    content="负载率=设备运行时间/设备开机时间"
                    placement="top-start"
                  >
                    <template #content>
                      <p>1.使用效率=使用量/运行时间（小时）</p>
                      <p>2.使用量单位标识： -焊剂KG -焊丝KG -导电嘴（个）</p>
                    </template>
                    <img src="@/assets/tooltip.png" alt="">
                  </el-tooltip>
                </span>
                <span
                  class="unit"
                >单位：{{
                  monthRankWeldingEquipmentConsumablesUsageEfficiency.Unit
                }}</span>
              </div>
              <customProcess
                :component-data="{
                  list: monthRankWeldingEquipmentConsumablesUsageEfficiency.Charts,
                }"
              />
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-top: 10px">
          <el-col :span="12">
            <el-row :gutter="12">
              <el-col :span="8">
                <el-card
                  v-loading="maintenanceWorkOrderProcessingStatusLoading"
                  shadow="never"
                >
                  <div slot="header" class="header">
                    <span>维修工单</span>
                  </div>
                  <maintenanceWorkOrder
                    :component-data="{
                      data: maintenanceWorkOrderProcessingStatusOne,
                    }"
                  />
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card
                  v-loading="maintenanceWorkOrderProcessingStatusLoading"
                  shadow="never"
                >
                  <div slot="header" class="header">
                    <span>维保工单</span>
                  </div>
                  <maintenanceWorkOrder
                    :component-data="{
                      data: maintenanceWorkOrderProcessingStatusTwo,
                    }"
                  />
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card v-loading="workOrderErrorTypeLoading" shadow="never">
                  <div slot="header" class="header">
                    <span>维修故障类型</span>
                  </div>
                  <repairFaultType
                    :component-data="{ workOrderErrorTypeList }"
                  />
                </el-card>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="12">
            <el-card
              v-loading="equipmentMaintenanceIntegrityRateLoading"
              shadow="never"
            >
              <div slot="header" class="header">
                <span>设备维修完好率</span>
                <el-date-picker
                  v-model="equipmentMaintenanceReadinessRateMonth"
                  size="small"
                  type="month"
                  placeholder="选择月"
                  :clearable="false"
                  :editable="false"
                  @change="equipmentMaintenanceReadinessRateMonthChange"
                />
              </div>
              <equipmentIntegrityRate
                :component-data="{ data: equipmentMaintenanceIntegrityRateObj }"
              />
            </el-card>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="6">
        <el-card v-loading="equipmentOperationListLoading" shadow="never">
          <div slot="header" class="header">
            <span>本月设备运行</span>
            <el-select
              v-model="equipmentOperationValue"
              :clearable="true"
              placeholder="请选择"
              @change="equipmentOperationChange"
            >
              <el-option
                v-for="item in equipmentStartupStatusMonthOptions"
                :key="item.Id"
                :label="item.Display_Name"
                :value="item.Id"
              />
            </el-select>
          </div>
          <equipmentOperation
            :component-data="{ data: equipmentOperationList }"
          />
        </el-card>
      </el-col>
    </el-row>

    <el-dialog
      v-dialogDrag
      :title="currentComponentTitle"
      :visible.sync="dialogVisible"
      custom-class="dialogCustomClass"
    >
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :component-data="componentsConfig"
        :components-funs="componentsFuns"
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  GetStatusAnalyseEqt,
  GetErrorRank,
  GetTopEqtError,
  GetStartAnalyseEqt,
  GetLoadAnalyseEqt,
  GetConsumptionAnalyseEqt,
  GetDeviceAnalyseWorkOrderCount,
  GetWorkOrderHandlingCount,
  GetWorkOrderErrorTypeList,
  GetDeviceServiceabilityRate,
  GetAGVAnalyseEqt,
  GetDictionaryDetailListByParentId,
  GetProduceTrend,
  GetDeviceStatusDetails,
  GetStartAnalyseEqtDetails
} from '@/api/business/eqptAsset'

import equipmentIntegrityRate from './components/equipmentIntegrityRate'
import equipmentOperation from './components/equipmentOperation'
import repairFaultType from './components/repairFaultType'
import customProcess from './components/customProcess'
import loadRateRanking from './components/loadRateRanking'
import maintenanceWorkOrder from './components/maintenanceWorkOrder'
import equipmentOperationStatus from './components/equipmentOperationStatus'
import agvBatteryLevel from './components/agvBatteryLevel'
import efficiencyAnalysis from './components/efficiencyAnalysis'
import equipmentFailure from './components/equipmentFailure'

import efficiencyAnalysisDetail from './components/efficiencyAnalysisDetail'
import equipmentFailureDetail from './components/equipmentFailureDetail'
import { GetEquipmentAssetPageListPJ } from '@/api/business/eqptAsset'
import dayjs from 'dayjs'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart, LineChart, PieChart } from 'echarts/charts'
import {
  GridComponent,
  LegendComponent,
  TooltipComponent,
  TitleComponent,
  DataZoomComponent
} from 'echarts/components'
use([
  CanvasRenderer,
  BarChart,
  LineChart,
  PieChart,
  DataZoomComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent
])
export default {
  name: 'EquipmentAnalysis',
  components: {
    VChart,
    equipmentOperation,
    equipmentIntegrityRate,
    repairFaultType,
    customProcess,
    loadRateRanking,
    maintenanceWorkOrder,
    equipmentOperationStatus,
    agvBatteryLevel,
    efficiencyAnalysis,
    equipmentFailure
  },
  mixins: [],
  data() {
    return {
      currentComponent: null,
      componentsConfig: {},
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.fetchData()
        }
      },
      dialogVisible: false,
      currentComponentTitle: '',
      //
      deviceStatusListLoading: false,
      deviceStatusList: [],
      // 设备运行状况
      equipmentOperationStatus: [],
      // RGV电量实时监测
      rgvDataListLoading: false,
      rgvDataList: [],
      // 本月设备维修完好率
      equipmentMaintenanceIntegrityRateLoading: false,
      equipmentMaintenanceReadinessRateMonth: dayjs(new Date()).format(
        'YYYY-MM'
      ),
      // 维修工单处理情况
      maintenanceWorkOrderProcessingStatusLoading: false,
      maintenanceWorkOrderProcessingStatusOne: [],
      // 维保工单处理情况
      maintenanceWorkOrderProcessingStatusTwo: [],
      // 本月焊接设备耗材使用效率排行
      monthRankWeldingEquipmentConsumablesUsageEfficiencyValue:
        'WireConsumption',
      // 本月焊丝使用效率
      rankProdEquipmentRateMonthLoading: false,
      rankProdEquipmentRateMonth: {},
      // 本月焊接设备耗材使用效率排行
      monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading: false,
      monthRankWeldingEquipmentConsumablesUsageEfficiency: {},
      // 设备异常情况排行
      equipmentAbnormalityRankingDataLoading: false,
      equipmentAbnormalityRankingData: [],
      //
      latestAlarmInformationDataLoading: false,
      latestAlarmInformationData: [],
      // 本月设备开机情况
      equipmentStartupStatusMonthValue: '',
      equipmentOperationValue: '',
      equipmentStartupStatusMonthOptions: [],

      // 能效（电）分析
      productionVolumeTrendValue: '',
      productionVolumeTrendLoading: false,
      productionVolumeTrendSelectOptions: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '一车间',
          value: '一车间'
        },
        {
          label: '二车间',
          value: '二车间'
        },
        {
          label: '配送中心',
          value: '配送中心'
        }
      ],
      productionVolumeTrendOptions: {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          show: false
          // top: "0",
          // left: "0",
          // itemWidth: 10,
          // itemHeight: 5,
          // icon: "rect",
          // textStyle: {
          //   fontSize: 12,
          //   color: "#999999",
          // },
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        yAxis: [
          {
            name: '用电量(kW·h)',
            type: 'value',
            position: 'left',
            axisLabel: {
              formatter: '{value}',
              textStyle: {
                color: '#298DFF' // 设置 y 轴标签文字颜色为红色
              }
            },
            nameTextStyle: {
              color: '#298DFF',
              fontSize: '13px'
            }
            // logBase: 10,
          },
          {
            name: '生产产量(t)',
            type: 'value',
            // min: 0,
            // max: 25,
            // interval: 5,
            axisLabel: {
              formatter: '{value}',
              textStyle: {
                color: '#FF902C' // 设置 y 轴标签文字颜色为红色
              }
            },
            nameTextStyle: {
              color: '#FF902C',
              fontSize: '13px'
            }
          }
        ],
        color: ['#298DFF', '#FF902C'],
        series: [
          {
            name: '用电量',
            // symbol: "none",
            data: [],
            // icon: 'rect',
            // yAxisIndex: 1,
            tooltip: {
              valueFormatter: function(value) {
                return `${value || 0}` + ' kW·h'
              }
            },
            type: 'line'
          },
          {
            name: '生产产量',
            // symbol: "none",
            data: [],
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: function(value) {
                return `${value || 0}` + ' t'
              }
            },
            // icon: 'rect',
            type: 'line'
          }
        ]
      },
      // 设备故障
      equipmentFailureTrendValue: '',
      equipmentFailureTrendSelectOptions: [],
      equipmentFailureTrendLoading: false,
      equipmentFailureTrendOptions: {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },

        color: ['rgba(41, 141, 255, 1)'],
        yAxis: [
          {
            type: 'value',
            position: 'left',
            logBase: 10
          }
        ],
        series: [
          {
            smooth: true,
            // symbol: "none",
            data: [],
            type: 'line',
            tooltip: {
              valueFormatter: function(value) {
                return `${value || 0}` + ' 个'
              }
            }
            // areaStyle: {
            //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            //     {
            //       offset: 0,
            //       color: "rgba(62, 204, 147, .5)",
            //     },
            //     {
            //       offset: 1,
            //       color: "rgba(57, 133, 238, 0)",
            //     },
            //   ]),
            // },
          }
        ]
      },
      // 本月设备维修完好率
      equipmentMaintenanceIntegrityRateObj: {},
      workOrderErrorTypeLoading: false,
      workOrderErrorTypeList: [],
      equipmentOperationListLoading: false,
      equipmentOperationList: []
    }
  },
  activated() {},
  mounted() {
    // 获取设备
    this.getEquipmentAssetPageListPJ()
    // 获取设备子类
    this.getDictionaryDetailListByParentId()
    // 获取设备数采分析设备运行状况
    this.getStatusAnalyseEqt()
    // 获取设备异常情况排行
    this.getErrorRank()
    // 获取设备数采异常信息
    this.getTopEqtError()
    // // 获取设备数采分析开机时间分析
    // this.getStartAnalyseEqt();
    // 获取设备数采分析负载率分析
    this.getLoadAnalyseEqt()
    // 获取本月耗材设备使用效率排行
    this.getConsumptionAnalyseEqt()
    // 获取设备故障
    this.getDeviceAnalyseWorkOrderCount()
    // 获取工单处理情况
    this.getWorkOrderHandlingCount()
    // 获取工单类型故障次数
    this.getWorkOrderErrorTypeList()
    // 获取设备完好率
    this.getDeviceServiceabilityRate()
    // 获取AGV运行状态
    this.getAGVAnalyseEqt()
    // 获取能效（电）分析
    this.getProduceTrend()
    // 获取设备状态
    this.getDeviceStatusDetails()
    // 获取设备运行
    this.getStartAnalyseEqtDetails()
  },
  methods: {
    // 打开 能效（电）分析
    productionVolumeTrendClick() {
      this.currentComponentTitle = '能效（电）分析'
      this.dialogVisible = true
      this.currentComponent = efficiencyAnalysisDetail
      this.componentsConfig = {
        selectOtherOptions: this.productionVolumeTrendSelectOptions
      }
    },
    // 打开 设备故障
    equipmentFailureTrendClick() {
      this.currentComponentTitle = '设备故障'
      this.dialogVisible = true
      this.currentComponent = equipmentFailureDetail
      this.componentsConfig = {
        selectOtherOptions: this.equipmentFailureTrendSelectOptions
      }
    },
    // 获取设备状态
    async getDeviceStatusDetails() {
      this.deviceStatusListLoading = false
      const res = await GetDeviceStatusDetails({})
      if (res.IsSucceed) {
        this.deviceStatusList = res.Data
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
      this.deviceStatusListLoading = false
    },
    // 获取设备
    async getEquipmentAssetPageListPJ() {
      const res = await GetEquipmentAssetPageListPJ({
        Display_Name: '',
        Device_Type_Id: 'cc81fe8a-f0a3-40c1-abf6-f553fa502a33',
        Device_Type_Detail_Id: '',
        Department: '',
        Page: 1,
        PageSize: 100
      })
      this.equipmentFailureTrendSelectOptions = res.Data.Data.filter(
        (item) => item.IsShow == 1
      )
      this.equipmentFailureTrendSelectOptions.unshift({
        Display_Name: '全部',
        Id: ''
      })
    },
    // 获取设备子类
    async getDictionaryDetailListByParentId() {
      const res = await GetDictionaryDetailListByParentId(
        'cc81fe8a-f0a3-40c1-abf6-f553fa502a33'
      )
      this.equipmentStartupStatusMonthOptions = res.Data
      this.equipmentStartupStatusMonthOptions.unshift({
        Display_Name: '全部',
        Id: ''
      })
    },
    // 能效（电）分析
    async getProduceTrend() {
      this.productionVolumeTrendLoading = false
      const res = await GetProduceTrend({
        NodeName: this.productionVolumeTrendValue
      })

      this.productionVolumeTrendOptions.xAxis.data = res.Data.map(
        (item) => item.Key
      )
      this.productionVolumeTrendOptions.series[0].data = res.Data.map(
        (item) => item.Electric
      )
      this.productionVolumeTrendOptions.series[1].data = res.Data.map(
        (item) => item.Produce
      )
      this.productionVolumeTrendLoading = false
    },
    // 获取设备数采分析设备运行状况
    async getStatusAnalyseEqt() {
      const res = await GetStatusAnalyseEqt({})
      this.equipmentOperationStatus = res.Data
    },
    // 获取设备异常情况排行
    async getErrorRank() {
      this.equipmentAbnormalityRankingDataLoading = true
      const res = await GetErrorRank({})

      this.equipmentAbnormalityRankingData = res.Data
      this.equipmentAbnormalityRankingDataLoading = false
    },
    // 获取设备数采异常信息
    async getTopEqtError() {
      this.latestAlarmInformationDataLoading = true

      const res = await GetTopEqtError({})
      this.latestAlarmInformationData = res.Data.slice(0, 5)
      this.latestAlarmInformationDataLoading = false
    },
    // 获取设备数采分析开机时间分析
    // async getStartAnalyseEqt() {
    //   let res = await GetStartAnalyseEqt({
    //     ID: this.equipmentStartupStatusMonthValue,
    //   });
    // },
    // 获取设备数采分析负载率分析
    async getLoadAnalyseEqt() {
      this.rankProdEquipmentRateMonthLoading = true
      const res = await GetLoadAnalyseEqt({})
      this.rankProdEquipmentRateMonth = res.Data
      this.rankProdEquipmentRateMonthLoading = false
    },
    // 获取本月耗材设备使用效率排行
    async getConsumptionAnalyseEqt() {
      this.monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading = true
      const res = await GetConsumptionAnalyseEqt({
        Content: this.monthRankWeldingEquipmentConsumablesUsageEfficiencyValue
      })
      this.monthRankWeldingEquipmentConsumablesUsageEfficiency = res.Data
      this.monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading = false
    },
    // 获取设备故障
    async getDeviceAnalyseWorkOrderCount() {
      this.equipmentFailureTrendLoading = true
      const res = await GetDeviceAnalyseWorkOrderCount({
        ID: this.equipmentFailureTrendValue,
        Time: ''
      })
      this.equipmentFailureTrendOptions.xAxis.data = res.Data.map(
        (item) => item.Label
      )
      this.equipmentFailureTrendOptions.series[0].data = res.Data.map(
        (item) => item.Value
      )
      this.equipmentFailureTrendLoading = false
    },

    // 获取工单处理情况
    async getWorkOrderHandlingCount() {
      this.maintenanceWorkOrderProcessingStatusLoading = true
      const res = await GetWorkOrderHandlingCount({})
      this.maintenanceWorkOrderProcessingStatusOne = res.Data.Reconditions
      this.maintenanceWorkOrderProcessingStatusTwo = res.Data.Maintenances
      this.maintenanceWorkOrderProcessingStatusLoading = false
    },

    // 获取工单类型故障次数
    async getWorkOrderErrorTypeList() {
      this.workOrderErrorTypeLoading = true
      const res = await GetWorkOrderErrorTypeList({})
      this.workOrderErrorTypeList = res.Data.map((item) => ({
        Value: item.Rate,
        Label: item.Type
      }))
      this.workOrderErrorTypeLoading = false
    },
    // 获取设备完好率
    async getDeviceServiceabilityRate() {
      this.equipmentMaintenanceIntegrityRateLoading = true
      const res = await GetDeviceServiceabilityRate({
        Time: dayjs(this.equipmentMaintenanceReadinessRateMonth).format(
          'YYYY-MM'
        )
      })
      this.equipmentMaintenanceIntegrityRateObj = res.Data
      this.equipmentMaintenanceIntegrityRateLoading = false
    },

    // 获取AGV运行状态
    async getAGVAnalyseEqt() {
      this.rgvDataListLoading = true
      const res = await GetAGVAnalyseEqt({})
      this.rgvDataList = res.Data
      this.rgvDataListLoading = false
    },
    // 设置表格颜色
    latestAlarmInformationDataClassName({ row, rowIndex }) {
      if (this.isEvenOrOdd(rowIndex)) {
        return 'row-one'
      } else {
        return 'row-two'
      }
    },

    HeaderRowClassName({ row, rowIndex }) {
      return 'row-header'
    },
    cellClassName({ row, rowIndex }) {
      return 'row-body'
    },

    // 设置表格颜色
    rowClassName({ row, rowIndex }) {
      if (this.isEvenOrOdd(rowIndex + 1)) {
        return 'row-one'
      } else {
        return 'row-two'
      }
    },

    productionVolumeTrendChange() {
      this.getProduceTrend()
    },
    equipmentFailureTrendChange() {
      this.getDeviceAnalyseWorkOrderCount()
    },
    equipmentMaintenanceReadinessRateMonthChange() {
      this.getDeviceServiceabilityRate()
    },
    handleClick(tab, event) {},
    //  判断是否是偶数行 还是奇数行
    isEvenOrOdd(number) {
      if (number % 2 === 0) {
        return true
      } else {
        return false
      }
    },
    // 设备运行
    async getStartAnalyseEqtDetails() {
      this.equipmentOperationListLoading = true
      const res = await GetStartAnalyseEqtDetails({
        ID: this.equipmentOperationValue
      })
      if (res.IsSucceed) {
        this.equipmentOperationList = res.Data
      }
      this.equipmentOperationListLoading = false
    },
    equipmentOperationChange() {
      this.getStartAnalyseEqtDetails()
    }
  }
}
</script>

<style lang="scss" scoped>
.equipmentAnalysis {
  // padding: 10px 15px;
  overflow-y: scroll;
  // height: calc(100vh - 96px);
  .dialogCustomClass {
    .formBox {
      height: 580px;
      padding: 0 16px;
      &::-webkit-scrollbar {
        display: none;
      }
    }
    .costomTitle {
      display: flex;
      align-items: center;
      color: #333;
      margin-bottom: 16px;
      span {
        display: inline-block;
        width: 2px;
        height: 14px;
        background: #009dff;
        margin-right: 6px;
      }
    }
    .dialogButton {
      display: flex;
      justify-content: flex-end;
      border-top: 1px solid #d0d3db;
      padding-top: 16px;
    }
  }
  .header {
    display: flex;
    // align-items: center;
    justify-content: space-between;
    height: 22px;
    > span {
      font-weight: bold;
    }
    .right {
      font-family: Helvetica, Helvetica;
      font-weight: bold;
      font-size: 14px;
      color: #298cfc;
      line-height: 0px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      cursor: pointer;
    }

    .unit {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      font-style: normal;
      text-transform: none;
    }
  }
  .chartCardConent {
    margin-top: -30px;
    .chartCardItem {
      display: flex;
      justify-content: flex-end;
    }
  }
  .content {
    display: flex;
    align-items: center;
    flex-direction: row;

    .left {
      width: 50%;
      .title {
        font-weight: bold;
      }
    }
    .right {
      width: 50%;
      .title {
        font-weight: bold;
      }
    }
  }

  .tablenumber {
    width: 30px;
    height: 23px;
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    > span {
      margin-top: 10px;
      font-weight: 500;
    }
  }

  ::v-deep .popover_latestAlarmInformation {
    .item {
      padding: 4px 0px;
      display: flex;
      span:first-of-type {
        font-weight: 500;
        font-size: 14px;
        color: #999999;
        width: 74px;
      }
      span:last-of-type {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
      }
    }
  }

  ::v-deep .el-card__header {
    border-bottom: none !important;
  }
  ::v-deep .el-progress__text {
    font-size: 18px !important;
    color: #666666 !important;
    // font-weight: bold;
  }
  ::v-deep.el-table .row-one {
    background: rgba(41, 141, 255, 0.03) !important;
  }

  ::v-deep .el-table .row-two {
    background: rgba(255, 255, 255, 1) !important;
  }

  ::v-deep .el-table .row-header {
    color: #333333 !important;
    font-weight: 500;
  }
  ::v-deep .el-table .row-body {
    color: #333333 !important;
    border: none !important;
  }
  ::v-deep .el-card {
    border: none !important;
  }
  ::v-deep .el-table::before {
    background: none !important;
  }
  ::v-deep .el-table th.is-leaf {
    border-bottom: none !important;
  }
}
</style>
