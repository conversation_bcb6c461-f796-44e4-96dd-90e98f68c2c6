{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\myTasks\\index.vue?vue&type=style&index=0&id=5bddab14&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\myTasks\\index.vue", "mtime": 1755506574396}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCiogewogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7Cn0KCi5sYXlvdXQgewogIGhlaWdodDogMTAwJTsKICB3aWR0aDogMTAwJTsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgOjp2LWRlZXAgewogICAgLkN1c3RvbUxheW91dCB7CiAgICAgIC5sYXlvdXRUYWJsZSB7CiAgICAgICAgaGVpZ2h0OiAwOwogICAgICAgIC5DdXN0b21UYWJsZSB7CiAgICAgICAgICBoZWlnaHQ6IDEwMCU7CiAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgICAgICAgIC50YWJsZSB7CiAgICAgICAgICAgIGZsZXg6IDE7CiAgICAgICAgICAgIGhlaWdodDogMDsKICAgICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgICAgICAgICAgLmVsLXRhYmxlIHsKICAgICAgICAgICAgICBmbGV4OiAxOwogICAgICAgICAgICAgIGhlaWdodDogMDsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4TA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/myWorkBench/myTasks", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n        >\n          <!-- <template #customBtn=\"{slotScope}\"><el-button type=\"text\" @click=\"goHandelAlarm(slotScope)\">去处�?/el-button></template> -->\n        </CustomTable>\n      </template>\n    </CustomLayout>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\nimport {\n  GetTaskPageList,\n  GetTaskType,\n  GetTaskStatus\n} from '@/api/business/myWorkBench'\nexport default {\n  name: 'MyTasks',\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout\n  },\n  data() {\n    return {\n      ruleForm: {\n        Name: '',\n        Type: '',\n        StartTime: null,\n        EndTime: null,\n        Status: null,\n        Date: []\n      },\n      taskTypeData: [],\n      customForm: {\n        formItems: [\n          {\n            key: 'Name',\n            label: '任务名称',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            width: '240px',\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'Type',\n            label: '任务类型',\n            type: 'select',\n            otherOptions: {\n              clearable: true\n            },\n            options: [\n\n            ],\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'Status',\n            label: '任务状�?,\n            type: 'select',\n            otherOptions: {\n              clearable: true\n            },\n            options: [],\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'Date',\n            label: '开始时�?,\n            type: 'datePicker',\n            otherOptions: {\n              type: 'datetimerange',\n              rangeSeparator: '�?,\n              startPlaceholder: '开始日�?,\n              endPlaceholder: '结束日期',\n              clearable: true,\n              valueFormat: 'yyyy-MM-dd HH:mm'\n            },\n            change: (e) => {\n              console.log(e)\n              if (e && e.length !== 0) {\n                this.ruleForm.StartTime = e[0]\n                this.ruleForm.EndTime = e[1]\n              } else {\n                this.ruleForm.StartTime = null\n                this.ruleForm.EndTime = null\n              }\n            }\n          }\n        ],\n        rules: {},\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        loading: false,\n        buttonConfig: {\n          buttonList: [\n\n          ]\n        },\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        height: '100%',\n        tableColumns: [\n          {\n            label: '任务状�?,\n            key: 'StatusName'\n          },\n          {\n            label: '任务类型',\n            key: 'Type'\n          },\n          {\n            label: '任务名称',\n            key: 'Name'\n          },\n          {\n            label: '来源',\n            key: 'Source'\n          },\n          {\n            label: '业务模块',\n            key: 'Module'\n          },\n          {\n            label: '任务开始时�?,\n            key: 'StartTime'\n          },\n          {\n            label: '计划完成时间',\n            key: 'EndTime'\n          },\n          {\n            label: '实际完成时间',\n            key: 'DoneTime'\n          }\n        ],\n        tableData: [],\n        tableActions: [\n          {\n            actionLabel: '去处�?,\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.goHandelAlarm(row)\n            }\n          }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.getTaskType()\n    this.getTaskStatus()\n    this.onFresh()\n  },\n  methods: {\n    // 获取任务类型\n    getTaskType() {\n      GetTaskType({}).then((res) => {\n        if (res.IsSucceed) {\n          const arr = []\n          const data = res.Data || null\n          data.forEach((item) => {\n            const obj = {\n              label: item.Name,\n              value: item.Value\n            }\n            arr.push(obj)\n          })\n          this.customForm.formItems.find((v) => v.key == 'Type').options = arr\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    // 获取任务类型\n    getTaskStatus() {\n      this.customTableConfig.loading = true\n      GetTaskStatus({}).then((res) => {\n        if (res.IsSucceed) {\n          const arr = []\n          const data = res.Data || null\n          data.forEach((item) => {\n            const obj = {\n              label: item.Name,\n              value: item.Value\n            }\n            arr.push(obj)\n          })\n          this.customForm.formItems.find((v) => v.key == 'Status').options = arr\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      }).finally(() => {\n        this.customTableConfig.loading = false\n      })\n    },\n    searchForm(data) {\n      console.log(data)\n      this.onFresh()\n    },\n    resetForm() {\n      this.onFresh()\n    },\n    onFresh() {\n      this.fetchData()\n    },\n    async fetchData() {\n      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {\n        this.ruleForm.StartTime = null\n        this.ruleForm.EndTime = null\n      }\n      await GetTaskPageList({\n        ...this.ruleForm,\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.customTableConfig.tableData = res.Data.Data\n          this.customTableConfig.total = res.Data.TotalCount\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n\n    // 去处理路由跳�?    goHandelAlarm(item) {\n      // this.$router.push({ name: item.Code })\n      console.log(item, 'item')\n      if (item.Url) {\n        let platform = '' // 子应�?        if (item.ModuleName == '后台设置') {\n          platform = 'management'\n        } else {\n          platform = 'digitalfactory'\n        }\n        this.$qiankun.switchMicroAppFn(\n          platform,\n          item.ModuleCode,\n          item.ModuleId,\n          item.Url\n        )\n      } else {\n        const platform = 'digitalfactory'\n        const code = 'szgc'\n        const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\n        let url = ''\n        if (item.Module == '环境管理') {\n          url = '/business/environment/alarmInformation'\n        } else\n        if (item.Module == '访客管理') {\n          url = '/business/visitorList'\n          console.log('访客管理')\n        }\n        this.$qiankun.switchMicroAppFn(platform, code, id, url)\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.onFresh()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`)\n      this.customTableConfig.currentPage = val\n      this.onFresh()\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n* {\n  box-sizing: border-box;\n}\n\n.layout {\n  height: 100%;\n  width: 100%;\n  position: absolute;\n  ::v-deep {\n    .CustomLayout {\n      .layoutTable {\n        height: 0;\n        .CustomTable {\n          height: 100%;\n          display: flex;\n          flex-direction: column;\n          .table {\n            flex: 1;\n            height: 0;\n            display: flex;\n            flex-direction: column;\n            .el-table {\n              flex: 1;\n              height: 0;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}