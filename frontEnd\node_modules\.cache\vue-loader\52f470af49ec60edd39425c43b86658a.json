{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\myTasks\\index.vue?vue&type=style&index=0&id=5bddab14&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\myTasks\\index.vue", "mtime": 1755674552430}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCiogew0KICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KfQ0KDQoubGF5b3V0IHsNCiAgaGVpZ2h0OiAxMDAlOw0KICB3aWR0aDogMTAwJTsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICA6OnYtZGVlcCB7DQogICAgLkN1c3RvbUxheW91dCB7DQogICAgICAubGF5b3V0VGFibGUgew0KICAgICAgICBoZWlnaHQ6IDA7DQogICAgICAgIC5DdXN0b21UYWJsZSB7DQogICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgICAgICAudGFibGUgew0KICAgICAgICAgICAgZmxleDogMTsNCiAgICAgICAgICAgIGhlaWdodDogMDsNCiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgICAgICAgLmVsLXRhYmxlIHsNCiAgICAgICAgICAgICAgZmxleDogMTsNCiAgICAgICAgICAgICAgaGVpZ2h0OiAwOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8TA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/myWorkBench/myTasks", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n        >\r\n          <!-- <template #customBtn=\"{slotScope}\"><el-button type=\"text\" @click=\"goHandelAlarm(slotScope)\">去处理</el-button></template> -->\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport {\r\n  GetTaskPageList,\r\n  GetTaskType,\r\n  GetTaskStatus\r\n} from '@/api/business/myWorkBench'\r\nexport default {\r\n  name: 'MyTasks',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        Name: '',\r\n        Type: '',\r\n        StartTime: null,\r\n        EndTime: null,\r\n        Status: null,\r\n        Date: []\r\n      },\r\n      taskTypeData: [],\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Name',\r\n            label: '任务名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Type',\r\n            label: '任务类型',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [\r\n\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Status',\r\n            label: '任务状态',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Date',\r\n            label: '开始时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              type: 'datetimerange',\r\n              rangeSeparator: '至',\r\n              startPlaceholder: '开始日期',\r\n              endPlaceholder: '结束日期',\r\n              clearable: true,\r\n              valueFormat: 'yyyy-MM-dd HH:mm'\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n              if (e && e.length !== 0) {\r\n                this.ruleForm.StartTime = e[0]\r\n                this.ruleForm.EndTime = e[1]\r\n              } else {\r\n                this.ruleForm.StartTime = null\r\n                this.ruleForm.EndTime = null\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        loading: false,\r\n        buttonConfig: {\r\n          buttonList: [\r\n\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          {\r\n            label: '任务状态',\r\n            key: 'StatusName'\r\n          },\r\n          {\r\n            label: '任务类型',\r\n            key: 'Type'\r\n          },\r\n          {\r\n            label: '任务名称',\r\n            key: 'Name'\r\n          },\r\n          {\r\n            label: '来源',\r\n            key: 'Source'\r\n          },\r\n          {\r\n            label: '业务模块',\r\n            key: 'Module'\r\n          },\r\n          {\r\n            label: '任务开始时间',\r\n            key: 'StartTime'\r\n          },\r\n          {\r\n            label: '计划完成时间',\r\n            key: 'EndTime'\r\n          },\r\n          {\r\n            label: '实际完成时间',\r\n            key: 'DoneTime'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '去处理',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.goHandelAlarm(row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getTaskType()\r\n    this.getTaskStatus()\r\n    this.onFresh()\r\n  },\r\n  methods: {\r\n    // 获取任务类型\r\n    getTaskType() {\r\n      GetTaskType({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const arr = []\r\n          const data = res.Data || null\r\n          data.forEach((item) => {\r\n            const obj = {\r\n              label: item.Name,\r\n              value: item.Value\r\n            }\r\n            arr.push(obj)\r\n          })\r\n          this.customForm.formItems.find((v) => v.key == 'Type').options = arr\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 获取任务类型\r\n    getTaskStatus() {\r\n      this.customTableConfig.loading = true\r\n      GetTaskStatus({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const arr = []\r\n          const data = res.Data || null\r\n          data.forEach((item) => {\r\n            const obj = {\r\n              label: item.Name,\r\n              value: item.Value\r\n            }\r\n            arr.push(obj)\r\n          })\r\n          this.customForm.formItems.find((v) => v.key == 'Status').options = arr\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.customTableConfig.loading = false\r\n      })\r\n    },\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.fetchData()\r\n    },\r\n    async fetchData() {\r\n      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null\r\n        this.ruleForm.EndTime = null\r\n      }\r\n      await GetTaskPageList({\r\n        ...this.ruleForm,\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.tableData = res.Data.Data\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 去处理路由跳转\r\n    goHandelAlarm(item) {\r\n      // this.$router.push({ name: item.Code })\r\n      console.log(item, 'item')\r\n      if (item.Url) {\r\n        let platform = '' // 子应用\r\n        if (item.ModuleName == '后台设置') {\r\n          platform = 'management'\r\n        } else {\r\n          platform = 'digitalfactory'\r\n        }\r\n        this.$qiankun.switchMicroAppFn(\r\n          platform,\r\n          item.ModuleCode,\r\n          item.ModuleId,\r\n          item.Url\r\n        )\r\n      } else {\r\n        const platform = 'digitalfactory'\r\n        const code = 'szgc'\r\n        const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\r\n        let url = ''\r\n        if (item.Module == '环境管理') {\r\n          url = '/business/environment/alarmInformation'\r\n        } else\r\n        if (item.Module == '访客管理') {\r\n          url = '/business/visitorList'\r\n          console.log('访客管理')\r\n        }\r\n        this.$qiankun.switchMicroAppFn(platform, code, id, url)\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.onFresh()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.onFresh()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.layout {\r\n  height: 100%;\r\n  width: 100%;\r\n  position: absolute;\r\n  ::v-deep {\r\n    .CustomLayout {\r\n      .layoutTable {\r\n        height: 0;\r\n        .CustomTable {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          .table {\r\n            flex: 1;\r\n            height: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            .el-table {\r\n              flex: 1;\r\n              height: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}