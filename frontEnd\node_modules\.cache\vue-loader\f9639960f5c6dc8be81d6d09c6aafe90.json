{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlAlarmDetails\\index.vue?vue&type=style&index=0&id=615c09fc&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlAlarmDetails\\index.vue", "mtime": 1755506319075}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoubXQyMCB7CiAgbWFyZ2luLXRvcDogMTBweDsKfQoubGF5b3V0ewogIGhlaWdodDogY2FsYygxMDB2aCAtIDkwcHgpOwogIG92ZXJmbG93OiBhdXRvOwp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiTA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/accessControl/accessControlAlarmDetails", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        >\n          <template #customBtn=\"{ slotScope }\">\n            <el-button\n              v-if=\"slotScope.Handle_Status != 2\"\n              type=\"text\"\n              @click=\"handleChange(slotScope)\"\n            >关闭</el-button>\n          </template>\n        </CustomTable>\n      </template>\n    </CustomLayout>\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <component\n        :is=\"currentComponent\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n      /></el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\nimport getGridByCode from '../../safetyManagement/mixins/index'\nimport DialogForm from './dialogForm.vue'\n\nimport { downloadFile } from '@/utils/downloadFile'\nimport dayjs from 'dayjs'\nimport {\n  entranceWarningGetWarningList,\n  ExportEntranceWarning,\n  UpdateEntranceWarningStatus\n} from '@/api/business/hazardousChemicals'\nexport default {\n  name: '',\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout\n  },\n  mixins: [getGridByCode],\n  data() {\n    return {\n      currentComponent: DialogForm,\n      componentsConfig: {\n        Data: {}\n      },\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true\n        },\n        close: () => {\n          this.dialogVisible = false\n          this.onFresh()\n        }\n      },\n      dialogVisible: false,\n      dialogTitle: '查看',\n      tableSelection: [],\n      ruleForm: {\n        Warning_Name: '',\n        Warning_Type: '',\n        Warning_Information: ''\n      },\n      customForm: {\n        formItems: [\n          {\n            key: 'Warning_Name',\n            label: '告警事件名称',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'Warning_Type',\n            label: '告警事件类型',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'Warning_Information',\n            label: '告警内容',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'Handle_Status',\n            label: '告警状态',\n            type: 'select',\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: '请选择告警状态'\n            },\n            options: [\n              {\n                label: '告警中',\n                value: 1\n              },\n              {\n                label: '已关闭',\n                value: 2\n              },\n              {\n                label: '已处理',\n                value: 3\n              }\n            ],\n            change: (e) => {\n              console.log(e)\n            }\n          }\n        ],\n        rules: {\n        },\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        buttonConfig: {\n          buttonList: [\n            {\n              text: '导出',\n              onclick: (item) => {\n                console.log(item)\n                this.handleExport()\n              }\n            }\n          ]\n        },\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        tableColumns: [],\n        tableData: [],\n        operateOptions: {\n          align: 'center'\n        },\n        tableActionsWidth: 120,\n        tableActions: [\n          // {\n          //   actionLabel: '关闭',\n          //   otherOptions: {\n          //     type: 'text'\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handleChange(row)\n          //   }\n          // },\n          {\n            actionLabel: '查看',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.handleEdit(row)\n            }\n          }\n        ]\n      }\n    }\n  },\n  computed: {},\n  created() {\n    this.init()\n  },\n  methods: {\n    searchForm(data) {\n      console.log(data)\n      this.customTableConfig.currentPage = 1\n      this.onFresh()\n    },\n    resetForm() {\n      this.onFresh()\n    },\n    onFresh() {\n      this.getEquipmentList()\n    },\n    init() {\n      this.getGridByCode('AccessControlAlarmDetails1')\n      this.getEquipmentList()\n    },\n    async getEquipmentList() {\n      const res = await entranceWarningGetWarningList({\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n        ...this.ruleForm\n      })\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data\n        this.customTableConfig.total = res.Data.TotalCount\n        if (this.customTableConfig.tableData.length > 0) {\n          this.customTableConfig.tableData.map(v => {\n            v.Warning_First_Time = (v.Warning_First_Time ?? '') != '' ? dayjs(v.Warning_First_Time).format('YYYY-MM-DD HH:mm:ss') : ''\n            v.Warning_Last_Time = (v.Warning_Last_Time ?? '') != '' ? dayjs(v.Warning_Last_Time).format('YYYY-MM-DD HH:mm:ss') : ''\n          })\n        }\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n    async handleExport() {\n      if (this.tableSelection.length == 0) {\n        this.$message.warning('请选择数据在导出')\n        return\n      }\n      const res = await ExportEntranceWarning({\n        id: this.tableSelection.map((item) => item.Id).toString(),\n        code: 'AccessControlAlarmDetails1'\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        downloadFile(res.Data, '告警明细数据')\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.getEquipmentList()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前页: ${val}`)\n      this.customTableConfig.currentPage = val\n      this.getEquipmentList()\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection\n    },\n    handleEdit(row) {\n      this.dialogVisible = true\n      this.componentsConfig.Data = row\n    },\n    handleChange(row) {\n      if (row.HandleStatusStr == '关闭') {\n        this.$message.warning('请勿重复操作')\n      } else {\n        this.$confirm('此操作将关闭该告警, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          UpdateEntranceWarningStatus({ id: row.Id, wid: row.WId, StatusEnum: 2 }).then(res => {\n            if (res.IsSucceed) {\n              this.$message.success('操作成功')\n              this.init()\n            } else {\n              this.$message.error(res.Message)\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消'\n          })\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.mt20 {\n  margin-top: 10px;\n}\n.layout{\n  height: calc(100vh - 90px);\n  overflow: auto;\n}\n</style>\n"]}]}