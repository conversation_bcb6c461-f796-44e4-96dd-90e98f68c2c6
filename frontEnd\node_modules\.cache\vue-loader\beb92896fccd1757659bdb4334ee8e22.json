{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\gas\\components\\gasUsed.vue?vue&type=style&index=0&id=058cdaf6&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\gas\\components\\gasUsed.vue", "mtime": 1754621285999}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["gasUsed.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "gasUsed.vue", "sourceRoot": "src/views/business/energyManagement/pJEnergyAnalysis/gas/components", "sourcesContent": ["<template>\n  <div class=\"gas-container\">\n    <div class=\"title\">\n      {{ dataObj.title }}\n      <el-tooltip class=\"item\" effect=\"dark\" placement=\"top-start\">\n        <div slot=\"content\">{{ dataObj.tooltip }}</div>\n        <img\n          style=\"width: 16px; height: 16px; margin-left: 8px\"\n          src=\"@/assets/question.png\"\n          alt=\"\"\n        >\n      </el-tooltip>\n    </div>\n    <div class=\"middle\">\n      <div class=\"Bgbox\">\n        <div class=\"fill\">\n          <div\n            :style=\"{ height: dataObj.fillHeight, background: dataObj.color }\"\n          />\n        </div>\n        <div class=\"iconBg\" />\n      </div>\n      <div v-if=\"dataObj.showTotal\" class=\"middleText\">\n        剩余\n        <span :style=\"{ color: dataObj.color }\">{{\n          dataObj.residue.value\n        }}</span> {{ dataObj.unit }}\n        <span :style=\"{ color: dataObj.color }\">{{\n          dataObj.residue.percentage\n        }}</span>\n      </div>\n    </div>\n\n    <!-- 龙建科工 暂时隐藏 -->\n    <!-- <el-row :gutter=\"20\" class=\"customRow\">\n      <el-col v-for=\"(item, index) in dataObj.colData\" :key=\"index\" :span=\"8\">\n        <div class=\"bottomContainer\">\n          <img class=\"arrow\" :src=\"gas\" alt=\"\">\n          <div class=\"down\">\n            <img\n              :src=\"item.iconName == 'delivery' ? delivery : workshop\"\n              alt=\"\"\n            >\n            <div class=\"textData\">\n              <h2>{{ item.Key }}</h2>\n              <p :style=\"{ color: dataObj.color }\">\n                <span style=\"width: 70px\">{{ item.Percent }} %</span><span\n                  style=\"flex: 1\"\n                >{{ item.Value }} {{ dataObj.unit }}</span>\n              </p>\n            </div>\n          </div>\n        </div>\n      </el-col>\n    </el-row> -->\n  </div>\n</template>\n\n<script>\nexport default {\n  components: {},\n  props: {\n    customGasUsedConfig: {\n      type: Object,\n      default: () => {}\n    },\n    isPhotovoltaic: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      workshop: require('@/assets/workshop.png'),\n      delivery: require('@/assets/delivery.png'),\n      gas: require('@/assets/Business/gas.png')\n    }\n  },\n  computed: {\n    dataObj() {\n      const data = {\n        ...this.customGasUsedConfig.baseData,\n        ...this.customGasUsedConfig.gasData\n      }\n      return data\n    }\n  },\n  created() {},\n  mounted() {},\n  methods: {}\n}\n</script>\n <style scoped lang='scss'>\n.gas-container {\n  width: 100%;\n  height: 363px;\n  padding: 16px 24px;\n  border-radius: 4px;\n  display: flex;\n  flex-direction: column;\n  background: #fff;\n  position: relative; // 添加相对定位\n  .title {\n    display: flex;\n    align-items: center;\n    margin-bottom: 24px;\n  }\n  .middle {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    padding: 12px;\n    height: 133px;\n    background: linear-gradient(\n      90deg,\n      rgba(41, 141, 255, 0.05) 0%,\n      rgba(41, 141, 255, 0) 100%\n    );\n    position: absolute; // 改为绝对定位\n    top: 50%; // 定位到垂直中点\n    left: 0; // 左侧对齐\n    right: 0; // 右侧对齐\n    transform: translateY(-50%); // 向上移动自身高度的一半\n    margin-top: 24px; // 补偿title的高度\n    .Bgbox {\n      width: 48px;\n      height: 110px;\n      margin-right: 24px;\n      position: relative;\n      .iconBg {\n        width: 48px;\n        height: 110px;\n        background: url(\"../../../../../../assets/Business/box.png\");\n        background-size: cover;\n        position: absolute;\n        top: 2px;\n        left: 0;\n      }\n      .fill {\n        position: absolute;\n        width: 40px;\n        height: 77px;\n        bottom: 12px;\n        left: 4px;\n        overflow: hidden;\n        div {\n          width: 40px;\n          position: absolute;\n          left: 0;\n          bottom: 0;\n        }\n      }\n    }\n    .middleText {\n      font-size: 24px;\n      color: #333;\n      span {\n        font-weight: bold;\n        margin: 0 16px;\n      }\n    }\n  }\n  .customRow {\n    display: flex;\n    flex-direction: row;\n    justify-content: center;\n    .bottomContainer {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      .arrow {\n        height: 72px;\n        width: 28px;\n      }\n      .down {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        height: 83px;\n        background: linear-gradient(\n          90deg,\n          rgba(41, 141, 255, 0.05) 0%,\n          rgba(41, 141, 255, 0) 100%\n        );\n        img {\n          width: 24px;\n          height: 24px;\n          margin-right: 16px;\n        }\n        .textData {\n          h2 {\n            color: #333;\n            font-size: 16px;\n            font-weight: 400;\n            margin-bottom: 16px;\n          }\n          p {\n            display: flex;\n          }\n          span {\n            display: block;\n            font-weight: bold;\n            font-size: 14px;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}