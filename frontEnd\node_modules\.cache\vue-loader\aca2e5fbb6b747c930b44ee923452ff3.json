{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\personnelManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\personnelManagement\\index.vue", "mtime": 1755674552430}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/personnelManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 personnelManagement\">\r\n    <custom-layout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"submitForm\"\r\n          @resetForm=\"fetchData\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </custom-layout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport getGridByCode from '../safetyManagement/mixins/index'\r\nimport { querypersonnel, DeletePersonnel, DownloadPersonnelsTemplate, DownloadPersonnelsToExcel, ExportPersonnelList } from '@/api/business/personnelManagement.js'\r\nimport {\r\n  GetDepartment,\r\n  GetCompany\r\n} from '@/api/business/accessControl'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport addDialog from './components/addDialog'\r\nimport DialogFormImport from './components/importFile'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nexport default {\r\n  components: {\r\n    CustomLayout,\r\n    CustomTable,\r\n    CustomForm\r\n  },\r\n  mixins: [getGridByCode, addRouterPage],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        name: '',\r\n        mobile: '',\r\n        personnelType: null,\r\n        companyId: '',\r\n        departmentId: '',\r\n        personnelStatus: null\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'name',\r\n            label: '姓名',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'mobile',\r\n            label: '联系方式',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'personnelType',\r\n            label: '人员类型',\r\n            type: 'select',\r\n            options: [\r\n              {\r\n                label: '系统人员',\r\n                value: '1'\r\n              },\r\n              {\r\n                label: '普通人员',\r\n                value: '2'\r\n              }\r\n            ],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'companyId',\r\n            label: '所属公司',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'departmentId',\r\n            label: '所属部门',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'personnelStatus',\r\n            label: '状态',\r\n            type: 'select',\r\n            options: [\r\n              {\r\n                label: '在职',\r\n                value: '1'\r\n              },\r\n              {\r\n                label: '离职',\r\n                value: '2'\r\n              }\r\n            ],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '导入模板下载',\r\n              icon: 'el-icon-download',\r\n              onclick: () => {\r\n                this.handleDownTemplate()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导入',\r\n              icon: 'el-icon-download',\r\n              onclick: () => {\r\n                this.handleImport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导出',\r\n              icon: 'el-icon-upload2',\r\n              onclick: () => {\r\n                this.handleExport()\r\n              }\r\n            },\r\n            {\r\n              text: '新增',\r\n              icon: 'el-icon-plus',\r\n              type: 'primary',\r\n              onclick: () => {\r\n                this.handleClick('add')\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        pageSizeOptions: [20, 40, 60, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center'\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleWatch(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleClick('edit', row)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDele(row.Id)\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      multipleSelection: [],\r\n      dialogVisible: false,\r\n      currentComponent: null,\r\n      dialogTitle: '新增',\r\n      componentsFuns: {\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.fetchData()\r\n        }\r\n      },\r\n      componentsConfig: {},\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/info',\r\n          hidden: true,\r\n          component: () => import('./info.vue'),\r\n          meta: { title: '人员详情' },\r\n          name: 'PersonnelManagementInfo'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  async created() {\r\n    this.fetchData()\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'departmentId'\r\n    ).options = await this.initGetDepartment()\r\n    // 所属单位\r\n    this.customForm.formItems.find((item) => item.key === 'companyId').options =\r\n      await this.initGetCompany()\r\n    this.getGridByCodeRender('PersonnelManagement', [{ key: 'Gender', Tag: 'span', condition: 1, val1: '男', val2: '女' }, { key: 'PersonnelStatus', Tag: 'span', condition: 1, val1: '在职', val2: '离职' }, { key: 'PersonnelType', Tag: 'span', condition: 1, val1: '系统人员', val2: '普通人员' }])\r\n  },\r\n  async mounted() {\r\n    this.customForm.formItems[1].options = await this.getDictionaryDetailListByCode('PatrolResult')\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      querypersonnel({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.tableData = res.Data.Data\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    submitForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData()\r\n    },\r\n    async initGetDepartment() {\r\n      const res = await GetDepartment({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    async initGetCompany() {\r\n      const res = await GetCompany({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    handleSizeChange(val) {\r\n      this.customTableConfig.pageSize = val\r\n      this.fetchData()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.customTableConfig.currentPage = val\r\n      this.fetchData()\r\n    },\r\n    handleSelectionChange(data) {\r\n      this.multipleSelection = data\r\n    },\r\n    // async handleExport() {\r\n    //   const res = await DownloadPersonnelsToExcel({\r\n    //     id: this.multipleSelection.map(v => v.Id)\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     downloadFile(res.Data, '人员管理')\r\n    //   } else {\r\n    //     this.$message({\r\n    //       type: 'error',\r\n    //       message: res.Message\r\n    //     })\r\n    //   }\r\n    // },\r\n    // v2 版本导出\r\n    async handleExport() {\r\n      const res = await ExportPersonnelList({\r\n        Id: this.multipleSelection.map(v => v.Id).toString(),\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        downloadFile(res.Data, '人员管理')\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    handleClick(type, data) {\r\n      this.dialogVisible = true\r\n      this.currentComponent = addDialog\r\n      if (type == 'add') {\r\n        this.dialogTitle = '新增'\r\n        this.componentsConfig = {\r\n          name: '',\r\n          mobile: '',\r\n          personnelType: null,\r\n          companyId: '',\r\n          departmentId: '',\r\n          personnelStatus: null\r\n        }\r\n      } else {\r\n        this.dialogTitle = '编辑'\r\n        this.componentsConfig = data\r\n      }\r\n    },\r\n    handleWatch(Id) {\r\n      this.$router.push({\r\n        name: 'PersonnelManagementInfo',\r\n        query: { pg_redirect: this.$route.name, Id }\r\n      })\r\n    },\r\n    handleDele(id) {\r\n      this.$confirm('是否确定删除该数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeletePersonnel({ id }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    handleDownTemplate() {\r\n      DownloadPersonnelsTemplate({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data, '授权名单导入模板')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    async handleImport() {\r\n      this.dialogTitle = '批量导入'\r\n      this.currentComponent = DialogFormImport\r\n      this.componentsConfig = {\r\n        disabled: true,\r\n        title: '批量导入'\r\n      }\r\n      this.dialogVisible = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n  <style scoped lang='scss'>\r\n  .personnelManagement{\r\n    // height: calc(100vh - 90px);\r\n    // overflow: hidden;\r\n  }\r\n</style>\r\n"]}]}