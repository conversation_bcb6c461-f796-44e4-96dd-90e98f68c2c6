<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        >
          <template #customBtn="{ slotScope }">
            <el-button
              v-if="slotScope.Status == 1"
              type="text"
              @click="handleEdit(2, slotScope, 'edit')"
              >编辑</el-button
            ></template
          >
        </CustomTable>
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      :title="dialogTitle"
      width="900px"
      :visible.sync="dialogVisible"
    >
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";
import getGridByCode from "../../safetyManagement/mixins/index";
import DialogForm from "./dialogForm.vue";
import DialogFormLook from "./dialogFormLook.vue";

import { downloadFile } from "@/utils/downloadFile";
import dayjs from "dayjs";
import {
  GetUsers,
  GetPageList,
  SaveNotice,
  BatchCloseNotice,
  GetNoticeInfo,
  GetNoticeDropDownOption,
  GetPublishUnitList,
  DeleteNotice,
} from "@/api/business/eventManagement";
export default {
  name: "",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
  },
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {
        Data: {},
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogVisible: false,
      dialogTitle: "查看",
      tableSelection: [],
      ruleForm: {
        Title: "",
        Type: "",
        Source: "",
        Module: "事件管理",
        NotifyUser: "",
        Date: [],
        StartTime: "",
        EndTime: "",
      },
      customForm: {
        formItems: [
          {
            key: "Title",
            label: "通知标题",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
          // {
          //   key: "Type",
          //   label: "通知类型",
          //   type: "select",
          //   options: [],
          //   otherOptions: {
          //     clearable: true,
          //   },
          //   change: (e) => {
          //     // change事件
          //     console.log(e);
          //   },
          // },
          // {
          //   key: "Source",
          //   label: "来源",
          //   type: "input",
          //   options: [],
          //   otherOptions: {
          //     clearable: true,
          //   },
          //   change: (e) => {
          //     console.log(e);
          //   },
          // },
          {
            key: "Module",
            label: "业务模块",
            type: "select",
            options: [
              {
                label: "全部",
                value: "",
              },
              {
                label: "事件管理",
                value: "事件管理",
              },
            ],
            otherOptions: {
              clearable: true,
            },
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
          {
            key: "NotifyUser",
            label: "通知人员",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
          {
            key: "Date", // 字段ID
            label: "发布时间", // Form的label
            type: "datePicker", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              type: "datetimerange",
              disabled: false,
              placeholder: "请输入...",
            },
            change: (e) => {
              // change事件
              console.log(e);
              this.ruleForm.StartTime = dayjs(e[0]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
              this.ruleForm.EndTime = dayjs(e[1]).format("YYYY-MM-DD HH:mm:ss");
            },
          },
        ],
        rules: {},
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "发布公告",
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: false, // 是否禁用
              icon: "", //  图标
              autofocus: false, // 是否聚焦
              type: "primary", // primary / success / warning / danger / info / text
              size: "small", // medium / small / mini
              onclick: (item) => {
                console.log(item);
                this.handleCreate();
              },
            },
            {
              text: "关闭",
              type: "danger",
              onclick: (item) => {
                console.log(item);
                this.handleClose();
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            otherOptions: {
              type: "selection",
              align: "center",
              fixed: "left",
            },
          },
          {
            label: "通知标题",
            key: "Title",
            otherOptions: {
              align: "center",
            },
          },
          // {
          //   label: "通知类型",
          //   key: "TypeName",
          //   otherOptions: {
          //     align: "center",
          //   },
          // },
          // {
          //   label: "来源",
          //   key: "Source",
          //   otherOptions: {
          //     align: "center",
          //   },
          // },
          {
            label: "业务模块",
            key: "Module",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "通知人员",
            key: "NotifyUser",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "公告状态",
            key: "StatusName",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "发布时间",
            key: "PublishTime",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "创建人",
            key: "Create_UserName",
            otherOptions: {
              align: "center",
            },
          },
        ],
        tableData: [],
        operateOptions: {
          align: "center",
        },
        tableActionsWidth: 160,
        tableActions: [
          {
            actionLabel: "查看详情",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, "view");
            },
          },
          {
            actionLabel: "删除",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleDelete(index, row);
            },
          },
          // {
          //   actionLabel: "编辑",
          //   otherOptions: {
          //     type: "text",
          //     disabled: true,
          //   },
          //   onclick: (index, row) => {
          //     console.log(row, "row");
          //     this.handleEdit(index, row, "edit");
          //   },
          // },
        ],
      },
    };
  },
  computed: {},
  created() {
    this.init();
  },
  mixins: [getGridByCode],
  methods: {
    searchForm(data) {
      console.log(data);
      this.customTableConfig.currentPage = 1;
      this.onFresh();
    },
    resetForm() {
      this.ruleForm.StartTime = null;
      this.ruleForm.EndTime = null;
      this.ruleForm.Date = null;
      this.onFresh();
    },
    onFresh() {
      this.GetPageList();
    },
    init() {
      // this.getGridByCode("AccessControlAlarmDetails1");
      this.GetPageList();
      this.getNoticeDropDownOption();
    },
    async getNoticeDropDownOption() {
      const res = await GetNoticeDropDownOption({});
      if (res.IsSucceed) {
        let result = res.Data || [];
        let noticeType = [];
        let noticeLevel = [];
        result.forEach((element) => {
          if (element.TypeName == "通知类型") {
            noticeType = element.Data.map((item) => ({
              value: item.Value,
              label: item.Name,
            }));
          } else if (element.TypeName == "发布层级") {
            noticeLevel = element.Data.map((item) => ({
              value: item.Value,
              label: item.Name,
            }));
          }
        });
        this.customForm.formItems.find((item) => item.key == "Type").options =
          noticeType;
      }
    },
    async GetPageList() {
      const res = await GetPageList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data;
        this.customTableConfig.total = res.Data.TotalCount;
        if (this.customTableConfig.tableData.length > 0) {
        }
      } else {
        this.$message.error(res.Message);
      }
    },
    // 通知公告删除
    async handleDelete(index, row) {
      console.log(row, "---");
      const res = await DeleteNotice({
        IDs: [row.Id],
      });
      if (res.IsSucceed) {
        this.onFresh();
        this.$message.success("删除成功");
      } else {
        this.$message.error(res.Message);
      }
    },
    handleCreate() {
      this.dialogTitle = "发布公告";
      this.dialogVisible = true;
      this.currentComponent = DialogForm;
      this.componentsConfig = {
        type: "add",
      };
    },
    handleEdit(index, row, type) {
      console.log(index, row, type);

      let isShowBtn = true;
      if (type === "view") {
        this.currentComponent = DialogFormLook;
        this.dialogTitle = "查看";
        this.componentsConfig = {
          ID: row.Id,
          disabled: true,
          type,
          title: "查看",
        };
        isShowBtn = false;
      } else if (type === "edit") {
        this.currentComponent = DialogForm;
        this.dialogTitle = "编辑";
        this.componentsConfig = {
          ID: row.Id,
          disabled: true,
          type,
          title: "编辑",
        };
        isShowBtn = true;
      }
      this.dialogVisible = true;
    },
    async handleClose() {
      if (this.tableSelection.length == 0) {
        this.$message.warning('请选择数据后再关闭!')
      } else {
        const flag = this.tableSelection.some(item => item.Status == 0 || item.Status == 2)
        if (flag) {
          this.$message.warning('此功能只能关闭待发布的公告!')
          return
        } else {
          const res = await BatchCloseNotice({
            id: this.tableSelection.map((item) => item.Id).toString(),
            // code: "AccessControlAlarmDetails1",
          });
          if (res.IsSucceed) {
            console.log(res);
            this.$message.success("操作成功");
            this.init();
            // downloadFile(res.Data, "告警明细数据");
          }
        }
      }
    },
    // async handleExport() {
    //   const res = await ExportEntranceWarning({
    //     id: this.tableSelection.map((item) => item.Id).toString(),
    //     code: "AccessControlAlarmDetails1",
    //   });
    //   if (res.IsSucceed) {
    //     console.log(res);
    //     downloadFile(res.Data, "告警明细数据");
    //   }
    // },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.GetPageList();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.GetPageList();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
    // handleEdit(row) {
    //   this.dialogVisible = true;
    //   this.componentsConfig.Data = row;
    // },
  },
};
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
.layout{
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
