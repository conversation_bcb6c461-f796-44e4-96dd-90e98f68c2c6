{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlRecord\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlRecord\\index.vue", "mtime": 1755674552409}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "dialogFormLook", "downloadFile", "dayjs", "GetRole", "GetTrafficRecordList", "ExportEntranceTrafficRecord", "GetDictionaryDetailListByCode", "name", "components", "data", "_this", "currentComponent", "componentsConfig", "componentsFuns", "open", "dialogVisible", "close", "onFresh", "dialogTitle", "tableSelection", "ruleForm", "P_Name", "BeginTime", "EndTime", "EntranceType", "EquipmentName", "Position", "Position_Name", "Traffic_Way", "P_Type", "customForm", "formItems", "key", "label", "type", "otherOptions", "clearable", "change", "e", "console", "log", "placeholder", "options", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "text", "onclick", "item", "handleExport", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "width", "align", "tableData", "tableActions", "actionLabel", "index", "row", "handleEdit", "computed", "created", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "initDeviceType", "find", "sent", "initGetRole", "t0", "t1", "call", "init", "stop", "methods", "code", "_callee2", "res", "_callee2$", "_context2", "dictionaryCode", "Data", "map", "Display_Name", "value", "Value", "abrupt", "_callee3", "_callee3$", "_context3", "searchForm", "resetForm", "_this3", "_callee4", "_callee4$", "_context4", "_objectSpread", "Parameter<PERSON>son", "Key", "Type", "Filter_Type", "Page", "PageSize", "Search", "SortName", "SortOrder", "TotalCount", "format", "IsSucceed", "Traffic_Time", "$message", "error", "Message", "handleCreate", "ID", "Id", "title", "_this4", "_callee5", "_callee5$", "_context5", "length", "warning", "id", "join", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "selection"], "sources": ["src/views/business/accessControl/accessControlRecord/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport dialogFormLook from './dialogFormLook.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n// import * as moment from 'moment'\r\nimport dayjs from 'dayjs'\r\n\r\nimport {\r\n  GetRole,\r\n  GetTrafficRecordList,\r\n  ExportEntranceTrafficRecord,\r\n  GetDictionaryDetailListByCode\r\n} from '@/api/business/accessControl'\r\n\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: dialogFormLook,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        P_Name: '',\r\n        BeginTime: '',\r\n        EndTime: '',\r\n        EntranceType: '',\r\n        EquipmentName: '',\r\n        Position: '',\r\n        Position_Name: '',\r\n        Traffic_Way: '',\r\n        P_Type: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'P_Name', // 字段ID\r\n            label: '姓名', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            // width: '240px',\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'BeginTime',\r\n            label: '通行开始时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EndTime',\r\n            label: '通行结束时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EntranceType', // 字段ID\r\n            label: '门禁类型', // Form的label\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentName', // 字段ID\r\n            label: '门禁设备', // Form的label\r\n            type: 'input',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position', // 字段ID\r\n            label: '安装位置', // Form的label\r\n            type: 'input',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position_Name', // 字段ID\r\n            label: '岗位类型', // Form的label\r\n            type: 'select',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Traffic_Way', // 字段ID\r\n            label: '通行方式', // Form的label\r\n            type: 'select',\r\n\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'P_Type', // 字段ID\r\n            label: '人员类型', // Form的label\r\n            type: 'select',\r\n\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            // {\r\n            //   text: '新增',\r\n            //   round: false, // 是否圆角\r\n            //   plain: false, // 是否朴素\r\n            //   circle: false, // 是否圆形\r\n            //   loading: false, // 是否加载中\r\n            //   disabled: false, // 是否禁用\r\n            //   icon: '', //  图标\r\n            //   autofocus: false, // 是否聚焦\r\n            //   type: 'primary', // primary / success / warning / danger / info / text\r\n            //   size: 'small', // medium / small / mini\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleCreate()\r\n            //   }\r\n            // },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleExport()\r\n              }\r\n            }\r\n            // {\r\n            //   text: '批量导出',\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleAllExport()\r\n            //   }\r\n            // }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          // {\r\n          //   width: 60,\r\n          //   label: '序号',\r\n          //   otherOptions: {\r\n          //     type: 'index',\r\n          //     align: 'center'\r\n          //   } // key\r\n          // },\r\n          {\r\n            label: '姓名',\r\n            key: 'P_Name'\r\n          },\r\n          // {\r\n          //   label: '图片',\r\n          //   key: 'Name'\r\n          // },\r\n          {\r\n            label: '性别',\r\n            key: 'P_Sex'\r\n          },\r\n          {\r\n            label: '联系方式',\r\n            key: 'Contact_Way'\r\n          },\r\n          {\r\n            label: '人员类型',\r\n            key: 'P_Type'\r\n          },\r\n          {\r\n            label: '岗位类型',\r\n            key: 'Position_Name'\r\n          },\r\n          {\r\n            label: '门禁类型',\r\n            key: 'Entrance_Equipment_Type'\r\n          },\r\n          {\r\n            label: '门禁名称',\r\n            key: 'Entrance_Equipment_Name'\r\n          },\r\n          {\r\n            label: '门禁位置',\r\n            key: 'Position'\r\n          },\r\n          {\r\n            label: '通行时间',\r\n            key: 'Traffic_Time'\r\n          },\r\n          {\r\n            label: '通行方式',\r\n            key: 'Traffic_Way'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          }\r\n          // {\r\n          //   actionLabel: '编辑',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleEdit(index, row, 'edit')\r\n          //   }\r\n          // },\r\n          // {\r\n          //   actionLabel: '删除',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleDelete(index, row)\r\n          //   }\r\n          // }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  async created() {\r\n    // 门禁类型\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'EntranceType'\r\n    ).options = await this.initDeviceType('Entrance_Type')\r\n    // 岗位类型\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'Position_Name'\r\n    ).options = await this.initGetRole('Entrance_Type')\r\n\r\n    console.log(await this.initDeviceType('Position_Type'), '1221212222222')\r\n    // 通行方式\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'Traffic_Way'\r\n    ).options = await this.initDeviceType('Traffic_Way')\r\n    // 人员类型\r\n    this.customForm.formItems.find((item) => item.key === 'P_Type').options =\r\n      await this.initDeviceType('P_Type')\r\n    this.init()\r\n  },\r\n  methods: {\r\n    async initDeviceType(code) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode: code\r\n      })\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    // 岗位类型\r\n    async initGetRole(code) {\r\n      const res = await GetRole({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.GetTrafficRecordList()\r\n    },\r\n    init() {\r\n      this.GetTrafficRecordList()\r\n    },\r\n    async GetTrafficRecordList() {\r\n      const res = await GetTrafficRecordList({\r\n        ParameterJson: [\r\n          {\r\n            Key: 'string',\r\n            Value: [null],\r\n            Type: 'string',\r\n            Filter_Type: 'string'\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n\r\n        Search: '',\r\n        SortName: '',\r\n        SortOrder: '',\r\n        TotalCount: 0,\r\n        P_Name: '',\r\n\r\n        EntranceType: '',\r\n        EquipmentName: '',\r\n        Position: '',\r\n        Position_Name: '',\r\n        Traffic_Way: '',\r\n        P_Type: '',\r\n        ...this.ruleForm,\r\n        BeginTime: this.ruleForm.EndTime\r\n          ? dayjs(this.ruleForm.BeginTime).format('YYYY-MM-DD')\r\n          : '',\r\n        EndTime: this.ruleForm.EndTime\r\n          ? dayjs(this.ruleForm.EndTime).format('YYYY-MM-DD')\r\n          : ''\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n          ...item,\r\n          Traffic_Time: dayjs(item.Traffic_Time).format('YYYY-MM-DD HH:mm:ss')\r\n        }))\r\n        console.log(res)\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.dialogVisible = true\r\n    },\r\n    // handleDelete(index, row) {\r\n    //   console.log(index, row)\r\n    //   console.log(this)\r\n    //   this.$confirm('确认删除？', {\r\n    //     type: 'warning'\r\n    //   })\r\n    //     .then(async(_) => {\r\n    //       const res = await DeleteEquipment({\r\n    //         IDs: [row.ID]\r\n    //       })\r\n    //       if (res.IsSucceed) {\r\n    //         this.init()\r\n    //       }\r\n    //     })\r\n    //     .catch((_) => {})\r\n    // },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.componentsConfig = {\r\n          ID: row.Id,\r\n          title: '查看',\r\n          row: row\r\n        }\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    async handleExport() {\r\n      if (this.tableSelection.length == 0) {\r\n        this.$message.warning('请选择数据在导出')\r\n        return\r\n      }\r\n      const res = await ExportEntranceTrafficRecord({\r\n        id: this.tableSelection.map((item) => item.Id).join(','),\r\n        code: 'accessControlRecord'\r\n        // ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    // async handleAllExport() {\r\n    //   const res = await ExportEntranceTrafficRecord({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: true,\r\n    //     Ids: [],\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   }\r\n    // },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AAEA,OAAAC,cAAA;AAEA,SAAAC,YAAA;AACA;AACA;AACA;AACA,OAAAC,KAAA;AAEA,SACAC,OAAA,EACAC,oBAAA,IAAAA,qBAAA,EACAC,2BAAA,EACAC,6BAAA,QACA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAV,WAAA,EAAAA,WAAA;IACA;IACA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAY,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,gBAAA,EAAAX,cAAA;MACAY,gBAAA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAJ,KAAA,CAAAK,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAN,KAAA,CAAAK,aAAA;UACAL,KAAA,CAAAO,OAAA;QACA;MACA;MACAF,aAAA;MACAG,WAAA;MACAC,cAAA;MAEAC,QAAA;QACAC,MAAA;QACAC,SAAA;QACAC,OAAA;QACAC,YAAA;QACAC,aAAA;QACAC,QAAA;QACAC,aAAA;QACAC,WAAA;QACAC,MAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAM,WAAA;UACA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAM,WAAA;UACA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UACAQ,OAAA;UACAP,YAAA;YACAM,WAAA;UACA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UACAC,YAAA;YACAM,WAAA;UACA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UACAC,YAAA;YACAM,WAAA;UACA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UACAC,YAAA;YACAM,WAAA;UACA;UACAC,OAAA;UACAL,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAEAQ,OAAA;UACAL,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAEAQ,OAAA;UACAL,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,EACA;QACAK,KAAA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAAC,IAAA;cACAb,OAAA,CAAAC,GAAA,CAAAY,IAAA;cACA1C,KAAA,CAAA2C,YAAA;YACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UAAA;QAEA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA,GACA;UACAC,KAAA;UACAxB,YAAA;YACAD,IAAA;YACA0B,KAAA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA3B,KAAA;UACAD,GAAA;QACA;QACA;QACA;QACA;QACA;QACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,EACA;QACA6B,SAAA;QACAC,YAAA,GACA;UACAC,WAAA;UACA5B,YAAA;YACAD,IAAA;UACA;UACAiB,OAAA,WAAAA,QAAAa,KAAA,EAAAC,GAAA;YACAvD,KAAA,CAAAwD,UAAA,CAAAF,KAAA,EAAAC,GAAA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAAA;MAEA;IACA;EACA;EACAE,QAAA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OAIAT,MAAA,CAAAU,cAAA;UAAA;YAFAV,MAAA,CAAAvC,UAAA,CAAAC,SAAA,CAAAiD,IAAA,CACA,UAAA5B,IAAA;cAAA,OAAAA,IAAA,CAAApB,GAAA;YAAA,CACA,EAAAU,OAAA,GAAAkC,QAAA,CAAAK,IAAA;YAAAL,QAAA,CAAAE,IAAA;YAAA,OAIAT,MAAA,CAAAa,WAAA;UAAA;YAFAb,MAAA,CAAAvC,UAAA,CAAAC,SAAA,CAAAiD,IAAA,CACA,UAAA5B,IAAA;cAAA,OAAAA,IAAA,CAAApB,GAAA;YAAA,CACA,EAAAU,OAAA,GAAAkC,QAAA,CAAAK,IAAA;YAAAL,QAAA,CAAAO,EAAA,GAEA5C,OAAA;YAAAqC,QAAA,CAAAE,IAAA;YAAA,OAAAT,MAAA,CAAAU,cAAA;UAAA;YAAAH,QAAA,CAAAQ,EAAA,GAAAR,QAAA,CAAAK,IAAA;YAAAL,QAAA,CAAAO,EAAA,CAAA3C,GAAA,CAAA6C,IAAA,CAAAT,QAAA,CAAAO,EAAA,EAAAP,QAAA,CAAAQ,EAAA;YAAAR,QAAA,CAAAE,IAAA;YAAA,OAIAT,MAAA,CAAAU,cAAA;UAAA;YAFAV,MAAA,CAAAvC,UAAA,CAAAC,SAAA,CAAAiD,IAAA,CACA,UAAA5B,IAAA;cAAA,OAAAA,IAAA,CAAApB,GAAA;YAAA,CACA,EAAAU,OAAA,GAAAkC,QAAA,CAAAK,IAAA;YAAAL,QAAA,CAAAE,IAAA;YAAA,OAGAT,MAAA,CAAAU,cAAA;UAAA;YADAV,MAAA,CAAAvC,UAAA,CAAAC,SAAA,CAAAiD,IAAA,WAAA5B,IAAA;cAAA,OAAAA,IAAA,CAAApB,GAAA;YAAA,GAAAU,OAAA,GAAAkC,QAAA,CAAAK,IAAA;YAEAZ,MAAA,CAAAiB,IAAA;UAAA;UAAA;YAAA,OAAAV,QAAA,CAAAW,IAAA;QAAA;MAAA,GAAAd,OAAA;IAAA;EACA;EACAe,OAAA;IACAT,cAAA,WAAAA,eAAAU,IAAA;MAAA,OAAAnB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAkB,SAAA;QAAA,IAAAC,GAAA,EAAAjD,OAAA;QAAA,OAAA6B,mBAAA,GAAAG,IAAA,UAAAkB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhB,IAAA,GAAAgB,SAAA,CAAAf,IAAA;YAAA;cAAAe,SAAA,CAAAf,IAAA;cAAA,OACAxE,6BAAA;gBACAwF,cAAA,EAAAL;cACA;YAAA;cAFAE,GAAA,GAAAE,SAAA,CAAAZ,IAAA;cAGAvC,OAAA,GAAAiD,GAAA,CAAAI,IAAA,CAAAC,GAAA,WAAA5C,IAAA,EAAAY,KAAA;gBAAA;kBACA/B,KAAA,EAAAmB,IAAA,CAAA6C,YAAA;kBACAC,KAAA,EAAA9C,IAAA,CAAA+C;gBACA;cAAA;cAAA,OAAAN,SAAA,CAAAO,MAAA,WACA1D,OAAA;YAAA;YAAA;cAAA,OAAAmD,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACA;IACAR,WAAA,WAAAA,YAAAO,IAAA;MAAA,OAAAnB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA6B,SAAA;QAAA,IAAAV,GAAA,EAAAjD,OAAA;QAAA,OAAA6B,mBAAA,GAAAG,IAAA,UAAA4B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1B,IAAA,GAAA0B,SAAA,CAAAzB,IAAA;YAAA;cAAAyB,SAAA,CAAAzB,IAAA;cAAA,OACA3E,OAAA;YAAA;cAAAwF,GAAA,GAAAY,SAAA,CAAAtB,IAAA;cACAvC,OAAA,GAAAiD,GAAA,CAAAI,IAAA,CAAAC,GAAA,WAAA5C,IAAA,EAAAY,KAAA;gBAAA;kBACA/B,KAAA,EAAAmB,IAAA,CAAA6C,YAAA;kBACAC,KAAA,EAAA9C,IAAA,CAAA+C;gBACA;cAAA;cAAA,OAAAI,SAAA,CAAAH,MAAA,WACA1D,OAAA;YAAA;YAAA;cAAA,OAAA6D,SAAA,CAAAhB,IAAA;UAAA;QAAA,GAAAc,QAAA;MAAA;IACA;IAEAG,UAAA,WAAAA,WAAA/F,IAAA;MACA8B,OAAA,CAAAC,GAAA,CAAA/B,IAAA;MACA,KAAAsC,iBAAA,CAAAQ,WAAA;MACA,KAAAtC,OAAA;IACA;IACAwF,SAAA,WAAAA,UAAA;MACA,KAAAxF,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAAb,oBAAA;IACA;IACAkF,IAAA,WAAAA,KAAA;MACA,KAAAlF,oBAAA;IACA;IACAA,oBAAA,WAAAA,qBAAA;MAAA,IAAAsG,MAAA;MAAA,OAAApC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAmC,SAAA;QAAA,IAAAhB,GAAA;QAAA,OAAApB,mBAAA,GAAAG,IAAA,UAAAkC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhC,IAAA,GAAAgC,SAAA,CAAA/B,IAAA;YAAA;cAAA+B,SAAA,CAAA/B,IAAA;cAAA,OACA1E,qBAAA,CAAA0G,aAAA,CAAAA,aAAA;gBACAC,aAAA,GACA;kBACAC,GAAA;kBACAb,KAAA;kBACAc,IAAA;kBACAC,WAAA;gBACA,EACA;gBACAC,IAAA,EAAAT,MAAA,CAAA3D,iBAAA,CAAAQ,WAAA;gBACA6D,QAAA,EAAAV,MAAA,CAAA3D,iBAAA,CAAAS,QAAA;gBAEA6D,MAAA;gBACAC,QAAA;gBACAC,SAAA;gBACAC,UAAA;gBACAnG,MAAA;gBAEAG,YAAA;gBACAC,aAAA;gBACAC,QAAA;gBACAC,aAAA;gBACAC,WAAA;gBACAC,MAAA;cAAA,GACA6E,MAAA,CAAAtF,QAAA;gBACAE,SAAA,EAAAoF,MAAA,CAAAtF,QAAA,CAAAG,OAAA,GACArB,KAAA,CAAAwG,MAAA,CAAAtF,QAAA,CAAAE,SAAA,EAAAmG,MAAA,iBACA;gBACAlG,OAAA,EAAAmF,MAAA,CAAAtF,QAAA,CAAAG,OAAA,GACArB,KAAA,CAAAwG,MAAA,CAAAtF,QAAA,CAAAG,OAAA,EAAAkG,MAAA,iBACA;cAAA,EACA;YAAA;cA/BA9B,GAAA,GAAAkB,SAAA,CAAA5B,IAAA;cAgCA,IAAAU,GAAA,CAAA+B,SAAA;gBACAhB,MAAA,CAAA3D,iBAAA,CAAAc,SAAA,GAAA8B,GAAA,CAAAI,IAAA,CAAAA,IAAA,CAAAC,GAAA,WAAA5C,IAAA;kBAAA,OAAA0D,aAAA,CAAAA,aAAA,KACA1D,IAAA;oBACAuE,YAAA,EAAAzH,KAAA,CAAAkD,IAAA,CAAAuE,YAAA,EAAAF,MAAA;kBAAA;gBAAA,CACA;gBACAlF,OAAA,CAAAC,GAAA,CAAAmD,GAAA;gBACAe,MAAA,CAAA3D,iBAAA,CAAAU,KAAA,GAAAkC,GAAA,CAAAI,IAAA,CAAAyB,UAAA;cACA;gBACAd,MAAA,CAAAkB,QAAA,CAAAC,KAAA,CAAAlC,GAAA,CAAAmC,OAAA;cACA;YAAA;YAAA;cAAA,OAAAjB,SAAA,CAAAtB,IAAA;UAAA;QAAA,GAAAoB,QAAA;MAAA;IACA;IACAoB,YAAA,WAAAA,aAAA;MACA,KAAA7G,WAAA;MACA,KAAAH,aAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAmD,UAAA,WAAAA,WAAAF,KAAA,EAAAC,GAAA,EAAA/B,IAAA;MACAK,OAAA,CAAAC,GAAA,CAAAwB,KAAA,EAAAC,GAAA,EAAA/B,IAAA;MACA,IAAAA,IAAA;QACA,KAAAhB,WAAA;QACA,KAAAN,gBAAA;UACAoH,EAAA,EAAA/D,GAAA,CAAAgE,EAAA;UACAC,KAAA;UACAjE,GAAA,EAAAA;QACA;MACA;MACA,KAAAlD,aAAA;IACA;IACAsC,YAAA,WAAAA,aAAA;MAAA,IAAA8E,MAAA;MAAA,OAAA7D,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA4D,SAAA;QAAA,IAAAzC,GAAA;QAAA,OAAApB,mBAAA,GAAAG,IAAA,UAAA2D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzD,IAAA,GAAAyD,SAAA,CAAAxD,IAAA;YAAA;cAAA,MACAqD,MAAA,CAAAhH,cAAA,CAAAoH,MAAA;gBAAAD,SAAA,CAAAxD,IAAA;gBAAA;cAAA;cACAqD,MAAA,CAAAP,QAAA,CAAAY,OAAA;cAAA,OAAAF,SAAA,CAAAlC,MAAA;YAAA;cAAAkC,SAAA,CAAAxD,IAAA;cAAA,OAGAzE,2BAAA;gBACAoI,EAAA,EAAAN,MAAA,CAAAhH,cAAA,CAAA6E,GAAA,WAAA5C,IAAA;kBAAA,OAAAA,IAAA,CAAA6E,EAAA;gBAAA,GAAAS,IAAA;gBACAjD,IAAA;gBACA;cACA;YAAA;cAJAE,GAAA,GAAA2C,SAAA,CAAArD,IAAA;cAKA,IAAAU,GAAA,CAAA+B,SAAA;gBACAnF,OAAA,CAAAC,GAAA,CAAAmD,GAAA;gBACA1F,YAAA,CAAA0F,GAAA,CAAAI,IAAA;cACA;gBACAoC,MAAA,CAAAP,QAAA,CAAAC,KAAA,CAAAlC,GAAA,CAAAmC,OAAA;cACA;YAAA;YAAA;cAAA,OAAAQ,SAAA,CAAA/C,IAAA;UAAA;QAAA,GAAA6C,QAAA;MAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAO,gBAAA,WAAAA,iBAAAC,GAAA;MACArG,OAAA,CAAAC,GAAA,iBAAAqG,MAAA,CAAAD,GAAA;MACA,KAAA7F,iBAAA,CAAAS,QAAA,GAAAoF,GAAA;MACA,KAAAtD,IAAA;IACA;IACAwD,mBAAA,WAAAA,oBAAAF,GAAA;MACArG,OAAA,CAAAC,GAAA,wBAAAqG,MAAA,CAAAD,GAAA;MACA,KAAA7F,iBAAA,CAAAQ,WAAA,GAAAqF,GAAA;MACA,KAAAtD,IAAA;IACA;IACAyD,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA7H,cAAA,GAAA6H,SAAA;IACA;EACA;AACA", "ignoreList": []}]}