{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlRecord\\index.vue?vue&type=style&index=0&id=64a16d22&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlRecord\\index.vue", "mtime": 1755674552409}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoubXQyMCB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQp9DQoubGF5b3V0ew0KICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA5MHB4KTsNCiAgb3ZlcmZsb3c6IGF1dG87DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkgBA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/accessControl/accessControlRecord", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport dialogFormLook from './dialogFormLook.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n// import * as moment from 'moment'\r\nimport dayjs from 'dayjs'\r\n\r\nimport {\r\n  GetRole,\r\n  GetTrafficRecordList,\r\n  ExportEntranceTrafficRecord,\r\n  GetDictionaryDetailListByCode\r\n} from '@/api/business/accessControl'\r\n\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: dialogFormLook,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        P_Name: '',\r\n        BeginTime: '',\r\n        EndTime: '',\r\n        EntranceType: '',\r\n        EquipmentName: '',\r\n        Position: '',\r\n        Position_Name: '',\r\n        Traffic_Way: '',\r\n        P_Type: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'P_Name', // 字段ID\r\n            label: '姓名', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            // width: '240px',\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'BeginTime',\r\n            label: '通行开始时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EndTime',\r\n            label: '通行结束时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EntranceType', // 字段ID\r\n            label: '门禁类型', // Form的label\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentName', // 字段ID\r\n            label: '门禁设备', // Form的label\r\n            type: 'input',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position', // 字段ID\r\n            label: '安装位置', // Form的label\r\n            type: 'input',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position_Name', // 字段ID\r\n            label: '岗位类型', // Form的label\r\n            type: 'select',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Traffic_Way', // 字段ID\r\n            label: '通行方式', // Form的label\r\n            type: 'select',\r\n\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'P_Type', // 字段ID\r\n            label: '人员类型', // Form的label\r\n            type: 'select',\r\n\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            // {\r\n            //   text: '新增',\r\n            //   round: false, // 是否圆角\r\n            //   plain: false, // 是否朴素\r\n            //   circle: false, // 是否圆形\r\n            //   loading: false, // 是否加载中\r\n            //   disabled: false, // 是否禁用\r\n            //   icon: '', //  图标\r\n            //   autofocus: false, // 是否聚焦\r\n            //   type: 'primary', // primary / success / warning / danger / info / text\r\n            //   size: 'small', // medium / small / mini\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleCreate()\r\n            //   }\r\n            // },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleExport()\r\n              }\r\n            }\r\n            // {\r\n            //   text: '批量导出',\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleAllExport()\r\n            //   }\r\n            // }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          // {\r\n          //   width: 60,\r\n          //   label: '序号',\r\n          //   otherOptions: {\r\n          //     type: 'index',\r\n          //     align: 'center'\r\n          //   } // key\r\n          // },\r\n          {\r\n            label: '姓名',\r\n            key: 'P_Name'\r\n          },\r\n          // {\r\n          //   label: '图片',\r\n          //   key: 'Name'\r\n          // },\r\n          {\r\n            label: '性别',\r\n            key: 'P_Sex'\r\n          },\r\n          {\r\n            label: '联系方式',\r\n            key: 'Contact_Way'\r\n          },\r\n          {\r\n            label: '人员类型',\r\n            key: 'P_Type'\r\n          },\r\n          {\r\n            label: '岗位类型',\r\n            key: 'Position_Name'\r\n          },\r\n          {\r\n            label: '门禁类型',\r\n            key: 'Entrance_Equipment_Type'\r\n          },\r\n          {\r\n            label: '门禁名称',\r\n            key: 'Entrance_Equipment_Name'\r\n          },\r\n          {\r\n            label: '门禁位置',\r\n            key: 'Position'\r\n          },\r\n          {\r\n            label: '通行时间',\r\n            key: 'Traffic_Time'\r\n          },\r\n          {\r\n            label: '通行方式',\r\n            key: 'Traffic_Way'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          }\r\n          // {\r\n          //   actionLabel: '编辑',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleEdit(index, row, 'edit')\r\n          //   }\r\n          // },\r\n          // {\r\n          //   actionLabel: '删除',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleDelete(index, row)\r\n          //   }\r\n          // }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  async created() {\r\n    // 门禁类型\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'EntranceType'\r\n    ).options = await this.initDeviceType('Entrance_Type')\r\n    // 岗位类型\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'Position_Name'\r\n    ).options = await this.initGetRole('Entrance_Type')\r\n\r\n    console.log(await this.initDeviceType('Position_Type'), '1221212222222')\r\n    // 通行方式\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'Traffic_Way'\r\n    ).options = await this.initDeviceType('Traffic_Way')\r\n    // 人员类型\r\n    this.customForm.formItems.find((item) => item.key === 'P_Type').options =\r\n      await this.initDeviceType('P_Type')\r\n    this.init()\r\n  },\r\n  methods: {\r\n    async initDeviceType(code) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode: code\r\n      })\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    // 岗位类型\r\n    async initGetRole(code) {\r\n      const res = await GetRole({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.GetTrafficRecordList()\r\n    },\r\n    init() {\r\n      this.GetTrafficRecordList()\r\n    },\r\n    async GetTrafficRecordList() {\r\n      const res = await GetTrafficRecordList({\r\n        ParameterJson: [\r\n          {\r\n            Key: 'string',\r\n            Value: [null],\r\n            Type: 'string',\r\n            Filter_Type: 'string'\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n\r\n        Search: '',\r\n        SortName: '',\r\n        SortOrder: '',\r\n        TotalCount: 0,\r\n        P_Name: '',\r\n\r\n        EntranceType: '',\r\n        EquipmentName: '',\r\n        Position: '',\r\n        Position_Name: '',\r\n        Traffic_Way: '',\r\n        P_Type: '',\r\n        ...this.ruleForm,\r\n        BeginTime: this.ruleForm.EndTime\r\n          ? dayjs(this.ruleForm.BeginTime).format('YYYY-MM-DD')\r\n          : '',\r\n        EndTime: this.ruleForm.EndTime\r\n          ? dayjs(this.ruleForm.EndTime).format('YYYY-MM-DD')\r\n          : ''\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n          ...item,\r\n          Traffic_Time: dayjs(item.Traffic_Time).format('YYYY-MM-DD HH:mm:ss')\r\n        }))\r\n        console.log(res)\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.dialogVisible = true\r\n    },\r\n    // handleDelete(index, row) {\r\n    //   console.log(index, row)\r\n    //   console.log(this)\r\n    //   this.$confirm('确认删除？', {\r\n    //     type: 'warning'\r\n    //   })\r\n    //     .then(async(_) => {\r\n    //       const res = await DeleteEquipment({\r\n    //         IDs: [row.ID]\r\n    //       })\r\n    //       if (res.IsSucceed) {\r\n    //         this.init()\r\n    //       }\r\n    //     })\r\n    //     .catch((_) => {})\r\n    // },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.componentsConfig = {\r\n          ID: row.Id,\r\n          title: '查看',\r\n          row: row\r\n        }\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    async handleExport() {\r\n      if (this.tableSelection.length == 0) {\r\n        this.$message.warning('请选择数据在导出')\r\n        return\r\n      }\r\n      const res = await ExportEntranceTrafficRecord({\r\n        id: this.tableSelection.map((item) => item.Id).join(','),\r\n        code: 'accessControlRecord'\r\n        // ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    // async handleAllExport() {\r\n    //   const res = await ExportEntranceTrafficRecord({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: true,\r\n    //     Ids: [],\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   }\r\n    // },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}