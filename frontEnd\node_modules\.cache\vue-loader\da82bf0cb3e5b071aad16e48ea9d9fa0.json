{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\personnelManagement\\pjIndex.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\personnelManagement\\pjIndex.vue", "mtime": 1755674552431}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["pjIndex.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "pjIndex.vue", "sourceRoot": "src/views/business/personnelManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 personnelManagement\">\r\n    <custom-layout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"submitForm\"\r\n          @resetForm=\"fetchData\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </custom-layout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport getGridByCode from '../safetyManagement/mixins/index'\r\nimport {\r\n  querypersonnel,\r\n  DeletePersonnel,\r\n  DownloadPersonnelsTemplate,\r\n  DownloadPersonnelsToExcel,\r\n  ExportPersonnelList\r\n} from '@/api/business/personnelManagementV2.js'\r\nimport { GetDepartment, GetCompany, GetMesTeams } from '@/api/business/accessControl'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport addDialog from './components/pjAddDialog'\r\nimport DialogFormImport from './components/pjImportFile'\r\nimport DialogPhotoFormImport from './components/pjPhotoImportFile'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nexport default {\r\n  components: {\r\n    CustomLayout,\r\n    CustomTable,\r\n    CustomForm\r\n  },\r\n  mixins: [getGridByCode, addRouterPage],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        name: '',\r\n        mobile: '',\r\n        personnelType: null,\r\n        companyId: '',\r\n        departmentId: '',\r\n        MesTeamId: '',\r\n        personnelStatus: null\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'name',\r\n            label: '姓名',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'mobile',\r\n            label: '联系方式',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'personnelType',\r\n            label: '人员类型',\r\n            type: 'select',\r\n            options: [\r\n              {\r\n                label: '系统人员',\r\n                value: '1'\r\n              },\r\n              {\r\n                label: '普通人员',\r\n                value: '2'\r\n              }\r\n            ],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'companyId',\r\n            label: '所属公司',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'departmentId',\r\n            label: '所属部门',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'personnelStatus',\r\n            label: '状态',\r\n            type: 'select',\r\n            options: [\r\n              {\r\n                label: '在职',\r\n                value: '1'\r\n              },\r\n              {\r\n                label: '离职',\r\n                value: '2'\r\n              }\r\n            ],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'MesTeamId',\r\n            label: 'MES班组',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n              filterable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '导入模板下载',\r\n              icon: 'el-icon-download',\r\n              onclick: () => {\r\n                this.handleDownTemplate()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导入',\r\n              icon: 'el-icon-upload2',\r\n              onclick: () => {\r\n                this.handleImport()\r\n              }\r\n            },\r\n            {\r\n              text: '照片导入',\r\n              icon: 'el-icon-upload2',\r\n              onclick: () => {\r\n                this.handlePhotoImport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导出',\r\n              icon: 'el-icon-download',\r\n              onclick: () => {\r\n                this.handleExport()\r\n              }\r\n            },\r\n            {\r\n              text: '新增',\r\n              icon: 'el-icon-plus',\r\n              type: 'primary',\r\n              onclick: () => {\r\n                this.handleClick('add')\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        pageSizeOptions: [20, 40, 60, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center',\r\n          width: '130px'\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleWatch(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleClick('edit', row)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDele(row.Id)\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      multipleSelection: [],\r\n      dialogVisible: false,\r\n      currentComponent: null,\r\n      dialogTitle: '新增',\r\n      componentsFuns: {\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.fetchData()\r\n        }\r\n      },\r\n      componentsConfig: {},\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/pjInfo',\r\n          hidden: true,\r\n          component: () => import('./pjInfo.vue'),\r\n          meta: { title: '人员详情' },\r\n          name: 'PersonnelManagementPJInfo'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  async created() {\r\n    this.fetchData()\r\n    this.getMesTeams()\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'departmentId'\r\n    ).options = await this.initGetDepartment()\r\n    // 所属单位\r\n    this.customForm.formItems.find((item) => item.key === 'companyId').options =\r\n      await this.initGetCompany()\r\n    this.getGridByCodeRender('PJPersonnelManagement', [\r\n      { key: 'Gender', Tag: 'span', condition: 1, val1: '男', val2: '女' },\r\n      {\r\n        key: 'PersonnelStatus',\r\n        Tag: 'span',\r\n        condition: 1,\r\n        val1: '在职',\r\n        val2: '离职'\r\n      },\r\n      {\r\n        key: 'PersonnelType',\r\n        Tag: 'span',\r\n        condition: 1,\r\n        val1: '系统人员',\r\n        val2: '普通人员'\r\n      }\r\n    ])\r\n  },\r\n  async mounted() {\r\n    this.customForm.formItems[1].options =\r\n      await this.getDictionaryDetailListByCode('PatrolResult')\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      querypersonnel({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.tableData = res.Data.Data\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n\r\n    submitForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData()\r\n    },\r\n    async initGetDepartment() {\r\n      const res = await GetDepartment({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    async initGetCompany() {\r\n      const res = await GetCompany({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    handleSizeChange(val) {\r\n      this.customTableConfig.pageSize = val\r\n      this.fetchData()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.customTableConfig.currentPage = val\r\n      this.fetchData()\r\n    },\r\n    handleSelectionChange(data) {\r\n      this.multipleSelection = data\r\n    },\r\n    // async handleExport() {\r\n    //   const res = await DownloadPersonnelsToExcel({\r\n    //     id: this.multipleSelection.map(v => v.Id)\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     downloadFile(res.Data, '人员管理')\r\n    //   } else {\r\n    //     this.$message({\r\n    //       type: 'error',\r\n    //       message: res.Message\r\n    //     })\r\n    //   }\r\n    // },\r\n    // v2 版本导出\r\n    async handleExport() {\r\n      const res = await ExportPersonnelList({\r\n        Id: this.multipleSelection.map((v) => v.Id).toString(),\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        downloadFile(res.Data, '人员管理')\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    handleClick(type, data) {\r\n      this.dialogVisible = true\r\n      this.currentComponent = addDialog\r\n      if (type == 'add') {\r\n        this.dialogTitle = '新增'\r\n        this.componentsConfig = {\r\n          name: '',\r\n          mobile: '',\r\n          personnelType: null,\r\n          companyId: '',\r\n          departmentId: '',\r\n          personnelStatus: null\r\n        }\r\n      } else {\r\n        this.dialogTitle = '编辑'\r\n        this.componentsConfig = data\r\n      }\r\n    },\r\n    handleWatch(Id) {\r\n      this.$router.push({\r\n        name: 'PersonnelManagementPJInfo',\r\n        query: { pg_redirect: this.$route.name, Id }\r\n      })\r\n    },\r\n    handleDele(id) {\r\n      this.$confirm('是否确定删除该数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeletePersonnel({ id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                type: 'error',\r\n                message: res.Message\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => { })\r\n    },\r\n    handleDownTemplate() {\r\n      DownloadPersonnelsTemplate({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data, '授权名单导入模板')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    async handleImport() {\r\n      this.dialogTitle = '批量导入'\r\n      this.currentComponent = DialogFormImport\r\n      this.componentsConfig = {\r\n        disabled: true,\r\n        title: '批量导入'\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    async handlePhotoImport() {\r\n      this.dialogTitle = '数据批量导入'\r\n      this.currentComponent = DialogPhotoFormImport\r\n      this.componentsConfig = {\r\n        disabled: true,\r\n        title: '数据批量导入'\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    async getMesTeams() {\r\n      const res = await GetMesTeams()\r\n      if (res.IsSucceed) {\r\n        this.customForm.formItems.find((item) => item.key === 'MesTeamId').options =\r\n          res.Data.map(v => {\r\n            v.value = v.MesTeamId\r\n            v.label = v.TeamName\r\n            return v\r\n          })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n  <style scoped lang='scss'>\r\n  .personnelManagement{\r\n    // height: calc(100vh - 90px);\r\n    // overflow: hidden;\r\n  }\r\n</style>\r\n"]}]}