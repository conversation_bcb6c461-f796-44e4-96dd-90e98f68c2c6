{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\alarmNotification\\index.vue?vue&type=style&index=0&id=3394966e&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\alarmNotification\\index.vue", "mtime": 1755674552429}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoqIHsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCn0NCg0KLmxheW91dCB7DQogIGhlaWdodDogMTAwJTsNCiAgd2lkdGg6IDEwMCU7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgOjp2LWRlZXAgew0KICAgIC5DdXN0b21MYXlvdXQgew0KICAgICAgLmxheW91dFRhYmxlIHsNCiAgICAgICAgaGVpZ2h0OiAwOw0KICAgICAgICAuQ3VzdG9tVGFibGUgew0KICAgICAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgICAgLnRhYmxlIHsNCiAgICAgICAgICAgIGZsZXg6IDE7DQogICAgICAgICAgICBoZWlnaHQ6IDA7DQogICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgICAgICAgIC5lbC10YWJsZSB7DQogICAgICAgICAgICAgIGZsZXg6IDE7DQogICAgICAgICAgICAgIGhlaWdodDogMDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLndhcm5pbmcgew0KICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogIHdpZHRoOiA4cHg7DQogIGhlaWdodDogOHB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIG1hcmdpbi1yaWdodDogNXB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmI2YjdmOw0KfQ0KLmhhbmRlbCB7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgd2lkdGg6IDEwcHg7DQogIGhlaWdodDogMTBweDsNCiAgYm9yZGVyLXJhZGl1czogNXB4Ow0KICBtYXJnaW4tcmlnaHQ6IDVweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzM2OGRmZjsNCn0NCi5jbG9zZSB7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgd2lkdGg6IDEwcHg7DQogIGhlaWdodDogMTBweDsNCiAgYm9yZGVyLXJhZGl1czogNXB4Ow0KICBtYXJnaW4tcmlnaHQ6IDVweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzM3YmU2YjsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAobA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/myWorkBench/alarmNotification", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n        >\r\n          <template #customBtn=\"{slotScope}\"><el-button v-if=\"slotScope.Status == 1\" type=\"text\" @click=\"handelClose(slotScope)\">关闭</el-button></template>\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport { GetModule, GetTypesByModule, GetWarnPageList, GetWarningStatus, GetWarningModule, GetWarningType, CloseWarning } from '@/api/business/myWorkBench'\r\nexport default {\r\n  name: 'MyTasks',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        Name: '',\r\n        OperateType: '',\r\n        StartTime: null,\r\n        EndTime: null,\r\n        Module: '',\r\n        Type: '',\r\n        Date: [],\r\n        Status: null\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Name',\r\n            label: '告警名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Module',\r\n            label: '业务模块',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              this.getTypesByModule(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Type',\r\n            label: '告警类型',\r\n            type: 'select',\r\n            otherOptions: {\r\n              disabled: true,\r\n              clearable: true\r\n            },\r\n            options: [\r\n              {\r\n                label: '全部',\r\n                value: ''\r\n              }\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Status',\r\n            label: '状态',\r\n            type: 'select',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Date',\r\n            label: '告警时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              type: 'datetimerange',\r\n              rangeSeparator: '至',\r\n              startPlaceholder: '开始日期',\r\n              endPlaceholder: '结束日期',\r\n              clearable: true,\r\n              valueFormat: 'yyyy-MM-dd HH:mm'\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n              if (e && e.length !== 0) {\r\n                this.ruleForm.StartTime = e[0]\r\n                this.ruleForm.EndTime = e[1]\r\n              } else {\r\n                this.ruleForm.StartTime = null\r\n                this.ruleForm.EndTime = null\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: []\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          {\r\n            label: '告警时间',\r\n            key: 'AlarmTime'\r\n          },\r\n          {\r\n            label: '状态',\r\n            key: 'Status',\r\n            render: (row) => {\r\n              const h = this.$createElement\r\n              return h('div', {}, [\r\n                h(\r\n                  'span',\r\n                  { class: row.Status == 1 ? 'warning' : row.Status == 2 ? 'close' : row.Status == 3 ? 'handel' : '' },\r\n                  ''\r\n                ),\r\n                h('span', {}, `${row.Status == 1 ? '告警中' : row.Status == 2 ? '已关闭' : row.Status == 3 ? '已处理' : ''}`)\r\n              ])\r\n            }\r\n          },\r\n          {\r\n            label: '告警类型',\r\n            key: 'Type'\r\n          },\r\n          {\r\n            label: '告警名称',\r\n            key: 'Name'\r\n          },\r\n          {\r\n            label: '来源',\r\n            key: 'Source'\r\n          },\r\n          {\r\n            label: '业务模块',\r\n            key: 'Module'\r\n          },\r\n          {\r\n            label: '处理内容',\r\n            key: 'Content'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.getDetail(row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getModule()\r\n    this.getWarningStatus()\r\n    this.onFresh()\r\n  },\r\n  methods: {\r\n    // 获取业务模块下拉\r\n    getModule() {\r\n      GetModule({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data || []\r\n          const arr = []\r\n          data.forEach((item) => {\r\n            const obj = {\r\n              label: item,\r\n              value: item\r\n            }\r\n            arr.push(obj)\r\n          })\r\n          arr.unshift({\r\n            label: '全部',\r\n            value: ''\r\n          })\r\n          console.log(arr)\r\n          this.customForm.formItems.find(\r\n            (v) => v.key == 'Module'\r\n          ).options = arr\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 获取告警类型\r\n    getTypesByModule(value) {\r\n      this.ruleForm.Type = ''\r\n      if (value) {\r\n        GetTypesByModule({ Type: 2, Module: value }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            const data = res.Data || []\r\n            const arr = []\r\n            data.forEach((item) => {\r\n              const obj = {\r\n                label: item,\r\n                value: item\r\n              }\r\n              arr.push(obj)\r\n            })\r\n            arr.unshift({\r\n              label: '全部',\r\n              value: ''\r\n            })\r\n            console.log(arr)\r\n            this.customForm.formItems.find(\r\n              (v) => v.key == 'Type'\r\n            ).otherOptions.disabled = false\r\n            this.customForm.formItems.find(\r\n              (v) => v.key == 'Type'\r\n            ).options = arr\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      } else {\r\n        this.customForm.formItems.find(\r\n          (v) => v.key == 'Type'\r\n        ).otherOptions.disabled = true\r\n        this.customForm.formItems.find((v) => v.key == 'Type').options =\r\n          []\r\n      }\r\n    },\r\n    // 获取任务状态\r\n    getWarningStatus() {\r\n      GetWarningStatus({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data || []\r\n          const arr = []\r\n          data.forEach((item) => {\r\n            const obj = {\r\n              label: item.Name,\r\n              value: item.Value\r\n            }\r\n            arr.push(obj)\r\n          })\r\n          arr.unshift({\r\n            label: '全部',\r\n            value: null\r\n          })\r\n          console.log(arr)\r\n          this.customForm.formItems.find(\r\n            (v) => v.key == 'Status'\r\n          ).options = arr\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.customForm.formItems.find(\r\n        (v) => v.key == 'Type'\r\n      ).otherOptions.disabled = true\r\n      this.customForm.formItems.find(\r\n        (v) => v.key == 'Type'\r\n      ).options = [{\r\n        label: '全部',\r\n        value: ''\r\n      }]\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.fetchData()\r\n    },\r\n    async fetchData() {\r\n      this.customTableConfig.loading = true\r\n      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {\r\n        this.ruleForm.StartTime = null\r\n        this.ruleForm.EndTime = null\r\n      }\r\n      await GetWarnPageList({\r\n        ...this.ruleForm,\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.tableData = res.Data.Data\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.customTableConfig.loading = false\r\n      })\r\n    },\r\n\r\n    // 关闭\r\n    handelClose(row) {\r\n      this.$confirm('是否关闭?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        CloseWarning({ Id: row.Id }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '关闭成功!'\r\n            })\r\n            this.onFresh()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    // 查看详情\r\n    getDetail(item) {\r\n      console.log(item)\r\n      if (item.Url) {\r\n        let platform = '' // 子应用\r\n        if (item.ModuleName == '后台设置') {\r\n          platform = 'management'\r\n        } else {\r\n          platform = 'digitalfactory'\r\n        }\r\n        this.$qiankun.switchMicroAppFn(\r\n          platform,\r\n          item.ModuleCode,\r\n          item.ModuleId,\r\n          item.Url\r\n        )\r\n      } else {\r\n        const platform = 'digitalfactory'\r\n        const code = 'szgc'\r\n        const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\r\n        let url = ''\r\n        if (item.Module == '能耗管理') {\r\n          url = '/business/energy/alarmDetail'\r\n        } else if (item.Module == '车辆道闸') {\r\n          url = '/bussiness/vehicle/alarm-info'\r\n        } else if (item.Module == '门禁管理') {\r\n          url = '/business/AccessControlAlarmDetails'\r\n        } else if (item.Module == '安防管理') {\r\n          url = '/business/equipmentAlarm'\r\n        } else if (item.Module == '危化品管理') {\r\n          url = '/business/hazchem/alarmInformation'\r\n        } else if (item.Module == '环境管理') {\r\n          url = '/business/environment/alarmInformation'\r\n        } else if (item.Module == '访客管理') {\r\n          url = '/business/energy/alarmDetail'\r\n          console.log('访客管理')\r\n        }\r\n        this.$qiankun.switchMicroAppFn(platform, code, id, url)\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.onFresh()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.onFresh()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.layout {\r\n  height: 100%;\r\n  width: 100%;\r\n  position: absolute;\r\n  ::v-deep {\r\n    .CustomLayout {\r\n      .layoutTable {\r\n        height: 0;\r\n        .CustomTable {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          .table {\r\n            flex: 1;\r\n            height: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            .el-table {\r\n              flex: 1;\r\n              height: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.warning {\r\n  display: inline-block;\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 4px;\r\n  margin-right: 5px;\r\n  background-color: #fb6b7f;\r\n}\r\n.handel {\r\n  display: inline-block;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 5px;\r\n  margin-right: 5px;\r\n  background-color: #368dff;\r\n}\r\n.close {\r\n  display: inline-block;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 5px;\r\n  margin-right: 5px;\r\n  background-color: #37be6b;\r\n}\r\n</style>\r\n"]}]}