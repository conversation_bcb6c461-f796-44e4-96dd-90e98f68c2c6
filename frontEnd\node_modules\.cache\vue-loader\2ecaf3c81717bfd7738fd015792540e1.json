{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\alarmNotification\\index.vue?vue&type=style&index=0&id=3394966e&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\myWorkBench\\alarmNotification\\index.vue", "mtime": 1755506574393}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoqIHsKICBib3gtc2l6aW5nOiBib3JkZXItYm94Owp9CgoubGF5b3V0IHsKICBoZWlnaHQ6IDEwMCU7CiAgd2lkdGg6IDEwMCU7CiAgcG9zaXRpb246IGFic29sdXRlOwogIDo6di1kZWVwIHsKICAgIC5DdXN0b21MYXlvdXQgewogICAgICAubGF5b3V0VGFibGUgewogICAgICAgIGhlaWdodDogMDsKICAgICAgICAuQ3VzdG9tVGFibGUgewogICAgICAgICAgaGVpZ2h0OiAxMDAlOwogICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAgICAudGFibGUgewogICAgICAgICAgICBmbGV4OiAxOwogICAgICAgICAgICBoZWlnaHQ6IDA7CiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAgICAgIC5lbC10YWJsZSB7CiAgICAgICAgICAgICAgZmxleDogMTsKICAgICAgICAgICAgICBoZWlnaHQ6IDA7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn0KCi53YXJuaW5nIHsKICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgd2lkdGg6IDhweDsKICBoZWlnaHQ6IDhweDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgbWFyZ2luLXJpZ2h0OiA1cHg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZiNmI3ZjsKfQouaGFuZGVsIHsKICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgd2lkdGg6IDEwcHg7CiAgaGVpZ2h0OiAxMHB4OwogIGJvcmRlci1yYWRpdXM6IDVweDsKICBtYXJnaW4tcmlnaHQ6IDVweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjMzY4ZGZmOwp9Ci5jbG9zZSB7CiAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogIHdpZHRoOiAxMHB4OwogIGhlaWdodDogMTBweDsKICBib3JkZXItcmFkaXVzOiA1cHg7CiAgbWFyZ2luLXJpZ2h0OiA1cHg7CiAgYmFja2dyb3VuZC1jb2xvcjogIzM3YmU2YjsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkbA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/myWorkBench/alarmNotification", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n        >\n          <template #customBtn=\"{slotScope}\"><el-button v-if=\"slotScope.Status == 1\" type=\"text\" @click=\"handelClose(slotScope)\">关闭</el-button></template>\n        </CustomTable>\n      </template>\n    </CustomLayout>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\nimport { GetModule, GetTypesByModule, GetWarnPageList, GetWarningStatus, GetWarningModule, GetWarningType, CloseWarning } from '@/api/business/myWorkBench'\nexport default {\n  name: 'MyTasks',\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout\n  },\n  data() {\n    return {\n      ruleForm: {\n        Name: '',\n        OperateType: '',\n        StartTime: null,\n        EndTime: null,\n        Module: '',\n        Type: '',\n        Date: [],\n        Status: null\n      },\n      customForm: {\n        formItems: [\n          {\n            key: 'Name',\n            label: '告警名称',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            width: '240px',\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'Module',\n            label: '业务模块',\n            type: 'select',\n            otherOptions: {\n              clearable: true\n            },\n            options: [],\n            change: (e) => {\n              this.getTypesByModule(e)\n            }\n          },\n          {\n            key: 'Type',\n            label: '告警类型',\n            type: 'select',\n            otherOptions: {\n              disabled: true,\n              clearable: true\n            },\n            options: [\n              {\n                label: '全部',\n                value: ''\n              }\n            ],\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'Status',\n            label: '状�?,\n            type: 'select',\n            otherOptions: {\n              clearable: true\n            },\n            options: [],\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'Date',\n            label: '告警时间',\n            type: 'datePicker',\n            otherOptions: {\n              type: 'datetimerange',\n              rangeSeparator: '�?,\n              startPlaceholder: '开始日�?,\n              endPlaceholder: '结束日期',\n              clearable: true,\n              valueFormat: 'yyyy-MM-dd HH:mm'\n            },\n            change: (e) => {\n              console.log(e)\n              if (e && e.length !== 0) {\n                this.ruleForm.StartTime = e[0]\n                this.ruleForm.EndTime = e[1]\n              } else {\n                this.ruleForm.StartTime = null\n                this.ruleForm.EndTime = null\n              }\n            }\n          }\n        ],\n        rules: {},\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        buttonConfig: {\n          buttonList: []\n        },\n        // 表格\n        loading: false,\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        height: '100%',\n        tableColumns: [\n          {\n            label: '告警时间',\n            key: 'AlarmTime'\n          },\n          {\n            label: '状�?,\n            key: 'Status',\n            render: (row) => {\n              const h = this.$createElement\n              return h('div', {}, [\n                h(\n                  'span',\n                  { class: row.Status == 1 ? 'warning' : row.Status == 2 ? 'close' : row.Status == 3 ? 'handel' : '' },\n                  ''\n                ),\n                h('span', {}, `${row.Status == 1 ? '告警�? : row.Status == 2 ? '已关�? : row.Status == 3 ? '已处�? : ''}`)\n              ])\n            }\n          },\n          {\n            label: '告警类型',\n            key: 'Type'\n          },\n          {\n            label: '告警名称',\n            key: 'Name'\n          },\n          {\n            label: '来源',\n            key: 'Source'\n          },\n          {\n            label: '业务模块',\n            key: 'Module'\n          },\n          {\n            label: '处理内容',\n            key: 'Content'\n          }\n        ],\n        tableData: [],\n        tableActions: [\n          {\n            actionLabel: '查看详情',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.getDetail(row)\n            }\n          }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.getModule()\n    this.getWarningStatus()\n    this.onFresh()\n  },\n  methods: {\n    // 获取业务模块下拉\n    getModule() {\n      GetModule({}).then((res) => {\n        if (res.IsSucceed) {\n          const data = res.Data || []\n          const arr = []\n          data.forEach((item) => {\n            const obj = {\n              label: item,\n              value: item\n            }\n            arr.push(obj)\n          })\n          arr.unshift({\n            label: '全部',\n            value: ''\n          })\n          console.log(arr)\n          this.customForm.formItems.find(\n            (v) => v.key == 'Module'\n          ).options = arr\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    // 获取告警类型\n    getTypesByModule(value) {\n      this.ruleForm.Type = ''\n      if (value) {\n        GetTypesByModule({ Type: 2, Module: value }).then((res) => {\n          if (res.IsSucceed) {\n            const data = res.Data || []\n            const arr = []\n            data.forEach((item) => {\n              const obj = {\n                label: item,\n                value: item\n              }\n              arr.push(obj)\n            })\n            arr.unshift({\n              label: '全部',\n              value: ''\n            })\n            console.log(arr)\n            this.customForm.formItems.find(\n              (v) => v.key == 'Type'\n            ).otherOptions.disabled = false\n            this.customForm.formItems.find(\n              (v) => v.key == 'Type'\n            ).options = arr\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        })\n      } else {\n        this.customForm.formItems.find(\n          (v) => v.key == 'Type'\n        ).otherOptions.disabled = true\n        this.customForm.formItems.find((v) => v.key == 'Type').options =\n          []\n      }\n    },\n    // 获取任务状�?    getWarningStatus() {\n      GetWarningStatus({}).then((res) => {\n        if (res.IsSucceed) {\n          const data = res.Data || []\n          const arr = []\n          data.forEach((item) => {\n            const obj = {\n              label: item.Name,\n              value: item.Value\n            }\n            arr.push(obj)\n          })\n          arr.unshift({\n            label: '全部',\n            value: null\n          })\n          console.log(arr)\n          this.customForm.formItems.find(\n            (v) => v.key == 'Status'\n          ).options = arr\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    searchForm(data) {\n      console.log(data)\n      this.onFresh()\n    },\n    resetForm() {\n      this.customForm.formItems.find(\n        (v) => v.key == 'Type'\n      ).otherOptions.disabled = true\n      this.customForm.formItems.find(\n        (v) => v.key == 'Type'\n      ).options = [{\n        label: '全部',\n        value: ''\n      }]\n      this.onFresh()\n    },\n    onFresh() {\n      this.fetchData()\n    },\n    async fetchData() {\n      this.customTableConfig.loading = true\n      if (!this.ruleForm.Date || this.ruleForm.Date.length == 0) {\n        this.ruleForm.StartTime = null\n        this.ruleForm.EndTime = null\n      }\n      await GetWarnPageList({\n        ...this.ruleForm,\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.customTableConfig.tableData = res.Data.Data\n          this.customTableConfig.total = res.Data.TotalCount\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      }).finally(() => {\n        this.customTableConfig.loading = false\n      })\n    },\n\n    // 关闭\n    handelClose(row) {\n      this.$confirm('是否关闭?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        CloseWarning({ Id: row.Id }).then((res) => {\n          if (res.IsSucceed) {\n            this.$message({\n              type: 'success',\n              message: '关闭成功!'\n            })\n            this.onFresh()\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删�?\n        })\n      })\n    },\n    // 查看详情\n    getDetail(item) {\n      console.log(item)\n      if (item.Url) {\n        let platform = '' // 子应�?        if (item.ModuleName == '后台设置') {\n          platform = 'management'\n        } else {\n          platform = 'digitalfactory'\n        }\n        this.$qiankun.switchMicroAppFn(\n          platform,\n          item.ModuleCode,\n          item.ModuleId,\n          item.Url\n        )\n      } else {\n        const platform = 'digitalfactory'\n        const code = 'szgc'\n        const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\n        let url = ''\n        if (item.Module == '能耗管�?) {\n          url = '/business/energy/alarmDetail'\n        } else if (item.Module == '车辆道闸') {\n          url = '/bussiness/vehicle/alarm-info'\n        } else if (item.Module == '门禁管理') {\n          url = '/business/AccessControlAlarmDetails'\n        } else if (item.Module == '安防管理') {\n          url = '/business/equipmentAlarm'\n        } else if (item.Module == '危化品管�?) {\n          url = '/business/hazchem/alarmInformation'\n        } else if (item.Module == '环境管理') {\n          url = '/business/environment/alarmInformation'\n        } else if (item.Module == '访客管理') {\n          url = '/business/energy/alarmDetail'\n          console.log('访客管理')\n        }\n        this.$qiankun.switchMicroAppFn(platform, code, id, url)\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.onFresh()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`)\n      this.customTableConfig.currentPage = val\n      this.onFresh()\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n* {\n  box-sizing: border-box;\n}\n\n.layout {\n  height: 100%;\n  width: 100%;\n  position: absolute;\n  ::v-deep {\n    .CustomLayout {\n      .layoutTable {\n        height: 0;\n        .CustomTable {\n          height: 100%;\n          display: flex;\n          flex-direction: column;\n          .table {\n            flex: 1;\n            height: 0;\n            display: flex;\n            flex-direction: column;\n            .el-table {\n              flex: 1;\n              height: 0;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n.warning {\n  display: inline-block;\n  width: 8px;\n  height: 8px;\n  border-radius: 4px;\n  margin-right: 5px;\n  background-color: #fb6b7f;\n}\n.handel {\n  display: inline-block;\n  width: 10px;\n  height: 10px;\n  border-radius: 5px;\n  margin-right: 5px;\n  background-color: #368dff;\n}\n.close {\n  display: inline-block;\n  width: 10px;\n  height: 10px;\n  border-radius: 5px;\n  margin-right: 5px;\n  background-color: #37be6b;\n}\n</style>\n"]}]}