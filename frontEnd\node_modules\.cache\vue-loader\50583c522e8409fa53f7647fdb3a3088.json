{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\systemSettings\\messageCenter\\index.vue?vue&type=style&index=0&id=b78503b2&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\systemSettings\\messageCenter\\index.vue", "mtime": 1755506574440}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoubXQyMCB7CiAgbWFyZ2luLXRvcDogMTBweDsKfQoubGF5b3V0ewogIGhlaWdodDogY2FsYygxMDB2aCAtIDkwcHgpOwogIG92ZXJmbG93OiBhdXRvOwp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsaA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/systemSettings/messageCenter", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        />\n      </template>\n    </CustomLayout>\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <component\n        :is=\"currentComponent\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n      /></el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\n// import getGridByCode from \"../../safetyManagement/mixins/index\";\n// import DialogForm from \"./dialogForm.vue\";\n\nimport { downloadFile } from '@/utils/downloadFile'\nimport dayjs from 'dayjs'\nimport {\n  GetWBMessageList,\n  GetMessageType,\n  GetPublishUnitList\n} from '@/api/business/eventManagement'\nexport default {\n  name: '',\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout\n  },\n  data() {\n    return {\n      currentComponent: null,\n      componentsConfig: {\n        Data: {}\n      },\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true\n        },\n        close: () => {\n          this.dialogVisible = false\n          this.onFresh()\n        }\n      },\n      dialogVisible: false,\n      dialogTitle: '编辑',\n      tableSelection: [],\n      ruleForm: {\n        Title: '',\n        MessageType: '',\n        Source: '',\n        CreateUser: '',\n        ReceiveUserName: '',\n        StartTime: null,\n        EndTime: null,\n        Date: []\n      },\n      customForm: {\n        formItems: [\n          {\n            key: 'Title',\n            label: '消息标题',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          // {\n          //   key: \"MessageType\",\n          //   label: \"事件类型\",\n          //   type: \"select\",\n          //   options: [],\n          //   otherOptions: {\n          //     clearable: true,\n          //   },\n          //   change: (e) => {\n          //     // change事件\n          //     console.log(e);\n          //     // this.GetTypesByModule();\n          //   },\n          // },\n          {\n            key: 'MessageType',\n            label: '消息类型',\n            type: 'select',\n            options: [],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'Source',\n            label: '来源',\n            type: 'select',\n            options: [],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'CreateUser',\n            label: '创建�?,\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'ReceiveUserName',\n            label: '接收�?,\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'Date', // 字段ID\n            label: '发送时�?, // Form的label\n            type: 'datePicker', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              type: 'daterange',\n              disabled: false,\n              placeholder: '请输�?..'\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n              if (e && e.length > 0) {\n                this.ruleForm.StartTime = dayjs(e[0]).format('YYYY-MM-DD')\n                this.ruleForm.EndTime = dayjs(e[1]).format('YYYY-MM-DD')\n              }\n            }\n          }\n        ],\n        rules: {},\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        loading: false,\n        buttonConfig: {\n          buttonList: [\n            // {\n            //   text: \"批量关闭\",\n            //   onclick: (item) => {\n            //     console.log(item);\n            //     this.handleClose();\n            //   },\n            // },\n          ]\n        },\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        tableColumns: [\n          {\n            otherOptions: {\n              type: 'selection',\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '消息标题',\n            key: 'Title',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '事件类型',\n            key: 'EventTypeName',\n            width: 140,\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '来源',\n            key: 'SourceName',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '业务模块',\n            key: 'Module',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '接收�?,\n            key: 'ReceiveUser',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '创建�?,\n            key: 'SendUserName',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '发送时�?,\n            key: 'SendTime',\n            otherOptions: {\n              align: 'center'\n            }\n          }\n        ],\n        tableData: [],\n        operateOptions: {\n          align: 'center',\n          width: '180'\n        },\n        tableActions: [\n          {\n            actionLabel: '查看详情',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              // this.handleEdit(row);\n              const platform = 'digitalfactory'\n              const code = 'szgc'\n              const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\n              let url = ''\n              if (row.Module == '能耗管�?) {\n                url = '/business/energy/alarmDetail'\n              } else if (row.Module == '车辆道闸') {\n                url = '/bussiness/vehicle/alarm-info'\n              } else if (row.Module == '门禁管理') {\n                url = '/business/AccessControlAlarmDetails'\n              } else if (row.Module == '安防管理') {\n                url = '/business/equipmentAlarm'\n              } else if (row.Module == '危化品管�?) {\n                url = '/business/hazchem/alarmInformation'\n              } else if (row.Module == '环境管理') {\n                url = '/business/environment/alarmInformation'\n              } else if (row.Module == '访客管理') {\n                url = '/business/energy/alarmDetail'\n                console.log('访客管理')\n              }\n              this.$qiankun.switchMicroAppFn(platform, code, id, url)\n            }\n          }\n          // {\n          //   actionLabel: \"编辑\",\n          //   otherOptions: {\n          //     type: \"text\",\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handleEdit(row);\n          //   },\n          // },\n        ]\n      }\n    }\n  },\n  computed: {},\n  created() {\n    this.init()\n\n    this.GetMessageType()\n    this.getPublishUnitList()\n  },\n  methods: {\n    async handleClose() {\n      const res = await SetWarningStatus({\n        Status: '2',\n        Ids: this.tableSelection.map((item) => item.Id)\n      })\n      if (res.IsSucceed) {\n        this.$message.success('操作成功')\n        this.onFresh()\n      }\n    },\n    async getPublishUnitList() {\n      const res = await GetPublishUnitList({})\n      if (res.IsSucceed) {\n        const result = res.Data || []\n        this.customForm.formItems.find(\n          (item) => item.key == 'Source'\n        ).options = result.map((item) => ({\n          value: item.Id,\n          label: item.Name\n        }))\n      }\n    },\n    async GetMessageType() {\n      const res = await GetMessageType({})\n      console.log(res, 'res')\n      if (res.IsSucceed) {\n        const result = res.Data || []\n        const typeList = result.map((item) => ({\n          value: item.Value,\n          label: item.Name\n        }))\n        console.log(typeList, 'typeList')\n        this.customForm.formItems.find((item) => item.key == 'MessageType').options =\n        typeList\n      }\n    },\n\n    searchForm(data) {\n      console.log(data)\n      this.onFresh()\n    },\n    resetForm() {\n      this.ruleForm.StartTime = null\n      this.ruleForm.EndTime = null\n      this.ruleForm.Date = null\n      this.onFresh()\n    },\n    onFresh() {\n      this.GetWBMessageList()\n    },\n\n    init() {\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\n      this.GetWBMessageList()\n    },\n    async GetWBMessageList() {\n      this.customTableConfig.loading = true\n      const res = await GetWBMessageList({\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n        ...this.ruleForm\n      }).finally(() => {\n        this.customTableConfig.loading = false\n      })\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data\n        this.customTableConfig.total = res.Data.TotalCount\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n    async handleExport() {\n      const res = await ExportEntranceWarning({\n        id: this.tableSelection.map((item) => item.Id).toString(),\n        code: 'AccessControlAlarmDetails1'\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        downloadFile(res.Data, '告警明细数据')\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.GetWBMessageList()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`)\n      this.customTableConfig.currentPage = val\n      this.GetWBMessageList()\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection\n    },\n    handleEdit(row) {\n      this.dialogVisible = true\n      this.componentsConfig.Data = row\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.mt20 {\n  margin-top: 10px;\n}\n.layout{\n  height: calc(100vh - 90px);\n  overflow: auto;\n}\n</style>\n"]}]}