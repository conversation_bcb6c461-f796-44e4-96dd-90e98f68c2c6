{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJsmartBroadcasting\\broadcastMediaFiles\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJsmartBroadcasting\\broadcastMediaFiles\\index.vue", "mtime": 1755506574490}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/SZCJsmartBroadcasting/broadcastMediaFiles", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <div class=\"tableNotice\">\r\n          <span></span>\r\n          <span>数据更新时间�?{{ updateDate }}</span>\r\n          <!-- <span>数据更新时间�?{{ customTableConfig.tableData[0].UpdateDate }}</span> -->\r\n        </div>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\n\r\nimport dayjs from \"dayjs\";\r\nimport {\r\n  GetMediaFileListSZCJ,\r\n  PostMediaFileDataList,\r\n} from \"@/api/business/smartBroadcasting\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      updateDate: \"\",\r\n      currentComponent: null,\r\n      componentsConfig: {\r\n        Data: {},\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.initData();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"编辑\",\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        FileName: \"\",\r\n        Date: [],\r\n        BeginCreateDate: null,\r\n        EndCreateDate: null,\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"FileName\",\r\n            label: \"文件名称\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Date\", // 字段ID\r\n            label: \"创建时间\", // Form的label\r\n            type: \"datePicker\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              type: \"daterange\",\r\n              disabled: false,\r\n              placeholder: \"请输�?..\",\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.BeginCreateDate = dayjs(e[0]).format(\r\n                  \"YYYY-MM-DD\"\r\n                );\r\n                this.ruleForm.EndCreateDate = dayjs(e[1]).format(\"YYYY-MM-DD\");\r\n              }\r\n            },\r\n          },\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"更新数据\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleResetData();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: \"文件名称\",\r\n            key: \"FileName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"播放时长\",\r\n            key: \"PlaybackTime\",\r\n            width: 140,\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"ID\",\r\n            key: \"MediaFileId\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"创建时间\",\r\n            key: \"CreateDate\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"创建来源\",\r\n            key: \"MediaSource\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: \"center\",\r\n          width: \"180\",\r\n        },\r\n        tableActions: [],\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    async handleResetData() {\r\n      const res = await PostMediaFileDataList({});\r\n      console.log(res, \"res\");\r\n      if (res.IsSucceed) {\r\n        this.initData();\r\n        this.$message({\r\n          type: \"success\",\r\n          message: \"更新成功\",\r\n        });\r\n      }\r\n    },\r\n\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1;\r\n      this.initData();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.BeginCreateDate = null;\r\n      this.ruleForm.EndCreateDate = null;\r\n      this.ruleForm.Date = null;\r\n      this.initData();\r\n    },\r\n\r\n    async initData() {\r\n      const res = await GetMediaFileListSZCJ({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n        if (res.Data.Data.length > 0) {\r\n          this.updateDate = res.Data.Data[0].UpdateDate || \"\";\r\n        }\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.initData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.initData();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleEdit(row) {\r\n      this.dialogVisible = true;\r\n      this.componentsConfig.Data = row;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.tableNotice {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 10px 15px;\r\n  color: rgba(34, 40, 52, 0.65);\r\n  font-size: 14px;\r\n}\r\n</style>\r\n"]}]}