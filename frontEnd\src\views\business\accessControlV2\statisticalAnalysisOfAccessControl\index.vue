<template>
  <div class="app-container abs100 analysisBox">
    <top :params-obj="crjr" />
    <middle :params-arr="paramsArr" />
    <bottom :params-obj="rllqs" />
  </div>
</template>

<script>
import Top from './components/top.vue'
import Middle from './components/middle.vue'
import Bottom from './components/bottom.vue'
import { GetJumpUrl } from '@/api/business/accessControlV2.js'
export default {
  components: {
    Top,
    Middle,
    Bottom
  },
  data() {
    return {
      crjr: {},
      paramsArr: [],
      rllqs: {}
    }
  },
  created() {
    this.getJumpUrl()
  },
  mounted() {

  },
  methods: {
    async getJumpUrl() {
      const res = await GetJumpUrl()
      if (res.IsSucceed) {
        res.Data.map(i => {
          if (i.ViewMore == '最新出入记录') {
            this.crjr = i
          } else if (i.ViewMore == '园区人流量趋势') {
            this.rllqs = i
          } else {
            this.paramsArr.push(i)
          }
        })
      }
    }
  }
}
</script>
<style scoped lang='scss'>
.analysisBox {
  // padding: 10px 15px;
  // height: calc(100vh - 90px);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}
</style>
