<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      :title="dialogTitle"
      width="30%"
      :visible.sync="dialogVisible"
      destroy-on-close
    >
      <el-form
        :model="addForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="权限组名称" prop="Name">
          <el-input v-model="addForm.Name"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="resetAddForm">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";
import getGridByCode from "../../safetyManagement/mixins/index";
import { GetList, DelAuthGroup, SaveAuthGroup, ExportData } from "@/api/business/purviewConfig";
import addRouterPage from "@/mixins/add-router-page";
import { downloadFile } from '@/utils/downloadFile'

export default {
  name: "",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
  },
  data() {
    return {
      dialogVisible: false,
      dialogTitle: "新增权限组",
      tableSelection: "",
      ruleForm: {
        Name: "",
      },
      addForm: {
        Name: "",
      },
      rules: {
        Name: [{ required: true, message: '请输入权限组名称', trigger: 'blur' }]
      },
      customForm: {
        formItems: [
          {
            key: "Name",
            label: "门禁组名称",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            change: (e) => {
              console.log(e);
            },
          }
        ],
        rules: {},
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "新增",
              type: "primary",
              size: "small",
              onclick: (item) => {
                console.log(item);
                this.handleCreate();
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            width: 50,
            otherOptions: {
              type: 'selection',
              align: 'center'
            }
          },
          {
            label: '门禁组',
            key: 'P_Name',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '设备数量',
            width: '50px',
            key: 'num',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '可通行人员',
            width: '50px',
            key: 'P_Name',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '同步状态',
            key: 'SyncRemark',
            otherOptions: {
              align: 'center'
            }
          },
        ],
        tableData: [],
        operateOptions: {
          width: "240px",
          align: "center",
        },
        tableActionsWidth: 160,
        tableActions: [
          {
            actionLabel: "查看",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleWatch(row);
            },
          },
          {
            actionLabel: "编辑",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleEdit(row);
            },
          },
          {
            actionLabel: "删除",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleDele(row.Id)
            },
          },
          {
            actionLabel: "导出",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              console.log(index, row);
              this.handleExport(row.Id);
            },
          },
        ],
      },
      addPageArray: [
        {
          path: this.$route.path + "/watchDevice",
          hidden: true,
          component: () => import("./watchDevice.vue"),
          meta: { title: `门禁权限配置详情` },
          name: "purviewConfigWatchDevice",
        },
        {
          path: this.$route.path + "/editDevice",
          hidden: true,
          component: () => import("./editDevice.vue"),
          meta: { title: `门禁权限配置编辑` },
          name: "purviewConfigEditDevice",
        },
      ],
    };
  },
  async created() {
    this.init();
  },
  watch: {
    'customTableConfig.tableData': {
      handler(newValue, oldValue) {
        this.customTableConfig.tableColumns = [
          {
            width: 50,
            otherOptions: {
              type: 'selection',
              align: 'center'
            }
          },
          {
            label: '门禁组',
            key: 'Name',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '设备数量',

            key: 'EquipCount',
            otherOptions: {
              align: 'center',
              width:'250px',
            }
          },
          {
            label: '可通行人员',

            key: 'UserCount',
            otherOptions: {
              align: 'center',
              width:'250px',
            }
          },
          {
            label: '同步状态',
            key: 'SyncRemark',
            otherOptions: {
              align: 'center'
            }
          },
        ]
      },
      deep: true
    }
  },
  mixins: [getGridByCode, addRouterPage],
  methods: {
    searchForm(data) {
      this.customTableConfig.currentPage = 1
      this.fetchData();
    },
    resetForm() {
      this.fetchData();
    },
    init() {
      this.fetchData();
    },
    async fetchData() {
      const res = await GetList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data;
        this.customTableConfig.total = res.Data.Total;
      } else {
        this.$message.error(res.Message);
      }
    },
    async handleCreate() {
      this.ruleForm.Name = ''
      this.dialogVisible = true
    },
    submitForm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          SaveAuthGroup(this.addForm).then(res => {
            if (res.IsSucceed) {
              this.$message.success('新增成功')
              this.fetchData()
              this.resetAddForm()
            } else {
              this.$message.error(res.Message)
            }
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetAddForm() {
      this.$refs.ruleForm.resetFields();
      this.dialogVisible = false
    },
    async handleWatch(row) {
      this.$router.push({
        name: "purviewConfigWatchDevice",
        query: { pg_redirect: this.$route.name, Id: row.Id },
      });
    },
    async handleEdit(row) {
      this.$router.push({
        name: "purviewConfigEditDevice",
        query: { pg_redirect: this.$route.name, Id: row.Id },
      });
    },
    async handleExport(Id) {
      const res = await ExportData({ Id });
      const url = window.URL.createObjectURL(
        new Blob([res], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        })
      );
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", "门禁权限配置.xlsx");
      document.body.appendChild(link);
      link.click();
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.fetchData();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
    handleDele(Id) {
      this.$confirm('是否确定删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DelAuthGroup({ Id }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.fetchData()
          } else {
            this.$message.error(res.Message)
          }
        })

      }).catch(() => {
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
.layout{
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
