{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\layout\\components\\AppMain.vue?vue&type=style&index=1&id=078753dd&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\layout\\components\\AppMain.vue", "mtime": 1755502545452}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8vIGZpeCBjc3Mgc3R5bGUgYnVnIGluIG9wZW4gZWwtZGlhbG9nDQouZWwtcG9wdXAtcGFyZW50LS1oaWRkZW4gew0KICAuZml4ZWQtaGVhZGVyIHsNCiAgICBwYWRkaW5nLXJpZ2h0OiAxNXB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["AppMain.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA;AACA;AACA;AACA;AACA;AACA", "file": "AppMain.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\r\n  <section class=\"app-main\">\r\n    <!-- <transition name=\"fade-transform\" mode=\"out-in\">\r\n      <keep-alive :max=\"10\">\r\n        <router-view :key=\"key\" v-if=\"cachedViews.includes(name)\" />\r\n      </keep-alive>\r\n    </transition>\r\n    <transition name=\"fade-transform\" mode=\"out-in\">\r\n      <router-view :key=\"key\"  v-if=\"!cachedViews.includes(name)\" />\r\n    </transition> -->\r\n    <transition name=\"fade-transform\" mode=\"out-in\">\r\n      <keep-alive :include=\"cachedViews\">\r\n        <router-view :key=\"key\" />\r\n      </keep-alive>\r\n    </transition>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'AppMain',\r\n  components: {},\r\n  computed: {\r\n    cachedViews() {\r\n      return this.$store.state.tagsView.cachedViews\r\n    },\r\n    key() {\r\n      return this.$route.path\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-main {\r\n  /* 50= navbar  50  */\r\n  min-height: calc(100vh - 50px - 42px);\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: hidden;\r\n  background: #f4f5f7;\r\n}\r\n\r\n.fixed-header + .app-main {\r\n  padding-top: 50px;\r\n}\r\n\r\n.hasTagsView {\r\n  .app-main {\r\n    /* 84 = navbar + tags-view = 50 + 34 */\r\n    min-height: calc(100vh - 90px);\r\n  }\r\n\r\n  .fixed-header + .app-main {\r\n    padding-top: 84px;\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n// fix css style bug in open el-dialog\r\n.el-popup-parent--hidden {\r\n  .fixed-header {\r\n    padding-right: 15px;\r\n  }\r\n}\r\n</style>\r\n"]}]}