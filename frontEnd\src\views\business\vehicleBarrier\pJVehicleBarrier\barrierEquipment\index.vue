<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          label-width="130px"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @closed="closedDialog"
    >
      <component
        :is="currentComponent"
        ref="dialogRef"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";

import baseInfo from "./dialog/baseInfo.vue";
import importDialog from "@/views/business/vehicleBarrier/components/import.vue";
import exportInfo from "@/views/business/vehicleBarrier/pJVehicleBarrier/mixins/export";

import { downloadFile } from "@/utils/downloadFile";
import {
  GetVBEquipList,
  DelEquip,
  ExportVBData,
  GetDropList,
  DownloadVBEquipTemplate,
  ImportVBEquipDataStream,
} from "@/api/business/vehicleBarrier.js";
import getAddress from "./index.js";
import addRouterPage from "@/mixins/add-router-page";
export default {
  Name: "",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
    baseInfo,
    importDialog,
  },
  mixins: [exportInfo, getAddress, addRouterPage],
  data() {
    return {
      currentComponent: baseInfo,
      componentsConfig: {
        interfaceName: ImportVBEquipDataStream,
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogVisible: false,
      dialogTitle: "",
      tableSelection: [],
      selectIds: [],
      ruleForm: {
        Name: "",
        Brand: "",
        Model: "",
        Vender: "",
        Status: "",
        PurposeCatetory: "",
        Scene: "",
        Site: "",
      },
      customForm: {
        formItems: [
          {
            key: "Name", // 字段ID
            label: "设备名称", // Form的label
            type: "input", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            placeholder: "请输入输入停车场名称",
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
            },
          },
          {
            key: "Brand",
            label: "品牌",
            type: "select",
            options: [],
            placeholder: "请输入停车场地址",
            otherOptions: {
              clearable: true,
            },
            change: (e) => {},
          },
          {
            key: "Model",
            label: "规格型号",
            type: "select",
            options: [],
            otherOptions: {
              clearable: true,
            },
            change: (e) => {},
          },
          {
            key: "position",
            label: "安装位置",
            type: "cascader",
            options: [],
            otherOptions: {
              clearable: true,
              require: true,
              disabled: false,
              separator: "-",
              props: {
                label: "Label",
                children: "Children",
                value: "Label",
              },
            },
            change: (e) => {
              if (e.length > 0) {
                this.ruleForm.PurposeCatetory = e[0];
                this.ruleForm.Scene = e[1];
                this.ruleForm.Site = e[2];
              } else {
                this.ruleForm.PurposeCatetory = "";
                this.ruleForm.Scene = "";
                this.ruleForm.Site = "";
              }
            },
          },
          {
            key: "Vender",
            label: "供应商",
            type: "select",
            options: [],
            otherOptions: {
              clearable: true,
            },
            change: (e) => {},
          },
          {
            key: "Status",
            label: "状态",
            type: "select",
            options: [],
            otherOptions: {
              clearable: true,
            },
            change: (e) => {},
          },
        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "新增",
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: false, // 是否禁用
              icon: "", //  图标
              autofocus: false, // 是否聚焦
              type: "primary", // primary / success / warning / danger / info / text
              size: "small", // medium / small / mini
              onclick: (item) => {
                console.log(item);
                this.handleCreate();
              },
            },
            {
              text: "下载模板",
              disabled: false, // 是否禁用
              onclick: (item) => {
                console.log(item);
                this.ExportData(
                  [],
                  "道闸设备管理模板",
                  DownloadVBEquipTemplate
                );
              },
            },
            {
              text: "批量导入",
              disabled: false, // 是否禁用
              onclick: (item) => {
                console.log(item);
                this.currentComponent = "importDialog";
                this.dialogVisible = true;
                this.dialogTitle = "批量导入";
              },
            },
            {
              key: "batch",
              disabled: false, // 是否禁用
              text: "批量导出",
              onclick: (item) => {
                console.log(item);
                this.ExportData(this.ruleForm, "道闸设备管理", ExportVBData);
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        height: "100%",
        tableActionsWidth: 220,
        tableColumns: [
          // {
          //   width: 50,
          //   otherOptions: {
          //     type: 'selection',
          //     align: 'center'
          //   }
          // },
          {
            label: "设备名称",
            key: "Name",
            otherOptions: {
              fixed: 'left'
            },
          },
          {
            label: "设备编码",
            key: "Code",
          },

          {
            label: "品牌",
            key: "Brand",
          },
          {
            label: "规格型号",
            key: "Model",
          },

          {
            label: "所属出入口",
            key: "EntranceName",
          },
          {
            label: "安装位置",
            key: "Address",
          },
          {
            label: "供应商",
            key: "Vender",
          },
          {
            label: "供应商联系方式",
            key: "VenderPhone",
          },
          {
            label: "状态",
            key: "StatusName",
          },
          {
            label: "更新人",
            key: "ModifyUserName",
          },
          {
            label: "更新时间",
            key: "ModifyDate",
          },
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: "编辑",
            otherOptions: {
              type: "text",
              disabled: false, // 是否禁用
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, "edit");
            },
          },
          {
            actionLabel: "删除",
            otherOptions: {
              type: "text",
              disabled: false, // 是否禁用
            },
            onclick: (index, row) => {
              this.handleDelete(index, row);
            },
          },
          {
            actionLabel: "查看详情",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.$router.push({
                name: "EquipmentView",
                query: { pg_redirect: this.$route.name, Id: row.Id },
              });
            },
          },
        ],
        operateOptions: {
          // width: 300 // 操作栏宽度
        },
      },
      allSelectOption: [],
      addPageArray: [
        {
          path: this.$route.path + "/view",
          hidden: true,
          component: () => import("./dialog/view.vue"),
          meta: { title: "道闸设备详情" },
          name: "EquipmentView",
        },
      ],
    };
  },
  computed: {},
  async created() {
    this.customForm.formItems.find((item) => item.key == "position").options =
      await this.getAddress();
    await this.getDropList();

    this.init();
  },
  methods: {
    searchForm(data) {
      this.customTableConfig.currentPage = 1;
      console.log(data);
      this.onFresh();
    },
    resetForm() {
      this.ruleForm.position = [];
      this.ruleForm.PurposeCatetory = "";
      this.ruleForm.Scene = "";
      this.ruleForm.Site = "";
      this.onFresh();
      this.getDropList();
    },
    onFresh() {
      this.fetchData();
      this.getDropList();
    },
    init() {
      this.fetchData();
      this.getDropList();
    },
    // 获取搜索下啦
    async getDropList() {
      await GetDropList({}).then((res) => {
        if (res.IsSucceed) {
          const data = res.Data;
          this.allSelectOption = data;
        } else {
          this.$message({
            type: "error",
            message: res.Message,
          });
        }
      });

      this.customForm.formItems.find((item) => item.key === "Brand").options =
        await this.handelOption("Brand");
      this.customForm.formItems.find((item) => item.key === "Model").options =
        await this.handelOption("Model");
      this.customForm.formItems.find((item) => item.key === "Vender").options =
        await this.handelOption("Vender");
      this.customForm.formItems.find((item) => item.key === "Status").options =
        await this.handelOption("Status");
    },

    async handelOption(key) {
      return await this.allSelectOption
        .find((item) => item.Name === key)
        .List.map((i) => {
          return {
            label: i.Value,
            value: i.Key,
          };
        });
    },
    async fetchData() {
      const res = await GetVBEquipList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data || [];
        this.customTableConfig.total = res.Data.Total;
      }
    },
    handleCreate() {
      this.currentComponent = "baseInfo";
      this.dialogTitle = "新增";
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.dialogRef.add();
      });
    },
    handleDelete(index, row) {
      console.log(index, row);
      console.log(this);
      this.$confirm("确认删除?", {
        type: "warning",
      })
        .then(async (_) => {
          const res = await DelEquip({
            Id: row.Id,
          });
          if (res.IsSucceed) {
            this.$message({
              message: "删除成功",
              type: "success",
            });
            this.init();
          } else {
            this.$message({
              message: res.Message,
              type: "error",
            });
          }
        })
        .catch((_) => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleEdit(index, row, type) {
      console.log(index, row, type);
      this.currentComponent = "baseInfo";
      if (type === "view") {
        this.dialogTitle = "查看";
      } else if (type === "edit") {
        this.dialogTitle = "编辑";
      }
      this.$nextTick(() => {
        this.$refs.dialogRef.init(index, row, type);
      });

      this.dialogVisible = true;
    },
    // 关闭弹窗
    closedDialog() {
      this.$refs.dialogRef.closeClearForm();
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.onFresh();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.onFresh();
    },
    handleSelectionChange(selection) {
      const Ids = [];
      this.tableSelection = selection;
      this.tableSelection.forEach((item) => {
        Ids.push(item.Id);
      });
      console.log(Ids);
      this.selectIds = Ids;
      console.log(this.tableSelection);
      if (this.tableSelection.length > 0) {
        this.customTableConfig.buttonConfig.buttonList.find(
          (v) => v.key == "batch"
        ).disabled = false;
      } else {
        this.customTableConfig.buttonConfig.buttonList.find(
          (v) => v.key == "batch"
        ).disabled = true;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/views/business/vehicleBarrier/index.scss";
.mt20 {
  margin-top: 10px;
}
::v-deep {
  .el-dialog__body {
    padding: 0px 20px 30px;
  }
}
</style>
