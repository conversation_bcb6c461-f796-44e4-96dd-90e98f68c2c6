{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\maintenanceAndUpkeep\\workOrderManagement\\components\\device.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\maintenanceAndUpkeep\\workOrderManagement\\components\\device.vue", "mtime": 1755674552428}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQ3VzdG9tTGF5b3V0IGZyb20gJ0AvYnVzaW5lc3NDb21wb25lbnRzL0N1c3RvbUxheW91dC9pbmRleC52dWUnDQppbXBvcnQgQ3VzdG9tVGFibGUgZnJvbSAnQC9idXNpbmVzc0NvbXBvbmVudHMvQ3VzdG9tVGFibGUvaW5kZXgudnVlJw0KLy8gaW1wb3J0IEF1dGhCdXR0b25zIGZyb20gIkAvbWl4aW5zL2F1dGgtYnV0dG9ucyI7DQppbXBvcnQgZWRpdERpYWxvZyBmcm9tICcuLi9lZGl0RGlhbG9nLnZ1ZScNCmltcG9ydCBjbG9zZVJhdGVEaWFsb2cgZnJvbSAnLi4vY2xvc2VSYXRlRGlhbG9nLnZ1ZScNCmltcG9ydCB7DQogIEdldFdvcmtPcmRlck1hbmFnZUxpc3QsDQogIEdldFdvcmtPcmRlclR5cGUsDQogIEdldFBlcnNvbkxpc3QsDQogIERlbGV0ZUNvYXRpbmdSZXF1aXIsDQogIFNlbmRXb3JrT3JkZXJQZXJzb24sDQogIEdldEVxdWlwRHJvcExpc3QNCn0gZnJvbSAnQC9hcGkvYnVzaW5lc3MvbWFpbnRlbmFuY2VBbmRVcGtlZXAuanMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgTmFtZTogJycsDQogIGNvbXBvbmVudHM6IHsNCiAgICBDdXN0b21UYWJsZSwNCiAgICBDdXN0b21MYXlvdXQsDQogICAgZWRpdERpYWxvZywNCiAgICBjbG9zZVJhdGVEaWFsb2cNCiAgfSwNCiAgcHJvcHM6IHsNCiAgICBmbGFnOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIHBlcnNvbkxpc3Q6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgZGVmYXVsdDogKCkgPT4gW10NCiAgICB9LA0KICAgIGVxdWlwT3B0aW9uczogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgYXV0aEJ1dHRvbnM6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IHt9DQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB1c2VySWQ6ICcnLA0KICAgICAgcXVlcnk6IHsNCiAgICAgICAgRGF0ZTogW10sDQogICAgICAgIE9yZGVyX0NvZGU6ICcnLA0KICAgICAgICBPcmRlcl9OYW1lOiAnJywNCiAgICAgICAgQ3JlYXRlX0RhdGU6ICcnLA0KICAgICAgICBDcmVhdGVfRURhdGU6ICcnLA0KICAgICAgICBTdGF0ZTogJycsDQogICAgICAgIFdvcmtPcmRlcl9TZXR1cF9JZDogJ3Nid2InLA0KICAgICAgICBNYWludGFpbl9QZXJzb246ICcnLA0KICAgICAgICBXb3JrT3JkZXJfU3RhdGU6IG51bGwsDQogICAgICAgIFR5cGU6IDENCiAgICAgIH0sDQogICAgICB0eXBlOiAnJywNCiAgICAgIHBpY2tlck9wdGlvbnM6IHsNCiAgICAgICAgc2hvcnRjdXRzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdGV4dDogJ+S7iuWkqScsDQogICAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbbmV3IERhdGUoKSwgbmV3IERhdGUoKV0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0ZXh0OiAn6L+RN+WkqScsDQogICAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpDQogICAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoKQ0KICAgICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiA3KQ0KICAgICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0ZXh0OiAn6L+RMzDlpKknLA0KICAgICAgICAgICAgb25DbGljayhwaWNrZXIpIHsNCiAgICAgICAgICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoKQ0KICAgICAgICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCkNCiAgICAgICAgICAgICAgc3RhcnQuc2V0VGltZShzdGFydC5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0ICogMzApDQogICAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIFtzdGFydCwgZW5kXSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRleHQ6ICfmnKzmnIgnLA0KICAgICAgICAgICAgb25DbGljayhwaWNrZXIpIHsNCiAgICAgICAgICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpDQogICAgICAgICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKA0KICAgICAgICAgICAgICAgIHRvZGF5LmdldEZ1bGxZZWFyKCksDQogICAgICAgICAgICAgICAgdG9kYXkuZ2V0TW9udGgoKSArIDEsDQogICAgICAgICAgICAgICAgMA0KICAgICAgICAgICAgICApDQogICAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUodG9kYXkuZ2V0RnVsbFllYXIoKSwgdG9kYXkuZ2V0TW9udGgoKSwgMSkNCiAgICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3N0YXJ0LCBlbmRdKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIHN0YXRlTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+W+heWkhOeQhicsDQogICAgICAgICAgY29kZTogMA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+WkhOeQhuS4rScsDQogICAgICAgICAgY29kZTogMQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+W+heWkjeajgCcsDQogICAgICAgICAgY29kZTogMg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+W+heivhOS7tycsDQogICAgICAgICAgY29kZTogMw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+WkhOeQhuWujOaIkCcsDQogICAgICAgICAgY29kZTogNA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+W3suWFs+mXrScsDQogICAgICAgICAgY29kZTogNQ0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgd29ya1R5cGVMaXN0OiBbDQogICAgICAgIC8vIHsNCiAgICAgICAgLy8gICBEaXNwbGF5X05hbWU6ICLlt7LlhbPpl60iLA0KICAgICAgICAvLyAgIFZhbHVlOiA1LA0KICAgICAgICAvLyB9LA0KICAgICAgXSwNCiAgICAgIGNvbXBvbmVudHNGdW5zOiB7DQogICAgICAgIG9wZW46ICgpID0+IHsNCiAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICAgIH0sDQogICAgICAgIGNsb3NlOiAoKSA9PiB7DQogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgICB0aGlzLm9uRnJlc2goKQ0KICAgICAgICB9LA0KICAgICAgICBjbG9zZUFuZEZyZXNoOiAoKSA9PiB7DQogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgICB0aGlzLm9uRnJlc2goKQ0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBkaWFsb2dUaXRsZTogJycsDQogICAgICB0YWJsZVNlbGVjdGlvbjogW10sDQogICAgICBzZWxlY3RJZHM6IFtdLA0KICAgICAgY3VzdG9tVGFibGVDb25maWc6IHsNCiAgICAgICAgLy8g6KGo5qC8DQogICAgICAgIHBhZ2VTaXplT3B0aW9uczogWzEwLCAyMCwgNTAsIDgwXSwNCiAgICAgICAgY3VycmVudFBhZ2U6IDEsDQogICAgICAgIHBhZ2VTaXplOiAyMCwNCiAgICAgICAgdG90YWw6IDAsDQogICAgICAgIGhlaWdodDogJzEwMCUnLA0KICAgICAgICB0YWJsZUNvbHVtbnM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICB3aWR0aDogNTAsDQogICAgICAgICAgICBsYWJlbDogJ+W6j+WPtycsDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgdHlwZTogJ2luZGV4JywNCiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+WPkei1t+aXtumXtCcsDQogICAgICAgICAgICBrZXk6ICdDcmVhdGVfRGF0ZScNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAn5bel5Y2V5ZCN56ewJywNCiAgICAgICAgICAgIGtleTogJ09yZGVyX05hbWUnDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+W3peWNleexu+WeiycsDQogICAgICAgICAgICBrZXk6ICdPcmRlcl9UeXBlJywNCiAgICAgICAgICAgIHJlbmRlcjogKHJvdykgPT4gew0KICAgICAgICAgICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgNCiAgICAgICAgICAgICAgICAnc3BhbicsDQogICAgICAgICAgICAgICAge30sDQogICAgICAgICAgICAgICAgcm93Lk9yZGVyX1R5cGUgPT09IG51bGwNCiAgICAgICAgICAgICAgICAgID8gJy0nDQogICAgICAgICAgICAgICAgICA6IHJvdy5PcmRlcl9UeXBlID09PSAnanNieCcNCiAgICAgICAgICAgICAgICAgICAgPyAn5Y2z5pe25oql5L+uJw0KICAgICAgICAgICAgICAgICAgICA6ICforr7lpIfnu7Tkv50nDQogICAgICAgICAgICAgICkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAn5bel5Y2V5Y+3JywNCiAgICAgICAgICAgIGtleTogJ09yZGVyX0NvZGUnDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+W8gOWni+WkhOeQhuaXtumXtCcsDQogICAgICAgICAgICBrZXk6ICdTdGFydF9UaW1lJw0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICflpITnkIblrozmiJDml7bpl7QnLA0KICAgICAgICAgICAga2V5OiAnRW5kX1RpbWUnDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+WkhOeQhueUqOaXticsDQogICAgICAgICAgICBrZXk6ICdUaW1lJw0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICfmiqXkv67pg6jpl6gnLA0KICAgICAgICAgICAga2V5OiAnRGVwYXJ0X05hbWUnDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+aKpeS/ruaWuScsDQogICAgICAgICAgICBrZXk6ICdXYXJyYW50eV9QZXJzb24nDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+e7tOS/ruS6uicsDQogICAgICAgICAgICBrZXk6ICdNYWludGFpbl9QZXJzb24nDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+W3peWNleeKtuaAgScsDQogICAgICAgICAgICBrZXk6ICdTdGF0ZScsDQogICAgICAgICAgICByZW5kZXI6IChyb3cpID0+IHsNCiAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuJGNyZWF0ZUVsZW1lbnQoDQogICAgICAgICAgICAgICAgJ3NwYW4nLA0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgIHN0eWxlOiB7DQogICAgICAgICAgICAgICAgICAgIGNvbG9yOg0KICAgICAgICAgICAgICAgICAgICAgIHJvdy5TdGF0ZSA9PT0gJzAnDQogICAgICAgICAgICAgICAgICAgICAgICA/ICcjRkY1RTdDJw0KICAgICAgICAgICAgICAgICAgICAgICAgOiByb3cuU3RhdGUgPT09ICcxJw0KICAgICAgICAgICAgICAgICAgICAgICAgICA/ICcjMjk4REZGJw0KICAgICAgICAgICAgICAgICAgICAgICAgICA6IHJvdy5TdGF0ZSA9PT0gJzInDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnI0ZGOTAyQycNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHJvdy5TdGF0ZSA9PT0gJzMnDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICcjMjk4REZGJw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiByb3cuU3RhdGUgPT09ICc0Jw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICcjMDBEM0E3Jw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICcjMzMzMzMzJw0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgcm93LlN0YXRlID09PSAnMCcNCiAgICAgICAgICAgICAgICAgID8gJ+W+heWkhOeQhicNCiAgICAgICAgICAgICAgICAgIDogcm93LlN0YXRlID09PSAnMScNCiAgICAgICAgICAgICAgICAgICAgPyAn5aSE55CG5LitJw0KICAgICAgICAgICAgICAgICAgICA6IHJvdy5TdGF0ZSA9PT0gJzInDQogICAgICAgICAgICAgICAgICAgICAgPyAn5b6F5aSN5qOAJw0KICAgICAgICAgICAgICAgICAgICAgIDogcm93LlN0YXRlID09PSAnMycNCiAgICAgICAgICAgICAgICAgICAgICAgID8gJ+W+heivhOS7tycNCiAgICAgICAgICAgICAgICAgICAgICAgIDogcm93LlN0YXRlID09PSAnNCcNCiAgICAgICAgICAgICAgICAgICAgICAgICAgPyAn5aSE55CG5a6M5oiQJw0KICAgICAgICAgICAgICAgICAgICAgICAgICA6ICflt7LlhbPpl60nDQogICAgICAgICAgICAgICkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIF0sDQogICAgICAgIHRhYmxlRGF0YTogW10sDQogICAgICAgIHRhYmxlQWN0aW9uc1dpZHRoOiAyMjAsDQogICAgICAgIHRhYmxlQWN0aW9uczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGFjdGlvbkxhYmVsOiAnJywNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICB0eXBlOiAndGV4dCcNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIF0sDQogICAgICAgIGJ1dHRvbkNvbmZpZzogew0KICAgICAgICAgIGJ1dHRvbkxpc3Q6IFtdDQogICAgICAgIH0sDQogICAgICAgIG9wZXJhdGVPcHRpb25zOiB7DQogICAgICAgICAgd2lkdGg6IDMwMCAvLyDmk43kvZzmoI/lrr3luqYNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHt9LA0KICAvLyBtaXhpbnM6IFtBdXRoQnV0dG9uc10sDQogIHdhdGNoOiB7DQogICAgLy8gICAnQXV0aEJ1dHRvbnMuYnV0dG9ucyc6ew0KICAgIC8vICAgICBoYW5kbGVyKHZhbCxvbGR2YWwpew0KICAgIC8vICAgICAgIGNvbnNvbGUubG9nKCdkZGRzcycsdmFsLG9sZHZhbCk7DQogICAgLy8gICAgICAgICB0aGlzLnNob3c9dHJ1ZQ0KICAgIC8vICAgICAgIH0NCg0KICAgIC8vICAgICB9DQogICAgLy8gICB9DQogICAgZmxhZzogew0KICAgICAgaGFuZGxlcih2YWwpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ2RkZHNzJywgdmFsKQ0KICAgICAgICB0aGlzLmluaXREYXRhKCkNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7fSwNCiAgbW91bnRlZCgpIHsNCiAgICAvLyDot7Povazorr7nva7pu5jorqTlj4LmlbANCiAgICAvLyBsZXQgSnVtcFBhcmFtcyA9IHRoaXMuJHFpYW5rdW4uZ2V0TWljcm9BcHBKdW1wUGFyYW1zRm4oKTsNCiAgICAvLyBjb25zb2xlLmxvZyhKdW1wUGFyYW1zLkNyZWF0ZV9EYXRlLCAi6Lez6L2s5Y+C5pWwLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0iKTsNCiAgICAvLyBpZiAoSnVtcFBhcmFtcy5pc0p1bXAgPT0gInRydWUiKSB7DQogICAgLy8gICB0aGlzLnF1ZXJ5LlN0YXRlID0gTnVtYmVyKEp1bXBQYXJhbXMuU3RhdGUpOw0KICAgIC8vICAgLy8gdGhpcy5xdWVyeS5DcmVhdGVfRGF0ZSA9IEp1bXBQYXJhbXMuQ3JlYXRlX0RhdGU7DQogICAgLy8gICAvLyB0aGlzLnF1ZXJ5LkNyZWF0ZV9FRGF0ZSA9IEp1bXBQYXJhbXMuQ3JlYXRlX0VEYXRlOw0KICAgIC8vICAgLy8gdGhpcy5xdWVyeS5EYXRlID0gW0p1bXBQYXJhbXMuQ3JlYXRlX0RhdGUsIEp1bXBQYXJhbXMuQ3JlYXRlX0VEYXRlXTsNCiAgICAvLyB9DQogICAgdGhpcy5pbml0RGF0YSgpDQogIH0sDQogIGJlZm9yZURlc3Ryb3koKSB7DQogICAgdGhpcy4kcWlhbmt1bi5zZXRNaWNyb0FwcEp1bXBQYXJhbXNGbigpDQogICAgdGhpcy5xdWVyeS5TdGF0ZSA9IG51bGwNCiAgICAvLyB0aGlzLnF1ZXJ5LkNyZWF0ZV9EYXRlID0gbnVsbDsNCiAgICAvLyB0aGlzLnF1ZXJ5LkNyZWF0ZV9FRGF0ZSA9IG51bGw7DQogICAgLy8gdGhpcy5xdWVyeS5EYXRlID0gW107DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBhc3luYyBpbml0RGF0YSgpIHsNCiAgICAgIC8vIGxldCByZXMgPSBhd2FpdCBHZXRXb3JrT3JkZXJUeXBlKHsgQ29kZTogIldvcmtPcmRlclR5cGUiIH0pOw0KICAgICAgLy8gY29uc29sZS5sb2cocmVzLCAiMTIxMjEyMTIiKTsNCiAgICAgIC8vIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAvLyAgIHRoaXMud29ya1R5cGVMaXN0ID0gcmVzLkRhdGE7DQogICAgICAvLyB9DQoNCiAgICAgIHRoaXMudXNlcklkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1VzZXJJZCcpDQogICAgICBpZiAodGhpcy4kcm91dGUucXVlcnkudHlwZSA9PT0gJ215Jykgew0KICAgICAgICB0aGlzLnF1ZXJ5LnR5cGUgPSAwDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnF1ZXJ5LnR5cGUgPSAxDQogICAgICB9DQogICAgICBhd2FpdCB0aGlzLmluaXQoKQ0KICAgIH0sDQogICAgb3BlbkFkZCgpIHsNCiAgICAgIHRoaXMuZGlhbG9nVGl0bGUgPSAn5paw5aKeJw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLiRyZWZzLmRpYWxvZ1JlZi5pbml0KDAsIHt9LCAnYWRkJykNCiAgICAgIH0pDQogICAgfSwNCiAgICBzZWFyY2hGb3JtKCkgew0KICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5jdXJyZW50UGFnZSA9IDENCiAgICAgIHRoaXMub25GcmVzaCgpDQogICAgfSwNCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMucXVlcnkgPSB7DQogICAgICAgIERhdGU6IFtdLA0KICAgICAgICBPcmRlcl9Db2RlOiAnJywNCiAgICAgICAgT3JkZXJfTmFtZTogJycsDQogICAgICAgIENyZWF0ZV9EYXRlOiAnJywNCiAgICAgICAgQ3JlYXRlX0VEYXRlOiAnJywNCiAgICAgICAgU3RhdGU6ICcnLA0KICAgICAgICBXb3JrT3JkZXJfU2V0dXBfSWQ6ICdzYndiJywNCiAgICAgICAgTWFpbnRhaW5fUGVyc29uOiAnJywNCiAgICAgICAgV29ya09yZGVyX1N0YXRlOiB0aGlzLnF1ZXJ5LldvcmtPcmRlcl9TdGF0ZQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5LnR5cGUgPT09ICdteScpIHsNCiAgICAgICAgdGhpcy5xdWVyeS50eXBlID0gMA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5xdWVyeS50eXBlID0gMQ0KICAgICAgfQ0KICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5jdXJyZW50UGFnZSA9IDENCiAgICAgIHRoaXMub25GcmVzaCgpDQogICAgfSwNCiAgICByZXNldEZvcm0oKSB7DQogICAgICB0aGlzLm9uRnJlc2goKQ0KICAgIH0sDQogICAgb25GcmVzaCgpIHsNCiAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICB9LA0KICAgIGluaXQoKSB7DQogICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgfSwNCiAgICBnZXRUeXBlTGlzdCgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCdyZXMuRGF0YXJlcy5EYXRhcmVzLkRhdGFyZXMuRGF0YS0tLS0tLS0tLS0tLS0tLS0tLS0nKQ0KICAgICAgLy8gR2V0V29ya09yZGVyVHlwZSh7IENvZGU6ICJXb3JrT3JkZXJUeXBlIiB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgIC8vICAgY29uc29sZS5sb2coDQogICAgICAvLyAgICAgcmVzLkRhdGEsDQogICAgICAvLyAgICAgInJlcy5EYXRhcmVzLkRhdGFyZXMuRGF0YXJlcy5EYXRhLS0tLS0tLS0tLS0tLS0tLS0tLSINCiAgICAgIC8vICAgKTsNCiAgICAgIC8vICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgIC8vICAgICB0aGlzLnR5cGVMaXN0ID0gcmVzLkRhdGE7DQogICAgICAvLyAgIH0NCiAgICAgIC8vIH0pOw0KICAgIH0sDQogICAgYXN5bmMgZmV0Y2hEYXRhKCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0V29ya09yZGVyTWFuYWdlTGlzdCh7DQogICAgICAgIG1vZGVsOiB0aGlzLnF1ZXJ5LA0KICAgICAgICBwYWdlSW5mbzogew0KICAgICAgICAgIFBhZ2U6IHRoaXMuY3VzdG9tVGFibGVDb25maWcuY3VycmVudFBhZ2UsDQogICAgICAgICAgUGFnZVNpemU6IHRoaXMuY3VzdG9tVGFibGVDb25maWcucGFnZVNpemUsDQogICAgICAgICAgU29ydE5hbWU6ICdDcmVhdGVfRGF0ZScsDQogICAgICAgICAgU29ydE9yZGVyOiAnREVTQycNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcudGFibGVEYXRhID0gcmVzLkRhdGEuRGF0YQ0KICAgICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLnRvdGFsID0gcmVzLkRhdGEuVG90YWxDb3VudA0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlQ3JlYXRlKCkgew0KICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9ICfmlrDlop4nDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnMuZGlhbG9nUmVmLmluaXQoMCwge30sICdkaXNwYXRjaCcpDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlKGluZGV4LCByb3cpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ivt+ehruiupO+8jOaYr+WQpuWIoOmZpOivpeaVsOaNrj8nLCB7DQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIERlbGV0ZUNvYXRpbmdSZXF1aXIoeyBJZDogcm93LklkIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnycsDQogICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIHRoaXMuaW5pdCgpDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChfKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig6ZmkJw0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgfSwNCiAgICAvLyDmiZPlvIDmlrDlop7nvJbovpHlvLnnqpcNCiAgICBhc3luYyBvcGVuRGlhbG9nKHR5cGUsIHJvdywgb3JkZXJUeXBlKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRXb3JrT3JkZXJUeXBlKHsgQ29kZTogJ1dvcmtPcmRlclR5cGUnIH0pDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICB0aGlzLndvcmtUeXBlTGlzdCA9IHJlcy5EYXRhDQogICAgICB9DQogICAgICB0aGlzLiRyZWZzLmVkaXREaWFsb2cuaGFuZGxlT3Blbih0eXBlLCByb3csIG9yZGVyVHlwZSwgdGhpcy53b3JrVHlwZUxpc3QpDQogICAgfSwNCiAgICAvLyDmiZPlvIDlhbPpl63lt6XljZXlvLnnqpfmiJbor4Tku7flvLnnqpcNCiAgICBvcGVuQ2xvc2VSYXRlKHJvdywgdHlwZSkgew0KICAgICAgdGhpcy4kcmVmcy5jbG9zZVJhdGVEaWFsb2cuaGFuZGxlT3Blbih0eXBlLCByb3cpDQogICAgfSwNCiAgICAvLyDmjqXljZUNCiAgICByZWNlaXZpbmdPcmRlcnMocm93KSB7DQogICAgICBTZW5kV29ya09yZGVyUGVyc29uKHsgSWQ6IHJvdy5JZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmjqXljZXmiJDlip8nKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVTaXplQ2hhbmdlKHZhbCkgew0KICAgICAgY29uc29sZS5sb2coYOavj+mhtSAke3ZhbH0g5p2hYCkNCiAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcucGFnZVNpemUgPSB2YWwNCiAgICAgIHRoaXMub25GcmVzaCgpDQogICAgfSwNCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKHZhbCkgew0KICAgICAgY29uc29sZS5sb2coYOW9k+WJjemhtTogJHt2YWx9YCkNCiAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcuY3VycmVudFBhZ2UgPSB2YWwNCiAgICAgIHRoaXMub25GcmVzaCgpDQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICBjb25zdCBJZHMgPSBbXQ0KICAgICAgdGhpcy50YWJsZVNlbGVjdGlvbiA9IHNlbGVjdGlvbg0KICAgICAgdGhpcy50YWJsZVNlbGVjdGlvbi5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIElkcy5wdXNoKGl0ZW0uSWQpDQogICAgICB9KQ0KICAgICAgY29uc29sZS5sb2coSWRzKQ0KICAgICAgdGhpcy5zZWxlY3RJZHMgPSBJZHMNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMudGFibGVTZWxlY3Rpb24pDQogICAgfSwNCg0KICAgIGNoYW5nZURhdGUoKSB7DQogICAgICB0aGlzLnF1ZXJ5LkNyZWF0ZV9EYXRlID0gdGhpcy5xdWVyeS5EYXRlID8gdGhpcy5xdWVyeS5EYXRlWzBdIDogbnVsbA0KICAgICAgdGhpcy5xdWVyeS5DcmVhdGVfRURhdGUgPSB0aGlzLnF1ZXJ5LkRhdGUgPyB0aGlzLnF1ZXJ5LkRhdGVbMV0gOiBudWxsDQogICAgfSwNCiAgICBnZXRCdG5BdXRoKGNvZGUpIHsNCiAgICAgIC8vIGNvbnNvbGUubG9nKGNvZGUsdGhpcy5BdXRoQnV0dG9ucyx0aGlzLkF1dGhCdXR0b25zLmJ1dHRvbnMuZmluZChpdGVtPT5pdGVtLkNvZGU9PT1jb2RlKSk7DQogICAgICByZXR1cm4gdGhpcy5hdXRoQnV0dG9ucy5idXR0b25zLmZpbmQoKGl0ZW0pID0+IGl0ZW0uQ29kZSA9PT0gY29kZSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["device.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi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file": "device.vue", "sourceRoot": "src/views/business/maintenanceAndUpkeep/workOrderManagement/components", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <div class=\"toolbox\">\r\n          <!-- <div>\r\n              <el-button @click=\"openDialog('add')\" type=\"primary\">新 增</el-button>\r\n            </div> -->\r\n          <div>\r\n            <el-form inline>\r\n              <el-form-item label=\"工单号:\" style=\"margin-bottom: 10px\">\r\n                <el-input\r\n                  v-model=\"query.Order_Code\"\r\n                  clearable\r\n                  style=\"width: 150px\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"工单名称:\" style=\"margin-bottom: 10px\">\r\n                <el-input\r\n                  v-model=\"query.Order_Name\"\r\n                  clearable\r\n                  style=\"width: 150px\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"发起时间:\" style=\"margin-bottom: 10px\">\r\n                <el-date-picker\r\n                  v-model=\"query.Date\"\r\n                  align=\"right\"\r\n                  type=\"daterange\"\r\n                  placeholder=\"选择日期\"\r\n                  style=\"width: 300px\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  :picker-options=\"pickerOptions\"\r\n                  @change=\"changeDate\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"工单状态:\" style=\"margin-bottom: 10px\">\r\n                <el-select\r\n                  v-model=\"query.State\"\r\n                  clearable\r\n                  filterable\r\n                  style=\"width: 120px\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in stateList\"\r\n                    :key=\"item.code\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.code\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n              <!-- <el-form-item label=\"工单类型:\" style=\"margin-bottom: 10px\">\r\n                  <el-select\r\n                    v-model=\"query.WorkOrder_Setup_Id\"\r\n                    clearable\r\n                    filterable\r\n                    style=\"width: 120px\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in workTypeList\"\r\n                      :key=\"item.Value\"\r\n                      :label=\"item.Display_Name\"\r\n                      :value=\"item.Value\"\r\n                    />\r\n                  </el-select>\r\n                </el-form-item> -->\r\n              <el-form-item label=\"维修人:\" style=\"margin-bottom: 10px\">\r\n                <el-select\r\n                  v-model=\"query.Maintain_Person\"\r\n                  clearable\r\n                  filterable\r\n                  style=\"width: 150px\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in personList\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item label=\"设备查询:\" style=\"margin-bottom: 10px\">\r\n                <el-select\r\n                  v-model=\"query.EquipId\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请输入设备\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in equipOptions\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"item.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-button @click=\"reset()\">重 置</el-button>\r\n              <el-button type=\"primary\" @click=\"searchForm()\">查 询</el-button>\r\n            </el-form>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <div class=\"toolbox\">\r\n          <div>\r\n            <el-radio-group\r\n              v-model=\"query.WorkOrder_State\"\r\n              class=\"typeline\"\r\n              @input=\"searchForm\"\r\n            >\r\n              <el-radio-button :label=\"null\">全部</el-radio-button>\r\n              <el-radio-button :label=\"0\">待处理</el-radio-button>\r\n              <el-radio-button :label=\"1\">处理中</el-radio-button>\r\n              <el-radio-button :label=\"2\">已处理</el-radio-button>\r\n            </el-radio-group>\r\n          </div>\r\n          <div>\r\n            <el-button\r\n              type=\"primary\"\r\n              @click=\"openDialog('add')\"\r\n            >新 增</el-button>\r\n          </div>\r\n        </div>\r\n        <CustomTable\r\n          style=\"height: calc(100vh - 350px)\"\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        >\r\n          <template #customBtn=\"{ slotScope }\">\r\n            <template v-if=\"slotScope.State === '0' && slotScope.Is_Anth\">\r\n              <el-button\r\n                v-if=\"getBtnAuth('dispatch')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('dispatch', slotScope, slotScope.Order_Type)\"\r\n              >派工</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('dispatch-myorder')\"\r\n                type=\"text\"\r\n                code=\"dispatch-myorder\"\r\n                @click=\"openDialog('dispatch', slotScope, slotScope.Order_Type)\"\r\n              >派工</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('receive')\"\r\n                type=\"text\"\r\n                @click=\"receivingOrders(slotScope)\"\r\n              >接单</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('receive-myorder')\"\r\n                type=\"text\"\r\n                code=\"receive-myorder\"\r\n                @click=\"receivingOrders(slotScope)\"\r\n              >接单</el-button>\r\n            </template>\r\n            <el-button\r\n              v-if=\"getBtnAuth('detail')\"\r\n              type=\"text\"\r\n              @click=\"openDialog('detail', slotScope, slotScope.Order_Type)\"\r\n            >查看详情</el-button>\r\n            <el-button\r\n              v-if=\"getBtnAuth('detail-myorder')\"\r\n              type=\"text\"\r\n              @click=\"openDialog('detail', slotScope, slotScope.Order_Type)\"\r\n            >查看详情</el-button>\r\n            <template\r\n              v-if=\"\r\n                slotScope.State === '1' &&\r\n                  slotScope.Maintain_Person_Id === userId &&\r\n                  slotScope.Order_Type === 'jsbx'\r\n              \"\r\n            >\r\n              <el-button\r\n                v-if=\"getBtnAuth('handle')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('handle', slotScope, slotScope.Order_Type)\"\r\n              >工单处理</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('handle-myorder')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('handle', slotScope, slotScope.Order_Type)\"\r\n              >工单处理</el-button>\r\n            </template>\r\n            <template\r\n              v-if=\"\r\n                slotScope.State === '2' &&\r\n                  slotScope.Is_Anth &&\r\n                  slotScope.Order_Type === 'jsbx'\r\n              \"\r\n            >\r\n              <el-button\r\n                v-if=\"getBtnAuth('recheck')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('recheck', slotScope, slotScope.Order_Type)\"\r\n              >工单复检</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('recheck-myorder')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('recheck', slotScope, slotScope.Order_Type)\"\r\n              >工单复检</el-button>\r\n            </template>\r\n            <template\r\n              v-if=\"\r\n                slotScope.State === '3' &&\r\n                  slotScope.Order_Type === 'jsbx' &&\r\n                  slotScope.Create_UserId === userId\r\n              \"\r\n            >\r\n              <el-button\r\n                v-if=\"getBtnAuth('rate')\"\r\n                type=\"text\"\r\n                @click=\"openCloseRate(slotScope, 'rate')\"\r\n              >工单评价</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('rate-myorder')\"\r\n                type=\"text\"\r\n                @click=\"openCloseRate(slotScope, 'rate')\"\r\n              >工单评价</el-button>\r\n            </template>\r\n            <template v-if=\"slotScope.State === '0' || slotScope.State === '1'\">\r\n              <el-button\r\n                v-if=\"getBtnAuth('close')\"\r\n                type=\"text\"\r\n                @click=\"openCloseRate(slotScope, 'close')\"\r\n              >关闭</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('close-myorder')\"\r\n                type=\"text\"\r\n                @click=\"openCloseRate(slotScope, 'close')\"\r\n              >关闭</el-button>\r\n            </template>\r\n          </template>\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n    <editDialog ref=\"editDialog\" @refresh=\"fetchData\" />\r\n    <closeRateDialog ref=\"closeRateDialog\" @refresh=\"fetchData\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\n// import AuthButtons from \"@/mixins/auth-buttons\";\r\nimport editDialog from '../editDialog.vue'\r\nimport closeRateDialog from '../closeRateDialog.vue'\r\nimport {\r\n  GetWorkOrderManageList,\r\n  GetWorkOrderType,\r\n  GetPersonList,\r\n  DeleteCoatingRequir,\r\n  SendWorkOrderPerson,\r\n  GetEquipDropList\r\n} from '@/api/business/maintenanceAndUpkeep.js'\r\n\r\nexport default {\r\n  Name: '',\r\n  components: {\r\n    CustomTable,\r\n    CustomLayout,\r\n    editDialog,\r\n    closeRateDialog\r\n  },\r\n  props: {\r\n    flag: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    personList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    equipOptions: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    authButtons: {\r\n      type: Object,\r\n      default: () => {}\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      userId: '',\r\n      query: {\r\n        Date: [],\r\n        Order_Code: '',\r\n        Order_Name: '',\r\n        Create_Date: '',\r\n        Create_EDate: '',\r\n        State: '',\r\n        WorkOrder_Setup_Id: 'sbwb',\r\n        Maintain_Person: '',\r\n        WorkOrder_State: null,\r\n        Type: 1\r\n      },\r\n      type: '',\r\n      pickerOptions: {\r\n        shortcuts: [\r\n          {\r\n            text: '今天',\r\n            onClick(picker) {\r\n              picker.$emit('pick', [new Date(), new Date()])\r\n            }\r\n          },\r\n          {\r\n            text: '近7天',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '近30天',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '本月',\r\n            onClick(picker) {\r\n              const today = new Date()\r\n              const end = new Date(\r\n                today.getFullYear(),\r\n                today.getMonth() + 1,\r\n                0\r\n              )\r\n              const start = new Date(today.getFullYear(), today.getMonth(), 1)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      stateList: [\r\n        {\r\n          name: '待处理',\r\n          code: 0\r\n        },\r\n        {\r\n          name: '处理中',\r\n          code: 1\r\n        },\r\n        {\r\n          name: '待复检',\r\n          code: 2\r\n        },\r\n        {\r\n          name: '待评价',\r\n          code: 3\r\n        },\r\n        {\r\n          name: '处理完成',\r\n          code: 4\r\n        },\r\n        {\r\n          name: '已关闭',\r\n          code: 5\r\n        }\r\n      ],\r\n      workTypeList: [\r\n        // {\r\n        //   Display_Name: \"已关闭\",\r\n        //   Value: 5,\r\n        // },\r\n      ],\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        },\r\n        closeAndFresh: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n      selectIds: [],\r\n      customTableConfig: {\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            label: '序号',\r\n            otherOptions: {\r\n              type: 'index',\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '发起时间',\r\n            key: 'Create_Date'\r\n          },\r\n          {\r\n            label: '工单名称',\r\n            key: 'Order_Name'\r\n          },\r\n          {\r\n            label: '工单类型',\r\n            key: 'Order_Type',\r\n            render: (row) => {\r\n              return this.$createElement(\r\n                'span',\r\n                {},\r\n                row.Order_Type === null\r\n                  ? '-'\r\n                  : row.Order_Type === 'jsbx'\r\n                    ? '即时报修'\r\n                    : '设备维保'\r\n              )\r\n            }\r\n          },\r\n          {\r\n            label: '工单号',\r\n            key: 'Order_Code'\r\n          },\r\n          {\r\n            label: '开始处理时间',\r\n            key: 'Start_Time'\r\n          },\r\n          {\r\n            label: '处理完成时间',\r\n            key: 'End_Time'\r\n          },\r\n          {\r\n            label: '处理用时',\r\n            key: 'Time'\r\n          },\r\n          {\r\n            label: '报修部门',\r\n            key: 'Depart_Name'\r\n          },\r\n          {\r\n            label: '报修方',\r\n            key: 'Warranty_Person'\r\n          },\r\n          {\r\n            label: '维修人',\r\n            key: 'Maintain_Person'\r\n          },\r\n          {\r\n            label: '工单状态',\r\n            key: 'State',\r\n            render: (row) => {\r\n              return this.$createElement(\r\n                'span',\r\n                {\r\n                  style: {\r\n                    color:\r\n                      row.State === '0'\r\n                        ? '#FF5E7C'\r\n                        : row.State === '1'\r\n                          ? '#298DFF'\r\n                          : row.State === '2'\r\n                            ? '#FF902C'\r\n                            : row.State === '3'\r\n                              ? '#298DFF'\r\n                              : row.State === '4'\r\n                                ? '#00D3A7'\r\n                                : '#333333'\r\n                  }\r\n                },\r\n                row.State === '0'\r\n                  ? '待处理'\r\n                  : row.State === '1'\r\n                    ? '处理中'\r\n                    : row.State === '2'\r\n                      ? '待复检'\r\n                      : row.State === '3'\r\n                        ? '待评价'\r\n                        : row.State === '4'\r\n                          ? '处理完成'\r\n                          : '已关闭'\r\n              )\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActionsWidth: 220,\r\n        tableActions: [\r\n          {\r\n            actionLabel: '',\r\n            otherOptions: {\r\n              type: 'text'\r\n            }\r\n          }\r\n        ],\r\n        buttonConfig: {\r\n          buttonList: []\r\n        },\r\n        operateOptions: {\r\n          width: 300 // 操作栏宽度\r\n        }\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  // mixins: [AuthButtons],\r\n  watch: {\r\n    //   'AuthButtons.buttons':{\r\n    //     handler(val,oldval){\r\n    //       console.log('dddss',val,oldval);\r\n    //         this.show=true\r\n    //       }\r\n\r\n    //     }\r\n    //   }\r\n    flag: {\r\n      handler(val) {\r\n        console.log('dddss', val)\r\n        this.initData()\r\n      }\r\n    }\r\n  },\r\n  created() {},\r\n  mounted() {\r\n    // 跳转设置默认参数\r\n    // let JumpParams = this.$qiankun.getMicroAppJumpParamsFn();\r\n    // console.log(JumpParams.Create_Date, \"跳转参数-----------------------\");\r\n    // if (JumpParams.isJump == \"true\") {\r\n    //   this.query.State = Number(JumpParams.State);\r\n    //   // this.query.Create_Date = JumpParams.Create_Date;\r\n    //   // this.query.Create_EDate = JumpParams.Create_EDate;\r\n    //   // this.query.Date = [JumpParams.Create_Date, JumpParams.Create_EDate];\r\n    // }\r\n    this.initData()\r\n  },\r\n  beforeDestroy() {\r\n    this.$qiankun.setMicroAppJumpParamsFn()\r\n    this.query.State = null\r\n    // this.query.Create_Date = null;\r\n    // this.query.Create_EDate = null;\r\n    // this.query.Date = [];\r\n  },\r\n  methods: {\r\n    async initData() {\r\n      // let res = await GetWorkOrderType({ Code: \"WorkOrderType\" });\r\n      // console.log(res, \"12121212\");\r\n      // if (res.IsSucceed) {\r\n      //   this.workTypeList = res.Data;\r\n      // }\r\n\r\n      this.userId = localStorage.getItem('UserId')\r\n      if (this.$route.query.type === 'my') {\r\n        this.query.type = 0\r\n      } else {\r\n        this.query.type = 1\r\n      }\r\n      await this.init()\r\n    },\r\n    openAdd() {\r\n      this.dialogTitle = '新增'\r\n      this.dialogVisible = true\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.init(0, {}, 'add')\r\n      })\r\n    },\r\n    searchForm() {\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    reset() {\r\n      this.query = {\r\n        Date: [],\r\n        Order_Code: '',\r\n        Order_Name: '',\r\n        Create_Date: '',\r\n        Create_EDate: '',\r\n        State: '',\r\n        WorkOrder_Setup_Id: 'sbwb',\r\n        Maintain_Person: '',\r\n        WorkOrder_State: this.query.WorkOrder_State\r\n      }\r\n      if (this.$route.query.type === 'my') {\r\n        this.query.type = 0\r\n      } else {\r\n        this.query.type = 1\r\n      }\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.fetchData()\r\n    },\r\n    init() {\r\n      this.fetchData()\r\n    },\r\n    getTypeList() {\r\n      console.log('res.Datares.Datares.Datares.Data-------------------')\r\n      // GetWorkOrderType({ Code: \"WorkOrderType\" }).then((res) => {\r\n      //   console.log(\r\n      //     res.Data,\r\n      //     \"res.Datares.Datares.Datares.Data-------------------\"\r\n      //   );\r\n      //   if (res.IsSucceed) {\r\n      //     this.typeList = res.Data;\r\n      //   }\r\n      // });\r\n    },\r\n    async fetchData() {\r\n      const res = await GetWorkOrderManageList({\r\n        model: this.query,\r\n        pageInfo: {\r\n          Page: this.customTableConfig.currentPage,\r\n          PageSize: this.customTableConfig.pageSize,\r\n          SortName: 'Create_Date',\r\n          SortOrder: 'DESC'\r\n        }\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.dialogVisible = true\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.init(0, {}, 'dispatch')\r\n      })\r\n    },\r\n    handleDelete(index, row) {\r\n      this.$confirm('请确认，是否删除该数据?', {\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeleteCoatingRequir({ Id: row.Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n              this.init()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch((_) => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    // 打开新增编辑弹窗\r\n    async openDialog(type, row, orderType) {\r\n      const res = await GetWorkOrderType({ Code: 'WorkOrderType' })\r\n      if (res.IsSucceed) {\r\n        this.workTypeList = res.Data\r\n      }\r\n      this.$refs.editDialog.handleOpen(type, row, orderType, this.workTypeList)\r\n    },\r\n    // 打开关闭工单弹窗或评价弹窗\r\n    openCloseRate(row, type) {\r\n      this.$refs.closeRateDialog.handleOpen(type, row)\r\n    },\r\n    // 接单\r\n    receivingOrders(row) {\r\n      SendWorkOrderPerson({ Id: row.Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.fetchData()\r\n          this.$message.success('接单成功')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.onFresh()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.onFresh()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      const Ids = []\r\n      this.tableSelection = selection\r\n      this.tableSelection.forEach((item) => {\r\n        Ids.push(item.Id)\r\n      })\r\n      console.log(Ids)\r\n      this.selectIds = Ids\r\n      console.log(this.tableSelection)\r\n    },\r\n\r\n    changeDate() {\r\n      this.query.Create_Date = this.query.Date ? this.query.Date[0] : null\r\n      this.query.Create_EDate = this.query.Date ? this.query.Date[1] : null\r\n    },\r\n    getBtnAuth(code) {\r\n      // console.log(code,this.AuthButtons,this.AuthButtons.buttons.find(item=>item.Code===code));\r\n      return this.authButtons.buttons.find((item) => item.Code === code)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n  <style lang=\"scss\" scoped>\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n.toolbox {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 5px;\r\n  ::v-deep .el-form-item {\r\n    margin-bottom: 0px;\r\n  }\r\n}\r\n.typeline {\r\n  ::v-deep .el-radio-button__inner {\r\n    border-radius: 2px;\r\n  }\r\n  ::v-deep .is-active {\r\n    .el-radio-button__inner {\r\n      background-color: #ffffff;\r\n      color: #298dff;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}