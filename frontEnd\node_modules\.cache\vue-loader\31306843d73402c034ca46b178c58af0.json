{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\alarmCenter\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\alarmCenter\\index.vue", "mtime": 1755506574315}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/eventManagement/alarmCenter", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        />\n      </template>\n    </CustomLayout>\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <component\n        :is=\"currentComponent\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n      /></el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\n// import getGridByCode from \"../../safetyManagement/mixins/index\";\n// import DialogForm from \"./dialogForm.vue\";\n\nimport { downloadFile } from '@/utils/downloadFile'\nimport dayjs from 'dayjs'\nimport {\n  GetWarningPageList,\n  SetWarningStatus,\n  GetTypesByModule,\n  GetModule\n} from '@/api/business/eventManagement'\nexport default {\n  name: '',\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout\n  },\n  data() {\n    return {\n      currentComponent: null,\n      componentsConfig: {\n        Data: {}\n      },\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true\n        },\n        close: () => {\n          this.dialogVisible = false\n          this.onFresh()\n        }\n      },\n      dialogVisible: false,\n      dialogTitle: '编辑',\n      tableSelection: [],\n      ruleForm: {\n        Module: '',\n        WarningType: '',\n        WarningName: '',\n        Status: '0',\n        Date: [],\n        WarningBeg: null,\n        WarningEnd: null\n      },\n      customForm: {\n        formItems: [\n          {\n            key: 'Module',\n            label: '业务模块',\n            type: 'select',\n            options: [],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n              this.GetTypesByModule()\n            }\n          },\n          {\n            key: 'WarningType',\n            label: '告警类型',\n            type: 'select',\n            options: [],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'WarningName',\n            label: '告警名称',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'Status',\n            label: '状�?,\n            type: 'select',\n            options: [\n              {\n                label: '全部',\n                value: '0'\n              },\n              {\n                label: '告警�?,\n                value: '1'\n              },\n              {\n                label: '已关�?,\n                value: '2'\n              }\n              // {\n              //   label: \"已处�?,\n              //   value: \"3\",\n              // },\n            ],\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'Date', // 字段ID\n            label: '告警时间', // Form的label\n            type: 'datePicker', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              type: 'daterange',\n              disabled: false,\n              placeholder: '请输�?..'\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n              this.ruleForm.WarningBeg = dayjs(e[0]).format('YYYY-MM-DD')\n              this.ruleForm.WarningEnd = dayjs(e[1]).format('YYYY-MM-DD')\n            }\n          }\n        ],\n        rules: {},\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        loading: false,\n        buttonConfig: {\n          buttonList: [\n            {\n              text: '批量关闭',\n              onclick: (item) => {\n                console.log(item)\n                this.handleClose()\n              }\n            }\n          ]\n        },\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        height: '100%',\n        tableColumns: [\n          {\n            otherOptions: {\n              type: 'selection',\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '告警时间',\n            key: 'WarningTime',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '告警事件名称',\n            key: 'Name',\n            width: 140,\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '告警编号',\n            key: 'BusinessNo',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '告警类型',\n            key: 'WarningType',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '来源',\n            key: 'SourceName',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '业务模块',\n            key: 'Module',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n\n          {\n            label: '状�?,\n            key: 'StatusDisplay',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '通知人员',\n            key: 'ActualReceiveUsersNames',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '通知方式',\n            key: 'MessageNoticeModeDisplay',\n            width: 180,\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '操作人员',\n            key: 'ModifyUserName',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '操作时间',\n            key: 'ModifyDate',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '处理内容',\n            key: 'DoneContent',\n            otherOptions: {\n              align: 'center'\n            }\n          }\n        ],\n        tableData: [],\n        operateOptions: {\n          align: 'center',\n          width: '180'\n        },\n        tableActions: [\n          {\n            actionLabel: '查看详情',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              console.log(row, 'row')\n              const platform = 'digitalfactory'\n              const code = 'szgc'\n              const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\n              let url = ''\n              if (row.Module == '能耗管�?) {\n                url = '/business/energy/alarmDetail'\n              } else if (row.Module == '车辆道闸') {\n                url = '/bussiness/vehicle/alarm-info'\n              } else if (row.Module == '门禁管理') {\n                url = '/business/AccessControlAlarmDetails'\n              } else if (row.Module == '安防管理') {\n                url = '/business/equipmentAlarm'\n              } else if (row.Module == '危化品管�?) {\n                url = '/business/hazchem/alarmInformation'\n              } else if (row.Module == '环境管理') {\n                url = '/business/environment/alarmInformation'\n              } else if (row.Module == '访客管理') {\n                // url = \"/business/energy/alarmDetail\";\n                console.log('访客管理')\n              }\n              this.$qiankun.switchMicroAppFn(platform, code, id, url)\n            }\n          }\n          // {\n          //   actionLabel: \"编辑\",\n          //   otherOptions: {\n          //     type: \"text\",\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handleEdit(row);\n          //   },\n          // },\n        ]\n      }\n    }\n  },\n  computed: {},\n  created() {\n    this.init()\n\n    this.GetModule()\n  },\n  // mixins: [getGridByCode],\n  methods: {\n    async handleClose() {\n      const res = await SetWarningStatus({\n        Status: '2',\n        Ids: this.tableSelection.map((item) => item.Id)\n      })\n      if (res.IsSucceed) {\n        this.$message.success('操作成功')\n        this.onFresh()\n      }\n    },\n    async GetTypesByModule() {\n      const res = await GetTypesByModule({\n        Type: '2',\n        Module: this.ruleForm.Module\n      })\n      console.log(res, 'res')\n      if (res.IsSucceed) {\n        const result = res.Data || []\n        const typeList = result.map((item) => ({\n          value: item,\n          label: item\n        }))\n        this.customForm.formItems.find(\n          (item) => item.key == 'WarningType'\n        ).options = typeList\n      }\n    },\n    async GetModule() {\n      const res = await GetModule({})\n      console.log(res, 'res')\n      if (res.IsSucceed) {\n        const result = res.Data || []\n        const moduleList = result.map((item) => ({\n          value: item,\n          label: item\n        }))\n        console.log(moduleList, 'moduleList')\n        this.customForm.formItems.find((item) => item.key == 'Module').options =\n          [\n            {\n              value: '',\n              label: '全部'\n            },\n            ...moduleList\n          ]\n      }\n    },\n\n    searchForm(data) {\n      console.log(data)\n      this.customTableConfig.currentPage = 1\n      this.onFresh()\n    },\n    resetForm() {\n      this.ruleForm.WarningBeg = null\n      this.ruleForm.WarningEnd = null\n      this.ruleForm.Date = null\n\n      this.onFresh()\n    },\n    onFresh() {\n      this.GetWarningPageList()\n    },\n\n    init() {\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\n      this.GetWarningPageList()\n    },\n    async GetWarningPageList() {\n      this.customTableConfig.loading = true\n      // delete this.ruleForm.Date;\n      const res = await GetWarningPageList({\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n        ...this.ruleForm\n      }).finally(() => {\n        this.customTableConfig.loading = false\n      })\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data\n        this.customTableConfig.total = res.Data.TotalCount\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n    async handleExport() {\n      const res = await ExportEntranceWarning({\n        id: this.tableSelection.map((item) => item.Id).toString(),\n        code: 'AccessControlAlarmDetails1'\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        downloadFile(res.Data, '告警明细数据')\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.GetWarningPageList()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`)\n      this.customTableConfig.currentPage = val\n      this.GetWarningPageList()\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection\n    },\n    handleEdit(row) {\n      this.dialogVisible = true\n      this.componentsConfig.Data = row\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n\n.layout {\n  height: 100%;\n  width: 100%;\n  position: absolute;\n  ::v-deep {\n    .CustomLayout {\n      .layoutTable {\n        height: 0;\n        .CustomTable {\n          height: 100%;\n          display: flex;\n          flex-direction: column;\n          .table {\n            flex: 1;\n            height: 0;\n            display: flex;\n            flex-direction: column;\n            .el-table {\n              flex: 1;\n              height: 0;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}