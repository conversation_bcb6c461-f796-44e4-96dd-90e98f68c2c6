<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          label-width="130px"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @closed="closedDialog"
    >
      <component
        :is="currentComponent"
        ref="dialogRef"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";

import baseInfo from "./dialog/baseInfo.vue";
import importDialog from "@/views/business/vehicleBarrier/components/import.vue";
import exportInfo from "@/views/business/vehicleBarrier/mixins/export.js";

import { downloadFile } from "@/utils/downloadFile";
import {
  GetBarrierPageList,
  DelEquipment,
  ExportBarrierEquipment,
  ImportBarrierEquipment,
} from "@/api/business/vehicleBarrier.js";
export default {
  Name: "",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
    baseInfo,
    importDialog,
  },
  mixins: [exportInfo],
  data() {
    return {
      currentComponent: baseInfo,
      componentsConfig: {
        interfaceName: ImportBarrierEquipment,
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogVisible: false,
      dialogTitle: "",
      tableSelection: [],
      selectIds: [],
      ruleForm: {
        Name: "",
        Code: "",
        Brand: "",
        EquipmentModel: "",
        Factory: "",
        Vender: "",
        Engineer: "",
      },
      customForm: {
        formItems: [
          {
            key: "Name", // 字段ID
            label: "设备名称", // Form的label
            type: "input", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            placeholder: "请输入输入停车场名称",
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
            },
          },
          {
            key: "Brand",
            label: "设备品牌",
            type: "input",
            placeholder: "请输入停车场地址",
            otherOptions: {
              clearable: true,
            },
          },
          {
            key: "EquipmentModel",
            label: "规格型号",
            type: "input",
            otherOptions: {
              clearable: true,
            },
          },
          {
            key: "Factory",
            label: "厂家",
            type: "input",
            otherOptions: {
              clearable: true,
            },
          },
          {
            key: "Code",
            label: "智能道闸设备编码",
            type: "input",
            otherOptions: {
              clearable: true,
            },
          },
          {
            key: "Engineer",
            label: "维修工程师",
            type: "input",
            otherOptions: {
              clearable: true,
            },
          },
        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "新增",
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: true, // 是否禁用
              icon: "", //  图标
              autofocus: false, // 是否聚焦
              type: "primary", // primary / success / warning / danger / info / text
              size: "small", // medium / small / mini
              onclick: (item) => {
                console.log(item);
                this.handleCreate();
              },
            },
            {
              text: "下载模板",
              disabled: true, // 是否禁用
              onclick: (item) => {
                console.log(item);
                this.ExportData(
                  [],
                  "智能道闸设备管理模板",
                  ExportBarrierEquipment
                );
              },
            },
            {
              text: "批量导入",
              disabled: true, // 是否禁用
              onclick: (item) => {
                console.log(item);
                this.currentComponent = "importDialog";
                this.dialogVisible = true;
                this.dialogTitle = "批量导入";
              },
            },
            {
              key: "batch",
              disabled: false, // 是否禁用
              text: "批量导出",
              onclick: (item) => {
                console.log(item);
                this.ExportData(
                  {
                    ...this.ruleForm,
                    Ids: this.selectIds.toString(),
                  },
                  "智能道闸设备管理",
                  ExportBarrierEquipment
                );
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        height: "100%",
        tableActionsWidth: 220,
        tableColumns: [
          {
            width: 50,
            otherOptions: {
              type: "selection",
              align: "center",
            },
          },
          {
            label: "智能道闸设备编码",
            key: "Code",
          },
          {
            label: "智能道闸设备名称",
            key: "Name",
          },
          {
            label: "品牌",
            key: "Brand",
          },
          {
            label: "规格型号",
            key: "EquipmentModel",
          },
          {
            label: "厂家",
            key: "Factory",
          },
          {
            label: "厂家联系方式",
            key: "FactoryPhone",
          },
          {
            label: "供应商名称",
            key: "Vender",
          },
          {
            label: "供应商联系方式",
            key: "VenderPhone",
          },
          {
            label: "维修工程师",
            key: "Engineer",
          },
          {
            label: "维修工程师联系方式",
            key: "EngineerPhone",
          },
          {
            label: "设备状态",
            key: "StatusName",
          },
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: "编辑",
            otherOptions: {
              type: "text",
              disabled: true, // 是否禁用
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, "edit");
            },
          },
          {
            actionLabel: "删除",
            otherOptions: {
              type: "text",
              disabled: true, // 是否禁用
            },
            onclick: (index, row) => {
              this.handleDelete(index, row);
            },
          },
          {
            actionLabel: "查看详情",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, "view");
            },
          },
        ],
        operateOptions: {
          // width: 300 // 操作栏宽度
        },
      },
    };
  },
  computed: {},
  created() {
    this.init();
  },
  methods: {
    searchForm(data) {
      this.customTableConfig.currentPage = 1;
      console.log(data);
      this.onFresh();
    },
    resetForm() {
      this.onFresh();
    },
    onFresh() {
      this.fetchData();
    },
    init() {
      this.fetchData();
    },
    async fetchData() {
      const res = await GetBarrierPageList({
        ParameterJson: [
          {
            Key: "",
            Value: [null],
            Type: "",
            Filter_Type: "",
          },
        ],
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data.map((v) => {
          v.StatusName = v.Status == 1 ? "启用" : "停用";
          return v;
        });
        console.log(res);
        this.customTableConfig.total = res.Data.Total;
      }
    },
    handleCreate() {
      this.currentComponent = "baseInfo";
      this.dialogTitle = "新增";
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.dialogRef.add();
      });
    },
    handleDelete(index, row) {
      console.log(index, row);
      console.log(this);
      this.$confirm("确认删除?", {
        type: "warning",
      })
        .then(async (_) => {
          const res = await DelEquipment({
            Id: row.Id,
          });
          if (res.IsSucceed) {
            this.$message({
              message: "删除成功",
              type: "success",
            });
            this.init();
          } else {
            this.$message({
              message: res.Message,
              type: "error",
            });
          }
        })
        .catch((_) => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleEdit(index, row, type) {
      console.log(index, row, type);
      this.currentComponent = "baseInfo";
      if (type === "view") {
        this.dialogTitle = "查看";
      } else if (type === "edit") {
        this.dialogTitle = "编辑";
      }
      this.$nextTick(() => {
        this.$refs.dialogRef.init(index, row, type);
      });

      this.dialogVisible = true;
    },
    // 关闭弹窗
    closedDialog() {
      this.$refs.dialogRef.closeClearForm();
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.onFresh();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.onFresh();
    },
    handleSelectionChange(selection) {
      const Ids = [];
      this.tableSelection = selection;
      this.tableSelection.forEach((item) => {
        Ids.push(item.Id);
      });
      console.log(Ids);
      this.selectIds = Ids;
      console.log(this.tableSelection);
      // if (this.tableSelection.length > 0) {
      //   this.customTableConfig.buttonConfig.buttonList.find(
      //     (v) => v.key == "batch"
      //   ).disabled = false;
      // } else {
      //   this.customTableConfig.buttonConfig.buttonList.find(
      //     (v) => v.key == "batch"
      //   ).disabled = true;
      // }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/views/business/vehicleBarrier/index.scss";
.mt20 {
  margin-top: 10px;
}
::v-deep {
  .el-dialog__body {
    padding: 0px 20px 30px;
  }
}
</style>
