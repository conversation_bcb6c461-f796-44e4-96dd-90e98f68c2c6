<template>
  <div class="app-container abs100 maintenanceBox">
    <el-row :gutter="12">
      <el-col :span="24">
        <el-card shadow="hover">
          <div class="search_content">
            <span class="label">选择维度</span>
            <el-radio-group
              v-model="yearMonthRadio"
              class="radio"
              @change="yearMonthRadioChange"
            >
              <el-radio-button label="year">年</el-radio-button>
              <el-radio-button label="month">月</el-radio-button>
            </el-radio-group>
            <el-date-picker
              v-if="yearMonthRadio == 'year'"
              v-model="yearMonthValue"
              class="picker"
              :clearable="false"
              value-format="yyyy"
              type="year"
              @change="pickChange"
            />
            <el-date-picker
              v-else
              v-model="yearMonthValue"
              class="picker"
              :clearable="false"
              value-format="yyyy-MM"
              type="month"
              @change="pickChange"
            />
            <el-button @click="reset">重置</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <top
      :date="yearMonthValue"
      :date-type="yearMonthRadio == 'month' ? 2 : 1"
      :is-flag="isFlag"
    />
    <second
      :date="yearMonthValue"
      :date-type="yearMonthRadio == 'month' ? 2 : 1"
      :is-flag="isFlag"
      :params="{ ViewMore: '待办维保', Enable: true }"
    />
    <third
      :date="yearMonthValue"
      :is-flag="isFlag"
      :date-type="yearMonthRadio == 'month' ? 2 : 1"
    />
    <bottom
      :date="yearMonthValue"
      :is-flag="isFlag"
      :date-type="yearMonthRadio == 'month' ? 2 : 1"
    />
  </div>
</template>

<script>
import Top from './components/top'
import Second from './components/second'
import Third from './components/third'
import Bottom from './components/bottom'
import dayjs from 'dayjs'
export default {
  components: {
    Top,
    Second,
    Third,
    Bottom
  },
  data() {
    return {
      yearMonthRadio: 'month',
      yearMonthValue: dayjs().format('YYYY-MM'),
      type: 'month',
      isFlag: true
    }
  },
  watch: {
    date(nv, ov) {
      this.today = nv
      this.initData()
    }
  },
  created() {},
  mounted() {},
  methods: {
    yearMonthRadioChange(val) {
      this.isFlag = !this.isFlag
      if (val == 'year') {
        this.yearMonthValue = dayjs().format('YYYY')
      } else {
        this.yearMonthValue = dayjs().format('YYYY-MM')
      }
    },
    reset() {
      this.isFlag = !this.isFlag
      this.type = 'month'
      this.yearMonthRadio = 'month'
      this.yearMonthValue = dayjs().format('YYYY-MM')
    },
    pickChange() {
      this.isFlag = !this.isFlag
    }
  }
}
</script>
<style scoped lang='scss'>
.maintenanceBox {
  // padding: 10px 15px;
  // box-sizing: border-box;
  // height: calc(100vh - 90px);
  overflow-y: auto;
  .search_content {
    display: flex;
    flex-direction: row;
    align-items: center;
    overflow: hidden;
    .label {
      margin-right: 10px;
    }
    .radio {
      margin-right: 10px;
    }
    .picker {
      margin-right: 10px;
    }
  }
  // ::v-deep .el-radio-button__inner {
  //   background-color: #ffffff;
  //   // padding: 6px 32px;
  //   height: 32px;
  //   // line-height: 32px;
  //   width: 80px;
  //   font-size: 14px;
  // }
}
</style>
