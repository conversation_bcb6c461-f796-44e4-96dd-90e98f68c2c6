{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\components\\electricityLoss.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\components\\electricityLoss.vue", "mtime": 1755741159520}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "use", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Gauge<PERSON>hart", "GridComponent", "LegendComponent", "TooltipComponent", "TitleComponent", "DataZoomComponent", "GetElectricEnergyLoss", "components", "data", "Value", "Percent", "Color", "colorStops", "loading", "computed", "parentData", "DateType", "StartTime", "randomInteger", "pie1OptionRef", "_this", "tooltip", "show", "series", "silent", "type", "zlevel", "startAngle", "endAngle", "clockwise", "radius", "splitNumber", "avoidLabelOverlap", "axisLine", "lineStyle", "color", "width", "itemStyle", "progress", "axisTick", "distance", "length", "splitLine", "axisLabel", "pointer", "title", "detail", "center", "x", "y", "x2", "y2", "label", "position", "formatter", "pamars", "concat", "fontSize", "labelLine", "watch", "handler", "nv", "ov", "getElectricEnergyLoss", "created", "mounted", "inject", "methods", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "_res$Data", "wrap", "_callee$", "_context", "prev", "next", "sent", "IsSucceed", "Data", "offset", "stop"], "sources": ["src/views/business/energyManagement/pJEnergyAnalysis/eleNew/components/electricityLoss.vue"], "sourcesContent": ["<template>\n  <div class=\"electricityLoss\">\n    <div class=\"title\">\n      <div class=\"left\">\n        电量损耗<el-tooltip\n          class=\"item\"\n          content=\"正常的用电，高低压之间的转换不会小于2%\"\n          placement=\"top-start\"\n        >\n          <img src=\"@/assets/tooltip.png\" alt=\"\">\n        </el-tooltip>\n      </div>\n    </div>\n    <div\n      v-loading=\"loading\"\n      class=\"linearGradient\"\n      element-loading-text=\"加载中...\"\n    >\n      <div class=\"top\">\n        <div>\n          <p class=\"mb16\">电量损耗</p>\n          <p class=\"mb8\">\n            <b>{{ Value }}</b><span>度</span>\n          </p>\n        </div>\n      </div>\n      <div class=\"bottom\">\n        <p>\n          损耗率\n          <span :class=\"['customTag', Percent > 2 ? 'warning' : 'green']\">\n            {{ Percent > 2 ? \"损耗超限\" : \"损耗正常\" }}\n          </span>\n        </p>\n        <div class=\"chartBox\">\n          <v-chart\n            ref=\"pie2ChartRef\"\n            class=\"pie2ChartDom\"\n            :option=\"pie1OptionRef\"\n            :autoresize=\"true\"\n          />\n        </div>\n      </div>\n      <div class=\"divider\" />\n      <div class=\"tips\">高低压转换及线路损耗 <br>正常损耗率 < 2%</div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport VChart from 'vue-echarts'\nimport { use } from 'echarts/core'\nimport { CanvasRenderer } from 'echarts/renderers'\nimport { PieChart, GaugeChart } from 'echarts/charts'\nimport {\n  GridComponent,\n  LegendComponent,\n  TooltipComponent,\n  TitleComponent,\n  DataZoomComponent\n} from 'echarts/components'\nuse([\n  CanvasRenderer,\n  PieChart,\n  DataZoomComponent,\n  GridComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent,\n  GaugeChart\n])\nimport { GetElectricEnergyLoss } from '@/api/business/energyManagement'\nexport default {\n  components: {\n    VChart\n  },\n  data() {\n    return {\n      Value: 0,\n      Percent: 0,\n      Color: '#15C49C',\n      colorStops: [],\n      loading: true\n    }\n  },\n  computed: {\n    parentData() {\n      return {\n        DateType: this.DateType(),\n        StartTime: this.StartTime(),\n        randomInteger: this.randomInteger()\n      }\n    },\n    pie1OptionRef() {\n      return {\n        tooltip: {\n          show: false\n        },\n        series: [\n          {\n            silent: false,\n            type: 'gauge',\n            zlevel: 2,\n            startAngle: 0,\n            endAngle: 360,\n            clockwise: true,\n            radius: '100%',\n            splitNumber: 5,\n            avoidLabelOverlap: false,\n            axisLine: {\n              show: true,\n              lineStyle: {\n                color: [\n                  [this.Percent / 100, this.Color],\n                  [1, '#f0f2f8']\n                ],\n                width: 16\n              }\n            },\n            itemStyle: {\n              color: 'rgba(255,255,255,0)'\n            },\n            progress: {\n              show: true\n            },\n            axisTick: {\n              show: true,\n              splitNumber: 1,\n              distance: -16,\n              lineStyle: {\n                color: '#ffffff',\n                width: 3\n              },\n              length: 20\n            }, // 刻度样式\n            splitLine: {\n              show: false\n            },\n            axisLabel: {\n              show: false\n            },\n            pointer: {\n              show: false\n            },\n            title: {\n              show: false\n            },\n            detail: {\n              // formatter: \"{value}%\",\n              show: false\n            }\n          },\n          {\n            type: 'pie',\n            silent: true,\n            center: ['50%', '50%'],\n            radius: ['0%', '65%'],\n            avoidLabelOverlap: false,\n            zlevel: 3,\n            itemStyle: {\n              color: {\n                type: 'linear',\n                x: 0,\n                y: 1,\n                x2: 0,\n                y2: 0,\n                colorStops: this.colorStops\n              }\n              //   borderColor: 'rgba(41, 141, 255, 0.2)'\n            },\n            label: {\n              show: true,\n              position: 'center',\n              formatter: (pamars) => {\n                return `${this.Percent}%`\n              },\n              fontSize: 18,\n              color: '#3f4652'\n            },\n            labelLine: {\n              show: false\n            },\n            data: [1]\n          }\n        ]\n      }\n    }\n  },\n  watch: {\n    parentData: {\n      handler(nv, ov) {\n        this.getElectricEnergyLoss()\n      }\n    }\n  },\n  created() {\n    this.getElectricEnergyLoss()\n  },\n  mounted() {},\n  inject: ['DateType', 'StartTime', 'randomInteger'],\n  methods: {\n    async getElectricEnergyLoss() {\n      this.loading = true\n      const res = await GetElectricEnergyLoss(this.parentData)\n      if (res.IsSucceed) {\n        const { Value, Percent } = res.Data\n        this.Value = Value\n        this.Percent = Percent\n        if (this.Percent < 2) {\n          this.Color = '#15C49C'\n          this.colorStops = [\n            {\n              offset: 0,\n              color: 'rgba(217, 247, 233, 1)'\n            },\n            {\n              offset: 1,\n              color: 'rgba(247, 252, 254, 1)'\n            }\n          ]\n        } else {\n          this.Color = '#FFC62C'\n          this.colorStops = [\n            {\n              offset: 0,\n              color: 'rgba(255, 235, 223, 1)'\n            },\n            {\n              offset: 1,\n              color: 'rgba(255, 250, 247, 1)'\n            }\n          ]\n        }\n      }\n      this.loading = false\n    }\n  }\n}\n</script>\n<style scoped lang=\"scss\">\n.electricityLoss {\n  height: 510px;\n  background: #fff;\n  border-radius: 4px;\n  width: 100%;\n  padding: 16px;\n  box-sizing: border-box;\n  margin-bottom: 16px;\n  .title {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n    .left {\n      color: #666;\n      font-weight: bold;\n      font-size: 16px;\n      img {\n        margin-left: 8px;\n      }\n    }\n  }\n  .linearGradient {\n    height: 440px;\n    border-radius: 2px;\n    box-sizing: border-box;\n    .top {\n      height: 128px;\n      margin-bottom: 35px;\n      border-radius: 2px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      border: 1px solid rgba(41, 141, 255, 0.1);\n      background: linear-gradient(\n        90deg,\n        rgba(41, 141, 255, 0.05) 0%,\n        rgba(41, 141, 255, 0) 100%\n      );\n      div {\n        min-width: 70px;\n      }\n      p {\n        font-size: 16px;\n        color: #1d2541;\n        b {\n          color: #394f7f;\n          font-size: 28px;\n        }\n        span {\n          color: #999;\n          font-size: 14px;\n          margin-left: 8px;\n        }\n      }\n    }\n    .divider {\n      width: 100%;\n      height: 1px;\n      background: #eee;\n      margin-bottom: 24px;\n    }\n    .bottom {\n      text-align: center;\n      color: #1d2541;\n      font-size: 16px;\n      .customTag {\n        display: inline-block;\n        width: 56px;\n        height: 20px;\n        line-height: 20px;\n        font-size: 12px;\n        border-radius: 2px;\n        margin-left: 16px;\n      }\n      .warning {\n        color: #ff902c;\n        background: rgba(255, 144, 44, 0.1);\n      }\n      .green {\n        color: #4ebf8b;\n        background: rgba(78, 191, 139, 0.1);\n      }\n      .chartBox {\n        height: 200px;\n        padding: 20px;\n      }\n    }\n  }\n  .tips {\n    color: #b8bec8;\n    text-align: center;\n    font-size: 12px;\n    line-height: 18px;\n  }\n  .mb8 {\n    margin-bottom: 8px;\n  }\n  .mb16 {\n    margin-bottom: 16px;\n  }\n  .mb24 {\n    margin-bottom: 24px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA,OAAAA,MAAA;AACA,SAAAC,GAAA;AACA,SAAAC,cAAA;AACA,SAAAC,QAAA,EAAAC,UAAA;AACA,SACAC,aAAA,EACAC,eAAA,EACAC,gBAAA,EACAC,cAAA,EACAC,iBAAA,QACA;AACAR,GAAA,EACAC,cAAA,EACAC,QAAA,EACAM,iBAAA,EACAJ,aAAA,EACAC,eAAA,EACAE,cAAA,EACAD,gBAAA,EACAH,UAAA,CACA;AACA,SAAAM,qBAAA;AACA;EACAC,UAAA;IACAX,MAAA,EAAAA;EACA;EACAY,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,OAAA;MACAC,KAAA;MACAC,UAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA;QACAC,QAAA,OAAAA,QAAA;QACAC,SAAA,OAAAA,SAAA;QACAC,aAAA,OAAAA,aAAA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA;QACAC,OAAA;UACAC,IAAA;QACA;QACAC,MAAA,GACA;UACAC,MAAA;UACAC,IAAA;UACAC,MAAA;UACAC,UAAA;UACAC,QAAA;UACAC,SAAA;UACAC,MAAA;UACAC,WAAA;UACAC,iBAAA;UACAC,QAAA;YACAX,IAAA;YACAY,SAAA;cACAC,KAAA,GACA,MAAAzB,OAAA,aAAAC,KAAA,GACA,eACA;cACAyB,KAAA;YACA;UACA;UACAC,SAAA;YACAF,KAAA;UACA;UACAG,QAAA;YACAhB,IAAA;UACA;UACAiB,QAAA;YACAjB,IAAA;YACAS,WAAA;YACAS,QAAA;YACAN,SAAA;cACAC,KAAA;cACAC,KAAA;YACA;YACAK,MAAA;UACA;UAAA;UACAC,SAAA;YACApB,IAAA;UACA;UACAqB,SAAA;YACArB,IAAA;UACA;UACAsB,OAAA;YACAtB,IAAA;UACA;UACAuB,KAAA;YACAvB,IAAA;UACA;UACAwB,MAAA;YACA;YACAxB,IAAA;UACA;QACA,GACA;UACAG,IAAA;UACAD,MAAA;UACAuB,MAAA;UACAjB,MAAA;UACAE,iBAAA;UACAN,MAAA;UACAW,SAAA;YACAF,KAAA;cACAV,IAAA;cACAuB,CAAA;cACAC,CAAA;cACAC,EAAA;cACAC,EAAA;cACAvC,UAAA,OAAAA;YACA;YACA;UACA;UACAwC,KAAA;YACA9B,IAAA;YACA+B,QAAA;YACAC,SAAA,WAAAA,UAAAC,MAAA;cACA,UAAAC,MAAA,CAAApC,KAAA,CAAAV,OAAA;YACA;YACA+C,QAAA;YACAtB,KAAA;UACA;UACAuB,SAAA;YACApC,IAAA;UACA;UACAd,IAAA;QACA;MAEA;IACA;EACA;EACAmD,KAAA;IACA5C,UAAA;MACA6C,OAAA,WAAAA,QAAAC,EAAA,EAAAC,EAAA;QACA,KAAAC,qBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,qBAAA;EACA;EACAE,OAAA,WAAAA,QAAA;EACAC,MAAA;EACAC,OAAA;IACAJ,qBAAA,WAAAA,sBAAA;MAAA,IAAAK,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA,EAAAC,SAAA,EAAAjE,KAAA,EAAAC,OAAA;QAAA,OAAA4D,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAX,MAAA,CAAAvD,OAAA;cAAAgE,QAAA,CAAAE,IAAA;cAAA,OACAzE,qBAAA,CAAA8D,MAAA,CAAArD,UAAA;YAAA;cAAA0D,GAAA,GAAAI,QAAA,CAAAG,IAAA;cACA,IAAAP,GAAA,CAAAQ,SAAA;gBAAAP,SAAA,GACAD,GAAA,CAAAS,IAAA,EAAAzE,KAAA,GAAAiE,SAAA,CAAAjE,KAAA,EAAAC,OAAA,GAAAgE,SAAA,CAAAhE,OAAA;gBACA0D,MAAA,CAAA3D,KAAA,GAAAA,KAAA;gBACA2D,MAAA,CAAA1D,OAAA,GAAAA,OAAA;gBACA,IAAA0D,MAAA,CAAA1D,OAAA;kBACA0D,MAAA,CAAAzD,KAAA;kBACAyD,MAAA,CAAAxD,UAAA,IACA;oBACAuE,MAAA;oBACAhD,KAAA;kBACA,GACA;oBACAgD,MAAA;oBACAhD,KAAA;kBACA,EACA;gBACA;kBACAiC,MAAA,CAAAzD,KAAA;kBACAyD,MAAA,CAAAxD,UAAA,IACA;oBACAuE,MAAA;oBACAhD,KAAA;kBACA,GACA;oBACAgD,MAAA;oBACAhD,KAAA;kBACA,EACA;gBACA;cACA;cACAiC,MAAA,CAAAvD,OAAA;YAAA;YAAA;cAAA,OAAAgE,QAAA,CAAAO,IAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}