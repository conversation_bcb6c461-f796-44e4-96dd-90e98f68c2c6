{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJenergyManagement\\monitoringEquipmentArchives\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJenergyManagement\\monitoringEquipmentArchives\\index.vue", "mtime": 1755506574466}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy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file": "index.vue", "sourceRoot": "src/views/business/SZCJenergyManagement/monitoringEquipmentArchives", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      top=\"6vh\"\r\n      :destroy-on-close=\"true\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { parseTime } from '@/utils'\r\n// import { baseUrl } from '@/utils/baseurl'\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport DialogForm from './dialogForm.vue'\r\nimport DialogQuota from './dialogQuota.vue'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport {\r\n  GetDictionaryDetailListByCode,\r\n  GetEquipmentListSZCJ,\r\n  DeleteEquipment,\r\n  ExportEnergyEquipmentSZCJ,\r\n  GetEqtEntity\r\n} from '@/api/business/energyManagement'\r\nexport default {\r\n  name: 'MonitoringEquipmentArchives',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Content', // 字段ID\r\n            label: '点表编号或名�?, // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            placeholder: '输入点表编号或名称进行搜�?,\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EqtType',\r\n            label: '点表类型',\r\n            type: 'select',\r\n            placeholder: '请选择点表类型',\r\n            options: [], // 类型数据列表\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position', // 字段ID\r\n            label: '安装位置', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            placeholder: '请输入安装位�?,\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '新增',\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载�?\n              disabled: false, // 是否禁用\r\n              icon: '', //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleCreate()\r\n              }\r\n            },\r\n            {\r\n              text: '导出',\r\n              disabled: true,\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleExport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleAllExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            width: 60,\r\n            label: '序号',\r\n            otherOptions: {\r\n              type: 'index',\r\n              align: 'center'\r\n            } // key\r\n            // otherOptions: {\r\n            //   width: 180, // 宽度\r\n            //   fixed: 'left', // left, right\r\n            //   align: 'center' //\tleft/center/right\r\n            // }\r\n          }\r\n          //   {\r\n          //     label: '设备编号',\r\n          //     key: 'HId'\r\n          //   }\r\n        ],\r\n        tableData: [],\r\n        tableActionsWidth: 180,\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '配额',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleQuota(row)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.getBaseData()\r\n    this.init()\r\n  },\r\n  methods: {\r\n    getBaseData() {\r\n      // 获取点表类型\r\n      GetDictionaryDetailListByCode({ dictionaryCode: 'PointTableType' }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data.map(item => {\r\n            return {\r\n              label: item.Display_Name,\r\n              value: item.Value\r\n            }\r\n          })\r\n          this.customForm.formItems[1].options = data\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            data: res.Message\r\n          })\r\n        }\r\n      })\r\n      // 获取表格配置\r\n      GetGridByCode({ code: 'monitoring_equipment_archives_list' }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data.ColumnList.map(item => {\r\n            return {\r\n              label: item.Display_Name,\r\n              key: item.Code\r\n            }\r\n          })\r\n          this.customTableConfig.tableColumns.push(...data)\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getEquipmentList()\r\n    },\r\n    init() {\r\n      this.getEquipmentList()\r\n    },\r\n    async getEquipmentList() {\r\n      const res = await GetEquipmentListSZCJ({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.customTableConfig.tableData = res.Data.Data.map(item => {\r\n          item.Date = item.Date ? parseTime(new Date(item.Date), '{y}-{m}-{d}') : ''\r\n          return item\r\n        })\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.dialogVisible = true\r\n      this.componentsConfig = {}\r\n      this.currentComponent = DialogForm\r\n      this.$nextTick(() => {\r\n        this.$refs.currentComponent.init('add')\r\n      })\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row)\r\n      this.$confirm('该操作将在监测设备档案中删除该点表台账信�?请确认是否删�?', {\r\n        type: 'warning'\r\n      })\r\n        .then(async(_) => {\r\n          const res = await DeleteEquipment({\r\n            IDs: [row.Id]\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功'\r\n            })\r\n            this.init()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n        .catch((_) => { })\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      this.currentComponent = DialogForm\r\n      this.dialogVisible = true\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.componentsConfig = { ...row }\r\n        this.$nextTick(() => {\r\n          this.$refs.currentComponent.init(type)\r\n        })\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        this.componentsConfig = { ...row }\r\n        this.$nextTick(() => {\r\n          this.$refs.currentComponent.init(type)\r\n        })\r\n      }\r\n    },\r\n    async handleExport() {\r\n      console.log(this.ruleForm)\r\n      console.log(this.tableSelection, 'this.tableSelection')\r\n      const res = await ExportEnergyEquipmentSZCJ({\r\n        IsAll: false,\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n        // const url = new URL(res.Data, baseUrl())\r\n        // window.open(url.href, '_blank')\r\n        this.$message({\r\n          type: 'success',\r\n          message: '导出成功!'\r\n        })\r\n      }\r\n    },\r\n    handleQuota(row) {\r\n      this.currentComponent = DialogQuota\r\n      this.dialogTitle = '编辑'\r\n      this.dialogVisible = true\r\n      let data = {}\r\n      GetEqtEntity({ ID: row.Id }).then(res => {\r\n        if (res.IsSucceed) {\r\n          data = Object.assign(data, res.Data)\r\n          console.log(1, data)\r\n          this.componentsConfig = { ...row, ...data }\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    async handleAllExport() {\r\n      const res = await ExportEnergyEquipmentSZCJ({\r\n        IsAll: true,\r\n        Ids: [],\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n        // const url = new URL(res.Data, baseUrl())\r\n        // window.open(url.href, '_blank')\r\n        this.$message({\r\n          type: 'success',\r\n          message: '导出成功!'\r\n        })\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n      this.customTableConfig.buttonConfig.buttonList[1].disabled = selection.length === 0\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n  <style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n\r\n"]}]}