{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\thresholdSetting\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\thresholdSetting\\index.vue", "mtime": 1755674552417}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQ3VzdG9tTGF5b3V0IGZyb20gJ0AvYnVzaW5lc3NDb21wb25lbnRzL0N1c3RvbUxheW91dC9pbmRleC52dWUnDQppbXBvcnQgQ3VzdG9tVGFibGUgZnJvbSAnQC9idXNpbmVzc0NvbXBvbmVudHMvQ3VzdG9tVGFibGUvaW5kZXgudnVlJw0KaW1wb3J0IEN1c3RvbUZvcm0gZnJvbSAnQC9idXNpbmVzc0NvbXBvbmVudHMvQ3VzdG9tRm9ybS9pbmRleC52dWUnDQppbXBvcnQgRGlhbG9nRm9ybSBmcm9tICcuL2RpYWxvZ0Zvcm0udnVlJw0KaW1wb3J0IHsNCiAgRGVsVGhyZXNob2xkU2V0dGluZywNCiAgR2V0UGFnZUxpc3QsDQogIEdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlDb2RlDQp9IGZyb20gJ0AvYXBpL2J1c2luZXNzL2VuZXJneU1hbmFnZW1lbnQnDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdNb25pdG9yRGF0YScsDQogIGNvbXBvbmVudHM6IHsNCiAgICBDdXN0b21UYWJsZSwNCiAgICAvLyBDdXN0b21CdXR0b24sDQogICAgLy8gQ3VzdG9tVGl0bGUsDQogICAgQ3VzdG9tRm9ybSwNCiAgICBDdXN0b21MYXlvdXQNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgY3VycmVudENvbXBvbmVudDogRGlhbG9nRm9ybSwNCiAgICAgIGNvbXBvbmVudHNDb25maWc6IHt9LA0KICAgICAgY29tcG9uZW50c0Z1bnM6IHsNCiAgICAgICAgb3BlbjogKCkgPT4gew0KICAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgY2xvc2U6ICgpID0+IHsNCiAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMuaW5pdCgpDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGRpYWxvZ1RpdGxlOiAnJywNCiAgICAgIHRhYmxlU2VsZWN0aW9uOiBbXSwNCg0KICAgICAgcnVsZUZvcm06IHsNCiAgICAgICAgTm9kZUlkOiAnJywNCiAgICAgICAgRGV2aWNlVHlwZUlkOiAnJw0KICAgICAgfSwNCiAgICAgIGN1c3RvbUZvcm06IHsNCiAgICAgICAgZm9ybUl0ZW1zOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiAnTm9kZUlkJywgLy8g5a2X5q61SUQNCiAgICAgICAgICAgIGxhYmVsOiAn57uf6K6h6IqC54K5JywgLy8gRm9ybeeahGxhYmVsDQogICAgICAgICAgICB0eXBlOiAnc2VsZWN0JywNCiAgICAgICAgICAgIG9wdGlvbnM6IFtdLCAvLyDnsbvlnovmlbDmja7liJfooagNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICBjbGVhcmFibGU6IHRydWUNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB3aWR0aDogJzI0MHB4JywNCiAgICAgICAgICAgIGNoYW5nZTogKGUpID0+IHsNCiAgICAgICAgICAgICAgLy8gY2hhbmdl5LqL5Lu2DQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKGUpDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBrZXk6ICdEZXZpY2VUeXBlSWQnLA0KICAgICAgICAgICAgbGFiZWw6ICfngrnooajnsbvlnosnLA0KICAgICAgICAgICAgdHlwZTogJ3NlbGVjdCcsDQogICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+mAieaLqeeCueihqOexu+WeiycsDQogICAgICAgICAgICBvcHRpb25zOiBbXSwgLy8g57G75Z6L5pWw5o2u5YiX6KGoDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgLy8g6Zmk5LqGbW9kZWzku6XlpJbnmoTlhbbku5bnmoTlj4LmlbAs5YW35L2T6K+35Y+C6ICDZWxlbWVudOaWh+ahow0KICAgICAgICAgICAgICBjbGVhcmFibGU6IHRydWUNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBjaGFuZ2U6IChlKSA9PiB7DQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKGUpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICBydWxlczogew0KICAgICAgICAgIC8vIOivt+WPgueFp2VsZW1lbnRGb3JtIHJ1bGVzDQogICAgICAgIH0sDQogICAgICAgIGN1c3RvbUZvcm1CdXR0b25zOiB7DQogICAgICAgICAgc3VibWl0TmFtZTogJ+afpeivoicsDQogICAgICAgICAgcmVzZXROYW1lOiAn6YeN572uJw0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgY3VzdG9tVGFibGVDb25maWc6IHsNCiAgICAgICAgYnV0dG9uQ29uZmlnOiB7DQogICAgICAgICAgYnV0dG9uTGlzdDogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICB0ZXh0OiAn5paw5aKeJywNCiAgICAgICAgICAgICAgcm91bmQ6IGZhbHNlLCAvLyDmmK/lkKblnIbop5INCiAgICAgICAgICAgICAgcGxhaW46IGZhbHNlLCAvLyDmmK/lkKbmnLTntKANCiAgICAgICAgICAgICAgY2lyY2xlOiBmYWxzZSwgLy8g5piv5ZCm5ZyG5b2iDQogICAgICAgICAgICAgIGxvYWRpbmc6IGZhbHNlLCAvLyDmmK/lkKbliqDovb3kuK0NCiAgICAgICAgICAgICAgZGlzYWJsZWQ6IGZhbHNlLCAvLyDmmK/lkKbnpoHnlKgNCiAgICAgICAgICAgICAgaWNvbjogJycsIC8vICDlm77moIcNCiAgICAgICAgICAgICAgYXV0b2ZvY3VzOiBmYWxzZSwgLy8g5piv5ZCm6IGa54SmDQogICAgICAgICAgICAgIHR5cGU6ICdwcmltYXJ5JywgLy8gcHJpbWFyeSAvIHN1Y2Nlc3MgLyB3YXJuaW5nIC8gZGFuZ2VyIC8gaW5mbyAvIHRleHQNCiAgICAgICAgICAgICAgc2l6ZTogJ3NtYWxsJywgLy8gbWVkaXVtIC8gc21hbGwgLyBtaW5pDQogICAgICAgICAgICAgIG9uY2xpY2s6IChpdGVtKSA9PiB7DQogICAgICAgICAgICAgICAgY29uc29sZS5sb2coaXRlbSkNCiAgICAgICAgICAgICAgICB0aGlzLmhhbmRsZUNyZWF0ZSgpDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIC8vIOihqOagvA0KICAgICAgICBwYWdlU2l6ZU9wdGlvbnM6IFsxMCwgMjAsIDUwLCA4MF0sDQogICAgICAgIGN1cnJlbnRQYWdlOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMjAsDQogICAgICAgIHRvdGFsOiAwLA0KICAgICAgICB0YWJsZUNvbHVtbnM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogJ+e7n+iuoeiKgueCuScsDQogICAgICAgICAgICBrZXk6ICdOb2RlTmFtZScNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAn54K56KGo57G75Z6LJywNCiAgICAgICAgICAgIGtleTogJ0RldmljZVR5cGVOYW1lJw0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICflkYrorabmj5DnpLonLA0KICAgICAgICAgICAga2V5OiAnUHJvbXB0Jw0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICflkYrorabnsbvlnosnLA0KICAgICAgICAgICAga2V5OiAnV2FybmluZ1R5cGVOYW1lJw0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICfmnaHku7YnLA0KICAgICAgICAgICAga2V5OiAnQ29uZGl0aW9uTmFtZScNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAn6ZiI5YC8JywNCiAgICAgICAgICAgIGtleTogJ1RocmVzaG9sZCcNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAn5q+U5a+56aG5JywNCiAgICAgICAgICAgIGtleTogJ0NvbnRyYXN0TmFtZScNCiAgICAgICAgICB9DQogICAgICAgIF0sDQogICAgICAgIHRhYmxlRGF0YTogW10sDQogICAgICAgIHRhYmxlQWN0aW9uczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGFjdGlvbkxhYmVsOiAn57yW6L6RJywNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICB0eXBlOiAndGV4dCcNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBvbmNsaWNrOiAoaW5kZXgsIHJvdykgPT4gew0KICAgICAgICAgICAgICB0aGlzLmhhbmRsZUVkaXQoaW5kZXgsIHJvdywgJ2VkaXQnKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgYWN0aW9uTGFiZWw6ICfliKDpmaQnLA0KICAgICAgICAgICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgICAgIHR5cGU6ICd0ZXh0Jw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIG9uY2xpY2s6IChpbmRleCwgcm93KSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuaGFuZGxlRGVsZXRlKGluZGV4LCByb3cpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9DQogICAgfQ0KICB9LA0KICBjb21wdXRlZDoge30sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRCYXNlRGF0YSgpDQogICAgdGhpcy5pbml0KCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldEJhc2VEYXRhKCkgew0KICAgICAgLy8g6I635Y+W54K56KGo57G75Z6LDQogICAgICBHZXREaWN0aW9uYXJ5RGV0YWlsTGlzdEJ5Q29kZSh7IGRpY3Rpb25hcnlDb2RlOiAnUG9pbnRUYWJsZVR5cGUnIH0pLnRoZW4oDQogICAgICAgIChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlcy5EYXRhLm1hcCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAgIGxhYmVsOiBpdGVtLkRpc3BsYXlfTmFtZSwNCiAgICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5JZA0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgdGhpcy5jdXN0b21Gb3JtLmZvcm1JdGVtc1sxXS5vcHRpb25zID0gZGF0YQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywNCiAgICAgICAgICAgICAgZGF0YTogcmVzLk1lc3NhZ2UNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICApDQogICAgICAvLyDojrflj5bnu5/orqHoioLngrkNCiAgICAgIEdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlDb2RlKHsgZGljdGlvbmFyeUNvZGU6ICdFbmVyZ3lOb2RlJyB9KS50aGVuKA0KICAgICAgICAocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXMuRGF0YS5tYXAoKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgICAgICBsYWJlbDogaXRlbS5EaXNwbGF5X05hbWUsDQogICAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0uSWQNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMuY3VzdG9tRm9ybS5mb3JtSXRlbXNbMF0ub3B0aW9ucyA9IGRhdGENCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsDQogICAgICAgICAgICAgIGRhdGE6IHJlcy5NZXNzYWdlDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgKQ0KICAgIH0sDQogICAgc2VhcmNoRm9ybShkYXRhKSB7DQogICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmN1cnJlbnRQYWdlID0gMQ0KICAgICAgdGhpcy5pbml0KCkNCiAgICB9LA0KICAgIHJlc2V0Rm9ybSgpIHsNCiAgICAgIHRoaXMuaW5pdCgpDQogICAgfSwNCiAgICBpbml0KCkgew0KICAgICAgdGhpcy5nZXREYXRhTGlzdCgpDQogICAgfSwNCiAgICBhc3luYyBnZXREYXRhTGlzdCgpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldFBhZ2VMaXN0KHsNCiAgICAgICAgUGFnZTogdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5jdXJyZW50UGFnZSwNCiAgICAgICAgUGFnZVNpemU6IHRoaXMuY3VzdG9tVGFibGVDb25maWcucGFnZVNpemUsDQogICAgICAgIC4uLnRoaXMucnVsZUZvcm0NCiAgICAgIH0pDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLnRhYmxlRGF0YSA9IHJlcy5EYXRhLkRhdGENCiAgICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy50b3RhbCA9IHJlcy5EYXRhLlRvdGFsDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICB0eXBlOiAnZXJyb3InLA0KICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVDcmVhdGUoKSB7DQogICAgICB0aGlzLmRpYWxvZ1RpdGxlID0gJ+aWsOWinicNCiAgICAgIHRoaXMuY29tcG9uZW50c0NvbmZpZyA9IHsNCiAgICAgICAgdHlwZTogJ2FkZCcNCiAgICAgIH0NCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIGhhbmRsZUVkaXQoaW5kZXgsIHJvdywgdHlwZSkgew0KICAgICAgY29uc29sZS5sb2coaW5kZXgsIHJvdywgdHlwZSkNCiAgICAgIGlmICh0eXBlID09PSAnZWRpdCcpIHsNCiAgICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9ICfnvJbovpEnDQogICAgICAgIHRoaXMuY29tcG9uZW50c0NvbmZpZyA9IHsgLi4ucm93LCB0eXBlIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIGFzeW5jIGhhbmRsZURlbGV0ZShpbmRleCwgcm93KSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCBEZWxUaHJlc2hvbGRTZXR0aW5nKHsNCiAgICAgICAgSWQ6IHJvdy5JZA0KICAgICAgfSkNCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIHRoaXMuaW5pdCgpDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywNCiAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfIScNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgaGFuZGxlU2l6ZUNoYW5nZSh2YWwpIHsNCiAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcucGFnZVNpemUgPSB2YWwNCiAgICAgIHRoaXMuaW5pdCgpDQogICAgfSwNCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKHZhbCkgew0KICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5jdXJyZW50UGFnZSA9IHZhbA0KICAgICAgdGhpcy5pbml0KCkNCiAgICB9LA0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMudGFibGVTZWxlY3Rpb24gPSBzZWxlY3Rpb24NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/energyManagement/thresholdSetting", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      top=\"6vh\"\r\n      :destroy-on-close=\"true\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport DialogForm from './dialogForm.vue'\r\nimport {\r\n  DelThresholdSetting,\r\n  GetPageList,\r\n  GetDictionaryDetailListByCode\r\n} from '@/api/business/energyManagement'\r\nexport default {\r\n  name: 'MonitorData',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.init()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        NodeId: '',\r\n        DeviceTypeId: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'NodeId', // 字段ID\r\n            label: '统计节点', // Form的label\r\n            type: 'select',\r\n            options: [], // 类型数据列表\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'DeviceTypeId',\r\n            label: '点表类型',\r\n            type: 'select',\r\n            placeholder: '请选择点表类型',\r\n            options: [], // 类型数据列表\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '新增',\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: false, // 是否禁用\r\n              icon: '', //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleCreate()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: '统计节点',\r\n            key: 'NodeName'\r\n          },\r\n          {\r\n            label: '点表类型',\r\n            key: 'DeviceTypeName'\r\n          },\r\n          {\r\n            label: '告警提示',\r\n            key: 'Prompt'\r\n          },\r\n          {\r\n            label: '告警类型',\r\n            key: 'WarningTypeName'\r\n          },\r\n          {\r\n            label: '条件',\r\n            key: 'ConditionName'\r\n          },\r\n          {\r\n            label: '阈值',\r\n            key: 'Threshold'\r\n          },\r\n          {\r\n            label: '比对项',\r\n            key: 'ContrastName'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.getBaseData()\r\n    this.init()\r\n  },\r\n  methods: {\r\n    getBaseData() {\r\n      // 获取点表类型\r\n      GetDictionaryDetailListByCode({ dictionaryCode: 'PointTableType' }).then(\r\n        (res) => {\r\n          if (res.IsSucceed) {\r\n            const data = res.Data.map((item) => {\r\n              return {\r\n                label: item.Display_Name,\r\n                value: item.Id\r\n              }\r\n            })\r\n            this.customForm.formItems[1].options = data\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              data: res.Message\r\n            })\r\n          }\r\n        }\r\n      )\r\n      // 获取统计节点\r\n      GetDictionaryDetailListByCode({ dictionaryCode: 'EnergyNode' }).then(\r\n        (res) => {\r\n          if (res.IsSucceed) {\r\n            const data = res.Data.map((item) => {\r\n              return {\r\n                label: item.Display_Name,\r\n                value: item.Id\r\n              }\r\n            })\r\n            this.customForm.formItems[0].options = data\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              data: res.Message\r\n            })\r\n          }\r\n        }\r\n      )\r\n    },\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.init()\r\n    },\r\n    resetForm() {\r\n      this.init()\r\n    },\r\n    init() {\r\n      this.getDataList()\r\n    },\r\n    async getDataList() {\r\n      const res = await GetPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        this.customTableConfig.total = res.Data.Total\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.componentsConfig = {\r\n        type: 'add'\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        this.componentsConfig = { ...row, type }\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    async handleDelete(index, row) {\r\n      const res = await DelThresholdSetting({\r\n        Id: row.Id\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.init()\r\n        this.$message({\r\n          type: 'success',\r\n          message: '删除成功!'\r\n        })\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.thresholdSetting {\r\n  overflow: hidden;\r\n}\r\n</style>\r\n\r\n"]}]}