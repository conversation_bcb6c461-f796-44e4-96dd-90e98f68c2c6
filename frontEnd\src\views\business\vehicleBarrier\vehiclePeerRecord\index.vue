<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      title="查看"
      :visible.sync="dialogVisible"
      width="600px"
    >
      <el-image v-if="PassImg" :src="PassImg" class="imgwapper" />
      <div v-else class="empty-img">暂无图片</div>
    </el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";
import {
  GetPassRecordList,
  ExportPassRecordData,
} from "@/api/business/vehicleBarrier.js";
import exportInfo from "@/views/business/vehicleBarrier/mixins/export.js";
import { parseTime } from "@/utils/index.js";
export default {
  Name: "vehiclePeerRecord",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
  },
  mixins: [exportInfo],
  data() {
    return {
      ruleForm: {
        VehicleOwnerName: "",
        VehicleOwnerPhone: "",
        Number: "",
        StartTime: null,
        EndTime: null,
        EquipmentDate: [],
      },
      dialogVisible: false,
      PassImg: "", // 图片
      vehicleTypeOption: [], // 车辆类型
      tableSelection: [],
      selectIds: [],
      customForm: {
        formItems: [
          {
            key: "VehicleOwnerName", // 字段ID
            label: "车主姓名", // Form的label
            type: "input", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
            },
            input: (e) => {},
            change: () => {},
          },
          {
            key: "VehicleOwnerPhone",
            label: "车主联系方式",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            input: (e) => {},
            change: () => {},
          },
          {
            key: "Number",
            label: "车牌",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            input: (e) => {},
            change: () => {},
          },
          // {
          //   key: 'Number',
          //   label: '通行方式',
          //   type: 'input',
          //   otherOptions: {
          //     clearable: true
          //   }
          // },
          {
            key: "EquipmentDate",
            label: "时间",
            type: "datePicker",
            otherOptions: {
              type: "datetimerange",
              rangeSeparator: "至",
              startPlaceholder: "开始日期",
              endPlaceholder: "结束日期",
              clearable: true,
              valueFormat: "yyyy-MM-dd HH:mm",
            },
            change: (e) => {
              this.ruleForm.StartTime = e[0];
              this.ruleForm.EndTime = e[1];
            },
          },
        ],
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              key: "batch",
              disabled: false, // 是否禁用
              text: "批量导出",
              onclick: (item) => {
                console.log(item);

                this.ExportData(
                  {
                    ...this.ruleForm,
                    Ids: this.selectIds.toString(),
                  },
                  "车辆通行记录",
                  ExportPassRecordData
                );
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        height: "100%",
        tableColumns: [
          {
            width: 50,
            otherOptions: {
              type: "selection",
              align: "center",
            },
          },
          {
            label: "车牌号码",
            key: "Number",
          },
          {
            label: "车辆品牌",
            key: "Brand",
          },
          {
            label: "车主姓名",
            key: "VehicleOwnerName",
          },
          {
            label: "车主联系方式",
            key: "VehicleOwnerPhone",
          },
          {
            label: "车辆分类",
            key: "TypeName",
          },
          {
            label: "车辆属性",
            key: "Attr",
          },
          {
            label: "过车方向",
            key: "PassTypeName",
          },
          {
            label: "时间",
            key: "PassTime",
            render: (row) => {
              return (
                <span>
                  {row.PassTime
                    ? parseTime(
                        new Date(row.PassTime),
                        "{y}-{m}-{d} {h}:{i}:{s}"
                      )
                    : null}
                </span>
              );
            },
          },
          {
            label: "出入口",
            key: "GatewayId",
          },
          {
            label: "通行方式",
            key: "PassModeName",
          },
          {
            label: "停车场",
            key: "ParkingName",
          },
          {
            label: "停车时长",
            key: "ParkingTime",
            // render: row => {
            //   return (<span>{row.ParkingTime ? parseTime(new Date(row.ParkingTime), '{y}-{m}-{d} {h}:{i}:{s}') : ''}</span>)
            // }
          },
          {
            label: "原因",
            key: "Reason",
          },
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: "查看",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleview(row);
            },
          },
        ],
      },
    };
  },
  created() {
    // 测试
    this.init();
  },
  methods: {
    searchForm(data) {
      this.customTableConfig.currentPage = 1;
      console.log(data);
      this.onFresh();
    },
    resetForm() {
      this.onFresh();
    },
    onFresh() {
      this.fetchData();
    },
    async init() {
      await this.fetchData();
    },
    async fetchData() {
      if (this.ruleForm.EquipmentDate.length == 0) {
        this.ruleForm.StartTime = null;
        this.ruleForm.EndTime = null;
      }
      const res = await GetPassRecordList({
        ParameterJson: [
          {
            Key: "",
            Value: [null],
            Type: "",
            Filter_Type: "",
          },
        ],
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data.map((item) => {
          item.PassTypeName =
            item.PassType == 1 ? "驶入" : item.PassType == 2 ? "驶出" : "-";

          switch (Number(item.PassMode)) {
            case 0:
              item.PassModeName = "其他";
              break;
            case 1:
              item.PassModeName = "客户端开闸放行";
              break;
            case 2:
              item.PassModeName = "遥控器开闸放行";
              break;
            case 3:
              item.PassModeName = "场内扫码支付放行";
              break;
            case 4:
              item.PassModeName = "车道静态码支付放行 ";
              break;
            case 5:
              item.PassModeName = "无感支付放行";
              break;
            case 6:
              item.PassModeName = "自动放行";
              break;
            default:
              item.PassModeName = "-";
              break;
          }

          return item;
        });
        this.customTableConfig.total = res.Data.Total;
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.onFresh();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.onFresh();
    },
    handleSelectionChange(selection) {
      const Ids = [];
      this.tableSelection = selection;
      this.tableSelection.forEach((item) => {
        Ids.push(item.Id);
      });
      console.log(Ids);
      this.selectIds = Ids;
      console.log(this.tableSelection);
      // if (this.tableSelection.length > 0) {
      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = false
      // } else {
      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = true
      // }
    },
    handleview(row) {
      this.dialogVisible = true;
      this.PassImg = row.PassImg;
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/views/business/vehicleBarrier/index.scss";

.imgwapper {
  width: 100px;
  height: 100px;
}
.empty-img {
  text-align: center;
}
</style>
