{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\personnelManagement\\pjIndex.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\personnelManagement\\pjIndex.vue", "mtime": 1755674552431}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "getGridByCode", "querypersonnel", "DeletePersonnel", "DownloadPersonnelsTemplate", "DownloadPersonnelsToExcel", "ExportPersonnelList", "GetDepartment", "GetCompany", "GetMesTeams", "downloadFile", "addDialog", "DialogFormImport", "DialogPhotoFormImport", "addRouterPage", "components", "mixins", "data", "_this", "ruleForm", "name", "mobile", "personnelType", "companyId", "departmentId", "MesTeamId", "personnelStatus", "customForm", "formItems", "key", "label", "type", "otherOptions", "clearable", "change", "e", "console", "log", "options", "value", "filterable", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "text", "icon", "onclick", "handleDownTemplate", "handleImport", "handlePhotoImport", "handleExport", "handleClick", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "tableData", "operateOptions", "align", "width", "tableActions", "actionLabel", "index", "row", "handleWatch", "Id", "handleDele", "multipleSelection", "dialogVisible", "currentComponent", "dialogTitle", "componentsFuns", "close", "fetchData", "componentsConfig", "addPageArray", "path", "$route", "hidden", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "meta", "title", "created", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getMesTeams", "initGetDepartment", "find", "item", "sent", "initGetCompany", "getGridByCodeRender", "Tag", "condition", "val1", "val2", "stop", "mounted", "_this3", "_callee2", "_callee2$", "_context2", "getDictionaryDetailListByCode", "methods", "_this4", "_objectSpread", "Page", "PageSize", "res", "IsSucceed", "Data", "TotalCount", "$message", "error", "Message", "submitForm", "_callee3", "_callee3$", "_context3", "map", "Display_Name", "Value", "abrupt", "_callee4", "_callee4$", "_context4", "handleSizeChange", "val", "handleCurrentChange", "handleSelectionChange", "_this5", "_callee5", "_callee5$", "_context5", "v", "toString", "message", "$router", "push", "query", "pg_redirect", "id", "_this6", "$confirm", "confirmButtonText", "cancelButtonText", "catch", "_this7", "_this8", "_callee6", "_callee6$", "_context6", "disabled", "_this9", "_callee7", "_callee7$", "_context7", "_this10", "_callee8", "_callee8$", "_context8", "TeamName"], "sources": ["src/views/business/personnelManagement/pjIndex.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 personnelManagement\">\r\n    <custom-layout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"submitForm\"\r\n          @resetForm=\"fetchData\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </custom-layout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport getGridByCode from '../safetyManagement/mixins/index'\r\nimport {\r\n  querypersonnel,\r\n  DeletePersonnel,\r\n  DownloadPersonnelsTemplate,\r\n  DownloadPersonnelsToExcel,\r\n  ExportPersonnelList\r\n} from '@/api/business/personnelManagementV2.js'\r\nimport { GetDepartment, GetCompany, GetMesTeams } from '@/api/business/accessControl'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport addDialog from './components/pjAddDialog'\r\nimport DialogFormImport from './components/pjImportFile'\r\nimport DialogPhotoFormImport from './components/pjPhotoImportFile'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nexport default {\r\n  components: {\r\n    CustomLayout,\r\n    CustomTable,\r\n    CustomForm\r\n  },\r\n  mixins: [getGridByCode, addRouterPage],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        name: '',\r\n        mobile: '',\r\n        personnelType: null,\r\n        companyId: '',\r\n        departmentId: '',\r\n        MesTeamId: '',\r\n        personnelStatus: null\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'name',\r\n            label: '姓名',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'mobile',\r\n            label: '联系方式',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'personnelType',\r\n            label: '人员类型',\r\n            type: 'select',\r\n            options: [\r\n              {\r\n                label: '系统人员',\r\n                value: '1'\r\n              },\r\n              {\r\n                label: '普通人员',\r\n                value: '2'\r\n              }\r\n            ],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'companyId',\r\n            label: '所属公司',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'departmentId',\r\n            label: '所属部门',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'personnelStatus',\r\n            label: '状态',\r\n            type: 'select',\r\n            options: [\r\n              {\r\n                label: '在职',\r\n                value: '1'\r\n              },\r\n              {\r\n                label: '离职',\r\n                value: '2'\r\n              }\r\n            ],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'MesTeamId',\r\n            label: 'MES班组',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n              filterable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '导入模板下载',\r\n              icon: 'el-icon-download',\r\n              onclick: () => {\r\n                this.handleDownTemplate()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导入',\r\n              icon: 'el-icon-upload2',\r\n              onclick: () => {\r\n                this.handleImport()\r\n              }\r\n            },\r\n            {\r\n              text: '照片导入',\r\n              icon: 'el-icon-upload2',\r\n              onclick: () => {\r\n                this.handlePhotoImport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导出',\r\n              icon: 'el-icon-download',\r\n              onclick: () => {\r\n                this.handleExport()\r\n              }\r\n            },\r\n            {\r\n              text: '新增',\r\n              icon: 'el-icon-plus',\r\n              type: 'primary',\r\n              onclick: () => {\r\n                this.handleClick('add')\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        pageSizeOptions: [20, 40, 60, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center',\r\n          width: '130px'\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleWatch(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleClick('edit', row)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDele(row.Id)\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      multipleSelection: [],\r\n      dialogVisible: false,\r\n      currentComponent: null,\r\n      dialogTitle: '新增',\r\n      componentsFuns: {\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.fetchData()\r\n        }\r\n      },\r\n      componentsConfig: {},\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/pjInfo',\r\n          hidden: true,\r\n          component: () => import('./pjInfo.vue'),\r\n          meta: { title: '人员详情' },\r\n          name: 'PersonnelManagementPJInfo'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  async created() {\r\n    this.fetchData()\r\n    this.getMesTeams()\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'departmentId'\r\n    ).options = await this.initGetDepartment()\r\n    // 所属单位\r\n    this.customForm.formItems.find((item) => item.key === 'companyId').options =\r\n      await this.initGetCompany()\r\n    this.getGridByCodeRender('PJPersonnelManagement', [\r\n      { key: 'Gender', Tag: 'span', condition: 1, val1: '男', val2: '女' },\r\n      {\r\n        key: 'PersonnelStatus',\r\n        Tag: 'span',\r\n        condition: 1,\r\n        val1: '在职',\r\n        val2: '离职'\r\n      },\r\n      {\r\n        key: 'PersonnelType',\r\n        Tag: 'span',\r\n        condition: 1,\r\n        val1: '系统人员',\r\n        val2: '普通人员'\r\n      }\r\n    ])\r\n  },\r\n  async mounted() {\r\n    this.customForm.formItems[1].options =\r\n      await this.getDictionaryDetailListByCode('PatrolResult')\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      querypersonnel({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.tableData = res.Data.Data\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n\r\n    submitForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData()\r\n    },\r\n    async initGetDepartment() {\r\n      const res = await GetDepartment({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    async initGetCompany() {\r\n      const res = await GetCompany({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    handleSizeChange(val) {\r\n      this.customTableConfig.pageSize = val\r\n      this.fetchData()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.customTableConfig.currentPage = val\r\n      this.fetchData()\r\n    },\r\n    handleSelectionChange(data) {\r\n      this.multipleSelection = data\r\n    },\r\n    // async handleExport() {\r\n    //   const res = await DownloadPersonnelsToExcel({\r\n    //     id: this.multipleSelection.map(v => v.Id)\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     downloadFile(res.Data, '人员管理')\r\n    //   } else {\r\n    //     this.$message({\r\n    //       type: 'error',\r\n    //       message: res.Message\r\n    //     })\r\n    //   }\r\n    // },\r\n    // v2 版本导出\r\n    async handleExport() {\r\n      const res = await ExportPersonnelList({\r\n        Id: this.multipleSelection.map((v) => v.Id).toString(),\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        downloadFile(res.Data, '人员管理')\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    handleClick(type, data) {\r\n      this.dialogVisible = true\r\n      this.currentComponent = addDialog\r\n      if (type == 'add') {\r\n        this.dialogTitle = '新增'\r\n        this.componentsConfig = {\r\n          name: '',\r\n          mobile: '',\r\n          personnelType: null,\r\n          companyId: '',\r\n          departmentId: '',\r\n          personnelStatus: null\r\n        }\r\n      } else {\r\n        this.dialogTitle = '编辑'\r\n        this.componentsConfig = data\r\n      }\r\n    },\r\n    handleWatch(Id) {\r\n      this.$router.push({\r\n        name: 'PersonnelManagementPJInfo',\r\n        query: { pg_redirect: this.$route.name, Id }\r\n      })\r\n    },\r\n    handleDele(id) {\r\n      this.$confirm('是否确定删除该数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeletePersonnel({ id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                type: 'error',\r\n                message: res.Message\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => { })\r\n    },\r\n    handleDownTemplate() {\r\n      DownloadPersonnelsTemplate({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data, '授权名单导入模板')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    async handleImport() {\r\n      this.dialogTitle = '批量导入'\r\n      this.currentComponent = DialogFormImport\r\n      this.componentsConfig = {\r\n        disabled: true,\r\n        title: '批量导入'\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    async handlePhotoImport() {\r\n      this.dialogTitle = '数据批量导入'\r\n      this.currentComponent = DialogPhotoFormImport\r\n      this.componentsConfig = {\r\n        disabled: true,\r\n        title: '数据批量导入'\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    async getMesTeams() {\r\n      const res = await GetMesTeams()\r\n      if (res.IsSucceed) {\r\n        this.customForm.formItems.find((item) => item.key === 'MesTeamId').options =\r\n          res.Data.map(v => {\r\n            v.value = v.MesTeamId\r\n            v.label = v.TeamName\r\n            return v\r\n          })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n  <style scoped lang='scss'>\r\n  .personnelManagement{\r\n    // height: calc(100vh - 90px);\r\n    // overflow: hidden;\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,OAAAC,aAAA;AACA,SACAC,cAAA,EACAC,eAAA,EACAC,0BAAA,EACAC,yBAAA,EACAC,mBAAA,QACA;AACA,SAAAC,aAAA,EAAAC,UAAA,EAAAC,WAAA;AACA,SAAAC,YAAA;AACA,OAAAC,SAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,qBAAA;AACA,OAAAC,aAAA;AACA;EACAC,UAAA;IACAjB,YAAA,EAAAA,YAAA;IACAC,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA;EACA;EACAgB,MAAA,GAAAf,aAAA,EAAAa,aAAA;EACAG,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,QAAA;QACAC,IAAA;QACAC,MAAA;QACAC,aAAA;QACAC,SAAA;QACAC,YAAA;QACAC,SAAA;QACAC,eAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAO,OAAA,GACA;YACAR,KAAA;YACAS,KAAA;UACA,GACA;YACAT,KAAA;YACAS,KAAA;UACA,EACA;UACAP,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAO,OAAA;UACAN,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAO,OAAA;UACAN,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAO,OAAA,GACA;YACAR,KAAA;YACAS,KAAA;UACA,GACA;YACAT,KAAA;YACAS,KAAA;UACA,EACA;UACAP,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAN,GAAA;UACAC,KAAA;UACAC,IAAA;UACAO,OAAA;UACAN,YAAA;YACAC,SAAA;YACAO,UAAA;UACA;UACAN,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,EACA;QACAM,KAAA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAC,IAAA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAA;cACAhC,KAAA,CAAAiC,kBAAA;YACA;UACA,GACA;YACAH,IAAA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAA;cACAhC,KAAA,CAAAkC,YAAA;YACA;UACA,GACA;YACAJ,IAAA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAA;cACAhC,KAAA,CAAAmC,iBAAA;YACA;UACA,GACA;YACAL,IAAA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAA;cACAhC,KAAA,CAAAoC,YAAA;YACA;UACA,GACA;YACAN,IAAA;YACAC,IAAA;YACAlB,IAAA;YACAmB,OAAA,WAAAA,QAAA;cACAhC,KAAA,CAAAqC,WAAA;YACA;UACA;QAEA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA;QACAC,SAAA;QACAC,cAAA;UACAC,KAAA;UACAC,KAAA;QACA;QACAC,YAAA,GACA;UACAC,WAAA;UACAlC,YAAA;YACAD,IAAA;UACA;UACAmB,OAAA,WAAAA,QAAAiB,KAAA,EAAAC,GAAA;YACAlD,KAAA,CAAAmD,WAAA,CAAAD,GAAA,CAAAE,EAAA;UACA;QACA,GACA;UACAJ,WAAA;UACAlC,YAAA;YACAD,IAAA;UACA;UACAmB,OAAA,WAAAA,QAAAiB,KAAA,EAAAC,GAAA;YACAlD,KAAA,CAAAqC,WAAA,SAAAa,GAAA;UACA;QACA,GACA;UACAF,WAAA;UACAlC,YAAA;YACAD,IAAA;UACA;UACAmB,OAAA,WAAAA,QAAAiB,KAAA,EAAAC,GAAA;YACAlD,KAAA,CAAAqD,UAAA,CAAAH,GAAA,CAAAE,EAAA;UACA;QACA;MAEA;MACAE,iBAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,cAAA;QACAC,KAAA,WAAAA,MAAA;UACA3D,KAAA,CAAAuD,aAAA;UACAvD,KAAA,CAAA4D,SAAA;QACA;MACA;MACAC,gBAAA;MACAC,YAAA,GACA;QACAC,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAC,IAAA;UAAAC,KAAA;QAAA;QACAvE,IAAA;MACA;IAEA;EACA;EACAwE,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAT,MAAA,CAAAf,SAAA;YACAe,MAAA,CAAAU,WAAA;YAAAH,QAAA,CAAAE,IAAA;YAAA,OAGAT,MAAA,CAAAW,iBAAA;UAAA;YAFAX,MAAA,CAAAlE,UAAA,CAAAC,SAAA,CAAA6E,IAAA,CACA,UAAAC,IAAA;cAAA,OAAAA,IAAA,CAAA7E,GAAA;YAAA,CACA,EAAAS,OAAA,GAAA8D,QAAA,CAAAO,IAAA;YAAAP,QAAA,CAAAE,IAAA;YAAA,OAGAT,MAAA,CAAAe,cAAA;UAAA;YADAf,MAAA,CAAAlE,UAAA,CAAAC,SAAA,CAAA6E,IAAA,WAAAC,IAAA;cAAA,OAAAA,IAAA,CAAA7E,GAAA;YAAA,GAAAS,OAAA,GAAA8D,QAAA,CAAAO,IAAA;YAEAd,MAAA,CAAAgB,mBAAA,2BACA;cAAAhF,GAAA;cAAAiF,GAAA;cAAAC,SAAA;cAAAC,IAAA;cAAAC,IAAA;YAAA,GACA;cACApF,GAAA;cACAiF,GAAA;cACAC,SAAA;cACAC,IAAA;cACAC,IAAA;YACA,GACA;cACApF,GAAA;cACAiF,GAAA;cACAC,SAAA;cACAC,IAAA;cACAC,IAAA;YACA,EACA;UAAA;UAAA;YAAA,OAAAb,QAAA,CAAAc,IAAA;QAAA;MAAA,GAAAjB,OAAA;IAAA;EACA;EACAkB,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAtB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAqB,SAAA;MAAA,OAAAtB,mBAAA,GAAAG,IAAA,UAAAoB,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAlB,IAAA,GAAAkB,SAAA,CAAAjB,IAAA;UAAA;YAAAiB,SAAA,CAAAjB,IAAA;YAAA,OAEAc,MAAA,CAAAI,6BAAA;UAAA;YADAJ,MAAA,CAAAzF,UAAA,CAAAC,SAAA,IAAAU,OAAA,GAAAiF,SAAA,CAAAZ,IAAA;UAAA;UAAA;YAAA,OAAAY,SAAA,CAAAL,IAAA;QAAA;MAAA,GAAAG,QAAA;IAAA;EAEA;EACAI,OAAA;IACA3C,SAAA,WAAAA,UAAA;MAAA,IAAA4C,MAAA;MACAxH,cAAA,CAAAyH,aAAA;QACAC,IAAA,OAAA/E,iBAAA,CAAAY,WAAA;QACAoE,QAAA,OAAAhF,iBAAA,CAAAa;MAAA,GACA,KAAAvC,QAAA,CACA,EAAAoE,IAAA,WAAAuC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAL,MAAA,CAAA7E,iBAAA,CAAAgB,SAAA,GAAAiE,GAAA,CAAAE,IAAA,CAAAA,IAAA;UACAN,MAAA,CAAA7E,iBAAA,CAAAc,KAAA,GAAAmE,GAAA,CAAAE,IAAA,CAAAC,UAAA;QACA;UACAP,MAAA,CAAAQ,QAAA,CAAAC,KAAA,CAAAL,GAAA,CAAAM,OAAA;QACA;MACA;IACA;IAEAC,UAAA,WAAAA,WAAApH,IAAA;MACA,KAAA4B,iBAAA,CAAAY,WAAA;MACA,KAAAqB,SAAA;IACA;IACA0B,iBAAA,WAAAA,kBAAA;MAAA,OAAAV,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAsC,SAAA;QAAA,IAAAR,GAAA,EAAAxF,OAAA;QAAA,OAAAyD,mBAAA,GAAAG,IAAA,UAAAqC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnC,IAAA,GAAAmC,SAAA,CAAAlC,IAAA;YAAA;cAAAkC,SAAA,CAAAlC,IAAA;cAAA,OACA/F,aAAA;YAAA;cAAAuH,GAAA,GAAAU,SAAA,CAAA7B,IAAA;cACArE,OAAA,GAAAwF,GAAA,CAAAE,IAAA,CAAAS,GAAA,WAAA/B,IAAA,EAAAvC,KAAA;gBAAA;kBACArC,KAAA,EAAA4E,IAAA,CAAAgC,YAAA;kBACAnG,KAAA,EAAAmE,IAAA,CAAAiC;gBACA;cAAA;cAAA,OAAAH,SAAA,CAAAI,MAAA,WACAtG,OAAA;YAAA;YAAA;cAAA,OAAAkG,SAAA,CAAAtB,IAAA;UAAA;QAAA,GAAAoB,QAAA;MAAA;IACA;IACA1B,cAAA,WAAAA,eAAA;MAAA,OAAAd,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA6C,SAAA;QAAA,IAAAf,GAAA,EAAAxF,OAAA;QAAA,OAAAyD,mBAAA,GAAAG,IAAA,UAAA4C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1C,IAAA,GAAA0C,SAAA,CAAAzC,IAAA;YAAA;cAAAyC,SAAA,CAAAzC,IAAA;cAAA,OACA9F,UAAA;YAAA;cAAAsH,GAAA,GAAAiB,SAAA,CAAApC,IAAA;cACArE,OAAA,GAAAwF,GAAA,CAAAE,IAAA,CAAAS,GAAA,WAAA/B,IAAA,EAAAvC,KAAA;gBAAA;kBACArC,KAAA,EAAA4E,IAAA,CAAAgC,YAAA;kBACAnG,KAAA,EAAAmE,IAAA,CAAAiC;gBACA;cAAA;cAAA,OAAAI,SAAA,CAAAH,MAAA,WACAtG,OAAA;YAAA;YAAA;cAAA,OAAAyG,SAAA,CAAA7B,IAAA;UAAA;QAAA,GAAA2B,QAAA;MAAA;IACA;IACAG,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAApG,iBAAA,CAAAa,QAAA,GAAAuF,GAAA;MACA,KAAAnE,SAAA;IACA;IACAoE,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAApG,iBAAA,CAAAY,WAAA,GAAAwF,GAAA;MACA,KAAAnE,SAAA;IACA;IACAqE,qBAAA,WAAAA,sBAAAlI,IAAA;MACA,KAAAuD,iBAAA,GAAAvD,IAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAqC,YAAA,WAAAA,aAAA;MAAA,IAAA8F,MAAA;MAAA,OAAAtD,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAqD,SAAA;QAAA,IAAAvB,GAAA;QAAA,OAAA/B,mBAAA,GAAAG,IAAA,UAAAoD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlD,IAAA,GAAAkD,SAAA,CAAAjD,IAAA;YAAA;cAAAiD,SAAA,CAAAjD,IAAA;cAAA,OACAhG,mBAAA,CAAAqH,aAAA;gBACArD,EAAA,EAAA8E,MAAA,CAAA5E,iBAAA,CAAAiE,GAAA,WAAAe,CAAA;kBAAA,OAAAA,CAAA,CAAAlF,EAAA;gBAAA,GAAAmF,QAAA;cAAA,GACAL,MAAA,CAAAjI,QAAA,CACA;YAAA;cAHA2G,GAAA,GAAAyB,SAAA,CAAA5C,IAAA;cAIA,IAAAmB,GAAA,CAAAC,SAAA;gBACArH,YAAA,CAAAoH,GAAA,CAAAE,IAAA;cACA;gBACAoB,MAAA,CAAAlB,QAAA;kBACAnG,IAAA;kBACA2H,OAAA,EAAA5B,GAAA,CAAAM;gBACA;cACA;YAAA;YAAA;cAAA,OAAAmB,SAAA,CAAArC,IAAA;UAAA;QAAA,GAAAmC,QAAA;MAAA;IACA;IACA9F,WAAA,WAAAA,YAAAxB,IAAA,EAAAd,IAAA;MACA,KAAAwD,aAAA;MACA,KAAAC,gBAAA,GAAA/D,SAAA;MACA,IAAAoB,IAAA;QACA,KAAA4C,WAAA;QACA,KAAAI,gBAAA;UACA3D,IAAA;UACAC,MAAA;UACAC,aAAA;UACAC,SAAA;UACAC,YAAA;UACAE,eAAA;QACA;MACA;QACA,KAAAiD,WAAA;QACA,KAAAI,gBAAA,GAAA9D,IAAA;MACA;IACA;IACAoD,WAAA,WAAAA,YAAAC,EAAA;MACA,KAAAqF,OAAA,CAAAC,IAAA;QACAxI,IAAA;QACAyI,KAAA;UAAAC,WAAA,OAAA5E,MAAA,CAAA9D,IAAA;UAAAkD,EAAA,EAAAA;QAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAwF,EAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACApI,IAAA;MACA,GACAwD,IAAA;QACApF,eAAA;UAAA4J,EAAA,EAAAA;QAAA,GAAAxE,IAAA,WAAAuC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAiC,MAAA,CAAA9B,QAAA;cACAnG,IAAA;cACA2H,OAAA;YACA;YACAM,MAAA,CAAAlF,SAAA;UACA;YACAkF,MAAA,CAAA9B,QAAA;cACAnG,IAAA;cACA2H,OAAA,EAAA5B,GAAA,CAAAM;YACA;UACA;QACA;MACA,GACAgC,KAAA;IACA;IACAjH,kBAAA,WAAAA,mBAAA;MAAA,IAAAkH,MAAA;MACAjK,0BAAA,KAAAmF,IAAA,WAAAuC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACArH,YAAA,CAAAoH,GAAA,CAAAE,IAAA;QACA;UACAqC,MAAA,CAAAnC,QAAA,CAAAC,KAAA,CAAAL,GAAA,CAAAM,OAAA;QACA;MACA;IACA;IACAhF,YAAA,WAAAA,aAAA;MAAA,IAAAkH,MAAA;MAAA,OAAAxE,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAuE,SAAA;QAAA,OAAAxE,mBAAA,GAAAG,IAAA,UAAAsE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApE,IAAA,GAAAoE,SAAA,CAAAnE,IAAA;YAAA;cACAgE,MAAA,CAAA3F,WAAA;cACA2F,MAAA,CAAA5F,gBAAA,GAAA9D,gBAAA;cACA0J,MAAA,CAAAvF,gBAAA;gBACA2F,QAAA;gBACA/E,KAAA;cACA;cACA2E,MAAA,CAAA7F,aAAA;YAAA;YAAA;cAAA,OAAAgG,SAAA,CAAAvD,IAAA;UAAA;QAAA,GAAAqD,QAAA;MAAA;IACA;IACAlH,iBAAA,WAAAA,kBAAA;MAAA,IAAAsH,MAAA;MAAA,OAAA7E,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA4E,SAAA;QAAA,OAAA7E,mBAAA,GAAAG,IAAA,UAAA2E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzE,IAAA,GAAAyE,SAAA,CAAAxE,IAAA;YAAA;cACAqE,MAAA,CAAAhG,WAAA;cACAgG,MAAA,CAAAjG,gBAAA,GAAA7D,qBAAA;cACA8J,MAAA,CAAA5F,gBAAA;gBACA2F,QAAA;gBACA/E,KAAA;cACA;cACAgF,MAAA,CAAAlG,aAAA;YAAA;YAAA;cAAA,OAAAqG,SAAA,CAAA5D,IAAA;UAAA;QAAA,GAAA0D,QAAA;MAAA;IACA;IACArE,WAAA,WAAAA,YAAA;MAAA,IAAAwE,OAAA;MAAA,OAAAjF,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAgF,SAAA;QAAA,IAAAlD,GAAA;QAAA,OAAA/B,mBAAA,GAAAG,IAAA,UAAA+E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7E,IAAA,GAAA6E,SAAA,CAAA5E,IAAA;YAAA;cAAA4E,SAAA,CAAA5E,IAAA;cAAA,OACA7F,WAAA;YAAA;cAAAqH,GAAA,GAAAoD,SAAA,CAAAvE,IAAA;cACA,IAAAmB,GAAA,CAAAC,SAAA;gBACAgD,OAAA,CAAApJ,UAAA,CAAAC,SAAA,CAAA6E,IAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAA7E,GAAA;gBAAA,GAAAS,OAAA,GACAwF,GAAA,CAAAE,IAAA,CAAAS,GAAA,WAAAe,CAAA;kBACAA,CAAA,CAAAjH,KAAA,GAAAiH,CAAA,CAAA/H,SAAA;kBACA+H,CAAA,CAAA1H,KAAA,GAAA0H,CAAA,CAAA2B,QAAA;kBACA,OAAA3B,CAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA0B,SAAA,CAAAhE,IAAA;UAAA;QAAA,GAAA8D,QAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}