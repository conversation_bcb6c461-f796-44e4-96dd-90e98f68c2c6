{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\internalVehicleManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\pJVehicleBarrier\\internalVehicleManagement\\index.vue", "mtime": 1755506574531}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw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file": "index.vue", "sourceRoot": "src/views/business/vehicleBarrier/pJVehicleBarrier/internalVehicleManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        >\r\n          <template #formSlot=\"{ slotScope }\">\r\n            <el-cascader\r\n              v-if=\"slotScope.key == 'DeptId'\"\r\n              style=\"width: 100%\"\r\n              ref=\"departmentPersonnelCascader\"\r\n              v-model=\"departmentPersonnelValue\"\r\n              :options=\"departmentPersonnelOptions\"\r\n              :props=\"{\r\n                expandTrigger: 'hover',\r\n                checkStrictly: true,\r\n              }\"\r\n              :clearable=\"true\"\r\n              @change=\"departmentPersonnelHandleChange\"\r\n            ></el-cascader>\r\n          </template>\r\n        </CustomForm>\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"600px\"\r\n      @closed=\"closedDialog\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"dialogRef\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport {\r\n  VehiclesGetVehicleList,\r\n  VehiclesDelVehicle,\r\n  VehiclesExportData,\r\n  VehiclesGetDeptList,\r\n  GetDictionaryDetailListByCode,\r\n  VehiclesDownloadTemplate,\r\n  VehiclesImportDataStream,\r\n} from \"@/api/business/vehicleBarrier.js\";\r\nimport baseInfo from \"./dialog/baseInfo.vue\";\r\nimport importDialog from \"@/views/business/vehicleBarrier/components/import.vue\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/pJVehicleBarrier/mixins/export\";\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nexport default {\r\n  Name: \"internalVehicleManagement\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n    baseInfo,\r\n    importDialog,\r\n  },\r\n  mixins: [exportInfo, addRouterPage],\r\n  data() {\r\n    return {\r\n      // 部门人员\r\n      departmentPersonnelValue: [],\r\n      departmentPersonnelOptions: [],\r\n\r\n      ruleForm: {\r\n        Number: \"\",\r\n        UserName: \"\",\r\n        UserPhone: \"\",\r\n        DeptId: \"\",\r\n        VehicleType: \"\",\r\n      },\r\n      componentsConfig: {\r\n        interfaceName: VehiclesImportDataStream,\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogTitle: \"\",\r\n      dialogVisible: false,\r\n      currentComponent: null,\r\n      //\r\n      PassImg: \"\", // 图片\r\n      vehicleTypeOption: [], // 车辆类型\r\n      tableSelection: [],\r\n      selectIds: [],\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Number\", // 字段ID\r\n            label: \"车牌号码\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"UserName\",\r\n            label: \"车主姓名\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"UserPhone\",\r\n            label: \"联系方式\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            input: (e) => {},\r\n            change: () => {},\r\n          },\r\n          {\r\n            key: \"DeptId\",\r\n            label: \"所属部�?,\r\n            type: \"slot\",\r\n          },\r\n          // {\r\n          //   key: \"DeptId\",\r\n          //   label: \"所属部�?,\r\n          //   type: \"select\",\r\n          //   options: [],\r\n          //   otherOptions: {\r\n          //     clearable: true,\r\n          //   },\r\n          //   change: (e) => {},\r\n          // },\r\n          {\r\n            key: \"VehicleType\",\r\n            label: \"车辆类型\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {},\r\n          },\r\n        ],\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"新增\",\r\n              type: \"primary\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n            {\r\n              text: \"下载模板\",\r\n              onclick: (item) => {\r\n                this.ExportData(\r\n                  [],\r\n                  \"内部车辆管理模板\",\r\n                  VehiclesDownloadTemplate\r\n                );\r\n              },\r\n            },\r\n            {\r\n              text: \"批量导入\",\r\n              onclick: (item) => {\r\n                this.currentComponent = \"importDialog\";\r\n                this.dialogVisible = true;\r\n                this.dialogTitle = \"批量导入\";\r\n                this.componentsConfig = {\r\n                  interfaceName: VehiclesImportDataStream,\r\n                };\r\n              },\r\n            },\r\n\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                this.ExportData(\r\n                  this.ruleForm,\r\n                  \"内部车辆管理\",\r\n                  VehiclesExportData\r\n                );\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [20, 50, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: \"100%\",\r\n        tableActionsWidth: \"160\",\r\n        tableColumns: [\r\n          {\r\n            label: \"车牌号码\",\r\n            key: \"Number\",\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: \"车主姓名\",\r\n            key: \"UserName\",\r\n          },\r\n          {\r\n            label: \"车主联系方式\",\r\n            key: \"UserPhone\",\r\n          },\r\n          {\r\n            label: \"所属部�?,\r\n            key: \"DeptName\",\r\n          },\r\n\r\n          {\r\n            label: \"车辆类型\",\r\n            key: \"VehicleTypeName\",\r\n          },\r\n          {\r\n            label: \"更新�?,\r\n            key: \"ModifyUserName\",\r\n          },\r\n          {\r\n            label: \"更新时间\",\r\n            key: \"ModifyDate\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.$router.push({\r\n                name: \"InternalVehicleManagementView\",\r\n                query: { pg_redirect: this.$route.name, Id: row.Id },\r\n              });\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, \"edit\");\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/view\",\r\n          hidden: true,\r\n          component: () => import(\"./dialog/view.vue\"),\r\n          meta: { title: \"内部车辆管理详情\" },\r\n          name: \"InternalVehicleManagementView\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    // 测试\r\n    this.init();\r\n    //\r\n    this.getDictionaryDetailListByCode();\r\n\r\n    this.vehiclesGetDeptList();\r\n  },\r\n  methods: {\r\n    // 车辆类型\r\n    async getDictionaryDetailListByCode() {\r\n      await GetDictionaryDetailListByCode({\r\n        dictionaryCode: \"VehiclesType\",\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.customForm.formItems.find(\r\n            (item) => item.key == \"VehicleType\"\r\n          ).options = res.Data.map((item) => ({\r\n            label: item.Display_Name,\r\n            value: item.Value,\r\n          }));\r\n        } else {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: res.Message,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 部门人员change\r\n    departmentPersonnelHandleChange(values) {\r\n      console.log(values, \"values\");\r\n      this.ruleForm.DeptId = values[values.length - 1];\r\n      // let userInfo =\r\n      //   this.$refs[\"departmentPersonnelCascader\"].getCheckedNodes()[0].data;\r\n      // this.ruleForm.UserPhone = userInfo.UserPhone;\r\n      // this.ruleForm.UserDeptName = userInfo.UserDeptName;\r\n      // this.ruleForm.UserDept = userInfo.UserDeptId;\r\n      // this.ruleForm.UserId = userInfo.Id;\r\n      // this.ruleForm.UserName = userInfo.Name;\r\n    },\r\n\r\n    // 部门\r\n    async vehiclesGetDeptList() {\r\n      await VehiclesGetDeptList({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          console.log(res, \"res\");\r\n          this.departmentPersonnelOptions = this.setCascadeData([res.Data]);\r\n          // this.customForm.formItems.find(\r\n          //   (item) => item.key == \"DeptId\"\r\n          // ).options = res.Data.map((item) => ({\r\n          //   label: item.DeptName,\r\n          //   value: item.DeptId,\r\n          // }));\r\n        } else {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: res.Message,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 递归\r\n    setCascadeData(data) {\r\n      return data.map((item) => {\r\n        //   label: item.DeptName,\r\n        //   value: item.DeptId,\r\n        let newItem = { ...item, label: item.DeptName, value: item.DeptId };\r\n        if (newItem.Child && newItem.Child.length > 0) {\r\n          newItem.children = this.setCascadeData(newItem.Child);\r\n        } else {\r\n          delete newItem.Child;\r\n        }\r\n        return newItem;\r\n      });\r\n    },\r\n\r\n    // v2 版本导出\r\n    async handleAllExport() {\r\n      const res = await VehiclesExportData({\r\n        // Id: this.tableSelection.map((item) => item.Id).toString(),\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        downloadFile(res.Data, \"21\");\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.departmentPersonnelValue = [];\r\n      this.ruleForm.DeptId = \"\";\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n    },\r\n    async init() {\r\n      await this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      const res = await VehiclesGetVehicleList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.Total;\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.currentComponent = \"baseInfo\";\r\n      this.dialogTitle = \"新增\";\r\n      this.dialogVisible = true;\r\n      this.componentsConfig = {\r\n        type: \"add\",\r\n      };\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row);\r\n      console.log(this);\r\n      this.$confirm(\"确认删除?\", {\r\n        type: \"warning\",\r\n      })\r\n        .then(async (_) => {\r\n          const res = await VehiclesDelVehicle({\r\n            Id: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: \"删除成功\",\r\n              type: \"success\",\r\n            });\r\n            this.init();\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: \"error\",\r\n            });\r\n          }\r\n        })\r\n        .catch((_) => {\r\n          this.$message({\r\n            type: \"info\",\r\n            message: \"已取消删�?,\r\n          });\r\n        });\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type);\r\n      this.currentComponent = \"baseInfo\";\r\n      if (type === \"edit\") {\r\n        this.dialogTitle = \"编辑\";\r\n      }\r\n      this.componentsConfig = {\r\n        row,\r\n        type,\r\n      };\r\n      this.dialogVisible = true;\r\n    },\r\n    // 关闭弹窗\r\n    closedDialog() {\r\n      this.dialogVisible = false;\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.onFresh();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.onFresh();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      // const Ids = [];\r\n      this.tableSelection = selection;\r\n      // this.tableSelection.forEach((item) => {\r\n      //   Ids.push(item.Id);\r\n      // });\r\n      // this.selectIds = Ids;\r\n    },\r\n    handleview(row) {\r\n      this.dialogVisible = true;\r\n      this.PassImg = row.PassImg;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n\r\n.imgwapper {\r\n  width: 100px;\r\n  height: 100px;\r\n}\r\n.empty-img {\r\n  text-align: center;\r\n}\r\n</style>\r\n"]}]}