{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\szcjPJEquipmentAssetList\\index.vue?vue&type=template&id=695d9b4e&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\szcjPJEquipmentAssetList\\index.vue", "mtime": 1755674552420}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1724304688265}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}