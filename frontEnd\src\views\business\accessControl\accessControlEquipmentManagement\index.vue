<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import DialogForm from './dialogForm.vue'
import DeviceConnect from './deviceConnect.vue'
import { downloadFile } from '@/utils/downloadFile'
import getGridByCode from '../../safetyManagement/mixins/index'
import addRouterPage from '@/mixins/add-router-page'
import {
  GetEquipmentlList,
  DelEquipment,
  ExportEntrancePersonnel,
  SyncEquipment
} from '@/api/business/hazardousChemicals'
import { GetParkArea } from '@/api/business/energyManagement.js'

export default {
  name: '',
  components: {
    CustomTable,
    CustomForm,
    CustomLayout
  },
  mixins: [getGridByCode, addRouterPage],
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {
        Data: {}
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      tableSelection: [],
      ruleForm: {
        Entrance_Equipment_Name: '',
        Entrance_Equipment_Type: '',
        Position: '',
        Platform_Name: '',
        Status: ''
      },
      customForm: {
        formItems: [
          {
            key: 'Entrance_Equipment_Name',
            label: '设备名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Entrance_Equipment_Type',
            label: '设备类型',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Position',
            label: '安装位置',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Platform_Name',
            label: '平台名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          /*  {
             key: 'Position',
             label: '应用场景',
             type: 'input',
             otherOptions: {
               clearable: true
             },
             change: (e) => {
               console.log(e)
             }
           },
           {
             key: 'Position',
             label: '所属区域',
             type: 'input',
             otherOptions: {
               clearable: true
             },
             change: (e) => {
               console.log(e)
             }
           }, */
          {
            key: 'Status',
            label: '状态',
            type: 'select',
            otherOptions: {
              clearable: true
            },
            options: [
              { label: '在线', value: '在线' },
              { label: '离线', value: '离线' }
            ],
            change: (e) => {
              console.log(e)
            }
          }
        ],
        rules: {
        },
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              key: 'synchronous',
              text: '设备同步',
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: false, // 是否禁用
              icon: '', //  图标
              autofocus: false, // 是否聚焦
              type: 'primary', // primary / success / warning / danger / info / text
              size: 'small', // medium / small / mini
              onclick: (item) => {
                console.log(item)
                this.handelSyncEquipment()
              }
            },
            // {
            //   text: '新增',
            //   round: false, // 是否圆角
            //   plain: false, // 是否朴素
            //   circle: false, // 是否圆形
            //   loading: false, // 是否加载中
            //   disabled: true, // 是否禁用
            //   icon: '', //  图标
            //   autofocus: false, // 是否聚焦
            //   type: 'primary', // primary / success / warning / danger / info / text
            //   size: 'small', // medium / small / mini
            //   onclick: (item) => {
            //     console.log(item)
            //     this.handleCreate()
            //   }
            // },
            {
              text: '批量导出',
              onclick: (item) => {
                console.log(item)
                this.handleExport()
              }
            }
          ]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [],
        tableData: [],
        loading: false,
        operateOptions: {
          width: '240px',
          align: 'center'
        },
        tableActionsWidth: 180,
        tableActions: [
          {
            actionLabel: '查看',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'view')
            }
          },
          {
            actionLabel: '编辑',
            otherOptions: {
              type: 'text',
              disabled: false // 是否禁用
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'edit')
            }
          },
          {
            actionLabel: '删除',
            otherOptions: {
              type: 'text',
              disabled: false // 是否禁用
            },
            onclick: (index, row) => {
              this.handleDelete(index, row)
            }
          }
          // {
          //   actionLabel: '设备连接',
          //   otherOptions: {
          //     type: 'text',
          //     disabled: true // 是否禁用
          //   },
          //   onclick: (index, row) => {
          //     this.handleConent(row)
          //   }
          // }
          /* {
            actionLabel: '通行授权',
            otherOptions: {
              type: 'text',
              disabled: true, // 是否禁用
            },
            onclick: (index, row) => {
              this.handleConfig(row)
            }
          }, */
        ]
      },
      Park_Area: '',
      addPageArray: [
        {
          path: this.$route.path + '/add',
          hidden: true,
          component: () => import('./add.vue'),
          name: 'ConfigureAuthorizationList',
          meta: { title: `配置授权名单` }
        }
      ]
    }
  },
  async created() {
    this.init()
    this.customForm.formItems[1].options = await this.getDictionaryDetailListByCode()
  },
  methods: {
    searchForm(data) {
      this.customTableConfig.currentPage = 1
      console.log(data)
      this.onFresh()
    },
    resetForm() {
      this.onFresh()
    },
    onFresh() {
      this.getEquipmentList()
    },
    init() {
      this.getGridByCodePic('AccessControlEquipmentManagement', 'Allow_Pass_Visitors', false)
      GetParkArea().then(res => {
        this.Park_Area = res.Data
      })
      this.getEquipmentList()
    },
    async getEquipmentList() {
      this.customTableConfig.loading = true
      const res = await GetEquipmentlList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm
      }).finally(() => {
        this.customTableConfig.loading = false
      })
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data
        this.customTableConfig.total = res.Data.TotalCount
      }
    },
    handleCreate() {
      this.dialogTitle = '新增'
      this.dialogVisible = true
      this.currentComponent = DialogForm
      this.componentsConfig.Data = {
        Entrance_Equipment_Number: '',
        Entrance_Equipment_Name: '',
        Entrance_Equipment_Type: '',
        Allow_Pass_Visitors: false,
        Recognition_Way: [],
        Park_area: [],
        Address: '',
        Position: '',
        Platform_Name: '',
        Platform_Contact_Way: '',
        Engineer: '',
        Engineer_Contact_Way: '',
        Equipment_Purpose_Catetory: '',
        Park_Area: this.Park_Area
      }
    },
    // 同步设备
    handelSyncEquipment() {
      this.$confirm('此操作将进行设备同步, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'synchronous').loading = true
        SyncEquipment({}).then((res) => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '同步成功!'
            })
            this.getEquipmentList()
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        }).finally(() => {
          this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'synchronous').loading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消同步'
        })
      })
    },
    handleDelete(index, row) {
      console.log(index, row)
      console.log(this)
      this.$confirm('确认删除？', {
        type: 'warning'
      })
        .then(async (_) => {
          const res = await DelEquipment({
            id: row.Id
          })
          if (res.IsSucceed) {
            this.$message.success('操作成功')
            this.getEquipmentList()
          } else {
            this.$message.error(res.Message)
          }
        })
        .catch((_) => { })
    },
    handleEdit(index, row, type) {
      console.log(index, row, type)
      this.currentComponent = DialogForm
      let isShowBtn = true
      if (type === 'view') {
        this.dialogTitle = '查看'
        isShowBtn = false
      } else if (type === 'edit') {
        this.dialogTitle = '编辑'
        isShowBtn = true
      }
      this.dialogVisible = true
      const Park_area = (row.Scene ?? '') == '' ? [row.Purpose_Catetory] : (row.Site ?? '') == '' ? [row.Purpose_Catetory, row.Scene] : [row.Purpose_Catetory, row.Scene, row.Site]
      // row.Allow_Pass_Visitors = row.Allow_Pass_Visitors.toString() == 'false' ? '不允许' : '允许'
      row.Pass_Visitors = row.Allow_Pass_Visitors.toString() == 'false' ? '不允许' : '允许'
      this.componentsConfig.Data = { ...row, Recognition_Way: (row.Recognition_Way ?? '').split(','), Park_Area: this.Park_Area, Park_area, isShowBtn }
    },
    async handleExport() {
      if (this.tableSelection.length == 0) {
        this.$message.warning('请选择数据在导出')
        return
      }
      const res = await ExportEntrancePersonnel({
        id: this.tableSelection.map((item) => item.Id).toString(),
        code: 'AccessControlEquipmentManagement'
      })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '门禁设备管理数据')
      } else {
        this.$message(res.Message)
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.getEquipmentList()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.getEquipmentList()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
    },
    handleConent(row) {
      this.dialogVisible = true
      this.dialogTitle = '确认设备连接'
      this.currentComponent = DeviceConnect
      this.componentsConfig.Data = { ...row }
    },
    handleConfig(row) {
      this.$router.push({ name: 'ConfigureAuthorizationList', query: { pg_redirect: this.$route.name, id: row.Id } })
    }
  }
}
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
.layout{
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
