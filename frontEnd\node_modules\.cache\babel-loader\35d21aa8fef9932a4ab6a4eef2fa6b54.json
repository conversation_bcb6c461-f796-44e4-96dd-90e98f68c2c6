{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\environmentalManagement\\monitorData\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\environmentalManagement\\monitorData\\index.vue", "mtime": 1755674552418}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "DialogFormLook", "downloadFile", "GetDataList", "ExportDataList", "GetEnviromentDTCList", "ExportEnvironmentDataList", "GetDictionaryDetailListByCode", "deviceTypeMixins", "otherMixin", "dayjs", "name", "components", "mixins", "data", "_this", "currentComponent", "componentsConfig", "componentsFuns", "open", "dialogVisible", "close", "onFresh", "dialogTitle", "tableSelection", "ruleForm", "Content", "EqtType", "Position", "DataType", "customForm", "formItems", "key", "label", "type", "otherOptions", "clearable", "placeholder", "width", "change", "e", "console", "log", "options", "Id", "deceiveTypeList", "find", "item", "value", "getDTCList", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "text", "onclick", "handleAllExport", "loading", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "tableData", "operateOptions", "tableActions", "actionLabel", "index", "row", "handleEdit", "computed", "mounted", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "init", "getDictionaryDetailListByCode", "sent", "stop", "methods", "searchForm", "resetForm", "_this3", "_callee2", "_callee2$", "_context2", "_objectSpread", "Parameter<PERSON>son", "Key", "Value", "Type", "Filter_Type", "Page", "PageSize", "SortName", "SortOrder", "Search", "IsAll", "then", "res", "IsSucceed", "Data", "length", "map", "Time", "format", "align", "fixed", "TotalCount", "$message", "error", "Message", "_arguments", "arguments", "_callee3", "dictionaryCode", "_callee3$", "_context3", "undefined", "push", "Display_Name", "abrupt", "ID", "disabled", "title", "_this4", "_callee4", "_callee4$", "_context4", "toString", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "selection"], "sources": ["src/views/business/environmentalManagement/monitorData/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport DialogFormLook from './dialogFormLook.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport {\r\n  GetDataList,\r\n  ExportDataList,\r\n  GetEnviromentDTCList,\r\n  ExportEnvironmentDataList\r\n} from '@/api/business/environmentalManagement'\r\nimport { GetDictionaryDetailListByCode } from '@/api/sys'\r\nimport { deviceTypeMixins } from '../../mixins/deviceType.js'\r\nimport otherMixin from '../../mixins/index.js'\r\n\r\n// import * as moment from 'moment'\r\nimport dayjs from 'dayjs'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  mixins: [deviceTypeMixins, otherMixin],\r\n  data() {\r\n    return {\r\n      currentComponent: DialogFormLook,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        DataType: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Content', // 字段ID\r\n            label: '', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '输入设备编号或名称进行搜索'\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EqtType',\r\n            label: '设备类型',\r\n            type: 'select',\r\n\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择设备类型'\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              this.ruleForm.DataType = ''\r\n              const Id = this.deceiveTypeList.find((item) => item.value === e).Id\r\n              this.getDTCList(GetEnviromentDTCList, Id, 'DataType', 'DataId')\r\n            }\r\n          },\r\n          {\r\n            key: 'DataType',\r\n            label: '数据类型',\r\n            type: 'select',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择设备类型'\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position', // 字段ID\r\n            label: '安装位置', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请输入安装位置'\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            // {\r\n            //   text: '新增',\r\n            //   round: false, // 是否圆角\r\n            //   plain: false, // 是否朴素\r\n            //   circle: false, // 是否圆形\r\n            //   loading: false, // 是否加载中\r\n            //   disabled: false, // 是否禁用\r\n            //   icon: '', //  图标\r\n            //   autofocus: false, // 是否聚焦\r\n            //   type: 'primary', // primary / success / warning / danger / info / text\r\n            //   size: 'small', // medium / small / mini\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleCreate()\r\n            //   }\r\n            // },\r\n            // {\r\n            //   text: '导出',\r\n            //   key: 'batch',\r\n            //   disabled: true,\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleExport()\r\n            //   }\r\n            // },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleAllExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          // {\r\n          //   otherOptions: {\r\n          //     width: 20,\r\n          //     type: \"selection\",\r\n          //     align: \"center\",\r\n          //   },\r\n          // },\r\n          // {\r\n          //   width: 60,\r\n          //   label: '序号',\r\n          //   otherOptions: {\r\n          //     type: 'index',\r\n          //     align: 'center'\r\n          //   }\r\n          // }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 200\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          }\r\n          // {\r\n          //   actionLabel: '编辑',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleEdit(index, row, 'edit')\r\n          //   }\r\n          // },\r\n          // {\r\n          //   actionLabel: '删除',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleDelete(index, row)\r\n          //   }\r\n          // }\r\n        ]\r\n      },\r\n      deceiveTypeList: []\r\n    }\r\n  },\r\n  computed: {},\r\n  async mounted() {\r\n    this.init()\r\n    this.deceiveTypeList = this.customForm.formItems.find((item) => item.key === 'EqtType').options = await this.getDictionaryDetailListByCode('EnvironmentEqtType', 'Value')\r\n    // this.initDeviceType(\"EqtType\", \"EnvironmentEqtType\");\r\n    this.getDTCList(GetEnviromentDTCList, '', 'DataType', 'DataId')\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.getDTCList(GetEnviromentDTCList, '', 'DataType', 'DataId')\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.GetDataList()\r\n    },\r\n    init() {\r\n      this.GetDataList()\r\n    },\r\n    async GetDataList() {\r\n      this.customTableConfig.loading = true\r\n      await GetDataList({\r\n        ParameterJson: [\r\n          {\r\n            Key: '',\r\n            Value: [null],\r\n            Type: '',\r\n            Filter_Type: ''\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        SortName: '',\r\n        SortOrder: '',\r\n        Search: '',\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        ...this.ruleForm\r\n      }).then((res) => {\r\n        this.customTableConfig.loading = false\r\n        if (res.IsSucceed) {\r\n          if (res.Data.Data && res.Data.Data.length > 0) {\r\n            this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n              ...item,\r\n              Time: dayjs(item.Time).format('YYYY-MM-DD HH:mm:ss')\r\n            }))\r\n          } else {\r\n            this.customTableConfig.tableData = []\r\n          }\r\n          /* this.customTableConfig.tableColumns = [].concat(\r\n            [\r\n              // {\r\n              //   width: 50,\r\n              //   otherOptions: {\r\n              //     type: 'selection',\r\n              //     align: 'center'\r\n              //   }\r\n              // },\r\n              // {\r\n              //   width: 60,\r\n              //   label: '序号',\r\n              //   otherOptions: {\r\n              //     type: 'index',\r\n              //     align: 'center'\r\n              //   }\r\n              // }\r\n            ],\r\n            res.Data.Data.Headers\r\n          )\r\n          console.log(res.Data.Data.Headers, 'tableColumns')\r\n          console.log(this.customTableConfig.tableColumns, 'tableColumns')\r\n          */\r\n          /** 2023-09-18 erwin modify */\r\n          this.customTableConfig.tableColumns = [\r\n            {\r\n              otherOptions: {\r\n                width: 20,\r\n                type: 'selection',\r\n                align: 'center'\r\n              }\r\n            },\r\n            {\r\n              width: 60,\r\n              label: '序号',\r\n              otherOptions: {\r\n                type: 'index',\r\n                align: 'center'\r\n              }\r\n            },\r\n            {\r\n              label: '设备编号',\r\n              key: 'EId',\r\n              otherOptions: {\r\n                fixed: 'left'\r\n              },\r\n            },\r\n            {\r\n              label: '设备名称',\r\n              key: 'Name',\r\n              otherOptions: {\r\n                fixed: 'left'\r\n              },\r\n            },\r\n            {\r\n              label: '设备类型',\r\n              key: 'EqtType'\r\n            },\r\n            {\r\n              label: '安装位置',\r\n              key: 'Position'\r\n            },\r\n            {\r\n              label: '数据更新时间',\r\n              key: 'Time'\r\n            },\r\n            {\r\n              label: '数据类型',\r\n              key: 'TypeDes'\r\n              // render: (row) => {\r\n              //   return `${row.TypeDes}(${row.Unit})`\r\n              // }\r\n            },\r\n            {\r\n              label: '数据参数',\r\n              key: 'Value'\r\n            },\r\n            {\r\n              label: '单位',\r\n              key: 'Unit'\r\n            }\r\n          ]\r\n          /** end  */\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    async getDictionaryDetailListByCode(dictionaryCode = 'deviceType', Value) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode\r\n      })\r\n      if (res.IsSucceed) {\r\n        const options = [{ label: '全部', value: '' }]\r\n        res.Data.map((item) => {\r\n          options.push({\r\n            label: item.Display_Name,\r\n            value: item[Value],\r\n            ...item\r\n          })\r\n        })\r\n        return options\r\n      }\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      this.dialogVisible = true\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.currentComponent = DialogFormLook\r\n        this.componentsConfig = {\r\n          ID: row.Id,\r\n          disabled: true,\r\n          title: '查看',\r\n          ...row\r\n        }\r\n      }\r\n      // else if (type === 'edit') {\r\n      //   this.dialogTitle = '编辑'\r\n      //   this.componentsConfig = {\r\n      //     ID: row.ID,\r\n      //     disabled: false,\r\n      //     title: '编辑'\r\n      //   }\r\n      // }\r\n    },\r\n    // async handleExport() {\r\n    //   console.log(this.ruleForm)\r\n    //   const res = await ExportDataList({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: false,\r\n    //     Ids: this.tableSelection.map((item) => item.Id),\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n    // async handleAllExport() {\r\n    //   const res = await ExportDataList({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: true,\r\n    //     Ids: [],\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n    // v2 版本导出\r\n    async handleAllExport() {\r\n      const res = await ExportEnvironmentDataList({\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        Id: this.tableSelection.map((item) => item.Id).toString(),\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n      // if (this.tableSelection.length > 0) {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find(\r\n      //     (v) => v.key == 'batch'\r\n      //   ).disabled = false\r\n      // } else {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find(\r\n      //     (v) => v.key == 'batch'\r\n      //   ).disabled = true\r\n      // }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AAEA,OAAAC,cAAA;AAEA,SAAAC,YAAA;AACA;AACA;;AAEA,SACAC,WAAA,IAAAA,YAAA,EACAC,cAAA,EACAC,oBAAA,EACAC,yBAAA,QACA;AACA,SAAAC,6BAAA;AACA,SAAAC,gBAAA;AACA,OAAAC,UAAA;;AAEA;AACA,OAAAC,KAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAb,WAAA,EAAAA,WAAA;IACA;IACA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAe,MAAA,GAAAL,gBAAA,EAAAC,UAAA;EACAK,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,gBAAA,EAAAf,cAAA;MACAgB,gBAAA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAJ,KAAA,CAAAK,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAN,KAAA,CAAAK,aAAA;UACAL,KAAA,CAAAO,OAAA;QACA;MACA;MACAF,aAAA;MACAG,WAAA;MACAC,cAAA;MAEAC,QAAA;QACAC,OAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAAA;;UAEAC,YAAA;YACA;YACAC,SAAA;YACAC,WAAA;UACA;UACAC,KAAA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAR,GAAA;UACAC,KAAA;UACAC,IAAA;UAEAC,YAAA;YACA;YACAC,SAAA;YACAC,WAAA;UACA;UACAM,OAAA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAzB,KAAA,CAAAU,QAAA,CAAAI,QAAA;YACA,IAAAe,EAAA,GAAA7B,KAAA,CAAA8B,eAAA,CAAAC,IAAA,WAAAC,IAAA;cAAA,OAAAA,IAAA,CAAAC,KAAA,KAAAR,CAAA;YAAA,GAAAI,EAAA;YACA7B,KAAA,CAAAkC,UAAA,CAAA5C,oBAAA,EAAAuC,EAAA;UACA;QACA,GACA;UACAZ,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACA;YACAC,SAAA;YACAC,WAAA;UACA;UACAM,OAAA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAR,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAAA;;UAEAC,YAAA;YACA;YACAC,SAAA;YACAC,WAAA;UACA;UACAE,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,EACA;QACAU,KAAA;UACA;QAAA,CACA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAAX,IAAA;cACAN,OAAA,CAAAC,GAAA,CAAAK,IAAA;cACAhC,KAAA,CAAA4C,eAAA;YACA;UACA;QAEA;QACA;QACAC,OAAA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QAAA,CACA;QACAC,SAAA;QACAC,cAAA;UACA7B,KAAA;QACA;QACA8B,YAAA,GACA;UACAC,WAAA;UACAlC,YAAA;YACAD,IAAA;UACA;UACAwB,OAAA,WAAAA,QAAAY,KAAA,EAAAC,GAAA;YACAxD,KAAA,CAAAyD,UAAA,CAAAF,KAAA,EAAAC,GAAA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAAA;MAEA;MACA1B,eAAA;IACA;EACA;EACA4B,QAAA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAT,MAAA,CAAAU,IAAA;YAAAH,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAW,6BAAA;UAAA;YAAAX,MAAA,CAAA9B,eAAA,GAAA8B,MAAA,CAAA7C,UAAA,CAAAC,SAAA,CAAAe,IAAA,WAAAC,IAAA;cAAA,OAAAA,IAAA,CAAAf,GAAA;YAAA,GAAAW,OAAA,GAAAuC,QAAA,CAAAK,IAAA;YACA;YACAZ,MAAA,CAAA1B,UAAA,CAAA5C,oBAAA;UAAA;UAAA;YAAA,OAAA6E,QAAA,CAAAM,IAAA;QAAA;MAAA,GAAAT,OAAA;IAAA;EACA;EACAU,OAAA;IACAC,UAAA,WAAAA,WAAA5E,IAAA;MACA,KAAAwC,iBAAA,CAAAQ,WAAA;MACA,KAAAxC,OAAA;IACA;IACAqE,SAAA,WAAAA,UAAA;MACA,KAAA1C,UAAA,CAAA5C,oBAAA;MACA,KAAAiB,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAAnB,WAAA;IACA;IACAkF,IAAA,WAAAA,KAAA;MACA,KAAAlF,WAAA;IACA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAyF,MAAA;MAAA,OAAAhB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAe,SAAA;QAAA,OAAAhB,mBAAA,GAAAG,IAAA,UAAAc,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAZ,IAAA,GAAAY,SAAA,CAAAX,IAAA;YAAA;cACAQ,MAAA,CAAAtC,iBAAA,CAAAM,OAAA;cAAAmC,SAAA,CAAAX,IAAA;cAAA,OACAjF,YAAA,CAAA6F,aAAA;gBACAC,aAAA,GACA;kBACAC,GAAA;kBACAC,KAAA;kBACAC,IAAA;kBACAC,WAAA;gBACA,EACA;gBACAC,IAAA,EAAAV,MAAA,CAAAtC,iBAAA,CAAAQ,WAAA;gBACAyC,QAAA,EAAAX,MAAA,CAAAtC,iBAAA,CAAAS,QAAA;gBACAyC,QAAA;gBACAC,SAAA;gBACAC,MAAA;gBACAhF,OAAA;gBACAC,OAAA;gBACAC,QAAA;gBACA+E,KAAA;cAAA,GACAf,MAAA,CAAAnE,QAAA,CACA,EAAAmF,IAAA,WAAAC,GAAA;gBACAjB,MAAA,CAAAtC,iBAAA,CAAAM,OAAA;gBACA,IAAAiD,GAAA,CAAAC,SAAA;kBACA,IAAAD,GAAA,CAAAE,IAAA,CAAAA,IAAA,IAAAF,GAAA,CAAAE,IAAA,CAAAA,IAAA,CAAAC,MAAA;oBACApB,MAAA,CAAAtC,iBAAA,CAAAY,SAAA,GAAA2C,GAAA,CAAAE,IAAA,CAAAA,IAAA,CAAAE,GAAA,WAAAlE,IAAA;sBAAA,OAAAiD,aAAA,CAAAA,aAAA,KACAjD,IAAA;wBACAmE,IAAA,EAAAxG,KAAA,CAAAqC,IAAA,CAAAmE,IAAA,EAAAC,MAAA;sBAAA;oBAAA,CACA;kBACA;oBACAvB,MAAA,CAAAtC,iBAAA,CAAAY,SAAA;kBACA;kBACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;kBACA;kBACA0B,MAAA,CAAAtC,iBAAA,CAAAW,YAAA,IACA;oBACA9B,YAAA;sBACAG,KAAA;sBACAJ,IAAA;sBACAkF,KAAA;oBACA;kBACA,GACA;oBACA9E,KAAA;oBACAL,KAAA;oBACAE,YAAA;sBACAD,IAAA;sBACAkF,KAAA;oBACA;kBACA,GACA;oBACAnF,KAAA;oBACAD,GAAA;oBACAG,YAAA;sBACAkF,KAAA;oBACA;kBACA,GACA;oBACApF,KAAA;oBACAD,GAAA;oBACAG,YAAA;sBACAkF,KAAA;oBACA;kBACA,GACA;oBACApF,KAAA;oBACAD,GAAA;kBACA,GACA;oBACAC,KAAA;oBACAD,GAAA;kBACA,GACA;oBACAC,KAAA;oBACAD,GAAA;kBACA,GACA;oBACAC,KAAA;oBACAD,GAAA;oBACA;oBACA;oBACA;kBACA,GACA;oBACAC,KAAA;oBACAD,GAAA;kBACA,GACA;oBACAC,KAAA;oBACAD,GAAA;kBACA,EACA;kBACA;kBACA4D,MAAA,CAAAtC,iBAAA,CAAAU,KAAA,GAAA6C,GAAA,CAAAE,IAAA,CAAAO,UAAA;gBACA;kBACA1B,MAAA,CAAA2B,QAAA,CAAAC,KAAA,CAAAX,GAAA,CAAAY,OAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA1B,SAAA,CAAAP,IAAA;UAAA;QAAA,GAAAK,QAAA;MAAA;IACA;IACAP,6BAAA,WAAAA,8BAAA;MAAA,IAAAoC,UAAA,GAAAC,SAAA;MAAA,OAAA/C,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA8C,SAAA;QAAA,IAAAC,cAAA,EAAA1B,KAAA,EAAAU,GAAA,EAAAlE,OAAA;QAAA,OAAAkC,mBAAA,GAAAG,IAAA,UAAA8C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5C,IAAA,GAAA4C,SAAA,CAAA3C,IAAA;YAAA;cAAAyC,cAAA,GAAAH,UAAA,CAAAV,MAAA,QAAAU,UAAA,QAAAM,SAAA,GAAAN,UAAA;cAAAvB,KAAA,GAAAuB,UAAA,CAAAV,MAAA,OAAAU,UAAA,MAAAM,SAAA;cAAAD,SAAA,CAAA3C,IAAA;cAAA,OACA7E,6BAAA;gBACAsH,cAAA,EAAAA;cACA;YAAA;cAFAhB,GAAA,GAAAkB,SAAA,CAAAxC,IAAA;cAAA,KAGAsB,GAAA,CAAAC,SAAA;gBAAAiB,SAAA,CAAA3C,IAAA;gBAAA;cAAA;cACAzC,OAAA;gBAAAV,KAAA;gBAAAe,KAAA;cAAA;cACA6D,GAAA,CAAAE,IAAA,CAAAE,GAAA,WAAAlE,IAAA;gBACAJ,OAAA,CAAAsF,IAAA,CAAAjC,aAAA;kBACA/D,KAAA,EAAAc,IAAA,CAAAmF,YAAA;kBACAlF,KAAA,EAAAD,IAAA,CAAAoD,KAAA;gBAAA,GACApD,IAAA,CACA;cACA;cAAA,OAAAgF,SAAA,CAAAI,MAAA,WACAxF,OAAA;YAAA;YAAA;cAAA,OAAAoF,SAAA,CAAAvC,IAAA;UAAA;QAAA,GAAAoC,QAAA;MAAA;IAEA;IACApD,UAAA,WAAAA,WAAAF,KAAA,EAAAC,GAAA,EAAArC,IAAA;MACAO,OAAA,CAAAC,GAAA,CAAA4B,KAAA,EAAAC,GAAA,EAAArC,IAAA;MACA,KAAAd,aAAA;MACA,IAAAc,IAAA;QACA,KAAAX,WAAA;QACA,KAAAP,gBAAA,GAAAf,cAAA;QACA,KAAAgB,gBAAA,GAAA+E,aAAA;UACAoC,EAAA,EAAA7D,GAAA,CAAA3B,EAAA;UACAyF,QAAA;UACAC,KAAA;QAAA,GACA/D,GAAA,CACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAZ,eAAA,WAAAA,gBAAA;MAAA,IAAA4E,MAAA;MAAA,OAAA3D,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA0D,SAAA;QAAA,IAAA3B,GAAA;QAAA,OAAAhC,mBAAA,GAAAG,IAAA,UAAAyD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvD,IAAA,GAAAuD,SAAA,CAAAtD,IAAA;YAAA;cAAAsD,SAAA,CAAAtD,IAAA;cAAA,OACA9E,yBAAA,CAAA0F,aAAA;gBACAtE,OAAA;gBACAC,OAAA;gBACAC,QAAA;gBACA+E,KAAA;gBACA/D,EAAA,EAAA2F,MAAA,CAAA/G,cAAA,CAAAyF,GAAA,WAAAlE,IAAA;kBAAA,OAAAA,IAAA,CAAAH,EAAA;gBAAA,GAAA+F,QAAA;cAAA,GACAJ,MAAA,CAAA9G,QAAA,CACA;YAAA;cAPAoF,GAAA,GAAA6B,SAAA,CAAAnD,IAAA;cAQA,IAAAsB,GAAA,CAAAC,SAAA;gBACArE,OAAA,CAAAC,GAAA,CAAAmE,GAAA;gBACA3G,YAAA,CAAA2G,GAAA,CAAAE,IAAA;cACA;gBACAwB,MAAA,CAAAhB,QAAA,CAAAC,KAAA,CAAAX,GAAA,CAAAY,OAAA;cACA;YAAA;YAAA;cAAA,OAAAiB,SAAA,CAAAlD,IAAA;UAAA;QAAA,GAAAgD,QAAA;MAAA;IACA;IACAI,gBAAA,WAAAA,iBAAAC,GAAA;MACApG,OAAA,CAAAC,GAAA,iBAAAoG,MAAA,CAAAD,GAAA;MACA,KAAAvF,iBAAA,CAAAS,QAAA,GAAA8E,GAAA;MACA,KAAAxD,IAAA;IACA;IACA0D,mBAAA,WAAAA,oBAAAF,GAAA;MACApG,OAAA,CAAAC,GAAA,wBAAAoG,MAAA,CAAAD,GAAA;MACA,KAAAvF,iBAAA,CAAAQ,WAAA,GAAA+E,GAAA;MACA,KAAAxD,IAAA;IACA;IACA2D,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAzH,cAAA,GAAAyH,SAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}