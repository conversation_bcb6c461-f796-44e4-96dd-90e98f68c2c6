{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\equipmentAnalysis\\index.vue?vue&type=style&index=0&id=b8ce1e8c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\equipmentAnalysis\\index.vue", "mtime": 1755674552419}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZXF1aXBtZW50QW5hbHlzaXMgew0KICAvLyBwYWRkaW5nOiAxMHB4IDE1cHg7DQogIG92ZXJmbG93LXk6IHNjcm9sbDsNCiAgLy8gaGVpZ2h0OiBjYWxjKDEwMHZoIC0gOTZweCk7DQogIC5kaWFsb2dDdXN0b21DbGFzcyB7DQogICAgLmZvcm1Cb3ggew0KICAgICAgaGVpZ2h0OiA1ODBweDsNCiAgICAgIHBhZGRpbmc6IDAgMTZweDsNCiAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyIHsNCiAgICAgICAgZGlzcGxheTogbm9uZTsNCiAgICAgIH0NCiAgICB9DQogICAgLmNvc3RvbVRpdGxlIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgY29sb3I6ICMzMzM7DQogICAgICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICAgICAgc3BhbiB7DQogICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICAgICAgd2lkdGg6IDJweDsNCiAgICAgICAgaGVpZ2h0OiAxNHB4Ow0KICAgICAgICBiYWNrZ3JvdW5kOiAjMDA5ZGZmOw0KICAgICAgICBtYXJnaW4tcmlnaHQ6IDZweDsNCiAgICAgIH0NCiAgICB9DQogICAgLmRpYWxvZ0J1dHRvbiB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsNCiAgICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZDBkM2RiOw0KICAgICAgcGFkZGluZy10b3A6IDE2cHg7DQogICAgfQ0KICB9DQogIC5oZWFkZXIgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgLy8gYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgaGVpZ2h0OiAyMnB4Ow0KICAgID4gc3BhbiB7DQogICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICB9DQogICAgLnJpZ2h0IHsNCiAgICAgIGZvbnQtZmFtaWx5OiBIZWx2ZXRpY2EsIEhlbHZldGljYTsNCiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgY29sb3I6ICMyOThjZmM7DQogICAgICBsaW5lLWhlaWdodDogMHB4Ow0KICAgICAgdGV4dC1hbGlnbjogbGVmdDsNCiAgICAgIGZvbnQtc3R5bGU6IG5vcm1hbDsNCiAgICAgIHRleHQtdHJhbnNmb3JtOiBub25lOw0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIH0NCg0KICAgIC51bml0IHsNCiAgICAgIGZvbnQtZmFtaWx5OiBNaWNyb3NvZnQgWWFIZWksIE1pY3Jvc29mdCBZYUhlaTsNCiAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICBjb2xvcjogIzk5OTk5OTsNCiAgICAgIGZvbnQtc3R5bGU6IG5vcm1hbDsNCiAgICAgIHRleHQtdHJhbnNmb3JtOiBub25lOw0KICAgIH0NCiAgfQ0KICAuY2hhcnRDYXJkQ29uZW50IHsNCiAgICBtYXJnaW4tdG9wOiAtMzBweDsNCiAgICAuY2hhcnRDYXJkSXRlbSB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsNCiAgICB9DQogIH0NCiAgLmNvbnRlbnQgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KDQogICAgLmxlZnQgew0KICAgICAgd2lkdGg6IDUwJTsNCiAgICAgIC50aXRsZSB7DQogICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgfQ0KICAgIH0NCiAgICAucmlnaHQgew0KICAgICAgd2lkdGg6IDUwJTsNCiAgICAgIC50aXRsZSB7DQogICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC50YWJsZW51bWJlciB7DQogICAgd2lkdGg6IDMwcHg7DQogICAgaGVpZ2h0OiAyM3B4Ow0KICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJTsNCiAgICBiYWNrZ3JvdW5kLXJlcGVhdDogbm8tcmVwZWF0Ow0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICA+IHNwYW4gew0KICAgICAgbWFyZ2luLXRvcDogMTBweDsNCiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgfQ0KICB9DQoNCiAgOjp2LWRlZXAgLnBvcG92ZXJfbGF0ZXN0QWxhcm1JbmZvcm1hdGlvbiB7DQogICAgLml0ZW0gew0KICAgICAgcGFkZGluZzogNHB4IDBweDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBzcGFuOmZpcnN0LW9mLXR5cGUgew0KICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgIGNvbG9yOiAjOTk5OTk5Ow0KICAgICAgICB3aWR0aDogNzRweDsNCiAgICAgIH0NCiAgICAgIHNwYW46bGFzdC1vZi10eXBlIHsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICA6OnYtZGVlcCAuZWwtY2FyZF9faGVhZGVyIHsNCiAgICBib3JkZXItYm90dG9tOiBub25lICFpbXBvcnRhbnQ7DQogIH0NCiAgOjp2LWRlZXAgLmVsLXByb2dyZXNzX190ZXh0IHsNCiAgICBmb250LXNpemU6IDE4cHggIWltcG9ydGFudDsNCiAgICBjb2xvcjogIzY2NjY2NiAhaW1wb3J0YW50Ow0KICAgIC8vIGZvbnQtd2VpZ2h0OiBib2xkOw0KICB9DQogIDo6di1kZWVwLmVsLXRhYmxlIC5yb3ctb25lIHsNCiAgICBiYWNrZ3JvdW5kOiByZ2JhKDQxLCAxNDEsIDI1NSwgMC4wMykgIWltcG9ydGFudDsNCiAgfQ0KDQogIDo6di1kZWVwIC5lbC10YWJsZSAucm93LXR3byB7DQogICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAxKSAhaW1wb3J0YW50Ow0KICB9DQoNCiAgOjp2LWRlZXAgLmVsLXRhYmxlIC5yb3ctaGVhZGVyIHsNCiAgICBjb2xvcjogIzMzMzMzMyAhaW1wb3J0YW50Ow0KICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogIH0NCiAgOjp2LWRlZXAgLmVsLXRhYmxlIC5yb3ctYm9keSB7DQogICAgY29sb3I6ICMzMzMzMzMgIWltcG9ydGFudDsNCiAgICBib3JkZXI6IG5vbmUgIWltcG9ydGFudDsNCiAgfQ0KICA6OnYtZGVlcCAuZWwtY2FyZCB7DQogICAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7DQogIH0NCiAgOjp2LWRlZXAgLmVsLXRhYmxlOjpiZWZvcmUgew0KICAgIGJhY2tncm91bmQ6IG5vbmUgIWltcG9ydGFudDsNCiAgfQ0KICA6OnYtZGVlcCAuZWwtdGFibGUgdGguaXMtbGVhZiB7DQogICAgYm9yZGVyLWJvdHRvbTogbm9uZSAhaW1wb3J0YW50Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2hCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/equipmentManagement/equipmentAnalysis", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 equipmentAnalysis\">\r\n    <el-row :gutter=\"12\">\r\n      <el-col :span=\"24\">\r\n        <equipmentOperationStatus\r\n          v-loading=\"deviceStatusListLoading\"\r\n          :component-data=\"{\r\n            deviceStatusList: deviceStatusList,\r\n          }\"\r\n        />\r\n        <!-- <el-card shadow=\"never\" v-loading=\"deviceStatusListLoading\">\r\n          <equipmentOperationStatus\r\n            :componentData=\"{\r\n              deviceStatusList: deviceStatusList,\r\n            }\"\r\n          ></equipmentOperationStatus>\r\n        </el-card> -->\r\n      </el-col>\r\n\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"24\">\r\n        <el-card v-loading=\"rgvDataListLoading\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>AGV电量实时监测</span>\r\n          </div>\r\n          <agvBatteryLevel\r\n            style=\"margin-top: -20px\"\r\n            :component-data=\"{\r\n              rgvDataList: rgvDataList,\r\n            }\"\r\n          />\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"12\">\r\n        <el-card\r\n          v-loading=\"equipmentAbnormalityRankingDataLoading\"\r\n          shadow=\"never\"\r\n        >\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>设备异常情况排行</span>\r\n          </div>\r\n          <div style=\"margin-top: -20px\">\r\n            <el-table\r\n              :data=\"equipmentAbnormalityRankingData\"\r\n              style=\"width: 100%\"\r\n              height=\"240\"\r\n              :highlight-current-row=\"false\"\r\n              :row-class-name=\"rowClassName\"\r\n              :header-row-class-name=\"HeaderRowClassName\"\r\n              :cell-class-name=\"cellClassName\"\r\n            >\r\n              <el-table-column label=\"排名\" width=\"60\">\r\n                <template slot-scope=\"scope\">\r\n                  <div\r\n                    v-if=\"scope.$index < 3\"\r\n                    class=\"tablenumber\"\r\n                    :style=\"{\r\n                      backgroundImage:\r\n                        'url(' +\r\n                        require(`@/assets/no_${scope.$index + 1}.png`) +\r\n                        ')',\r\n                    }\"\r\n                  >\r\n                    <span> {{ scope.$index + 1 }}</span>\r\n                  </div>\r\n                  <div v-else class=\"tablenumber\">\r\n                    <span> {{ scope.$index + 1 }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Name\" label=\"设备名称\" />\r\n              <el-table-column prop=\"Area\" label=\"所属车间\" />\r\n              <el-table-column\r\n                prop=\"ErrorTime\"\r\n                label=\"设备异常时间\"\r\n                width=\"140\"\r\n              />\r\n              <el-table-column\r\n                prop=\"StartTime\"\r\n                label=\"设备开机时间\"\r\n                width=\"140\"\r\n              />\r\n              <el-table-column prop=\"ErrPercent\" label=\"异常率\" width=\"100\" />\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-card v-loading=\"latestAlarmInformationDataLoading\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>最新告警消息</span>\r\n          </div>\r\n          <div style=\"margin-top: -20px\">\r\n            <el-table\r\n              :data=\"latestAlarmInformationData\"\r\n              style=\"width: 100%\"\r\n              height=\"240\"\r\n              :show-header=\"true\"\r\n              :highlight-current-row=\"false\"\r\n              :row-class-name=\"rowClassName\"\r\n              :header-row-class-name=\"HeaderRowClassName\"\r\n              :cell-class-name=\"cellClassName\"\r\n            >\r\n              <el-table-column prop=\"Time\" label=\"告警时间\" width=\"160\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.Time || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Name\" label=\"告警设备\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.Name || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"StatusDes\" label=\"设备状态\" width=\"90\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.StatusDes || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Brand\" label=\"设备品牌\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.Brand || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <!-- <el-table-column prop=\"Content\" label=\"异常信息\" width=\"90\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.Content || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column> -->\r\n              <el-table-column label=\"\" width=\"40\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-popover placement=\"bottom-end\" width=\"\" trigger=\"hover\">\r\n                    <div class=\"popover_latestAlarmInformation\">\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >告警时间</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.Time || \"-\" }}</span>\r\n                      </div>\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >告警设备</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.Name || \"-\" }}</span>\r\n                      </div>\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >设备状态</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.StatusDes || \"-\" }}</span>\r\n                      </div>\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >设备品牌</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.Brand || \"-\" }}</span>\r\n                      </div>\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >异常信息</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.Content || \"-\" }}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div\r\n                      slot=\"reference\"\r\n                      style=\"\r\n                        height: 100%;\r\n                        display: flex;\r\n                        justify-content: center;\r\n                      \"\r\n                    >\r\n                      <i class=\"el-icon-arrow-right\" />\r\n                    </div>\r\n                  </el-popover>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"18\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"12\">\r\n            <el-card v-loading=\"productionVolumeTrendLoading\" shadow=\"never\">\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>能效（电）分析</span>\r\n                <span\r\n                  class=\"right\"\r\n                  @click=\"productionVolumeTrendClick\"\r\n                >更多<i class=\"el-icon-arrow-right\" /></span>\r\n              </div>\r\n              <div class=\"chartCardConent\">\r\n                <div class=\"chartCardItem\">\r\n                  <el-select\r\n                    v-model=\"productionVolumeTrendValue\"\r\n                    :clearable=\"true\"\r\n                    placeholder=\"请选择\"\r\n                    @change=\"productionVolumeTrendChange\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in productionVolumeTrendSelectOptions\"\r\n                      :key=\"item.value\"\r\n                      :label=\"item.label\"\r\n                      :value=\"item.value\"\r\n                    />\r\n                  </el-select>\r\n                </div>\r\n                <efficiencyAnalysis\r\n                  :component-data=\"{\r\n                    productionVolumeTrendOptions: productionVolumeTrendOptions,\r\n                  }\"\r\n                />\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card v-loading=\"equipmentFailureTrendLoading\" shadow=\"never\">\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>设备故障</span>\r\n                <span\r\n                  class=\"right\"\r\n                  @click=\"equipmentFailureTrendClick\"\r\n                >更多<i class=\"el-icon-arrow-right\" /></span>\r\n              </div>\r\n              <div class=\"chartCardConent\">\r\n                <div class=\"chartCardItem\">\r\n                  <el-select\r\n                    v-model=\"equipmentFailureTrendValue\"\r\n                    placeholder=\"请选择\"\r\n                    :clearable=\"true\"\r\n                    @change=\"equipmentFailureTrendChange\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in equipmentFailureTrendSelectOptions\"\r\n                      :key=\"item.Id\"\r\n                      :label=\"item.Display_Name\"\r\n                      :value=\"item.Id\"\r\n                    />\r\n                  </el-select>\r\n                </div>\r\n                <equipmentFailure\r\n                  :component-data=\"{\r\n                    equipmentFailureTrendOptions: equipmentFailureTrendOptions,\r\n                  }\"\r\n                />\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"12\">\r\n            <el-card\r\n              v-loading=\"rankProdEquipmentRateMonthLoading\"\r\n              shadow=\"never\"\r\n            >\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>本月生产设备负载率排行\r\n                  <el-tooltip\r\n                    class=\"item\"\r\n                    content=\"负载率=设备运行时间/设备开机时间\"\r\n                    placement=\"top-start\"\r\n                  >\r\n                    <img src=\"@/assets/tooltip.png\" alt=\"\">\r\n                  </el-tooltip>\r\n                </span>\r\n                <span\r\n                  class=\"unit\"\r\n                >单位：{{ rankProdEquipmentRateMonth.Unit }}</span>\r\n              </div>\r\n              <customProcess\r\n                :component-data=\"{ list: rankProdEquipmentRateMonth.Charts }\"\r\n              />\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card\r\n              v-loading=\"\r\n                monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading\r\n              \"\r\n              shadow=\"never\"\r\n            >\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>本月焊丝使用效率\r\n                  <el-tooltip\r\n                    class=\"item\"\r\n                    content=\"负载率=设备运行时间/设备开机时间\"\r\n                    placement=\"top-start\"\r\n                  >\r\n                    <template #content>\r\n                      <p>1.使用效率=使用量/运行时间（小时）</p>\r\n                      <p>2.使用量单位标识： -焊剂KG -焊丝KG -导电嘴（个）</p>\r\n                    </template>\r\n                    <img src=\"@/assets/tooltip.png\" alt=\"\">\r\n                  </el-tooltip>\r\n                </span>\r\n                <span\r\n                  class=\"unit\"\r\n                >单位：{{\r\n                  monthRankWeldingEquipmentConsumablesUsageEfficiency.Unit\r\n                }}</span>\r\n              </div>\r\n              <customProcess\r\n                :component-data=\"{\r\n                  list: monthRankWeldingEquipmentConsumablesUsageEfficiency.Charts,\r\n                }\"\r\n              />\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"12\">\r\n            <el-row :gutter=\"12\">\r\n              <el-col :span=\"8\">\r\n                <el-card\r\n                  v-loading=\"maintenanceWorkOrderProcessingStatusLoading\"\r\n                  shadow=\"never\"\r\n                >\r\n                  <div slot=\"header\" class=\"header\">\r\n                    <span>维修工单</span>\r\n                  </div>\r\n                  <maintenanceWorkOrder\r\n                    :component-data=\"{\r\n                      data: maintenanceWorkOrderProcessingStatusOne,\r\n                    }\"\r\n                  />\r\n                </el-card>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-card\r\n                  v-loading=\"maintenanceWorkOrderProcessingStatusLoading\"\r\n                  shadow=\"never\"\r\n                >\r\n                  <div slot=\"header\" class=\"header\">\r\n                    <span>维保工单</span>\r\n                  </div>\r\n                  <maintenanceWorkOrder\r\n                    :component-data=\"{\r\n                      data: maintenanceWorkOrderProcessingStatusTwo,\r\n                    }\"\r\n                  />\r\n                </el-card>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-card v-loading=\"workOrderErrorTypeLoading\" shadow=\"never\">\r\n                  <div slot=\"header\" class=\"header\">\r\n                    <span>维修故障类型</span>\r\n                  </div>\r\n                  <repairFaultType\r\n                    :component-data=\"{ workOrderErrorTypeList }\"\r\n                  />\r\n                </el-card>\r\n              </el-col>\r\n            </el-row>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card\r\n              v-loading=\"equipmentMaintenanceIntegrityRateLoading\"\r\n              shadow=\"never\"\r\n            >\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>设备维修完好率</span>\r\n                <el-date-picker\r\n                  v-model=\"equipmentMaintenanceReadinessRateMonth\"\r\n                  size=\"small\"\r\n                  type=\"month\"\r\n                  placeholder=\"选择月\"\r\n                  :clearable=\"false\"\r\n                  :editable=\"false\"\r\n                  @change=\"equipmentMaintenanceReadinessRateMonthChange\"\r\n                />\r\n              </div>\r\n              <equipmentIntegrityRate\r\n                :component-data=\"{ data: equipmentMaintenanceIntegrityRateObj }\"\r\n              />\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-card v-loading=\"equipmentOperationListLoading\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>本月设备运行</span>\r\n            <el-select\r\n              v-model=\"equipmentOperationValue\"\r\n              :clearable=\"true\"\r\n              placeholder=\"请选择\"\r\n              @change=\"equipmentOperationChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in equipmentStartupStatusMonthOptions\"\r\n                :key=\"item.Id\"\r\n                :label=\"item.Display_Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n          <equipmentOperation\r\n            :component-data=\"{ data: equipmentOperationList }\"\r\n          />\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"currentComponentTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      custom-class=\"dialogCustomClass\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :component-data=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  GetStatusAnalyseEqt,\r\n  GetErrorRank,\r\n  GetTopEqtError,\r\n  GetStartAnalyseEqt,\r\n  GetLoadAnalyseEqt,\r\n  GetConsumptionAnalyseEqt,\r\n  GetDeviceAnalyseWorkOrderCount,\r\n  GetWorkOrderHandlingCount,\r\n  GetWorkOrderErrorTypeList,\r\n  GetDeviceServiceabilityRate,\r\n  GetAGVAnalyseEqt,\r\n  GetDictionaryDetailListByParentId,\r\n  GetProduceTrend,\r\n  GetDeviceStatusDetails,\r\n  GetStartAnalyseEqtDetails\r\n} from '@/api/business/eqptAsset'\r\n\r\nimport equipmentIntegrityRate from './components/equipmentIntegrityRate'\r\nimport equipmentOperation from './components/equipmentOperation'\r\nimport repairFaultType from './components/repairFaultType'\r\nimport customProcess from './components/customProcess'\r\nimport loadRateRanking from './components/loadRateRanking'\r\nimport maintenanceWorkOrder from './components/maintenanceWorkOrder'\r\nimport equipmentOperationStatus from './components/equipmentOperationStatus'\r\nimport agvBatteryLevel from './components/agvBatteryLevel'\r\nimport efficiencyAnalysis from './components/efficiencyAnalysis'\r\nimport equipmentFailure from './components/equipmentFailure'\r\n\r\nimport efficiencyAnalysisDetail from './components/efficiencyAnalysisDetail'\r\nimport equipmentFailureDetail from './components/equipmentFailureDetail'\r\nimport { GetEquipmentAssetPageListPJ } from '@/api/business/eqptAsset'\r\nimport dayjs from 'dayjs'\r\nimport VChart from 'vue-echarts'\r\nimport { use } from 'echarts/core'\r\nimport { CanvasRenderer } from 'echarts/renderers'\r\nimport { BarChart, LineChart, PieChart } from 'echarts/charts'\r\nimport {\r\n  GridComponent,\r\n  LegendComponent,\r\n  TooltipComponent,\r\n  TitleComponent,\r\n  DataZoomComponent\r\n} from 'echarts/components'\r\nuse([\r\n  CanvasRenderer,\r\n  BarChart,\r\n  LineChart,\r\n  PieChart,\r\n  DataZoomComponent,\r\n  GridComponent,\r\n  LegendComponent,\r\n  TitleComponent,\r\n  TooltipComponent\r\n])\r\nexport default {\r\n  name: 'EquipmentAnalysis',\r\n  components: {\r\n    VChart,\r\n    equipmentOperation,\r\n    equipmentIntegrityRate,\r\n    repairFaultType,\r\n    customProcess,\r\n    loadRateRanking,\r\n    maintenanceWorkOrder,\r\n    equipmentOperationStatus,\r\n    agvBatteryLevel,\r\n    efficiencyAnalysis,\r\n    equipmentFailure\r\n  },\r\n  mixins: [],\r\n  data() {\r\n    return {\r\n      currentComponent: null,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.fetchData()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      currentComponentTitle: '',\r\n      //\r\n      deviceStatusListLoading: false,\r\n      deviceStatusList: [],\r\n      // 设备运行状况\r\n      equipmentOperationStatus: [],\r\n      // RGV电量实时监测\r\n      rgvDataListLoading: false,\r\n      rgvDataList: [],\r\n      // 本月设备维修完好率\r\n      equipmentMaintenanceIntegrityRateLoading: false,\r\n      equipmentMaintenanceReadinessRateMonth: dayjs(new Date()).format(\r\n        'YYYY-MM'\r\n      ),\r\n      // 维修工单处理情况\r\n      maintenanceWorkOrderProcessingStatusLoading: false,\r\n      maintenanceWorkOrderProcessingStatusOne: [],\r\n      // 维保工单处理情况\r\n      maintenanceWorkOrderProcessingStatusTwo: [],\r\n      // 本月焊接设备耗材使用效率排行\r\n      monthRankWeldingEquipmentConsumablesUsageEfficiencyValue:\r\n        'WireConsumption',\r\n      // 本月焊丝使用效率\r\n      rankProdEquipmentRateMonthLoading: false,\r\n      rankProdEquipmentRateMonth: {},\r\n      // 本月焊接设备耗材使用效率排行\r\n      monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading: false,\r\n      monthRankWeldingEquipmentConsumablesUsageEfficiency: {},\r\n      // 设备异常情况排行\r\n      equipmentAbnormalityRankingDataLoading: false,\r\n      equipmentAbnormalityRankingData: [],\r\n      //\r\n      latestAlarmInformationDataLoading: false,\r\n      latestAlarmInformationData: [],\r\n      // 本月设备开机情况\r\n      equipmentStartupStatusMonthValue: '',\r\n      equipmentOperationValue: '',\r\n      equipmentStartupStatusMonthOptions: [],\r\n\r\n      // 能效（电）分析\r\n      productionVolumeTrendValue: '',\r\n      productionVolumeTrendLoading: false,\r\n      productionVolumeTrendSelectOptions: [\r\n        {\r\n          label: '全部',\r\n          value: ''\r\n        },\r\n        {\r\n          label: '一车间',\r\n          value: '一车间'\r\n        },\r\n        {\r\n          label: '二车间',\r\n          value: '二车间'\r\n        },\r\n        {\r\n          label: '配送中心',\r\n          value: '配送中心'\r\n        }\r\n      ],\r\n      productionVolumeTrendOptions: {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        legend: {\r\n          show: false\r\n          // top: \"0\",\r\n          // left: \"0\",\r\n          // itemWidth: 10,\r\n          // itemHeight: 5,\r\n          // icon: \"rect\",\r\n          // textStyle: {\r\n          //   fontSize: 12,\r\n          //   color: \"#999999\",\r\n          // },\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        yAxis: [\r\n          {\r\n            name: '用电量(kW·h)',\r\n            type: 'value',\r\n            position: 'left',\r\n            axisLabel: {\r\n              formatter: '{value}',\r\n              textStyle: {\r\n                color: '#298DFF' // 设置 y 轴标签文字颜色为红色\r\n              }\r\n            },\r\n            nameTextStyle: {\r\n              color: '#298DFF',\r\n              fontSize: '13px'\r\n            }\r\n            // logBase: 10,\r\n          },\r\n          {\r\n            name: '生产产量(t)',\r\n            type: 'value',\r\n            // min: 0,\r\n            // max: 25,\r\n            // interval: 5,\r\n            axisLabel: {\r\n              formatter: '{value}',\r\n              textStyle: {\r\n                color: '#FF902C' // 设置 y 轴标签文字颜色为红色\r\n              }\r\n            },\r\n            nameTextStyle: {\r\n              color: '#FF902C',\r\n              fontSize: '13px'\r\n            }\r\n          }\r\n        ],\r\n        color: ['#298DFF', '#FF902C'],\r\n        series: [\r\n          {\r\n            name: '用电量',\r\n            // symbol: \"none\",\r\n            data: [],\r\n            // icon: 'rect',\r\n            // yAxisIndex: 1,\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return `${value || 0}` + ' kW·h'\r\n              }\r\n            },\r\n            type: 'line'\r\n          },\r\n          {\r\n            name: '生产产量',\r\n            // symbol: \"none\",\r\n            data: [],\r\n            yAxisIndex: 1,\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return `${value || 0}` + ' t'\r\n              }\r\n            },\r\n            // icon: 'rect',\r\n            type: 'line'\r\n          }\r\n        ]\r\n      },\r\n      // 设备故障\r\n      equipmentFailureTrendValue: '',\r\n      equipmentFailureTrendSelectOptions: [],\r\n      equipmentFailureTrendLoading: false,\r\n      equipmentFailureTrendOptions: {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n\r\n        color: ['rgba(41, 141, 255, 1)'],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            position: 'left',\r\n            logBase: 10\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            smooth: true,\r\n            // symbol: \"none\",\r\n            data: [],\r\n            type: 'line',\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return `${value || 0}` + ' 个'\r\n              }\r\n            }\r\n            // areaStyle: {\r\n            //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n            //     {\r\n            //       offset: 0,\r\n            //       color: \"rgba(62, 204, 147, .5)\",\r\n            //     },\r\n            //     {\r\n            //       offset: 1,\r\n            //       color: \"rgba(57, 133, 238, 0)\",\r\n            //     },\r\n            //   ]),\r\n            // },\r\n          }\r\n        ]\r\n      },\r\n      // 本月设备维修完好率\r\n      equipmentMaintenanceIntegrityRateObj: {},\r\n      workOrderErrorTypeLoading: false,\r\n      workOrderErrorTypeList: [],\r\n      equipmentOperationListLoading: false,\r\n      equipmentOperationList: []\r\n    }\r\n  },\r\n  activated() {},\r\n  mounted() {\r\n    // 获取设备\r\n    this.getEquipmentAssetPageListPJ()\r\n    // 获取设备子类\r\n    this.getDictionaryDetailListByParentId()\r\n    // 获取设备数采分析设备运行状况\r\n    this.getStatusAnalyseEqt()\r\n    // 获取设备异常情况排行\r\n    this.getErrorRank()\r\n    // 获取设备数采异常信息\r\n    this.getTopEqtError()\r\n    // // 获取设备数采分析开机时间分析\r\n    // this.getStartAnalyseEqt();\r\n    // 获取设备数采分析负载率分析\r\n    this.getLoadAnalyseEqt()\r\n    // 获取本月耗材设备使用效率排行\r\n    this.getConsumptionAnalyseEqt()\r\n    // 获取设备故障\r\n    this.getDeviceAnalyseWorkOrderCount()\r\n    // 获取工单处理情况\r\n    this.getWorkOrderHandlingCount()\r\n    // 获取工单类型故障次数\r\n    this.getWorkOrderErrorTypeList()\r\n    // 获取设备完好率\r\n    this.getDeviceServiceabilityRate()\r\n    // 获取AGV运行状态\r\n    this.getAGVAnalyseEqt()\r\n    // 获取能效（电）分析\r\n    this.getProduceTrend()\r\n    // 获取设备状态\r\n    this.getDeviceStatusDetails()\r\n    // 获取设备运行\r\n    this.getStartAnalyseEqtDetails()\r\n  },\r\n  methods: {\r\n    // 打开 能效（电）分析\r\n    productionVolumeTrendClick() {\r\n      this.currentComponentTitle = '能效（电）分析'\r\n      this.dialogVisible = true\r\n      this.currentComponent = efficiencyAnalysisDetail\r\n      this.componentsConfig = {\r\n        selectOtherOptions: this.productionVolumeTrendSelectOptions\r\n      }\r\n    },\r\n    // 打开 设备故障\r\n    equipmentFailureTrendClick() {\r\n      this.currentComponentTitle = '设备故障'\r\n      this.dialogVisible = true\r\n      this.currentComponent = equipmentFailureDetail\r\n      this.componentsConfig = {\r\n        selectOtherOptions: this.equipmentFailureTrendSelectOptions\r\n      }\r\n    },\r\n    // 获取设备状态\r\n    async getDeviceStatusDetails() {\r\n      this.deviceStatusListLoading = false\r\n      const res = await GetDeviceStatusDetails({})\r\n      if (res.IsSucceed) {\r\n        this.deviceStatusList = res.Data\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n      this.deviceStatusListLoading = false\r\n    },\r\n    // 获取设备\r\n    async getEquipmentAssetPageListPJ() {\r\n      const res = await GetEquipmentAssetPageListPJ({\r\n        Display_Name: '',\r\n        Device_Type_Id: 'cc81fe8a-f0a3-40c1-abf6-f553fa502a33',\r\n        Device_Type_Detail_Id: '',\r\n        Department: '',\r\n        Page: 1,\r\n        PageSize: 100\r\n      })\r\n      this.equipmentFailureTrendSelectOptions = res.Data.Data.filter(\r\n        (item) => item.IsShow == 1\r\n      )\r\n      this.equipmentFailureTrendSelectOptions.unshift({\r\n        Display_Name: '全部',\r\n        Id: ''\r\n      })\r\n    },\r\n    // 获取设备子类\r\n    async getDictionaryDetailListByParentId() {\r\n      const res = await GetDictionaryDetailListByParentId(\r\n        'cc81fe8a-f0a3-40c1-abf6-f553fa502a33'\r\n      )\r\n      this.equipmentStartupStatusMonthOptions = res.Data\r\n      this.equipmentStartupStatusMonthOptions.unshift({\r\n        Display_Name: '全部',\r\n        Id: ''\r\n      })\r\n    },\r\n    // 能效（电）分析\r\n    async getProduceTrend() {\r\n      this.productionVolumeTrendLoading = false\r\n      const res = await GetProduceTrend({\r\n        NodeName: this.productionVolumeTrendValue\r\n      })\r\n\r\n      this.productionVolumeTrendOptions.xAxis.data = res.Data.map(\r\n        (item) => item.Key\r\n      )\r\n      this.productionVolumeTrendOptions.series[0].data = res.Data.map(\r\n        (item) => item.Electric\r\n      )\r\n      this.productionVolumeTrendOptions.series[1].data = res.Data.map(\r\n        (item) => item.Produce\r\n      )\r\n      this.productionVolumeTrendLoading = false\r\n    },\r\n    // 获取设备数采分析设备运行状况\r\n    async getStatusAnalyseEqt() {\r\n      const res = await GetStatusAnalyseEqt({})\r\n      this.equipmentOperationStatus = res.Data\r\n    },\r\n    // 获取设备异常情况排行\r\n    async getErrorRank() {\r\n      this.equipmentAbnormalityRankingDataLoading = true\r\n      const res = await GetErrorRank({})\r\n\r\n      this.equipmentAbnormalityRankingData = res.Data\r\n      this.equipmentAbnormalityRankingDataLoading = false\r\n    },\r\n    // 获取设备数采异常信息\r\n    async getTopEqtError() {\r\n      this.latestAlarmInformationDataLoading = true\r\n\r\n      const res = await GetTopEqtError({})\r\n      this.latestAlarmInformationData = res.Data.slice(0, 5)\r\n      this.latestAlarmInformationDataLoading = false\r\n    },\r\n    // 获取设备数采分析开机时间分析\r\n    // async getStartAnalyseEqt() {\r\n    //   let res = await GetStartAnalyseEqt({\r\n    //     ID: this.equipmentStartupStatusMonthValue,\r\n    //   });\r\n    // },\r\n    // 获取设备数采分析负载率分析\r\n    async getLoadAnalyseEqt() {\r\n      this.rankProdEquipmentRateMonthLoading = true\r\n      const res = await GetLoadAnalyseEqt({})\r\n      this.rankProdEquipmentRateMonth = res.Data\r\n      this.rankProdEquipmentRateMonthLoading = false\r\n    },\r\n    // 获取本月耗材设备使用效率排行\r\n    async getConsumptionAnalyseEqt() {\r\n      this.monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading = true\r\n      const res = await GetConsumptionAnalyseEqt({\r\n        Content: this.monthRankWeldingEquipmentConsumablesUsageEfficiencyValue\r\n      })\r\n      this.monthRankWeldingEquipmentConsumablesUsageEfficiency = res.Data\r\n      this.monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading = false\r\n    },\r\n    // 获取设备故障\r\n    async getDeviceAnalyseWorkOrderCount() {\r\n      this.equipmentFailureTrendLoading = true\r\n      const res = await GetDeviceAnalyseWorkOrderCount({\r\n        ID: this.equipmentFailureTrendValue,\r\n        Time: ''\r\n      })\r\n      this.equipmentFailureTrendOptions.xAxis.data = res.Data.map(\r\n        (item) => item.Label\r\n      )\r\n      this.equipmentFailureTrendOptions.series[0].data = res.Data.map(\r\n        (item) => item.Value\r\n      )\r\n      this.equipmentFailureTrendLoading = false\r\n    },\r\n\r\n    // 获取工单处理情况\r\n    async getWorkOrderHandlingCount() {\r\n      this.maintenanceWorkOrderProcessingStatusLoading = true\r\n      const res = await GetWorkOrderHandlingCount({})\r\n      this.maintenanceWorkOrderProcessingStatusOne = res.Data.Reconditions\r\n      this.maintenanceWorkOrderProcessingStatusTwo = res.Data.Maintenances\r\n      this.maintenanceWorkOrderProcessingStatusLoading = false\r\n    },\r\n\r\n    // 获取工单类型故障次数\r\n    async getWorkOrderErrorTypeList() {\r\n      this.workOrderErrorTypeLoading = true\r\n      const res = await GetWorkOrderErrorTypeList({})\r\n      this.workOrderErrorTypeList = res.Data.map((item) => ({\r\n        Value: item.Rate,\r\n        Label: item.Type\r\n      }))\r\n      this.workOrderErrorTypeLoading = false\r\n    },\r\n    // 获取设备完好率\r\n    async getDeviceServiceabilityRate() {\r\n      this.equipmentMaintenanceIntegrityRateLoading = true\r\n      const res = await GetDeviceServiceabilityRate({\r\n        Time: dayjs(this.equipmentMaintenanceReadinessRateMonth).format(\r\n          'YYYY-MM'\r\n        )\r\n      })\r\n      this.equipmentMaintenanceIntegrityRateObj = res.Data\r\n      this.equipmentMaintenanceIntegrityRateLoading = false\r\n    },\r\n\r\n    // 获取AGV运行状态\r\n    async getAGVAnalyseEqt() {\r\n      this.rgvDataListLoading = true\r\n      const res = await GetAGVAnalyseEqt({})\r\n      this.rgvDataList = res.Data\r\n      this.rgvDataListLoading = false\r\n    },\r\n    // 设置表格颜色\r\n    latestAlarmInformationDataClassName({ row, rowIndex }) {\r\n      if (this.isEvenOrOdd(rowIndex)) {\r\n        return 'row-one'\r\n      } else {\r\n        return 'row-two'\r\n      }\r\n    },\r\n\r\n    HeaderRowClassName({ row, rowIndex }) {\r\n      return 'row-header'\r\n    },\r\n    cellClassName({ row, rowIndex }) {\r\n      return 'row-body'\r\n    },\r\n\r\n    // 设置表格颜色\r\n    rowClassName({ row, rowIndex }) {\r\n      if (this.isEvenOrOdd(rowIndex + 1)) {\r\n        return 'row-one'\r\n      } else {\r\n        return 'row-two'\r\n      }\r\n    },\r\n\r\n    productionVolumeTrendChange() {\r\n      this.getProduceTrend()\r\n    },\r\n    equipmentFailureTrendChange() {\r\n      this.getDeviceAnalyseWorkOrderCount()\r\n    },\r\n    equipmentMaintenanceReadinessRateMonthChange() {\r\n      this.getDeviceServiceabilityRate()\r\n    },\r\n    handleClick(tab, event) {},\r\n    //  判断是否是偶数行 还是奇数行\r\n    isEvenOrOdd(number) {\r\n      if (number % 2 === 0) {\r\n        return true\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    // 设备运行\r\n    async getStartAnalyseEqtDetails() {\r\n      this.equipmentOperationListLoading = true\r\n      const res = await GetStartAnalyseEqtDetails({\r\n        ID: this.equipmentOperationValue\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.equipmentOperationList = res.Data\r\n      }\r\n      this.equipmentOperationListLoading = false\r\n    },\r\n    equipmentOperationChange() {\r\n      this.getStartAnalyseEqtDetails()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.equipmentAnalysis {\r\n  // padding: 10px 15px;\r\n  overflow-y: scroll;\r\n  // height: calc(100vh - 96px);\r\n  .dialogCustomClass {\r\n    .formBox {\r\n      height: 580px;\r\n      padding: 0 16px;\r\n      &::-webkit-scrollbar {\r\n        display: none;\r\n      }\r\n    }\r\n    .costomTitle {\r\n      display: flex;\r\n      align-items: center;\r\n      color: #333;\r\n      margin-bottom: 16px;\r\n      span {\r\n        display: inline-block;\r\n        width: 2px;\r\n        height: 14px;\r\n        background: #009dff;\r\n        margin-right: 6px;\r\n      }\r\n    }\r\n    .dialogButton {\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      border-top: 1px solid #d0d3db;\r\n      padding-top: 16px;\r\n    }\r\n  }\r\n  .header {\r\n    display: flex;\r\n    // align-items: center;\r\n    justify-content: space-between;\r\n    height: 22px;\r\n    > span {\r\n      font-weight: bold;\r\n    }\r\n    .right {\r\n      font-family: Helvetica, Helvetica;\r\n      font-weight: bold;\r\n      font-size: 14px;\r\n      color: #298cfc;\r\n      line-height: 0px;\r\n      text-align: left;\r\n      font-style: normal;\r\n      text-transform: none;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .unit {\r\n      font-family: Microsoft YaHei, Microsoft YaHei;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #999999;\r\n      font-style: normal;\r\n      text-transform: none;\r\n    }\r\n  }\r\n  .chartCardConent {\r\n    margin-top: -30px;\r\n    .chartCardItem {\r\n      display: flex;\r\n      justify-content: flex-end;\r\n    }\r\n  }\r\n  .content {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: row;\r\n\r\n    .left {\r\n      width: 50%;\r\n      .title {\r\n        font-weight: bold;\r\n      }\r\n    }\r\n    .right {\r\n      width: 50%;\r\n      .title {\r\n        font-weight: bold;\r\n      }\r\n    }\r\n  }\r\n\r\n  .tablenumber {\r\n    width: 30px;\r\n    height: 23px;\r\n    background-size: 100%;\r\n    background-repeat: no-repeat;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    > span {\r\n      margin-top: 10px;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  ::v-deep .popover_latestAlarmInformation {\r\n    .item {\r\n      padding: 4px 0px;\r\n      display: flex;\r\n      span:first-of-type {\r\n        font-weight: 500;\r\n        font-size: 14px;\r\n        color: #999999;\r\n        width: 74px;\r\n      }\r\n      span:last-of-type {\r\n        font-weight: 500;\r\n        font-size: 14px;\r\n        color: #333333;\r\n      }\r\n    }\r\n  }\r\n\r\n  ::v-deep .el-card__header {\r\n    border-bottom: none !important;\r\n  }\r\n  ::v-deep .el-progress__text {\r\n    font-size: 18px !important;\r\n    color: #666666 !important;\r\n    // font-weight: bold;\r\n  }\r\n  ::v-deep.el-table .row-one {\r\n    background: rgba(41, 141, 255, 0.03) !important;\r\n  }\r\n\r\n  ::v-deep .el-table .row-two {\r\n    background: rgba(255, 255, 255, 1) !important;\r\n  }\r\n\r\n  ::v-deep .el-table .row-header {\r\n    color: #333333 !important;\r\n    font-weight: 500;\r\n  }\r\n  ::v-deep .el-table .row-body {\r\n    color: #333333 !important;\r\n    border: none !important;\r\n  }\r\n  ::v-deep .el-card {\r\n    border: none !important;\r\n  }\r\n  ::v-deep .el-table::before {\r\n    background: none !important;\r\n  }\r\n  ::v-deep .el-table th.is-leaf {\r\n    border-bottom: none !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}