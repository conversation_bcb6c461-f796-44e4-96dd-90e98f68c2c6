{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\gas\\components\\gasUsed.vue?vue&type=template&id=058cdaf6&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\gas\\components\\gasUsed.vue", "mtime": 1754621285999}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1724304688265}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}