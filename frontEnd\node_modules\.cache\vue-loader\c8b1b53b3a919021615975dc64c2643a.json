{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\water\\components\\bar.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\water\\components\\bar.vue", "mtime": 1755743644481}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["bar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAmBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "bar.vue", "sourceRoot": "src/views/business/energyManagement/pJEnergyAnalysis/water/components", "sourcesContent": ["<template>\n  <div class=\"chart-container\">\n    <div class=\"title\">\n      <div class=\"count\">\n        共计用水<span style=\"color: #00d3a7\">{{ Total }}</span>吨\n      </div>\n    </div>\n    <div class=\"cs-chart\">\n      <v-chart\n        ref=\"barChartRef\"\n        class=\"barChartDom\"\n        :option=\"barOptionRef\"\n        :autoresize=\"true\"\n      />\n    </div>\n  </div>\n</template>\n\n<script>\nimport VChart from 'vue-echarts'\nimport { use } from 'echarts/core'\nimport { CanvasRenderer } from 'echarts/renderers'\nimport { BarChart } from 'echarts/charts'\nimport {\n  GridComponent,\n  LegendComponent,\n  TooltipComponent,\n  TitleComponent,\n  DataZoomComponent\n} from 'echarts/components'\nimport { GetWaterAnalyseStatistic } from '@/api/business/pJEnergyAnalysis'\nuse([\n  CanvasRenderer,\n  BarChart,\n  DataZoomComponent,\n  GridComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent\n])\nexport default {\n  components: {\n    VChart\n  },\n  data() {\n    return {\n      barOptionRef: {\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: [],\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: false\n          }\n        },\n        grid: {\n          left: '2%',\n          right: '0%',\n          bottom: '10%',\n          containLabel: true\n        },\n        yAxis: {\n          type: 'value',\n          nameTextStyle: {\n            color: '#888888'\n          }\n        },\n        series: [\n          {\n            name: '共计用水',\n            data: [],\n            type: 'bar',\n            symbol: 'emptyCircle',\n            tooltip: {\n              valueFormatter: function(value) {\n                return value + ' 吨'\n              }\n            },\n            barWidth: 10,\n            itemStyle: {\n              color: '#00D3A7'\n            }\n          }\n        ]\n      },\n      Total: 0\n    }\n  },\n  computed: {\n    parentData() {\n      return {\n        DateType: this.DateType(),\n        StartTime: this.StartTime(),\n        EndTime: this.EndTime(),\n        randomInteger: this.randomInteger()\n      }\n    }\n  },\n  watch: {\n    parentData: {\n      handler(nv, ov) {\n        console.log(12)\n        this.fetchData()\n      }\n    }\n  },\n  created() {\n    this.fetchData()\n  },\n  inject: ['DateType', 'StartTime', 'EndTime', 'randomInteger'],\n  methods: {\n    async fetchData() {\n      const res = await GetWaterAnalyseStatistic(this.parentData)\n      if (res.IsSucceed) {\n        this.Total = res.Data.Total\n        const xAxisData = (res.Data.List ?? []).map(item => item.Key)\n        const seriesData = (res.Data.List ?? []).map(item => item.Value)\n        this.barOptionRef.xAxis.data = xAxisData\n        this.barOptionRef.series[0].data = seriesData\n      }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.chart-container {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  padding: 16px 24px;\n  box-sizing: border-box;\n  background: #fff;\n  .title {\n    height: 40px;\n    display: flex;\n    align-items: center;\n    // justify-content: space-between;\n    padding-top: 16px;\n    .count {\n      font-weight: bold;\n      font-size: 16px;\n      color: #666666;\n      margin-right: 8px;\n      > span {\n        margin: 0 5px;\n      }\n    }\n    .lengend {\n      > span {\n        display: inline-block;\n        width: 10px;\n        height: 4px;\n        margin-right: 8px;\n      }\n      font-size: 12px;\n      color: #999999;\n      display: flex;\n      align-items: center;\n    }\n  }\n\n  .cs-chart {\n    flex: 1;\n\n    .no-data {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 26px;\n      width: 100%;\n      height: 100%;\n    }\n  }\n}\n</style>\n"]}]}