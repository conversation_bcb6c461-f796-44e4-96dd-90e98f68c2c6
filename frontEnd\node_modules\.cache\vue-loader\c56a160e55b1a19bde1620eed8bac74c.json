{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlAlarmDetails\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlAlarmDetails\\index.vue", "mtime": 1755506319075}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/accessControl/accessControlAlarmDetails", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        >\n          <template #customBtn=\"{ slotScope }\">\n            <el-button\n              v-if=\"slotScope.Handle_Status != 2\"\n              type=\"text\"\n              @click=\"handleChange(slotScope)\"\n            >关闭</el-button>\n          </template>\n        </CustomTable>\n      </template>\n    </CustomLayout>\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <component\n        :is=\"currentComponent\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n      /></el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\nimport getGridByCode from '../../safetyManagement/mixins/index'\nimport DialogForm from './dialogForm.vue'\n\nimport { downloadFile } from '@/utils/downloadFile'\nimport dayjs from 'dayjs'\nimport {\n  entranceWarningGetWarningList,\n  ExportEntranceWarning,\n  UpdateEntranceWarningStatus\n} from '@/api/business/hazardousChemicals'\nexport default {\n  name: '',\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout\n  },\n  mixins: [getGridByCode],\n  data() {\n    return {\n      currentComponent: DialogForm,\n      componentsConfig: {\n        Data: {}\n      },\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true\n        },\n        close: () => {\n          this.dialogVisible = false\n          this.onFresh()\n        }\n      },\n      dialogVisible: false,\n      dialogTitle: '查看',\n      tableSelection: [],\n      ruleForm: {\n        Warning_Name: '',\n        Warning_Type: '',\n        Warning_Information: ''\n      },\n      customForm: {\n        formItems: [\n          {\n            key: 'Warning_Name',\n            label: '告警事件名称',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'Warning_Type',\n            label: '告警事件类型',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              console.log(e)\n            }\n          },\n          {\n            key: 'Warning_Information',\n            label: '告警内容',\n            type: 'input',\n            otherOptions: {\n              clearable: true\n            },\n            change: (e) => {\n              // change事件\n              console.log(e)\n            }\n          },\n          {\n            key: 'Handle_Status',\n            label: '告警状态',\n            type: 'select',\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: '请选择告警状态'\n            },\n            options: [\n              {\n                label: '告警中',\n                value: 1\n              },\n              {\n                label: '已关闭',\n                value: 2\n              },\n              {\n                label: '已处理',\n                value: 3\n              }\n            ],\n            change: (e) => {\n              console.log(e)\n            }\n          }\n        ],\n        rules: {\n        },\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        buttonConfig: {\n          buttonList: [\n            {\n              text: '导出',\n              onclick: (item) => {\n                console.log(item)\n                this.handleExport()\n              }\n            }\n          ]\n        },\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        tableColumns: [],\n        tableData: [],\n        operateOptions: {\n          align: 'center'\n        },\n        tableActionsWidth: 120,\n        tableActions: [\n          // {\n          //   actionLabel: '关闭',\n          //   otherOptions: {\n          //     type: 'text'\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handleChange(row)\n          //   }\n          // },\n          {\n            actionLabel: '查看',\n            otherOptions: {\n              type: 'text'\n            },\n            onclick: (index, row) => {\n              this.handleEdit(row)\n            }\n          }\n        ]\n      }\n    }\n  },\n  computed: {},\n  created() {\n    this.init()\n  },\n  methods: {\n    searchForm(data) {\n      console.log(data)\n      this.customTableConfig.currentPage = 1\n      this.onFresh()\n    },\n    resetForm() {\n      this.onFresh()\n    },\n    onFresh() {\n      this.getEquipmentList()\n    },\n    init() {\n      this.getGridByCode('AccessControlAlarmDetails1')\n      this.getEquipmentList()\n    },\n    async getEquipmentList() {\n      const res = await entranceWarningGetWarningList({\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n        ...this.ruleForm\n      })\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data\n        this.customTableConfig.total = res.Data.TotalCount\n        if (this.customTableConfig.tableData.length > 0) {\n          this.customTableConfig.tableData.map(v => {\n            v.Warning_First_Time = (v.Warning_First_Time ?? '') != '' ? dayjs(v.Warning_First_Time).format('YYYY-MM-DD HH:mm:ss') : ''\n            v.Warning_Last_Time = (v.Warning_Last_Time ?? '') != '' ? dayjs(v.Warning_Last_Time).format('YYYY-MM-DD HH:mm:ss') : ''\n          })\n        }\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n    async handleExport() {\n      if (this.tableSelection.length == 0) {\n        this.$message.warning('请选择数据在导出')\n        return\n      }\n      const res = await ExportEntranceWarning({\n        id: this.tableSelection.map((item) => item.Id).toString(),\n        code: 'AccessControlAlarmDetails1'\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        downloadFile(res.Data, '告警明细数据')\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.getEquipmentList()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前页: ${val}`)\n      this.customTableConfig.currentPage = val\n      this.getEquipmentList()\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection\n    },\n    handleEdit(row) {\n      this.dialogVisible = true\n      this.componentsConfig.Data = row\n    },\n    handleChange(row) {\n      if (row.HandleStatusStr == '关闭') {\n        this.$message.warning('请勿重复操作')\n      } else {\n        this.$confirm('此操作将关闭该告警, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          UpdateEntranceWarningStatus({ id: row.Id, wid: row.WId, StatusEnum: 2 }).then(res => {\n            if (res.IsSucceed) {\n              this.$message.success('操作成功')\n              this.init()\n            } else {\n              this.$message.error(res.Message)\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消'\n          })\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.mt20 {\n  margin-top: 10px;\n}\n.layout{\n  height: calc(100vh - 90px);\n  overflow: auto;\n}\n</style>\n"]}]}