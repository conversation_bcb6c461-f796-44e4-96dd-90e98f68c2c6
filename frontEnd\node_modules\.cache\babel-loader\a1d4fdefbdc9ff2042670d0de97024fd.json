{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\workOrderStatistics\\repair\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\workOrderStatistics\\repair\\index.vue", "mtime": 1755674552421}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetWorkOrderManageList", "GetWorkorderStatistics", "GetTimeoutStatistics", "GetSatisfactionStatistics", "GetProcessedRank", "GetWorkShopCase", "GetWorkOrderTrend", "GetWorkOrderErrorType", "GetEquipFailureRateRank", "GetDeviceServiceabilityRate", "dayjs", "<PERSON><PERSON>", "use", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Line<PERSON>hart", "<PERSON><PERSON><PERSON>", "Gauge<PERSON>hart", "GridComponent", "LegendComponent", "TooltipComponent", "TitleComponent", "DataZoomComponent", "ToolboxComponent", "editDialog", "name", "components", "mixins", "data", "jumpUrlList", "pendingRepairRequestStatus", "text", "value", "color", "pendingRepairRequestData", "repairOverview", "yearMonthRadio", "yearMonthType", "yearMonthValue", "Date", "format", "scrolltimer", "trendRepairReportsVariousWorkshopsOptions", "tooltip", "trigger", "axisPointer", "type", "legend", "top", "right", "itemWidth", "itemHeight", "itemGap", "grid", "left", "bottom", "containLabel", "xAxis", "axisLine", "show", "axisTick", "yAxis", "position", "logBase", "series", "repairStatusEachWorkshopOptions", "formatter", "params", "concat", "percent", "_defineProperty", "orient", "textStyle", "rich", "labelMark", "width", "valueMark", "percentMark", "radius", "labelLine", "normal", "length", "length2", "lineStyle", "label", "padding", "a", "lineHeight", "align", "hr", "borderColor", "borderWidth", "height", "per", "console", "log", "emphasis", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "repairFaultTypeOptions", "repairResponseConfig", "repairResponseOptions", "crossStyle", "axisLabel", "<PERSON><PERSON><PERSON><PERSON>", "stack", "valueFormatter", "smooth", "symbol", "yAxisIndex", "repairSatisfactionConfig", "repairSatisfactionOptions", "equipmentIntegrityRate", "equipmentIntegrityRateOptions", "silent", "zlevel", "startAngle", "endAngle", "clockwise", "splitNumber", "avoidLabelOverlap", "progress", "distance", "splitLine", "pointer", "title", "detail", "center", "x", "y", "x2", "y2", "colorStops", "offset", "pamars", "fontSize", "repairProcessingPersonnelCompleteRankingData", "equipmentFailureRateRanking", "activated", "<PERSON><PERSON><PERSON><PERSON>", "autoScroll", "mounted", "initData", "methods", "openDialog", "row", "orderType", "$refs", "handleOpen", "getWorkOrderManageList", "getTimeoutStatistics", "getWorkorderStatistics", "getSatisfactionStatistics", "getProcessedRank", "getWorkShopCase", "getWorkOrderTrend", "getWorkOrderErrorType", "getEquipFailureRateRank", "getDeviceServiceabilityRate", "resetForm", "yearMonthRadioChange", "e", "yearMonthPickerChange", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "model", "Order_Code", "Order_Name", "Create_Date", "Create_EDate", "State", "WorkOrder_Setup_Id", "Maintain_Person", "WorkOrder_State", "Type", "pageInfo", "Page", "PageSize", "SortName", "SortOrder", "sent", "Data", "stop", "_this2", "_callee2", "average", "averageStr", "_callee2$", "_context2", "WorkOrderType", "DateType", "StartTime", "getStartTime", "Avg", "min", "max", "roundCap", "opacity", "offsetCenter", "valueAnimation", "_this3", "_callee3", "_callee3$", "_context3", "_this4", "_callee4", "_callee4$", "_context4", "map", "item", "Label", "Value", "Rate", "obj", "find", "_this5", "_callee5", "_callee5$", "_context5", "_this6", "_callee6", "xAxisData", "_callee6$", "_context6", "ShopData", "ele", "ShopName", "focus", "_this7", "_callee7", "_callee7$", "_context7", "ServiceabilityRate", "_this8", "_callee8", "_callee8$", "_context8", "_this9", "_callee9", "_callee9$", "_context9", "List", "Timely", "Timeout", "Percent", "_this10", "_callee10", "_callee10$", "_context10", "getWaitingTime", "date", "startDate", "endDate", "difference", "Math", "abs", "days", "floor", "hours", "minutes", "formattedDifference", "getStatusStyle", "table", "scroll_Table", "divData", "bodyWrapper", "window", "clearInterval", "setInterval", "scrollTop", "clientHeight", "scrollHeight", "repairProcessingPersonnelCompleteRankingDataClassName", "_ref", "rowIndex", "isEvenOrOdd", "pendingRepairRequestDataClassName", "_ref2", "number", "lookMoreDetail", "Platform", "ModuleId", "localStorage", "getItem", "ModuleCode", "startOfMonth", "startOf", "endOfMonth", "endOf", "$qiankun", "switchMicroAppFn"], "sources": ["src/views/business/equipmentManagement/workOrderStatistics/repair/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 workOrderStatistics_repair\">\r\n    <el-row :gutter=\"12\">\r\n      <el-col :span=\"24\">\r\n        <el-card shadow=\"hover\">\r\n          <div class=\"search_content\">\r\n            <span class=\"label\">选择维度</span>\r\n            <el-radio-group\r\n              v-model=\"yearMonthRadio\"\r\n              class=\"radio\"\r\n              @change=\"yearMonthRadioChange\"\r\n            >\r\n              <el-radio-button label=\"1\">年</el-radio-button>\r\n              <el-radio-button label=\"2\">月</el-radio-button>\r\n            </el-radio-group>\r\n            <el-date-picker\r\n              v-model=\"yearMonthValue\"\r\n              class=\"picker\"\r\n              :editable=\"false\"\r\n              :clearable=\"false\"\r\n              :type=\"yearMonthType\"\r\n              @change=\"yearMonthPickerChange\"\r\n            />\r\n            <el-button @click=\"resetForm\">重置</el-button>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"10\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <div class=\"title_content\">\r\n              <span>报修总览</span>\r\n              <el-popover\r\n                placement=\"top-start\"\r\n                title=\"说明\"\r\n                width=\"420\"\r\n                trigger=\"hover\"\r\n              >\r\n                <div>\r\n                  <span>待处理：工单中心工单状态为待处理的所有工单数量</span><br>\r\n                  <span>处理中：工单中心工单状态为处理中，待复检的所有工单数量</span><br>\r\n                  <span>已处理：工单中心工单状态为待评价，处理完成，已关闭的所有工单数量</span>\r\n                </div>\r\n                <img\r\n                  slot=\"reference\"\r\n                  style=\"width: 16px; height: 16px\"\r\n                  src=\"@/assets/question.png\"\r\n                  alt=\"\"\r\n                >\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n          <div class=\"repairOverview_content\">\r\n            <div class=\"top\">\r\n              <div class=\"main\">\r\n                <img\r\n                  class=\"left\"\r\n                  src=\"@/assets/totalUmberSorkOrders.png\"\r\n                  alt=\"\"\r\n                >\r\n                <div class=\"right\">\r\n                  <span class=\"text\" style=\"color: #298dff\">{{\r\n                    repairOverview.Total\r\n                  }}</span>\r\n                  <span class=\"value\">报修总数</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"main\">\r\n                <img class=\"left\" src=\"@/assets/repairTime.png\" alt=\"\">\r\n                <div class=\"right\">\r\n                  <span class=\"text\" style=\"color: #ffae2c\">{{\r\n                    repairOverview.FixTime\r\n                  }}</span>\r\n                  <div class=\"value\">\r\n                    <span>平均修复时间</span>\r\n                    <el-popover\r\n                      placement=\"top-start\"\r\n                      title=\"说明\"\r\n                      trigger=\"hover\"\r\n                    >\r\n                      <span>平均修复时间=工单总处理时长/工单个数</span>\r\n                      <img\r\n                        slot=\"reference\"\r\n                        style=\"width: 16px; height: 16px\"\r\n                        src=\"@/assets/question.png\"\r\n                        alt=\"\"\r\n                      >\r\n                    </el-popover>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"bottom\">\r\n              <div class=\"main\">\r\n                <img class=\"left\" src=\"@/assets/pendingProcessing.png\" alt=\"\">\r\n                <div class=\"right\">\r\n                  <span class=\"value\">待处理</span>\r\n                  <span class=\"text\" style=\"color: #ff5e7c\">{{\r\n                    repairOverview.Pending\r\n                  }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"main\">\r\n                <img class=\"left\" src=\"@/assets/processing.png\" alt=\"\">\r\n                <div class=\"right\">\r\n                  <span class=\"value\">处理中</span>\r\n                  <span class=\"text\" style=\"color: #298dff\">{{\r\n                    repairOverview.Processing\r\n                  }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"main\">\r\n                <img class=\"left\" src=\"@/assets/processed.png\" alt=\"\">\r\n                <div class=\"right\">\r\n                  <span class=\"value\">已处理</span>\r\n                  <span class=\"text\" style=\"color: #00d3a7\">{{\r\n                    repairOverview.Processed\r\n                  }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"14\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>待办报修</span>\r\n            <el-button\r\n              type=\"text\"\r\n              @click=\"lookMoreDetail('WorkOrderManagement')\"\r\n            >查看更多 <i class=\"el-icon-arrow-right\" /></el-button>\r\n          </div>\r\n          <div style=\"margin-top: -20px\">\r\n            <el-table\r\n              ref=\"scroll_Table\"\r\n              :data=\"pendingRepairRequestData\"\r\n              style=\"width: 100%\"\r\n              height=\"240\"\r\n              :highlight-current-row=\"false\"\r\n              :row-class-name=\"pendingRepairRequestDataClassName\"\r\n              @mouseenter.native=\"autoScroll(true)\"\r\n              @mouseleave.native=\"autoScroll(false)\"\r\n            >\r\n              <el-table-column prop=\"Order_Name\" label=\"报修名称\" />\r\n              <el-table-column prop=\"State\" label=\"报修状态\">\r\n                <template slot-scope=\"scope\">\r\n                  <span\r\n                    :style=\"{\r\n                      color: getStatusStyle(scope.row).color,\r\n                    }\"\r\n                  >\r\n                    {{ getStatusStyle(scope.row).text }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Warranty_Person\" label=\"提交人员\" />\r\n              <el-table-column prop=\"Create_Date\" label=\"创建时间\" />\r\n              <el-table-column label=\"等待时长\">\r\n                <template slot-scope=\"scope\">\r\n                  <span> {{ getWaitingTime(scope.row.Create_Date) }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"ErrPercent\" label=\"操作\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    type=\"text\"\r\n                    size=\"small\"\r\n                    @click=\"\r\n                      openDialog('detail', scope.row, scope.row.Order_Type)\r\n                    \"\r\n                  >查看详情</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"9\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>报修故障类型</span>\r\n          </div>\r\n          <div class=\"equipmentStartupStatus_content\">\r\n            <v-chart\r\n              ref=\"repairFaultTypeRef\"\r\n              class=\"repairFaultType\"\r\n              :option=\"repairFaultTypeOptions\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"5\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <div class=\"title_content\">\r\n              <span>设备完好率</span>\r\n              <el-popover\r\n                placement=\"top-start\"\r\n                title=\"说明\"\r\n                width=\"420\"\r\n                trigger=\"hover\"\r\n              >\r\n                <div>\r\n                  <span>1.设备维修完好率=月完好天数/月总天数</span><br>\r\n                  <span>2.月完好天数：当天无未处理完成的工单即为完好，每天24：00进行当天工单状态统计</span>\r\n                </div>\r\n                <img\r\n                  slot=\"reference\"\r\n                  style=\"width: 16px; height: 16px\"\r\n                  src=\"@/assets/question.png\"\r\n                  alt=\"\"\r\n                >\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n          <div class=\"equipmentIntegrityRate_content\">\r\n            <div style=\"width: 60%; height: 100%\">\r\n              <v-chart\r\n                ref=\"equipmentIntegrityRateRef\"\r\n                class=\"equipmentIntegrityRate\"\r\n                :option=\"equipmentIntegrityRateOptions\"\r\n                :autoresize=\"true\"\r\n              />\r\n            </div>\r\n            <div class=\"equipmentIntegrityRatelists\">\r\n              <div class=\"equipmentIntegrityRatelist\">\r\n                <span class=\"label\">完好率</span>\r\n                <span\r\n                  class=\"value\"\r\n                  style=\"color: #00d3a7\"\r\n                >{{ equipmentIntegrityRate.ServiceabilityRate }}%</span>\r\n              </div>\r\n              <div class=\"equipmentIntegrityRatelist\">\r\n                <span class=\"label\">统计天数</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  equipmentIntegrityRate.StatisticsDays\r\n                }}</span>\r\n              </div>\r\n              <div class=\"equipmentIntegrityRatelist\">\r\n                <span class=\"label\">完好天数</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  equipmentIntegrityRate.ServiceabilityDays\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"5\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <div class=\"title_content\">\r\n              <span>设备故障率排行</span>\r\n              <el-popover\r\n                placement=\"top-start\"\r\n                title=\"说明\"\r\n                width=\"420\"\r\n                trigger=\"hover\"\r\n              >\r\n                <div>\r\n                  <span>代表单位时间内设备的故障次数多少，计算方式如下： 故障率=\r\n                    累计故障次数/设备开机总时间h×100%</span>\r\n                </div>\r\n                <img\r\n                  slot=\"reference\"\r\n                  style=\"width: 16px; height: 16px\"\r\n                  src=\"@/assets/question.png\"\r\n                  alt=\"\"\r\n                >\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n          <div class=\"productionEquipmentLoadRateRanking_content\">\r\n            <div\r\n              v-for=\"(item, index) in equipmentFailureRateRanking\"\r\n              :key=\"index\"\r\n              class=\"item\"\r\n            >\r\n              <div class=\"top\">\r\n                <span>{{ item.EquipName }}</span>\r\n                <span>{{ item.FailureRate }}</span>\r\n              </div>\r\n              <el-progress\r\n                class=\"bottom\"\r\n                :percentage=\"item.FailureRate\"\r\n                :show-text=\"false\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"5\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>报修满意度</span>\r\n          </div>\r\n          <div class=\"repairSatisfaction_content\">\r\n            <div style=\"width: 60%; height: 100%\">\r\n              <v-chart\r\n                ref=\"repairSatisfactionRef\"\r\n                class=\"repairSatisfaction\"\r\n                :option=\"repairSatisfactionOptions\"\r\n                :autoresize=\"true\"\r\n              />\r\n            </div>\r\n            <div class=\"repairSatisfactionlists\">\r\n              <div class=\"repairSatisfactionlist\">\r\n                <span class=\"label\">处理报修总数</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  repairSatisfactionConfig.Total\r\n                }}</span>\r\n              </div>\r\n              <div class=\"repairSatisfactionlist\" style=\"margin-top: 20px\">\r\n                <span class=\"label\">最高满意度</span>\r\n                <span\r\n                  class=\"value\"\r\n                  style=\"color: #00d3a7\"\r\n                >{{ repairSatisfactionConfig.Max }} 分</span>\r\n              </div>\r\n              <div class=\"repairSatisfactionlist\">\r\n                <span class=\"label\">最低满意度</span>\r\n                <span\r\n                  class=\"value\"\r\n                  style=\"color: #ff902c\"\r\n                >{{ repairSatisfactionConfig.Min }} 分</span>\r\n              </div>\r\n              <div class=\"repairSatisfactionlist\">\r\n                <span class=\"label\">综合满意度</span>\r\n                <span\r\n                  class=\"value\"\r\n                  style=\"color: #298dff\"\r\n                >{{ repairSatisfactionConfig.Avg }} 分</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"14\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <div class=\"title_content\">\r\n              <span>报修响应</span>\r\n              <el-popover\r\n                placement=\"top-start\"\r\n                title=\"说明\"\r\n                width=\"420\"\r\n                trigger=\"hover\"\r\n              >\r\n                <div>\r\n                  <span>1.响应及时率：计算工单创建时间到接单时间的时长，该时间越短则代表处理人员的响应速度越快。</span><br>\r\n                  <span>2.响应超时：接单响应时间超过 1小时\r\n                    的工单计算为响应超时。</span>\r\n                </div>\r\n                <img\r\n                  slot=\"reference\"\r\n                  style=\"width: 16px; height: 16px\"\r\n                  src=\"@/assets/question.png\"\r\n                  alt=\"\"\r\n                >\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n          <div class=\"repairResponse_content\">\r\n            <div class=\"repairResponselists\">\r\n              <div class=\"repairResponselist\">\r\n                <span class=\"label\">报修总数</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  repairResponseConfig.Total\r\n                }}</span>\r\n              </div>\r\n              <div class=\"repairResponselist\">\r\n                <span class=\"label\">响应及时</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  repairResponseConfig.Timely\r\n                }}</span>\r\n              </div>\r\n              <div class=\"repairResponselist\">\r\n                <span class=\"label\">响应超时</span>\r\n                <span class=\"value\" style=\"color: #ff902c\">{{\r\n                  repairResponseConfig.Timeout\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n            <v-chart\r\n              ref=\"repairResponseRef\"\r\n              class=\"repairResponse\"\r\n              :option=\"repairResponseOptions\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"10\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>报修处理人员完成排名</span>\r\n          </div>\r\n          <div class=\"equipmentFailureTrend_content\" style=\"margin-top: -20px\">\r\n            <el-table\r\n              :data=\"repairProcessingPersonnelCompleteRankingData\"\r\n              style=\"width: 100%\"\r\n              height=\"475\"\r\n              :highlight-current-row=\"false\"\r\n              :row-class-name=\"\r\n                repairProcessingPersonnelCompleteRankingDataClassName\r\n              \"\r\n            >\r\n              <!-- ref=\"scroll_Table\"\r\n              @mouseenter.native=\"autoScroll(true)\"\r\n              @mouseleave.native=\"autoScroll(false)\" -->\r\n              <el-table-column label=\"排名\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <div\r\n                    v-if=\"scope.$index < 3\"\r\n                    class=\"tablenumber\"\r\n                    :style=\"{\r\n                      backgroundImage:\r\n                        'url(' +\r\n                        require(`../../../../../assets/no_${\r\n                          scope.$index + 1\r\n                        }.png`) +\r\n                        ')',\r\n                    }\"\r\n                  >\r\n                    <span> {{ scope.$index + 1 }}</span>\r\n                  </div>\r\n                  <div v-else class=\"tablenumber\">\r\n                    <span> {{ scope.$index + 1 }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Name\" label=\"姓名\" width=\"100\" />\r\n              <el-table-column prop=\"Count\" sortable label=\"数量\" width=\"100\" />\r\n              <el-table-column prop=\"Duration\" sortable label=\"用时\" />\r\n              <el-table-column\r\n                prop=\"Timely\"\r\n                sortable\r\n                label=\"响应及时率\"\r\n                width=\"120\"\r\n              />\r\n              <el-table-column\r\n                prop=\"Satisfaction\"\r\n                sortable\r\n                label=\"综合满意度\"\r\n                width=\"120\"\r\n              />\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"9\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>各车间报修情况</span>\r\n          </div>\r\n          <div class=\"maintenanceWorkOrderProcessingStatus_content\">\r\n            <v-chart\r\n              ref=\"repairStatusEachWorkshopRef\"\r\n              class=\"repairStatusEachWorkshop\"\r\n              :option=\"repairStatusEachWorkshopOptions\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"15\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>各车间报修趋势</span>\r\n          </div>\r\n          <div class=\"repairStatusEachWorkshop_content\">\r\n            <v-chart\r\n              ref=\"trendRepairReportsVariousWorkshopsRef\"\r\n              class=\"trendRepairReportsVariousWorkshops\"\r\n              :option=\"trendRepairReportsVariousWorkshopsOptions\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <editDialog ref=\"editDialog\" @refresh=\"initData\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  GetWorkOrderManageList,\r\n  GetWorkorderStatistics,\r\n  GetTimeoutStatistics,\r\n  GetSatisfactionStatistics,\r\n  GetProcessedRank,\r\n  GetWorkShopCase,\r\n  GetWorkOrderTrend,\r\n  GetWorkOrderErrorType,\r\n  GetEquipFailureRateRank,\r\n  GetDeviceServiceabilityRate\r\n} from '@/api/business/equipmentManagement'\r\nimport dayjs from 'dayjs'\r\nimport VChart from 'vue-echarts'\r\nimport { use } from 'echarts/core'\r\nimport { CanvasRenderer } from 'echarts/renderers'\r\nimport { BarChart, LineChart, PieChart, GaugeChart } from 'echarts/charts'\r\n\r\nimport {\r\n  GridComponent,\r\n  LegendComponent,\r\n  TooltipComponent,\r\n  TitleComponent,\r\n  DataZoomComponent,\r\n  ToolboxComponent\r\n} from 'echarts/components'\r\n\r\nimport editDialog from '@/views/business/maintenanceAndUpkeep/workOrderManagement/editDialog.vue'\r\nuse([\r\n  CanvasRenderer,\r\n  BarChart,\r\n  LineChart,\r\n  PieChart,\r\n  GaugeChart,\r\n  DataZoomComponent,\r\n  GridComponent,\r\n  LegendComponent,\r\n  TitleComponent,\r\n  TooltipComponent,\r\n  ToolboxComponent\r\n])\r\nexport default {\r\n  name: 'EquipmentAnalysis',\r\n  components: {\r\n    VChart,\r\n    editDialog\r\n  },\r\n  mixins: [],\r\n  data() {\r\n    return {\r\n      // 查看更多跳转列表\r\n      jumpUrlList: [],\r\n      // 待办报修\r\n      pendingRepairRequestStatus: [\r\n        {\r\n          text: '待处理',\r\n          value: '0',\r\n          color: '#FF5E7C'\r\n        },\r\n        {\r\n          text: '处理中',\r\n          value: '1',\r\n          color: '#298DFF'\r\n        },\r\n        {\r\n          text: '待复检',\r\n          value: '2',\r\n          color: '#FF902C'\r\n        },\r\n        {\r\n          text: '待评价',\r\n          value: '3',\r\n          color: '#298DFF'\r\n        },\r\n        {\r\n          text: '处理完成',\r\n          value: '4',\r\n          color: '#00D3A7'\r\n        },\r\n        {\r\n          text: '已关闭',\r\n          value: '5',\r\n          color: '#333333'\r\n        }\r\n      ],\r\n      pendingRepairRequestData: [],\r\n      repairOverview: {},\r\n      yearMonthRadio: '2',\r\n      yearMonthType: 'month',\r\n      yearMonthValue: dayjs(new Date()).format('YYYY-MM'),\r\n      scrolltimer: '', // 自动滚动的定时任务\r\n      // 各车间报修趋势\r\n      trendRepairReportsVariousWorkshopsOptions: {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          }\r\n        },\r\n        legend: {\r\n          top: '0',\r\n          right: '0',\r\n          itemWidth: 16,\r\n          itemHeight: 8,\r\n          itemGap: 10\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        color: ['#4EBF8B', '#66CBF0', '#298DFF', '#FF902C'],\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            position: 'left',\r\n            logBase: 10\r\n          }\r\n        ],\r\n        series: [\r\n          // {\r\n          //   name: \"一车间\",\r\n          //   type: \"bar\",\r\n          //   barWidth: 20,\r\n          //   stack: \"vehicle\",\r\n          //   emphasis: {\r\n          //     focus: \"series\",\r\n          //   },\r\n          //   data: [],\r\n          // },\r\n          // {\r\n          //   name: \"二车间\",\r\n          //   type: \"bar\",\r\n          //   stack: \"vehicle\",\r\n          //   emphasis: {\r\n          //     focus: \"series\",\r\n          //   },\r\n          //   data: [],\r\n          // },\r\n          // {\r\n          //   name: \"配送中心\",\r\n          //   type: \"bar\",\r\n          //   stack: \"vehicle\",\r\n          //   emphasis: {\r\n          //     focus: \"series\",\r\n          //   },\r\n          //   data: [],\r\n          // },\r\n          // {\r\n          //   name: \"响应及时率\",\r\n          //   type: \"line\",\r\n          //   smooth: true,\r\n          //   symbol: \"none\",\r\n          //   yAxisIndex: 1,\r\n          //   tooltip: {\r\n          //     valueFormatter: function (value) {\r\n          //       return value + \" %\";\r\n          //     },\r\n          //   },\r\n          //   data: [],\r\n          // },\r\n        ]\r\n      },\r\n      // 各车间报修情况\r\n      repairStatusEachWorkshopOptions: {\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            return ` ${params.name} ${params.percent}%  `\r\n          }\r\n        },\r\n        color: ['#4EBF8B', '#298DFF', '#51A1FD'],\r\n        legend: {\r\n          orient: 'vertical',\r\n          right: '0',\r\n          bottom: 'center',\r\n          itemWidth: 12,\r\n          itemHeight: 6,\r\n          textStyle: {\r\n            color: 'rgba(34, 40, 52, 0.65)'\r\n          },\r\n          textStyle: {\r\n            rich: {\r\n              labelMark: {\r\n                width: 60,\r\n                color: '#222834'\r\n              },\r\n              valueMark: {\r\n                width: 40,\r\n                color: '#66CBF0'\r\n              },\r\n              percentMark: {\r\n                width: 40,\r\n                color: '#298DFF'\r\n              }\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            type: 'pie',\r\n            radius: '50%',\r\n            right: 150,\r\n            data: [],\r\n            labelLine: {\r\n              // 设置延长线的长度\r\n              normal: {\r\n                length: 5, // 设置延长线的长度\r\n                length2: 10, // 设置第二段延长线的长度\r\n                lineStyle: {\r\n                  color: 'rgba(194, 203, 226, 1)'\r\n                }\r\n              }\r\n            },\r\n            label: {\r\n              normal: {\r\n                // formatter: '{d}%, {c} \\n\\n',\r\n                formatter: ' {c|{b}}  {per|{d}%} \\n{hr|}\\n{a|}', // 这里最后另一行设置了一个空数据是为了能让延长线与hr线对接起来\r\n                padding: [0, -4], // 取消hr线跟延长线之间的间隙\r\n                rich: {\r\n                  a: {\r\n                    color: '#999',\r\n                    lineHeight: 20, // 设置最后一行空数据高度，为了能让延长线与hr线对接起来\r\n                    align: 'center'\r\n                  },\r\n                  hr: {\r\n                    // 设置hr是为了让中间线能够自适应长度\r\n                    borderColor: 'rgba(194, 203, 226, 1)', // hr的颜色为auto时候会主动显示颜色的\r\n                    width: '105%',\r\n                    borderWidth: 0.5,\r\n                    height: 0.5\r\n                  },\r\n                  per: {\r\n                    // 用百分比数据来调整下数字位置，显的好看些。如果不设置，formatter最后一行的空数据就不需要\r\n                    padding: [4, 0],\r\n                    // color: \"rgba(194, 203, 226, 1)\",\r\n                    color: function(params) {\r\n                      console.log(params, 'params-------------')\r\n                      // 通过数据项的颜色来设置文字颜色\r\n                      return params.color\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                shadowBlur: 10,\r\n                shadowOffsetX: 0,\r\n                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n              }\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      // 报修故障类型\r\n      repairFaultTypeOptions: {\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            return ` ${params.name} ${params.percent}%  `\r\n          }\r\n        },\r\n        color: ['#4EBF8B', '#298DFF', '#51A1FD'],\r\n        legend: {\r\n          orient: 'vertical',\r\n          right: '0',\r\n          bottom: 'center',\r\n          itemWidth: 12,\r\n          itemHeight: 6,\r\n          textStyle: {\r\n            color: 'rgba(34, 40, 52, 0.65)'\r\n          },\r\n          formatter: function(name) {\r\n            return `${name}`\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            type: 'pie',\r\n            radius: '50%',\r\n            // right: 100,\r\n            data: [],\r\n            labelLine: {\r\n              // 设置延长线的长度\r\n              normal: {\r\n                length: 5, // 设置延长线的长度\r\n                length2: 10, // 设置第二段延长线的长度\r\n                lineStyle: {\r\n                  color: 'rgba(194, 203, 226, 1)'\r\n                }\r\n              }\r\n            },\r\n            label: {\r\n              normal: {\r\n                // formatter: '{d}%, {c} \\n\\n',\r\n                formatter: ' {c|{b}}  {per|{d}%} \\n{hr|}\\n{a|}', // 这里最后另一行设置了一个空数据是为了能让延长线与hr线对接起来\r\n                padding: [0, -4], // 取消hr线跟延长线之间的间隙\r\n                rich: {\r\n                  a: {\r\n                    color: '#999',\r\n                    lineHeight: 20, // 设置最后一行空数据高度，为了能让延长线与hr线对接起来\r\n                    align: 'center'\r\n                  },\r\n                  hr: {\r\n                    // 设置hr是为了让中间线能够自适应长度\r\n                    borderColor: 'rgba(194, 203, 226, 1)', // hr的颜色为auto时候会主动显示颜色的\r\n                    width: '105%',\r\n                    borderWidth: 0.5,\r\n                    height: 0.5\r\n                  },\r\n                  per: {\r\n                    // 用百分比数据来调整下数字位置，显的好看些。如果不设置，formatter最后一行的空数据就不需要\r\n                    padding: [4, 0]\r\n                  }\r\n                }\r\n              }\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                shadowBlur: 10,\r\n                shadowOffsetX: 0,\r\n                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n              }\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      // 报修响应\r\n      repairResponseConfig: {},\r\n      repairResponseOptions: {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            crossStyle: {\r\n              color: '#999'\r\n            }\r\n          }\r\n        },\r\n        legend: {\r\n          top: '0',\r\n          right: '0',\r\n          itemWidth: 16,\r\n          itemHeight: 8,\r\n          itemGap: 10\r\n        },\r\n        color: ['#298DFF', '#FF902C', '#00D3A7'],\r\n        xAxis: [\r\n          {\r\n            type: 'category',\r\n            data: [],\r\n            axisPointer: {\r\n              type: 'shadow'\r\n            },\r\n            axisLine: {\r\n              show: false\r\n            },\r\n            axisTick: {\r\n              show: false\r\n            }\r\n          }\r\n        ],\r\n        yAxis: [\r\n          {\r\n            type: 'value'\r\n            // min: 0,\r\n            // max: 250,\r\n            // interval: 50,\r\n            // axisLabel: {\r\n            //   formatter: \"{value} ml\",\r\n            // },\r\n          },\r\n          {\r\n            type: 'value',\r\n            // min: 0,\r\n            // max: 25,\r\n            // interval: 5,\r\n            axisLabel: {\r\n              formatter: '{value} %'\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '响应及时数',\r\n            type: 'bar',\r\n            barWidth: 20,\r\n            stack: 'vehicle',\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return value\r\n              }\r\n            },\r\n            data: []\r\n          },\r\n          {\r\n            name: '响应超时数',\r\n            type: 'bar',\r\n            barWidth: 20,\r\n            stack: 'vehicle',\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return value\r\n              }\r\n            },\r\n            data: []\r\n          },\r\n          {\r\n            name: '响应及时率',\r\n            type: 'line',\r\n            smooth: true,\r\n            symbol: 'none',\r\n            yAxisIndex: 1,\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return value + ' %'\r\n              }\r\n            },\r\n            data: []\r\n          }\r\n        ]\r\n      },\r\n      // 报修满意度\r\n      repairSatisfactionConfig: {},\r\n      repairSatisfactionOptions: {\r\n        series: []\r\n      },\r\n      // 设备完好率\r\n      equipmentIntegrityRate: {},\r\n      equipmentIntegrityRateOptions: {\r\n        tooltip: {\r\n          show: false\r\n        },\r\n        series: [\r\n          {\r\n            // 外圆\r\n            silent: false,\r\n            type: 'gauge',\r\n            zlevel: 2,\r\n            startAngle: 0,\r\n            endAngle: 360,\r\n            clockwise: true,\r\n            radius: '75%',\r\n            splitNumber: 5,\r\n            avoidLabelOverlap: false,\r\n            axisLine: {\r\n              show: true\r\n              // lineStyle: {\r\n              //   color: [\r\n              //     [80 / 100, \"rgba(0, 211, 167, 0.3)\"],\r\n              //     [1, \"#f0f2f8\"],\r\n              //   ],\r\n              //   width: 16,\r\n              // },\r\n            },\r\n            itemStyle: {\r\n              color: 'rgba(255,255,255,0)'\r\n            },\r\n            progress: {\r\n              show: true\r\n            },\r\n            axisTick: {\r\n              show: true,\r\n              splitNumber: 1,\r\n              distance: -16,\r\n              lineStyle: {\r\n                color: '#ffffff',\r\n                width: 3\r\n              },\r\n              length: 20\r\n            }, // 刻度样式\r\n            splitLine: {\r\n              show: false\r\n            },\r\n            axisLabel: {\r\n              show: false\r\n            },\r\n            pointer: {\r\n              show: false\r\n            },\r\n            title: {\r\n              show: false\r\n            },\r\n            detail: {\r\n              show: false\r\n            }\r\n          },\r\n          {\r\n            // 外圆2\r\n            type: 'pie',\r\n            silent: true,\r\n            center: ['50%', '50%'],\r\n            radius: ['0%', '50%'],\r\n            avoidLabelOverlap: false,\r\n            zlevel: 3,\r\n            itemStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 1,\r\n                x2: 0,\r\n                y2: 0,\r\n                colorStops: [\r\n                  {\r\n                    offset: 0,\r\n                    color: 'rgba(0, 211, 167, 0.3)'\r\n                  },\r\n                  {\r\n                    offset: 1,\r\n                    color: 'rgba(57, 133, 238, 0)'\r\n                  }\r\n                ]\r\n              },\r\n              borderColor: 'rgba(0, 211, 167, 0.2)'\r\n            },\r\n            label: {\r\n              show: true,\r\n              position: 'center',\r\n              formatter: (pamars) => {\r\n                return `0%`\r\n              },\r\n              fontSize: 24,\r\n              color: '#3f4652'\r\n            },\r\n            labelLine: {\r\n              show: false\r\n            },\r\n            data: [1]\r\n          }\r\n        ]\r\n      },\r\n      // 报修处理人员完成排名\r\n      repairProcessingPersonnelCompleteRankingData: [],\r\n      // 获取设备故障率排行\r\n      equipmentFailureRateRanking: []\r\n    }\r\n  },\r\n  activated() {},\r\n  beforeDestroy() {\r\n    this.autoScroll(true)\r\n  },\r\n  mounted() {\r\n    this.initData()\r\n\r\n    this.autoScroll()\r\n  },\r\n  methods: {\r\n    // 打开弹框\r\n    openDialog(type, row, orderType) {\r\n      this.$refs.editDialog.handleOpen(type, row, orderType)\r\n    },\r\n    // 初始化加载数据\r\n    initData() {\r\n      // 待办报修\r\n      this.getWorkOrderManageList()\r\n      // 获取工单总览统计\r\n      this.getTimeoutStatistics()\r\n      // 获取工单响应超时统计\r\n      this.getWorkorderStatistics()\r\n      // 获取工单满意度统计\r\n      this.getSatisfactionStatistics()\r\n      // 获取处理人员完成排名\r\n      this.getProcessedRank()\r\n\r\n      // 获取各车间工单情况\r\n      this.getWorkShopCase()\r\n      // 获取各车间趋势\r\n      this.getWorkOrderTrend()\r\n      // 获取报修工单故障类型\r\n      this.getWorkOrderErrorType()\r\n\r\n      // 获取设备完好率\r\n      this.getEquipFailureRateRank()\r\n      // 获取设备故障率排行\r\n      this.getDeviceServiceabilityRate()\r\n    },\r\n    // 充值表单数据并加载\r\n    resetForm() {\r\n      this.yearMonthType = 'month'\r\n      this.yearMonthValue = dayjs(new Date()).format('YYYY-MM')\r\n      this.initData()\r\n    },\r\n    // 筛选条件\r\n    yearMonthRadioChange(e) {\r\n      if (e == 1) {\r\n        this.yearMonthType = 'year'\r\n        this.yearMonthValue = dayjs(new Date()).format('YYYY')\r\n      } else if (e == 2) {\r\n        this.yearMonthType = 'month'\r\n        this.yearMonthValue = dayjs(new Date()).format('YYYY-MM')\r\n      }\r\n      this.initData()\r\n    },\r\n    // 年 月 切换\r\n    yearMonthPickerChange() {\r\n      this.initData()\r\n    },\r\n    // 待办报修\r\n    async getWorkOrderManageList() {\r\n      const res = await GetWorkOrderManageList({\r\n        model: {\r\n          Date: [],\r\n          Order_Code: '',\r\n          Order_Name: '',\r\n          Create_Date: '',\r\n          Create_EDate: '',\r\n          State: '',\r\n          WorkOrder_Setup_Id: 'jsbx',\r\n          Maintain_Person: '',\r\n          WorkOrder_State: 0,\r\n          Type: 1,\r\n          type: 1\r\n        },\r\n        pageInfo: {\r\n          Page: 1,\r\n          PageSize: 10,\r\n          SortName: 'Create_Date',\r\n          SortOrder: 'DESC'\r\n        }\r\n      })\r\n      this.pendingRepairRequestData = res.Data.Data\r\n    },\r\n    // 获取工单满意度统计\r\n    async getSatisfactionStatistics() {\r\n      const res = await GetSatisfactionStatistics({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairSatisfactionConfig = res.Data\r\n      const average = res.Data.Avg || 0\r\n      let averageStr = ''\r\n      if (average >= 4) {\r\n        averageStr = '优'\r\n      } else if (average < 4 && average >= 3) {\r\n        averageStr = '良'\r\n      } else if (average < 3 && average >= 2) {\r\n        averageStr = '中'\r\n      } else if (average < 2) {\r\n        averageStr = '差'\r\n      }\r\n      this.repairSatisfactionOptions.series = [\r\n        {\r\n          name: '外部刻度',\r\n          type: 'gauge',\r\n          radius: '100%',\r\n          splitNumber: 20,\r\n          min: 0,\r\n          max: 100,\r\n          startAngle: 225,\r\n          endAngle: -45,\r\n          axisLine: {\r\n            roundCap: true,\r\n            lineStyle: {\r\n              width: 0,\r\n              opacity: 0\r\n            }\r\n          },\r\n          axisLabel: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            show: true,\r\n            length: 3,\r\n            lineStyle: {\r\n              color: '#a9afb8',\r\n              width: 1\r\n            }\r\n          },\r\n          detail: {\r\n            show: false\r\n          },\r\n          pointer: {\r\n            show: false\r\n          }\r\n        },\r\n        {\r\n          name: '内部刻度',\r\n          type: 'gauge',\r\n          radius: '80%',\r\n          splitNumber: 20,\r\n          min: 0,\r\n          max: 100,\r\n          startAngle: 225,\r\n          endAngle: -45,\r\n          title: {\r\n            show: true,\r\n            fontSize: 12,\r\n            color: '#505D6F',\r\n            offsetCenter: ['0', '-20%']\r\n          },\r\n          data: [\r\n            {\r\n              value: [],\r\n              name: '报修处理满意度'\r\n            }\r\n          ],\r\n          detail: {\r\n            valueAnimation: true,\r\n            formatter: () => {\r\n              return averageStr\r\n            },\r\n            fontSize: 14,\r\n            color: '#298DFF',\r\n            offsetCenter: [0, '10%']\r\n          },\r\n          axisLine: {\r\n            roundCap: true,\r\n            lineStyle: {\r\n              width: 20,\r\n              color: [\r\n                [\r\n                  (average * 20) / 100,\r\n                  {\r\n                    type: 'linear',\r\n                    x: 0,\r\n                    y: 1,\r\n                    x2: 0,\r\n                    y2: 0,\r\n                    colorStops: [\r\n                      {\r\n                        offset: 0,\r\n                        color: '#50FFE4'\r\n                      },\r\n                      {\r\n                        offset: 1,\r\n                        color: '#298DFF'\r\n                      }\r\n                    ]\r\n                  }\r\n                ],\r\n                [1, 'rgba(225,225,225,0.4)']\r\n              ]\r\n            }\r\n          },\r\n          axisLabel: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          },\r\n          pointer: {\r\n            show: false\r\n          }\r\n        }\r\n      ]\r\n    },\r\n    // 获取处理人员完成排名\r\n    async getProcessedRank() {\r\n      const res = await GetProcessedRank({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairProcessingPersonnelCompleteRankingData = res.Data\r\n    },\r\n    // 获取各车间工单情况\r\n    async getWorkShopCase() {\r\n      const res = await GetWorkShopCase({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      console.log(res, 'res')\r\n      const repairStatusEachWorkshopOptions = res.Data.map((item) => ({\r\n        name: item.Label,\r\n        value: item.Value,\r\n        percent: item.Rate\r\n      }))\r\n      this.repairStatusEachWorkshopOptions.series[0].data =\r\n        repairStatusEachWorkshopOptions\r\n      this.repairStatusEachWorkshopOptions.legend.formatter = function(name) {\r\n        const obj = repairStatusEachWorkshopOptions.find(\r\n          (item) => item.name == name\r\n        )\r\n        return `{labelMark|${obj.name}} {valueMark|${obj.value} 次}  {percentMark|${obj.percent} %}`\r\n      }\r\n    },\r\n    // 获取报修工单故障类型\r\n    async getWorkOrderErrorType() {\r\n      const res = await GetWorkOrderErrorType({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairFaultTypeOptions.series[0].data = res.Data.map((item) => ({\r\n        name: item.Label,\r\n        value: item.Value,\r\n        percent: item.Rate\r\n      }))\r\n    },\r\n    // 获取各车间趋势\r\n    async getWorkOrderTrend() {\r\n      const res = await GetWorkOrderTrend({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      let xAxisData = []\r\n      this.trendRepairReportsVariousWorkshopsOptions.series = res.Data.map(\r\n        (item) => {\r\n          xAxisData = item.ShopData.map((ele) => ele.Label)\r\n          if (item.ShopName == '报修数量') {\r\n            return {\r\n              name: item.ShopName,\r\n              type: 'line',\r\n              smooth: true,\r\n              symbol: 'none',\r\n              data: item.ShopData.map((ele) => ele.Value)\r\n            }\r\n          } else {\r\n            return {\r\n              name: item.ShopName,\r\n              type: 'bar',\r\n              stack: 'vehicle',\r\n              emphasis: {\r\n                focus: 'series'\r\n              },\r\n              data: item.ShopData.map((ele) => ele.Value)\r\n            }\r\n          }\r\n        }\r\n      )\r\n      this.trendRepairReportsVariousWorkshopsOptions.xAxis.data = xAxisData\r\n    },\r\n\r\n    // 获取设备完好率\r\n    async getDeviceServiceabilityRate() {\r\n      const res = await GetDeviceServiceabilityRate({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.equipmentIntegrityRate = res.Data\r\n      this.equipmentIntegrityRateOptions.series[0].axisLine.lineStyle = {\r\n        color: [\r\n          [res.Data.ServiceabilityRate / 100, 'rgba(0, 211, 167, 0.3)'],\r\n          [1, '#f0f2f8']\r\n        ],\r\n        width: 16\r\n      }\r\n      this.equipmentIntegrityRateOptions.series[1].label.formatter = function(\r\n        pamars\r\n      ) {\r\n        return `${res.Data.ServiceabilityRate}%`\r\n      }\r\n    },\r\n    // 获取设备故障率排行\r\n    async getEquipFailureRateRank() {\r\n      const res = await GetEquipFailureRateRank({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.equipmentFailureRateRanking = res.Data\r\n    },\r\n\r\n    // 获取工单总览统计\r\n    async getTimeoutStatistics() {\r\n      const res = await GetTimeoutStatistics({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairResponseConfig = res.Data\r\n      this.repairResponseOptions.xAxis[0].data = res.Data.List.map(\r\n        (item) => item.Date\r\n      )\r\n      this.repairResponseOptions.series[0].data = res.Data.List.map(\r\n        (item) => item.Timely\r\n      )\r\n      this.repairResponseOptions.series[1].data = res.Data.List.map(\r\n        (item) => item.Timeout\r\n      )\r\n      this.repairResponseOptions.series[2].data = res.Data.List.map(\r\n        (item) => item.Percent\r\n      )\r\n    },\r\n    // 获取工单响应超时统计\r\n    async getWorkorderStatistics() {\r\n      const res = await GetWorkorderStatistics({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairOverview = res.Data\r\n    },\r\n    // 获取等待时长\r\n    getWaitingTime(date) {\r\n      const startDate = new Date(date)\r\n      var endDate = new Date() // 获取当前时间\r\n      var difference = Math.abs(endDate - startDate)\r\n      var days = Math.floor(difference / (1000 * 60 * 60 * 24))\r\n      var hours = Math.floor(\r\n        (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)\r\n      )\r\n      var minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))\r\n      var formattedDifference = ''\r\n\r\n      if (days > 0) {\r\n        formattedDifference += days + '天'\r\n      }\r\n      if (hours > 0) {\r\n        formattedDifference += hours + '小时'\r\n      }\r\n      if (minutes > 0 || (days === 0 && hours === 0)) {\r\n        formattedDifference += minutes + '分钟'\r\n      }\r\n      return formattedDifference\r\n    },\r\n    // 获取状态样式\r\n    getStatusStyle(row) {\r\n      return this.pendingRepairRequestStatus.find(\r\n        (item) => item.value == row.State\r\n      )\r\n    },\r\n    // 获取日期时间\r\n    getStartTime(type) {\r\n      if (type == 1) {\r\n        return dayjs(this.yearMonthValue).format('YYYY')\r\n      } else if (type == 2) {\r\n        return dayjs(this.yearMonthValue).format('YYYY-MM')\r\n      }\r\n    },\r\n    // 设置表格自动滚动\r\n    autoScroll(stop) {\r\n      const table = this.$refs.scroll_Table\r\n      // 拿到表格中承载数据的div元素\r\n      const divData = table.$refs.bodyWrapper\r\n      // 拿到元素后，对元素进行定时增加距离顶部距离，实现滚动效果(此配置为每100毫秒移动1像素)\r\n      if (stop) {\r\n        // 再通过事件监听，监听到 组件销毁 后，再执行关闭计时器。\r\n        window.clearInterval(this.scrolltimer)\r\n      } else {\r\n        this.scrolltimer = window.setInterval(() => {\r\n          // 元素自增距离顶部1像素\r\n          divData.scrollTop += 2\r\n          // 判断元素是否滚动到底部(可视高度+距离顶部=整个高度)\r\n          if (\r\n            divData.clientHeight + divData.scrollTop ==\r\n            divData.scrollHeight\r\n          ) {\r\n            // 重置table距离顶部距离\r\n            divData.scrollTop = 0\r\n            // 重置table距离顶部距离。值=(滚动到底部时，距离顶部的大小) - 整个高度/2\r\n            // divData.scrollTop = divData.scrollTop - divData.scrollHeight / 2\r\n          }\r\n        }, 120) // 滚动速度\r\n      }\r\n    },\r\n\r\n    // 设置表格颜色\r\n    repairProcessingPersonnelCompleteRankingDataClassName({ row, rowIndex }) {\r\n      if (this.isEvenOrOdd(rowIndex + 1)) {\r\n        return 'row-one'\r\n      } else {\r\n        return 'row-two'\r\n      }\r\n    },\r\n    // 设置表格颜色\r\n    pendingRepairRequestDataClassName({ row, rowIndex }) {\r\n      if (this.isEvenOrOdd(rowIndex + 1)) {\r\n        return 'row-one'\r\n      } else {\r\n        return 'row-two'\r\n      }\r\n    },\r\n    //  判断是否是偶数行 还是奇数行\r\n    isEvenOrOdd(number) {\r\n      if (number % 2 === 0) {\r\n        return true\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    // 查看更多\r\n    lookMoreDetail() {\r\n      // let Url = this.jumpUrlList.find(\r\n      //   (item) => item.ModuleCode == ModuleCode\r\n      // ).Url;\r\n      const Platform = 'digitalfactory'\r\n      const ModuleId = localStorage.getItem('ModuleId')\r\n      const ModuleCode = localStorage.getItem('ModuleCode')\r\n      // 获取本月的第一天\r\n      const startOfMonth = dayjs().startOf('month')\r\n      // 获取本月的最后一天\r\n      const endOfMonth = dayjs().endOf('month')\r\n      // 输出结果\r\n      // Create_Date:\"2024-07-19\"\r\n      // Create_EDate:\"2024-08-14\"\r\n      this.$qiankun.switchMicroAppFn(\r\n        Platform,\r\n        'gzt',\r\n        '7622d042-b114-46a0-b1ba-b5621622f058',\r\n        `/business/maintenanceAndUpkeep/workOrderManagement?State=0&ActiveName=first&isJump=true`\r\n      )\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.workOrderStatistics_repair {\r\n  // padding: 10px 15px;\r\n  // height: calc(100vh - 90px);\r\n  overflow-y: auto;\r\n  .header {\r\n    display: flex;\r\n    // align-items: center;\r\n    justify-content: space-between;\r\n    height: 22px;\r\n    > span {\r\n      font-weight: bold;\r\n    }\r\n    .title_content {\r\n      display: flex;\r\n      font-weight: bold;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      span {\r\n        margin-right: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .search_content {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    .label {\r\n      margin-right: 10px;\r\n    }\r\n    .radio {\r\n      margin-right: 10px;\r\n    }\r\n    .picker {\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .repairSatisfaction_content {\r\n    height: 220px;\r\n    width: 100%;\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .repairSatisfactionlists {\r\n      display: flex;\r\n      flex-direction: column;\r\n      .repairSatisfactionlist {\r\n        padding: 2px 0px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        .label {\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          margin-right: 10px;\r\n        }\r\n        .value {\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .repairOverview_content {\r\n    height: 220px;\r\n    .bottom {\r\n      margin-top: 20px;\r\n      display: grid;\r\n      width: 100%;\r\n      grid-template-columns: repeat(3, calc(33% - 10px));\r\n      grid-gap: 20px; /* 设置间距 */\r\n      .main {\r\n        border-radius: 8px 8px 8px 8px;\r\n        padding: 10px 15px;\r\n        background: #fafdff;\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        justify-content: center;\r\n        .left {\r\n          width: 70px;\r\n          height: 70px;\r\n        }\r\n        .right {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          .text {\r\n            font-weight: bold;\r\n            font-size: 24px;\r\n            margin-top: 10px;\r\n          }\r\n          .value {\r\n            font-weight: 400;\r\n            font-size: 14px;\r\n            color: #666666;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .top {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      justify-content: space-around;\r\n      background: #fafdff;\r\n      border-radius: 8px 8px 8px 8px;\r\n      padding: 10px 15px;\r\n      .main {\r\n        display: flex;\r\n        flex-direction: row;\r\n        .left {\r\n          width: 80px;\r\n          height: 80px;\r\n        }\r\n        .right {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          .text {\r\n            font-weight: bold;\r\n            font-size: 24px;\r\n          }\r\n          .value {\r\n            font-weight: 400;\r\n            font-size: 14px;\r\n            color: #666666;\r\n            margin-top: 10px;\r\n            display: flex;\r\n            flex-direction: row;\r\n            align-items: center;\r\n            > span {\r\n              margin-right: 10px;\r\n              display: flex;\r\n              flex-direction: row;\r\n              align-items: center;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .maintenanceWorkOrderProcessingStatus_content {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 300px;\r\n    .left {\r\n      width: 50%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      padding: 0px 18px;\r\n      .title {\r\n        color: rgba(34, 40, 52, 0.85);\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        padding: 18px 0px;\r\n      }\r\n      .left_content {\r\n        background-color: #fafdff;\r\n        padding: 10px 15px;\r\n      }\r\n    }\r\n    .right {\r\n      width: 50%;\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      padding: 0px 18px;\r\n      .title {\r\n        color: rgba(34, 40, 52, 0.85);\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        padding: 18px 0px;\r\n      }\r\n      .right_content {\r\n        background-color: #fafdff;\r\n        padding: 10px 15px;\r\n      }\r\n    }\r\n    .item {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      margin-bottom: 20px;\r\n      .top {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-bottom: 6px;\r\n        font-size: 14px;\r\n        .left {\r\n          color: #333333;\r\n          font-size: 14px;\r\n        }\r\n        .right_color_one {\r\n          color: #298dff;\r\n        }\r\n        .right_color_two {\r\n          color: #00d3a7;\r\n        }\r\n        .right {\r\n          display: flex;\r\n          flex-direction: row;\r\n          .one {\r\n            margin-right: 14px;\r\n          }\r\n          .two {\r\n            width: 36px;\r\n            display: block;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .productionEquipmentLoadRateRanking_content {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: column;\r\n    height: 220px;\r\n    overflow-y: scroll;\r\n    scrollbar-width: none;\r\n    // scrollbar-color: transparent transparent;\r\n    .item {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      margin-bottom: 20px;\r\n      .top {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-bottom: 6px;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .equipmentIntegrityRate_content {\r\n    height: 220px;\r\n    display: flex;\r\n    flex-direction: row;\r\n    justify-content: space-between;\r\n    .equipmentIntegrityRatelists {\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: center;\r\n      width: 40%;\r\n      .equipmentIntegrityRatelist {\r\n        padding: 2px 0px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        margin-right: 20px;\r\n        .label {\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          margin-right: 10px;\r\n        }\r\n        .value {\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .repairResponse_content {\r\n    height: 300px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    .repairResponselists {\r\n      margin-top: -40px;\r\n      background: #fafdff;\r\n      border-radius: 8px 8px 8px 8px;\r\n      padding: 10px 30px;\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      justify-content: space-around;\r\n      width: 60%;\r\n      margin-bottom: 10px;\r\n      .repairResponselist {\r\n        padding: 2px 0px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        margin-right: 20px;\r\n        .label {\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          margin-right: 10px;\r\n        }\r\n        .value {\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .equipmentStartupStatus_content {\r\n    height: 220px;\r\n  }\r\n  .equipmentFailureTrend_content {\r\n    height: 320px;\r\n    .tablenumber {\r\n      width: 30px;\r\n      height: 23px;\r\n      background-size: 100%;\r\n      background-repeat: no-repeat;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      > span {\r\n        margin-top: 10px;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n  .repairStatusEachWorkshop_content {\r\n    height: 300px;\r\n    .right {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      .right_num {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  ::v-deep .el-card__header {\r\n    border-bottom: none !important;\r\n  }\r\n  ::v-deep .el-progress__text {\r\n    font-size: 18px !important;\r\n    color: #666666 !important;\r\n  }\r\n  ::v-deep.el-table .row-one {\r\n    background: rgba(41, 141, 255, 0.03) !important;\r\n  }\r\n\r\n  ::v-deep .el-table .row-two {\r\n    background: rgba(255, 255, 255, 1) !important;\r\n  }\r\n\r\n  // ::v-deep .el-radio-button__inner {\r\n  //   background-color: #ffffff;\r\n  //   // padding: 6px 32px;\r\n  //   height: 32px;\r\n  //   // line-height: 32px;\r\n  //   width: 80px;\r\n  //   font-size: 14px;\r\n  // }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkfA,SACAA,sBAAA,EACAC,sBAAA,EACAC,oBAAA,EACAC,yBAAA,EACAC,gBAAA,EACAC,eAAA,EACAC,iBAAA,EACAC,qBAAA,EACAC,uBAAA,EACAC,2BAAA,QACA;AACA,OAAAC,KAAA;AACA,OAAAC,MAAA;AACA,SAAAC,GAAA;AACA,SAAAC,cAAA;AACA,SAAAC,QAAA,EAAAC,SAAA,EAAAC,QAAA,EAAAC,UAAA;AAEA,SACAC,aAAA,EACAC,eAAA,EACAC,gBAAA,EACAC,cAAA,EACAC,iBAAA,EACAC,gBAAA,QACA;AAEA,OAAAC,UAAA;AACAZ,GAAA,EACAC,cAAA,EACAC,QAAA,EACAC,SAAA,EACAC,QAAA,EACAC,UAAA,EACAK,iBAAA,EACAJ,aAAA,EACAC,eAAA,EACAE,cAAA,EACAD,gBAAA,EACAG,gBAAA,CACA;AACA;EACAE,IAAA;EACAC,UAAA;IACAf,MAAA,EAAAA,MAAA;IACAa,UAAA,EAAAA;EACA;EACAG,MAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,WAAA;MACA;MACAC,0BAAA,GACA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAF,IAAA;QACAC,KAAA;QACAC,KAAA;MACA,EACA;MACAC,wBAAA;MACAC,cAAA;MACAC,cAAA;MACAC,aAAA;MACAC,cAAA,EAAA5B,KAAA,KAAA6B,IAAA,IAAAC,MAAA;MACAC,WAAA;MAAA;MACA;MACAC,yCAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACAC,IAAA;UACA;QACA;QACAC,MAAA;UACAC,GAAA;UACAC,KAAA;UACAC,SAAA;UACAC,UAAA;UACAC,OAAA;QACA;QACAC,IAAA;UACAC,IAAA;UACAL,KAAA;UACAM,MAAA;UACAC,YAAA;QACA;QACAvB,KAAA;QACAwB,KAAA;UACAX,IAAA;UACAlB,IAAA;UACA8B,QAAA;YACAC,IAAA;UACA;UACAC,QAAA;YACAD,IAAA;UACA;QACA;QACAE,KAAA,GACA;UACAf,IAAA;UACAgB,QAAA;UACAC,OAAA;QACA,EACA;QACAC,MAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QAAA;MAEA;MACA;MACAC,+BAAA;QACAtB,OAAA;UACAC,OAAA;UACAsB,SAAA,WAAAA,UAAAC,MAAA;YACA,WAAAC,MAAA,CAAAD,MAAA,CAAA1C,IAAA,OAAA2C,MAAA,CAAAD,MAAA,CAAAE,OAAA;UACA;QACA;QACApC,KAAA;QACAc,MAAA,EAAAuB,eAAA;UACAC,MAAA;UACAtB,KAAA;UACAM,MAAA;UACAL,SAAA;UACAC,UAAA;UACAqB,SAAA;YACAvC,KAAA;UACA;QAAA,gBACA;UACAwC,IAAA;YACAC,SAAA;cACAC,KAAA;cACA1C,KAAA;YACA;YACA2C,SAAA;cACAD,KAAA;cACA1C,KAAA;YACA;YACA4C,WAAA;cACAF,KAAA;cACA1C,KAAA;YACA;UACA;QACA,EACA;QACA+B,MAAA,GACA;UACAlB,IAAA;UACAgC,MAAA;UACA7B,KAAA;UACArB,IAAA;UACAmD,SAAA;YACA;YACAC,MAAA;cACAC,MAAA;cAAA;cACAC,OAAA;cAAA;cACAC,SAAA;gBACAlD,KAAA;cACA;YACA;UACA;UACAmD,KAAA;YACAJ,MAAA;cACA;cACAd,SAAA;cAAA;cACAmB,OAAA;cAAA;cACAZ,IAAA;gBACAa,CAAA;kBACArD,KAAA;kBACAsD,UAAA;kBAAA;kBACAC,KAAA;gBACA;gBACAC,EAAA;kBACA;kBACAC,WAAA;kBAAA;kBACAf,KAAA;kBACAgB,WAAA;kBACAC,MAAA;gBACA;gBACAC,GAAA;kBACA;kBACAR,OAAA;kBACA;kBACApD,KAAA,WAAAA,MAAAkC,MAAA;oBACA2B,OAAA,CAAAC,GAAA,CAAA5B,MAAA;oBACA;oBACA,OAAAA,MAAA,CAAAlC,KAAA;kBACA;gBACA;cACA;YACA;UACA;UACA+D,QAAA;YACAC,SAAA;cACAC,UAAA;cACAC,aAAA;cACAC,WAAA;YACA;UACA;QACA;MAEA;MACA;MACAC,sBAAA;QACA1D,OAAA;UACAC,OAAA;UACAsB,SAAA,WAAAA,UAAAC,MAAA;YACA,WAAAC,MAAA,CAAAD,MAAA,CAAA1C,IAAA,OAAA2C,MAAA,CAAAD,MAAA,CAAAE,OAAA;UACA;QACA;QACApC,KAAA;QACAc,MAAA;UACAwB,MAAA;UACAtB,KAAA;UACAM,MAAA;UACAL,SAAA;UACAC,UAAA;UACAqB,SAAA;YACAvC,KAAA;UACA;UACAiC,SAAA,WAAAA,UAAAzC,IAAA;YACA,UAAA2C,MAAA,CAAA3C,IAAA;UACA;QACA;QACAuC,MAAA,GACA;UACAlB,IAAA;UACAgC,MAAA;UACA;UACAlD,IAAA;UACAmD,SAAA;YACA;YACAC,MAAA;cACAC,MAAA;cAAA;cACAC,OAAA;cAAA;cACAC,SAAA;gBACAlD,KAAA;cACA;YACA;UACA;UACAmD,KAAA;YACAJ,MAAA;cACA;cACAd,SAAA;cAAA;cACAmB,OAAA;cAAA;cACAZ,IAAA;gBACAa,CAAA;kBACArD,KAAA;kBACAsD,UAAA;kBAAA;kBACAC,KAAA;gBACA;gBACAC,EAAA;kBACA;kBACAC,WAAA;kBAAA;kBACAf,KAAA;kBACAgB,WAAA;kBACAC,MAAA;gBACA;gBACAC,GAAA;kBACA;kBACAR,OAAA;gBACA;cACA;YACA;UACA;UACAW,QAAA;YACAC,SAAA;cACAC,UAAA;cACAC,aAAA;cACAC,WAAA;YACA;UACA;QACA;MAEA;MACA;MACAE,oBAAA;MACAC,qBAAA;QACA5D,OAAA;UACAC,OAAA;UACAC,WAAA;YACAC,IAAA;YACA0D,UAAA;cACAvE,KAAA;YACA;UACA;QACA;QACAc,MAAA;UACAC,GAAA;UACAC,KAAA;UACAC,SAAA;UACAC,UAAA;UACAC,OAAA;QACA;QACAnB,KAAA;QACAwB,KAAA,GACA;UACAX,IAAA;UACAlB,IAAA;UACAiB,WAAA;YACAC,IAAA;UACA;UACAY,QAAA;YACAC,IAAA;UACA;UACAC,QAAA;YACAD,IAAA;UACA;QACA,EACA;QACAE,KAAA,GACA;UACAf,IAAA;UACA;UACA;UACA;UACA;UACA;UACA;QACA,GACA;UACAA,IAAA;UACA;UACA;UACA;UACA2D,SAAA;YACAvC,SAAA;UACA;QACA,EACA;QACAF,MAAA,GACA;UACAvC,IAAA;UACAqB,IAAA;UACA4D,QAAA;UACAC,KAAA;UACAhE,OAAA;YACAiE,cAAA,WAAAA,eAAA5E,KAAA;cACA,OAAAA,KAAA;YACA;UACA;UACAJ,IAAA;QACA,GACA;UACAH,IAAA;UACAqB,IAAA;UACA4D,QAAA;UACAC,KAAA;UACAhE,OAAA;YACAiE,cAAA,WAAAA,eAAA5E,KAAA;cACA,OAAAA,KAAA;YACA;UACA;UACAJ,IAAA;QACA,GACA;UACAH,IAAA;UACAqB,IAAA;UACA+D,MAAA;UACAC,MAAA;UACAC,UAAA;UACApE,OAAA;YACAiE,cAAA,WAAAA,eAAA5E,KAAA;cACA,OAAAA,KAAA;YACA;UACA;UACAJ,IAAA;QACA;MAEA;MACA;MACAoF,wBAAA;MACAC,yBAAA;QACAjD,MAAA;MACA;MACA;MACAkD,sBAAA;MACAC,6BAAA;QACAxE,OAAA;UACAgB,IAAA;QACA;QACAK,MAAA,GACA;UACA;UACAoD,MAAA;UACAtE,IAAA;UACAuE,MAAA;UACAC,UAAA;UACAC,QAAA;UACAC,SAAA;UACA1C,MAAA;UACA2C,WAAA;UACAC,iBAAA;UACAhE,QAAA;YACAC,IAAA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA;UACAsC,SAAA;YACAhE,KAAA;UACA;UACA0F,QAAA;YACAhE,IAAA;UACA;UACAC,QAAA;YACAD,IAAA;YACA8D,WAAA;YACAG,QAAA;YACAzC,SAAA;cACAlD,KAAA;cACA0C,KAAA;YACA;YACAM,MAAA;UACA;UAAA;UACA4C,SAAA;YACAlE,IAAA;UACA;UACA8C,SAAA;YACA9C,IAAA;UACA;UACAmE,OAAA;YACAnE,IAAA;UACA;UACAoE,KAAA;YACApE,IAAA;UACA;UACAqE,MAAA;YACArE,IAAA;UACA;QACA,GACA;UACA;UACAb,IAAA;UACAsE,MAAA;UACAa,MAAA;UACAnD,MAAA;UACA4C,iBAAA;UACAL,MAAA;UACApB,SAAA;YACAhE,KAAA;cACAa,IAAA;cACAoF,CAAA;cACAC,CAAA;cACAC,EAAA;cACAC,EAAA;cACAC,UAAA,GACA;gBACAC,MAAA;gBACAtG,KAAA;cACA,GACA;gBACAsG,MAAA;gBACAtG,KAAA;cACA;YAEA;YACAyD,WAAA;UACA;UACAN,KAAA;YACAzB,IAAA;YACAG,QAAA;YACAI,SAAA,WAAAA,UAAAsE,MAAA;cACA;YACA;YACAC,QAAA;YACAxG,KAAA;UACA;UACA8C,SAAA;YACApB,IAAA;UACA;UACA/B,IAAA;QACA;MAEA;MACA;MACA8G,4CAAA;MACA;MACAC,2BAAA;IACA;EACA;EACAC,SAAA,WAAAA,UAAA;EACAC,aAAA,WAAAA,cAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IAEA,KAAAF,UAAA;EACA;EACAG,OAAA;IACA;IACAC,UAAA,WAAAA,WAAApG,IAAA,EAAAqG,GAAA,EAAAC,SAAA;MACA,KAAAC,KAAA,CAAA7H,UAAA,CAAA8H,UAAA,CAAAxG,IAAA,EAAAqG,GAAA,EAAAC,SAAA;IACA;IACA;IACAJ,QAAA,WAAAA,SAAA;MACA;MACA,KAAAO,sBAAA;MACA;MACA,KAAAC,oBAAA;MACA;MACA,KAAAC,sBAAA;MACA;MACA,KAAAC,yBAAA;MACA;MACA,KAAAC,gBAAA;;MAEA;MACA,KAAAC,eAAA;MACA;MACA,KAAAC,iBAAA;MACA;MACA,KAAAC,qBAAA;;MAEA;MACA,KAAAC,uBAAA;MACA;MACA,KAAAC,2BAAA;IACA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAA5H,aAAA;MACA,KAAAC,cAAA,GAAA5B,KAAA,KAAA6B,IAAA,IAAAC,MAAA;MACA,KAAAwG,QAAA;IACA;IACA;IACAkB,oBAAA,WAAAA,qBAAAC,CAAA;MACA,IAAAA,CAAA;QACA,KAAA9H,aAAA;QACA,KAAAC,cAAA,GAAA5B,KAAA,KAAA6B,IAAA,IAAAC,MAAA;MACA,WAAA2H,CAAA;QACA,KAAA9H,aAAA;QACA,KAAAC,cAAA,GAAA5B,KAAA,KAAA6B,IAAA,IAAAC,MAAA;MACA;MACA,KAAAwG,QAAA;IACA;IACA;IACAoB,qBAAA,WAAAA,sBAAA;MACA,KAAApB,QAAA;IACA;IACA;IACAO,sBAAA,WAAAA,uBAAA;MAAA,IAAAc,KAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACA/K,sBAAA;gBACAgL,KAAA;kBACAzI,IAAA;kBACA0I,UAAA;kBACAC,UAAA;kBACAC,WAAA;kBACAC,YAAA;kBACAC,KAAA;kBACAC,kBAAA;kBACAC,eAAA;kBACAC,eAAA;kBACAC,IAAA;kBACA3I,IAAA;gBACA;gBACA4I,QAAA;kBACAC,IAAA;kBACAC,QAAA;kBACAC,QAAA;kBACAC,SAAA;gBACA;cACA;YAAA;cApBApB,GAAA,GAAAG,QAAA,CAAAkB,IAAA;cAqBA1B,KAAA,CAAAnI,wBAAA,GAAAwI,GAAA,CAAAsB,IAAA,CAAAA,IAAA;YAAA;YAAA;cAAA,OAAAnB,QAAA,CAAAoB,IAAA;UAAA;QAAA,GAAAxB,OAAA;MAAA;IACA;IACA;IACAf,yBAAA,WAAAA,0BAAA;MAAA,IAAAwC,MAAA;MAAA,OAAA5B,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA2B,SAAA;QAAA,IAAAzB,GAAA,EAAA0B,OAAA,EAAAC,UAAA;QAAA,OAAA9B,mBAAA,GAAAI,IAAA,UAAA2B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzB,IAAA,GAAAyB,SAAA,CAAAxB,IAAA;YAAA;cAAAwB,SAAA,CAAAxB,IAAA;cAAA,OACA5K,yBAAA;gBACAqM,aAAA;gBACAC,QAAA,EAAAP,MAAA,CAAA9J,cAAA;gBACAsK,SAAA,EAAAR,MAAA,CAAAS,YAAA,CAAAT,MAAA,CAAA9J,cAAA;cACA;YAAA;cAJAsI,GAAA,GAAA6B,SAAA,CAAAR,IAAA;cAKAG,MAAA,CAAAlF,wBAAA,GAAA0D,GAAA,CAAAsB,IAAA;cACAI,OAAA,GAAA1B,GAAA,CAAAsB,IAAA,CAAAY,GAAA;cACAP,UAAA;cACA,IAAAD,OAAA;gBACAC,UAAA;cACA,WAAAD,OAAA,QAAAA,OAAA;gBACAC,UAAA;cACA,WAAAD,OAAA,QAAAA,OAAA;gBACAC,UAAA;cACA,WAAAD,OAAA;gBACAC,UAAA;cACA;cACAH,MAAA,CAAAjF,yBAAA,CAAAjD,MAAA,IACA;gBACAvC,IAAA;gBACAqB,IAAA;gBACAgC,MAAA;gBACA2C,WAAA;gBACAoF,GAAA;gBACAC,GAAA;gBACAxF,UAAA;gBACAC,QAAA;gBACA7D,QAAA;kBACAqJ,QAAA;kBACA5H,SAAA;oBACAR,KAAA;oBACAqI,OAAA;kBACA;gBACA;gBACAvG,SAAA;kBACA9C,IAAA;gBACA;gBACAC,QAAA;kBACAD,IAAA;gBACA;gBACAkE,SAAA;kBACAlE,IAAA;kBACAsB,MAAA;kBACAE,SAAA;oBACAlD,KAAA;oBACA0C,KAAA;kBACA;gBACA;gBACAqD,MAAA;kBACArE,IAAA;gBACA;gBACAmE,OAAA;kBACAnE,IAAA;gBACA;cACA,GACA;gBACAlC,IAAA;gBACAqB,IAAA;gBACAgC,MAAA;gBACA2C,WAAA;gBACAoF,GAAA;gBACAC,GAAA;gBACAxF,UAAA;gBACAC,QAAA;gBACAQ,KAAA;kBACApE,IAAA;kBACA8E,QAAA;kBACAxG,KAAA;kBACAgL,YAAA;gBACA;gBACArL,IAAA,GACA;kBACAI,KAAA;kBACAP,IAAA;gBACA,EACA;gBACAuG,MAAA;kBACAkF,cAAA;kBACAhJ,SAAA,WAAAA,UAAA;oBACA,OAAAmI,UAAA;kBACA;kBACA5D,QAAA;kBACAxG,KAAA;kBACAgL,YAAA;gBACA;gBACAvJ,QAAA;kBACAqJ,QAAA;kBACA5H,SAAA;oBACAR,KAAA;oBACA1C,KAAA,GACA,CACAmK,OAAA,aACA;sBACAtJ,IAAA;sBACAoF,CAAA;sBACAC,CAAA;sBACAC,EAAA;sBACAC,EAAA;sBACAC,UAAA,GACA;wBACAC,MAAA;wBACAtG,KAAA;sBACA,GACA;wBACAsG,MAAA;wBACAtG,KAAA;sBACA;oBAEA,EACA,EACA;kBAEA;gBACA;gBACAwE,SAAA;kBACA9C,IAAA;gBACA;gBACAC,QAAA;kBACAD,IAAA;gBACA;gBACAkE,SAAA;kBACAlE,IAAA;gBACA;gBACAmE,OAAA;kBACAnE,IAAA;gBACA;cACA,EACA;YAAA;YAAA;cAAA,OAAA4I,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IACA;IACAxC,gBAAA,WAAAA,iBAAA;MAAA,IAAAwD,MAAA;MAAA,OAAA7C,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA4C,SAAA;QAAA,IAAA1C,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA0C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxC,IAAA,GAAAwC,SAAA,CAAAvC,IAAA;YAAA;cAAAuC,SAAA,CAAAvC,IAAA;cAAA,OACA3K,gBAAA;gBACAoM,aAAA;gBACAC,QAAA,EAAAU,MAAA,CAAA/K,cAAA;gBACAsK,SAAA,EAAAS,MAAA,CAAAR,YAAA,CAAAQ,MAAA,CAAA/K,cAAA;cACA;YAAA;cAJAsI,GAAA,GAAA4C,SAAA,CAAAvB,IAAA;cAKAoB,MAAA,CAAAzE,4CAAA,GAAAgC,GAAA,CAAAsB,IAAA;YAAA;YAAA;cAAA,OAAAsB,SAAA,CAAArB,IAAA;UAAA;QAAA,GAAAmB,QAAA;MAAA;IACA;IACA;IACAxD,eAAA,WAAAA,gBAAA;MAAA,IAAA2D,MAAA;MAAA,OAAAjD,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAgD,SAAA;QAAA,IAAA9C,GAAA,EAAAzG,+BAAA;QAAA,OAAAsG,mBAAA,GAAAI,IAAA,UAAA8C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5C,IAAA,GAAA4C,SAAA,CAAA3C,IAAA;YAAA;cAAA2C,SAAA,CAAA3C,IAAA;cAAA,OACA1K,eAAA;gBACAmM,aAAA;gBACAC,QAAA,EAAAc,MAAA,CAAAnL,cAAA;gBACAsK,SAAA,EAAAa,MAAA,CAAAZ,YAAA,CAAAY,MAAA,CAAAnL,cAAA;cACA;YAAA;cAJAsI,GAAA,GAAAgD,SAAA,CAAA3B,IAAA;cAKAjG,OAAA,CAAAC,GAAA,CAAA2E,GAAA;cACAzG,+BAAA,GAAAyG,GAAA,CAAAsB,IAAA,CAAA2B,GAAA,WAAAC,IAAA;gBAAA;kBACAnM,IAAA,EAAAmM,IAAA,CAAAC,KAAA;kBACA7L,KAAA,EAAA4L,IAAA,CAAAE,KAAA;kBACAzJ,OAAA,EAAAuJ,IAAA,CAAAG;gBACA;cAAA;cACAR,MAAA,CAAAtJ,+BAAA,CAAAD,MAAA,IAAApC,IAAA,GACAqC,+BAAA;cACAsJ,MAAA,CAAAtJ,+BAAA,CAAAlB,MAAA,CAAAmB,SAAA,aAAAzC,IAAA;gBACA,IAAAuM,GAAA,GAAA/J,+BAAA,CAAAgK,IAAA,CACA,UAAAL,IAAA;kBAAA,OAAAA,IAAA,CAAAnM,IAAA,IAAAA,IAAA;gBAAA,CACA;gBACA,qBAAA2C,MAAA,CAAA4J,GAAA,CAAAvM,IAAA,mBAAA2C,MAAA,CAAA4J,GAAA,CAAAhM,KAAA,6BAAAoC,MAAA,CAAA4J,GAAA,CAAA3J,OAAA;cACA;YAAA;YAAA;cAAA,OAAAqJ,SAAA,CAAAzB,IAAA;UAAA;QAAA,GAAAuB,QAAA;MAAA;IACA;IACA;IACA1D,qBAAA,WAAAA,sBAAA;MAAA,IAAAoE,MAAA;MAAA,OAAA5D,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA2D,SAAA;QAAA,IAAAzD,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAyD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvD,IAAA,GAAAuD,SAAA,CAAAtD,IAAA;YAAA;cAAAsD,SAAA,CAAAtD,IAAA;cAAA,OACAxK,qBAAA;gBACAiM,aAAA;gBACAC,QAAA,EAAAyB,MAAA,CAAA9L,cAAA;gBACAsK,SAAA,EAAAwB,MAAA,CAAAvB,YAAA,CAAAuB,MAAA,CAAA9L,cAAA;cACA;YAAA;cAJAsI,GAAA,GAAA2D,SAAA,CAAAtC,IAAA;cAKAmC,MAAA,CAAA7H,sBAAA,CAAArC,MAAA,IAAApC,IAAA,GAAA8I,GAAA,CAAAsB,IAAA,CAAA2B,GAAA,WAAAC,IAAA;gBAAA;kBACAnM,IAAA,EAAAmM,IAAA,CAAAC,KAAA;kBACA7L,KAAA,EAAA4L,IAAA,CAAAE,KAAA;kBACAzJ,OAAA,EAAAuJ,IAAA,CAAAG;gBACA;cAAA;YAAA;YAAA;cAAA,OAAAM,SAAA,CAAApC,IAAA;UAAA;QAAA,GAAAkC,QAAA;MAAA;IACA;IACA;IACAtE,iBAAA,WAAAA,kBAAA;MAAA,IAAAyE,MAAA;MAAA,OAAAhE,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA+D,SAAA;QAAA,IAAA7D,GAAA,EAAA8D,SAAA;QAAA,OAAAjE,mBAAA,GAAAI,IAAA,UAAA8D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,IAAA,GAAA4D,SAAA,CAAA3D,IAAA;YAAA;cAAA2D,SAAA,CAAA3D,IAAA;cAAA,OACAzK,iBAAA;gBACAkM,aAAA;gBACAC,QAAA,EAAA6B,MAAA,CAAAlM,cAAA;gBACAsK,SAAA,EAAA4B,MAAA,CAAA3B,YAAA,CAAA2B,MAAA,CAAAlM,cAAA;cACA;YAAA;cAJAsI,GAAA,GAAAgE,SAAA,CAAA3C,IAAA;cAKAyC,SAAA;cACAF,MAAA,CAAA5L,yCAAA,CAAAsB,MAAA,GAAA0G,GAAA,CAAAsB,IAAA,CAAA2B,GAAA,CACA,UAAAC,IAAA;gBACAY,SAAA,GAAAZ,IAAA,CAAAe,QAAA,CAAAhB,GAAA,WAAAiB,GAAA;kBAAA,OAAAA,GAAA,CAAAf,KAAA;gBAAA;gBACA,IAAAD,IAAA,CAAAiB,QAAA;kBACA;oBACApN,IAAA,EAAAmM,IAAA,CAAAiB,QAAA;oBACA/L,IAAA;oBACA+D,MAAA;oBACAC,MAAA;oBACAlF,IAAA,EAAAgM,IAAA,CAAAe,QAAA,CAAAhB,GAAA,WAAAiB,GAAA;sBAAA,OAAAA,GAAA,CAAAd,KAAA;oBAAA;kBACA;gBACA;kBACA;oBACArM,IAAA,EAAAmM,IAAA,CAAAiB,QAAA;oBACA/L,IAAA;oBACA6D,KAAA;oBACAX,QAAA;sBACA8I,KAAA;oBACA;oBACAlN,IAAA,EAAAgM,IAAA,CAAAe,QAAA,CAAAhB,GAAA,WAAAiB,GAAA;sBAAA,OAAAA,GAAA,CAAAd,KAAA;oBAAA;kBACA;gBACA;cACA,CACA;cACAQ,MAAA,CAAA5L,yCAAA,CAAAe,KAAA,CAAA7B,IAAA,GAAA4M,SAAA;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAAzC,IAAA;UAAA;QAAA,GAAAsC,QAAA;MAAA;IACA;IAEA;IACAvE,2BAAA,WAAAA,4BAAA;MAAA,IAAA+E,MAAA;MAAA,OAAAzE,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAwE,SAAA;QAAA,IAAAtE,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAsE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApE,IAAA,GAAAoE,SAAA,CAAAnE,IAAA;YAAA;cAAAmE,SAAA,CAAAnE,IAAA;cAAA,OACAtK,2BAAA;gBACA+L,aAAA;gBACAC,QAAA,EAAAsC,MAAA,CAAA3M,cAAA;gBACAsK,SAAA,EAAAqC,MAAA,CAAApC,YAAA,CAAAoC,MAAA,CAAA3M,cAAA;cACA;YAAA;cAJAsI,GAAA,GAAAwE,SAAA,CAAAnD,IAAA;cAKAgD,MAAA,CAAA7H,sBAAA,GAAAwD,GAAA,CAAAsB,IAAA;cACA+C,MAAA,CAAA5H,6BAAA,CAAAnD,MAAA,IAAAN,QAAA,CAAAyB,SAAA;gBACAlD,KAAA,GACA,CAAAyI,GAAA,CAAAsB,IAAA,CAAAmD,kBAAA,mCACA,eACA;gBACAxK,KAAA;cACA;cACAoK,MAAA,CAAA5H,6BAAA,CAAAnD,MAAA,IAAAoB,KAAA,CAAAlB,SAAA,aACAsE,MAAA,EACA;gBACA,UAAApE,MAAA,CAAAsG,GAAA,CAAAsB,IAAA,CAAAmD,kBAAA;cACA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAjD,IAAA;UAAA;QAAA,GAAA+C,QAAA;MAAA;IACA;IACA;IACAjF,uBAAA,WAAAA,wBAAA;MAAA,IAAAqF,MAAA;MAAA,OAAA9E,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA6E,SAAA;QAAA,IAAA3E,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA2E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzE,IAAA,GAAAyE,SAAA,CAAAxE,IAAA;YAAA;cAAAwE,SAAA,CAAAxE,IAAA;cAAA,OACAvK,uBAAA;gBACAgM,aAAA;gBACAC,QAAA,EAAA2C,MAAA,CAAAhN,cAAA;gBACAsK,SAAA,EAAA0C,MAAA,CAAAzC,YAAA,CAAAyC,MAAA,CAAAhN,cAAA;cACA;YAAA;cAJAsI,GAAA,GAAA6E,SAAA,CAAAxD,IAAA;cAKAqD,MAAA,CAAAzG,2BAAA,GAAA+B,GAAA,CAAAsB,IAAA;YAAA;YAAA;cAAA,OAAAuD,SAAA,CAAAtD,IAAA;UAAA;QAAA,GAAAoD,QAAA;MAAA;IACA;IAEA;IACA7F,oBAAA,WAAAA,qBAAA;MAAA,IAAAgG,MAAA;MAAA,OAAAlF,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAiF,SAAA;QAAA,IAAA/E,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA+E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7E,IAAA,GAAA6E,SAAA,CAAA5E,IAAA;YAAA;cAAA4E,SAAA,CAAA5E,IAAA;cAAA,OACA7K,oBAAA;gBACAsM,aAAA;gBACAC,QAAA,EAAA+C,MAAA,CAAApN,cAAA;gBACAsK,SAAA,EAAA8C,MAAA,CAAA7C,YAAA,CAAA6C,MAAA,CAAApN,cAAA;cACA;YAAA;cAJAsI,GAAA,GAAAiF,SAAA,CAAA5D,IAAA;cAKAyD,MAAA,CAAAlJ,oBAAA,GAAAoE,GAAA,CAAAsB,IAAA;cACAwD,MAAA,CAAAjJ,qBAAA,CAAA9C,KAAA,IAAA7B,IAAA,GAAA8I,GAAA,CAAAsB,IAAA,CAAA4D,IAAA,CAAAjC,GAAA,CACA,UAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAArL,IAAA;cAAA,CACA;cACAiN,MAAA,CAAAjJ,qBAAA,CAAAvC,MAAA,IAAApC,IAAA,GAAA8I,GAAA,CAAAsB,IAAA,CAAA4D,IAAA,CAAAjC,GAAA,CACA,UAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAiC,MAAA;cAAA,CACA;cACAL,MAAA,CAAAjJ,qBAAA,CAAAvC,MAAA,IAAApC,IAAA,GAAA8I,GAAA,CAAAsB,IAAA,CAAA4D,IAAA,CAAAjC,GAAA,CACA,UAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAkC,OAAA;cAAA,CACA;cACAN,MAAA,CAAAjJ,qBAAA,CAAAvC,MAAA,IAAApC,IAAA,GAAA8I,GAAA,CAAAsB,IAAA,CAAA4D,IAAA,CAAAjC,GAAA,CACA,UAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAmC,OAAA;cAAA,CACA;YAAA;YAAA;cAAA,OAAAJ,SAAA,CAAA1D,IAAA;UAAA;QAAA,GAAAwD,QAAA;MAAA;IACA;IACA;IACAhG,sBAAA,WAAAA,uBAAA;MAAA,IAAAuG,OAAA;MAAA,OAAA1F,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAyF,UAAA;QAAA,IAAAvF,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAuF,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArF,IAAA,GAAAqF,UAAA,CAAApF,IAAA;YAAA;cAAAoF,UAAA,CAAApF,IAAA;cAAA,OACA9K,sBAAA;gBACAuM,aAAA;gBACAC,QAAA,EAAAuD,OAAA,CAAA5N,cAAA;gBACAsK,SAAA,EAAAsD,OAAA,CAAArD,YAAA,CAAAqD,OAAA,CAAA5N,cAAA;cACA;YAAA;cAJAsI,GAAA,GAAAyF,UAAA,CAAApE,IAAA;cAKAiE,OAAA,CAAA7N,cAAA,GAAAuI,GAAA,CAAAsB,IAAA;YAAA;YAAA;cAAA,OAAAmE,UAAA,CAAAlE,IAAA;UAAA;QAAA,GAAAgE,SAAA;MAAA;IACA;IACA;IACAG,cAAA,WAAAA,eAAAC,IAAA;MACA,IAAAC,SAAA,OAAA/N,IAAA,CAAA8N,IAAA;MACA,IAAAE,OAAA,OAAAhO,IAAA;MACA,IAAAiO,UAAA,GAAAC,IAAA,CAAAC,GAAA,CAAAH,OAAA,GAAAD,SAAA;MACA,IAAAK,IAAA,GAAAF,IAAA,CAAAG,KAAA,CAAAJ,UAAA;MACA,IAAAK,KAAA,GAAAJ,IAAA,CAAAG,KAAA,CACAJ,UAAA,2CACA;MACA,IAAAM,OAAA,GAAAL,IAAA,CAAAG,KAAA,CAAAJ,UAAA;MACA,IAAAO,mBAAA;MAEA,IAAAJ,IAAA;QACAI,mBAAA,IAAAJ,IAAA;MACA;MACA,IAAAE,KAAA;QACAE,mBAAA,IAAAF,KAAA;MACA;MACA,IAAAC,OAAA,QAAAH,IAAA,UAAAE,KAAA;QACAE,mBAAA,IAAAD,OAAA;MACA;MACA,OAAAC,mBAAA;IACA;IACA;IACAC,cAAA,WAAAA,eAAA7H,GAAA;MACA,YAAArH,0BAAA,CAAAmM,IAAA,CACA,UAAAL,IAAA;QAAA,OAAAA,IAAA,CAAA5L,KAAA,IAAAmH,GAAA,CAAAkC,KAAA;MAAA,CACA;IACA;IACA;IACAsB,YAAA,WAAAA,aAAA7J,IAAA;MACA,IAAAA,IAAA;QACA,OAAApC,KAAA,MAAA4B,cAAA,EAAAE,MAAA;MACA,WAAAM,IAAA;QACA,OAAApC,KAAA,MAAA4B,cAAA,EAAAE,MAAA;MACA;IACA;IACA;IACAsG,UAAA,WAAAA,WAAAmD,IAAA;MACA,IAAAgF,KAAA,QAAA5H,KAAA,CAAA6H,YAAA;MACA;MACA,IAAAC,OAAA,GAAAF,KAAA,CAAA5H,KAAA,CAAA+H,WAAA;MACA;MACA,IAAAnF,IAAA;QACA;QACAoF,MAAA,CAAAC,aAAA,MAAA7O,WAAA;MACA;QACA,KAAAA,WAAA,GAAA4O,MAAA,CAAAE,WAAA;UACA;UACAJ,OAAA,CAAAK,SAAA;UACA;UACA,IACAL,OAAA,CAAAM,YAAA,GAAAN,OAAA,CAAAK,SAAA,IACAL,OAAA,CAAAO,YAAA,EACA;YACA;YACAP,OAAA,CAAAK,SAAA;YACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAG,qDAAA,WAAAA,sDAAAC,IAAA;MAAA,IAAAzI,GAAA,GAAAyI,IAAA,CAAAzI,GAAA;QAAA0I,QAAA,GAAAD,IAAA,CAAAC,QAAA;MACA,SAAAC,WAAA,CAAAD,QAAA;QACA;MACA;QACA;MACA;IACA;IACA;IACAE,iCAAA,WAAAA,kCAAAC,KAAA;MAAA,IAAA7I,GAAA,GAAA6I,KAAA,CAAA7I,GAAA;QAAA0I,QAAA,GAAAG,KAAA,CAAAH,QAAA;MACA,SAAAC,WAAA,CAAAD,QAAA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAAG,MAAA;MACA,IAAAA,MAAA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA;MACA;MACA;MACA,IAAAC,QAAA;MACA,IAAAC,QAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,IAAAC,UAAA,GAAAF,YAAA,CAAAC,OAAA;MACA;MACA,IAAAE,YAAA,GAAA9R,KAAA,GAAA+R,OAAA;MACA;MACA,IAAAC,UAAA,GAAAhS,KAAA,GAAAiS,KAAA;MACA;MACA;MACA;MACA,KAAAC,QAAA,CAAAC,gBAAA,CACAV,QAAA,EACA,OACA,iIAEA;IACA;EACA;AACA", "ignoreList": []}]}