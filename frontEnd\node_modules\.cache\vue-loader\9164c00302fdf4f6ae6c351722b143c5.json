{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\taskSet\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\taskSet\\index.vue", "mtime": 1755506574326}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/business/eventManagement/taskSet", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout\r\n      :layoutConfig=\"{\r\n        isShowSearchForm: false,\r\n      }\"\r\n    >\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport DialogForm from \"./dialogForm.vue\";\r\n\r\nimport {\r\n  GetTaskConfigPageList,\r\n  EnableMobileMessageNotice,\r\n  EnableSiteNotice,\r\n  EnableTaskConfig,\r\n  GetNoticeDropDownOption,\r\n} from \"@/api/business/eventManagement\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        Data: {},\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"访客管理审核\",\r\n      tableSelection: [],\r\n      ruleForm: {},\r\n      customForm: {\r\n        formItems: [],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"启用\",\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载�?\n              disabled: true, // 是否禁用\r\n              icon: \"\", //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleEnable();\r\n              },\r\n            },\r\n            {\r\n              text: \"关闭\",\r\n              type: \"danger\",\r\n              disabled: true, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleClose();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            otherOptions: {\r\n              type: \"selection\",\r\n              align: \"center\",\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"任务名称\",\r\n            key: \"Name\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"任务层级\",\r\n            key: \"SourceTypeDisplay\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"业务模块\",\r\n            key: \"Module\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"任务类型\",\r\n            key: \"TaskType\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"触发任务条件\",\r\n            key: \"TriggerConditionDescription\",\r\n            width: 180,\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"任务时长\",\r\n            key: \"Duration\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"单位\",\r\n            key: \"DurationUnitDisplay\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"规则描述\",\r\n            key: \"RuleDescription\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"站内通知\",\r\n            key: \"EnableSiteNotice\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement(\"el-switch\", {\r\n                props: {\r\n                  value: row.EnableSiteNotice,\r\n                  \"active-color\": \"#13ce66\",\r\n                },\r\n                on: {\r\n                  change: (e) => {\r\n                    this.handleSiteNoticeCloseEnable(row.Id, e);\r\n                  },\r\n                },\r\n              });\r\n            },\r\n          },\r\n          {\r\n            label: \"短信通知\",\r\n            key: \"EnableMobileMessageNotice\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement(\"el-switch\", {\r\n                props: {\r\n                  value: row.EnableMobileMessageNotice,\r\n                  \"active-color\": \"#13ce66\",\r\n                },\r\n                on: {\r\n                  change: (e) => {\r\n                    this.handleMobileMessageCloseEnable(row.Id, e);\r\n                  },\r\n                },\r\n              });\r\n            },\r\n          },\r\n          {\r\n            label: \"指派角色\",\r\n            key: \"Roles\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement(\r\n                \"span\",\r\n                {\r\n                  style: {\r\n                    color: \"#3582fb\",\r\n                  },\r\n                  on: {\r\n                    click: () => {\r\n                      this.handleEdit(row, \"role\");\r\n                    },\r\n                  },\r\n                },\r\n                row.Roles.join(\",\") || \"添加\"\r\n              );\r\n            },\r\n          },\r\n          {\r\n            label: \"指派�?,\r\n            key: \"Users\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement(\r\n                \"span\",\r\n                {\r\n                  style: {\r\n                    color: \"#3582fb\",\r\n                  },\r\n                  on: {\r\n                    click: () => {\r\n                      this.handleEdit(row, \"user\");\r\n                    },\r\n                  },\r\n                },\r\n                row.Users.join(\",\") || \"添加\"\r\n              );\r\n            },\r\n          },\r\n          {\r\n            label: \"执行周期\",\r\n            key: \"InformCycleDisplay\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"上次执行时间\",\r\n            key: \"LastInformTime\",\r\n            width: 180,\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"启用状�?,\r\n            key: \"Enable\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement(\"el-switch\", {\r\n                props: {\r\n                  value: row.Enable,\r\n                  \"active-color\": \"#13ce66\",\r\n                },\r\n                on: {\r\n                  change: (e) => {\r\n                    this.handleCloseEnable(row.Id, e);\r\n                  },\r\n                },\r\n              });\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: \"center\",\r\n        },\r\n        tableActions: [],\r\n      },\r\n    };\r\n  },\r\n  watch: {\r\n    tableSelection: {\r\n      handler(newval, oldval) {\r\n        console.log(newval);\r\n        if (newval.length > 0) {\r\n          this.customTableConfig.buttonConfig.buttonList.forEach((ele) => {\r\n            ele.disabled = false;\r\n          });\r\n        } else {\r\n          this.customTableConfig.buttonConfig.buttonList.forEach((ele) => {\r\n            ele.disabled = true;\r\n          });\r\n        }\r\n      },\r\n    },\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init();\r\n  },\r\n  // mixins: [getGridByCode],\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1;\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.GetTaskConfigPageList();\r\n    },\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.GetTaskConfigPageList();\r\n      this.getNoticeDropDownOption();\r\n    },\r\n    async getNoticeDropDownOption() {\r\n      const res = await GetNoticeDropDownOption({});\r\n      if (res.IsSucceed) {\r\n        let result = res.Data || [];\r\n        let noticeType = [];\r\n        let noticeLevel = [];\r\n        result.forEach((element) => {\r\n          if (element.TypeName == \"通知类型\") {\r\n            noticeType = element.Data.map((item) => ({\r\n              value: item.Value,\r\n              label: item.Name,\r\n            }));\r\n          } else if (element.TypeName == \"发布层级\") {\r\n            noticeLevel = element.Data.map((item) => ({\r\n              value: item.Value,\r\n              label: item.Name,\r\n            }));\r\n          }\r\n        });\r\n        // this.customForm.formItems.find((item) => item.key == \"Type\").options =\r\n        //   noticeType;\r\n      }\r\n    },\r\n    async GetTaskConfigPageList() {\r\n      const res = await GetTaskConfigPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n        if (this.customTableConfig.tableData.length > 0) {\r\n        }\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n\r\n    async handleMobileMessageCloseEnable(id, status) {\r\n      const res = await EnableMobileMessageNotice({\r\n        Ids: [id],\r\n        Enable: status,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        this.init();\r\n      }\r\n    },\r\n\r\n    async handleSiteNoticeCloseEnable(id, status) {\r\n      const res = await EnableSiteNotice({\r\n        Ids: [id],\r\n        Enable: status,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        this.init();\r\n      }\r\n    },\r\n\r\n    async handleCloseEnable(id, status) {\r\n      const res = await EnableTaskConfig({\r\n        Ids: [id],\r\n        Enable: status,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        this.init();\r\n      }\r\n    },\r\n    handleEdit(row, type) {\r\n      console.log(row, \"row\");\r\n      this.dialogTitle = `访客管理审核�?{row.Name}）`;\r\n      this.dialogVisible = true;\r\n      this.componentsConfig = {\r\n        ID: row.Id,\r\n        type,\r\n        UserIds: row.UserIds,\r\n        Users: row.Users,\r\n        RoleIds: row.RoleIds,\r\n        Roles: row.Roles,\r\n      };\r\n    },\r\n    async handleEnable() {\r\n      const res = await EnableTaskConfig({\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n        Enable: true,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        this.init();\r\n      }\r\n    },\r\n\r\n    async handleClose() {\r\n      const res = await EnableTaskConfig({\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n        Enable: false,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        this.init();\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.GetTaskConfigPageList();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.GetTaskConfigPageList();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: 100%;\r\n  width: 100%;\r\n  position: absolute;\r\n  ::v-deep {\r\n    .CustomLayout {\r\n      .layoutTable {\r\n        height: 0;\r\n        .CustomTable {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          .table {\r\n            flex: 1;\r\n            height: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            .el-table {\r\n              flex: 1;\r\n              height: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}