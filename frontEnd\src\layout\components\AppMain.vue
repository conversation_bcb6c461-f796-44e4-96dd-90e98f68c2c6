<template>
  <section class="app-main">
    <!-- <transition name="fade-transform" mode="out-in">
      <keep-alive :max="10">
        <router-view :key="key" v-if="cachedViews.includes(name)" />
      </keep-alive>
    </transition>
    <transition name="fade-transform" mode="out-in">
      <router-view :key="key"  v-if="!cachedViews.includes(name)" />
    </transition> -->
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  components: {},
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px - 42px);
  width: 100%;
  position: relative;
  overflow: hidden;
  background: #f4f5f7;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 90px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
