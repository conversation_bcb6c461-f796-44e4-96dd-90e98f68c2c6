{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\alarmLinkageSettings\\playAudioSettings.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\alarmLinkageSettings\\playAudioSettings.vue", "mtime": 1755674552413}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "playAudioSettingsDialogForm", "downloadFile", "dayjs", "GetWarningSettingList", "EditWarningSetting", "GetWarningSettingEntity", "DeleteWarningSetting", "name", "components", "data", "_this", "currentComponent", "componentsConfig", "Data", "componentsFuns", "open", "dialogVisible", "close", "onFresh", "dialogTitle", "tableSelection", "customTableConfig", "buttonConfig", "buttonList", "text", "type", "onclick", "item", "console", "log", "handleCreate", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "label", "key", "otherOptions", "align", "render", "row", "MediaName", "$createElement", "style", "color", "tableData", "operateOptions", "width", "tableActions", "actionLabel", "index", "handleEdit", "handleDelete", "computed", "created", "init", "methods", "handleClose", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "SetWarningStatus", "Status", "Ids", "map", "Id", "sent", "IsSucceed", "$message", "success", "stop", "_this3", "_callee2", "_callee2$", "_context2", "Page", "PageSize", "TotalCount", "error", "Message", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "selection", "_this4", "$confirm", "then", "_ref", "_callee3", "_", "_callee3$", "_context3", "ID", "_x", "apply", "arguments", "catch"], "sources": ["src/views/business/behaviorAnalysis/alarmLinkageSettings/playAudioSettings.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <el-card class=\"box-card\">\r\n      <CustomTable\r\n        :custom-table-config=\"customTableConfig\"\r\n        @handleSizeChange=\"handleSizeChange\"\r\n        @handleCurrentChange=\"handleCurrentChange\"\r\n        @handleSelectionChange=\"handleSelectionChange\"\r\n      />\r\n    </el-card>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\n// import getGridByCode from \"../../safetyManagement/mixins/index\";\r\nimport playAudioSettingsDialogForm from \"./playAudioSettingsDialogForm.vue\";\r\n\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\nimport dayjs from \"dayjs\";\r\nimport {\r\n  GetWarningSettingList,\r\n  EditWarningSetting,\r\n  GetWarningSettingEntity,\r\n  DeleteWarningSetting,\r\n} from \"@/api/business/behaviorAnalysis\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: null,\r\n      componentsConfig: {\r\n        Data: {},\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"编辑\",\r\n      tableSelection: [],\r\n\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"新增\",\r\n              type: \"primary\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            label: \"报警类型\",\r\n            key: \"WarningTypeDes\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"联动广播文件\",\r\n            key: \"MediaName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              if (row.MediaName == \"文件失效\") {\r\n                return this.$createElement(\r\n                  \"span\",\r\n                  {\r\n                    style: {\r\n                      color: \"red\",\r\n                    },\r\n                  },\r\n                  row.MediaName\r\n                );\r\n              }\r\n              return this.$createElement(\"span\", {}, row.MediaName);\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: \"center\",\r\n          width: \"180\",\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"修改\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    async handleClose() {\r\n      const res = await SetWarningStatus({\r\n        Status: \"2\",\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.$message.success(\"操作成功\");\r\n        this.onFresh();\r\n      }\r\n    },\r\n\r\n    onFresh() {\r\n      this.GetWarningSettingList();\r\n    },\r\n\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.GetWarningSettingList();\r\n    },\r\n    async GetWarningSettingList() {\r\n      const res = await GetWarningSettingList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.GetWarningSettingList();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.GetWarningSettingList();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = \"新增告警设置\";\r\n      this.componentsConfig = {\r\n        type: \"add\",\r\n      };\r\n      this.dialogVisible = true;\r\n      this.currentComponent = playAudioSettingsDialogForm;\r\n    },\r\n    handleEdit(row) {\r\n      this.dialogVisible = true;\r\n      this.dialogTitle = \"编辑告警设置\";\r\n      this.currentComponent = playAudioSettingsDialogForm;\r\n      this.componentsConfig = {\r\n        type: \"edit\",\r\n        data: row,\r\n      };\r\n    },\r\n    handleDelete(index, row) {\r\n      this.$confirm(\"请确认是否删除？\", \"删除\", {\r\n        type: \"error\",\r\n      })\r\n        .then(async (_) => {\r\n          const res = await DeleteWarningSetting({\r\n            ID: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.init();\r\n          } else {\r\n            this.$message.error(res.Message);\r\n          }\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.box-card{\r\n  height: calc(100vh - 190px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAqBA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA;AACA,OAAAC,2BAAA;AAEA,SAAAC,YAAA;AACA,OAAAC,KAAA;AACA,SACAC,qBAAA,IAAAA,sBAAA,EACAC,kBAAA,EACAC,uBAAA,EACAC,oBAAA,QACA;AACA;EACAC,IAAA;EACAC,UAAA;IACAV,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAY,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,gBAAA;MACAC,gBAAA;QACAC,IAAA;MACA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAL,KAAA,CAAAM,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAP,KAAA,CAAAM,aAAA;UACAN,KAAA,CAAAQ,OAAA;QACA;MACA;MACAF,aAAA;MACAG,WAAA;MACAC,cAAA;MAEAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAC,IAAA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAAC,IAAA;cACAC,OAAA,CAAAC,GAAA,CAAAF,IAAA;cACAjB,KAAA,CAAAoB,YAAA;YACA;UACA;QAEA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA,GACA;UACAC,KAAA;UACAC,GAAA;UACAC,YAAA;YACAC,KAAA;UACA;QACA,GACA;UACAH,KAAA;UACAC,GAAA;UACAC,YAAA;YACAC,KAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,SAAA;cACA,OAAAhC,KAAA,CAAAiC,cAAA,CACA,QACA;gBACAC,KAAA;kBACAC,KAAA;gBACA;cACA,GACAJ,GAAA,CAAAC,SACA;YACA;YACA,OAAAhC,KAAA,CAAAiC,cAAA,aAAAF,GAAA,CAAAC,SAAA;UACA;QACA,EACA;QACAI,SAAA;QACAC,cAAA;UACAR,KAAA;UACAS,KAAA;QACA;QACAC,YAAA,GACA;UACAC,WAAA;UACAZ,YAAA;YACAb,IAAA;UACA;UACAC,OAAA,WAAAA,QAAAyB,KAAA,EAAAV,GAAA;YACA/B,KAAA,CAAA0C,UAAA,CAAAX,GAAA;UACA;QACA,GACA;UACAS,WAAA;UACAZ,YAAA;YACAb,IAAA;UACA;UACAC,OAAA,WAAAA,QAAAyB,KAAA,EAAAV,GAAA;YACA/B,KAAA,CAAA2C,YAAA,CAAAF,KAAA,EAAAV,GAAA;UACA;QACA;MAEA;IACA;EACA;EACAa,QAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAC,gBAAA;gBACAC,MAAA;gBACAC,GAAA,EAAAb,MAAA,CAAAvC,cAAA,CAAAqD,GAAA,WAAA9C,IAAA;kBAAA,OAAAA,IAAA,CAAA+C,EAAA;gBAAA;cACA;YAAA;cAHAV,GAAA,GAAAG,QAAA,CAAAQ,IAAA;cAIA,IAAAX,GAAA,CAAAY,SAAA;gBACAjB,MAAA,CAAAkB,QAAA,CAAAC,OAAA;gBACAnB,MAAA,CAAAzC,OAAA;cACA;YAAA;YAAA;cAAA,OAAAiD,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA;IACA;IAEA7C,OAAA,WAAAA,QAAA;MACA,KAAAf,qBAAA;IACA;IAEAqD,IAAA,WAAAA,KAAA;MACA;MACA,KAAArD,qBAAA;IACA;IACAA,qBAAA,WAAAA,sBAAA;MAAA,IAAA6E,MAAA;MAAA,OAAApB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAmB,SAAA;QAAA,IAAAjB,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAiB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAf,IAAA,GAAAe,SAAA,CAAAd,IAAA;YAAA;cAAAc,SAAA,CAAAd,IAAA;cAAA,OACAlE,sBAAA;gBACAiF,IAAA,EAAAJ,MAAA,CAAA3D,iBAAA,CAAAW,WAAA;gBACAqD,QAAA,EAAAL,MAAA,CAAA3D,iBAAA,CAAAY;cACA;YAAA;cAHA+B,GAAA,GAAAmB,SAAA,CAAAR,IAAA;cAIA,IAAAX,GAAA,CAAAY,SAAA;gBACAI,MAAA,CAAA3D,iBAAA,CAAAyB,SAAA,GAAAkB,GAAA,CAAAnD,IAAA,CAAAA,IAAA;gBACAmE,MAAA,CAAA3D,iBAAA,CAAAa,KAAA,GAAA8B,GAAA,CAAAnD,IAAA,CAAAyE,UAAA;cACA;gBACAN,MAAA,CAAAH,QAAA,CAAAU,KAAA,CAAAvB,GAAA,CAAAwB,OAAA;cACA;YAAA;YAAA;cAAA,OAAAL,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IAEAQ,gBAAA,WAAAA,iBAAAC,GAAA;MACA9D,OAAA,CAAAC,GAAA,iBAAA8D,MAAA,CAAAD,GAAA;MACA,KAAArE,iBAAA,CAAAY,QAAA,GAAAyD,GAAA;MACA,KAAAvF,qBAAA;IACA;IACAyF,mBAAA,WAAAA,oBAAAF,GAAA;MACA9D,OAAA,CAAAC,GAAA,wBAAA8D,MAAA,CAAAD,GAAA;MACA,KAAArE,iBAAA,CAAAW,WAAA,GAAA0D,GAAA;MACA,KAAAvF,qBAAA;IACA;IACA0F,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA1E,cAAA,GAAA0E,SAAA;IACA;IACAhE,YAAA,WAAAA,aAAA;MACA,KAAAX,WAAA;MACA,KAAAP,gBAAA;QACAa,IAAA;MACA;MACA,KAAAT,aAAA;MACA,KAAAL,gBAAA,GAAAX,2BAAA;IACA;IACAoD,UAAA,WAAAA,WAAAX,GAAA;MACA,KAAAzB,aAAA;MACA,KAAAG,WAAA;MACA,KAAAR,gBAAA,GAAAX,2BAAA;MACA,KAAAY,gBAAA;QACAa,IAAA;QACAhB,IAAA,EAAAgC;MACA;IACA;IACAY,YAAA,WAAAA,aAAAF,KAAA,EAAAV,GAAA;MAAA,IAAAsD,MAAA;MACA,KAAAC,QAAA;QACAvE,IAAA;MACA,GACAwE,IAAA;QAAA,IAAAC,IAAA,GAAAtC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAqC,SAAAC,CAAA;UAAA,IAAApC,GAAA;UAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAoC,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAlC,IAAA,GAAAkC,SAAA,CAAAjC,IAAA;cAAA;gBAAAiC,SAAA,CAAAjC,IAAA;gBAAA,OACA/D,oBAAA;kBACAiG,EAAA,EAAA9D,GAAA,CAAAiC;gBACA;cAAA;gBAFAV,GAAA,GAAAsC,SAAA,CAAA3B,IAAA;gBAGA,IAAAX,GAAA,CAAAY,SAAA;kBACAmB,MAAA,CAAAvC,IAAA;gBACA;kBACAuC,MAAA,CAAAlB,QAAA,CAAAU,KAAA,CAAAvB,GAAA,CAAAwB,OAAA;gBACA;cAAA;cAAA;gBAAA,OAAAc,SAAA,CAAAvB,IAAA;YAAA;UAAA,GAAAoB,QAAA;QAAA,CACA;QAAA,iBAAAK,EAAA;UAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAC,SAAA;QAAA;MAAA,KACAC,KAAA,WAAAP,CAAA;IACA;EACA;AACA", "ignoreList": []}]}