<template>
  <div class="chart-container">
    <div class="title">
      <div class="count">
        共计用{{ baseDataObj.title
        }}<span :style="{ color: baseDataObj.color }">{{
          baseDataObj.Total
        }}</span>
        {{ baseDataObj.unit }}
      </div>
    </div>
    <el-radio-group v-model="DataType" class="radioGroup" @change="radioChange">
      <el-radio-button
        v-for="(item, index) in radioGroupData"
        :key="index"
        :label="item"
      >{{ item }}</el-radio-button>
      <el-button
        v-if="baseDataObj.title != '丙烷'"
        type="text"
        style="margin-left: 20px"
        @click="handleClick"
      >气体流量</el-button>
    </el-radio-group>

    <div class="cs-chart">
      <div v-if="barOptionRef.xAxis.data.length == 0" class="empty">
        暂无数据
      </div>
      <v-chart
        v-else
        ref="barChartRef"
        class="barChartDom"
        :option="barOptionRef"
        :autoresize="true"
      />
    </div>
  </div>
</template>

<script>
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart } from 'echarts/charts'
import {
  GridComponent,
  LegendComponent,
  TooltipComponent,
  TitleComponent,
  DataZoomComponent
} from 'echarts/components'
use([
  CanvasRenderer,
  BarChart,
  DataZoomComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent
])
export default {
  components: {
    VChart
  },
  props: {
    customBarChatConfig: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      DataType: '全部',
      baseData: {},
      radioGroupData: []
    }
  },
  computed: {
    barOptionRef() {
      this.baseData = this.customBarChatConfig.baseData
      this.radioGroupData = this.customBarChatConfig.radioGroupData
      this.DataType = this.customBarChatConfig.baseData.DataType ?? '全部'
      return this.customBarChatConfig.barData
    },
    baseDataObj() {
      console.log(
        this.customBarChatConfig.baseData,
        'this.customBarChatConfig.baseData'
      )
      return this.customBarChatConfig.baseData
    }
  },
  methods: {
    radioChange(val) {
      this.$emit('radioChange', { val, GasType: this.baseData.GasType })
    },
    handleClick() {
      this.$emit('gasFlow', { val: this.DataType, GasType: this.baseData.GasType })
    }
  }
}
</script>

  <style scoped lang="scss">
.chart-container {
  width: 100%;
  height: 363px;
  display: flex;
  flex-direction: column;
  padding: 16px 24px;
  box-sizing: border-box;
  background: #fff;
  .title {
    height: 40px;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    padding-top: 16px;
    .count {
      font-weight: bold;
      font-size: 16px;
      color: #666666;
      margin-right: 8px;
      > span {
        margin: 0 5px;
      }
    }
    .lengend {
      > span {
        display: inline-block;
        width: 10px;
        height: 4px;
        margin-right: 8px;
      }
      font-size: 12px;
      color: #999999;
      display: flex;
      align-items: center;
    }
  }
  .radioGroup {
    margin-top: 30px;
    margin-left: 35px;
  }
  // ::v-deep .el-radio-button__inner {
  //   background-color: #ffffff;
  //   height: 32px;
  //   width: 80px;
  //   font-size: 14px;
  // }

  .cs-chart {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    .empty {
      font-size: 14px;
      color: #5e6d82;
    }
  }
}
</style>
