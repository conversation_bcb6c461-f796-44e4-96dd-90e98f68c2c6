<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      :title="dialogTitle"
      top="10vh"
      :visible.sync="dialogVisible"
      destroy-on-close
    >
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

  <script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";
import DialogForm from "./dialogForm.vue";
import { downloadFile } from "@/utils/downloadFile";
import getGridByCode from "../../safetyManagement/mixins/index";
import {
  GetPageList,
  ExportVisitorEquipment,
  GetVisitorsEntity,
  ApprovalVisitors,
  SendPassCheck,
  SendPassCheckAgain,
  ExportData,
  ExportVisitorsList,
} from "@/api/business/visitorManagement";
import dayjs from "dayjs";
import addRouterPage from "@/mixins/add-router-page";

export default {
  name: "",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
  },
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {
        Data: {},
        visitorTravelType: [],
        visitorReceiverUnit: [],
        visitorType: [],
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogVisible: false,
      dialogTitle: "",
      tableSelection: [],
      ruleForm: {
        Name: "",
        EquipmentType: [],
        PlateNumber: "",
        Unit: "",
        Status: null,
      },
      customForm: {
        formItems: [
          {
            key: "Name",
            label: "访客姓名",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "EquipmentType",
            label: "访客预约时间",
            type: "datePicker",
            options: [],
            otherOptions: {
              clearable: true,
              type: "daterange",
              rangeSeparator: "至",
              startPlaceholder: "开始日期",
              endPlaceholder: "结束日期",
            },
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "PlateNumber",
            label: "车牌号码",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "Unit",
            label: "被访单位",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "Status",
            label: "状态",
            type: "select",
            otherOptions: {
              clearable: true,
            },
            options: [
              { label: "全部", value: "" },
              { label: "未受理", value: 0 },
              { label: "已审核", value: 1 },
              { label: "未通过", value: 2 },
              { label: "已过期", value: 3 },
            ],
            change: (e) => {
              console.log(e);
            },
          },
        ],
        rules: {},
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "新增",
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: false, // 是否禁用
              icon: "", //  图标
              autofocus: false, // 是否聚焦
              type: "primary", // primary / success / warning / danger / info / text
              size: "small", // medium / small / mini
              onclick: (item) => {
                console.log(item);
                this.handleCreate();
              },
            },
            {
              text: "批量导出",
              onclick: (item) => {
                console.log(item);
                this.handleExport();
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [],
        tableData: [],
        operateOptions: {
          width: "240px",
          align: "center",
        },
        tableActionsWidth: 220,
        tableActions: [
          {
            actionLabel: "发送通行证",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handlePass(row.Id);
            },
          },
          {
            actionLabel: "查看",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleEdit(row);
            },
          },
          /* {
            actionLabel: '审核',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              if (row.Status == '未受理') {
                this.handleToExamine(row.Id)
              } else {
                this.$message.warning(`该数据${row.Status}`)
              }
            }
          }, */
          {
            actionLabel: "再次生成通行码",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleAgain(row.Id);
            },
          },
        ],
      },
      addPageArray: [
        {
          path: this.$route.path + "/addVisitor",
          hidden: true,
          component: () => import("./addVisitor.vue"),
          meta: { title: `新增访客` },
          name: "AddVisitor",
        },
      ],
    };
  },
  async created() {
    this.init();
  },
  mixins: [getGridByCode, addRouterPage],
  methods: {
    searchForm(data) {
      this.customTableConfig.currentPage = 1;
      this.onFresh();
    },
    resetForm() {
      this.onFresh();
    },
    onFresh() {
      this.fetchData();
    },
    init() {
      this.getGridByCode("visitorList");
      this.fetchData();
    },
    async fetchData() {
      let Start = "";
      let End = "";
      if ((this.ruleForm.EquipmentType ?? []).length > 0) {
        Start = dayjs(this.ruleForm.EquipmentType[0]).format("YYYY-MM-DD");
        End = dayjs(this.ruleForm.EquipmentType[1]).format("YYYY-MM-DD");
      }
      const res = await GetPageList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
        Start,
        End,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data;
        this.customTableConfig.total = res.Data.TotalCount;
      } else {
        this.$message.error(res.Message);
      }
    },
    async handleCreate() {
      this.$router.push({
        name: "AddVisitor",
        query: { pg_redirect: this.$route.name },
      });
      /*  this.dialogTitle = '新增'
       this.dialogVisible = true
       this.currentComponent = DialogForm
       this.componentsConfig.Data = {
         Name: '',
         Sex: '',
         Link: '',
         Type: '',
         VisitTime: '',
         TravelType: '',
         Count: '',
         CarCount: '',
         VisitorName: '',
         PlateNumber: '',
         Reason: '',
         VisitorUnit: '',
         Unit: '',
         UnitLink: '',
         Receiver: '',
         ReceiverLink: '',
         RealReceiver: '',
         isWatch: false
       }
       this.componentsConfig.visitorTravelType = await this.getDictionaryDetailListByCode('VisitorTravelType')
       this.componentsConfig.visitorReceiverUnit = await this.getDictionaryDetailListByCode('VisitorReceiverUnit')
       this.componentsConfig.visitorType = await this.getDictionaryDetailListByCode('VisitorType') */
    },
    async handleEdit(row) {
      // let res = await GetVisitorsEntity({ id: row.Id });
      // if (res.IsSucceed) {
      //   this.componentsConfig.Data = { ...res.Data, isWatch: true };
      // }
      this.componentsConfig.Data = row;
      this.currentComponent = DialogForm;
      this.dialogTitle = "查看";
      this.dialogVisible = true;
      // this.componentsConfig.visitorTravelType =
      //   await this.getDictionaryDetailListByCode("VisitorTravelType");
      // this.componentsConfig.visitorReceiverUnit =
      //   await this.getDictionaryDetailListByCode("VisitorReceiverUnit");
      // this.componentsConfig.visitorType =
      //   await this.getDictionaryDetailListByCode("VisitorType");
    },
    // async handleExport() {
    //   const res = await ExportData({
    //     Ids: this.tableSelection.map((item) => item.Id).toString(),
    //   });
    //   // downloadFile(res.Data, '访客列表数据')
    //   const url = window.URL.createObjectURL(
    //     new Blob([res], {
    //       type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    //     })
    //   );
    //   const link = document.createElement("a");
    //   link.style.display = "none";
    //   link.href = url;
    //   link.setAttribute("download", "访客列表数据");
    //   document.body.appendChild(link);
    //   link.click();
    // },
    // v2 版本  访客列表导出

    async handleExport() {
      let Start = "";
      let End = "";
      if ((this.ruleForm.EquipmentType ?? []).length > 0) {
        Start = dayjs(this.ruleForm.EquipmentType[0]).format("YYYY-MM-DD");
        End = dayjs(this.ruleForm.EquipmentType[1]).format("YYYY-MM-DD");
      }
      const res = await ExportVisitorsList({
        ...this.ruleForm,
        Start,
        End,
        Id: this.tableSelection.map((item) => item.Id).toString(),
      });

      if (res.IsSucceed) {
        this.$message.success("导出成功");
        downloadFile(res.Data, "访客列表数据");
      } else {
        this.$message.error(res.Message);
      }
      // downloadFile(res.Data, '访客列表数据')
      // const url = window.URL.createObjectURL(
      //   new Blob([res], {
      //     type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      //   })
      // );
      // const link = document.createElement("a");
      // link.style.display = "none";
      // link.href = url;
      // link.setAttribute("download", "访客列表数据");
      // document.body.appendChild(link);
      // link.click();
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.fetchData();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
    handleToExamine(ID) {
      this.$confirm("是否确认审核此访客信息?", "确认审核", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          ApprovalVisitors({ ID }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                type: "success",
                message: "审核成功!",
              });
              this.fetchData();
            } else {
              this.$message.error(res.Message);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    handlePass(Id) {
      this.$confirm("是否确认发送通行证给此访客?", "发送通行证", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          SendPassCheck({ Id }).then((res) => {
            if (res.IsSucceed) {
              this.$message.success("操作成功");
            } else {
              this.$message.error(res.Message);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    handleAgain(Id) {
      this.$confirm("是否确认再次激活通行码?", "发送通行证", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          SendPassCheckAgain({ Id }).then((res) => {
            if (res.IsSucceed) {
              this.$message.success("操作成功");
            } else {
              this.$message.error(res.Message);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
  },
};
</script>

  <style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
.layout{
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
