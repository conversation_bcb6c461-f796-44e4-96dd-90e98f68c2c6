{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\workOrderStatistics\\repair\\index.vue?vue&type=style&index=0&id=814e163c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\workOrderStatistics\\repair\\index.vue", "mtime": 1755674552421}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi53b3JrT3JkZXJTdGF0aXN0aWNzX3JlcGFpciB7DQogIC8vIHBhZGRpbmc6IDEwcHggMTVweDsNCiAgLy8gaGVpZ2h0OiBjYWxjKDEwMHZoIC0gOTBweCk7DQogIG92ZXJmbG93LXk6IGF1dG87DQogIC5oZWFkZXIgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgLy8gYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgaGVpZ2h0OiAyMnB4Ow0KICAgID4gc3BhbiB7DQogICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICB9DQogICAgLnRpdGxlX2NvbnRlbnQgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBzcGFuIHsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5zZWFyY2hfY29udGVudCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgLmxhYmVsIHsNCiAgICAgIG1hcmdpbi1yaWdodDogMTBweDsNCiAgICB9DQogICAgLnJhZGlvIHsNCiAgICAgIG1hcmdpbi1yaWdodDogMTBweDsNCiAgICB9DQogICAgLnBpY2tlciB7DQogICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQogICAgfQ0KICB9DQoNCiAgLnJlcGFpclNhdGlzZmFjdGlvbl9jb250ZW50IHsNCiAgICBoZWlnaHQ6IDIyMHB4Ow0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAucmVwYWlyU2F0aXNmYWN0aW9ubGlzdHMgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAucmVwYWlyU2F0aXNmYWN0aW9ubGlzdCB7DQogICAgICAgIHBhZGRpbmc6IDJweCAwcHg7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIC5sYWJlbCB7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgY29sb3I6ICM2NjY2NjY7DQogICAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KICAgICAgICB9DQogICAgICAgIC52YWx1ZSB7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLnJlcGFpck92ZXJ2aWV3X2NvbnRlbnQgew0KICAgIGhlaWdodDogMjIwcHg7DQogICAgLmJvdHRvbSB7DQogICAgICBtYXJnaW4tdG9wOiAyMHB4Ow0KICAgICAgZGlzcGxheTogZ3JpZDsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMywgY2FsYygzMyUgLSAxMHB4KSk7DQogICAgICBncmlkLWdhcDogMjBweDsgLyog6K6+572u6Ze06LedICovDQogICAgICAubWFpbiB7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDhweCA4cHggOHB4IDhweDsNCiAgICAgICAgcGFkZGluZzogMTBweCAxNXB4Ow0KICAgICAgICBiYWNrZ3JvdW5kOiAjZmFmZGZmOw0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgICAgLmxlZnQgew0KICAgICAgICAgIHdpZHRoOiA3MHB4Ow0KICAgICAgICAgIGhlaWdodDogNzBweDsNCiAgICAgICAgfQ0KICAgICAgICAucmlnaHQgew0KICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgICAgIC50ZXh0IHsNCiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgICAgICAgZm9udC1zaXplOiAyNHB4Ow0KICAgICAgICAgICAgbWFyZ2luLXRvcDogMTBweDsNCiAgICAgICAgICB9DQogICAgICAgICAgLnZhbHVlIHsNCiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgICBjb2xvcjogIzY2NjY2NjsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogICAgLnRvcCB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWFyb3VuZDsNCiAgICAgIGJhY2tncm91bmQ6ICNmYWZkZmY7DQogICAgICBib3JkZXItcmFkaXVzOiA4cHggOHB4IDhweCA4cHg7DQogICAgICBwYWRkaW5nOiAxMHB4IDE1cHg7DQogICAgICAubWFpbiB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7DQogICAgICAgIC5sZWZ0IHsNCiAgICAgICAgICB3aWR0aDogODBweDsNCiAgICAgICAgICBoZWlnaHQ6IDgwcHg7DQogICAgICAgIH0NCiAgICAgICAgLnJpZ2h0IHsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgICAgICAudGV4dCB7DQogICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMjRweDsNCiAgICAgICAgICB9DQogICAgICAgICAgLnZhbHVlIHsNCiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgICBjb2xvcjogIzY2NjY2NjsNCiAgICAgICAgICAgIG1hcmdpbi10b3A6IDEwcHg7DQogICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICA+IHNwYW4gew0KICAgICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQogICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7DQogICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLm1haW50ZW5hbmNlV29ya09yZGVyUHJvY2Vzc2luZ1N0YXR1c19jb250ZW50IHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgaGVpZ2h0OiAzMDBweDsNCiAgICAubGVmdCB7DQogICAgICB3aWR0aDogNTAlOw0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICBwYWRkaW5nOiAwcHggMThweDsNCiAgICAgIC50aXRsZSB7DQogICAgICAgIGNvbG9yOiByZ2JhKDM0LCA0MCwgNTIsIDAuODUpOw0KICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgICBwYWRkaW5nOiAxOHB4IDBweDsNCiAgICAgIH0NCiAgICAgIC5sZWZ0X2NvbnRlbnQgew0KICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmZGZmOw0KICAgICAgICBwYWRkaW5nOiAxMHB4IDE1cHg7DQogICAgICB9DQogICAgfQ0KICAgIC5yaWdodCB7DQogICAgICB3aWR0aDogNTAlOw0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQoNCiAgICAgIHBhZGRpbmc6IDBweCAxOHB4Ow0KICAgICAgLnRpdGxlIHsNCiAgICAgICAgY29sb3I6IHJnYmEoMzQsIDQwLCA1MiwgMC44NSk7DQogICAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgIHBhZGRpbmc6IDE4cHggMHB4Ow0KICAgICAgfQ0KICAgICAgLnJpZ2h0X2NvbnRlbnQgew0KICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmZGZmOw0KICAgICAgICBwYWRkaW5nOiAxMHB4IDE1cHg7DQogICAgICB9DQogICAgfQ0KICAgIC5pdGVtIHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICAgICAgLnRvcCB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogNnB4Ow0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgIC5sZWZ0IHsNCiAgICAgICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgIH0NCiAgICAgICAgLnJpZ2h0X2NvbG9yX29uZSB7DQogICAgICAgICAgY29sb3I6ICMyOThkZmY7DQogICAgICAgIH0NCiAgICAgICAgLnJpZ2h0X2NvbG9yX3R3byB7DQogICAgICAgICAgY29sb3I6ICMwMGQzYTc7DQogICAgICAgIH0NCiAgICAgICAgLnJpZ2h0IHsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7DQogICAgICAgICAgLm9uZSB7DQogICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDE0cHg7DQogICAgICAgICAgfQ0KICAgICAgICAgIC50d28gew0KICAgICAgICAgICAgd2lkdGg6IDM2cHg7DQogICAgICAgICAgICBkaXNwbGF5OiBibG9jazsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLnByb2R1Y3Rpb25FcXVpcG1lbnRMb2FkUmF0ZVJhbmtpbmdfY29udGVudCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgaGVpZ2h0OiAyMjBweDsNCiAgICBvdmVyZmxvdy15OiBzY3JvbGw7DQogICAgc2Nyb2xsYmFyLXdpZHRoOiBub25lOw0KICAgIC8vIHNjcm9sbGJhci1jb2xvcjogdHJhbnNwYXJlbnQgdHJhbnNwYXJlbnQ7DQogICAgLml0ZW0gew0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQogICAgICAudG9wIHsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgICBtYXJnaW4tYm90dG9tOiA2cHg7DQogICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAuZXF1aXBtZW50SW50ZWdyaXR5UmF0ZV9jb250ZW50IHsNCiAgICBoZWlnaHQ6IDIyMHB4Ow0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgLmVxdWlwbWVudEludGVncml0eVJhdGVsaXN0cyB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgd2lkdGg6IDQwJTsNCiAgICAgIC5lcXVpcG1lbnRJbnRlZ3JpdHlSYXRlbGlzdCB7DQogICAgICAgIHBhZGRpbmc6IDJweCAwcHg7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7DQogICAgICAgIG1hcmdpbi1yaWdodDogMjBweDsNCiAgICAgICAgLmxhYmVsIHsNCiAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICBjb2xvcjogIzY2NjY2NjsNCiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQogICAgICAgIH0NCiAgICAgICAgLnZhbHVlIHsNCiAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAucmVwYWlyUmVzcG9uc2VfY29udGVudCB7DQogICAgaGVpZ2h0OiAzMDBweDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAucmVwYWlyUmVzcG9uc2VsaXN0cyB7DQogICAgICBtYXJnaW4tdG9wOiAtNDBweDsNCiAgICAgIGJhY2tncm91bmQ6ICNmYWZkZmY7DQogICAgICBib3JkZXItcmFkaXVzOiA4cHggOHB4IDhweCA4cHg7DQogICAgICBwYWRkaW5nOiAxMHB4IDMwcHg7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWFyb3VuZDsNCiAgICAgIHdpZHRoOiA2MCU7DQogICAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAgICAgLnJlcGFpclJlc3BvbnNlbGlzdCB7DQogICAgICAgIHBhZGRpbmc6IDJweCAwcHg7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIG1hcmdpbi1yaWdodDogMjBweDsNCiAgICAgICAgLmxhYmVsIHsNCiAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICBjb2xvcjogIzY2NjY2NjsNCiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQogICAgICAgIH0NCiAgICAgICAgLnZhbHVlIHsNCiAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLmVxdWlwbWVudFN0YXJ0dXBTdGF0dXNfY29udGVudCB7DQogICAgaGVpZ2h0OiAyMjBweDsNCiAgfQ0KICAuZXF1aXBtZW50RmFpbHVyZVRyZW5kX2NvbnRlbnQgew0KICAgIGhlaWdodDogMzIwcHg7DQogICAgLnRhYmxlbnVtYmVyIHsNCiAgICAgIHdpZHRoOiAzMHB4Ow0KICAgICAgaGVpZ2h0OiAyM3B4Ow0KICAgICAgYmFja2dyb3VuZC1zaXplOiAxMDAlOw0KICAgICAgYmFja2dyb3VuZC1yZXBlYXQ6IG5vLXJlcGVhdDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICA+IHNwYW4gew0KICAgICAgICBtYXJnaW4tdG9wOiAxMHB4Ow0KICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KICAucmVwYWlyU3RhdHVzRWFjaFdvcmtzaG9wX2NvbnRlbnQgew0KICAgIGhlaWdodDogMzAwcHg7DQogICAgLnJpZ2h0IHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIC5yaWdodF9udW0gew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIDo6di1kZWVwIC5lbC1jYXJkX19oZWFkZXIgew0KICAgIGJvcmRlci1ib3R0b206IG5vbmUgIWltcG9ydGFudDsNCiAgfQ0KICA6OnYtZGVlcCAuZWwtcHJvZ3Jlc3NfX3RleHQgew0KICAgIGZvbnQtc2l6ZTogMThweCAhaW1wb3J0YW50Ow0KICAgIGNvbG9yOiAjNjY2NjY2ICFpbXBvcnRhbnQ7DQogIH0NCiAgOjp2LWRlZXAuZWwtdGFibGUgLnJvdy1vbmUgew0KICAgIGJhY2tncm91bmQ6IHJnYmEoNDEsIDE0MSwgMjU1LCAwLjAzKSAhaW1wb3J0YW50Ow0KICB9DQoNCiAgOjp2LWRlZXAgLmVsLXRhYmxlIC5yb3ctdHdvIHsNCiAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDEpICFpbXBvcnRhbnQ7DQogIH0NCg0KICAvLyA6OnYtZGVlcCAuZWwtcmFkaW8tYnV0dG9uX19pbm5lciB7DQogIC8vICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjsNCiAgLy8gICAvLyBwYWRkaW5nOiA2cHggMzJweDsNCiAgLy8gICBoZWlnaHQ6IDMycHg7DQogIC8vICAgLy8gbGluZS1oZWlnaHQ6IDMycHg7DQogIC8vICAgd2lkdGg6IDgwcHg7DQogIC8vICAgZm9udC1zaXplOiAxNHB4Ow0KICAvLyB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8+CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/equipmentManagement/workOrderStatistics/repair", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 workOrderStatistics_repair\">\r\n    <el-row :gutter=\"12\">\r\n      <el-col :span=\"24\">\r\n        <el-card shadow=\"hover\">\r\n          <div class=\"search_content\">\r\n            <span class=\"label\">选择维度</span>\r\n            <el-radio-group\r\n              v-model=\"yearMonthRadio\"\r\n              class=\"radio\"\r\n              @change=\"yearMonthRadioChange\"\r\n            >\r\n              <el-radio-button label=\"1\">年</el-radio-button>\r\n              <el-radio-button label=\"2\">月</el-radio-button>\r\n            </el-radio-group>\r\n            <el-date-picker\r\n              v-model=\"yearMonthValue\"\r\n              class=\"picker\"\r\n              :editable=\"false\"\r\n              :clearable=\"false\"\r\n              :type=\"yearMonthType\"\r\n              @change=\"yearMonthPickerChange\"\r\n            />\r\n            <el-button @click=\"resetForm\">重置</el-button>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"10\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <div class=\"title_content\">\r\n              <span>报修总览</span>\r\n              <el-popover\r\n                placement=\"top-start\"\r\n                title=\"说明\"\r\n                width=\"420\"\r\n                trigger=\"hover\"\r\n              >\r\n                <div>\r\n                  <span>待处理：工单中心工单状态为待处理的所有工单数量</span><br>\r\n                  <span>处理中：工单中心工单状态为处理中，待复检的所有工单数量</span><br>\r\n                  <span>已处理：工单中心工单状态为待评价，处理完成，已关闭的所有工单数量</span>\r\n                </div>\r\n                <img\r\n                  slot=\"reference\"\r\n                  style=\"width: 16px; height: 16px\"\r\n                  src=\"@/assets/question.png\"\r\n                  alt=\"\"\r\n                >\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n          <div class=\"repairOverview_content\">\r\n            <div class=\"top\">\r\n              <div class=\"main\">\r\n                <img\r\n                  class=\"left\"\r\n                  src=\"@/assets/totalUmberSorkOrders.png\"\r\n                  alt=\"\"\r\n                >\r\n                <div class=\"right\">\r\n                  <span class=\"text\" style=\"color: #298dff\">{{\r\n                    repairOverview.Total\r\n                  }}</span>\r\n                  <span class=\"value\">报修总数</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"main\">\r\n                <img class=\"left\" src=\"@/assets/repairTime.png\" alt=\"\">\r\n                <div class=\"right\">\r\n                  <span class=\"text\" style=\"color: #ffae2c\">{{\r\n                    repairOverview.FixTime\r\n                  }}</span>\r\n                  <div class=\"value\">\r\n                    <span>平均修复时间</span>\r\n                    <el-popover\r\n                      placement=\"top-start\"\r\n                      title=\"说明\"\r\n                      trigger=\"hover\"\r\n                    >\r\n                      <span>平均修复时间=工单总处理时长/工单个数</span>\r\n                      <img\r\n                        slot=\"reference\"\r\n                        style=\"width: 16px; height: 16px\"\r\n                        src=\"@/assets/question.png\"\r\n                        alt=\"\"\r\n                      >\r\n                    </el-popover>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"bottom\">\r\n              <div class=\"main\">\r\n                <img class=\"left\" src=\"@/assets/pendingProcessing.png\" alt=\"\">\r\n                <div class=\"right\">\r\n                  <span class=\"value\">待处理</span>\r\n                  <span class=\"text\" style=\"color: #ff5e7c\">{{\r\n                    repairOverview.Pending\r\n                  }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"main\">\r\n                <img class=\"left\" src=\"@/assets/processing.png\" alt=\"\">\r\n                <div class=\"right\">\r\n                  <span class=\"value\">处理中</span>\r\n                  <span class=\"text\" style=\"color: #298dff\">{{\r\n                    repairOverview.Processing\r\n                  }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"main\">\r\n                <img class=\"left\" src=\"@/assets/processed.png\" alt=\"\">\r\n                <div class=\"right\">\r\n                  <span class=\"value\">已处理</span>\r\n                  <span class=\"text\" style=\"color: #00d3a7\">{{\r\n                    repairOverview.Processed\r\n                  }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"14\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>待办报修</span>\r\n            <el-button\r\n              type=\"text\"\r\n              @click=\"lookMoreDetail('WorkOrderManagement')\"\r\n            >查看更多 <i class=\"el-icon-arrow-right\" /></el-button>\r\n          </div>\r\n          <div style=\"margin-top: -20px\">\r\n            <el-table\r\n              ref=\"scroll_Table\"\r\n              :data=\"pendingRepairRequestData\"\r\n              style=\"width: 100%\"\r\n              height=\"240\"\r\n              :highlight-current-row=\"false\"\r\n              :row-class-name=\"pendingRepairRequestDataClassName\"\r\n              @mouseenter.native=\"autoScroll(true)\"\r\n              @mouseleave.native=\"autoScroll(false)\"\r\n            >\r\n              <el-table-column prop=\"Order_Name\" label=\"报修名称\" />\r\n              <el-table-column prop=\"State\" label=\"报修状态\">\r\n                <template slot-scope=\"scope\">\r\n                  <span\r\n                    :style=\"{\r\n                      color: getStatusStyle(scope.row).color,\r\n                    }\"\r\n                  >\r\n                    {{ getStatusStyle(scope.row).text }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Warranty_Person\" label=\"提交人员\" />\r\n              <el-table-column prop=\"Create_Date\" label=\"创建时间\" />\r\n              <el-table-column label=\"等待时长\">\r\n                <template slot-scope=\"scope\">\r\n                  <span> {{ getWaitingTime(scope.row.Create_Date) }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"ErrPercent\" label=\"操作\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    type=\"text\"\r\n                    size=\"small\"\r\n                    @click=\"\r\n                      openDialog('detail', scope.row, scope.row.Order_Type)\r\n                    \"\r\n                  >查看详情</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"9\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>报修故障类型</span>\r\n          </div>\r\n          <div class=\"equipmentStartupStatus_content\">\r\n            <v-chart\r\n              ref=\"repairFaultTypeRef\"\r\n              class=\"repairFaultType\"\r\n              :option=\"repairFaultTypeOptions\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"5\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <div class=\"title_content\">\r\n              <span>设备完好率</span>\r\n              <el-popover\r\n                placement=\"top-start\"\r\n                title=\"说明\"\r\n                width=\"420\"\r\n                trigger=\"hover\"\r\n              >\r\n                <div>\r\n                  <span>1.设备维修完好率=月完好天数/月总天数</span><br>\r\n                  <span>2.月完好天数：当天无未处理完成的工单即为完好，每天24：00进行当天工单状态统计</span>\r\n                </div>\r\n                <img\r\n                  slot=\"reference\"\r\n                  style=\"width: 16px; height: 16px\"\r\n                  src=\"@/assets/question.png\"\r\n                  alt=\"\"\r\n                >\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n          <div class=\"equipmentIntegrityRate_content\">\r\n            <div style=\"width: 60%; height: 100%\">\r\n              <v-chart\r\n                ref=\"equipmentIntegrityRateRef\"\r\n                class=\"equipmentIntegrityRate\"\r\n                :option=\"equipmentIntegrityRateOptions\"\r\n                :autoresize=\"true\"\r\n              />\r\n            </div>\r\n            <div class=\"equipmentIntegrityRatelists\">\r\n              <div class=\"equipmentIntegrityRatelist\">\r\n                <span class=\"label\">完好率</span>\r\n                <span\r\n                  class=\"value\"\r\n                  style=\"color: #00d3a7\"\r\n                >{{ equipmentIntegrityRate.ServiceabilityRate }}%</span>\r\n              </div>\r\n              <div class=\"equipmentIntegrityRatelist\">\r\n                <span class=\"label\">统计天数</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  equipmentIntegrityRate.StatisticsDays\r\n                }}</span>\r\n              </div>\r\n              <div class=\"equipmentIntegrityRatelist\">\r\n                <span class=\"label\">完好天数</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  equipmentIntegrityRate.ServiceabilityDays\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"5\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <div class=\"title_content\">\r\n              <span>设备故障率排行</span>\r\n              <el-popover\r\n                placement=\"top-start\"\r\n                title=\"说明\"\r\n                width=\"420\"\r\n                trigger=\"hover\"\r\n              >\r\n                <div>\r\n                  <span>代表单位时间内设备的故障次数多少，计算方式如下： 故障率=\r\n                    累计故障次数/设备开机总时间h×100%</span>\r\n                </div>\r\n                <img\r\n                  slot=\"reference\"\r\n                  style=\"width: 16px; height: 16px\"\r\n                  src=\"@/assets/question.png\"\r\n                  alt=\"\"\r\n                >\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n          <div class=\"productionEquipmentLoadRateRanking_content\">\r\n            <div\r\n              v-for=\"(item, index) in equipmentFailureRateRanking\"\r\n              :key=\"index\"\r\n              class=\"item\"\r\n            >\r\n              <div class=\"top\">\r\n                <span>{{ item.EquipName }}</span>\r\n                <span>{{ item.FailureRate }}</span>\r\n              </div>\r\n              <el-progress\r\n                class=\"bottom\"\r\n                :percentage=\"item.FailureRate\"\r\n                :show-text=\"false\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"5\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>报修满意度</span>\r\n          </div>\r\n          <div class=\"repairSatisfaction_content\">\r\n            <div style=\"width: 60%; height: 100%\">\r\n              <v-chart\r\n                ref=\"repairSatisfactionRef\"\r\n                class=\"repairSatisfaction\"\r\n                :option=\"repairSatisfactionOptions\"\r\n                :autoresize=\"true\"\r\n              />\r\n            </div>\r\n            <div class=\"repairSatisfactionlists\">\r\n              <div class=\"repairSatisfactionlist\">\r\n                <span class=\"label\">处理报修总数</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  repairSatisfactionConfig.Total\r\n                }}</span>\r\n              </div>\r\n              <div class=\"repairSatisfactionlist\" style=\"margin-top: 20px\">\r\n                <span class=\"label\">最高满意度</span>\r\n                <span\r\n                  class=\"value\"\r\n                  style=\"color: #00d3a7\"\r\n                >{{ repairSatisfactionConfig.Max }} 分</span>\r\n              </div>\r\n              <div class=\"repairSatisfactionlist\">\r\n                <span class=\"label\">最低满意度</span>\r\n                <span\r\n                  class=\"value\"\r\n                  style=\"color: #ff902c\"\r\n                >{{ repairSatisfactionConfig.Min }} 分</span>\r\n              </div>\r\n              <div class=\"repairSatisfactionlist\">\r\n                <span class=\"label\">综合满意度</span>\r\n                <span\r\n                  class=\"value\"\r\n                  style=\"color: #298dff\"\r\n                >{{ repairSatisfactionConfig.Avg }} 分</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"14\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <div class=\"title_content\">\r\n              <span>报修响应</span>\r\n              <el-popover\r\n                placement=\"top-start\"\r\n                title=\"说明\"\r\n                width=\"420\"\r\n                trigger=\"hover\"\r\n              >\r\n                <div>\r\n                  <span>1.响应及时率：计算工单创建时间到接单时间的时长，该时间越短则代表处理人员的响应速度越快。</span><br>\r\n                  <span>2.响应超时：接单响应时间超过 1小时\r\n                    的工单计算为响应超时。</span>\r\n                </div>\r\n                <img\r\n                  slot=\"reference\"\r\n                  style=\"width: 16px; height: 16px\"\r\n                  src=\"@/assets/question.png\"\r\n                  alt=\"\"\r\n                >\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n          <div class=\"repairResponse_content\">\r\n            <div class=\"repairResponselists\">\r\n              <div class=\"repairResponselist\">\r\n                <span class=\"label\">报修总数</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  repairResponseConfig.Total\r\n                }}</span>\r\n              </div>\r\n              <div class=\"repairResponselist\">\r\n                <span class=\"label\">响应及时</span>\r\n                <span class=\"value\" style=\"color: #298dff\">{{\r\n                  repairResponseConfig.Timely\r\n                }}</span>\r\n              </div>\r\n              <div class=\"repairResponselist\">\r\n                <span class=\"label\">响应超时</span>\r\n                <span class=\"value\" style=\"color: #ff902c\">{{\r\n                  repairResponseConfig.Timeout\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n            <v-chart\r\n              ref=\"repairResponseRef\"\r\n              class=\"repairResponse\"\r\n              :option=\"repairResponseOptions\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"10\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>报修处理人员完成排名</span>\r\n          </div>\r\n          <div class=\"equipmentFailureTrend_content\" style=\"margin-top: -20px\">\r\n            <el-table\r\n              :data=\"repairProcessingPersonnelCompleteRankingData\"\r\n              style=\"width: 100%\"\r\n              height=\"475\"\r\n              :highlight-current-row=\"false\"\r\n              :row-class-name=\"\r\n                repairProcessingPersonnelCompleteRankingDataClassName\r\n              \"\r\n            >\r\n              <!-- ref=\"scroll_Table\"\r\n              @mouseenter.native=\"autoScroll(true)\"\r\n              @mouseleave.native=\"autoScroll(false)\" -->\r\n              <el-table-column label=\"排名\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <div\r\n                    v-if=\"scope.$index < 3\"\r\n                    class=\"tablenumber\"\r\n                    :style=\"{\r\n                      backgroundImage:\r\n                        'url(' +\r\n                        require(`../../../../../assets/no_${\r\n                          scope.$index + 1\r\n                        }.png`) +\r\n                        ')',\r\n                    }\"\r\n                  >\r\n                    <span> {{ scope.$index + 1 }}</span>\r\n                  </div>\r\n                  <div v-else class=\"tablenumber\">\r\n                    <span> {{ scope.$index + 1 }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Name\" label=\"姓名\" width=\"100\" />\r\n              <el-table-column prop=\"Count\" sortable label=\"数量\" width=\"100\" />\r\n              <el-table-column prop=\"Duration\" sortable label=\"用时\" />\r\n              <el-table-column\r\n                prop=\"Timely\"\r\n                sortable\r\n                label=\"响应及时率\"\r\n                width=\"120\"\r\n              />\r\n              <el-table-column\r\n                prop=\"Satisfaction\"\r\n                sortable\r\n                label=\"综合满意度\"\r\n                width=\"120\"\r\n              />\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"9\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>各车间报修情况</span>\r\n          </div>\r\n          <div class=\"maintenanceWorkOrderProcessingStatus_content\">\r\n            <v-chart\r\n              ref=\"repairStatusEachWorkshopRef\"\r\n              class=\"repairStatusEachWorkshop\"\r\n              :option=\"repairStatusEachWorkshopOptions\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"15\">\r\n        <el-card shadow=\"hover\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>各车间报修趋势</span>\r\n          </div>\r\n          <div class=\"repairStatusEachWorkshop_content\">\r\n            <v-chart\r\n              ref=\"trendRepairReportsVariousWorkshopsRef\"\r\n              class=\"trendRepairReportsVariousWorkshops\"\r\n              :option=\"trendRepairReportsVariousWorkshopsOptions\"\r\n              :autoresize=\"true\"\r\n            />\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <editDialog ref=\"editDialog\" @refresh=\"initData\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  GetWorkOrderManageList,\r\n  GetWorkorderStatistics,\r\n  GetTimeoutStatistics,\r\n  GetSatisfactionStatistics,\r\n  GetProcessedRank,\r\n  GetWorkShopCase,\r\n  GetWorkOrderTrend,\r\n  GetWorkOrderErrorType,\r\n  GetEquipFailureRateRank,\r\n  GetDeviceServiceabilityRate\r\n} from '@/api/business/equipmentManagement'\r\nimport dayjs from 'dayjs'\r\nimport VChart from 'vue-echarts'\r\nimport { use } from 'echarts/core'\r\nimport { CanvasRenderer } from 'echarts/renderers'\r\nimport { BarChart, LineChart, PieChart, GaugeChart } from 'echarts/charts'\r\n\r\nimport {\r\n  GridComponent,\r\n  LegendComponent,\r\n  TooltipComponent,\r\n  TitleComponent,\r\n  DataZoomComponent,\r\n  ToolboxComponent\r\n} from 'echarts/components'\r\n\r\nimport editDialog from '@/views/business/maintenanceAndUpkeep/workOrderManagement/editDialog.vue'\r\nuse([\r\n  CanvasRenderer,\r\n  BarChart,\r\n  LineChart,\r\n  PieChart,\r\n  GaugeChart,\r\n  DataZoomComponent,\r\n  GridComponent,\r\n  LegendComponent,\r\n  TitleComponent,\r\n  TooltipComponent,\r\n  ToolboxComponent\r\n])\r\nexport default {\r\n  name: 'EquipmentAnalysis',\r\n  components: {\r\n    VChart,\r\n    editDialog\r\n  },\r\n  mixins: [],\r\n  data() {\r\n    return {\r\n      // 查看更多跳转列表\r\n      jumpUrlList: [],\r\n      // 待办报修\r\n      pendingRepairRequestStatus: [\r\n        {\r\n          text: '待处理',\r\n          value: '0',\r\n          color: '#FF5E7C'\r\n        },\r\n        {\r\n          text: '处理中',\r\n          value: '1',\r\n          color: '#298DFF'\r\n        },\r\n        {\r\n          text: '待复检',\r\n          value: '2',\r\n          color: '#FF902C'\r\n        },\r\n        {\r\n          text: '待评价',\r\n          value: '3',\r\n          color: '#298DFF'\r\n        },\r\n        {\r\n          text: '处理完成',\r\n          value: '4',\r\n          color: '#00D3A7'\r\n        },\r\n        {\r\n          text: '已关闭',\r\n          value: '5',\r\n          color: '#333333'\r\n        }\r\n      ],\r\n      pendingRepairRequestData: [],\r\n      repairOverview: {},\r\n      yearMonthRadio: '2',\r\n      yearMonthType: 'month',\r\n      yearMonthValue: dayjs(new Date()).format('YYYY-MM'),\r\n      scrolltimer: '', // 自动滚动的定时任务\r\n      // 各车间报修趋势\r\n      trendRepairReportsVariousWorkshopsOptions: {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          }\r\n        },\r\n        legend: {\r\n          top: '0',\r\n          right: '0',\r\n          itemWidth: 16,\r\n          itemHeight: 8,\r\n          itemGap: 10\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        color: ['#4EBF8B', '#66CBF0', '#298DFF', '#FF902C'],\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            position: 'left',\r\n            logBase: 10\r\n          }\r\n        ],\r\n        series: [\r\n          // {\r\n          //   name: \"一车间\",\r\n          //   type: \"bar\",\r\n          //   barWidth: 20,\r\n          //   stack: \"vehicle\",\r\n          //   emphasis: {\r\n          //     focus: \"series\",\r\n          //   },\r\n          //   data: [],\r\n          // },\r\n          // {\r\n          //   name: \"二车间\",\r\n          //   type: \"bar\",\r\n          //   stack: \"vehicle\",\r\n          //   emphasis: {\r\n          //     focus: \"series\",\r\n          //   },\r\n          //   data: [],\r\n          // },\r\n          // {\r\n          //   name: \"配送中心\",\r\n          //   type: \"bar\",\r\n          //   stack: \"vehicle\",\r\n          //   emphasis: {\r\n          //     focus: \"series\",\r\n          //   },\r\n          //   data: [],\r\n          // },\r\n          // {\r\n          //   name: \"响应及时率\",\r\n          //   type: \"line\",\r\n          //   smooth: true,\r\n          //   symbol: \"none\",\r\n          //   yAxisIndex: 1,\r\n          //   tooltip: {\r\n          //     valueFormatter: function (value) {\r\n          //       return value + \" %\";\r\n          //     },\r\n          //   },\r\n          //   data: [],\r\n          // },\r\n        ]\r\n      },\r\n      // 各车间报修情况\r\n      repairStatusEachWorkshopOptions: {\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            return ` ${params.name} ${params.percent}%  `\r\n          }\r\n        },\r\n        color: ['#4EBF8B', '#298DFF', '#51A1FD'],\r\n        legend: {\r\n          orient: 'vertical',\r\n          right: '0',\r\n          bottom: 'center',\r\n          itemWidth: 12,\r\n          itemHeight: 6,\r\n          textStyle: {\r\n            color: 'rgba(34, 40, 52, 0.65)'\r\n          },\r\n          textStyle: {\r\n            rich: {\r\n              labelMark: {\r\n                width: 60,\r\n                color: '#222834'\r\n              },\r\n              valueMark: {\r\n                width: 40,\r\n                color: '#66CBF0'\r\n              },\r\n              percentMark: {\r\n                width: 40,\r\n                color: '#298DFF'\r\n              }\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            type: 'pie',\r\n            radius: '50%',\r\n            right: 150,\r\n            data: [],\r\n            labelLine: {\r\n              // 设置延长线的长度\r\n              normal: {\r\n                length: 5, // 设置延长线的长度\r\n                length2: 10, // 设置第二段延长线的长度\r\n                lineStyle: {\r\n                  color: 'rgba(194, 203, 226, 1)'\r\n                }\r\n              }\r\n            },\r\n            label: {\r\n              normal: {\r\n                // formatter: '{d}%, {c} \\n\\n',\r\n                formatter: ' {c|{b}}  {per|{d}%} \\n{hr|}\\n{a|}', // 这里最后另一行设置了一个空数据是为了能让延长线与hr线对接起来\r\n                padding: [0, -4], // 取消hr线跟延长线之间的间隙\r\n                rich: {\r\n                  a: {\r\n                    color: '#999',\r\n                    lineHeight: 20, // 设置最后一行空数据高度，为了能让延长线与hr线对接起来\r\n                    align: 'center'\r\n                  },\r\n                  hr: {\r\n                    // 设置hr是为了让中间线能够自适应长度\r\n                    borderColor: 'rgba(194, 203, 226, 1)', // hr的颜色为auto时候会主动显示颜色的\r\n                    width: '105%',\r\n                    borderWidth: 0.5,\r\n                    height: 0.5\r\n                  },\r\n                  per: {\r\n                    // 用百分比数据来调整下数字位置，显的好看些。如果不设置，formatter最后一行的空数据就不需要\r\n                    padding: [4, 0],\r\n                    // color: \"rgba(194, 203, 226, 1)\",\r\n                    color: function(params) {\r\n                      console.log(params, 'params-------------')\r\n                      // 通过数据项的颜色来设置文字颜色\r\n                      return params.color\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                shadowBlur: 10,\r\n                shadowOffsetX: 0,\r\n                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n              }\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      // 报修故障类型\r\n      repairFaultTypeOptions: {\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            return ` ${params.name} ${params.percent}%  `\r\n          }\r\n        },\r\n        color: ['#4EBF8B', '#298DFF', '#51A1FD'],\r\n        legend: {\r\n          orient: 'vertical',\r\n          right: '0',\r\n          bottom: 'center',\r\n          itemWidth: 12,\r\n          itemHeight: 6,\r\n          textStyle: {\r\n            color: 'rgba(34, 40, 52, 0.65)'\r\n          },\r\n          formatter: function(name) {\r\n            return `${name}`\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            type: 'pie',\r\n            radius: '50%',\r\n            // right: 100,\r\n            data: [],\r\n            labelLine: {\r\n              // 设置延长线的长度\r\n              normal: {\r\n                length: 5, // 设置延长线的长度\r\n                length2: 10, // 设置第二段延长线的长度\r\n                lineStyle: {\r\n                  color: 'rgba(194, 203, 226, 1)'\r\n                }\r\n              }\r\n            },\r\n            label: {\r\n              normal: {\r\n                // formatter: '{d}%, {c} \\n\\n',\r\n                formatter: ' {c|{b}}  {per|{d}%} \\n{hr|}\\n{a|}', // 这里最后另一行设置了一个空数据是为了能让延长线与hr线对接起来\r\n                padding: [0, -4], // 取消hr线跟延长线之间的间隙\r\n                rich: {\r\n                  a: {\r\n                    color: '#999',\r\n                    lineHeight: 20, // 设置最后一行空数据高度，为了能让延长线与hr线对接起来\r\n                    align: 'center'\r\n                  },\r\n                  hr: {\r\n                    // 设置hr是为了让中间线能够自适应长度\r\n                    borderColor: 'rgba(194, 203, 226, 1)', // hr的颜色为auto时候会主动显示颜色的\r\n                    width: '105%',\r\n                    borderWidth: 0.5,\r\n                    height: 0.5\r\n                  },\r\n                  per: {\r\n                    // 用百分比数据来调整下数字位置，显的好看些。如果不设置，formatter最后一行的空数据就不需要\r\n                    padding: [4, 0]\r\n                  }\r\n                }\r\n              }\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                shadowBlur: 10,\r\n                shadowOffsetX: 0,\r\n                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n              }\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      // 报修响应\r\n      repairResponseConfig: {},\r\n      repairResponseOptions: {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            crossStyle: {\r\n              color: '#999'\r\n            }\r\n          }\r\n        },\r\n        legend: {\r\n          top: '0',\r\n          right: '0',\r\n          itemWidth: 16,\r\n          itemHeight: 8,\r\n          itemGap: 10\r\n        },\r\n        color: ['#298DFF', '#FF902C', '#00D3A7'],\r\n        xAxis: [\r\n          {\r\n            type: 'category',\r\n            data: [],\r\n            axisPointer: {\r\n              type: 'shadow'\r\n            },\r\n            axisLine: {\r\n              show: false\r\n            },\r\n            axisTick: {\r\n              show: false\r\n            }\r\n          }\r\n        ],\r\n        yAxis: [\r\n          {\r\n            type: 'value'\r\n            // min: 0,\r\n            // max: 250,\r\n            // interval: 50,\r\n            // axisLabel: {\r\n            //   formatter: \"{value} ml\",\r\n            // },\r\n          },\r\n          {\r\n            type: 'value',\r\n            // min: 0,\r\n            // max: 25,\r\n            // interval: 5,\r\n            axisLabel: {\r\n              formatter: '{value} %'\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '响应及时数',\r\n            type: 'bar',\r\n            barWidth: 20,\r\n            stack: 'vehicle',\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return value\r\n              }\r\n            },\r\n            data: []\r\n          },\r\n          {\r\n            name: '响应超时数',\r\n            type: 'bar',\r\n            barWidth: 20,\r\n            stack: 'vehicle',\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return value\r\n              }\r\n            },\r\n            data: []\r\n          },\r\n          {\r\n            name: '响应及时率',\r\n            type: 'line',\r\n            smooth: true,\r\n            symbol: 'none',\r\n            yAxisIndex: 1,\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return value + ' %'\r\n              }\r\n            },\r\n            data: []\r\n          }\r\n        ]\r\n      },\r\n      // 报修满意度\r\n      repairSatisfactionConfig: {},\r\n      repairSatisfactionOptions: {\r\n        series: []\r\n      },\r\n      // 设备完好率\r\n      equipmentIntegrityRate: {},\r\n      equipmentIntegrityRateOptions: {\r\n        tooltip: {\r\n          show: false\r\n        },\r\n        series: [\r\n          {\r\n            // 外圆\r\n            silent: false,\r\n            type: 'gauge',\r\n            zlevel: 2,\r\n            startAngle: 0,\r\n            endAngle: 360,\r\n            clockwise: true,\r\n            radius: '75%',\r\n            splitNumber: 5,\r\n            avoidLabelOverlap: false,\r\n            axisLine: {\r\n              show: true\r\n              // lineStyle: {\r\n              //   color: [\r\n              //     [80 / 100, \"rgba(0, 211, 167, 0.3)\"],\r\n              //     [1, \"#f0f2f8\"],\r\n              //   ],\r\n              //   width: 16,\r\n              // },\r\n            },\r\n            itemStyle: {\r\n              color: 'rgba(255,255,255,0)'\r\n            },\r\n            progress: {\r\n              show: true\r\n            },\r\n            axisTick: {\r\n              show: true,\r\n              splitNumber: 1,\r\n              distance: -16,\r\n              lineStyle: {\r\n                color: '#ffffff',\r\n                width: 3\r\n              },\r\n              length: 20\r\n            }, // 刻度样式\r\n            splitLine: {\r\n              show: false\r\n            },\r\n            axisLabel: {\r\n              show: false\r\n            },\r\n            pointer: {\r\n              show: false\r\n            },\r\n            title: {\r\n              show: false\r\n            },\r\n            detail: {\r\n              show: false\r\n            }\r\n          },\r\n          {\r\n            // 外圆2\r\n            type: 'pie',\r\n            silent: true,\r\n            center: ['50%', '50%'],\r\n            radius: ['0%', '50%'],\r\n            avoidLabelOverlap: false,\r\n            zlevel: 3,\r\n            itemStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 1,\r\n                x2: 0,\r\n                y2: 0,\r\n                colorStops: [\r\n                  {\r\n                    offset: 0,\r\n                    color: 'rgba(0, 211, 167, 0.3)'\r\n                  },\r\n                  {\r\n                    offset: 1,\r\n                    color: 'rgba(57, 133, 238, 0)'\r\n                  }\r\n                ]\r\n              },\r\n              borderColor: 'rgba(0, 211, 167, 0.2)'\r\n            },\r\n            label: {\r\n              show: true,\r\n              position: 'center',\r\n              formatter: (pamars) => {\r\n                return `0%`\r\n              },\r\n              fontSize: 24,\r\n              color: '#3f4652'\r\n            },\r\n            labelLine: {\r\n              show: false\r\n            },\r\n            data: [1]\r\n          }\r\n        ]\r\n      },\r\n      // 报修处理人员完成排名\r\n      repairProcessingPersonnelCompleteRankingData: [],\r\n      // 获取设备故障率排行\r\n      equipmentFailureRateRanking: []\r\n    }\r\n  },\r\n  activated() {},\r\n  beforeDestroy() {\r\n    this.autoScroll(true)\r\n  },\r\n  mounted() {\r\n    this.initData()\r\n\r\n    this.autoScroll()\r\n  },\r\n  methods: {\r\n    // 打开弹框\r\n    openDialog(type, row, orderType) {\r\n      this.$refs.editDialog.handleOpen(type, row, orderType)\r\n    },\r\n    // 初始化加载数据\r\n    initData() {\r\n      // 待办报修\r\n      this.getWorkOrderManageList()\r\n      // 获取工单总览统计\r\n      this.getTimeoutStatistics()\r\n      // 获取工单响应超时统计\r\n      this.getWorkorderStatistics()\r\n      // 获取工单满意度统计\r\n      this.getSatisfactionStatistics()\r\n      // 获取处理人员完成排名\r\n      this.getProcessedRank()\r\n\r\n      // 获取各车间工单情况\r\n      this.getWorkShopCase()\r\n      // 获取各车间趋势\r\n      this.getWorkOrderTrend()\r\n      // 获取报修工单故障类型\r\n      this.getWorkOrderErrorType()\r\n\r\n      // 获取设备完好率\r\n      this.getEquipFailureRateRank()\r\n      // 获取设备故障率排行\r\n      this.getDeviceServiceabilityRate()\r\n    },\r\n    // 充值表单数据并加载\r\n    resetForm() {\r\n      this.yearMonthType = 'month'\r\n      this.yearMonthValue = dayjs(new Date()).format('YYYY-MM')\r\n      this.initData()\r\n    },\r\n    // 筛选条件\r\n    yearMonthRadioChange(e) {\r\n      if (e == 1) {\r\n        this.yearMonthType = 'year'\r\n        this.yearMonthValue = dayjs(new Date()).format('YYYY')\r\n      } else if (e == 2) {\r\n        this.yearMonthType = 'month'\r\n        this.yearMonthValue = dayjs(new Date()).format('YYYY-MM')\r\n      }\r\n      this.initData()\r\n    },\r\n    // 年 月 切换\r\n    yearMonthPickerChange() {\r\n      this.initData()\r\n    },\r\n    // 待办报修\r\n    async getWorkOrderManageList() {\r\n      const res = await GetWorkOrderManageList({\r\n        model: {\r\n          Date: [],\r\n          Order_Code: '',\r\n          Order_Name: '',\r\n          Create_Date: '',\r\n          Create_EDate: '',\r\n          State: '',\r\n          WorkOrder_Setup_Id: 'jsbx',\r\n          Maintain_Person: '',\r\n          WorkOrder_State: 0,\r\n          Type: 1,\r\n          type: 1\r\n        },\r\n        pageInfo: {\r\n          Page: 1,\r\n          PageSize: 10,\r\n          SortName: 'Create_Date',\r\n          SortOrder: 'DESC'\r\n        }\r\n      })\r\n      this.pendingRepairRequestData = res.Data.Data\r\n    },\r\n    // 获取工单满意度统计\r\n    async getSatisfactionStatistics() {\r\n      const res = await GetSatisfactionStatistics({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairSatisfactionConfig = res.Data\r\n      const average = res.Data.Avg || 0\r\n      let averageStr = ''\r\n      if (average >= 4) {\r\n        averageStr = '优'\r\n      } else if (average < 4 && average >= 3) {\r\n        averageStr = '良'\r\n      } else if (average < 3 && average >= 2) {\r\n        averageStr = '中'\r\n      } else if (average < 2) {\r\n        averageStr = '差'\r\n      }\r\n      this.repairSatisfactionOptions.series = [\r\n        {\r\n          name: '外部刻度',\r\n          type: 'gauge',\r\n          radius: '100%',\r\n          splitNumber: 20,\r\n          min: 0,\r\n          max: 100,\r\n          startAngle: 225,\r\n          endAngle: -45,\r\n          axisLine: {\r\n            roundCap: true,\r\n            lineStyle: {\r\n              width: 0,\r\n              opacity: 0\r\n            }\r\n          },\r\n          axisLabel: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            show: true,\r\n            length: 3,\r\n            lineStyle: {\r\n              color: '#a9afb8',\r\n              width: 1\r\n            }\r\n          },\r\n          detail: {\r\n            show: false\r\n          },\r\n          pointer: {\r\n            show: false\r\n          }\r\n        },\r\n        {\r\n          name: '内部刻度',\r\n          type: 'gauge',\r\n          radius: '80%',\r\n          splitNumber: 20,\r\n          min: 0,\r\n          max: 100,\r\n          startAngle: 225,\r\n          endAngle: -45,\r\n          title: {\r\n            show: true,\r\n            fontSize: 12,\r\n            color: '#505D6F',\r\n            offsetCenter: ['0', '-20%']\r\n          },\r\n          data: [\r\n            {\r\n              value: [],\r\n              name: '报修处理满意度'\r\n            }\r\n          ],\r\n          detail: {\r\n            valueAnimation: true,\r\n            formatter: () => {\r\n              return averageStr\r\n            },\r\n            fontSize: 14,\r\n            color: '#298DFF',\r\n            offsetCenter: [0, '10%']\r\n          },\r\n          axisLine: {\r\n            roundCap: true,\r\n            lineStyle: {\r\n              width: 20,\r\n              color: [\r\n                [\r\n                  (average * 20) / 100,\r\n                  {\r\n                    type: 'linear',\r\n                    x: 0,\r\n                    y: 1,\r\n                    x2: 0,\r\n                    y2: 0,\r\n                    colorStops: [\r\n                      {\r\n                        offset: 0,\r\n                        color: '#50FFE4'\r\n                      },\r\n                      {\r\n                        offset: 1,\r\n                        color: '#298DFF'\r\n                      }\r\n                    ]\r\n                  }\r\n                ],\r\n                [1, 'rgba(225,225,225,0.4)']\r\n              ]\r\n            }\r\n          },\r\n          axisLabel: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          },\r\n          pointer: {\r\n            show: false\r\n          }\r\n        }\r\n      ]\r\n    },\r\n    // 获取处理人员完成排名\r\n    async getProcessedRank() {\r\n      const res = await GetProcessedRank({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairProcessingPersonnelCompleteRankingData = res.Data\r\n    },\r\n    // 获取各车间工单情况\r\n    async getWorkShopCase() {\r\n      const res = await GetWorkShopCase({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      console.log(res, 'res')\r\n      const repairStatusEachWorkshopOptions = res.Data.map((item) => ({\r\n        name: item.Label,\r\n        value: item.Value,\r\n        percent: item.Rate\r\n      }))\r\n      this.repairStatusEachWorkshopOptions.series[0].data =\r\n        repairStatusEachWorkshopOptions\r\n      this.repairStatusEachWorkshopOptions.legend.formatter = function(name) {\r\n        const obj = repairStatusEachWorkshopOptions.find(\r\n          (item) => item.name == name\r\n        )\r\n        return `{labelMark|${obj.name}} {valueMark|${obj.value} 次}  {percentMark|${obj.percent} %}`\r\n      }\r\n    },\r\n    // 获取报修工单故障类型\r\n    async getWorkOrderErrorType() {\r\n      const res = await GetWorkOrderErrorType({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairFaultTypeOptions.series[0].data = res.Data.map((item) => ({\r\n        name: item.Label,\r\n        value: item.Value,\r\n        percent: item.Rate\r\n      }))\r\n    },\r\n    // 获取各车间趋势\r\n    async getWorkOrderTrend() {\r\n      const res = await GetWorkOrderTrend({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      let xAxisData = []\r\n      this.trendRepairReportsVariousWorkshopsOptions.series = res.Data.map(\r\n        (item) => {\r\n          xAxisData = item.ShopData.map((ele) => ele.Label)\r\n          if (item.ShopName == '报修数量') {\r\n            return {\r\n              name: item.ShopName,\r\n              type: 'line',\r\n              smooth: true,\r\n              symbol: 'none',\r\n              data: item.ShopData.map((ele) => ele.Value)\r\n            }\r\n          } else {\r\n            return {\r\n              name: item.ShopName,\r\n              type: 'bar',\r\n              stack: 'vehicle',\r\n              emphasis: {\r\n                focus: 'series'\r\n              },\r\n              data: item.ShopData.map((ele) => ele.Value)\r\n            }\r\n          }\r\n        }\r\n      )\r\n      this.trendRepairReportsVariousWorkshopsOptions.xAxis.data = xAxisData\r\n    },\r\n\r\n    // 获取设备完好率\r\n    async getDeviceServiceabilityRate() {\r\n      const res = await GetDeviceServiceabilityRate({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.equipmentIntegrityRate = res.Data\r\n      this.equipmentIntegrityRateOptions.series[0].axisLine.lineStyle = {\r\n        color: [\r\n          [res.Data.ServiceabilityRate / 100, 'rgba(0, 211, 167, 0.3)'],\r\n          [1, '#f0f2f8']\r\n        ],\r\n        width: 16\r\n      }\r\n      this.equipmentIntegrityRateOptions.series[1].label.formatter = function(\r\n        pamars\r\n      ) {\r\n        return `${res.Data.ServiceabilityRate}%`\r\n      }\r\n    },\r\n    // 获取设备故障率排行\r\n    async getEquipFailureRateRank() {\r\n      const res = await GetEquipFailureRateRank({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.equipmentFailureRateRanking = res.Data\r\n    },\r\n\r\n    // 获取工单总览统计\r\n    async getTimeoutStatistics() {\r\n      const res = await GetTimeoutStatistics({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairResponseConfig = res.Data\r\n      this.repairResponseOptions.xAxis[0].data = res.Data.List.map(\r\n        (item) => item.Date\r\n      )\r\n      this.repairResponseOptions.series[0].data = res.Data.List.map(\r\n        (item) => item.Timely\r\n      )\r\n      this.repairResponseOptions.series[1].data = res.Data.List.map(\r\n        (item) => item.Timeout\r\n      )\r\n      this.repairResponseOptions.series[2].data = res.Data.List.map(\r\n        (item) => item.Percent\r\n      )\r\n    },\r\n    // 获取工单响应超时统计\r\n    async getWorkorderStatistics() {\r\n      const res = await GetWorkorderStatistics({\r\n        WorkOrderType: 'jsbx',\r\n        DateType: this.yearMonthRadio,\r\n        StartTime: this.getStartTime(this.yearMonthRadio)\r\n      })\r\n      this.repairOverview = res.Data\r\n    },\r\n    // 获取等待时长\r\n    getWaitingTime(date) {\r\n      const startDate = new Date(date)\r\n      var endDate = new Date() // 获取当前时间\r\n      var difference = Math.abs(endDate - startDate)\r\n      var days = Math.floor(difference / (1000 * 60 * 60 * 24))\r\n      var hours = Math.floor(\r\n        (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)\r\n      )\r\n      var minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))\r\n      var formattedDifference = ''\r\n\r\n      if (days > 0) {\r\n        formattedDifference += days + '天'\r\n      }\r\n      if (hours > 0) {\r\n        formattedDifference += hours + '小时'\r\n      }\r\n      if (minutes > 0 || (days === 0 && hours === 0)) {\r\n        formattedDifference += minutes + '分钟'\r\n      }\r\n      return formattedDifference\r\n    },\r\n    // 获取状态样式\r\n    getStatusStyle(row) {\r\n      return this.pendingRepairRequestStatus.find(\r\n        (item) => item.value == row.State\r\n      )\r\n    },\r\n    // 获取日期时间\r\n    getStartTime(type) {\r\n      if (type == 1) {\r\n        return dayjs(this.yearMonthValue).format('YYYY')\r\n      } else if (type == 2) {\r\n        return dayjs(this.yearMonthValue).format('YYYY-MM')\r\n      }\r\n    },\r\n    // 设置表格自动滚动\r\n    autoScroll(stop) {\r\n      const table = this.$refs.scroll_Table\r\n      // 拿到表格中承载数据的div元素\r\n      const divData = table.$refs.bodyWrapper\r\n      // 拿到元素后，对元素进行定时增加距离顶部距离，实现滚动效果(此配置为每100毫秒移动1像素)\r\n      if (stop) {\r\n        // 再通过事件监听，监听到 组件销毁 后，再执行关闭计时器。\r\n        window.clearInterval(this.scrolltimer)\r\n      } else {\r\n        this.scrolltimer = window.setInterval(() => {\r\n          // 元素自增距离顶部1像素\r\n          divData.scrollTop += 2\r\n          // 判断元素是否滚动到底部(可视高度+距离顶部=整个高度)\r\n          if (\r\n            divData.clientHeight + divData.scrollTop ==\r\n            divData.scrollHeight\r\n          ) {\r\n            // 重置table距离顶部距离\r\n            divData.scrollTop = 0\r\n            // 重置table距离顶部距离。值=(滚动到底部时，距离顶部的大小) - 整个高度/2\r\n            // divData.scrollTop = divData.scrollTop - divData.scrollHeight / 2\r\n          }\r\n        }, 120) // 滚动速度\r\n      }\r\n    },\r\n\r\n    // 设置表格颜色\r\n    repairProcessingPersonnelCompleteRankingDataClassName({ row, rowIndex }) {\r\n      if (this.isEvenOrOdd(rowIndex + 1)) {\r\n        return 'row-one'\r\n      } else {\r\n        return 'row-two'\r\n      }\r\n    },\r\n    // 设置表格颜色\r\n    pendingRepairRequestDataClassName({ row, rowIndex }) {\r\n      if (this.isEvenOrOdd(rowIndex + 1)) {\r\n        return 'row-one'\r\n      } else {\r\n        return 'row-two'\r\n      }\r\n    },\r\n    //  判断是否是偶数行 还是奇数行\r\n    isEvenOrOdd(number) {\r\n      if (number % 2 === 0) {\r\n        return true\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    // 查看更多\r\n    lookMoreDetail() {\r\n      // let Url = this.jumpUrlList.find(\r\n      //   (item) => item.ModuleCode == ModuleCode\r\n      // ).Url;\r\n      const Platform = 'digitalfactory'\r\n      const ModuleId = localStorage.getItem('ModuleId')\r\n      const ModuleCode = localStorage.getItem('ModuleCode')\r\n      // 获取本月的第一天\r\n      const startOfMonth = dayjs().startOf('month')\r\n      // 获取本月的最后一天\r\n      const endOfMonth = dayjs().endOf('month')\r\n      // 输出结果\r\n      // Create_Date:\"2024-07-19\"\r\n      // Create_EDate:\"2024-08-14\"\r\n      this.$qiankun.switchMicroAppFn(\r\n        Platform,\r\n        'gzt',\r\n        '7622d042-b114-46a0-b1ba-b5621622f058',\r\n        `/business/maintenanceAndUpkeep/workOrderManagement?State=0&ActiveName=first&isJump=true`\r\n      )\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.workOrderStatistics_repair {\r\n  // padding: 10px 15px;\r\n  // height: calc(100vh - 90px);\r\n  overflow-y: auto;\r\n  .header {\r\n    display: flex;\r\n    // align-items: center;\r\n    justify-content: space-between;\r\n    height: 22px;\r\n    > span {\r\n      font-weight: bold;\r\n    }\r\n    .title_content {\r\n      display: flex;\r\n      font-weight: bold;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      span {\r\n        margin-right: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .search_content {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    .label {\r\n      margin-right: 10px;\r\n    }\r\n    .radio {\r\n      margin-right: 10px;\r\n    }\r\n    .picker {\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .repairSatisfaction_content {\r\n    height: 220px;\r\n    width: 100%;\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .repairSatisfactionlists {\r\n      display: flex;\r\n      flex-direction: column;\r\n      .repairSatisfactionlist {\r\n        padding: 2px 0px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        .label {\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          margin-right: 10px;\r\n        }\r\n        .value {\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .repairOverview_content {\r\n    height: 220px;\r\n    .bottom {\r\n      margin-top: 20px;\r\n      display: grid;\r\n      width: 100%;\r\n      grid-template-columns: repeat(3, calc(33% - 10px));\r\n      grid-gap: 20px; /* 设置间距 */\r\n      .main {\r\n        border-radius: 8px 8px 8px 8px;\r\n        padding: 10px 15px;\r\n        background: #fafdff;\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        justify-content: center;\r\n        .left {\r\n          width: 70px;\r\n          height: 70px;\r\n        }\r\n        .right {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          .text {\r\n            font-weight: bold;\r\n            font-size: 24px;\r\n            margin-top: 10px;\r\n          }\r\n          .value {\r\n            font-weight: 400;\r\n            font-size: 14px;\r\n            color: #666666;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .top {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      justify-content: space-around;\r\n      background: #fafdff;\r\n      border-radius: 8px 8px 8px 8px;\r\n      padding: 10px 15px;\r\n      .main {\r\n        display: flex;\r\n        flex-direction: row;\r\n        .left {\r\n          width: 80px;\r\n          height: 80px;\r\n        }\r\n        .right {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          .text {\r\n            font-weight: bold;\r\n            font-size: 24px;\r\n          }\r\n          .value {\r\n            font-weight: 400;\r\n            font-size: 14px;\r\n            color: #666666;\r\n            margin-top: 10px;\r\n            display: flex;\r\n            flex-direction: row;\r\n            align-items: center;\r\n            > span {\r\n              margin-right: 10px;\r\n              display: flex;\r\n              flex-direction: row;\r\n              align-items: center;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .maintenanceWorkOrderProcessingStatus_content {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 300px;\r\n    .left {\r\n      width: 50%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      padding: 0px 18px;\r\n      .title {\r\n        color: rgba(34, 40, 52, 0.85);\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        padding: 18px 0px;\r\n      }\r\n      .left_content {\r\n        background-color: #fafdff;\r\n        padding: 10px 15px;\r\n      }\r\n    }\r\n    .right {\r\n      width: 50%;\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      padding: 0px 18px;\r\n      .title {\r\n        color: rgba(34, 40, 52, 0.85);\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        padding: 18px 0px;\r\n      }\r\n      .right_content {\r\n        background-color: #fafdff;\r\n        padding: 10px 15px;\r\n      }\r\n    }\r\n    .item {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      margin-bottom: 20px;\r\n      .top {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-bottom: 6px;\r\n        font-size: 14px;\r\n        .left {\r\n          color: #333333;\r\n          font-size: 14px;\r\n        }\r\n        .right_color_one {\r\n          color: #298dff;\r\n        }\r\n        .right_color_two {\r\n          color: #00d3a7;\r\n        }\r\n        .right {\r\n          display: flex;\r\n          flex-direction: row;\r\n          .one {\r\n            margin-right: 14px;\r\n          }\r\n          .two {\r\n            width: 36px;\r\n            display: block;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .productionEquipmentLoadRateRanking_content {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: column;\r\n    height: 220px;\r\n    overflow-y: scroll;\r\n    scrollbar-width: none;\r\n    // scrollbar-color: transparent transparent;\r\n    .item {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      margin-bottom: 20px;\r\n      .top {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-bottom: 6px;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .equipmentIntegrityRate_content {\r\n    height: 220px;\r\n    display: flex;\r\n    flex-direction: row;\r\n    justify-content: space-between;\r\n    .equipmentIntegrityRatelists {\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: center;\r\n      width: 40%;\r\n      .equipmentIntegrityRatelist {\r\n        padding: 2px 0px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        margin-right: 20px;\r\n        .label {\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          margin-right: 10px;\r\n        }\r\n        .value {\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .repairResponse_content {\r\n    height: 300px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    .repairResponselists {\r\n      margin-top: -40px;\r\n      background: #fafdff;\r\n      border-radius: 8px 8px 8px 8px;\r\n      padding: 10px 30px;\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      justify-content: space-around;\r\n      width: 60%;\r\n      margin-bottom: 10px;\r\n      .repairResponselist {\r\n        padding: 2px 0px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        margin-right: 20px;\r\n        .label {\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          margin-right: 10px;\r\n        }\r\n        .value {\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .equipmentStartupStatus_content {\r\n    height: 220px;\r\n  }\r\n  .equipmentFailureTrend_content {\r\n    height: 320px;\r\n    .tablenumber {\r\n      width: 30px;\r\n      height: 23px;\r\n      background-size: 100%;\r\n      background-repeat: no-repeat;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      > span {\r\n        margin-top: 10px;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n  .repairStatusEachWorkshop_content {\r\n    height: 300px;\r\n    .right {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      .right_num {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  ::v-deep .el-card__header {\r\n    border-bottom: none !important;\r\n  }\r\n  ::v-deep .el-progress__text {\r\n    font-size: 18px !important;\r\n    color: #666666 !important;\r\n  }\r\n  ::v-deep.el-table .row-one {\r\n    background: rgba(41, 141, 255, 0.03) !important;\r\n  }\r\n\r\n  ::v-deep .el-table .row-two {\r\n    background: rgba(255, 255, 255, 1) !important;\r\n  }\r\n\r\n  // ::v-deep .el-radio-button__inner {\r\n  //   background-color: #ffffff;\r\n  //   // padding: 6px 32px;\r\n  //   height: 32px;\r\n  //   // line-height: 32px;\r\n  //   width: 80px;\r\n  //   font-size: 14px;\r\n  // }\r\n}\r\n</style>\r\n"]}]}