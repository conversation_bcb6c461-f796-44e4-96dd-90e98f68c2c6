{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlPersonnelManagement\\index.vue?vue&type=style&index=0&id=3a6d5ab0&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlPersonnelManagement\\index.vue", "mtime": 1755506574156}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLm10MjAgew0KICBtYXJnaW4tdG9wOiAxMHB4Ow0KfQ0KLmxheW91dHsNCiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gOTBweCk7DQogIG92ZXJmbG93OiBhdXRvOw0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAspBA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/accessControl/accessControlPersonnelManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        >\r\n          <template #customBtn=\"{ slotScope }\">\r\n            <el-button type=\"text\" @click=\"handleEnable(slotScope)\">{{\r\n              slotScope.Status == \"0\" ? \"禁用\" : \"启用\"\r\n            }}</el-button></template>\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport DialogForm from './dialogForm.vue'\r\nimport DialogFormLook from './dialogFormLook.vue'\r\nimport DialogFormImport from './dialogFormImport.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport {\r\n  GetPersonnelList,\r\n  SubPersonnel,\r\n  EntrancePersonnelInfo,\r\n  UpdateStatus,\r\n  DelPersonnel,\r\n  PersonnelImportTemplate,\r\n  EntrancePersonnelImport,\r\n  ExportEntrancePersonnel,\r\n  GetRole,\r\n  GetDepartment,\r\n  GetCompany,\r\n  GetDictionaryDetailListByCode\r\n} from '@/api/business/accessControl'\r\nimport { GetOssUrl } from '@/api/sys/index'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        P_Name: '',\r\n        Contact_Way: '',\r\n        P_Type: '',\r\n        Position_Name: '',\r\n        P_Unit: '',\r\n        P_Department: ''\r\n        // P_Workshop: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'P_Name', // 字段ID\r\n            label: '姓名', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Contact_Way', // 字段ID\r\n            label: '联系方式', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'P_Type',\r\n            label: '人员类型',\r\n            type: 'select',\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position_Name',\r\n            label: '岗位名称',\r\n            type: 'select',\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'P_Unit',\r\n            label: '所属单�?,\r\n            type: 'select',\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'P_Department',\r\n            label: '所属部�?,\r\n            type: 'select',\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n          // {\r\n          //   key: 'P_Workshop',\r\n          //   label: '所属车�?,\r\n          //   type: 'select',\r\n          //   options: [\r\n          //   ],\r\n          //   change: (e) => {\r\n          //     console.log(e)\r\n          //   }\r\n          // }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '新增',\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载�?\n              disabled: false, // 是否禁用\r\n              icon: '', //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleCreate()\r\n              }\r\n            },\r\n            {\r\n              text: '导入模板下载',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleTemDownload()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导入',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleImport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        operateOptions: {\r\n          width: 200\r\n        },\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          // {\r\n          //   width: 60,\r\n          //   label: '序号',\r\n          //   otherOptions: {\r\n          //     type: 'index',\r\n          //     align: 'center'\r\n          //   } // key\r\n          // },\r\n          {\r\n            label: '员工工号',\r\n            key: 'P_Number'\r\n          },\r\n          {\r\n            label: '姓名',\r\n            key: 'P_Name'\r\n          },\r\n          {\r\n            label: '性别',\r\n            key: 'P_Sex'\r\n          },\r\n          {\r\n            label: '联系方式',\r\n            key: 'Contact_Way'\r\n          },\r\n          {\r\n            label: '人脸照片',\r\n            key: 'Face_picture',\r\n            otherOptions: {\r\n              align: 'center'\r\n            },\r\n            render: (row) => {\r\n              if (row.Face_Picture_Url) {\r\n                return this.$createElement('el-image', {\r\n                  style: {\r\n                    width: '40px',\r\n                    height: '40px'\r\n                  },\r\n                  attrs: {\r\n                    fit: 'cover',\r\n                    src: row.Face_Picture_Url,\r\n                    previewSrcList: [row.Face_Picture_Url]\r\n                  }\r\n                })\r\n              }\r\n            }\r\n          },\r\n          {\r\n            label: '人员类型',\r\n            key: 'P_Type'\r\n          },\r\n          {\r\n            label: '岗位名称',\r\n            key: 'Position_Name'\r\n          },\r\n          {\r\n            label: '所属部�?,\r\n            key: 'P_Department'\r\n          },\r\n          // {\r\n          //   label: '所属班�?,\r\n          //   key: 'P_Group'\r\n          // },\r\n          // {\r\n          //   label: '所属车�?,\r\n          //   key: 'P_Workshop'\r\n          // },\r\n          {\r\n            label: '所属单�?,\r\n            key: 'P_Unit'\r\n          },\r\n          {\r\n            label: '状�?,\r\n            key: 'Status',\r\n            render: (row) => {\r\n              const text = row.Status === '0' ? '启用' : '禁用'\r\n              return this.$createElement('span', {}, text)\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '修改',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          // {\r\n          //   actionLabel: '启用',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleEnable(index, row, 'edit')\r\n          //   }\r\n          // },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  async created() {\r\n    // 岗位类型\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'Position_Name'\r\n    ).options = await this.initGetRole('Entrance_Type')\r\n    // 所属部�?\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'P_Department'\r\n    ).options = await this.initGetDepartment('Entrance_Type')\r\n    // 所属单�?\n    this.customForm.formItems.find((item) => item.key === 'P_Unit').options =\r\n      await this.initGetCompany('Entrance_Type')\r\n    // 人员类型\r\n    this.customForm.formItems.find((item) => item.key === 'P_Type').options =\r\n      await this.initDeviceType('P_Type')\r\n    this.init()\r\n  },\r\n  methods: {\r\n    // 人员类型\r\n    async initDeviceType(code) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode: code\r\n      })\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    // 岗位类型\r\n    async initGetRole(code) {\r\n      const res = await GetRole({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    // 所属部�?\n    async initGetDepartment(code) {\r\n      const res = await GetDepartment({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    // 所属单�?\n    async initGetCompany(code) {\r\n      const res = await GetCompany({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getPersonnelList()\r\n    },\r\n    init() {\r\n      this.getPersonnelList()\r\n    },\r\n    async getPersonnelList() {\r\n      const res = await GetPersonnelList({\r\n        ParameterJson: [\r\n          {\r\n            Key: '',\r\n            Value: [null],\r\n            Type: '',\r\n            Filter_Type: ''\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        Search: '',\r\n        SortName: '',\r\n        SortOrder: '',\r\n        P_Name: '',\r\n        Contact_Way: '',\r\n        P_Type: '',\r\n        Position_Name: '',\r\n        P_Unit: '',\r\n        P_Department: '',\r\n        P_Workshop: '',\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        console.log(res)\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n        this.handelImage(res.Data.Data)\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    async handelImage(data) {\r\n      const promises = data.map(async(v) => {\r\n        let Face_Picture_Url = ''\r\n        if (v.Face_Picture) {\r\n          const imageRes = await GetOssUrl({ url: v.Face_Picture })\r\n          Face_Picture_Url = imageRes.Data\r\n        }\r\n        return (function() {\r\n          const Face_PictureUrl = Face_Picture_Url\r\n          // 使用闭包来捕获变量的当前�?\n          v.Face_Picture_Url = v.Face_Picture ? Face_PictureUrl : ''\r\n          return v\r\n        })()\r\n      })\r\n\r\n      Promise.all(promises)\r\n        .then((data) => {\r\n          this.customTableConfig.tableData = data\r\n          console.log(data)\r\n        })\r\n        .catch((error) => {\r\n          console.error(error)\r\n        })\r\n    },\r\n\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.componentsConfig = {\r\n        disabled: false,\r\n        title: '新增'\r\n      }\r\n      this.dialogVisible = true\r\n      this.currentComponent = DialogForm\r\n    },\r\n    // 启动 停用\r\n    async handleEnable(row) {\r\n      const text = row.Status === '0' ? '禁用' : '启用'\r\n      const textStatus = row.Status === '0' ? '1' : '0'\r\n      this.$confirm(`确认${text}？`, {\r\n        type: 'warning'\r\n      })\r\n        .then(async(_) => {\r\n          const res = await UpdateStatus({\r\n            id: row.Id,\r\n            status: textStatus\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.init()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n        .catch((_) => {})\r\n    },\r\n    handleDelete(index, row) {\r\n      this.$confirm('确认删除�?, {\r\n        type: 'warning'\r\n      })\r\n        .then(async(_) => {\r\n          const res = await DelPersonnel({\r\n            id: row.Id\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.init()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n        .catch((_) => {})\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.currentComponent = DialogFormLook\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: true,\r\n          title: '查看',\r\n          row: row\r\n        }\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        this.currentComponent = DialogForm\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: true,\r\n          title: '编辑',\r\n          row: row\r\n        }\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    async handleTemDownload() {\r\n      const res = await PersonnelImportTemplate({\r\n        code: 'accessControlPersonnelManagement'\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n        this.$message({\r\n          type: 'success',\r\n          message: '下载成功!'\r\n        })\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    async handleExport() {\r\n      if (this.tableSelection.length == 0) {\r\n        this.$message.warning('请选择数据在导�?)\r\n        return\r\n      }\r\n      const res = await ExportEntrancePersonnel({\r\n        id: this.tableSelection.map((item) => item.Id).join(','),\r\n        code: 'accessControlPersonnelManagement',\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    async handleImport() {\r\n      this.dialogTitle = '批量导入'\r\n      this.currentComponent = DialogFormImport\r\n      this.componentsConfig = {\r\n        disabled: true,\r\n        title: '批量导入'\r\n      }\r\n      this.dialogVisible = true\r\n      // const res = await ExportEntrancePersonnel({\r\n      //   id: this.tableSelection.map((item) => item.Id),\r\n      //   ...this.ruleForm\r\n      // })\r\n      // if (res.IsSucceed) {\r\n      //   console.log(res)\r\n      //   downloadFile(res.Data, '21')\r\n      // }\r\n    },\r\n    async handleAllExport() {\r\n      const res = await ExportEntrancePersonnel({\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        Ids: [],\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}