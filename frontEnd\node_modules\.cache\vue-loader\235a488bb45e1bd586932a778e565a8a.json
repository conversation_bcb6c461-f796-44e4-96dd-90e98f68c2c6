{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJsafetyManagement\\equipmentManagement\\index.vue?vue&type=style&index=0&id=25032426&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJsafetyManagement\\equipmentManagement\\index.vue", "mtime": 1755674552406}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5lcXVpcG1lbnRNYW5hZ2VtZW50IHsNCiAgLy8gaGVpZ2h0OiBjYWxjKDEwMHZoIC0gOTBweCk7DQogIC8vIG92ZXJmbG93OiBoaWRkZW47DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoXA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/SZCJsafetyManagement/equipmentManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 equipmentManagement\">\r\n    <custom-layout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"submitForm\"\r\n          @resetForm=\"fetchData\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </custom-layout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport getGridByCode from '../mixins/index'\r\nimport { GetEquipmentListSZCJ, MonitoreImportTemplate, ExportMonitoreEquipment, LookVideo, DelEquipment, MonitoreEquipmentInfo } from '@/api/business/safetyManagement'\r\nimport DialogForm from './components/dialogForm.vue'\r\nimport WatchVideoDialog from './components/watchVideoDialog.vue'\r\nimport DeviceInfoDialog from './components/deviceInfoDialog.vue'\r\nimport ImportFile from './components/importFile.vue'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport { GetDictionaryTreeDetailListByCode } from '@/api/sys'\r\nimport { GetParkArea, GetTreeAddress } from '@/api/business/energyManagement.js'\r\n\r\nexport default {\r\n  components: {\r\n    CustomLayout,\r\n    CustomTable,\r\n    CustomForm\r\n  },\r\n  mixins: [getGridByCode],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        EquipmentName: '',\r\n        EquipmentNumber: '',\r\n        InstallSite: '',\r\n        EquipmentType: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'EquipmentName',\r\n            label: '设备名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentNumber',\r\n            label: '设备编码',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'InstallSite',\r\n            label: '设备部署位置',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentType',\r\n            label: '设备类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '下载模板',\r\n              onclick: () => {\r\n                this.handleDownTemplate()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导入',\r\n              onclick: () => {\r\n                this.handleImport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导出',\r\n              onclick: () => {\r\n                this.handleExport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量删除',\r\n              onclick: () => {\r\n                this.handleDelete('batch')\r\n              }\r\n            },\r\n            {\r\n              text: '新增',\r\n              type: 'primary',\r\n              onclick: () => {\r\n                this.handleEdit()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [20, 40, 60, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 1000,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center'\r\n        },\r\n        tableActionsWidth: 260,\r\n        tableActions: [\r\n          {\r\n            actionLabel: '监控链接',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDeviceInfo(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete('single', row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '查看视频',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleLookVideo(row.Id)\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '新增',\r\n      currentComponent: null,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n        },\r\n        fetchData: () => {\r\n          this.fetchData()\r\n        }\r\n      },\r\n      multipleSelection: [],\r\n      Park_Area: ''\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchData()\r\n    this.getGridByCode('equipmentManagement')\r\n    GetParkArea().then(res => {\r\n      this.Park_Area = res.Data\r\n    })\r\n  },\r\n  async mounted() {\r\n    this.customForm.formItems[3].options = await this.getDictionaryDetailListByCode()\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      GetEquipmentListSZCJ({\r\n        ...this.ruleForm, Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n          this.customTableConfig.tableData = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    handleDelete(type, id) {\r\n      if (type == 'batch') {\r\n        if (this.multipleSelection.length == 0) {\r\n          this.$message.warning('请选择数据!')\r\n          return\r\n        } else {\r\n          id = this.multipleSelection.map(item => item.Id).join(',')\r\n        }\r\n      }\r\n      this.$confirm('确认删除？', {\r\n        type: 'warning'\r\n      })\r\n        .then(async(_) => {\r\n          await DelEquipment({ id }).then(res => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success('删除成功!')\r\n              this.fetchData()\r\n            } else {\r\n              this.$message.error(res.Message)\r\n            }\r\n          })\r\n        })\r\n        .catch((_) => { })\r\n    },\r\n    handleEdit(index, row, type = 'add') {\r\n      this.currentComponent = DialogForm\r\n      if (type === 'add') {\r\n        this.dialogTitle = '新增'\r\n        this.componentsConfig.Data = {\r\n          Monitore_Equipment_Number: '',\r\n          Monitore_Equipment_Name: '',\r\n          Monitore_Equipment_SN_Number: '',\r\n          Monitore_Equipment_Type: '',\r\n          Pisition: '',\r\n          Monitore_Equipment_Name: '',\r\n          Park_Area: this.Park_Area,\r\n          Site: '',\r\n          Address: '',\r\n          Brand: '',\r\n          Version: '',\r\n          Equipment_Purpose_Catetory: ''\r\n        }\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        row.Park_area = [row.Purpose_Catetory, row.Scene, row.Site]\r\n        this.componentsConfig.Data = { ...row, Park_Area: this.Park_Area }\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    submitForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData()\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.fetchData()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.customTableConfig.currentPage = val\r\n      this.fetchData()\r\n    },\r\n    handleSelectionChange(data) {\r\n      console.log(data)\r\n      this.multipleSelection = data\r\n    },\r\n    handleLookVideo(id) {\r\n      this.currentComponent = WatchVideoDialog\r\n      this.dialogTitle = '查看视频'\r\n      this.dialogVisible = true\r\n      this.componentsConfig.Data = id\r\n    },\r\n    handleDeviceInfo(id) {\r\n      this.currentComponent = DeviceInfoDialog\r\n      this.dialogTitle = '监控链接'\r\n      this.dialogVisible = true\r\n      MonitoreEquipmentInfo({ id }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.componentsConfig.Data = {\r\n            Mainstream_Code: res.Data.Mainstream_Code,\r\n            Substream_Code: res.Data.Substream_Code,\r\n            Url: res.Data.Url\r\n          }\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleDownTemplate() {\r\n      MonitoreImportTemplate({ code: 'equipmentManagement' }).then(res => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data, '安防监控设备管理导入模板')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleExport() {\r\n      let id = ''\r\n      if (this.multipleSelection.length == 0) {\r\n        this.$message.warning('请选择数据!')\r\n        return\r\n      } else {\r\n        id = this.multipleSelection.map(item => item.Id).join(',')\r\n      }\r\n      ExportMonitoreEquipment({\r\n        code: 'equipmentManagement',\r\n        id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('导出成功')\r\n          downloadFile(res.Data, '安防监控设备管理数据')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleImport() {\r\n      this.currentComponent = ImportFile\r\n      this.dialogTitle = '批量导入'\r\n      this.dialogVisible = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang='scss'>\r\n.equipmentManagement {\r\n  // height: calc(100vh - 90px);\r\n  // overflow: hidden;\r\n}\r\n</style>\r\n"]}]}