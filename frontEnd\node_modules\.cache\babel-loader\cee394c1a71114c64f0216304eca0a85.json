{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\equipmentAnalysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\equipmentManagement\\equipmentAnalysis\\index.vue", "mtime": 1755674552419}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetStatusAnalyseEqt", "GetErrorRank", "GetTopEqtError", "GetStartAnalyseEqt", "GetLoadAnalyseEqt", "GetConsumptionAnalyseEqt", "GetDeviceAnalyseWorkOrderCount", "GetWorkOrderHandlingCount", "GetWorkOrderErrorTypeList", "GetDeviceServiceabilityRate", "GetAGVAnalyseEqt", "GetDictionaryDetailListByParentId", "GetProduceTrend", "GetDeviceStatusDetails", "GetStartAnalyseEqtDetails", "equipmentIntegrityRate", "equipmentOperation", "repairFaultType", "customProcess", "loadRateRanking", "maintenanceWorkOrder", "equipmentOperationStatus", "agvBatteryLevel", "efficiencyAnalysis", "equipmentFailure", "efficiencyAnalysisDetail", "equipmentFailureDetail", "GetEquipmentAssetPageListPJ", "dayjs", "<PERSON><PERSON>", "use", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Line<PERSON>hart", "<PERSON><PERSON><PERSON>", "GridComponent", "LegendComponent", "TooltipComponent", "TitleComponent", "DataZoomComponent", "name", "components", "mixins", "data", "_this", "currentComponent", "componentsConfig", "componentsFuns", "open", "dialogVisible", "close", "fetchData", "currentComponentTitle", "deviceStatusListLoading", "deviceStatusList", "rgvDataListLoading", "rgvDataList", "equipmentMaintenanceIntegrityRateLoading", "equipmentMaintenanceReadinessRateMonth", "Date", "format", "maintenanceWorkOrderProcessingStatusLoading", "maintenanceWorkOrderProcessingStatusOne", "maintenanceWorkOrderProcessingStatusTwo", "monthRankWeldingEquipmentConsumablesUsageEfficiencyValue", "rankProdEquipmentRateMonthLoading", "rankProdEquipmentRateMonth", "monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading", "monthRankWeldingEquipmentConsumablesUsageEfficiency", "equipmentAbnormalityRankingDataLoading", "equipmentAbnormalityRankingData", "latestAlarmInformationDataLoading", "latestAlarmInformationData", "equipmentStartupStatusMonthValue", "equipmentOperationValue", "equipmentStartupStatusMonthOptions", "productionVolumeTrendValue", "productionVolumeTrendLoading", "productionVolumeTrendSelectOptions", "label", "value", "productionVolumeTrendOptions", "tooltip", "trigger", "legend", "show", "xAxis", "type", "axisLine", "axisTick", "yAxis", "position", "axisLabel", "formatter", "textStyle", "color", "nameTextStyle", "fontSize", "series", "valueFormatter", "concat", "yAxisIndex", "equipmentFailureTrendValue", "equipmentFailureTrendSelectOptions", "equipmentFailureTrendLoading", "equipmentFailureTrendOptions", "logBase", "smooth", "equipmentMaintenanceIntegrityRateObj", "workOrderErrorTypeLoading", "workOrderErrorTypeList", "equipmentOperationListLoading", "equipmentOperationList", "activated", "mounted", "getEquipmentAssetPageListPJ", "getDictionaryDetailListByParentId", "getStatusAnalyseEqt", "getErrorRank", "getTopEqtError", "getLoadAnalyseEqt", "getConsumptionAnalyseEqt", "getDeviceAnalyseWorkOrderCount", "getWorkOrderHandlingCount", "getWorkOrderErrorTypeList", "getDeviceServiceabilityRate", "getAGVAnalyseEqt", "getProduceTrend", "getDeviceStatusDetails", "getStartAnalyseEqtDetails", "methods", "productionVolumeTrendClick", "selectOtherOptions", "equipmentFailureTrendClick", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "IsSucceed", "Data", "$message", "message", "Message", "stop", "_this3", "_callee2", "_callee2$", "_context2", "Display_Name", "Device_Type_Id", "Device_Type_Detail_Id", "Department", "Page", "PageSize", "filter", "item", "IsShow", "unshift", "Id", "_this4", "_callee3", "_callee3$", "_context3", "_this5", "_callee4", "_callee4$", "_context4", "NodeName", "map", "Key", "Electric", "Produce", "_this6", "_callee5", "_callee5$", "_context5", "_this7", "_callee6", "_callee6$", "_context6", "_this8", "_callee7", "_callee7$", "_context7", "slice", "_this9", "_callee8", "_callee8$", "_context8", "_this10", "_callee9", "_callee9$", "_context9", "Content", "_this11", "_callee10", "_callee10$", "_context10", "ID", "Time", "Label", "Value", "_this12", "_callee11", "_callee11$", "_context11", "Reconditions", "Maintenances", "_this13", "_callee12", "_callee12$", "_context12", "Rate", "Type", "_this14", "_callee13", "_callee13$", "_context13", "_this15", "_callee14", "_callee14$", "_context14", "latestAlarmInformationDataClassName", "_ref", "row", "rowIndex", "isEvenOrOdd", "HeaderRowClassName", "_ref2", "cellClassName", "_ref3", "rowClassName", "_ref4", "productionVolumeTrendChange", "equipmentFailureTrendChange", "equipmentMaintenanceReadinessRateMonthChange", "handleClick", "tab", "event", "number", "_this16", "_callee15", "_callee15$", "_context15", "equipmentOperationChange"], "sources": ["src/views/business/equipmentManagement/equipmentAnalysis/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 equipmentAnalysis\">\r\n    <el-row :gutter=\"12\">\r\n      <el-col :span=\"24\">\r\n        <equipmentOperationStatus\r\n          v-loading=\"deviceStatusListLoading\"\r\n          :component-data=\"{\r\n            deviceStatusList: deviceStatusList,\r\n          }\"\r\n        />\r\n        <!-- <el-card shadow=\"never\" v-loading=\"deviceStatusListLoading\">\r\n          <equipmentOperationStatus\r\n            :componentData=\"{\r\n              deviceStatusList: deviceStatusList,\r\n            }\"\r\n          ></equipmentOperationStatus>\r\n        </el-card> -->\r\n      </el-col>\r\n\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"24\">\r\n        <el-card v-loading=\"rgvDataListLoading\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>AGV电量实时监测</span>\r\n          </div>\r\n          <agvBatteryLevel\r\n            style=\"margin-top: -20px\"\r\n            :component-data=\"{\r\n              rgvDataList: rgvDataList,\r\n            }\"\r\n          />\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"12\">\r\n        <el-card\r\n          v-loading=\"equipmentAbnormalityRankingDataLoading\"\r\n          shadow=\"never\"\r\n        >\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>设备异常情况排行</span>\r\n          </div>\r\n          <div style=\"margin-top: -20px\">\r\n            <el-table\r\n              :data=\"equipmentAbnormalityRankingData\"\r\n              style=\"width: 100%\"\r\n              height=\"240\"\r\n              :highlight-current-row=\"false\"\r\n              :row-class-name=\"rowClassName\"\r\n              :header-row-class-name=\"HeaderRowClassName\"\r\n              :cell-class-name=\"cellClassName\"\r\n            >\r\n              <el-table-column label=\"排名\" width=\"60\">\r\n                <template slot-scope=\"scope\">\r\n                  <div\r\n                    v-if=\"scope.$index < 3\"\r\n                    class=\"tablenumber\"\r\n                    :style=\"{\r\n                      backgroundImage:\r\n                        'url(' +\r\n                        require(`@/assets/no_${scope.$index + 1}.png`) +\r\n                        ')',\r\n                    }\"\r\n                  >\r\n                    <span> {{ scope.$index + 1 }}</span>\r\n                  </div>\r\n                  <div v-else class=\"tablenumber\">\r\n                    <span> {{ scope.$index + 1 }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Name\" label=\"设备名称\" />\r\n              <el-table-column prop=\"Area\" label=\"所属车间\" />\r\n              <el-table-column\r\n                prop=\"ErrorTime\"\r\n                label=\"设备异常时间\"\r\n                width=\"140\"\r\n              />\r\n              <el-table-column\r\n                prop=\"StartTime\"\r\n                label=\"设备开机时间\"\r\n                width=\"140\"\r\n              />\r\n              <el-table-column prop=\"ErrPercent\" label=\"异常率\" width=\"100\" />\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-card v-loading=\"latestAlarmInformationDataLoading\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>最新告警消息</span>\r\n          </div>\r\n          <div style=\"margin-top: -20px\">\r\n            <el-table\r\n              :data=\"latestAlarmInformationData\"\r\n              style=\"width: 100%\"\r\n              height=\"240\"\r\n              :show-header=\"true\"\r\n              :highlight-current-row=\"false\"\r\n              :row-class-name=\"rowClassName\"\r\n              :header-row-class-name=\"HeaderRowClassName\"\r\n              :cell-class-name=\"cellClassName\"\r\n            >\r\n              <el-table-column prop=\"Time\" label=\"告警时间\" width=\"160\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.Time || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Name\" label=\"告警设备\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.Name || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"StatusDes\" label=\"设备状态\" width=\"90\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.StatusDes || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"Brand\" label=\"设备品牌\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.Brand || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <!-- <el-table-column prop=\"Content\" label=\"异常信息\" width=\"90\">\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ scope.row.Content || \"-\" }}</span>\r\n                </template>\r\n              </el-table-column> -->\r\n              <el-table-column label=\"\" width=\"40\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-popover placement=\"bottom-end\" width=\"\" trigger=\"hover\">\r\n                    <div class=\"popover_latestAlarmInformation\">\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >告警时间</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.Time || \"-\" }}</span>\r\n                      </div>\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >告警设备</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.Name || \"-\" }}</span>\r\n                      </div>\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >设备状态</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.StatusDes || \"-\" }}</span>\r\n                      </div>\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >设备品牌</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.Brand || \"-\" }}</span>\r\n                      </div>\r\n                      <div class=\"item\" style=\"padding: 4px 0px; display: flex\">\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #999999;\r\n                            width: 74px;\r\n                          \"\r\n                        >异常信息</span>\r\n                        <span\r\n                          style=\"\r\n                            font-weight: 500;\r\n                            font-size: 14px;\r\n                            color: #333333;\r\n                          \"\r\n                        >{{ scope.row.Content || \"-\" }}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div\r\n                      slot=\"reference\"\r\n                      style=\"\r\n                        height: 100%;\r\n                        display: flex;\r\n                        justify-content: center;\r\n                      \"\r\n                    >\r\n                      <i class=\"el-icon-arrow-right\" />\r\n                    </div>\r\n                  </el-popover>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n      <el-col :span=\"18\">\r\n        <el-row :gutter=\"12\">\r\n          <el-col :span=\"12\">\r\n            <el-card v-loading=\"productionVolumeTrendLoading\" shadow=\"never\">\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>能效（电）分析</span>\r\n                <span\r\n                  class=\"right\"\r\n                  @click=\"productionVolumeTrendClick\"\r\n                >更多<i class=\"el-icon-arrow-right\" /></span>\r\n              </div>\r\n              <div class=\"chartCardConent\">\r\n                <div class=\"chartCardItem\">\r\n                  <el-select\r\n                    v-model=\"productionVolumeTrendValue\"\r\n                    :clearable=\"true\"\r\n                    placeholder=\"请选择\"\r\n                    @change=\"productionVolumeTrendChange\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in productionVolumeTrendSelectOptions\"\r\n                      :key=\"item.value\"\r\n                      :label=\"item.label\"\r\n                      :value=\"item.value\"\r\n                    />\r\n                  </el-select>\r\n                </div>\r\n                <efficiencyAnalysis\r\n                  :component-data=\"{\r\n                    productionVolumeTrendOptions: productionVolumeTrendOptions,\r\n                  }\"\r\n                />\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card v-loading=\"equipmentFailureTrendLoading\" shadow=\"never\">\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>设备故障</span>\r\n                <span\r\n                  class=\"right\"\r\n                  @click=\"equipmentFailureTrendClick\"\r\n                >更多<i class=\"el-icon-arrow-right\" /></span>\r\n              </div>\r\n              <div class=\"chartCardConent\">\r\n                <div class=\"chartCardItem\">\r\n                  <el-select\r\n                    v-model=\"equipmentFailureTrendValue\"\r\n                    placeholder=\"请选择\"\r\n                    :clearable=\"true\"\r\n                    @change=\"equipmentFailureTrendChange\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in equipmentFailureTrendSelectOptions\"\r\n                      :key=\"item.Id\"\r\n                      :label=\"item.Display_Name\"\r\n                      :value=\"item.Id\"\r\n                    />\r\n                  </el-select>\r\n                </div>\r\n                <equipmentFailure\r\n                  :component-data=\"{\r\n                    equipmentFailureTrendOptions: equipmentFailureTrendOptions,\r\n                  }\"\r\n                />\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"12\">\r\n            <el-card\r\n              v-loading=\"rankProdEquipmentRateMonthLoading\"\r\n              shadow=\"never\"\r\n            >\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>本月生产设备负载率排行\r\n                  <el-tooltip\r\n                    class=\"item\"\r\n                    content=\"负载率=设备运行时间/设备开机时间\"\r\n                    placement=\"top-start\"\r\n                  >\r\n                    <img src=\"@/assets/tooltip.png\" alt=\"\">\r\n                  </el-tooltip>\r\n                </span>\r\n                <span\r\n                  class=\"unit\"\r\n                >单位：{{ rankProdEquipmentRateMonth.Unit }}</span>\r\n              </div>\r\n              <customProcess\r\n                :component-data=\"{ list: rankProdEquipmentRateMonth.Charts }\"\r\n              />\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card\r\n              v-loading=\"\r\n                monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading\r\n              \"\r\n              shadow=\"never\"\r\n            >\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>本月焊丝使用效率\r\n                  <el-tooltip\r\n                    class=\"item\"\r\n                    content=\"负载率=设备运行时间/设备开机时间\"\r\n                    placement=\"top-start\"\r\n                  >\r\n                    <template #content>\r\n                      <p>1.使用效率=使用量/运行时间（小时）</p>\r\n                      <p>2.使用量单位标识： -焊剂KG -焊丝KG -导电嘴（个）</p>\r\n                    </template>\r\n                    <img src=\"@/assets/tooltip.png\" alt=\"\">\r\n                  </el-tooltip>\r\n                </span>\r\n                <span\r\n                  class=\"unit\"\r\n                >单位：{{\r\n                  monthRankWeldingEquipmentConsumablesUsageEfficiency.Unit\r\n                }}</span>\r\n              </div>\r\n              <customProcess\r\n                :component-data=\"{\r\n                  list: monthRankWeldingEquipmentConsumablesUsageEfficiency.Charts,\r\n                }\"\r\n              />\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"12\" style=\"margin-top: 10px\">\r\n          <el-col :span=\"12\">\r\n            <el-row :gutter=\"12\">\r\n              <el-col :span=\"8\">\r\n                <el-card\r\n                  v-loading=\"maintenanceWorkOrderProcessingStatusLoading\"\r\n                  shadow=\"never\"\r\n                >\r\n                  <div slot=\"header\" class=\"header\">\r\n                    <span>维修工单</span>\r\n                  </div>\r\n                  <maintenanceWorkOrder\r\n                    :component-data=\"{\r\n                      data: maintenanceWorkOrderProcessingStatusOne,\r\n                    }\"\r\n                  />\r\n                </el-card>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-card\r\n                  v-loading=\"maintenanceWorkOrderProcessingStatusLoading\"\r\n                  shadow=\"never\"\r\n                >\r\n                  <div slot=\"header\" class=\"header\">\r\n                    <span>维保工单</span>\r\n                  </div>\r\n                  <maintenanceWorkOrder\r\n                    :component-data=\"{\r\n                      data: maintenanceWorkOrderProcessingStatusTwo,\r\n                    }\"\r\n                  />\r\n                </el-card>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-card v-loading=\"workOrderErrorTypeLoading\" shadow=\"never\">\r\n                  <div slot=\"header\" class=\"header\">\r\n                    <span>维修故障类型</span>\r\n                  </div>\r\n                  <repairFaultType\r\n                    :component-data=\"{ workOrderErrorTypeList }\"\r\n                  />\r\n                </el-card>\r\n              </el-col>\r\n            </el-row>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card\r\n              v-loading=\"equipmentMaintenanceIntegrityRateLoading\"\r\n              shadow=\"never\"\r\n            >\r\n              <div slot=\"header\" class=\"header\">\r\n                <span>设备维修完好率</span>\r\n                <el-date-picker\r\n                  v-model=\"equipmentMaintenanceReadinessRateMonth\"\r\n                  size=\"small\"\r\n                  type=\"month\"\r\n                  placeholder=\"选择月\"\r\n                  :clearable=\"false\"\r\n                  :editable=\"false\"\r\n                  @change=\"equipmentMaintenanceReadinessRateMonthChange\"\r\n                />\r\n              </div>\r\n              <equipmentIntegrityRate\r\n                :component-data=\"{ data: equipmentMaintenanceIntegrityRateObj }\"\r\n              />\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-card v-loading=\"equipmentOperationListLoading\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"header\">\r\n            <span>本月设备运行</span>\r\n            <el-select\r\n              v-model=\"equipmentOperationValue\"\r\n              :clearable=\"true\"\r\n              placeholder=\"请选择\"\r\n              @change=\"equipmentOperationChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in equipmentStartupStatusMonthOptions\"\r\n                :key=\"item.Id\"\r\n                :label=\"item.Display_Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n          <equipmentOperation\r\n            :component-data=\"{ data: equipmentOperationList }\"\r\n          />\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"currentComponentTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      custom-class=\"dialogCustomClass\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :component-data=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  GetStatusAnalyseEqt,\r\n  GetErrorRank,\r\n  GetTopEqtError,\r\n  GetStartAnalyseEqt,\r\n  GetLoadAnalyseEqt,\r\n  GetConsumptionAnalyseEqt,\r\n  GetDeviceAnalyseWorkOrderCount,\r\n  GetWorkOrderHandlingCount,\r\n  GetWorkOrderErrorTypeList,\r\n  GetDeviceServiceabilityRate,\r\n  GetAGVAnalyseEqt,\r\n  GetDictionaryDetailListByParentId,\r\n  GetProduceTrend,\r\n  GetDeviceStatusDetails,\r\n  GetStartAnalyseEqtDetails\r\n} from '@/api/business/eqptAsset'\r\n\r\nimport equipmentIntegrityRate from './components/equipmentIntegrityRate'\r\nimport equipmentOperation from './components/equipmentOperation'\r\nimport repairFaultType from './components/repairFaultType'\r\nimport customProcess from './components/customProcess'\r\nimport loadRateRanking from './components/loadRateRanking'\r\nimport maintenanceWorkOrder from './components/maintenanceWorkOrder'\r\nimport equipmentOperationStatus from './components/equipmentOperationStatus'\r\nimport agvBatteryLevel from './components/agvBatteryLevel'\r\nimport efficiencyAnalysis from './components/efficiencyAnalysis'\r\nimport equipmentFailure from './components/equipmentFailure'\r\n\r\nimport efficiencyAnalysisDetail from './components/efficiencyAnalysisDetail'\r\nimport equipmentFailureDetail from './components/equipmentFailureDetail'\r\nimport { GetEquipmentAssetPageListPJ } from '@/api/business/eqptAsset'\r\nimport dayjs from 'dayjs'\r\nimport VChart from 'vue-echarts'\r\nimport { use } from 'echarts/core'\r\nimport { CanvasRenderer } from 'echarts/renderers'\r\nimport { BarChart, LineChart, PieChart } from 'echarts/charts'\r\nimport {\r\n  GridComponent,\r\n  LegendComponent,\r\n  TooltipComponent,\r\n  TitleComponent,\r\n  DataZoomComponent\r\n} from 'echarts/components'\r\nuse([\r\n  CanvasRenderer,\r\n  BarChart,\r\n  LineChart,\r\n  PieChart,\r\n  DataZoomComponent,\r\n  GridComponent,\r\n  LegendComponent,\r\n  TitleComponent,\r\n  TooltipComponent\r\n])\r\nexport default {\r\n  name: 'EquipmentAnalysis',\r\n  components: {\r\n    VChart,\r\n    equipmentOperation,\r\n    equipmentIntegrityRate,\r\n    repairFaultType,\r\n    customProcess,\r\n    loadRateRanking,\r\n    maintenanceWorkOrder,\r\n    equipmentOperationStatus,\r\n    agvBatteryLevel,\r\n    efficiencyAnalysis,\r\n    equipmentFailure\r\n  },\r\n  mixins: [],\r\n  data() {\r\n    return {\r\n      currentComponent: null,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.fetchData()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      currentComponentTitle: '',\r\n      //\r\n      deviceStatusListLoading: false,\r\n      deviceStatusList: [],\r\n      // 设备运行状况\r\n      equipmentOperationStatus: [],\r\n      // RGV电量实时监测\r\n      rgvDataListLoading: false,\r\n      rgvDataList: [],\r\n      // 本月设备维修完好率\r\n      equipmentMaintenanceIntegrityRateLoading: false,\r\n      equipmentMaintenanceReadinessRateMonth: dayjs(new Date()).format(\r\n        'YYYY-MM'\r\n      ),\r\n      // 维修工单处理情况\r\n      maintenanceWorkOrderProcessingStatusLoading: false,\r\n      maintenanceWorkOrderProcessingStatusOne: [],\r\n      // 维保工单处理情况\r\n      maintenanceWorkOrderProcessingStatusTwo: [],\r\n      // 本月焊接设备耗材使用效率排行\r\n      monthRankWeldingEquipmentConsumablesUsageEfficiencyValue:\r\n        'WireConsumption',\r\n      // 本月焊丝使用效率\r\n      rankProdEquipmentRateMonthLoading: false,\r\n      rankProdEquipmentRateMonth: {},\r\n      // 本月焊接设备耗材使用效率排行\r\n      monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading: false,\r\n      monthRankWeldingEquipmentConsumablesUsageEfficiency: {},\r\n      // 设备异常情况排行\r\n      equipmentAbnormalityRankingDataLoading: false,\r\n      equipmentAbnormalityRankingData: [],\r\n      //\r\n      latestAlarmInformationDataLoading: false,\r\n      latestAlarmInformationData: [],\r\n      // 本月设备开机情况\r\n      equipmentStartupStatusMonthValue: '',\r\n      equipmentOperationValue: '',\r\n      equipmentStartupStatusMonthOptions: [],\r\n\r\n      // 能效（电）分析\r\n      productionVolumeTrendValue: '',\r\n      productionVolumeTrendLoading: false,\r\n      productionVolumeTrendSelectOptions: [\r\n        {\r\n          label: '全部',\r\n          value: ''\r\n        },\r\n        {\r\n          label: '一车间',\r\n          value: '一车间'\r\n        },\r\n        {\r\n          label: '二车间',\r\n          value: '二车间'\r\n        },\r\n        {\r\n          label: '配送中心',\r\n          value: '配送中心'\r\n        }\r\n      ],\r\n      productionVolumeTrendOptions: {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        legend: {\r\n          show: false\r\n          // top: \"0\",\r\n          // left: \"0\",\r\n          // itemWidth: 10,\r\n          // itemHeight: 5,\r\n          // icon: \"rect\",\r\n          // textStyle: {\r\n          //   fontSize: 12,\r\n          //   color: \"#999999\",\r\n          // },\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        yAxis: [\r\n          {\r\n            name: '用电量(kW·h)',\r\n            type: 'value',\r\n            position: 'left',\r\n            axisLabel: {\r\n              formatter: '{value}',\r\n              textStyle: {\r\n                color: '#298DFF' // 设置 y 轴标签文字颜色为红色\r\n              }\r\n            },\r\n            nameTextStyle: {\r\n              color: '#298DFF',\r\n              fontSize: '13px'\r\n            }\r\n            // logBase: 10,\r\n          },\r\n          {\r\n            name: '生产产量(t)',\r\n            type: 'value',\r\n            // min: 0,\r\n            // max: 25,\r\n            // interval: 5,\r\n            axisLabel: {\r\n              formatter: '{value}',\r\n              textStyle: {\r\n                color: '#FF902C' // 设置 y 轴标签文字颜色为红色\r\n              }\r\n            },\r\n            nameTextStyle: {\r\n              color: '#FF902C',\r\n              fontSize: '13px'\r\n            }\r\n          }\r\n        ],\r\n        color: ['#298DFF', '#FF902C'],\r\n        series: [\r\n          {\r\n            name: '用电量',\r\n            // symbol: \"none\",\r\n            data: [],\r\n            // icon: 'rect',\r\n            // yAxisIndex: 1,\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return `${value || 0}` + ' kW·h'\r\n              }\r\n            },\r\n            type: 'line'\r\n          },\r\n          {\r\n            name: '生产产量',\r\n            // symbol: \"none\",\r\n            data: [],\r\n            yAxisIndex: 1,\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return `${value || 0}` + ' t'\r\n              }\r\n            },\r\n            // icon: 'rect',\r\n            type: 'line'\r\n          }\r\n        ]\r\n      },\r\n      // 设备故障\r\n      equipmentFailureTrendValue: '',\r\n      equipmentFailureTrendSelectOptions: [],\r\n      equipmentFailureTrendLoading: false,\r\n      equipmentFailureTrendOptions: {\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n\r\n        color: ['rgba(41, 141, 255, 1)'],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            position: 'left',\r\n            logBase: 10\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            smooth: true,\r\n            // symbol: \"none\",\r\n            data: [],\r\n            type: 'line',\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return `${value || 0}` + ' 个'\r\n              }\r\n            }\r\n            // areaStyle: {\r\n            //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n            //     {\r\n            //       offset: 0,\r\n            //       color: \"rgba(62, 204, 147, .5)\",\r\n            //     },\r\n            //     {\r\n            //       offset: 1,\r\n            //       color: \"rgba(57, 133, 238, 0)\",\r\n            //     },\r\n            //   ]),\r\n            // },\r\n          }\r\n        ]\r\n      },\r\n      // 本月设备维修完好率\r\n      equipmentMaintenanceIntegrityRateObj: {},\r\n      workOrderErrorTypeLoading: false,\r\n      workOrderErrorTypeList: [],\r\n      equipmentOperationListLoading: false,\r\n      equipmentOperationList: []\r\n    }\r\n  },\r\n  activated() {},\r\n  mounted() {\r\n    // 获取设备\r\n    this.getEquipmentAssetPageListPJ()\r\n    // 获取设备子类\r\n    this.getDictionaryDetailListByParentId()\r\n    // 获取设备数采分析设备运行状况\r\n    this.getStatusAnalyseEqt()\r\n    // 获取设备异常情况排行\r\n    this.getErrorRank()\r\n    // 获取设备数采异常信息\r\n    this.getTopEqtError()\r\n    // // 获取设备数采分析开机时间分析\r\n    // this.getStartAnalyseEqt();\r\n    // 获取设备数采分析负载率分析\r\n    this.getLoadAnalyseEqt()\r\n    // 获取本月耗材设备使用效率排行\r\n    this.getConsumptionAnalyseEqt()\r\n    // 获取设备故障\r\n    this.getDeviceAnalyseWorkOrderCount()\r\n    // 获取工单处理情况\r\n    this.getWorkOrderHandlingCount()\r\n    // 获取工单类型故障次数\r\n    this.getWorkOrderErrorTypeList()\r\n    // 获取设备完好率\r\n    this.getDeviceServiceabilityRate()\r\n    // 获取AGV运行状态\r\n    this.getAGVAnalyseEqt()\r\n    // 获取能效（电）分析\r\n    this.getProduceTrend()\r\n    // 获取设备状态\r\n    this.getDeviceStatusDetails()\r\n    // 获取设备运行\r\n    this.getStartAnalyseEqtDetails()\r\n  },\r\n  methods: {\r\n    // 打开 能效（电）分析\r\n    productionVolumeTrendClick() {\r\n      this.currentComponentTitle = '能效（电）分析'\r\n      this.dialogVisible = true\r\n      this.currentComponent = efficiencyAnalysisDetail\r\n      this.componentsConfig = {\r\n        selectOtherOptions: this.productionVolumeTrendSelectOptions\r\n      }\r\n    },\r\n    // 打开 设备故障\r\n    equipmentFailureTrendClick() {\r\n      this.currentComponentTitle = '设备故障'\r\n      this.dialogVisible = true\r\n      this.currentComponent = equipmentFailureDetail\r\n      this.componentsConfig = {\r\n        selectOtherOptions: this.equipmentFailureTrendSelectOptions\r\n      }\r\n    },\r\n    // 获取设备状态\r\n    async getDeviceStatusDetails() {\r\n      this.deviceStatusListLoading = false\r\n      const res = await GetDeviceStatusDetails({})\r\n      if (res.IsSucceed) {\r\n        this.deviceStatusList = res.Data\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n      this.deviceStatusListLoading = false\r\n    },\r\n    // 获取设备\r\n    async getEquipmentAssetPageListPJ() {\r\n      const res = await GetEquipmentAssetPageListPJ({\r\n        Display_Name: '',\r\n        Device_Type_Id: 'cc81fe8a-f0a3-40c1-abf6-f553fa502a33',\r\n        Device_Type_Detail_Id: '',\r\n        Department: '',\r\n        Page: 1,\r\n        PageSize: 100\r\n      })\r\n      this.equipmentFailureTrendSelectOptions = res.Data.Data.filter(\r\n        (item) => item.IsShow == 1\r\n      )\r\n      this.equipmentFailureTrendSelectOptions.unshift({\r\n        Display_Name: '全部',\r\n        Id: ''\r\n      })\r\n    },\r\n    // 获取设备子类\r\n    async getDictionaryDetailListByParentId() {\r\n      const res = await GetDictionaryDetailListByParentId(\r\n        'cc81fe8a-f0a3-40c1-abf6-f553fa502a33'\r\n      )\r\n      this.equipmentStartupStatusMonthOptions = res.Data\r\n      this.equipmentStartupStatusMonthOptions.unshift({\r\n        Display_Name: '全部',\r\n        Id: ''\r\n      })\r\n    },\r\n    // 能效（电）分析\r\n    async getProduceTrend() {\r\n      this.productionVolumeTrendLoading = false\r\n      const res = await GetProduceTrend({\r\n        NodeName: this.productionVolumeTrendValue\r\n      })\r\n\r\n      this.productionVolumeTrendOptions.xAxis.data = res.Data.map(\r\n        (item) => item.Key\r\n      )\r\n      this.productionVolumeTrendOptions.series[0].data = res.Data.map(\r\n        (item) => item.Electric\r\n      )\r\n      this.productionVolumeTrendOptions.series[1].data = res.Data.map(\r\n        (item) => item.Produce\r\n      )\r\n      this.productionVolumeTrendLoading = false\r\n    },\r\n    // 获取设备数采分析设备运行状况\r\n    async getStatusAnalyseEqt() {\r\n      const res = await GetStatusAnalyseEqt({})\r\n      this.equipmentOperationStatus = res.Data\r\n    },\r\n    // 获取设备异常情况排行\r\n    async getErrorRank() {\r\n      this.equipmentAbnormalityRankingDataLoading = true\r\n      const res = await GetErrorRank({})\r\n\r\n      this.equipmentAbnormalityRankingData = res.Data\r\n      this.equipmentAbnormalityRankingDataLoading = false\r\n    },\r\n    // 获取设备数采异常信息\r\n    async getTopEqtError() {\r\n      this.latestAlarmInformationDataLoading = true\r\n\r\n      const res = await GetTopEqtError({})\r\n      this.latestAlarmInformationData = res.Data.slice(0, 5)\r\n      this.latestAlarmInformationDataLoading = false\r\n    },\r\n    // 获取设备数采分析开机时间分析\r\n    // async getStartAnalyseEqt() {\r\n    //   let res = await GetStartAnalyseEqt({\r\n    //     ID: this.equipmentStartupStatusMonthValue,\r\n    //   });\r\n    // },\r\n    // 获取设备数采分析负载率分析\r\n    async getLoadAnalyseEqt() {\r\n      this.rankProdEquipmentRateMonthLoading = true\r\n      const res = await GetLoadAnalyseEqt({})\r\n      this.rankProdEquipmentRateMonth = res.Data\r\n      this.rankProdEquipmentRateMonthLoading = false\r\n    },\r\n    // 获取本月耗材设备使用效率排行\r\n    async getConsumptionAnalyseEqt() {\r\n      this.monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading = true\r\n      const res = await GetConsumptionAnalyseEqt({\r\n        Content: this.monthRankWeldingEquipmentConsumablesUsageEfficiencyValue\r\n      })\r\n      this.monthRankWeldingEquipmentConsumablesUsageEfficiency = res.Data\r\n      this.monthRankWeldingEquipmentConsumablesUsageEfficiencyLoading = false\r\n    },\r\n    // 获取设备故障\r\n    async getDeviceAnalyseWorkOrderCount() {\r\n      this.equipmentFailureTrendLoading = true\r\n      const res = await GetDeviceAnalyseWorkOrderCount({\r\n        ID: this.equipmentFailureTrendValue,\r\n        Time: ''\r\n      })\r\n      this.equipmentFailureTrendOptions.xAxis.data = res.Data.map(\r\n        (item) => item.Label\r\n      )\r\n      this.equipmentFailureTrendOptions.series[0].data = res.Data.map(\r\n        (item) => item.Value\r\n      )\r\n      this.equipmentFailureTrendLoading = false\r\n    },\r\n\r\n    // 获取工单处理情况\r\n    async getWorkOrderHandlingCount() {\r\n      this.maintenanceWorkOrderProcessingStatusLoading = true\r\n      const res = await GetWorkOrderHandlingCount({})\r\n      this.maintenanceWorkOrderProcessingStatusOne = res.Data.Reconditions\r\n      this.maintenanceWorkOrderProcessingStatusTwo = res.Data.Maintenances\r\n      this.maintenanceWorkOrderProcessingStatusLoading = false\r\n    },\r\n\r\n    // 获取工单类型故障次数\r\n    async getWorkOrderErrorTypeList() {\r\n      this.workOrderErrorTypeLoading = true\r\n      const res = await GetWorkOrderErrorTypeList({})\r\n      this.workOrderErrorTypeList = res.Data.map((item) => ({\r\n        Value: item.Rate,\r\n        Label: item.Type\r\n      }))\r\n      this.workOrderErrorTypeLoading = false\r\n    },\r\n    // 获取设备完好率\r\n    async getDeviceServiceabilityRate() {\r\n      this.equipmentMaintenanceIntegrityRateLoading = true\r\n      const res = await GetDeviceServiceabilityRate({\r\n        Time: dayjs(this.equipmentMaintenanceReadinessRateMonth).format(\r\n          'YYYY-MM'\r\n        )\r\n      })\r\n      this.equipmentMaintenanceIntegrityRateObj = res.Data\r\n      this.equipmentMaintenanceIntegrityRateLoading = false\r\n    },\r\n\r\n    // 获取AGV运行状态\r\n    async getAGVAnalyseEqt() {\r\n      this.rgvDataListLoading = true\r\n      const res = await GetAGVAnalyseEqt({})\r\n      this.rgvDataList = res.Data\r\n      this.rgvDataListLoading = false\r\n    },\r\n    // 设置表格颜色\r\n    latestAlarmInformationDataClassName({ row, rowIndex }) {\r\n      if (this.isEvenOrOdd(rowIndex)) {\r\n        return 'row-one'\r\n      } else {\r\n        return 'row-two'\r\n      }\r\n    },\r\n\r\n    HeaderRowClassName({ row, rowIndex }) {\r\n      return 'row-header'\r\n    },\r\n    cellClassName({ row, rowIndex }) {\r\n      return 'row-body'\r\n    },\r\n\r\n    // 设置表格颜色\r\n    rowClassName({ row, rowIndex }) {\r\n      if (this.isEvenOrOdd(rowIndex + 1)) {\r\n        return 'row-one'\r\n      } else {\r\n        return 'row-two'\r\n      }\r\n    },\r\n\r\n    productionVolumeTrendChange() {\r\n      this.getProduceTrend()\r\n    },\r\n    equipmentFailureTrendChange() {\r\n      this.getDeviceAnalyseWorkOrderCount()\r\n    },\r\n    equipmentMaintenanceReadinessRateMonthChange() {\r\n      this.getDeviceServiceabilityRate()\r\n    },\r\n    handleClick(tab, event) {},\r\n    //  判断是否是偶数行 还是奇数行\r\n    isEvenOrOdd(number) {\r\n      if (number % 2 === 0) {\r\n        return true\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    // 设备运行\r\n    async getStartAnalyseEqtDetails() {\r\n      this.equipmentOperationListLoading = true\r\n      const res = await GetStartAnalyseEqtDetails({\r\n        ID: this.equipmentOperationValue\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.equipmentOperationList = res.Data\r\n      }\r\n      this.equipmentOperationListLoading = false\r\n    },\r\n    equipmentOperationChange() {\r\n      this.getStartAnalyseEqtDetails()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.equipmentAnalysis {\r\n  // padding: 10px 15px;\r\n  overflow-y: scroll;\r\n  // height: calc(100vh - 96px);\r\n  .dialogCustomClass {\r\n    .formBox {\r\n      height: 580px;\r\n      padding: 0 16px;\r\n      &::-webkit-scrollbar {\r\n        display: none;\r\n      }\r\n    }\r\n    .costomTitle {\r\n      display: flex;\r\n      align-items: center;\r\n      color: #333;\r\n      margin-bottom: 16px;\r\n      span {\r\n        display: inline-block;\r\n        width: 2px;\r\n        height: 14px;\r\n        background: #009dff;\r\n        margin-right: 6px;\r\n      }\r\n    }\r\n    .dialogButton {\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      border-top: 1px solid #d0d3db;\r\n      padding-top: 16px;\r\n    }\r\n  }\r\n  .header {\r\n    display: flex;\r\n    // align-items: center;\r\n    justify-content: space-between;\r\n    height: 22px;\r\n    > span {\r\n      font-weight: bold;\r\n    }\r\n    .right {\r\n      font-family: Helvetica, Helvetica;\r\n      font-weight: bold;\r\n      font-size: 14px;\r\n      color: #298cfc;\r\n      line-height: 0px;\r\n      text-align: left;\r\n      font-style: normal;\r\n      text-transform: none;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .unit {\r\n      font-family: Microsoft YaHei, Microsoft YaHei;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #999999;\r\n      font-style: normal;\r\n      text-transform: none;\r\n    }\r\n  }\r\n  .chartCardConent {\r\n    margin-top: -30px;\r\n    .chartCardItem {\r\n      display: flex;\r\n      justify-content: flex-end;\r\n    }\r\n  }\r\n  .content {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: row;\r\n\r\n    .left {\r\n      width: 50%;\r\n      .title {\r\n        font-weight: bold;\r\n      }\r\n    }\r\n    .right {\r\n      width: 50%;\r\n      .title {\r\n        font-weight: bold;\r\n      }\r\n    }\r\n  }\r\n\r\n  .tablenumber {\r\n    width: 30px;\r\n    height: 23px;\r\n    background-size: 100%;\r\n    background-repeat: no-repeat;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    > span {\r\n      margin-top: 10px;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  ::v-deep .popover_latestAlarmInformation {\r\n    .item {\r\n      padding: 4px 0px;\r\n      display: flex;\r\n      span:first-of-type {\r\n        font-weight: 500;\r\n        font-size: 14px;\r\n        color: #999999;\r\n        width: 74px;\r\n      }\r\n      span:last-of-type {\r\n        font-weight: 500;\r\n        font-size: 14px;\r\n        color: #333333;\r\n      }\r\n    }\r\n  }\r\n\r\n  ::v-deep .el-card__header {\r\n    border-bottom: none !important;\r\n  }\r\n  ::v-deep .el-progress__text {\r\n    font-size: 18px !important;\r\n    color: #666666 !important;\r\n    // font-weight: bold;\r\n  }\r\n  ::v-deep.el-table .row-one {\r\n    background: rgba(41, 141, 255, 0.03) !important;\r\n  }\r\n\r\n  ::v-deep .el-table .row-two {\r\n    background: rgba(255, 255, 255, 1) !important;\r\n  }\r\n\r\n  ::v-deep .el-table .row-header {\r\n    color: #333333 !important;\r\n    font-weight: 500;\r\n  }\r\n  ::v-deep .el-table .row-body {\r\n    color: #333333 !important;\r\n    border: none !important;\r\n  }\r\n  ::v-deep .el-card {\r\n    border: none !important;\r\n  }\r\n  ::v-deep .el-table::before {\r\n    background: none !important;\r\n  }\r\n  ::v-deep .el-table th.is-leaf {\r\n    border-bottom: none !important;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgeA,SACAA,mBAAA,EACAC,YAAA,EACAC,cAAA,EACAC,kBAAA,EACAC,iBAAA,EACAC,wBAAA,EACAC,8BAAA,EACAC,yBAAA,EACAC,yBAAA,EACAC,2BAAA,EACAC,gBAAA,EACAC,iCAAA,EACAC,eAAA,EACAC,sBAAA,EACAC,yBAAA,QACA;AAEA,OAAAC,sBAAA;AACA,OAAAC,kBAAA;AACA,OAAAC,eAAA;AACA,OAAAC,aAAA;AACA,OAAAC,eAAA;AACA,OAAAC,oBAAA;AACA,OAAAC,wBAAA;AACA,OAAAC,eAAA;AACA,OAAAC,kBAAA;AACA,OAAAC,gBAAA;AAEA,OAAAC,wBAAA;AACA,OAAAC,sBAAA;AACA,SAAAC,2BAAA;AACA,OAAAC,KAAA;AACA,OAAAC,MAAA;AACA,SAAAC,GAAA;AACA,SAAAC,cAAA;AACA,SAAAC,QAAA,EAAAC,SAAA,EAAAC,QAAA;AACA,SACAC,aAAA,EACAC,eAAA,EACAC,gBAAA,EACAC,cAAA,EACAC,iBAAA,QACA;AACAT,GAAA,EACAC,cAAA,EACAC,QAAA,EACAC,SAAA,EACAC,QAAA,EACAK,iBAAA,EACAJ,aAAA,EACAC,eAAA,EACAE,cAAA,EACAD,gBAAA,CACA;AACA;EACAG,IAAA;EACAC,UAAA;IACAZ,MAAA,EAAAA,MAAA;IACAb,kBAAA,EAAAA,kBAAA;IACAD,sBAAA,EAAAA,sBAAA;IACAE,eAAA,EAAAA,eAAA;IACAC,aAAA,EAAAA,aAAA;IACAC,eAAA,EAAAA,eAAA;IACAC,oBAAA,EAAAA,oBAAA;IACAC,wBAAA,EAAAA,wBAAA;IACAC,eAAA,EAAAA,eAAA;IACAC,kBAAA,EAAAA,kBAAA;IACAC,gBAAA,EAAAA;EACA;EACAkB,MAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,gBAAA;MACAC,gBAAA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAJ,KAAA,CAAAK,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAN,KAAA,CAAAK,aAAA;UACAL,KAAA,CAAAO,SAAA;QACA;MACA;MACAF,aAAA;MACAG,qBAAA;MACA;MACAC,uBAAA;MACAC,gBAAA;MACA;MACAjC,wBAAA;MACA;MACAkC,kBAAA;MACAC,WAAA;MACA;MACAC,wCAAA;MACAC,sCAAA,EAAA9B,KAAA,KAAA+B,IAAA,IAAAC,MAAA,CACA,SACA;MACA;MACAC,2CAAA;MACAC,uCAAA;MACA;MACAC,uCAAA;MACA;MACAC,wDAAA,EACA;MACA;MACAC,iCAAA;MACAC,0BAAA;MACA;MACAC,0DAAA;MACAC,mDAAA;MACA;MACAC,sCAAA;MACAC,+BAAA;MACA;MACAC,iCAAA;MACAC,0BAAA;MACA;MACAC,gCAAA;MACAC,uBAAA;MACAC,kCAAA;MAEA;MACAC,0BAAA;MACAC,4BAAA;MACAC,kCAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAC,4BAAA;QACAC,OAAA;UACAC,OAAA;QACA;QACAC,MAAA;UACAC,IAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACAC,KAAA;UACAC,IAAA;UACA5C,IAAA;UACA6C,QAAA;YACAH,IAAA;UACA;UACAI,QAAA;YACAJ,IAAA;UACA;QACA;QACAK,KAAA,GACA;UACAlD,IAAA;UACA+C,IAAA;UACAI,QAAA;UACAC,SAAA;YACAC,SAAA;YACAC,SAAA;cACAC,KAAA;YACA;UACA;UACAC,aAAA;YACAD,KAAA;YACAE,QAAA;UACA;UACA;QACA,GACA;UACAzD,IAAA;UACA+C,IAAA;UACA;UACA;UACA;UACAK,SAAA;YACAC,SAAA;YACAC,SAAA;cACAC,KAAA;YACA;UACA;UACAC,aAAA;YACAD,KAAA;YACAE,QAAA;UACA;QACA,EACA;QACAF,KAAA;QACAG,MAAA,GACA;UACA1D,IAAA;UACA;UACAG,IAAA;UACA;UACA;UACAuC,OAAA;YACAiB,cAAA,WAAAA,eAAAnB,KAAA;cACA,UAAAoB,MAAA,CAAApB,KAAA;YACA;UACA;UACAO,IAAA;QACA,GACA;UACA/C,IAAA;UACA;UACAG,IAAA;UACA0D,UAAA;UACAnB,OAAA;YACAiB,cAAA,WAAAA,eAAAnB,KAAA;cACA,UAAAoB,MAAA,CAAApB,KAAA;YACA;UACA;UACA;UACAO,IAAA;QACA;MAEA;MACA;MACAe,0BAAA;MACAC,kCAAA;MACAC,4BAAA;MACAC,4BAAA;QACAvB,OAAA;UACAC,OAAA;QACA;QACAG,KAAA;UACAC,IAAA;UACA5C,IAAA;UACA6C,QAAA;YACAH,IAAA;UACA;UACAI,QAAA;YACAJ,IAAA;UACA;QACA;QAEAU,KAAA;QACAL,KAAA,GACA;UACAH,IAAA;UACAI,QAAA;UACAe,OAAA;QACA,EACA;QACAR,MAAA,GACA;UACAS,MAAA;UACA;UACAhE,IAAA;UACA4C,IAAA;UACAL,OAAA;YACAiB,cAAA,WAAAA,eAAAnB,KAAA;cACA,UAAAoB,MAAA,CAAApB,KAAA;YACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MAEA;MACA;MACA4B,oCAAA;MACAC,yBAAA;MACAC,sBAAA;MACAC,6BAAA;MACAC,sBAAA;IACA;EACA;EACAC,SAAA,WAAAA,UAAA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,2BAAA;IACA;IACA,KAAAC,iCAAA;IACA;IACA,KAAAC,mBAAA;IACA;IACA,KAAAC,YAAA;IACA;IACA,KAAAC,cAAA;IACA;IACA;IACA;IACA,KAAAC,iBAAA;IACA;IACA,KAAAC,wBAAA;IACA;IACA,KAAAC,8BAAA;IACA;IACA,KAAAC,yBAAA;IACA;IACA,KAAAC,yBAAA;IACA;IACA,KAAAC,2BAAA;IACA;IACA,KAAAC,gBAAA;IACA;IACA,KAAAC,eAAA;IACA;IACA,KAAAC,sBAAA;IACA;IACA,KAAAC,yBAAA;EACA;EACAC,OAAA;IACA;IACAC,0BAAA,WAAAA,2BAAA;MACA,KAAA/E,qBAAA;MACA,KAAAH,aAAA;MACA,KAAAJ,gBAAA,GAAApB,wBAAA;MACA,KAAAqB,gBAAA;QACAsF,kBAAA,OAAAtD;MACA;IACA;IACA;IACAuD,0BAAA,WAAAA,2BAAA;MACA,KAAAjF,qBAAA;MACA,KAAAH,aAAA;MACA,KAAAJ,gBAAA,GAAAnB,sBAAA;MACA,KAAAoB,gBAAA;QACAsF,kBAAA,OAAA7B;MACA;IACA;IACA;IACAyB,sBAAA,WAAAA,uBAAA;MAAA,IAAAM,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,MAAA,CAAAjF,uBAAA;cAAAyF,QAAA,CAAAE,IAAA;cAAA,OACAnI,sBAAA;YAAA;cAAA8H,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,GAAA,CAAAO,SAAA;gBACAZ,MAAA,CAAAhF,gBAAA,GAAAqF,GAAA,CAAAQ,IAAA;cACA;gBACAb,MAAA,CAAAc,QAAA;kBACAC,OAAA,EAAAV,GAAA,CAAAW,OAAA;kBACA/D,IAAA;gBACA;cACA;cACA+C,MAAA,CAAAjF,uBAAA;YAAA;YAAA;cAAA,OAAAyF,QAAA,CAAAS,IAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IACA;IACA;IACAvB,2BAAA,WAAAA,4BAAA;MAAA,IAAAqC,MAAA;MAAA,OAAAjB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAgB,SAAA;QAAA,IAAAd,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAc,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAZ,IAAA,GAAAY,SAAA,CAAAX,IAAA;YAAA;cAAAW,SAAA,CAAAX,IAAA;cAAA,OACArH,2BAAA;gBACAiI,YAAA;gBACAC,cAAA;gBACAC,qBAAA;gBACAC,UAAA;gBACAC,IAAA;gBACAC,QAAA;cACA;YAAA;cAPAtB,GAAA,GAAAgB,SAAA,CAAAV,IAAA;cAQAO,MAAA,CAAAjD,kCAAA,GAAAoC,GAAA,CAAAQ,IAAA,CAAAA,IAAA,CAAAe,MAAA,CACA,UAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAC,MAAA;cAAA,CACA;cACAZ,MAAA,CAAAjD,kCAAA,CAAA8D,OAAA;gBACAT,YAAA;gBACAU,EAAA;cACA;YAAA;YAAA;cAAA,OAAAX,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IACA;IACArC,iCAAA,WAAAA,kCAAA;MAAA,IAAAmD,MAAA;MAAA,OAAAhC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA+B,SAAA;QAAA,IAAA7B,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA6B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3B,IAAA,GAAA2B,SAAA,CAAA1B,IAAA;YAAA;cAAA0B,SAAA,CAAA1B,IAAA;cAAA,OACArI,iCAAA,CACA,sCACA;YAAA;cAFAgI,GAAA,GAAA+B,SAAA,CAAAzB,IAAA;cAGAsB,MAAA,CAAA5F,kCAAA,GAAAgE,GAAA,CAAAQ,IAAA;cACAoB,MAAA,CAAA5F,kCAAA,CAAA0F,OAAA;gBACAT,YAAA;gBACAU,EAAA;cACA;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAAnB,IAAA;UAAA;QAAA,GAAAiB,QAAA;MAAA;IACA;IACA;IACAzC,eAAA,WAAAA,gBAAA;MAAA,IAAA4C,MAAA;MAAA,OAAApC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAmC,SAAA;QAAA,IAAAjC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAiC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/B,IAAA,GAAA+B,SAAA,CAAA9B,IAAA;YAAA;cACA2B,MAAA,CAAA9F,4BAAA;cAAAiG,SAAA,CAAA9B,IAAA;cAAA,OACApI,eAAA;gBACAmK,QAAA,EAAAJ,MAAA,CAAA/F;cACA;YAAA;cAFA+D,GAAA,GAAAmC,SAAA,CAAA7B,IAAA;cAIA0B,MAAA,CAAA1F,4BAAA,CAAAK,KAAA,CAAA3C,IAAA,GAAAgG,GAAA,CAAAQ,IAAA,CAAA6B,GAAA,CACA,UAAAb,IAAA;gBAAA,OAAAA,IAAA,CAAAc,GAAA;cAAA,CACA;cACAN,MAAA,CAAA1F,4BAAA,CAAAiB,MAAA,IAAAvD,IAAA,GAAAgG,GAAA,CAAAQ,IAAA,CAAA6B,GAAA,CACA,UAAAb,IAAA;gBAAA,OAAAA,IAAA,CAAAe,QAAA;cAAA,CACA;cACAP,MAAA,CAAA1F,4BAAA,CAAAiB,MAAA,IAAAvD,IAAA,GAAAgG,GAAA,CAAAQ,IAAA,CAAA6B,GAAA,CACA,UAAAb,IAAA;gBAAA,OAAAA,IAAA,CAAAgB,OAAA;cAAA,CACA;cACAR,MAAA,CAAA9F,4BAAA;YAAA;YAAA;cAAA,OAAAiG,SAAA,CAAAvB,IAAA;UAAA;QAAA,GAAAqB,QAAA;MAAA;IACA;IACA;IACAvD,mBAAA,WAAAA,oBAAA;MAAA,IAAA+D,MAAA;MAAA,OAAA7C,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA4C,SAAA;QAAA,IAAA1C,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA0C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxC,IAAA,GAAAwC,SAAA,CAAAvC,IAAA;YAAA;cAAAuC,SAAA,CAAAvC,IAAA;cAAA,OACAhJ,mBAAA;YAAA;cAAA2I,GAAA,GAAA4C,SAAA,CAAAtC,IAAA;cACAmC,MAAA,CAAA/J,wBAAA,GAAAsH,GAAA,CAAAQ,IAAA;YAAA;YAAA;cAAA,OAAAoC,SAAA,CAAAhC,IAAA;UAAA;QAAA,GAAA8B,QAAA;MAAA;IACA;IACA;IACA/D,YAAA,WAAAA,aAAA;MAAA,IAAAkE,MAAA;MAAA,OAAAjD,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAgD,SAAA;QAAA,IAAA9C,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA8C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5C,IAAA,GAAA4C,SAAA,CAAA3C,IAAA;YAAA;cACAwC,MAAA,CAAAnH,sCAAA;cAAAsH,SAAA,CAAA3C,IAAA;cAAA,OACA/I,YAAA;YAAA;cAAA0I,GAAA,GAAAgD,SAAA,CAAA1C,IAAA;cAEAuC,MAAA,CAAAlH,+BAAA,GAAAqE,GAAA,CAAAQ,IAAA;cACAqC,MAAA,CAAAnH,sCAAA;YAAA;YAAA;cAAA,OAAAsH,SAAA,CAAApC,IAAA;UAAA;QAAA,GAAAkC,QAAA;MAAA;IACA;IACA;IACAlE,cAAA,WAAAA,eAAA;MAAA,IAAAqE,MAAA;MAAA,OAAArD,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAoD,SAAA;QAAA,IAAAlD,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAkD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhD,IAAA,GAAAgD,SAAA,CAAA/C,IAAA;YAAA;cACA4C,MAAA,CAAArH,iCAAA;cAAAwH,SAAA,CAAA/C,IAAA;cAAA,OAEA9I,cAAA;YAAA;cAAAyI,GAAA,GAAAoD,SAAA,CAAA9C,IAAA;cACA2C,MAAA,CAAApH,0BAAA,GAAAmE,GAAA,CAAAQ,IAAA,CAAA6C,KAAA;cACAJ,MAAA,CAAArH,iCAAA;YAAA;YAAA;cAAA,OAAAwH,SAAA,CAAAxC,IAAA;UAAA;QAAA,GAAAsC,QAAA;MAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACArE,iBAAA,WAAAA,kBAAA;MAAA,IAAAyE,MAAA;MAAA,OAAA1D,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAyD,SAAA;QAAA,IAAAvD,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAuD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArD,IAAA,GAAAqD,SAAA,CAAApD,IAAA;YAAA;cACAiD,MAAA,CAAAhI,iCAAA;cAAAmI,SAAA,CAAApD,IAAA;cAAA,OACA5I,iBAAA;YAAA;cAAAuI,GAAA,GAAAyD,SAAA,CAAAnD,IAAA;cACAgD,MAAA,CAAA/H,0BAAA,GAAAyE,GAAA,CAAAQ,IAAA;cACA8C,MAAA,CAAAhI,iCAAA;YAAA;YAAA;cAAA,OAAAmI,SAAA,CAAA7C,IAAA;UAAA;QAAA,GAAA2C,QAAA;MAAA;IACA;IACA;IACAzE,wBAAA,WAAAA,yBAAA;MAAA,IAAA4E,OAAA;MAAA,OAAA9D,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA6D,SAAA;QAAA,IAAA3D,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA2D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzD,IAAA,GAAAyD,SAAA,CAAAxD,IAAA;YAAA;cACAqD,OAAA,CAAAlI,0DAAA;cAAAqI,SAAA,CAAAxD,IAAA;cAAA,OACA3I,wBAAA;gBACAoM,OAAA,EAAAJ,OAAA,CAAArI;cACA;YAAA;cAFA2E,GAAA,GAAA6D,SAAA,CAAAvD,IAAA;cAGAoD,OAAA,CAAAjI,mDAAA,GAAAuE,GAAA,CAAAQ,IAAA;cACAkD,OAAA,CAAAlI,0DAAA;YAAA;YAAA;cAAA,OAAAqI,SAAA,CAAAjD,IAAA;UAAA;QAAA,GAAA+C,QAAA;MAAA;IACA;IACA;IACA5E,8BAAA,WAAAA,+BAAA;MAAA,IAAAgF,OAAA;MAAA,OAAAnE,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAkE,UAAA;QAAA,IAAAhE,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAgE,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9D,IAAA,GAAA8D,UAAA,CAAA7D,IAAA;YAAA;cACA0D,OAAA,CAAAlG,4BAAA;cAAAqG,UAAA,CAAA7D,IAAA;cAAA,OACA1I,8BAAA;gBACAwM,EAAA,EAAAJ,OAAA,CAAApG,0BAAA;gBACAyG,IAAA;cACA;YAAA;cAHApE,GAAA,GAAAkE,UAAA,CAAA5D,IAAA;cAIAyD,OAAA,CAAAjG,4BAAA,CAAAnB,KAAA,CAAA3C,IAAA,GAAAgG,GAAA,CAAAQ,IAAA,CAAA6B,GAAA,CACA,UAAAb,IAAA;gBAAA,OAAAA,IAAA,CAAA6C,KAAA;cAAA,CACA;cACAN,OAAA,CAAAjG,4BAAA,CAAAP,MAAA,IAAAvD,IAAA,GAAAgG,GAAA,CAAAQ,IAAA,CAAA6B,GAAA,CACA,UAAAb,IAAA;gBAAA,OAAAA,IAAA,CAAA8C,KAAA;cAAA,CACA;cACAP,OAAA,CAAAlG,4BAAA;YAAA;YAAA;cAAA,OAAAqG,UAAA,CAAAtD,IAAA;UAAA;QAAA,GAAAoD,SAAA;MAAA;IACA;IAEA;IACAhF,yBAAA,WAAAA,0BAAA;MAAA,IAAAuF,OAAA;MAAA,OAAA3E,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA0E,UAAA;QAAA,IAAAxE,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAwE,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAtE,IAAA,GAAAsE,UAAA,CAAArE,IAAA;YAAA;cACAkE,OAAA,CAAArJ,2CAAA;cAAAwJ,UAAA,CAAArE,IAAA;cAAA,OACAzI,yBAAA;YAAA;cAAAoI,GAAA,GAAA0E,UAAA,CAAApE,IAAA;cACAiE,OAAA,CAAApJ,uCAAA,GAAA6E,GAAA,CAAAQ,IAAA,CAAAmE,YAAA;cACAJ,OAAA,CAAAnJ,uCAAA,GAAA4E,GAAA,CAAAQ,IAAA,CAAAoE,YAAA;cACAL,OAAA,CAAArJ,2CAAA;YAAA;YAAA;cAAA,OAAAwJ,UAAA,CAAA9D,IAAA;UAAA;QAAA,GAAA4D,SAAA;MAAA;IACA;IAEA;IACAvF,yBAAA,WAAAA,0BAAA;MAAA,IAAA4F,OAAA;MAAA,OAAAjF,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAgF,UAAA;QAAA,IAAA9E,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA8E,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA5E,IAAA,GAAA4E,UAAA,CAAA3E,IAAA;YAAA;cACAwE,OAAA,CAAA3G,yBAAA;cAAA8G,UAAA,CAAA3E,IAAA;cAAA,OACAxI,yBAAA;YAAA;cAAAmI,GAAA,GAAAgF,UAAA,CAAA1E,IAAA;cACAuE,OAAA,CAAA1G,sBAAA,GAAA6B,GAAA,CAAAQ,IAAA,CAAA6B,GAAA,WAAAb,IAAA;gBAAA;kBACA8C,KAAA,EAAA9C,IAAA,CAAAyD,IAAA;kBACAZ,KAAA,EAAA7C,IAAA,CAAA0D;gBACA;cAAA;cACAL,OAAA,CAAA3G,yBAAA;YAAA;YAAA;cAAA,OAAA8G,UAAA,CAAApE,IAAA;UAAA;QAAA,GAAAkE,SAAA;MAAA;IACA;IACA;IACA5F,2BAAA,WAAAA,4BAAA;MAAA,IAAAiG,OAAA;MAAA,OAAAvF,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAsF,UAAA;QAAA,IAAApF,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAoF,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAlF,IAAA,GAAAkF,UAAA,CAAAjF,IAAA;YAAA;cACA8E,OAAA,CAAArK,wCAAA;cAAAwK,UAAA,CAAAjF,IAAA;cAAA,OACAvI,2BAAA;gBACAsM,IAAA,EAAAnL,KAAA,CAAAkM,OAAA,CAAApK,sCAAA,EAAAE,MAAA,CACA,SACA;cACA;YAAA;cAJA+E,GAAA,GAAAsF,UAAA,CAAAhF,IAAA;cAKA6E,OAAA,CAAAlH,oCAAA,GAAA+B,GAAA,CAAAQ,IAAA;cACA2E,OAAA,CAAArK,wCAAA;YAAA;YAAA;cAAA,OAAAwK,UAAA,CAAA1E,IAAA;UAAA;QAAA,GAAAwE,SAAA;MAAA;IACA;IAEA;IACAjG,gBAAA,WAAAA,iBAAA;MAAA,IAAAoG,OAAA;MAAA,OAAA3F,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA0F,UAAA;QAAA,IAAAxF,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAwF,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAtF,IAAA,GAAAsF,UAAA,CAAArF,IAAA;YAAA;cACAkF,OAAA,CAAA3K,kBAAA;cAAA8K,UAAA,CAAArF,IAAA;cAAA,OACAtI,gBAAA;YAAA;cAAAiI,GAAA,GAAA0F,UAAA,CAAApF,IAAA;cACAiF,OAAA,CAAA1K,WAAA,GAAAmF,GAAA,CAAAQ,IAAA;cACA+E,OAAA,CAAA3K,kBAAA;YAAA;YAAA;cAAA,OAAA8K,UAAA,CAAA9E,IAAA;UAAA;QAAA,GAAA4E,SAAA;MAAA;IACA;IACA;IACAG,mCAAA,WAAAA,oCAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAC,QAAA,GAAAF,IAAA,CAAAE,QAAA;MACA,SAAAC,WAAA,CAAAD,QAAA;QACA;MACA;QACA;MACA;IACA;IAEAE,kBAAA,WAAAA,mBAAAC,KAAA;MAAA,IAAAJ,GAAA,GAAAI,KAAA,CAAAJ,GAAA;QAAAC,QAAA,GAAAG,KAAA,CAAAH,QAAA;MACA;IACA;IACAI,aAAA,WAAAA,cAAAC,KAAA;MAAA,IAAAN,GAAA,GAAAM,KAAA,CAAAN,GAAA;QAAAC,QAAA,GAAAK,KAAA,CAAAL,QAAA;MACA;IACA;IAEA;IACAM,YAAA,WAAAA,aAAAC,KAAA;MAAA,IAAAR,GAAA,GAAAQ,KAAA,CAAAR,GAAA;QAAAC,QAAA,GAAAO,KAAA,CAAAP,QAAA;MACA,SAAAC,WAAA,CAAAD,QAAA;QACA;MACA;QACA;MACA;IACA;IAEAQ,2BAAA,WAAAA,4BAAA;MACA,KAAAlH,eAAA;IACA;IACAmH,2BAAA,WAAAA,4BAAA;MACA,KAAAxH,8BAAA;IACA;IACAyH,4CAAA,WAAAA,6CAAA;MACA,KAAAtH,2BAAA;IACA;IACAuH,WAAA,WAAAA,YAAAC,GAAA,EAAAC,KAAA;IACA;IACAZ,WAAA,WAAAA,YAAAa,MAAA;MACA,IAAAA,MAAA;QACA;MACA;QACA;MACA;IACA;IACA;IACAtH,yBAAA,WAAAA,0BAAA;MAAA,IAAAuH,OAAA;MAAA,OAAAjH,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAgH,UAAA;QAAA,IAAA9G,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA8G,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA5G,IAAA,GAAA4G,UAAA,CAAA3G,IAAA;YAAA;cACAwG,OAAA,CAAAzI,6BAAA;cAAA4I,UAAA,CAAA3G,IAAA;cAAA,OACAlI,yBAAA;gBACAgM,EAAA,EAAA0C,OAAA,CAAA9K;cACA;YAAA;cAFAiE,GAAA,GAAAgH,UAAA,CAAA1G,IAAA;cAGA,IAAAN,GAAA,CAAAO,SAAA;gBACAsG,OAAA,CAAAxI,sBAAA,GAAA2B,GAAA,CAAAQ,IAAA;cACA;cACAqG,OAAA,CAAAzI,6BAAA;YAAA;YAAA;cAAA,OAAA4I,UAAA,CAAApG,IAAA;UAAA;QAAA,GAAAkG,SAAA;MAAA;IACA;IACAG,wBAAA,WAAAA,yBAAA;MACA,KAAA3H,yBAAA;IACA;EACA;AACA", "ignoreList": []}]}