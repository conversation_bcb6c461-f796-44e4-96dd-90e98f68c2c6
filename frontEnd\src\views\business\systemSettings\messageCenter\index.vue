<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
// import getGridByCode from "../../safetyManagement/mixins/index";
// import DialogForm from "./dialogForm.vue";

import { downloadFile } from '@/utils/downloadFile'
import dayjs from 'dayjs'
import {
  GetWBMessageList,
  GetMessageType,
  GetPublishUnitList
} from '@/api/business/eventManagement'
export default {
  name: '',
  components: {
    CustomTable,
    CustomForm,
    CustomLayout
  },
  data() {
    return {
      currentComponent: null,
      componentsConfig: {
        Data: {}
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '编辑',
      tableSelection: [],
      ruleForm: {
        Title: '',
        MessageType: '',
        Source: '',
        CreateUser: '',
        ReceiveUserName: '',
        StartTime: null,
        EndTime: null,
        Date: []
      },
      customForm: {
        formItems: [
          {
            key: 'Title',
            label: '消息标题',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          // {
          //   key: "MessageType",
          //   label: "事件类型",
          //   type: "select",
          //   options: [],
          //   otherOptions: {
          //     clearable: true,
          //   },
          //   change: (e) => {
          //     // change事件
          //     console.log(e);
          //     // this.GetTypesByModule();
          //   },
          // },
          {
            key: 'MessageType',
            label: '消息类型',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'Source',
            label: '来源',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'CreateUser',
            label: '创建人',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'ReceiveUserName',
            label: '接收人',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'Date', // 字段ID
            label: '发送时间', // Form的label
            type: 'datePicker', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              type: 'daterange',
              disabled: false,
              placeholder: '请输入...'
            },
            change: (e) => {
              // change事件
              console.log(e)
              if (e && e.length > 0) {
                this.ruleForm.StartTime = dayjs(e[0]).format('YYYY-MM-DD')
                this.ruleForm.EndTime = dayjs(e[1]).format('YYYY-MM-DD')
              }
            }
          }
        ],
        rules: {},
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        loading: false,
        buttonConfig: {
          buttonList: [
            // {
            //   text: "批量关闭",
            //   onclick: (item) => {
            //     console.log(item);
            //     this.handleClose();
            //   },
            // },
          ]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            otherOptions: {
              type: 'selection',
              align: 'center',
              fixed: 'left'
            }
          },
          {
            label: '消息标题',
            key: 'Title',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '事件类型',
            key: 'EventTypeName',
            width: 140,
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '来源',
            key: 'SourceName',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '业务模块',
            key: 'Module',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '接收方',
            key: 'ReceiveUser',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '创建人',
            key: 'SendUserName',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '发送时间',
            key: 'SendTime',
            otherOptions: {
              align: 'center'
            }
          }
        ],
        tableData: [],
        operateOptions: {
          align: 'center',
          width: '180'
        },
        tableActions: [
          {
            actionLabel: '查看详情',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              // this.handleEdit(row);
              const platform = 'digitalfactory'
              const code = 'szgc'
              const id = '97b119f9-e634-4d95-87b0-df2433dc7893'
              let url = ''
              if (row.Module == '能耗管理') {
                url = '/business/energy/alarmDetail'
              } else if (row.Module == '车辆道闸') {
                url = '/bussiness/vehicle/alarm-info'
              } else if (row.Module == '门禁管理') {
                url = '/business/AccessControlAlarmDetails'
              } else if (row.Module == '安防管理') {
                url = '/business/equipmentAlarm'
              } else if (row.Module == '危化品管理') {
                url = '/business/hazchem/alarmInformation'
              } else if (row.Module == '环境管理') {
                url = '/business/environment/alarmInformation'
              } else if (row.Module == '访客管理') {
                url = '/business/energy/alarmDetail'
                console.log('访客管理')
              }
              this.$qiankun.switchMicroAppFn(platform, code, id, url)
            }
          }
          // {
          //   actionLabel: "编辑",
          //   otherOptions: {
          //     type: "text",
          //   },
          //   onclick: (index, row) => {
          //     this.handleEdit(row);
          //   },
          // },
        ]
      }
    }
  },
  computed: {},
  created() {
    this.init()

    this.GetMessageType()
    this.getPublishUnitList()
  },
  methods: {
    async handleClose() {
      const res = await SetWarningStatus({
        Status: '2',
        Ids: this.tableSelection.map((item) => item.Id)
      })
      if (res.IsSucceed) {
        this.$message.success('操作成功')
        this.onFresh()
      }
    },
    async getPublishUnitList() {
      const res = await GetPublishUnitList({})
      if (res.IsSucceed) {
        const result = res.Data || []
        this.customForm.formItems.find(
          (item) => item.key == 'Source'
        ).options = result.map((item) => ({
          value: item.Id,
          label: item.Name
        }))
      }
    },
    async GetMessageType() {
      const res = await GetMessageType({})
      console.log(res, 'res')
      if (res.IsSucceed) {
        const result = res.Data || []
        const typeList = result.map((item) => ({
          value: item.Value,
          label: item.Name
        }))
        console.log(typeList, 'typeList')
        this.customForm.formItems.find((item) => item.key == 'MessageType').options =
        typeList
      }
    },

    searchForm(data) {
      console.log(data)
      this.onFresh()
    },
    resetForm() {
      this.ruleForm.StartTime = null
      this.ruleForm.EndTime = null
      this.ruleForm.Date = null
      this.onFresh()
    },
    onFresh() {
      this.GetWBMessageList()
    },

    init() {
      // this.getGridByCode("AccessControlAlarmDetails1");
      this.GetWBMessageList()
    },
    async GetWBMessageList() {
      this.customTableConfig.loading = true
      const res = await GetWBMessageList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm
      }).finally(() => {
        this.customTableConfig.loading = false
      })
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data
        this.customTableConfig.total = res.Data.TotalCount
      } else {
        this.$message.error(res.Message)
      }
    },
    async handleExport() {
      const res = await ExportEntranceWarning({
        id: this.tableSelection.map((item) => item.Id).toString(),
        code: 'AccessControlAlarmDetails1'
      })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '告警明细数据')
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.GetWBMessageList()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.GetWBMessageList()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
    },
    handleEdit(row) {
      this.dialogVisible = true
      this.componentsConfig.Data = row
    }
  }
}
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
.layout{
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
