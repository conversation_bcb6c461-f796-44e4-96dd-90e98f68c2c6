{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\workshopBulletinBoard\\pjProductionConfiguration\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\workshopBulletinBoard\\pjProductionConfiguration\\index.vue", "mtime": 1755674552441}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["parseTime", "CustomLayout", "CustomTable", "CustomForm", "DialogForm", "dialogImport", "GetPreferenceSettingValue", "GetGridByCode", "RoleAuthorization", "addRouterPage", "GetPageList", "DeleteEntity", "name", "components", "mixins", "data", "_this", "addPageArray", "path", "$route", "hidden", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "meta", "title", "currentComponent", "componentsConfig", "componentsFuns", "open", "dialogVisible", "close", "onFresh", "dialogTitle", "tableSelection", "ruleForm", "Board_Name", "customForm", "formItems", "key", "label", "type", "placeholder", "otherOptions", "clearable", "change", "e", "console", "log", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "text", "round", "plain", "circle", "loading", "disabled", "icon", "autofocus", "size", "onclick", "item", "handleCreate", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "width", "align", "tableData", "tableActionsWidth", "tableActions", "actionLabel", "index", "row", "handleView2", "handleEdit", "handleDelete", "handleCopy", "handleImport", "roleAuthorizationList", "Big_Screen_Url", "computed", "created", "getBaseData", "init", "activated", "methods", "_this2", "code", "res", "IsSucceed", "_this2$customTableCon", "Data", "ColumnList", "map", "temp", "Display_Name", "Code", "render", "$createElement", "attrs", "on", "click", "val", "handleView", "push", "apply", "_toConsumableArray", "$message", "message", "Message", "workObjId", "localStorage", "getItem", "menuId", "Id", "menuType", "roleType", "sign", "find", "display_name", "is_enabled", "searchForm", "resetForm", "getPageList", "_this3", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "_objectSpread", "Page", "PageSize", "sent", "Modify_Date", "Date", "Create_Date", "TotalCount", "stop", "$router", "query", "pg_redirect", "_this4", "$nextTick", "$refs", "initView", "Equipment_Ids", "window", "concat", "id", "textareaEle", "document", "createElement", "value", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "_this5", "_this6", "$confirm", "_ref", "_callee2", "_", "_callee2$", "_context2", "_x", "arguments", "catch", "handleSizeChange", "handleCurrentChange", "handleSelectionChange", "selection"], "sources": ["src/views/business/workshopBulletinBoard/pjProductionConfiguration/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      top=\"10vh\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n    <!-- 导入弹窗 -->\r\n    <!-- <dialogImport ref=\"dialogImport\" /> -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { parseTime } from \"@/utils\";\r\n// import { baseUrl } from '@/utils/baseurl'\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport DialogForm from \"./components/dialogTable.vue\";\r\nimport dialogImport from \"./components/dialogImport.vue\";\r\n// import { downloadFile } from '@/utils/downloadFile'\r\nimport { GetPreferenceSettingValue } from \"@/api/sys/system-setting\";\r\nimport { GetGridByCode } from \"@/api/sys\";\r\nimport { RoleAuthorization } from \"@/api/user\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\nimport {\r\n  GetPageList,\r\n  DeleteEntity,\r\n} from \"@/api/business/productionConfiguration\";\r\n// import { divide } from 'xe-utils'\r\nexport default {\r\n  name: \"ProductionConfiguration\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/add\",\r\n          hidden: true,\r\n          component: () => import(\"./components/add.vue\"),\r\n          name: \"ProductionConfigurationAdd\",\r\n          meta: { title: `新增` },\r\n        },\r\n        {\r\n          path: this.$route.path + \"/edit\",\r\n          hidden: true,\r\n          component: () => import(\"./components/add.vue\"),\r\n          name: \"ProductionConfigurationEdit\",\r\n          meta: { title: `编辑` },\r\n        },\r\n        {\r\n          path: this.$route.path + \"/view\",\r\n          hidden: true,\r\n          component: () => import(\"./components/add.vue\"),\r\n          name: \"ProductionConfigurationView\",\r\n          meta: { title: `查看` },\r\n        },\r\n      ],\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"\",\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Board_Name: \"\",\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Board_Name\", // 字段ID\r\n            label: \"看板名称\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            placeholder: \"请输入看板\",\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"新增\",\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: false, // 是否禁用\r\n              icon: \"\", //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 60,\r\n            label: \"序号\",\r\n            otherOptions: {\r\n              type: \"index\",\r\n              align: \"center\",\r\n            },\r\n          },\r\n          //   {\r\n          //     label: '报表编码',\r\n          //     key: 'Code'\r\n          //   },\r\n          //   {\r\n          //     label: '报表名称',\r\n          //     key: 'Name'\r\n          //   },\r\n          //   {\r\n          //     label: '报表创建时间',\r\n          //     key: 'Create_Date'\r\n          //   },\r\n          //   {\r\n          //     label: '报表创建人',\r\n          //     key: 'Create_UserName'\r\n          //   }\r\n        ],\r\n        tableData: [],\r\n        tableActionsWidth: 240,\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleView2(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"复制链接\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleCopy(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"导入数据\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: false,\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleImport(index, row);\r\n            },\r\n          },\r\n        ],\r\n        otherOptions: {},\r\n      },\r\n      roleAuthorizationList: [],\r\n      Big_Screen_Url:'',\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.getBaseData();\r\n    this.init();\r\n  },\r\n  activated() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    getBaseData() {\r\n      // 获取表格配置\r\n      GetGridByCode({ code: \"production_configuration_list\" }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data.ColumnList.map((item) => {\r\n            const temp = {\r\n              label: item.Display_Name,\r\n              key: item.Code,\r\n            };\r\n            if (item.Code === \"Ids\") {\r\n              temp.render = (row) => {\r\n                return this.$createElement(\r\n                  \"el-button\",\r\n                  {\r\n                    attrs: {\r\n                      type: \"text\",\r\n                    },\r\n                    on: {\r\n                      click: (val) => {\r\n                        this.handleView(val, row);\r\n                      },\r\n                    },\r\n                  },\r\n                  \"查看\"\r\n                );\r\n              };\r\n            }\r\n            return temp;\r\n          });\r\n          this.customTableConfig.tableColumns.push(...data);\r\n        } else {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: res.Message,\r\n          });\r\n        }\r\n      });\r\n      /**\r\n     *  menuType: 2, //1PC 2app\r\n        roleType: 3, //1菜单权限，2列权限 ，3按钮权限\r\n     */\r\n      RoleAuthorization({\r\n        workObjId: localStorage.getItem(\"Last_Working_Object_Id\"),\r\n        menuId: this.$route.meta.Id,\r\n        menuType: 1,\r\n        roleType: 3,\r\n        sign: 10,\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.roleAuthorizationList = res.Data;\r\n          this.customTableConfig.tableActions[1].otherOptions.disabled =\r\n            !this.roleAuthorizationList.find(\r\n              (item) => item.display_name === \"编辑\"\r\n            ).is_enabled;\r\n          this.customTableConfig.tableActions[2].otherOptions.disabled =\r\n            !this.roleAuthorizationList.find(\r\n              (item) => item.display_name === \"删除\"\r\n            ).is_enabled;\r\n          this.customTableConfig.tableActions[4].otherOptions.disabled =\r\n            !this.roleAuthorizationList.find(\r\n              (item) => item.display_name === \"导入数据\"\r\n            ).is_enabled;\r\n        } else {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: res.Message,\r\n          });\r\n        }\r\n      });\r\n\r\n      GetPreferenceSettingValue({\r\n        Code: \"Big_screen\",\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.Big_Screen_Url = res.Data;\r\n        }\r\n      });\r\n    },\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.getPageList();\r\n    },\r\n    init() {\r\n      this.getPageList();\r\n    },\r\n    async getPageList() {\r\n      const res = await GetPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => {\r\n          item.Modify_Date = item.Modify_Date\r\n            ? parseTime(new Date(item.Modify_Date), \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n            : \"\";\r\n          item.Create_Date = item.Create_Date\r\n            ? parseTime(new Date(item.Create_Date), \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n            : \"\";\r\n          // item.Is_Push = item.Is_Push ? '是' : '否'\r\n          return item;\r\n        });\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n      } else {\r\n        this.$message({\r\n          type: \"error\",\r\n          message: res.Message,\r\n        });\r\n      }\r\n    },\r\n    // 新增\r\n    handleCreate() {\r\n      //   this.dialogTitle = '新增'\r\n      //   this.dialogVisible = true\r\n      this.$router.push({\r\n        name: \"ProductionConfigurationAdd\",\r\n        query: { pg_redirect: this.$route.name, type: 1 },\r\n      });\r\n      // this.$qiankun.switchMicroAppFn(\r\n      //   \"project\",\r\n      //   \"szdn\",\r\n      //   \"1cf6f8ac-d9d0-4b18-9959-5e4ef37886f4\",\r\n      //   `/business/workshop/productionConfiguration/add?pg_redirect=${this.$route.name}&type=1`\r\n      // );\r\n    },\r\n    // 查看绑定设备明细\r\n    handleView(index, row) {\r\n      console.log(index, row);\r\n      this.currentComponent = DialogForm;\r\n      this.dialogTitle = \"查看绑定设备明细\";\r\n      this.dialogVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.currentComponent.initView(row.Equipment_Ids);\r\n      });\r\n    },\r\n    // 查看看板\r\n    handleView2(index, row) {\r\n      window.open(\r\n        `${this.Big_Screen_Url}/pjProductionBoard/index?id=${\r\n          row.Id\r\n        }&tenant=${localStorage.getItem(\"tenant\")}`,\r\n        \"_blank\"\r\n      );\r\n      // window.open(\r\n      //   `${process.env.VUE_APP_SCREEN_URL}/productionBoard/index?id=${\r\n      //     row.Id\r\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`,\r\n      //   \"_blank\"\r\n      // );\r\n      // if (process.env.NODE_ENV === \"development\") {\r\n      //   window.open(\r\n      //     `http://localhost:5173/productionBoard/index?id=${\r\n      //       row.Id\r\n      //     }&tenant=${localStorage.getItem(\"tenant\")}`,\r\n      //     \"_blank\"\r\n      //   );\r\n      // } else {\r\n      //   window.open(\r\n      //     `http://wnpzgc-test.bimtk.com/productionBoard/index?id=${\r\n      //       row.Id\r\n      //     }&tenant=${localStorage.getItem(\"tenant\")}`,\r\n      //     \"_blank\"\r\n      //   );\r\n      // }\r\n    },\r\n    // 编辑\r\n    handleEdit(index, row) {\r\n      console.log(index, row);\r\n      this.$router.push({\r\n        name: \"ProductionConfigurationAdd\",\r\n        query: { pg_redirect: this.$route.name, type: 2, id: row.Id },\r\n      });\r\n    },\r\n    // 复制链接\r\n    handleCopy(index, row) {\r\n      // console.log(\r\n      //   process.env.VUE_APP_SCREEN_URL,\r\n      //   \"process.env.VUE_APP_SCREEN_URL\"\r\n      // );\r\n      const textareaEle = document.createElement(\"textarea\");\r\n      // if (process.env.NODE_ENV === \"development\") {\r\n      //   textareaEle.value = `http://localhost:5173/productionBoard/index?id=${\r\n      //     row.Id\r\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`;\r\n      // } else {\r\n      //   textareaEle.value = `http://wnpzgc-test.bimtk.com/productionBoard/index?id=${\r\n      //     row.Id\r\n      //   }&tenant=${localStorage.getItem(\"tenant\")}`;\r\n      // }\r\n      textareaEle.value = `${this.Big_Screen_Url}/pjProductionBoard/index?id=${\r\n        row.Id\r\n      }&tenant=${localStorage.getItem(\"tenant\")}`;\r\n\r\n      document.body.appendChild(textareaEle);\r\n      textareaEle.select();\r\n      document.execCommand(\"copy\");\r\n      document.body.removeChild(textareaEle);\r\n      this.$message({\r\n        type: \"success\",\r\n        message: \"已成功复制到截切板\",\r\n      });\r\n    },\r\n    // 导入数据\r\n    handleImport(index, row) {\r\n      console.log(index, row);\r\n      this.currentComponent = dialogImport;\r\n      this.dialogTitle = \"导入数据\";\r\n      this.dialogVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.currentComponent.init(row);\r\n      });\r\n    },\r\n    // 删除\r\n    handleDelete(index, row) {\r\n      console.log(index, row);\r\n      this.$confirm(\"确认删除？\", {\r\n        type: \"warning\",\r\n      })\r\n        .then(async (_) => {\r\n          const res = await DeleteEntity({\r\n            id: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: \"success\",\r\n              message: \"删除成功\",\r\n            });\r\n            this.init();\r\n          } else {\r\n            this.$message({\r\n              type: \"error\",\r\n              message: res.Message,\r\n            });\r\n          }\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.init();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.init();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n  <style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n::v-deep .el-table__fixed-body-wrapper {\r\n  top: 40px !important;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,SAAAA,SAAA;AACA;AACA,OAAAC,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,YAAA;AACA;AACA,SAAAC,yBAAA;AACA,SAAAC,aAAA;AACA,SAAAC,iBAAA;AACA,OAAAC,aAAA;AACA,SACAC,WAAA,EACAC,YAAA,QACA;AACA;AACA;EACAC,IAAA;EACAC,UAAA;IACAX,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAa,MAAA,GAAAL,aAAA;EACAM,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,YAAA,GACA;QACAC,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAd,IAAA;QACAe,IAAA;UAAAC,KAAA;QAAA;MACA,GACA;QACAV,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAd,IAAA;QACAe,IAAA;UAAAC,KAAA;QAAA;MACA,GACA;QACAV,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAd,IAAA;QACAe,IAAA;UAAAC,KAAA;QAAA;MACA,EACA;MACAC,gBAAA,EAAAzB,UAAA;MACA0B,gBAAA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAhB,KAAA,CAAAiB,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAlB,KAAA,CAAAiB,aAAA;UACAjB,KAAA,CAAAmB,OAAA;QACA;MACA;MACAF,aAAA;MACAG,WAAA;MACAC,cAAA;MAEAC,QAAA;QACAC,UAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAAA;UACAC,WAAA;UACAC,YAAA;YACA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,EACA;QACAG,KAAA;UACA;QAAA,CACA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAC,IAAA;YACAC,KAAA;YAAA;YACAC,KAAA;YAAA;YACAC,MAAA;YAAA;YACAC,OAAA;YAAA;YACAC,QAAA;YAAA;YACAC,IAAA;YAAA;YACAC,SAAA;YAAA;YACAtB,IAAA;YAAA;YACAuB,IAAA;YAAA;YACAC,OAAA,WAAAA,QAAAC,IAAA;cACAnB,OAAA,CAAAC,GAAA,CAAAkB,IAAA;cACArD,KAAA,CAAAsD,YAAA;YACA;UACA;QAEA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA,GACA;UACAC,KAAA;UACAjC,KAAA;UACAG,YAAA;YACAF,IAAA;YACAiC,KAAA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAAA,CACA;QACAC,SAAA;QACAC,iBAAA;QACAC,YAAA,GACA;UACAC,WAAA;UACAnC,YAAA;YACAF,IAAA;YACAoB,QAAA;UACA;UACAI,OAAA,WAAAA,QAAAc,KAAA,EAAAC,GAAA;YACAnE,KAAA,CAAAoE,WAAA,CAAAF,KAAA,EAAAC,GAAA;UACA;QACA,GACA;UACAF,WAAA;UACAnC,YAAA;YACAF,IAAA;YACAoB,QAAA;UACA;UACAI,OAAA,WAAAA,QAAAc,KAAA,EAAAC,GAAA;YACAnE,KAAA,CAAAqE,UAAA,CAAAH,KAAA,EAAAC,GAAA;UACA;QACA,GACA;UACAF,WAAA;UACAnC,YAAA;YACAF,IAAA;YACAoB,QAAA;UACA;UACAI,OAAA,WAAAA,QAAAc,KAAA,EAAAC,GAAA;YACAnE,KAAA,CAAAsE,YAAA,CAAAJ,KAAA,EAAAC,GAAA;UACA;QACA,GACA;UACAF,WAAA;UACAnC,YAAA;YACAF,IAAA;YACAoB,QAAA;UACA;UACAI,OAAA,WAAAA,QAAAc,KAAA,EAAAC,GAAA;YACAnE,KAAA,CAAAuE,UAAA,CAAAL,KAAA,EAAAC,GAAA;UACA;QACA,GACA;UACAF,WAAA;UACAnC,YAAA;YACAF,IAAA;YACAoB,QAAA;UACA;UACAI,OAAA,WAAAA,QAAAc,KAAA,EAAAC,GAAA;YACAnE,KAAA,CAAAwE,YAAA,CAAAN,KAAA,EAAAC,GAAA;UACA;QACA,EACA;QACArC,YAAA;MACA;MACA2C,qBAAA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;IACA,KAAAC,IAAA;EACA;EACAC,SAAA,WAAAA,UAAA;IACA,KAAAD,IAAA;EACA;EACAE,OAAA;IACAH,WAAA,WAAAA,YAAA;MAAA,IAAAI,MAAA;MACA;MACA1F,aAAA;QAAA2F,IAAA;MAAA,GAAA1E,IAAA,WAAA2E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UAAA,IAAAC,qBAAA;UACA,IAAAtF,IAAA,GAAAoF,GAAA,CAAAG,IAAA,CAAAC,UAAA,CAAAC,GAAA,WAAAnC,IAAA;YACA,IAAAoC,IAAA;cACA9D,KAAA,EAAA0B,IAAA,CAAAqC,YAAA;cACAhE,GAAA,EAAA2B,IAAA,CAAAsC;YACA;YACA,IAAAtC,IAAA,CAAAsC,IAAA;cACAF,IAAA,CAAAG,MAAA,aAAAzB,GAAA;gBACA,OAAAc,MAAA,CAAAY,cAAA,CACA,aACA;kBACAC,KAAA;oBACAlE,IAAA;kBACA;kBACAmE,EAAA;oBACAC,KAAA,WAAAA,MAAAC,GAAA;sBACAhB,MAAA,CAAAiB,UAAA,CAAAD,GAAA,EAAA9B,GAAA;oBACA;kBACA;gBACA,GACA,IACA;cACA;YACA;YACA,OAAAsB,IAAA;UACA;UACA,CAAAJ,qBAAA,GAAAJ,MAAA,CAAAzC,iBAAA,CAAAmB,YAAA,EAAAwC,IAAA,CAAAC,KAAA,CAAAf,qBAAA,EAAAgB,kBAAA,CAAAtG,IAAA;QACA;UACAkF,MAAA,CAAAqB,QAAA;YACA1E,IAAA;YACA2E,OAAA,EAAApB,GAAA,CAAAqB;UACA;QACA;MACA;MACA;AACA;AACA;AACA;MACAhH,iBAAA;QACAiH,SAAA,EAAAC,YAAA,CAAAC,OAAA;QACAC,MAAA,OAAAzG,MAAA,CAAAQ,IAAA,CAAAkG,EAAA;QACAC,QAAA;QACAC,QAAA;QACAC,IAAA;MACA,GAAAxG,IAAA,WAAA2E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAH,MAAA,CAAAR,qBAAA,GAAAU,GAAA,CAAAG,IAAA;UACAL,MAAA,CAAAzC,iBAAA,CAAAwB,YAAA,IAAAlC,YAAA,CAAAkB,QAAA,GACA,CAAAiC,MAAA,CAAAR,qBAAA,CAAAwC,IAAA,CACA,UAAA5D,IAAA;YAAA,OAAAA,IAAA,CAAA6D,YAAA;UAAA,CACA,EAAAC,UAAA;UACAlC,MAAA,CAAAzC,iBAAA,CAAAwB,YAAA,IAAAlC,YAAA,CAAAkB,QAAA,GACA,CAAAiC,MAAA,CAAAR,qBAAA,CAAAwC,IAAA,CACA,UAAA5D,IAAA;YAAA,OAAAA,IAAA,CAAA6D,YAAA;UAAA,CACA,EAAAC,UAAA;UACAlC,MAAA,CAAAzC,iBAAA,CAAAwB,YAAA,IAAAlC,YAAA,CAAAkB,QAAA,GACA,CAAAiC,MAAA,CAAAR,qBAAA,CAAAwC,IAAA,CACA,UAAA5D,IAAA;YAAA,OAAAA,IAAA,CAAA6D,YAAA;UAAA,CACA,EAAAC,UAAA;QACA;UACAlC,MAAA,CAAAqB,QAAA;YACA1E,IAAA;YACA2E,OAAA,EAAApB,GAAA,CAAAqB;UACA;QACA;MACA;MAEAlH,yBAAA;QACAqG,IAAA;MACA,GAAAnF,IAAA,WAAA2E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAH,MAAA,CAAAP,cAAA,GAAAS,GAAA,CAAAG,IAAA;QACA;MACA;IACA;IACA8B,UAAA,WAAAA,WAAArH,IAAA;MACAmC,OAAA,CAAAC,GAAA,CAAApC,IAAA;MACA,KAAAoB,OAAA;IACA;IACAkG,SAAA,WAAAA,UAAA;MACA,KAAAlG,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAAmG,WAAA;IACA;IACAxC,IAAA,WAAAA,KAAA;MACA,KAAAwC,WAAA;IACA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAxC,GAAA;QAAA,OAAAsC,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAtI,WAAA,CAAAuI,aAAA;gBACAC,IAAA,EAAAX,MAAA,CAAA/E,iBAAA,CAAAgB,WAAA;gBACA2E,QAAA,EAAAZ,MAAA,CAAA/E,iBAAA,CAAAiB;cAAA,GACA8D,MAAA,CAAAjG,QAAA,CACA;YAAA;cAJA6D,GAAA,GAAA2C,QAAA,CAAAM,IAAA;cAKA,IAAAjD,GAAA,CAAAC,SAAA;gBACAmC,MAAA,CAAA/E,iBAAA,CAAAsB,SAAA,GAAAqB,GAAA,CAAAG,IAAA,CAAAA,IAAA,CAAAE,GAAA,WAAAnC,IAAA;kBACAA,IAAA,CAAAgF,WAAA,GAAAhF,IAAA,CAAAgF,WAAA,GACArJ,SAAA,KAAAsJ,IAAA,CAAAjF,IAAA,CAAAgF,WAAA,gCACA;kBACAhF,IAAA,CAAAkF,WAAA,GAAAlF,IAAA,CAAAkF,WAAA,GACAvJ,SAAA,KAAAsJ,IAAA,CAAAjF,IAAA,CAAAkF,WAAA,gCACA;kBACA;kBACA,OAAAlF,IAAA;gBACA;gBACAkE,MAAA,CAAA/E,iBAAA,CAAAkB,KAAA,GAAAyB,GAAA,CAAAG,IAAA,CAAAkD,UAAA;cACA;gBACAjB,MAAA,CAAAjB,QAAA;kBACA1E,IAAA;kBACA2E,OAAA,EAAApB,GAAA,CAAAqB;gBACA;cACA;YAAA;YAAA;cAAA,OAAAsB,QAAA,CAAAW,IAAA;UAAA;QAAA,GAAAd,OAAA;MAAA;IACA;IACA;IACArE,YAAA,WAAAA,aAAA;MACA;MACA;MACA,KAAAoF,OAAA,CAAAvC,IAAA;QACAvG,IAAA;QACA+I,KAAA;UAAAC,WAAA,OAAAzI,MAAA,CAAAP,IAAA;UAAAgC,IAAA;QAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAsE,UAAA,WAAAA,WAAAhC,KAAA,EAAAC,GAAA;MAAA,IAAA0E,MAAA;MACA3G,OAAA,CAAAC,GAAA,CAAA+B,KAAA,EAAAC,GAAA;MACA,KAAAtD,gBAAA,GAAAzB,UAAA;MACA,KAAAgC,WAAA;MACA,KAAAH,aAAA;MACA,KAAA6H,SAAA;QACAD,MAAA,CAAAE,KAAA,CAAAlI,gBAAA,CAAAmI,QAAA,CAAA7E,GAAA,CAAA8E,aAAA;MACA;IACA;IACA;IACA7E,WAAA,WAAAA,YAAAF,KAAA,EAAAC,GAAA;MACA+E,MAAA,CAAAlI,IAAA,IAAAmI,MAAA,CACA,KAAAzE,cAAA,kCAAAyE,MAAA,CACAhF,GAAA,CAAA0C,EAAA,cAAAsC,MAAA,CACAzC,YAAA,CAAAC,OAAA,aACA,QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAtC,UAAA,WAAAA,WAAAH,KAAA,EAAAC,GAAA;MACAjC,OAAA,CAAAC,GAAA,CAAA+B,KAAA,EAAAC,GAAA;MACA,KAAAuE,OAAA,CAAAvC,IAAA;QACAvG,IAAA;QACA+I,KAAA;UAAAC,WAAA,OAAAzI,MAAA,CAAAP,IAAA;UAAAgC,IAAA;UAAAwH,EAAA,EAAAjF,GAAA,CAAA0C;QAAA;MACA;IACA;IACA;IACAtC,UAAA,WAAAA,WAAAL,KAAA,EAAAC,GAAA;MACA;MACA;MACA;MACA;MACA,IAAAkF,WAAA,GAAAC,QAAA,CAAAC,aAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAF,WAAA,CAAAG,KAAA,MAAAL,MAAA,MAAAzE,cAAA,kCAAAyE,MAAA,CACAhF,GAAA,CAAA0C,EAAA,cAAAsC,MAAA,CACAzC,YAAA,CAAAC,OAAA;MAEA2C,QAAA,CAAAG,IAAA,CAAAC,WAAA,CAAAL,WAAA;MACAA,WAAA,CAAAM,MAAA;MACAL,QAAA,CAAAM,WAAA;MACAN,QAAA,CAAAG,IAAA,CAAAI,WAAA,CAAAR,WAAA;MACA,KAAA/C,QAAA;QACA1E,IAAA;QACA2E,OAAA;MACA;IACA;IACA;IACA/B,YAAA,WAAAA,aAAAN,KAAA,EAAAC,GAAA;MAAA,IAAA2F,MAAA;MACA5H,OAAA,CAAAC,GAAA,CAAA+B,KAAA,EAAAC,GAAA;MACA,KAAAtD,gBAAA,GAAAxB,YAAA;MACA,KAAA+B,WAAA;MACA,KAAAH,aAAA;MACA,KAAA6H,SAAA;QACAgB,MAAA,CAAAf,KAAA,CAAAlI,gBAAA,CAAAiE,IAAA,CAAAX,GAAA;MACA;IACA;IACA;IACAG,YAAA,WAAAA,aAAAJ,KAAA,EAAAC,GAAA;MAAA,IAAA4F,MAAA;MACA7H,OAAA,CAAAC,GAAA,CAAA+B,KAAA,EAAAC,GAAA;MACA,KAAA6F,QAAA;QACApI,IAAA;MACA,GACApB,IAAA;QAAA,IAAAyJ,IAAA,GAAAzC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAwC,SAAAC,CAAA;UAAA,IAAAhF,GAAA;UAAA,OAAAsC,mBAAA,GAAAG,IAAA,UAAAwC,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAtC,IAAA,GAAAsC,SAAA,CAAArC,IAAA;cAAA;gBAAAqC,SAAA,CAAArC,IAAA;gBAAA,OACArI,YAAA;kBACAyJ,EAAA,EAAAjF,GAAA,CAAA0C;gBACA;cAAA;gBAFA1B,GAAA,GAAAkF,SAAA,CAAAjC,IAAA;gBAGA,IAAAjD,GAAA,CAAAC,SAAA;kBACA2E,MAAA,CAAAzD,QAAA;oBACA1E,IAAA;oBACA2E,OAAA;kBACA;kBACAwD,MAAA,CAAAjF,IAAA;gBACA;kBACAiF,MAAA,CAAAzD,QAAA;oBACA1E,IAAA;oBACA2E,OAAA,EAAApB,GAAA,CAAAqB;kBACA;gBACA;cAAA;cAAA;gBAAA,OAAA6D,SAAA,CAAA5B,IAAA;YAAA;UAAA,GAAAyB,QAAA;QAAA,CACA;QAAA,iBAAAI,EAAA;UAAA,OAAAL,IAAA,CAAA7D,KAAA,OAAAmE,SAAA;QAAA;MAAA,KACAC,KAAA,WAAAL,CAAA;IACA;IACAM,gBAAA,WAAAA,iBAAAxE,GAAA;MACA/D,OAAA,CAAAC,GAAA,iBAAAgH,MAAA,CAAAlD,GAAA;MACA,KAAAzD,iBAAA,CAAAiB,QAAA,GAAAwC,GAAA;MACA,KAAAnB,IAAA;IACA;IACA4F,mBAAA,WAAAA,oBAAAzE,GAAA;MACA/D,OAAA,CAAAC,GAAA,wBAAAgH,MAAA,CAAAlD,GAAA;MACA,KAAAzD,iBAAA,CAAAgB,WAAA,GAAAyC,GAAA;MACA,KAAAnB,IAAA;IACA;IACA6F,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAvJ,cAAA,GAAAuJ,SAAA;IACA;EACA;AACA", "ignoreList": []}]}