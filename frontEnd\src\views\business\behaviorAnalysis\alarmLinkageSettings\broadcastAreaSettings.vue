<template>
  <div class="app-container abs100">
    <el-card class="box-card">
      <CustomTable
        :custom-table-config="customTableConfig"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
        @handleSelectionChange="handleSelectionChange"
      />
    </el-card>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";
// import getGridByCode from "../../safetyManagement/mixins/index";
import broadcastAreaSettingsDialogForm from "./broadcastAreaSettingsDialogForm.vue";

import dayjs from "dayjs";
import {
  GetAreaSettingList,
  BatchEditAreaSetting,
  GetAreaEntity,
  DeleteAreaSetting,
} from "@/api/business/behaviorAnalysis";
export default {
  name: "",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
  },
  data() {
    return {
      currentComponent: null,
      componentsConfig: {
        Data: {},
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogVisible: false,
      dialogTitle: "编辑",
      tableSelection: [],

      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "新增",
              type: "primary",
              onclick: (item) => {
                console.log(item);
                this.handleCreate();
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            label: "报警位置",
            key: "Position",
            otherOptions: {
              align: "center",
              width: "360",
            },
          },
          {
            label: "广播设备名称",
            key: "EquipmentNameArr",
            otherOptions: {
              align: "center",
            },
            render: (row) => {
              if (row.EquipmentNameArr == "广播失效") {
                return this.$createElement(
                  "span",
                  {
                    style: {
                      color: "red",
                    },
                  },
                  row.EquipmentNameArr
                );
              }
              return this.$createElement(
                "span",
                {},
                row.EquipmentNameArr.join(",")
              );
            },
          },
        ],
        tableData: [],
        operateOptions: {
          align: "center",
          width: "180",
        },
        tableActions: [
          {
            actionLabel: "修改",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleEdit(row);
            },
          },
          {
            actionLabel: "删除",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleDelete(index, row);
            },
          },
        ],
      },
    };
  },
  computed: {},
  created() {
    this.init();
  },
  methods: {
    async handleClose() {
      const res = await SetWarningStatus({
        Status: "2",
        Ids: this.tableSelection.map((item) => item.Id),
      });
      if (res.IsSucceed) {
        this.$message.success("操作成功");
        this.onFresh();
      }
    },

    onFresh() {
      this.GetAreaSettingList();
    },

    init() {
      // this.getGridByCode("AccessControlAlarmDetails1");
      this.GetAreaSettingList();
    },
    async GetAreaSettingList() {
      const res = await GetAreaSettingList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data;
        this.customTableConfig.total = res.Data.TotalCount;
      } else {
        this.$message.error(res.Message);
      }
    },

    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.GetAreaSettingList();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.GetAreaSettingList();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
    handleCreate() {
      this.dialogTitle = "新增告警设置";
      this.componentsConfig = {
        type: "add",
      };
      this.dialogVisible = true;
      this.currentComponent = broadcastAreaSettingsDialogForm;
    },
    handleEdit(row) {
      this.dialogVisible = true;
      this.dialogTitle = "编辑告警设置";
      this.currentComponent = broadcastAreaSettingsDialogForm;
      this.componentsConfig = {
        type: "edit",
        data: row,
      };
    },
    handleDelete(index, row) {
      this.$confirm("请确认是否删除？", "删除", {
        type: "error",
      })
        .then(async (_) => {
          const res = await DeleteAreaSetting({
            PurposeCatetory: row.PurposeCatetory,
            Scene: row.Scene,
            Site: row.Site,
          });
          if (res.IsSucceed) {
            this.init();
            this.$message.success("删除成功");
          } else {
            this.$message.error(res.Message);
          }
        })
        .catch((_) => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
.box-card{
  height: calc(100vh - 190px);
  overflow: auto;
}
</style>
