{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJbehaviorAnalysis\\behaviorAnalysisAlarm\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJbehaviorAnalysis\\behaviorAnalysisAlarm\\index.vue", "mtime": 1755506574450}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/SZCJbehaviorAnalysis/behaviorAnalysisAlarm", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport dialogForm from \"./dialogForm.vue\";\r\nimport dayjs from \"dayjs\";\r\n\r\nimport {\r\n  GetBehaviorWarningListSZCJ,\r\n  GetBehaviorWarningEntity,\r\n  TriggerBehaviorWarning,\r\n} from \"@/api/business/behaviorAnalysis\";\r\nimport { GetDictionaryDetailListByCode } from \"@/api/sys\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: dialogForm,\r\n      componentsConfig: {\r\n        Data: {},\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"告警详情\",\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        DeviceName: \"\",\r\n        WarningType: \"\",\r\n        HandleStatus: \"\",\r\n        Date: [],\r\n        BeginWarningTime: null,\r\n        EndWarningTime: null,\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Date\", // 字段ID\r\n            label: \"告警时间\", // Form的label\r\n            type: \"datePicker\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              type: \"daterange\",\r\n              disabled: false,\r\n              placeholder: \"请输�?..\",\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.BeginWarningTime = dayjs(e[0]).format(\r\n                  \"YYYY-MM-DD\"\r\n                );\r\n                this.ruleForm.EndWarningTime = dayjs(e[1]).format(\"YYYY-MM-DD\");\r\n              }\r\n            },\r\n          },\r\n          {\r\n            key: \"WarningType\",\r\n            label: \"告警类型\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n              this.GetTypesByModule();\r\n            },\r\n          },\r\n          {\r\n            key: \"DeviceName\",\r\n            label: \"告警设备\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"HandleStatus\",\r\n            label: \"状�?,\r\n            type: \"select\",\r\n            options: [\r\n              {\r\n                label: \"待广�?,\r\n                value: \"1\",\r\n              },\r\n              {\r\n                label: \"已提�?,\r\n                value: \"2\",\r\n              },\r\n              {\r\n                label: \"提交成功\",\r\n                value: \"3\",\r\n              },\r\n              {\r\n                label: \"提交失败\",\r\n                value: \"4\",\r\n              },\r\n            ],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          // buttonList: [\r\n          //   {\r\n          //     text: \"批量关闭\",\r\n          //     onclick: (item) => {\r\n          //       console.log(item);\r\n          //       this.handleClose();\r\n          //     },\r\n          //   },\r\n          // ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          // {\r\n          //   otherOptions: {\r\n          //     type: \"selection\",\r\n          //     align: \"center\",\r\n          //     fixed: \"left\",\r\n          //   },\r\n          // },\r\n          {\r\n            label: \"告警时间\",\r\n            key: \"WarningTime\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"告警类型\",\r\n            key: \"WarningTypeDes\",\r\n            width: 140,\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"告警设备\",\r\n            key: \"DeviceName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"告警设备编码\",\r\n            key: \"DeviceCode\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"设备地址\",\r\n            key: \"Position\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"联动广播\",\r\n            key: \"BroadcastEquipmentCount\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"广播时间\",\r\n            key: \"BroadcastTime\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"状�?,\r\n            key: \"HandleStatus\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              if (row.HandleStatus == 1) {\r\n                return this.$createElement(\"span\", {}, \"待广�?);\r\n              } else if (row.HandleStatus == 2) {\r\n                return this.$createElement(\"span\", {}, \"已提�?);\r\n              } else if (row.HandleStatus == 3) {\r\n                return this.$createElement(\r\n                  \"span\",\r\n                  {\r\n                    style: {\r\n                      color: \"green\",\r\n                    },\r\n                  },\r\n                  \"提交成功\"\r\n                );\r\n              } else if (row.HandleStatus == 4) {\r\n                return this.$createElement(\r\n                  \"span\",\r\n                  {\r\n                    style: {\r\n                      color: \"red\",\r\n                    },\r\n                  },\r\n                  \"提交失败\"\r\n                );\r\n              }\r\n              return this.$createElement(\"span\", {}, \"\");\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: \"center\",\r\n          width: \"180\",\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看详情\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"重新广播\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleRebroadcast(row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init();\r\n\r\n    this.getDictionaryDetailListByCode();\r\n  },\r\n  // mixins: [getGridByCode],\r\n  methods: {\r\n    async handleClose() {\r\n      const res = await SetWarningStatus({\r\n        Status: \"2\",\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.$message.success(\"操作成功\");\r\n        this.onFresh();\r\n      }\r\n    },\r\n    async getDictionaryDetailListByCode() {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode: \"BehaviorWarningType\",\r\n      });\r\n      if (res.IsSucceed) {\r\n        let result = res.Data || [];\r\n        let warningType = result.map((item) => ({\r\n          value: item.Value,\r\n          label: item.Display_Name,\r\n        }));\r\n        this.customForm.formItems.find(\r\n          (item) => item.key == \"WarningType\"\r\n        ).options = warningType;\r\n      }\r\n    },\r\n\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.BeginWarningTime = null;\r\n      this.ruleForm.EndWarningTime = null;\r\n      this.ruleForm.Date = null;\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.GetBehaviorWarningList();\r\n    },\r\n\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.GetBehaviorWarningList();\r\n    },\r\n    async GetBehaviorWarningList() {\r\n      const res = await GetBehaviorWarningListSZCJ({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n\r\n    async handleRebroadcast(row) {\r\n      const res = await TriggerBehaviorWarning({\r\n        ID: row.Id,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.$message.success(\"操作成功\");\r\n        this.init();\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.GetBehaviorWarningList();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.GetBehaviorWarningList();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleEdit(row) {\r\n      this.dialogVisible = true;\r\n      this.componentsConfig.Data = row;\r\n      this.componentsConfig = {\r\n        type: \"edit\",\r\n        data: row,\r\n      };\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n"]}]}