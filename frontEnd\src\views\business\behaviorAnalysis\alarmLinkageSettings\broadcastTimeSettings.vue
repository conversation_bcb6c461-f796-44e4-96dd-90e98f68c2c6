<template>
  <div class="app-container abs100">
    <el-card class="box-card">
      <div
        v-loading="loading"
        class="main-wapper"
        :style="{
          maxHeight: !IsEdit
            ? 'calc(100% - 30px - 14px)'
            : 'calc(100% - 14px - 30px - 30px)',
        }"
      >
        <div v-for="(item, idx) in timeData" :key="idx">
          <!-- <el-form v-if="!item.IsDeleted" ref="formRef" :model="item" inline label-width="120px" :rules="idx !== timeData.findIndex(item => !item.IsDeleted) ? formRules : {}" :hide-required-asterisk="true"> -->
          <el-form
            v-if="!item.IsDeleted"
            ref="formRef"
            :model="item"
            inline
            label-width="120px"
            :rules="formRules"
            :hide-required-asterisk="true"
          >
            <el-form-item
              :label="
                idx == timeData.findIndex((item) => !item.IsDeleted)
                  ? '提交广播时间'
                  : ' '
              "
              prop="time"
            >
              <el-time-picker
                v-model="item.time"
                is-range
                :disabled="!IsEdit"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围"
                value-format="HH:mm:ss"
                format="HH:mm:ss"
                @change="changeTime(idx, $event)"
              />
            </el-form-item>
            <el-button
              v-if="IsEdit"
              type="text"
              style="color: #f56c6c"
              @click="handeldelete(item, idx)"
              >删除</el-button
            >
          </el-form>
        </div>
      </div>
      <div v-if="IsEdit">
        <el-button
          type="text"
          style="font-size: 12px; margin-left: 120px"
          @click="handeladd()"
          >+新增</el-button
        >
      </div>
      <div class="prompt">
        系统将于以上设置时间段进行行为分析告警信息广播播放，其他时间识别行为分析告警但不进行广播播放
      </div>
      <div class="cs-btn">
        <el-button v-if="!IsEdit" type="primary" @click="IsEdit = !IsEdit"
          >编辑</el-button
        >
        <el-button
          v-if="IsEdit"
          type="primary"
          :loading="btnloading"
          @click="handelSubmit"
          >确认</el-button
        >
        <el-button v-if="IsEdit" @click="handelCancel">取消</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import { GetBroTimeRangeList, BatchEditBroTimeRange } from '@/api/business/behaviorAnalysis'
import dayjs from 'dayjs'
export default {
  data() {
    return {
      timeData: [],
      IsEdit: false,
      formRules: { time: [{ required: true, message: '请填写时间', trigger: 'change' }] },
      loading: false,
      btnloading: false
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    // 新增
    handeladd() {
      this.timeData.push({
        Id: '',
        StartTime: '',
        EndTime: '',
        time: null,
        IsDeleted: false
      })
    },
    // 删除
    handeldelete(item, idx) {
      if (item.Id) {
        this.timeData[idx].IsDeleted = true
      } else {
        this.timeData.splice(idx, 1)
      }
    },
    // 时间
    changeTime(index, e) {
      console.log(index, e)
      const day = dayjs().format('YYYY-MM-DD')
      this.timeData[index].StartTime = day + ' ' + e[0]
      this.timeData[index].EndTime = day + ' ' + e[1]
      console.log(this.timeData[index])
    },
    // 获取数据
    fetchData() {
      // this.loading = true
      GetBroTimeRangeList({ page: 1, PageSize: -1 }).then((res) => {
        if (res.IsSucceed) {
          if (res.Data.Data.length > 0) {
            this.timeData = res.Data.Data.map((item) => {
              item.time = [item.StartTime, item.EndTime]
              return item
            })
          } else {
            this.timeData = []
            this.handeladd()
          }
          console.log(this.timeData, ' this.timeData')
        } else {
          this.$message.error(res.Message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 提交
    handelSubmit() {
      const validatePromises = this.$refs.formRef.map((item, index) => {
        return new Promise((resolve, reject) => {
          item.validate((valid) => {
            if (valid) {
              resolve()
            } else {
              reject()
            }
          })
        })
      })

      Promise.all(validatePromises)
        .then(() => {
          this.saveInfo()
        })
        .catch(() => {
          return false
        })
    },

    saveInfo() {
      this.btnloading = true
      console.log('111')
      BatchEditBroTimeRange(this.timeData).then((res) => {
        if (res.IsSucceed) {
          this.$message({
            type: 'success',
            message: '保存成功'
          })
          this.fetchData()
        } else {
          this.$message.error(res.Message)
        }
      }).finally(() => {
        this.btnloading = false
      })
    },
    // 取消
    handelCancel() {
      this.timeData = []
      this.IsEdit = !this.IsEdit
      this.fetchData()
    }
  }
}
</script>

<style scoped lang="scss">
.box-card div:not(:first-child) {
  .el-form {
    .el-form-item {
      ::v-deep.el-form-item__label {
        // color: transparent !important;
      }
    }
  }
}

.layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  .box-card {
    height: calc(100vh - 190px);
    overflow: auto;
    max-height: 100%;
    ::v-deep .el-card__body {
      max-height: 100%;
    }
    .main-wapper {
      // max-height: calc(100% - 34px - 30px);
      overflow: auto;
    }
    .prompt {
      color: #a0a0a0;
      font-size: 12px;
      margin: 10px 0;
      margin-left: 120px;
    }

    .cs-btn {
      margin-left: 120px;
    }
  }
}
</style>
