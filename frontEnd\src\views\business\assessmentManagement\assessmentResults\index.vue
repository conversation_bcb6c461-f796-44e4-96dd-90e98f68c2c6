<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible">
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
    /></el-dialog>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";
import dialogView from "./dialog/view.vue";
// import { downloadFile } from "@/utils/downloadFile";
// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'
// import CustomButton from '@/businessComponents/CustomButton/index.vue'

import {
  GetPageList,
  GetDetail,
  Distribute,
  ExportData,
} from "@/api/business/processDocIssuance";
import { GetDictionaryDetailListByCode } from "@/api/sys";
import exportInfo from "@/views/business/vehicleBarrier/mixins/export.js";
// import * as moment from 'moment'
// import dayjs from "dayjs";
export default {
  name: "",
  components: {
    CustomTable,
    // CustomButton,
    // CustomTitle,
    CustomForm,
    CustomLayout,
  },
  mixins: [exportInfo],
  // mixins: [deviceTypeMixins, otherMixin],
  data() {
    return {
      currentComponent: dialogView,
      componentsConfig: {},
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      dialogVisible: false,
      dialogTitle: "",
      tableSelection: [],

      ruleForm: {
        EquipCode: "",

        Status: "",
      },
      customForm: {
        formItems: [
          {
            key: "Status", // 字段ID
            label: "考核角色", // Form的label
            type: "select", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            options: [
              {
                label: "下发中",
                value: "1",
              },
              {
                label: "下发成功",
                value: "2",
              },
              {
                label: "下发失败",
                value: "3",
              },
            ],
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
            },
            change: (e) => {
              // change事件
              console.log(e);
            },
          },
          {
            key: "EquipCode",
            label: "考核月",
            type: "datePicker",
            otherOptions: {
              type: "month",
              clearable: true,
              valueFormat: "yyyy-MM-dd",
            },
            change: (e) => {
              // if (e.length > 0) {
              //   this.ruleForm.CreateStartTime = e[0] + " 00:00:00";
              //   this.ruleForm.CreateEndTime = e[1] + " 23:59:59";
              // } else {
              //   this.ruleForm.CreateStartTime = null;
              //   this.ruleForm.CreateEndTime = null;
              // }
            },
          },
        ],
        rules: {},
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "添加配置",
              onclick: (item) => {
                console.log(item);
                this.dialogVisible = true;
                this.dialogTitle = "添加配置";
                // this.handleAllExport();
                // this.ExportData(this.ruleForm, "工艺文件下发", ExportData);
              },
            },
          ],
        },
        // 表格
        loading: false,
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            label: "考核角色",
            key: "DistributeDate",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "考核月",
            key: "FileName",
            otherOptions: {
              align: "center",
            },
          },
          {
            label: "得分",
            key: "EquipName",
            otherOptions: {
              align: "center",
            },
          },
        ],
        tableData: [],
        operateOptions: {
          width: 200,
        },
        tableActions: [
          {
            actionLabel: "下载明细",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {},
          },
        ],
      },
      deceiveTypeList: [],
    };
  },
  computed: {},
  mounted() {
    this.init();
  },
  methods: {
    searchForm(data) {
      this.customTableConfig.currentPage = 1;
      this.onFresh();
    },
    resetForm() {
      this.ruleForm.ExcuteStartTime = null;
      this.ruleForm.ExcuteEndTime = null;
      this.ruleForm.CreateStartTime = null;
      this.ruleForm.CreateEndTime = null;
      this.onFresh();
    },
    onFresh() {
      this.GetDataList();
    },
    init() {
      this.GetDataList();
    },
    async GetDataList() {
      this.customTableConfig.loading = true;
      let res = await GetPageList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      this.customTableConfig.loading = false;
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data;
        this.customTableConfig.total = res.Data.Total;
      } else {
        this.$message.error(res.Message);
      }
    },
    async getDictionaryDetailListByCode(dictionaryCode = "deviceType", Value) {
      const res = await GetDictionaryDetailListByCode({
        dictionaryCode,
      });
      if (res.IsSucceed) {
        const options = [{ label: "全部", value: "" }];
        res.Data.map((item) => {
          options.push({
            label: item.Display_Name,
            value: item[Value],
            ...item,
          });
        });
        return options;
      }
    },
    handleEdit(index, row, type) {
      console.log(index, row, type);
      this.dialogVisible = true;
      // if (type === "view") {
      //   this.dialogTitle = "查看";
      //   this.currentComponent = null;
      //   this.componentsConfig = {
      //     ID: row.Id,
      //     disabled: true,
      //     title: "查看",
      //     ...row,
      //   };
      // }
      // else if (type === 'edit') {
      //   this.dialogTitle = '编辑'
      //   this.componentsConfig = {
      //     ID: row.ID,
      //     disabled: false,
      //     title: '编辑'
      //   }
      // }
    },
    // async handleExport() {
    //   console.log(this.ruleForm)
    //   const res = await ExportDataList({
    //     Content: '',
    //     EqtType: '',
    //     Position: '',
    //     IsAll: false,
    //     Ids: this.tableSelection.map((item) => item.Id),
    //     ...this.ruleForm
    //   })
    //   if (res.IsSucceed) {
    //     console.log(res)
    //     downloadFile(res.Data, '21')
    //   } else {
    //     this.$message.error(res.Message)
    //   }
    // },
    // async handleAllExport() {
    //   const res = await ExportDataList({
    //     Content: '',
    //     EqtType: '',
    //     Position: '',
    //     IsAll: true,
    //     Ids: [],
    //     ...this.ruleForm
    //   })
    //   if (res.IsSucceed) {
    //     console.log(res)
    //     downloadFile(res.Data, '21')
    //   } else {
    //     this.$message.error(res.Message)
    //   }
    // },
    // v2 版本导出
    // async handleAllExport() {
    //   const res = await ExportData({
    //     ...this.ruleForm,
    //   });
    //   if (res.IsSucceed) {
    //     console.log(res);
    //     downloadFile(res.Data, "21");
    //   } else {
    //     this.$message.error(res.Message);
    //   }
    // },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.init();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
  },
};
</script>

<style lang="scss" scoped>
.layout {
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
