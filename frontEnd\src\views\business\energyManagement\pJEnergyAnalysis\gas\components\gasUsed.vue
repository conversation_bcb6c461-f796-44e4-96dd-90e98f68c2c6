<template>
  <div class="gas-container">
    <div class="title">
      {{ dataObj.title }}
      <el-tooltip class="item" effect="dark" placement="top-start">
        <div slot="content">{{ dataObj.tooltip }}</div>
        <img
          style="width: 16px; height: 16px; margin-left: 8px"
          src="@/assets/question.png"
          alt=""
        >
      </el-tooltip>
    </div>
    <div class="middle">
      <div class="Bgbox">
        <div class="fill">
          <div
            :style="{ height: dataObj.fillHeight, background: dataObj.color }"
          />
        </div>
        <div class="iconBg" />
      </div>
      <div v-if="dataObj.showTotal" class="middleText">
        剩余
        <span :style="{ color: dataObj.color }">{{
          dataObj.residue.value
        }}</span> {{ dataObj.unit }}
        <span :style="{ color: dataObj.color }">{{
          dataObj.residue.percentage
        }}</span>
      </div>
    </div>

    <!-- 龙建科工 暂时隐藏 -->
    <!-- <el-row :gutter="20" class="customRow">
      <el-col v-for="(item, index) in dataObj.colData" :key="index" :span="8">
        <div class="bottomContainer">
          <img class="arrow" :src="gas" alt="">
          <div class="down">
            <img
              :src="item.iconName == 'delivery' ? delivery : workshop"
              alt=""
            >
            <div class="textData">
              <h2>{{ item.Key }}</h2>
              <p :style="{ color: dataObj.color }">
                <span style="width: 70px">{{ item.Percent }} %</span><span
                  style="flex: 1"
                >{{ item.Value }} {{ dataObj.unit }}</span>
              </p>
            </div>
          </div>
        </div>
      </el-col>
    </el-row> -->
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    customGasUsedConfig: {
      type: Object,
      default: () => {}
    },
    isPhotovoltaic: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      workshop: require('@/assets/workshop.png'),
      delivery: require('@/assets/delivery.png'),
      gas: require('@/assets/Business/gas.png')
    }
  },
  computed: {
    dataObj() {
      const data = {
        ...this.customGasUsedConfig.baseData,
        ...this.customGasUsedConfig.gasData
      }
      return data
    }
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>
 <style scoped lang='scss'>
.gas-container {
  width: 100%;
  height: 363px;
  padding: 16px 24px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  background: #fff;
  position: relative; // 添加相对定位
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
  }
  .middle {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px;
    height: 133px;
    background: linear-gradient(
      90deg,
      rgba(41, 141, 255, 0.05) 0%,
      rgba(41, 141, 255, 0) 100%
    );
    position: absolute; // 改为绝对定位
    top: 50%; // 定位到垂直中点
    left: 0; // 左侧对齐
    right: 0; // 右侧对齐
    transform: translateY(-50%); // 向上移动自身高度的一半
    margin-top: 24px; // 补偿title的高度
    .Bgbox {
      width: 48px;
      height: 110px;
      margin-right: 24px;
      position: relative;
      .iconBg {
        width: 48px;
        height: 110px;
        background: url("../../../../../../assets/Business/box.png");
        background-size: cover;
        position: absolute;
        top: 2px;
        left: 0;
      }
      .fill {
        position: absolute;
        width: 40px;
        height: 77px;
        bottom: 12px;
        left: 4px;
        overflow: hidden;
        div {
          width: 40px;
          position: absolute;
          left: 0;
          bottom: 0;
        }
      }
    }
    .middleText {
      font-size: 24px;
      color: #333;
      span {
        font-weight: bold;
        margin: 0 16px;
      }
    }
  }
  .customRow {
    display: flex;
    flex-direction: row;
    justify-content: center;
    .bottomContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
      .arrow {
        height: 72px;
        width: 28px;
      }
      .down {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 83px;
        background: linear-gradient(
          90deg,
          rgba(41, 141, 255, 0.05) 0%,
          rgba(41, 141, 255, 0) 100%
        );
        img {
          width: 24px;
          height: 24px;
          margin-right: 16px;
        }
        .textData {
          h2 {
            color: #333;
            font-size: 16px;
            font-weight: 400;
            margin-bottom: 16px;
          }
          p {
            display: flex;
          }
          span {
            display: block;
            font-weight: bold;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>
