{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\maintenanceAndUpkeep\\workOrderManagement\\components\\device.vue?vue&type=style&index=0&id=5da2298c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\maintenanceAndUpkeep\\workOrderManagement\\components\\device.vue", "mtime": 1755674552428}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KQGltcG9ydCAiQC92aWV3cy9idXNpbmVzcy92ZWhpY2xlQmFycmllci9pbmRleC5zY3NzIjsNCi50b29sYm94IHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiA1cHg7DQogIDo6di1kZWVwIC5lbC1mb3JtLWl0ZW0gew0KICAgIG1hcmdpbi1ib3R0b206IDBweDsNCiAgfQ0KfQ0KLnR5cGVsaW5lIHsNCiAgOjp2LWRlZXAgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgew0KICAgIGJvcmRlci1yYWRpdXM6IDJweDsNCiAgfQ0KICA6OnYtZGVlcCAuaXMtYWN0aXZlIHsNCiAgICAuZWwtcmFkaW8tYnV0dG9uX19pbm5lciB7DQogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmOw0KICAgICAgY29sb3I6ICMyOThkZmY7DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["device.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAktBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "device.vue", "sourceRoot": "src/views/business/maintenanceAndUpkeep/workOrderManagement/components", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <div class=\"toolbox\">\r\n          <!-- <div>\r\n              <el-button @click=\"openDialog('add')\" type=\"primary\">新 增</el-button>\r\n            </div> -->\r\n          <div>\r\n            <el-form inline>\r\n              <el-form-item label=\"工单号:\" style=\"margin-bottom: 10px\">\r\n                <el-input\r\n                  v-model=\"query.Order_Code\"\r\n                  clearable\r\n                  style=\"width: 150px\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"工单名称:\" style=\"margin-bottom: 10px\">\r\n                <el-input\r\n                  v-model=\"query.Order_Name\"\r\n                  clearable\r\n                  style=\"width: 150px\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"发起时间:\" style=\"margin-bottom: 10px\">\r\n                <el-date-picker\r\n                  v-model=\"query.Date\"\r\n                  align=\"right\"\r\n                  type=\"daterange\"\r\n                  placeholder=\"选择日期\"\r\n                  style=\"width: 300px\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  :picker-options=\"pickerOptions\"\r\n                  @change=\"changeDate\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"工单状态:\" style=\"margin-bottom: 10px\">\r\n                <el-select\r\n                  v-model=\"query.State\"\r\n                  clearable\r\n                  filterable\r\n                  style=\"width: 120px\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in stateList\"\r\n                    :key=\"item.code\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.code\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n              <!-- <el-form-item label=\"工单类型:\" style=\"margin-bottom: 10px\">\r\n                  <el-select\r\n                    v-model=\"query.WorkOrder_Setup_Id\"\r\n                    clearable\r\n                    filterable\r\n                    style=\"width: 120px\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in workTypeList\"\r\n                      :key=\"item.Value\"\r\n                      :label=\"item.Display_Name\"\r\n                      :value=\"item.Value\"\r\n                    />\r\n                  </el-select>\r\n                </el-form-item> -->\r\n              <el-form-item label=\"维修人:\" style=\"margin-bottom: 10px\">\r\n                <el-select\r\n                  v-model=\"query.Maintain_Person\"\r\n                  clearable\r\n                  filterable\r\n                  style=\"width: 150px\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in personList\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item label=\"设备查询:\" style=\"margin-bottom: 10px\">\r\n                <el-select\r\n                  v-model=\"query.EquipId\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请输入设备\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in equipOptions\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"item.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-button @click=\"reset()\">重 置</el-button>\r\n              <el-button type=\"primary\" @click=\"searchForm()\">查 询</el-button>\r\n            </el-form>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <div class=\"toolbox\">\r\n          <div>\r\n            <el-radio-group\r\n              v-model=\"query.WorkOrder_State\"\r\n              class=\"typeline\"\r\n              @input=\"searchForm\"\r\n            >\r\n              <el-radio-button :label=\"null\">全部</el-radio-button>\r\n              <el-radio-button :label=\"0\">待处理</el-radio-button>\r\n              <el-radio-button :label=\"1\">处理中</el-radio-button>\r\n              <el-radio-button :label=\"2\">已处理</el-radio-button>\r\n            </el-radio-group>\r\n          </div>\r\n          <div>\r\n            <el-button\r\n              type=\"primary\"\r\n              @click=\"openDialog('add')\"\r\n            >新 增</el-button>\r\n          </div>\r\n        </div>\r\n        <CustomTable\r\n          style=\"height: calc(100vh - 350px)\"\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        >\r\n          <template #customBtn=\"{ slotScope }\">\r\n            <template v-if=\"slotScope.State === '0' && slotScope.Is_Anth\">\r\n              <el-button\r\n                v-if=\"getBtnAuth('dispatch')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('dispatch', slotScope, slotScope.Order_Type)\"\r\n              >派工</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('dispatch-myorder')\"\r\n                type=\"text\"\r\n                code=\"dispatch-myorder\"\r\n                @click=\"openDialog('dispatch', slotScope, slotScope.Order_Type)\"\r\n              >派工</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('receive')\"\r\n                type=\"text\"\r\n                @click=\"receivingOrders(slotScope)\"\r\n              >接单</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('receive-myorder')\"\r\n                type=\"text\"\r\n                code=\"receive-myorder\"\r\n                @click=\"receivingOrders(slotScope)\"\r\n              >接单</el-button>\r\n            </template>\r\n            <el-button\r\n              v-if=\"getBtnAuth('detail')\"\r\n              type=\"text\"\r\n              @click=\"openDialog('detail', slotScope, slotScope.Order_Type)\"\r\n            >查看详情</el-button>\r\n            <el-button\r\n              v-if=\"getBtnAuth('detail-myorder')\"\r\n              type=\"text\"\r\n              @click=\"openDialog('detail', slotScope, slotScope.Order_Type)\"\r\n            >查看详情</el-button>\r\n            <template\r\n              v-if=\"\r\n                slotScope.State === '1' &&\r\n                  slotScope.Maintain_Person_Id === userId &&\r\n                  slotScope.Order_Type === 'jsbx'\r\n              \"\r\n            >\r\n              <el-button\r\n                v-if=\"getBtnAuth('handle')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('handle', slotScope, slotScope.Order_Type)\"\r\n              >工单处理</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('handle-myorder')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('handle', slotScope, slotScope.Order_Type)\"\r\n              >工单处理</el-button>\r\n            </template>\r\n            <template\r\n              v-if=\"\r\n                slotScope.State === '2' &&\r\n                  slotScope.Is_Anth &&\r\n                  slotScope.Order_Type === 'jsbx'\r\n              \"\r\n            >\r\n              <el-button\r\n                v-if=\"getBtnAuth('recheck')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('recheck', slotScope, slotScope.Order_Type)\"\r\n              >工单复检</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('recheck-myorder')\"\r\n                type=\"text\"\r\n                @click=\"openDialog('recheck', slotScope, slotScope.Order_Type)\"\r\n              >工单复检</el-button>\r\n            </template>\r\n            <template\r\n              v-if=\"\r\n                slotScope.State === '3' &&\r\n                  slotScope.Order_Type === 'jsbx' &&\r\n                  slotScope.Create_UserId === userId\r\n              \"\r\n            >\r\n              <el-button\r\n                v-if=\"getBtnAuth('rate')\"\r\n                type=\"text\"\r\n                @click=\"openCloseRate(slotScope, 'rate')\"\r\n              >工单评价</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('rate-myorder')\"\r\n                type=\"text\"\r\n                @click=\"openCloseRate(slotScope, 'rate')\"\r\n              >工单评价</el-button>\r\n            </template>\r\n            <template v-if=\"slotScope.State === '0' || slotScope.State === '1'\">\r\n              <el-button\r\n                v-if=\"getBtnAuth('close')\"\r\n                type=\"text\"\r\n                @click=\"openCloseRate(slotScope, 'close')\"\r\n              >关闭</el-button>\r\n              <el-button\r\n                v-if=\"getBtnAuth('close-myorder')\"\r\n                type=\"text\"\r\n                @click=\"openCloseRate(slotScope, 'close')\"\r\n              >关闭</el-button>\r\n            </template>\r\n          </template>\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n    <editDialog ref=\"editDialog\" @refresh=\"fetchData\" />\r\n    <closeRateDialog ref=\"closeRateDialog\" @refresh=\"fetchData\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\n// import AuthButtons from \"@/mixins/auth-buttons\";\r\nimport editDialog from '../editDialog.vue'\r\nimport closeRateDialog from '../closeRateDialog.vue'\r\nimport {\r\n  GetWorkOrderManageList,\r\n  GetWorkOrderType,\r\n  GetPersonList,\r\n  DeleteCoatingRequir,\r\n  SendWorkOrderPerson,\r\n  GetEquipDropList\r\n} from '@/api/business/maintenanceAndUpkeep.js'\r\n\r\nexport default {\r\n  Name: '',\r\n  components: {\r\n    CustomTable,\r\n    CustomLayout,\r\n    editDialog,\r\n    closeRateDialog\r\n  },\r\n  props: {\r\n    flag: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    personList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    equipOptions: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    authButtons: {\r\n      type: Object,\r\n      default: () => {}\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      userId: '',\r\n      query: {\r\n        Date: [],\r\n        Order_Code: '',\r\n        Order_Name: '',\r\n        Create_Date: '',\r\n        Create_EDate: '',\r\n        State: '',\r\n        WorkOrder_Setup_Id: 'sbwb',\r\n        Maintain_Person: '',\r\n        WorkOrder_State: null,\r\n        Type: 1\r\n      },\r\n      type: '',\r\n      pickerOptions: {\r\n        shortcuts: [\r\n          {\r\n            text: '今天',\r\n            onClick(picker) {\r\n              picker.$emit('pick', [new Date(), new Date()])\r\n            }\r\n          },\r\n          {\r\n            text: '近7天',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '近30天',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '本月',\r\n            onClick(picker) {\r\n              const today = new Date()\r\n              const end = new Date(\r\n                today.getFullYear(),\r\n                today.getMonth() + 1,\r\n                0\r\n              )\r\n              const start = new Date(today.getFullYear(), today.getMonth(), 1)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      stateList: [\r\n        {\r\n          name: '待处理',\r\n          code: 0\r\n        },\r\n        {\r\n          name: '处理中',\r\n          code: 1\r\n        },\r\n        {\r\n          name: '待复检',\r\n          code: 2\r\n        },\r\n        {\r\n          name: '待评价',\r\n          code: 3\r\n        },\r\n        {\r\n          name: '处理完成',\r\n          code: 4\r\n        },\r\n        {\r\n          name: '已关闭',\r\n          code: 5\r\n        }\r\n      ],\r\n      workTypeList: [\r\n        // {\r\n        //   Display_Name: \"已关闭\",\r\n        //   Value: 5,\r\n        // },\r\n      ],\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        },\r\n        closeAndFresh: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n      selectIds: [],\r\n      customTableConfig: {\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            label: '序号',\r\n            otherOptions: {\r\n              type: 'index',\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '发起时间',\r\n            key: 'Create_Date'\r\n          },\r\n          {\r\n            label: '工单名称',\r\n            key: 'Order_Name'\r\n          },\r\n          {\r\n            label: '工单类型',\r\n            key: 'Order_Type',\r\n            render: (row) => {\r\n              return this.$createElement(\r\n                'span',\r\n                {},\r\n                row.Order_Type === null\r\n                  ? '-'\r\n                  : row.Order_Type === 'jsbx'\r\n                    ? '即时报修'\r\n                    : '设备维保'\r\n              )\r\n            }\r\n          },\r\n          {\r\n            label: '工单号',\r\n            key: 'Order_Code'\r\n          },\r\n          {\r\n            label: '开始处理时间',\r\n            key: 'Start_Time'\r\n          },\r\n          {\r\n            label: '处理完成时间',\r\n            key: 'End_Time'\r\n          },\r\n          {\r\n            label: '处理用时',\r\n            key: 'Time'\r\n          },\r\n          {\r\n            label: '报修部门',\r\n            key: 'Depart_Name'\r\n          },\r\n          {\r\n            label: '报修方',\r\n            key: 'Warranty_Person'\r\n          },\r\n          {\r\n            label: '维修人',\r\n            key: 'Maintain_Person'\r\n          },\r\n          {\r\n            label: '工单状态',\r\n            key: 'State',\r\n            render: (row) => {\r\n              return this.$createElement(\r\n                'span',\r\n                {\r\n                  style: {\r\n                    color:\r\n                      row.State === '0'\r\n                        ? '#FF5E7C'\r\n                        : row.State === '1'\r\n                          ? '#298DFF'\r\n                          : row.State === '2'\r\n                            ? '#FF902C'\r\n                            : row.State === '3'\r\n                              ? '#298DFF'\r\n                              : row.State === '4'\r\n                                ? '#00D3A7'\r\n                                : '#333333'\r\n                  }\r\n                },\r\n                row.State === '0'\r\n                  ? '待处理'\r\n                  : row.State === '1'\r\n                    ? '处理中'\r\n                    : row.State === '2'\r\n                      ? '待复检'\r\n                      : row.State === '3'\r\n                        ? '待评价'\r\n                        : row.State === '4'\r\n                          ? '处理完成'\r\n                          : '已关闭'\r\n              )\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActionsWidth: 220,\r\n        tableActions: [\r\n          {\r\n            actionLabel: '',\r\n            otherOptions: {\r\n              type: 'text'\r\n            }\r\n          }\r\n        ],\r\n        buttonConfig: {\r\n          buttonList: []\r\n        },\r\n        operateOptions: {\r\n          width: 300 // 操作栏宽度\r\n        }\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  // mixins: [AuthButtons],\r\n  watch: {\r\n    //   'AuthButtons.buttons':{\r\n    //     handler(val,oldval){\r\n    //       console.log('dddss',val,oldval);\r\n    //         this.show=true\r\n    //       }\r\n\r\n    //     }\r\n    //   }\r\n    flag: {\r\n      handler(val) {\r\n        console.log('dddss', val)\r\n        this.initData()\r\n      }\r\n    }\r\n  },\r\n  created() {},\r\n  mounted() {\r\n    // 跳转设置默认参数\r\n    // let JumpParams = this.$qiankun.getMicroAppJumpParamsFn();\r\n    // console.log(JumpParams.Create_Date, \"跳转参数-----------------------\");\r\n    // if (JumpParams.isJump == \"true\") {\r\n    //   this.query.State = Number(JumpParams.State);\r\n    //   // this.query.Create_Date = JumpParams.Create_Date;\r\n    //   // this.query.Create_EDate = JumpParams.Create_EDate;\r\n    //   // this.query.Date = [JumpParams.Create_Date, JumpParams.Create_EDate];\r\n    // }\r\n    this.initData()\r\n  },\r\n  beforeDestroy() {\r\n    this.$qiankun.setMicroAppJumpParamsFn()\r\n    this.query.State = null\r\n    // this.query.Create_Date = null;\r\n    // this.query.Create_EDate = null;\r\n    // this.query.Date = [];\r\n  },\r\n  methods: {\r\n    async initData() {\r\n      // let res = await GetWorkOrderType({ Code: \"WorkOrderType\" });\r\n      // console.log(res, \"12121212\");\r\n      // if (res.IsSucceed) {\r\n      //   this.workTypeList = res.Data;\r\n      // }\r\n\r\n      this.userId = localStorage.getItem('UserId')\r\n      if (this.$route.query.type === 'my') {\r\n        this.query.type = 0\r\n      } else {\r\n        this.query.type = 1\r\n      }\r\n      await this.init()\r\n    },\r\n    openAdd() {\r\n      this.dialogTitle = '新增'\r\n      this.dialogVisible = true\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.init(0, {}, 'add')\r\n      })\r\n    },\r\n    searchForm() {\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    reset() {\r\n      this.query = {\r\n        Date: [],\r\n        Order_Code: '',\r\n        Order_Name: '',\r\n        Create_Date: '',\r\n        Create_EDate: '',\r\n        State: '',\r\n        WorkOrder_Setup_Id: 'sbwb',\r\n        Maintain_Person: '',\r\n        WorkOrder_State: this.query.WorkOrder_State\r\n      }\r\n      if (this.$route.query.type === 'my') {\r\n        this.query.type = 0\r\n      } else {\r\n        this.query.type = 1\r\n      }\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.fetchData()\r\n    },\r\n    init() {\r\n      this.fetchData()\r\n    },\r\n    getTypeList() {\r\n      console.log('res.Datares.Datares.Datares.Data-------------------')\r\n      // GetWorkOrderType({ Code: \"WorkOrderType\" }).then((res) => {\r\n      //   console.log(\r\n      //     res.Data,\r\n      //     \"res.Datares.Datares.Datares.Data-------------------\"\r\n      //   );\r\n      //   if (res.IsSucceed) {\r\n      //     this.typeList = res.Data;\r\n      //   }\r\n      // });\r\n    },\r\n    async fetchData() {\r\n      const res = await GetWorkOrderManageList({\r\n        model: this.query,\r\n        pageInfo: {\r\n          Page: this.customTableConfig.currentPage,\r\n          PageSize: this.customTableConfig.pageSize,\r\n          SortName: 'Create_Date',\r\n          SortOrder: 'DESC'\r\n        }\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.dialogVisible = true\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.init(0, {}, 'dispatch')\r\n      })\r\n    },\r\n    handleDelete(index, row) {\r\n      this.$confirm('请确认，是否删除该数据?', {\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeleteCoatingRequir({ Id: row.Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n              this.init()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch((_) => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    // 打开新增编辑弹窗\r\n    async openDialog(type, row, orderType) {\r\n      const res = await GetWorkOrderType({ Code: 'WorkOrderType' })\r\n      if (res.IsSucceed) {\r\n        this.workTypeList = res.Data\r\n      }\r\n      this.$refs.editDialog.handleOpen(type, row, orderType, this.workTypeList)\r\n    },\r\n    // 打开关闭工单弹窗或评价弹窗\r\n    openCloseRate(row, type) {\r\n      this.$refs.closeRateDialog.handleOpen(type, row)\r\n    },\r\n    // 接单\r\n    receivingOrders(row) {\r\n      SendWorkOrderPerson({ Id: row.Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.fetchData()\r\n          this.$message.success('接单成功')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.onFresh()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.onFresh()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      const Ids = []\r\n      this.tableSelection = selection\r\n      this.tableSelection.forEach((item) => {\r\n        Ids.push(item.Id)\r\n      })\r\n      console.log(Ids)\r\n      this.selectIds = Ids\r\n      console.log(this.tableSelection)\r\n    },\r\n\r\n    changeDate() {\r\n      this.query.Create_Date = this.query.Date ? this.query.Date[0] : null\r\n      this.query.Create_EDate = this.query.Date ? this.query.Date[1] : null\r\n    },\r\n    getBtnAuth(code) {\r\n      // console.log(code,this.AuthButtons,this.AuthButtons.buttons.find(item=>item.Code===code));\r\n      return this.authButtons.buttons.find((item) => item.Code === code)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n  <style lang=\"scss\" scoped>\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n.toolbox {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 5px;\r\n  ::v-deep .el-form-item {\r\n    margin-bottom: 0px;\r\n  }\r\n}\r\n.typeline {\r\n  ::v-deep .el-radio-button__inner {\r\n    border-radius: 2px;\r\n  }\r\n  ::v-deep .is-active {\r\n    .el-radio-button__inner {\r\n      background-color: #ffffff;\r\n      color: #298dff;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}