<template>
  <div class="app-container abs100">
    <CustomLayout
      :layoutConfig="{
        isShowLayoutChart: true,
      }"
    >
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutChart>
        <div class="progressBox">
          <div class="progressTwo">
            <div class="left">
              <span class="title">当前在园车辆总数</span>
              <div class="info" style="height: 60.5px">
                <span class="colorActive textActive">{{
                  inParkStatistics.Total
                }}</span>
              </div>
            </div>
          </div>
          <div class="progressTwo">
            <div class="left">
              <span class="title">访问类型统计</span>
              <div class="info">
                <span class="num">
                  <span
                    :class="[index == 0 ? 'colorActive textActive' : '']"
                    v-for="(item, index) in inParkStatistics.Access"
                    :key="index"
                    >{{ index == 0 ? "" : "/" }} {{ item.Value }}</span
                  >
                </span>
                <span class="text">
                  <span
                    :class="[index == 0 ? 'colorActive' : '']"
                    v-for="(item, index) in inParkStatistics.Access"
                    :key="index"
                    >{{ index == 0 ? "" : "/" }} {{ item.Key }}</span
                  >
                </span>
              </div>
            </div>
            <el-progress
              class="right"
              type="circle"
              :stroke-width="20"
              :percentage="AccessPrecent.toFixed(2)"
            ></el-progress>
          </div>
          <div class="progressTwo">
            <div class="left">
              <span class="title">入园超时统计</span>
              <div class="info">
                <span class="num">
                  <span
                    :class="[index == 0 ? 'colorActive textActive' : '']"
                    v-for="(item, index) in inParkStatistics.Timeout"
                    :key="index"
                    >{{ index == 0 ? "" : "/" }} {{ item.Value }}</span
                  >
                </span>
                <span class="text">
                  <span
                    :class="[index == 0 ? 'colorActive' : '']"
                    v-for="(item, index) in inParkStatistics.Timeout"
                    :key="index"
                    >{{ index == 0 ? "" : "/" }} {{ item.Key }}</span
                  >
                </span>
              </div>
            </div>
            <el-progress
              class="right"
              type="circle"
              :stroke-width="20"
              :percentage="TimeoutPrecent.toFixed(2)"
            ></el-progress>
          </div>
          <div class="progressTwo">
            <div class="left">
              <span class="title">车辆类型统计</span>
              <div class="info">
                <span
                  style="color: #298dff; margin-bottom: 10px"
                  v-for="(item, index) in inParkStatistics.VehiclesType"
                  :key="index"
                  >{{ item.name }}: {{ item.value }}
                </span>
              </div>
            </div>
            <v-chart
              ref="pieChartRef"
              class="pieChartDom"
              :option="pieOptionOption"
              :autoresize="true"
            />
          </div>
        </div>
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";
import {
  VBInParkVehiclesGetInParkVehiclesList,
  VBInParkVehiclesGetInParkStatistics,
  VBInParkVehiclesVehicleLeaving,
  VBInParkVehiclesExportData,
  VBPassRecordGetDropList,
} from "@/api/business/vehicleBarrier.js";
import exportInfo from "@/views/business/vehicleBarrier/mixins/export.js";
import addRouterPage from "@/mixins/add-router-page";

import VChart from "vue-echarts";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { PieChart } from "echarts/charts";
import {
  GridComponent,
  LegendComponent,
  TooltipComponent,
  TitleComponent,
  DataZoomComponent,
} from "echarts/components";
use([
  CanvasRenderer,
  PieChart,
  DataZoomComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
]);

export default {
  Name: "vehiclePeerRecord",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
    VChart,
  },
  mixins: [exportInfo, addRouterPage],
  data() {
    return {
      pieOptionOption: {
        tooltip: {
          trigger: "item",
        },
        legend: {
          show: false,
        },
        series: [
          {
            name: "车辆类型统计",
            type: "pie",
            radius: ["60%", "90%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            labelLine: {
              show: false,
            },
            data: [],
            // color:['#298DFF']
          },
        ],
      },
      ruleForm: {
        Number: "",
        Date: [],
        StartTime: null,
        EndTime: null,
        AccessType: "",
        UserName: "",
        VehicleType: "",
        Timeout: "",
      },

      tableSelection: [],

      customForm: {
        formItems: [
          {
            key: "Number", // 字段ID
            label: "车牌号码", // Form的label
            type: "input", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
            },
            input: (e) => {},
            change: () => {},
          },
          {
            key: "Date",
            label: "通行时间",
            type: "datePicker",
            otherOptions: {
              type: "datetimerange",
              rangeSeparator: "至",
              startPlaceholder: "开始日期",
              endPlaceholder: "结束日期",
              clearable: true,
              valueFormat: "yyyy-MM-dd HH:mm",
            },
            change: (e) => {
              if (e && e.length > 0) {
                this.ruleForm.StartTime = e[0];
                this.ruleForm.EndTime = e[1];
              } else {
                this.ruleForm.StartTime = null;
                this.ruleForm.EndTime = null;
              }
            },
          },
          {
            key: "VehicleType",
            label: "车辆类型",
            type: "select",
            options: [],
            otherOptions: {
              clearable: true,
            },
            change: (e) => {},
          },
          {
            key: "UserName",
            label: "车主姓名",
            type: "input",
            otherOptions: {
              clearable: true,
            },
            input: (e) => {},
            change: () => {},
          },
          {
            key: "AccessType",
            label: "访问类型",
            type: "select",
            options: [],
            otherOptions: {
              clearable: true,
            },
            change: (e) => {},
          },
          {
            key: "Timeout",
            label: "是否超时",
            type: "select",
            options: [
              {
                label: "是",
                value: true,
              },
              {
                label: "否",
                value: false,
              },
            ],
            otherOptions: {
              clearable: true,
            },
            change: (e) => {},
          },
        ],
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              key: "batch",
              disabled: false, // 是否禁用
              text: "批量导出",
              onclick: (item) => {
                console.log(item);
                this.ExportData(
                  this.ruleForm,
                  "在园车俩",
                  VBInParkVehiclesExportData
                );
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        height: "100%",
        tableColumns: [
          // {
          //   width: 50,
          //   otherOptions: {
          //     type: 'selection',
          //     align: 'center'
          //   }
          // },
          {
            label: "驶入时间",
            key: "PassTime",
            otherOptions: {
              fixed: "left",
            },
          },
          {
            label: "车牌号码",
            key: "Number",
            otherOptions: {
              fixed: "left",
            },
          },
          {
            label: "车辆类型",
            key: "VehicleType",
          },
          {
            label: "车主姓名",
            key: "UserName",
          },
          {
            label: "车主联系方式",
            key: "UserPhone",
          },
          {
            label: "访问类型",
            key: "AccessType",
          },
          {
            label: "出入口",
            key: "EntranceName",
          },
          {
            label: "入园时长",
            key: "ParkTime",
          },
          {
            label: "超时时长",
            key: "Timeout",
          },
        ],
        tableData: [],
        tableActionsWidth: 120,
        tableActions: [
          {
            actionLabel: "查看",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.$router.push({
                name: "vehiclesInTheParkView",
                query: { pg_redirect: this.$route.name, Id: row.Id },
              });
            },
          },
          {
            actionLabel: "车辆出园",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleLeave(row);
            },
          },
        ],
      },
      addPageArray: [
        {
          path: this.$route.path + "/view",
          hidden: true,
          component: () => import("./dialog/view.vue"),
          meta: { title: "在园车辆详情" },
          name: "vehiclesInTheParkView",
        },
      ],
      inParkStatistics: {},

      AccessPrecent: 0,
      TimeoutPrecent: 0,
    };
  },
  created() {
    this.vBPassRecordGetDropList();
    // this.init();
    // this.vBInParkVehiclesGetInParkStatistics();
  },
  async mounted() {
    // 跳转设置默认参数
    let JumpParams = this.$qiankun.getMicroAppJumpParamsFn();
    if (JumpParams.isJump == "true") {
      this.ruleForm.Timeout = JumpParams.Timeout == "true";
    }
    this.onFresh();
  },
  beforeDestroy() {
    this.$qiankun.setMicroAppJumpParamsFn();
    this.ruleForm.Timeout = null;
  },
  methods: {
    async vBInParkVehiclesGetInParkStatistics() {
      if (this.ruleForm.Date.length == 0) {
        this.ruleForm.StartTime = null;
        this.ruleForm.EndTime = null;
      }
      let res = await VBInParkVehiclesGetInParkStatistics({
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.inParkStatistics = res.Data;
        let AccessTotal = res.Data.Access.reduce(
          (accumulator, currentValue) =>
            accumulator + parseInt(currentValue.Value),
          0
        );
        let TimeoutTotal = res.Data.Timeout.reduce(
          (accumulator, currentValue) =>
            accumulator + parseInt(currentValue.Value),
          0
        );
        // let VehiclesTypeTotal = res.Data.VehiclesType.map(
        //   (obj) => obj.value
        // ).reduce(
        //   (accumulator, currentValue) => accumulator + currentValue.Value,
        //   0
        // );
        this.inParkStatistics.Access = res.Data.Access;
        this.inParkStatistics.Timeout = res.Data.Timeout;
        this.inParkStatistics.VehiclesType = res.Data.VehiclesType.map(
          (item) => ({
            value: item.Value,
            name: item.Key,
          })
        );
        this.pieOptionOption.series[0].data =
          this.inParkStatistics.VehiclesType;

        this.AccessPrecent =
          (Number(res.Data.Access[0].Value) / AccessTotal) * 100;
        this.TimeoutPrecent =
          (Number(res.Data.Timeout[0].Value) / TimeoutTotal) * 100;
        if (AccessTotal == 0) {
          this.AccessPrecent = 0;
        }
        if (TimeoutTotal == 0) {
          this.TimeoutPrecent = 0;
        }
      }
    },
    async vBPassRecordGetDropList() {
      let res = await VBPassRecordGetDropList({});
      if (res.IsSucceed) {
        this.customForm.formItems.find((v) => v.key == "VehicleType").options =
          res.Data.find((v) => v.Name == "VehicleType").List.map((item) => ({
            label: item.Key,
            value: item.Value,
          }));
        this.customForm.formItems.find((v) => v.key == "AccessType").options =
          res.Data.find((v) => v.Name == "AccessType").List.map((item) => ({
            label: item.Key,
            value: item.Value,
          }));
      }
    },
    searchForm(data) {
      this.customTableConfig.currentPage = 1;
      console.log(data);
      this.onFresh();
    },
    resetForm() {
      this.onFresh();
    },
    onFresh() {
      this.fetchData();
      this.vBInParkVehiclesGetInParkStatistics();
    },
    async init() {
      await this.fetchData();
    },
    async fetchData() {
      if (this.ruleForm.Date.length == 0) {
        this.ruleForm.StartTime = null;
        this.ruleForm.EndTime = null;
      }
      const res = await VBInParkVehiclesGetInParkVehiclesList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data;
        this.customTableConfig.total = res.Data.Total;
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.onFresh();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.onFresh();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
    handleLeave(row) {
      this.$confirm(
        '<div><div>是否确认进行车辆出园</div><div style="font-size:12px;color:#aaa">车辆出园操作将标识该车为出园状态</div></div>',
        "操作确认",
        {
          type: "warning",
          dangerouslyUseHTMLString: true,
        }
      )
        .then(async (_) => {
          const res = await VBInParkVehiclesVehicleLeaving({
            Id: row.Id,
          });
          if (res.IsSucceed) {
            this.$message.success("操作成功");
            this.init();
          } else {
            this.$message.error(res.Message);
          }
        })
        .catch((_) => {});
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/views/business/vehicleBarrier/index.scss";

.imgwapper {
  width: 100px;
  height: 100px;
}
.empty-img {
  text-align: center;
}
.progressBox {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 20px 0px;
  .pieChartDom {
    width: 126px;
    height: 126px;
  }
  .progressTwo {
    height: 126px;
    display: flex;
    flex-direction: row;
    .left {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 600;
      }
      .info {
        display: flex;
        flex-direction: column;
        .num {
          color: #aaa;
          margin-bottom: 10px;
        }
        .text {
          color: #aaa;
        }
        .colorActive {
          color: #298dff;
        }
        .textActive {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
