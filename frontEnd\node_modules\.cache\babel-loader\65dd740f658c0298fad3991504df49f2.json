{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlPersonnelManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlPersonnelManagement\\index.vue", "mtime": 1755674552408}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "DialogForm", "DialogFormLook", "DialogFormImport", "downloadFile", "GetPersonnelList", "SubPersonnel", "EntrancePersonnelInfo", "UpdateStatus", "DelPersonnel", "PersonnelImportTemplate", "EntrancePersonnelImport", "ExportEntrancePersonnel", "GetRole", "GetDepartment", "GetCompany", "GetDictionaryDetailListByCode", "GetOssUrl", "name", "components", "data", "_this", "currentComponent", "componentsConfig", "componentsFuns", "open", "dialogVisible", "close", "onFresh", "dialogTitle", "tableSelection", "ruleForm", "P_Name", "Contact_Way", "P_Type", "Position_Name", "P_Unit", "P_Department", "customForm", "formItems", "key", "label", "type", "otherOptions", "clearable", "width", "change", "e", "console", "log", "options", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "text", "round", "plain", "circle", "loading", "disabled", "icon", "autofocus", "size", "onclick", "item", "handleCreate", "handleTemDownload", "handleImport", "handleExport", "pageSizeOptions", "currentPage", "pageSize", "total", "operateOptions", "tableColumns", "align", "render", "row", "Face_Picture_Url", "$createElement", "style", "height", "attrs", "fit", "src", "previewSrcList", "Status", "tableData", "tableActions", "actionLabel", "index", "handleEdit", "handleDelete", "computed", "created", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "initGetRole", "find", "sent", "initGetDepartment", "initGetCompany", "initDeviceType", "init", "stop", "methods", "code", "_callee2", "res", "_callee2$", "_context2", "dictionaryCode", "Data", "map", "Display_Name", "value", "Value", "abrupt", "_callee3", "_callee3$", "_context3", "_callee4", "_callee4$", "_context4", "_callee5", "_callee5$", "_context5", "searchForm", "resetForm", "getPersonnelList", "_this3", "_callee6", "_callee6$", "_context6", "_objectSpread", "Parameter<PERSON>son", "Key", "Type", "Filter_Type", "Page", "PageSize", "Search", "SortName", "SortOrder", "P_Workshop", "IsSucceed", "TotalCount", "handelImage", "$message", "message", "Message", "_this4", "_callee8", "promises", "_callee8$", "_context8", "_ref", "_callee7", "v", "imageRes", "_callee7$", "_context7", "Face_Picture", "url", "Face_PictureUrl", "_x", "apply", "arguments", "Promise", "all", "then", "catch", "error", "title", "handleEnable", "_this5", "_callee10", "textStatus", "_callee10$", "_context10", "$confirm", "concat", "_ref2", "_callee9", "_", "_callee9$", "_context9", "id", "Id", "status", "_x2", "_this6", "_ref3", "_callee11", "_callee11$", "_context11", "_x3", "ID", "_this7", "_callee12", "_callee12$", "_context12", "_this8", "_callee13", "_callee13$", "_context13", "length", "warning", "join", "_this9", "_callee14", "_callee14$", "_context14", "handleAllExport", "_this10", "_callee15", "_callee15$", "_context15", "Content", "EqtType", "Position", "IsAll", "Ids", "handleSizeChange", "val", "handleCurrentChange", "handleSelectionChange", "selection"], "sources": ["src/views/business/accessControl/accessControlPersonnelManagement/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        >\r\n          <template #customBtn=\"{ slotScope }\">\r\n            <el-button type=\"text\" @click=\"handleEnable(slotScope)\">{{\r\n              slotScope.Status == \"0\" ? \"禁用\" : \"启用\"\r\n            }}</el-button></template>\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport DialogForm from './dialogForm.vue'\r\nimport DialogFormLook from './dialogFormLook.vue'\r\nimport DialogFormImport from './dialogFormImport.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport {\r\n  GetPersonnelList,\r\n  SubPersonnel,\r\n  EntrancePersonnelInfo,\r\n  UpdateStatus,\r\n  DelPersonnel,\r\n  PersonnelImportTemplate,\r\n  EntrancePersonnelImport,\r\n  ExportEntrancePersonnel,\r\n  GetRole,\r\n  GetDepartment,\r\n  GetCompany,\r\n  GetDictionaryDetailListByCode\r\n} from '@/api/business/accessControl'\r\nimport { GetOssUrl } from '@/api/sys/index'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        P_Name: '',\r\n        Contact_Way: '',\r\n        P_Type: '',\r\n        Position_Name: '',\r\n        P_Unit: '',\r\n        P_Department: ''\r\n        // P_Workshop: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'P_Name', // 字段ID\r\n            label: '姓名', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Contact_Way', // 字段ID\r\n            label: '联系方式', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'P_Type',\r\n            label: '人员类型',\r\n            type: 'select',\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position_Name',\r\n            label: '岗位名称',\r\n            type: 'select',\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'P_Unit',\r\n            label: '所属单位',\r\n            type: 'select',\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'P_Department',\r\n            label: '所属部门',\r\n            type: 'select',\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n          // {\r\n          //   key: 'P_Workshop',\r\n          //   label: '所属车间',\r\n          //   type: 'select',\r\n          //   options: [\r\n          //   ],\r\n          //   change: (e) => {\r\n          //     console.log(e)\r\n          //   }\r\n          // }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '新增',\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: false, // 是否禁用\r\n              icon: '', //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleCreate()\r\n              }\r\n            },\r\n            {\r\n              text: '导入模板下载',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleTemDownload()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导入',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleImport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        operateOptions: {\r\n          width: 200\r\n        },\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          // {\r\n          //   width: 60,\r\n          //   label: '序号',\r\n          //   otherOptions: {\r\n          //     type: 'index',\r\n          //     align: 'center'\r\n          //   } // key\r\n          // },\r\n          {\r\n            label: '员工工号',\r\n            key: 'P_Number'\r\n          },\r\n          {\r\n            label: '姓名',\r\n            key: 'P_Name'\r\n          },\r\n          {\r\n            label: '性别',\r\n            key: 'P_Sex'\r\n          },\r\n          {\r\n            label: '联系方式',\r\n            key: 'Contact_Way'\r\n          },\r\n          {\r\n            label: '人脸照片',\r\n            key: 'Face_picture',\r\n            otherOptions: {\r\n              align: 'center'\r\n            },\r\n            render: (row) => {\r\n              if (row.Face_Picture_Url) {\r\n                return this.$createElement('el-image', {\r\n                  style: {\r\n                    width: '40px',\r\n                    height: '40px'\r\n                  },\r\n                  attrs: {\r\n                    fit: 'cover',\r\n                    src: row.Face_Picture_Url,\r\n                    previewSrcList: [row.Face_Picture_Url]\r\n                  }\r\n                })\r\n              }\r\n            }\r\n          },\r\n          {\r\n            label: '人员类型',\r\n            key: 'P_Type'\r\n          },\r\n          {\r\n            label: '岗位名称',\r\n            key: 'Position_Name'\r\n          },\r\n          {\r\n            label: '所属部门',\r\n            key: 'P_Department'\r\n          },\r\n          // {\r\n          //   label: '所属班组',\r\n          //   key: 'P_Group'\r\n          // },\r\n          // {\r\n          //   label: '所属车间',\r\n          //   key: 'P_Workshop'\r\n          // },\r\n          {\r\n            label: '所属单位',\r\n            key: 'P_Unit'\r\n          },\r\n          {\r\n            label: '状态',\r\n            key: 'Status',\r\n            render: (row) => {\r\n              const text = row.Status === '0' ? '启用' : '禁用'\r\n              return this.$createElement('span', {}, text)\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '修改',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'edit')\r\n            }\r\n          },\r\n          // {\r\n          //   actionLabel: '启用',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleEnable(index, row, 'edit')\r\n          //   }\r\n          // },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  async created() {\r\n    // 岗位类型\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'Position_Name'\r\n    ).options = await this.initGetRole('Entrance_Type')\r\n    // 所属部门\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'P_Department'\r\n    ).options = await this.initGetDepartment('Entrance_Type')\r\n    // 所属单位\r\n    this.customForm.formItems.find((item) => item.key === 'P_Unit').options =\r\n      await this.initGetCompany('Entrance_Type')\r\n    // 人员类型\r\n    this.customForm.formItems.find((item) => item.key === 'P_Type').options =\r\n      await this.initDeviceType('P_Type')\r\n    this.init()\r\n  },\r\n  methods: {\r\n    // 人员类型\r\n    async initDeviceType(code) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode: code\r\n      })\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    // 岗位类型\r\n    async initGetRole(code) {\r\n      const res = await GetRole({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    // 所属部门\r\n    async initGetDepartment(code) {\r\n      const res = await GetDepartment({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    // 所属单位\r\n    async initGetCompany(code) {\r\n      const res = await GetCompany({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getPersonnelList()\r\n    },\r\n    init() {\r\n      this.getPersonnelList()\r\n    },\r\n    async getPersonnelList() {\r\n      const res = await GetPersonnelList({\r\n        ParameterJson: [\r\n          {\r\n            Key: '',\r\n            Value: [null],\r\n            Type: '',\r\n            Filter_Type: ''\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        Search: '',\r\n        SortName: '',\r\n        SortOrder: '',\r\n        P_Name: '',\r\n        Contact_Way: '',\r\n        P_Type: '',\r\n        Position_Name: '',\r\n        P_Unit: '',\r\n        P_Department: '',\r\n        P_Workshop: '',\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        console.log(res)\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n        this.handelImage(res.Data.Data)\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    async handelImage(data) {\r\n      const promises = data.map(async(v) => {\r\n        let Face_Picture_Url = ''\r\n        if (v.Face_Picture) {\r\n          const imageRes = await GetOssUrl({ url: v.Face_Picture })\r\n          Face_Picture_Url = imageRes.Data\r\n        }\r\n        return (function() {\r\n          const Face_PictureUrl = Face_Picture_Url\r\n          // 使用闭包来捕获变量的当前值\r\n          v.Face_Picture_Url = v.Face_Picture ? Face_PictureUrl : ''\r\n          return v\r\n        })()\r\n      })\r\n\r\n      Promise.all(promises)\r\n        .then((data) => {\r\n          this.customTableConfig.tableData = data\r\n          console.log(data)\r\n        })\r\n        .catch((error) => {\r\n          console.error(error)\r\n        })\r\n    },\r\n\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.componentsConfig = {\r\n        disabled: false,\r\n        title: '新增'\r\n      }\r\n      this.dialogVisible = true\r\n      this.currentComponent = DialogForm\r\n    },\r\n    // 启动 停用\r\n    async handleEnable(row) {\r\n      const text = row.Status === '0' ? '禁用' : '启用'\r\n      const textStatus = row.Status === '0' ? '1' : '0'\r\n      this.$confirm(`确认${text}？`, {\r\n        type: 'warning'\r\n      })\r\n        .then(async(_) => {\r\n          const res = await UpdateStatus({\r\n            id: row.Id,\r\n            status: textStatus\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.init()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n        .catch((_) => {})\r\n    },\r\n    handleDelete(index, row) {\r\n      this.$confirm('确认删除？', {\r\n        type: 'warning'\r\n      })\r\n        .then(async(_) => {\r\n          const res = await DelPersonnel({\r\n            id: row.Id\r\n          })\r\n          if (res.IsSucceed) {\r\n            this.init()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n        .catch((_) => {})\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.currentComponent = DialogFormLook\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: true,\r\n          title: '查看',\r\n          row: row\r\n        }\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        this.currentComponent = DialogForm\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: true,\r\n          title: '编辑',\r\n          row: row\r\n        }\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    async handleTemDownload() {\r\n      const res = await PersonnelImportTemplate({\r\n        code: 'accessControlPersonnelManagement'\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n        this.$message({\r\n          type: 'success',\r\n          message: '下载成功!'\r\n        })\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    async handleExport() {\r\n      if (this.tableSelection.length == 0) {\r\n        this.$message.warning('请选择数据在导出')\r\n        return\r\n      }\r\n      const res = await ExportEntrancePersonnel({\r\n        id: this.tableSelection.map((item) => item.Id).join(','),\r\n        code: 'accessControlPersonnelManagement',\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    async handleImport() {\r\n      this.dialogTitle = '批量导入'\r\n      this.currentComponent = DialogFormImport\r\n      this.componentsConfig = {\r\n        disabled: true,\r\n        title: '批量导入'\r\n      }\r\n      this.dialogVisible = true\r\n      // const res = await ExportEntrancePersonnel({\r\n      //   id: this.tableSelection.map((item) => item.Id),\r\n      //   ...this.ruleForm\r\n      // })\r\n      // if (res.IsSucceed) {\r\n      //   console.log(res)\r\n      //   downloadFile(res.Data, '21')\r\n      // }\r\n    },\r\n    async handleAllExport() {\r\n      const res = await ExportEntrancePersonnel({\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        Ids: [],\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AAEA,OAAAC,UAAA;AACA,OAAAC,cAAA;AACA,OAAAC,gBAAA;AAEA,SAAAC,YAAA;AACA;AACA;;AAEA,SACAC,gBAAA,EACAC,YAAA,EACAC,qBAAA,EACAC,YAAA,EACAC,YAAA,EACAC,uBAAA,EACAC,uBAAA,EACAC,uBAAA,EACAC,OAAA,EACAC,aAAA,EACAC,UAAA,EACAC,6BAAA,QACA;AACA,SAAAC,SAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACApB,WAAA,EAAAA,WAAA;IACA;IACA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAsB,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,gBAAA,EAAArB,UAAA;MACAsB,gBAAA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAJ,KAAA,CAAAK,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAN,KAAA,CAAAK,aAAA;UACAL,KAAA,CAAAO,OAAA;QACA;MACA;MACAF,aAAA;MACAG,WAAA;MACAC,cAAA;MAEAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,MAAA;QACAC,aAAA;QACAC,MAAA;QACAC,YAAA;QACA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAAA;UACAC,YAAA;YACA;YACAC,SAAA;UACA;UACAC,KAAA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAAA;UACAC,YAAA;YACA;YACAC,SAAA;UACA;UACAE,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAQ,OAAA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAQ,OAAA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAQ,OAAA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAQ,OAAA;UACAJ,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAAA,CACA;QACAI,KAAA;UACA;QAAA,CACA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAC,IAAA;YACAC,KAAA;YAAA;YACAC,KAAA;YAAA;YACAC,MAAA;YAAA;YACAC,OAAA;YAAA;YACAC,QAAA;YAAA;YACAC,IAAA;YAAA;YACAC,SAAA;YAAA;YACAvB,IAAA;YAAA;YACAwB,IAAA;YAAA;YACAC,OAAA,WAAAA,QAAAC,IAAA;cACApB,OAAA,CAAAC,GAAA,CAAAmB,IAAA;cACA/C,KAAA,CAAAgD,YAAA;YACA;UACA,GACA;YACAX,IAAA;YACAS,OAAA,WAAAA,QAAAC,IAAA;cACApB,OAAA,CAAAC,GAAA,CAAAmB,IAAA;cACA/C,KAAA,CAAAiD,iBAAA;YACA;UACA,GACA;YACAZ,IAAA;YACAS,OAAA,WAAAA,QAAAC,IAAA;cACApB,OAAA,CAAAC,GAAA,CAAAmB,IAAA;cACA/C,KAAA,CAAAkD,YAAA;YACA;UACA,GACA;YACAb,IAAA;YACAS,OAAA,WAAAA,QAAAC,IAAA;cACApB,OAAA,CAAAC,GAAA,CAAAmB,IAAA;cACA/C,KAAA,CAAAmD,YAAA;YACA;UACA;QAEA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,cAAA;UACAhC,KAAA;QACA;QACAiC,YAAA,GACA;UACAjC,KAAA;UACAF,YAAA;YACAD,IAAA;YACAqC,KAAA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACAtC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;UACAG,YAAA;YACAoC,KAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,gBAAA;cACA,OAAA7D,KAAA,CAAA8D,cAAA;gBACAC,KAAA;kBACAvC,KAAA;kBACAwC,MAAA;gBACA;gBACAC,KAAA;kBACAC,GAAA;kBACAC,GAAA,EAAAP,GAAA,CAAAC,gBAAA;kBACAO,cAAA,GAAAR,GAAA,CAAAC,gBAAA;gBACA;cACA;YACA;UACA;QACA,GACA;UACAzC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACAC,KAAA;UACAD,GAAA;QACA,GACA;UACAC,KAAA;UACAD,GAAA;UACAwC,MAAA,WAAAA,OAAAC,GAAA;YACA,IAAAvB,IAAA,GAAAuB,GAAA,CAAAS,MAAA;YACA,OAAArE,KAAA,CAAA8D,cAAA,aAAAzB,IAAA;UACA;QACA,EACA;QACAiC,SAAA;QACAC,YAAA,GACA;UACAC,WAAA;UACAlD,YAAA;YACAD,IAAA;UACA;UACAyB,OAAA,WAAAA,QAAA2B,KAAA,EAAAb,GAAA;YACA5D,KAAA,CAAA0E,UAAA,CAAAD,KAAA,EAAAb,GAAA;UACA;QACA,GACA;UACAY,WAAA;UACAlD,YAAA;YACAD,IAAA;UACA;UACAyB,OAAA,WAAAA,QAAA2B,KAAA,EAAAb,GAAA;YACA5D,KAAA,CAAA0E,UAAA,CAAAD,KAAA,EAAAb,GAAA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACAY,WAAA;UACAlD,YAAA;YACAD,IAAA;UACA;UACAyB,OAAA,WAAAA,QAAA2B,KAAA,EAAAb,GAAA;YACA5D,KAAA,CAAA2E,YAAA,CAAAF,KAAA,EAAAb,GAAA;UACA;QACA;MAEA;IACA;EACA;EACAgB,QAAA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OAIAT,MAAA,CAAAU,WAAA;UAAA;YAFAV,MAAA,CAAA7D,UAAA,CAAAC,SAAA,CAAAuE,IAAA,CACA,UAAA1C,IAAA;cAAA,OAAAA,IAAA,CAAA5B,GAAA;YAAA,CACA,EAAAU,OAAA,GAAAwD,QAAA,CAAAK,IAAA;YAAAL,QAAA,CAAAE,IAAA;YAAA,OAIAT,MAAA,CAAAa,iBAAA;UAAA;YAFAb,MAAA,CAAA7D,UAAA,CAAAC,SAAA,CAAAuE,IAAA,CACA,UAAA1C,IAAA;cAAA,OAAAA,IAAA,CAAA5B,GAAA;YAAA,CACA,EAAAU,OAAA,GAAAwD,QAAA,CAAAK,IAAA;YAAAL,QAAA,CAAAE,IAAA;YAAA,OAGAT,MAAA,CAAAc,cAAA;UAAA;YADAd,MAAA,CAAA7D,UAAA,CAAAC,SAAA,CAAAuE,IAAA,WAAA1C,IAAA;cAAA,OAAAA,IAAA,CAAA5B,GAAA;YAAA,GAAAU,OAAA,GAAAwD,QAAA,CAAAK,IAAA;YAAAL,QAAA,CAAAE,IAAA;YAAA,OAIAT,MAAA,CAAAe,cAAA;UAAA;YADAf,MAAA,CAAA7D,UAAA,CAAAC,SAAA,CAAAuE,IAAA,WAAA1C,IAAA;cAAA,OAAAA,IAAA,CAAA5B,GAAA;YAAA,GAAAU,OAAA,GAAAwD,QAAA,CAAAK,IAAA;YAEAZ,MAAA,CAAAgB,IAAA;UAAA;UAAA;YAAA,OAAAT,QAAA,CAAAU,IAAA;QAAA;MAAA,GAAAb,OAAA;IAAA;EACA;EACAc,OAAA;IACA;IACAH,cAAA,WAAAA,eAAAI,IAAA;MAAA,OAAAlB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAiB,SAAA;QAAA,IAAAC,GAAA,EAAAtE,OAAA;QAAA,OAAAmD,mBAAA,GAAAG,IAAA,UAAAiB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAf,IAAA,GAAAe,SAAA,CAAAd,IAAA;YAAA;cAAAc,SAAA,CAAAd,IAAA;cAAA,OACA5F,6BAAA;gBACA2G,cAAA,EAAAL;cACA;YAAA;cAFAE,GAAA,GAAAE,SAAA,CAAAX,IAAA;cAGA7D,OAAA,GAAAsE,GAAA,CAAAI,IAAA,CAAAC,GAAA,WAAAzD,IAAA,EAAA0B,KAAA;gBAAA;kBACArD,KAAA,EAAA2B,IAAA,CAAA0D,YAAA;kBACAC,KAAA,EAAA3D,IAAA,CAAA4D;gBACA;cAAA;cAAA,OAAAN,SAAA,CAAAO,MAAA,WACA/E,OAAA;YAAA;YAAA;cAAA,OAAAwE,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACA;IACAV,WAAA,WAAAA,YAAAS,IAAA;MAAA,OAAAlB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA4B,SAAA;QAAA,IAAAV,GAAA,EAAAtE,OAAA;QAAA,OAAAmD,mBAAA,GAAAG,IAAA,UAAA2B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzB,IAAA,GAAAyB,SAAA,CAAAxB,IAAA;YAAA;cAAAwB,SAAA,CAAAxB,IAAA;cAAA,OACA/F,OAAA;YAAA;cAAA2G,GAAA,GAAAY,SAAA,CAAArB,IAAA;cACA7D,OAAA,GAAAsE,GAAA,CAAAI,IAAA,CAAAC,GAAA,WAAAzD,IAAA,EAAA0B,KAAA;gBAAA;kBACArD,KAAA,EAAA2B,IAAA,CAAA0D,YAAA;kBACAC,KAAA,EAAA3D,IAAA,CAAA4D;gBACA;cAAA;cAAA,OAAAI,SAAA,CAAAH,MAAA,WACA/E,OAAA;YAAA;YAAA;cAAA,OAAAkF,SAAA,CAAAhB,IAAA;UAAA;QAAA,GAAAc,QAAA;MAAA;IACA;IACA;IACAlB,iBAAA,WAAAA,kBAAAM,IAAA;MAAA,OAAAlB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA+B,SAAA;QAAA,IAAAb,GAAA,EAAAtE,OAAA;QAAA,OAAAmD,mBAAA,GAAAG,IAAA,UAAA8B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5B,IAAA,GAAA4B,SAAA,CAAA3B,IAAA;YAAA;cAAA2B,SAAA,CAAA3B,IAAA;cAAA,OACA9F,aAAA;YAAA;cAAA0G,GAAA,GAAAe,SAAA,CAAAxB,IAAA;cACA7D,OAAA,GAAAsE,GAAA,CAAAI,IAAA,CAAAC,GAAA,WAAAzD,IAAA,EAAA0B,KAAA;gBAAA;kBACArD,KAAA,EAAA2B,IAAA,CAAA0D,YAAA;kBACAC,KAAA,EAAA3D,IAAA,CAAA4D;gBACA;cAAA;cAAA,OAAAO,SAAA,CAAAN,MAAA,WACA/E,OAAA;YAAA;YAAA;cAAA,OAAAqF,SAAA,CAAAnB,IAAA;UAAA;QAAA,GAAAiB,QAAA;MAAA;IACA;IACA;IACApB,cAAA,WAAAA,eAAAK,IAAA;MAAA,OAAAlB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAkC,SAAA;QAAA,IAAAhB,GAAA,EAAAtE,OAAA;QAAA,OAAAmD,mBAAA,GAAAG,IAAA,UAAAiC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/B,IAAA,GAAA+B,SAAA,CAAA9B,IAAA;YAAA;cAAA8B,SAAA,CAAA9B,IAAA;cAAA,OACA7F,UAAA;YAAA;cAAAyG,GAAA,GAAAkB,SAAA,CAAA3B,IAAA;cACA7D,OAAA,GAAAsE,GAAA,CAAAI,IAAA,CAAAC,GAAA,WAAAzD,IAAA,EAAA0B,KAAA;gBAAA;kBACArD,KAAA,EAAA2B,IAAA,CAAA0D,YAAA;kBACAC,KAAA,EAAA3D,IAAA,CAAA4D;gBACA;cAAA;cAAA,OAAAU,SAAA,CAAAT,MAAA,WACA/E,OAAA;YAAA;YAAA;cAAA,OAAAwF,SAAA,CAAAtB,IAAA;UAAA;QAAA,GAAAoB,QAAA;MAAA;IACA;IACAG,UAAA,WAAAA,WAAAvH,IAAA;MACA,KAAAmC,iBAAA,CAAAmB,WAAA;MACA1B,OAAA,CAAAC,GAAA,CAAA7B,IAAA;MACA,KAAAQ,OAAA;IACA;IACAgH,SAAA,WAAAA,UAAA;MACA,KAAAhH,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAAiH,gBAAA;IACA;IACA1B,IAAA,WAAAA,KAAA;MACA,KAAA0B,gBAAA;IACA;IACAA,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MAAA,OAAA1C,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAyC,SAAA;QAAA,IAAAvB,GAAA;QAAA,OAAAnB,mBAAA,GAAAG,IAAA,UAAAwC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtC,IAAA,GAAAsC,SAAA,CAAArC,IAAA;YAAA;cAAAqC,SAAA,CAAArC,IAAA;cAAA,OACAvG,gBAAA,CAAA6I,aAAA;gBACAC,aAAA,GACA;kBACAC,GAAA;kBACApB,KAAA;kBACAqB,IAAA;kBACAC,WAAA;gBACA,EACA;gBACAC,IAAA,EAAAT,MAAA,CAAAvF,iBAAA,CAAAmB,WAAA;gBACA8E,QAAA,EAAAV,MAAA,CAAAvF,iBAAA,CAAAoB,QAAA;gBACA8E,MAAA;gBACAC,QAAA;gBACAC,SAAA;gBACA3H,MAAA;gBACAC,WAAA;gBACAC,MAAA;gBACAC,aAAA;gBACAC,MAAA;gBACAC,YAAA;gBACAuH,UAAA;cAAA,GACAd,MAAA,CAAA/G,QAAA,CACA;YAAA;cAtBAyF,GAAA,GAAAyB,SAAA,CAAAlC,IAAA;cAuBA,IAAAS,GAAA,CAAAqC,SAAA;gBACAf,MAAA,CAAAvF,iBAAA,CAAAoC,SAAA,GAAA6B,GAAA,CAAAI,IAAA,CAAAA,IAAA;gBACA5E,OAAA,CAAAC,GAAA,CAAAuE,GAAA;gBACAsB,MAAA,CAAAvF,iBAAA,CAAAqB,KAAA,GAAA4C,GAAA,CAAAI,IAAA,CAAAkC,UAAA;gBACAhB,MAAA,CAAAiB,WAAA,CAAAvC,GAAA,CAAAI,IAAA,CAAAA,IAAA;cACA;gBACAkB,MAAA,CAAAkB,QAAA;kBACAtH,IAAA;kBACAuH,OAAA,EAAAzC,GAAA,CAAA0C;gBACA;cACA;YAAA;YAAA;cAAA,OAAAjB,SAAA,CAAA7B,IAAA;UAAA;QAAA,GAAA2B,QAAA;MAAA;IACA;IACAgB,WAAA,WAAAA,YAAA3I,IAAA;MAAA,IAAA+I,MAAA;MAAA,OAAA/D,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA8D,SAAA;QAAA,IAAAC,QAAA;QAAA,OAAAhE,mBAAA,GAAAG,IAAA,UAAA8D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,IAAA,GAAA4D,SAAA,CAAA3D,IAAA;YAAA;cACAyD,QAAA,GAAAjJ,IAAA,CAAAyG,GAAA;gBAAA,IAAA2C,IAAA,GAAApE,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAmE,SAAAC,CAAA;kBAAA,IAAAxF,gBAAA,EAAAyF,QAAA;kBAAA,OAAAtE,mBAAA,GAAAG,IAAA,UAAAoE,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAAlE,IAAA,GAAAkE,SAAA,CAAAjE,IAAA;sBAAA;wBACA1B,gBAAA;wBAAA,KACAwF,CAAA,CAAAI,YAAA;0BAAAD,SAAA,CAAAjE,IAAA;0BAAA;wBAAA;wBAAAiE,SAAA,CAAAjE,IAAA;wBAAA,OACA3F,SAAA;0BAAA8J,GAAA,EAAAL,CAAA,CAAAI;wBAAA;sBAAA;wBAAAH,QAAA,GAAAE,SAAA,CAAA9D,IAAA;wBACA7B,gBAAA,GAAAyF,QAAA,CAAA/C,IAAA;sBAAA;wBAAA,OAAAiD,SAAA,CAAA5C,MAAA,WAEA;0BACA,IAAA+C,eAAA,GAAA9F,gBAAA;0BACA;0BACAwF,CAAA,CAAAxF,gBAAA,GAAAwF,CAAA,CAAAI,YAAA,GAAAE,eAAA;0BACA,OAAAN,CAAA;wBACA;sBAAA;sBAAA;wBAAA,OAAAG,SAAA,CAAAzD,IAAA;oBAAA;kBAAA,GAAAqD,QAAA;gBAAA,CACA;gBAAA,iBAAAQ,EAAA;kBAAA,OAAAT,IAAA,CAAAU,KAAA,OAAAC,SAAA;gBAAA;cAAA;cAEAC,OAAA,CAAAC,GAAA,CAAAhB,QAAA,EACAiB,IAAA,WAAAlK,IAAA;gBACA+I,MAAA,CAAA5G,iBAAA,CAAAoC,SAAA,GAAAvE,IAAA;gBACA4B,OAAA,CAAAC,GAAA,CAAA7B,IAAA;cACA,GACAmK,KAAA,WAAAC,KAAA;gBACAxI,OAAA,CAAAwI,KAAA,CAAAA,KAAA;cACA;YAAA;YAAA;cAAA,OAAAjB,SAAA,CAAAnD,IAAA;UAAA;QAAA,GAAAgD,QAAA;MAAA;IACA;IAEA/F,YAAA,WAAAA,aAAA;MACA,KAAAxC,WAAA;MACA,KAAAN,gBAAA;QACAwC,QAAA;QACA0H,KAAA;MACA;MACA,KAAA/J,aAAA;MACA,KAAAJ,gBAAA,GAAArB,UAAA;IACA;IACA;IACAyL,YAAA,WAAAA,aAAAzG,GAAA;MAAA,IAAA0G,MAAA;MAAA,OAAAvF,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAsF,UAAA;QAAA,IAAAlI,IAAA,EAAAmI,UAAA;QAAA,OAAAxF,mBAAA,GAAAG,IAAA,UAAAsF,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApF,IAAA,GAAAoF,UAAA,CAAAnF,IAAA;YAAA;cACAlD,IAAA,GAAAuB,GAAA,CAAAS,MAAA;cACAmG,UAAA,GAAA5G,GAAA,CAAAS,MAAA;cACAiG,MAAA,CAAAK,QAAA,gBAAAC,MAAA,CAAAvI,IAAA;gBACAhB,IAAA;cACA,GACA4I,IAAA;gBAAA,IAAAY,KAAA,GAAA9F,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA6F,SAAAC,CAAA;kBAAA,IAAA5E,GAAA;kBAAA,OAAAnB,mBAAA,GAAAG,IAAA,UAAA6F,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAA3F,IAAA,GAAA2F,SAAA,CAAA1F,IAAA;sBAAA;wBAAA0F,SAAA,CAAA1F,IAAA;wBAAA,OACApG,YAAA;0BACA+L,EAAA,EAAAtH,GAAA,CAAAuH,EAAA;0BACAC,MAAA,EAAAZ;wBACA;sBAAA;wBAHArE,GAAA,GAAA8E,SAAA,CAAAvF,IAAA;wBAIA,IAAAS,GAAA,CAAAqC,SAAA;0BACA8B,MAAA,CAAAxE,IAAA;wBACA;0BACAwE,MAAA,CAAA3B,QAAA;4BACAtH,IAAA;4BACAuH,OAAA,EAAAzC,GAAA,CAAA0C;0BACA;wBACA;sBAAA;sBAAA;wBAAA,OAAAoC,SAAA,CAAAlF,IAAA;oBAAA;kBAAA,GAAA+E,QAAA;gBAAA,CACA;gBAAA,iBAAAO,GAAA;kBAAA,OAAAR,KAAA,CAAAhB,KAAA,OAAAC,SAAA;gBAAA;cAAA,KACAI,KAAA,WAAAa,CAAA;YAAA;YAAA;cAAA,OAAAL,UAAA,CAAA3E,IAAA;UAAA;QAAA,GAAAwE,SAAA;MAAA;IACA;IACA5F,YAAA,WAAAA,aAAAF,KAAA,EAAAb,GAAA;MAAA,IAAA0H,MAAA;MACA,KAAAX,QAAA;QACAtJ,IAAA;MACA,GACA4I,IAAA;QAAA,IAAAsB,KAAA,GAAAxG,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAuG,UAAAT,CAAA;UAAA,IAAA5E,GAAA;UAAA,OAAAnB,mBAAA,GAAAG,IAAA,UAAAsG,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAApG,IAAA,GAAAoG,UAAA,CAAAnG,IAAA;cAAA;gBAAAmG,UAAA,CAAAnG,IAAA;gBAAA,OACAnG,YAAA;kBACA8L,EAAA,EAAAtH,GAAA,CAAAuH;gBACA;cAAA;gBAFAhF,GAAA,GAAAuF,UAAA,CAAAhG,IAAA;gBAGA,IAAAS,GAAA,CAAAqC,SAAA;kBACA8C,MAAA,CAAAxF,IAAA;gBACA;kBACAwF,MAAA,CAAA3C,QAAA;oBACAtH,IAAA;oBACAuH,OAAA,EAAAzC,GAAA,CAAA0C;kBACA;gBACA;cAAA;cAAA;gBAAA,OAAA6C,UAAA,CAAA3F,IAAA;YAAA;UAAA,GAAAyF,SAAA;QAAA,CACA;QAAA,iBAAAG,GAAA;UAAA,OAAAJ,KAAA,CAAA1B,KAAA,OAAAC,SAAA;QAAA;MAAA,KACAI,KAAA,WAAAa,CAAA;IACA;IACArG,UAAA,WAAAA,WAAAD,KAAA,EAAAb,GAAA,EAAAvC,IAAA;MACAM,OAAA,CAAAC,GAAA,CAAA6C,KAAA,EAAAb,GAAA,EAAAvC,IAAA;MACA,IAAAA,IAAA;QACA,KAAAb,WAAA;QACA,KAAAP,gBAAA,GAAApB,cAAA;QACA,KAAAqB,gBAAA;UACA0L,EAAA,EAAAhI,GAAA,CAAAgI,EAAA;UACAlJ,QAAA;UACA0H,KAAA;UACAxG,GAAA,EAAAA;QACA;MACA,WAAAvC,IAAA;QACA,KAAAb,WAAA;QACA,KAAAP,gBAAA,GAAArB,UAAA;QACA,KAAAsB,gBAAA;UACA0L,EAAA,EAAAhI,GAAA,CAAAgI,EAAA;UACAlJ,QAAA;UACA0H,KAAA;UACAxG,GAAA,EAAAA;QACA;MACA;MACA,KAAAvD,aAAA;IACA;IACA4C,iBAAA,WAAAA,kBAAA;MAAA,IAAA4I,MAAA;MAAA,OAAA9G,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA6G,UAAA;QAAA,IAAA3F,GAAA;QAAA,OAAAnB,mBAAA,GAAAG,IAAA,UAAA4G,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1G,IAAA,GAAA0G,UAAA,CAAAzG,IAAA;YAAA;cAAAyG,UAAA,CAAAzG,IAAA;cAAA,OACAlG,uBAAA;gBACA4G,IAAA;cACA;YAAA;cAFAE,GAAA,GAAA6F,UAAA,CAAAtG,IAAA;cAGA,IAAAS,GAAA,CAAAqC,SAAA;gBACA7G,OAAA,CAAAC,GAAA,CAAAuE,GAAA;gBACApH,YAAA,CAAAoH,GAAA,CAAAI,IAAA;gBACAsF,MAAA,CAAAlD,QAAA;kBACAtH,IAAA;kBACAuH,OAAA;gBACA;cACA;gBACAiD,MAAA,CAAAlD,QAAA;kBACAtH,IAAA;kBACAuH,OAAA,EAAAzC,GAAA,CAAA0C;gBACA;cACA;YAAA;YAAA;cAAA,OAAAmD,UAAA,CAAAjG,IAAA;UAAA;QAAA,GAAA+F,SAAA;MAAA;IACA;IACA3I,YAAA,WAAAA,aAAA;MAAA,IAAA8I,MAAA;MAAA,OAAAlH,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAiH,UAAA;QAAA,IAAA/F,GAAA;QAAA,OAAAnB,mBAAA,GAAAG,IAAA,UAAAgH,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9G,IAAA,GAAA8G,UAAA,CAAA7G,IAAA;YAAA;cAAA,MACA0G,MAAA,CAAAxL,cAAA,CAAA4L,MAAA;gBAAAD,UAAA,CAAA7G,IAAA;gBAAA;cAAA;cACA0G,MAAA,CAAAtD,QAAA,CAAA2D,OAAA;cAAA,OAAAF,UAAA,CAAAxF,MAAA;YAAA;cAAAwF,UAAA,CAAA7G,IAAA;cAAA,OAGAhG,uBAAA,CAAAsI,aAAA;gBACAqD,EAAA,EAAAe,MAAA,CAAAxL,cAAA,CAAA+F,GAAA,WAAAzD,IAAA;kBAAA,OAAAA,IAAA,CAAAoI,EAAA;gBAAA,GAAAoB,IAAA;gBACAtG,IAAA;cAAA,GACAgG,MAAA,CAAAvL,QAAA,CACA;YAAA;cAJAyF,GAAA,GAAAiG,UAAA,CAAA1G,IAAA;cAKA,IAAAS,GAAA,CAAAqC,SAAA;gBACA7G,OAAA,CAAAC,GAAA,CAAAuE,GAAA;gBACApH,YAAA,CAAAoH,GAAA,CAAAI,IAAA;cACA;gBACA0F,MAAA,CAAAtD,QAAA;kBACAtH,IAAA;kBACAuH,OAAA,EAAAzC,GAAA,CAAA0C;gBACA;cACA;YAAA;YAAA;cAAA,OAAAuD,UAAA,CAAArG,IAAA;UAAA;QAAA,GAAAmG,SAAA;MAAA;IACA;IACAhJ,YAAA,WAAAA,aAAA;MAAA,IAAAsJ,MAAA;MAAA,OAAAzH,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAwH,UAAA;QAAA,OAAAzH,mBAAA,GAAAG,IAAA,UAAAuH,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArH,IAAA,GAAAqH,UAAA,CAAApH,IAAA;YAAA;cACAiH,MAAA,CAAAhM,WAAA;cACAgM,MAAA,CAAAvM,gBAAA,GAAAnB,gBAAA;cACA0N,MAAA,CAAAtM,gBAAA;gBACAwC,QAAA;gBACA0H,KAAA;cACA;cACAoC,MAAA,CAAAnM,aAAA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YAAA;YAAA;cAAA,OAAAsM,UAAA,CAAA5G,IAAA;UAAA;QAAA,GAAA0G,SAAA;MAAA;IACA;IACAG,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MAAA,OAAA9H,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA6H,UAAA;QAAA,IAAA3G,GAAA;QAAA,OAAAnB,mBAAA,GAAAG,IAAA,UAAA4H,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1H,IAAA,GAAA0H,UAAA,CAAAzH,IAAA;YAAA;cAAAyH,UAAA,CAAAzH,IAAA;cAAA,OACAhG,uBAAA,CAAAsI,aAAA;gBACAoF,OAAA;gBACAC,OAAA;gBACAC,QAAA;gBACAC,KAAA;gBACAC,GAAA;cAAA,GACAR,OAAA,CAAAnM,QAAA,CACA;YAAA;cAPAyF,GAAA,GAAA6G,UAAA,CAAAtH,IAAA;cAQA,IAAAS,GAAA,CAAAqC,SAAA;gBACA7G,OAAA,CAAAC,GAAA,CAAAuE,GAAA;gBACApH,YAAA,CAAAoH,GAAA,CAAAI,IAAA;cACA;gBACAsG,OAAA,CAAAlE,QAAA;kBACAtH,IAAA;kBACAuH,OAAA,EAAAzC,GAAA,CAAA0C;gBACA;cACA;YAAA;YAAA;cAAA,OAAAmE,UAAA,CAAAjH,IAAA;UAAA;QAAA,GAAA+G,SAAA;MAAA;IACA;IACAQ,gBAAA,WAAAA,iBAAAC,GAAA;MACA5L,OAAA,CAAAC,GAAA,iBAAAgJ,MAAA,CAAA2C,GAAA;MACA,KAAArL,iBAAA,CAAAoB,QAAA,GAAAiK,GAAA;MACA,KAAAzH,IAAA;IACA;IACA0H,mBAAA,WAAAA,oBAAAD,GAAA;MACA5L,OAAA,CAAAC,GAAA,wBAAAgJ,MAAA,CAAA2C,GAAA;MACA,KAAArL,iBAAA,CAAAmB,WAAA,GAAAkK,GAAA;MACA,KAAAzH,IAAA;IACA;IACA2H,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAjN,cAAA,GAAAiN,SAAA;IACA;EACA;AACA", "ignoreList": []}]}