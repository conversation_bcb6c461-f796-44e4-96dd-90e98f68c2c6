{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\visitorManagement\\visitorList\\index.vue?vue&type=style&index=0&id=ed41d588&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\visitorManagement\\visitorList\\index.vue", "mtime": 1755674552440}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5tdDIwIHsNCiAgbWFyZ2luLXRvcDogMTBweDsNCn0NCi5sYXlvdXR7DQogIGhlaWdodDogY2FsYygxMDB2aCAtIDkwcHgpOw0KICBvdmVyZmxvdzogYXV0bzsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4eA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/visitorManagement/visitorList", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      top=\"10vh\"\r\n      :visible.sync=\"dialogVisible\"\r\n      destroy-on-close\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n  <script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport DialogForm from \"./dialogForm.vue\";\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\nimport getGridByCode from \"../../safetyManagement/mixins/index\";\r\nimport {\r\n  GetPageList,\r\n  ExportVisitorEquipment,\r\n  GetVisitorsEntity,\r\n  ApprovalVisitors,\r\n  SendPassCheck,\r\n  SendPassCheckAgain,\r\n  ExportData,\r\n  ExportVisitorsList,\r\n} from \"@/api/business/visitorManagement\";\r\nimport dayjs from \"dayjs\";\r\nimport addRouterPage from \"@/mixins/add-router-page\";\r\n\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        Data: {},\r\n        visitorTravelType: [],\r\n        visitorReceiverUnit: [],\r\n        visitorType: [],\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"\",\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        Name: \"\",\r\n        EquipmentType: [],\r\n        PlateNumber: \"\",\r\n        Unit: \"\",\r\n        Status: null,\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Name\",\r\n            label: \"访客姓名\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"EquipmentType\",\r\n            label: \"访客预约时间\",\r\n            type: \"datePicker\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n              type: \"daterange\",\r\n              rangeSeparator: \"至\",\r\n              startPlaceholder: \"开始日期\",\r\n              endPlaceholder: \"结束日期\",\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"PlateNumber\",\r\n            label: \"车牌号码\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Unit\",\r\n            label: \"被访单位\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Status\",\r\n            label: \"状态\",\r\n            type: \"select\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            options: [\r\n              { label: \"全部\", value: \"\" },\r\n              { label: \"未受理\", value: 0 },\r\n              { label: \"已审核\", value: 1 },\r\n              { label: \"未通过\", value: 2 },\r\n              { label: \"已过期\", value: 3 },\r\n            ],\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"新增\",\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: false, // 是否禁用\r\n              icon: \"\", //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n            {\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleExport();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: \"240px\",\r\n          align: \"center\",\r\n        },\r\n        tableActionsWidth: 220,\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"发送通行证\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handlePass(row.Id);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(row);\r\n            },\r\n          },\r\n          /* {\r\n            actionLabel: '审核',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              if (row.Status == '未受理') {\r\n                this.handleToExamine(row.Id)\r\n              } else {\r\n                this.$message.warning(`该数据${row.Status}`)\r\n              }\r\n            }\r\n          }, */\r\n          {\r\n            actionLabel: \"再次生成通行码\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleAgain(row.Id);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + \"/addVisitor\",\r\n          hidden: true,\r\n          component: () => import(\"./addVisitor.vue\"),\r\n          meta: { title: `新增访客` },\r\n          name: \"AddVisitor\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  async created() {\r\n    this.init();\r\n  },\r\n  mixins: [getGridByCode, addRouterPage],\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n    },\r\n    init() {\r\n      this.getGridByCode(\"visitorList\");\r\n      this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      let Start = \"\";\r\n      let End = \"\";\r\n      if ((this.ruleForm.EquipmentType ?? []).length > 0) {\r\n        Start = dayjs(this.ruleForm.EquipmentType[0]).format(\"YYYY-MM-DD\");\r\n        End = dayjs(this.ruleForm.EquipmentType[1]).format(\"YYYY-MM-DD\");\r\n      }\r\n      const res = await GetPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n        Start,\r\n        End,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    async handleCreate() {\r\n      this.$router.push({\r\n        name: \"AddVisitor\",\r\n        query: { pg_redirect: this.$route.name },\r\n      });\r\n      /*  this.dialogTitle = '新增'\r\n       this.dialogVisible = true\r\n       this.currentComponent = DialogForm\r\n       this.componentsConfig.Data = {\r\n         Name: '',\r\n         Sex: '',\r\n         Link: '',\r\n         Type: '',\r\n         VisitTime: '',\r\n         TravelType: '',\r\n         Count: '',\r\n         CarCount: '',\r\n         VisitorName: '',\r\n         PlateNumber: '',\r\n         Reason: '',\r\n         VisitorUnit: '',\r\n         Unit: '',\r\n         UnitLink: '',\r\n         Receiver: '',\r\n         ReceiverLink: '',\r\n         RealReceiver: '',\r\n         isWatch: false\r\n       }\r\n       this.componentsConfig.visitorTravelType = await this.getDictionaryDetailListByCode('VisitorTravelType')\r\n       this.componentsConfig.visitorReceiverUnit = await this.getDictionaryDetailListByCode('VisitorReceiverUnit')\r\n       this.componentsConfig.visitorType = await this.getDictionaryDetailListByCode('VisitorType') */\r\n    },\r\n    async handleEdit(row) {\r\n      // let res = await GetVisitorsEntity({ id: row.Id });\r\n      // if (res.IsSucceed) {\r\n      //   this.componentsConfig.Data = { ...res.Data, isWatch: true };\r\n      // }\r\n      this.componentsConfig.Data = row;\r\n      this.currentComponent = DialogForm;\r\n      this.dialogTitle = \"查看\";\r\n      this.dialogVisible = true;\r\n      // this.componentsConfig.visitorTravelType =\r\n      //   await this.getDictionaryDetailListByCode(\"VisitorTravelType\");\r\n      // this.componentsConfig.visitorReceiverUnit =\r\n      //   await this.getDictionaryDetailListByCode(\"VisitorReceiverUnit\");\r\n      // this.componentsConfig.visitorType =\r\n      //   await this.getDictionaryDetailListByCode(\"VisitorType\");\r\n    },\r\n    // async handleExport() {\r\n    //   const res = await ExportData({\r\n    //     Ids: this.tableSelection.map((item) => item.Id).toString(),\r\n    //   });\r\n    //   // downloadFile(res.Data, '访客列表数据')\r\n    //   const url = window.URL.createObjectURL(\r\n    //     new Blob([res], {\r\n    //       type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\r\n    //     })\r\n    //   );\r\n    //   const link = document.createElement(\"a\");\r\n    //   link.style.display = \"none\";\r\n    //   link.href = url;\r\n    //   link.setAttribute(\"download\", \"访客列表数据\");\r\n    //   document.body.appendChild(link);\r\n    //   link.click();\r\n    // },\r\n    // v2 版本  访客列表导出\r\n\r\n    async handleExport() {\r\n      let Start = \"\";\r\n      let End = \"\";\r\n      if ((this.ruleForm.EquipmentType ?? []).length > 0) {\r\n        Start = dayjs(this.ruleForm.EquipmentType[0]).format(\"YYYY-MM-DD\");\r\n        End = dayjs(this.ruleForm.EquipmentType[1]).format(\"YYYY-MM-DD\");\r\n      }\r\n      const res = await ExportVisitorsList({\r\n        ...this.ruleForm,\r\n        Start,\r\n        End,\r\n        Id: this.tableSelection.map((item) => item.Id).toString(),\r\n      });\r\n\r\n      if (res.IsSucceed) {\r\n        this.$message.success(\"导出成功\");\r\n        downloadFile(res.Data, \"访客列表数据\");\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n      // downloadFile(res.Data, '访客列表数据')\r\n      // const url = window.URL.createObjectURL(\r\n      //   new Blob([res], {\r\n      //     type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\r\n      //   })\r\n      // );\r\n      // const link = document.createElement(\"a\");\r\n      // link.style.display = \"none\";\r\n      // link.href = url;\r\n      // link.setAttribute(\"download\", \"访客列表数据\");\r\n      // document.body.appendChild(link);\r\n      // link.click();\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.fetchData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.fetchData();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleToExamine(ID) {\r\n      this.$confirm(\"是否确认审核此访客信息?\", \"确认审核\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          ApprovalVisitors({ ID }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"审核成功!\",\r\n              });\r\n              this.fetchData();\r\n            } else {\r\n              this.$message.error(res.Message);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"info\",\r\n            message: \"已取消\",\r\n          });\r\n        });\r\n    },\r\n    handlePass(Id) {\r\n      this.$confirm(\"是否确认发送通行证给此访客?\", \"发送通行证\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          SendPassCheck({ Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success(\"操作成功\");\r\n            } else {\r\n              this.$message.error(res.Message);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"info\",\r\n            message: \"已取消\",\r\n          });\r\n        });\r\n    },\r\n    handleAgain(Id) {\r\n      this.$confirm(\"是否确认再次激活通行码?\", \"发送通行证\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          SendPassCheckAgain({ Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success(\"操作成功\");\r\n            } else {\r\n              this.$message.error(res.Message);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"info\",\r\n            message: \"已取消\",\r\n          });\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n  <style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}