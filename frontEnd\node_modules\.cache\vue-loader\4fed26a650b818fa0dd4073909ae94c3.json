{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\alarmSettings\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\alarmSettings\\index.vue", "mtime": 1755506574319}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/business/eventManagement/alarmSettings", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout\n      :layout-config=\"{\n        isShowSearchForm: false,\n      }\"\n    >\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n        />\n      </template>\n    </CustomLayout>\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <component\n        :is=\"currentComponent\"\n        v-if=\"dialogVisible\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n      /></el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\nimport DialogForm from './dialogForm.vue'\n\nimport {\n  GetWarningConfigPageList,\n  GetNoticeDropDownOption,\n  EnableWarningSiteNotice,\n  EnableWarningMobileMessageNotice,\n  EnableWarningConfig,\n  SetWarningNoticeObject\n} from '@/api/business/eventManagement'\nexport default {\n  name: '',\n  components: {\n    CustomTable,\n    CustomForm,\n    CustomLayout\n  },\n  data() {\n    return {\n      currentComponent: DialogForm,\n      componentsConfig: {\n        Data: {}\n      },\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true\n        },\n        close: () => {\n          this.dialogVisible = false\n          this.onFresh()\n        }\n      },\n      dialogVisible: false,\n      dialogTitle: '告警管理审核',\n      tableSelection: [],\n      ruleForm: {},\n      customForm: {\n        formItems: [],\n        rules: {},\n        customFormButtons: {\n          submitName: '查询',\n          resetName: '重置'\n        }\n      },\n      customTableConfig: {\n        buttonConfig: {\n          buttonList: [\n            {\n              text: '启用',\n              round: false, // 是否圆角\n              plain: false, // 是否朴素\n              circle: false, // 是否圆形\n              loading: false, // 是否加载�?              disabled: true, // 是否禁用\n              icon: '', //  图标\n              autofocus: false, // 是否聚焦\n              type: 'primary', // primary / success / warning / danger / info / text\n              size: 'small', // medium / small / mini\n              onclick: (item) => {\n                console.log(item)\n                this.handleEnable()\n              }\n            },\n            {\n              text: '关闭',\n              type: 'danger',\n              disabled: true, // 是否禁用\n              onclick: (item) => {\n                console.log(item)\n                this.handleClose()\n              }\n            }\n          ]\n        },\n        // 表格\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        height: '100%',\n        tableColumns: [\n          {\n            otherOptions: {\n              type: 'selection',\n              align: 'center',\n              fixed: 'left'\n            }\n          },\n          {\n            label: '告警层级',\n            key: 'SourceTypeDisplay',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '业务模块',\n            key: 'Module',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '告警类型',\n            key: 'WarningType',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '规则描述',\n            key: 'RuleDescription',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '站内通知',\n            key: 'EnableSiteNotice',\n            otherOptions: {\n              align: 'center'\n            },\n            render: (row) => {\n              return this.$createElement('el-switch', {\n                props: {\n                  value: row.EnableSiteNotice,\n                  'active-color': '#13ce66'\n                },\n                on: {\n                  change: (e) => {\n                    this.handleSiteNoticeCloseEnable(row.Id, e)\n                  }\n                }\n              })\n            }\n          },\n          {\n            label: '短信通知',\n            key: 'EnableMobileMessageNotice',\n            otherOptions: {\n              align: 'center'\n            },\n            render: (row) => {\n              return this.$createElement('el-switch', {\n                props: {\n                  value: row.EnableMobileMessageNotice,\n                  'active-color': '#13ce66'\n                },\n                on: {\n                  change: (e) => {\n                    this.handleMobileMessageCloseEnable(row.Id, e)\n                  }\n                }\n              })\n            }\n          },\n          {\n            label: '通知角色',\n            key: 'Roles',\n            otherOptions: {\n              align: 'center'\n            },\n            render: (row) => {\n              return this.$createElement(\n                'span',\n                {\n                  style: {\n                    color: '#3582fb'\n                  },\n                  on: {\n                    click: () => {\n                      this.handleEdit(row, 'role')\n                    }\n                  }\n                },\n                row.Roles.join(',') || '添加'\n              )\n            }\n          },\n          {\n            label: '通知�?,\n            key: 'Users',\n            otherOptions: {\n              align: 'center'\n            },\n            render: (row) => {\n              return this.$createElement(\n                'span',\n                {\n                  style: {\n                    color: '#3582fb'\n                  },\n                  on: {\n                    click: () => {\n                      this.handleEdit(row, 'user')\n                    }\n                  }\n                },\n                row.Users.join(',') || '添加'\n              )\n            }\n          },\n          {\n            label: '执行周期',\n            key: 'InformCycleDisplay',\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '上次执行时间',\n            key: 'LastInformTime',\n            width: 180,\n            otherOptions: {\n              align: 'center'\n            }\n          },\n          {\n            label: '启用状�?,\n            key: 'Enable',\n            otherOptions: {\n              align: 'center'\n            },\n            render: (row) => {\n              return this.$createElement('el-switch', {\n                props: {\n                  value: row.Enable,\n                  'active-color': '#13ce66'\n                },\n                on: {\n                  change: (e) => {\n                    this.handleCloseEnable(row.Id, e)\n                  }\n                }\n              })\n            }\n          }\n        ],\n        tableData: [],\n        operateOptions: {\n          align: 'center'\n        },\n        tableActions: []\n      }\n    }\n  },\n  watch: {\n    tableSelection: {\n      handler(newval, oldval) {\n        console.log(newval)\n        if (newval.length > 0) {\n          this.customTableConfig.buttonConfig.buttonList.forEach((ele) => {\n            ele.disabled = false\n          })\n        } else {\n          this.customTableConfig.buttonConfig.buttonList.forEach((ele) => {\n            ele.disabled = true\n          })\n        }\n      }\n    }\n  },\n  created() {\n    this.init()\n  },\n  // mixins: [getGridByCode],\n  methods: {\n    searchForm(data) {\n      console.log(data)\n      this.customTableConfig.currentPage = 1\n      this.onFresh()\n    },\n    resetForm() {\n      this.onFresh()\n    },\n    onFresh() {\n      this.GetWarningConfigPageList()\n    },\n    init() {\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\n      this.GetWarningConfigPageList()\n      this.getNoticeDropDownOption()\n    },\n    async getNoticeDropDownOption() {\n      const res = await GetNoticeDropDownOption({})\n      if (res.IsSucceed) {\n        const result = res.Data || []\n        let noticeType = []\n        let noticeLevel = []\n        result.forEach((element) => {\n          if (element.TypeName === '通知类型') {\n            noticeType = element.Data.map((item) => ({\n              value: item.Value,\n              label: item.Name\n            }))\n          } else if (element.TypeName === '发布层级') {\n            noticeLevel = element.Data.map((item) => ({\n              value: item.Value,\n              label: item.Name\n            }))\n          }\n        })\n        // this.customForm.formItems.find((item) => item.key == \"Type\").options =\n        //   noticeType;\n      }\n    },\n    async GetWarningConfigPageList() {\n      const res = await GetWarningConfigPageList({\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n        ...this.ruleForm\n      })\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data\n        this.customTableConfig.total = res.Data.TotalCount\n        if (this.customTableConfig.tableData.length > 0) {\n          console.log(this.customTableConfig.tableData.length)\n        }\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n\n    async handleMobileMessageCloseEnable(id, status) {\n      const res = await EnableWarningMobileMessageNotice({\n        Ids: [id],\n        Enable: status\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        this.init()\n      }\n    },\n\n    async handleSiteNoticeCloseEnable(id, status) {\n      const res = await EnableWarningSiteNotice({\n        Ids: [id],\n        Enable: status\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        this.init()\n      }\n    },\n\n    async handleCloseEnable(id, status) {\n      const res = await EnableWarningConfig({\n        Ids: [id],\n        Enable: status\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        this.init()\n      }\n    },\n    handleEdit(row, type) {\n      console.log(row, 'row')\n      this.dialogTitle = `告警管理审核`\n      this.dialogVisible = true\n      this.componentsConfig = {\n        ID: row.Id,\n        type,\n        UserIds: row.UserIds,\n        Users: row.Users,\n        RoleIds: row.RoleIds,\n        Roles: row.Roles\n      }\n    },\n    async handleEnable() {\n      const res = await EnableWarningConfig({\n        Ids: this.tableSelection.map((item) => item.Id),\n        Enable: true\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        this.init()\n      }\n    },\n\n    async handleClose() {\n      const res = await EnableWarningConfig({\n        Ids: this.tableSelection.map((item) => item.Id),\n        Enable: false\n      })\n      if (res.IsSucceed) {\n        console.log(res)\n        this.init()\n      }\n    },\n\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.customTableConfig.pageSize = val\n      this.GetWarningConfigPageList()\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`)\n      this.customTableConfig.currentPage = val\n      this.GetWarningConfigPageList()\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.layout {\n  height: 100%;\n  width: 100%;\n  position: absolute;\n  ::v-deep {\n    .CustomLayout {\n      .layoutTable {\n        height: 0;\n        .CustomTable {\n          height: 100%;\n          display: flex;\n          flex-direction: column;\n          .table {\n            flex: 1;\n            height: 0;\n            display: flex;\n            flex-direction: column;\n            .el-table {\n              flex: 1;\n              height: 0;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}