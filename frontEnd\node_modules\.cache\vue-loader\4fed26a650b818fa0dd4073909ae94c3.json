{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\alarmSettings\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\alarmSettings\\index.vue", "mtime": 1755674552422}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/business/eventManagement/alarmSettings", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout\r\n      :layout-config=\"{\r\n        isShowSearchForm: false,\r\n      }\"\r\n    >\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport DialogForm from './dialogForm.vue'\r\n\r\nimport {\r\n  GetWarningConfigPageList,\r\n  GetNoticeDropDownOption,\r\n  EnableWarningSiteNotice,\r\n  EnableWarningMobileMessageNotice,\r\n  EnableWarningConfig,\r\n  SetWarningNoticeObject\r\n} from '@/api/business/eventManagement'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '告警管理审核',\r\n      tableSelection: [],\r\n      ruleForm: {},\r\n      customForm: {\r\n        formItems: [],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '启用',\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载中\r\n              disabled: true, // 是否禁用\r\n              icon: '', //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: 'primary', // primary / success / warning / danger / info / text\r\n              size: 'small', // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleEnable()\r\n              }\r\n            },\r\n            {\r\n              text: '关闭',\r\n              type: 'danger',\r\n              disabled: true, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleClose()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: '100%',\r\n        tableColumns: [\r\n          {\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center',\r\n              fixed: 'left'\r\n            }\r\n          },\r\n          {\r\n            label: '告警层级',\r\n            key: 'SourceTypeDisplay',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '业务模块',\r\n            key: 'Module',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '告警类型',\r\n            key: 'WarningType',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '规则描述',\r\n            key: 'RuleDescription',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '站内通知',\r\n            key: 'EnableSiteNotice',\r\n            otherOptions: {\r\n              align: 'center'\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement('el-switch', {\r\n                props: {\r\n                  value: row.EnableSiteNotice,\r\n                  'active-color': '#13ce66'\r\n                },\r\n                on: {\r\n                  change: (e) => {\r\n                    this.handleSiteNoticeCloseEnable(row.Id, e)\r\n                  }\r\n                }\r\n              })\r\n            }\r\n          },\r\n          {\r\n            label: '短信通知',\r\n            key: 'EnableMobileMessageNotice',\r\n            otherOptions: {\r\n              align: 'center'\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement('el-switch', {\r\n                props: {\r\n                  value: row.EnableMobileMessageNotice,\r\n                  'active-color': '#13ce66'\r\n                },\r\n                on: {\r\n                  change: (e) => {\r\n                    this.handleMobileMessageCloseEnable(row.Id, e)\r\n                  }\r\n                }\r\n              })\r\n            }\r\n          },\r\n          {\r\n            label: '通知角色',\r\n            key: 'Roles',\r\n            otherOptions: {\r\n              align: 'center'\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement(\r\n                'span',\r\n                {\r\n                  style: {\r\n                    color: '#3582fb'\r\n                  },\r\n                  on: {\r\n                    click: () => {\r\n                      this.handleEdit(row, 'role')\r\n                    }\r\n                  }\r\n                },\r\n                row.Roles.join(',') || '添加'\r\n              )\r\n            }\r\n          },\r\n          {\r\n            label: '通知人',\r\n            key: 'Users',\r\n            otherOptions: {\r\n              align: 'center'\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement(\r\n                'span',\r\n                {\r\n                  style: {\r\n                    color: '#3582fb'\r\n                  },\r\n                  on: {\r\n                    click: () => {\r\n                      this.handleEdit(row, 'user')\r\n                    }\r\n                  }\r\n                },\r\n                row.Users.join(',') || '添加'\r\n              )\r\n            }\r\n          },\r\n          {\r\n            label: '执行周期',\r\n            key: 'InformCycleDisplay',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '上次执行时间',\r\n            key: 'LastInformTime',\r\n            width: 180,\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '启用状态',\r\n            key: 'Enable',\r\n            otherOptions: {\r\n              align: 'center'\r\n            },\r\n            render: (row) => {\r\n              return this.$createElement('el-switch', {\r\n                props: {\r\n                  value: row.Enable,\r\n                  'active-color': '#13ce66'\r\n                },\r\n                on: {\r\n                  change: (e) => {\r\n                    this.handleCloseEnable(row.Id, e)\r\n                  }\r\n                }\r\n              })\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center'\r\n        },\r\n        tableActions: []\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    tableSelection: {\r\n      handler(newval, oldval) {\r\n        console.log(newval)\r\n        if (newval.length > 0) {\r\n          this.customTableConfig.buttonConfig.buttonList.forEach((ele) => {\r\n            ele.disabled = false\r\n          })\r\n        } else {\r\n          this.customTableConfig.buttonConfig.buttonList.forEach((ele) => {\r\n            ele.disabled = true\r\n          })\r\n        }\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.init()\r\n  },\r\n  // mixins: [getGridByCode],\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.GetWarningConfigPageList()\r\n    },\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.GetWarningConfigPageList()\r\n      this.getNoticeDropDownOption()\r\n    },\r\n    async getNoticeDropDownOption() {\r\n      const res = await GetNoticeDropDownOption({})\r\n      if (res.IsSucceed) {\r\n        const result = res.Data || []\r\n        let noticeType = []\r\n        let noticeLevel = []\r\n        result.forEach((element) => {\r\n          if (element.TypeName === '通知类型') {\r\n            noticeType = element.Data.map((item) => ({\r\n              value: item.Value,\r\n              label: item.Name\r\n            }))\r\n          } else if (element.TypeName === '发布层级') {\r\n            noticeLevel = element.Data.map((item) => ({\r\n              value: item.Value,\r\n              label: item.Name\r\n            }))\r\n          }\r\n        })\r\n        // this.customForm.formItems.find((item) => item.key == \"Type\").options =\r\n        //   noticeType;\r\n      }\r\n    },\r\n    async GetWarningConfigPageList() {\r\n      const res = await GetWarningConfigPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n        if (this.customTableConfig.tableData.length > 0) {\r\n          console.log(this.customTableConfig.tableData.length)\r\n        }\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n\r\n    async handleMobileMessageCloseEnable(id, status) {\r\n      const res = await EnableWarningMobileMessageNotice({\r\n        Ids: [id],\r\n        Enable: status\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.init()\r\n      }\r\n    },\r\n\r\n    async handleSiteNoticeCloseEnable(id, status) {\r\n      const res = await EnableWarningSiteNotice({\r\n        Ids: [id],\r\n        Enable: status\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.init()\r\n      }\r\n    },\r\n\r\n    async handleCloseEnable(id, status) {\r\n      const res = await EnableWarningConfig({\r\n        Ids: [id],\r\n        Enable: status\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.init()\r\n      }\r\n    },\r\n    handleEdit(row, type) {\r\n      console.log(row, 'row')\r\n      this.dialogTitle = `告警管理审核`\r\n      this.dialogVisible = true\r\n      this.componentsConfig = {\r\n        ID: row.Id,\r\n        type,\r\n        UserIds: row.UserIds,\r\n        Users: row.Users,\r\n        RoleIds: row.RoleIds,\r\n        Roles: row.Roles\r\n      }\r\n    },\r\n    async handleEnable() {\r\n      const res = await EnableWarningConfig({\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n        Enable: true\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.init()\r\n      }\r\n    },\r\n\r\n    async handleClose() {\r\n      const res = await EnableWarningConfig({\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n        Enable: false\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.init()\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.GetWarningConfigPageList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.GetWarningConfigPageList()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: 100%;\r\n  width: 100%;\r\n  position: absolute;\r\n  ::v-deep {\r\n    .CustomLayout {\r\n      .layoutTable {\r\n        height: 0;\r\n        .CustomTable {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          .table {\r\n            flex: 1;\r\n            height: 0;\r\n            display: flex;\r\n            flex-direction: column;\r\n            .el-table {\r\n              flex: 1;\r\n              height: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}