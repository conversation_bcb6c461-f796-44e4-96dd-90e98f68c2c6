{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomTable\\index.vue?vue&type=style&index=0&id=d02c7400&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomTable\\index.vue", "mtime": 1755682470696}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLkN1c3RvbVRhYmxlIHsKICBoZWlnaHQ6IDEwMCU7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIC5idXR0b24gewogICAgZGlzcGxheTogZmxleDsKICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOwogIH0KICAudGFibGUgewogICAgZmxleDogMTsKICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICBtYXJnaW4tdG9wOiAxMHB4OwogIH0KICAucGFnaW5hdGlvbiB7CiAgICBtYXJnaW4tdG9wOiAxMHB4OwogICAgZGlzcGxheTogZmxleDsKICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOwogIH0KICAubm8td3JhcC1jZWxsIHsKICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgfQogIC8vIGRpc3BsYXk6IGZsZXg7CiAgLy8gZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAvLyBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTsKICAvLyAvLyBwYWRkaW5nOiAxMHB4IDE1cHg7CiAgLy8gLnRhYmxlewogIC8vICAgcGFkZGluZzogMnB4IDVweDsKICAvLyB9CiAgLy8gLmVsLXBhZ2luYXRpb257CiAgLy8gICBkaXNwbGF5OiBmbGV4OwogIC8vICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKCiAgLy8gfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4SA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "index.vue", "sourceRoot": "src/businessComponents/CustomTable", "sourcesContent": ["\n<template>\n  <div class=\"CustomTable\">\n    <div\n      v-if=\"customTableObj.buttonConfig.buttonList.length > 0\"\n      class=\"button\"\n    >\n      <CustomButton :custom-button-config=\"customTableObj.buttonConfig\" />\n    </div>\n    <div\n      class=\"table\"\n      :style=\"{\n        marginTop:\n          customTableObj.buttonConfig.buttonList.length == 0 ? 0 : '10px',\n      }\"\n    >\n      <el-table\n        ref=\"table\"\n        v-loading=\"customTableObj.loading\"\n        :data=\"tableData\"\n        :border=\"customTableObj.border == false ? false : true\"\n        :fit=\"true\"\n        stripe\n        :height=\"customTableObj.height ? customTableObj.height : tableHeight\"\n        :max-height=\"\n          customTableObj.height ? customTableObj.height : tableHeight\n        \"\n        style=\"width: 100%\"\n        :row-key=\"showSelection && customTableObj.rowKey ? customTableObj.rowKey : ''\"\n        @selection-change=\"selectionChange\"\n        @select=\"select\"\n        @select-all=\"selectAll\"\n      >\n        <el-table-column\n          v-if=\"showSelection\"\n          type=\"selection\"\n          label=\"selection\"\n          :reserve-selection=\"!!customTableObj.rowKey\"\n        />\n        <el-table-column v-if=\"showIndex\" type=\"index\" label=\"序号\" fixed=\"left\" />\n        <!-- 循环动态列 -->\n        <template v-for=\"(column, index) in tableColumns.columns\">\n          <el-table-column\n            v-if=\"!column.hide\"\n            :key=\"index\"\n            v-bind=\"column.otherOptions\"\n            :prop=\"column.key\"\n            :label=\"column.label\"\n            :min-width=\"column.width\"\n            :resizable=\"true\"\n          >\n            <!-- show-overflow-tooltip -->\n            <!-- :width=\"column.width\" -->\n            <!-- :width=\"flexColumnWidth(column.label, column.key)\" -->\n            <template v-slot=\"scope\">\n              <render-dom\n                v-if=\"column.render\"\n                :render=\"() => column.render(scope.row)\"\n              />\n              <span v-else style=\"white-space: nowrap\">\n                {{\n                  scope.row[column.key] === 0\n                    ? \"0\"\n                    : scope.row[column.key]\n                      ? scope.row[column.key]\n                      : \"-\"\n                }}</span>\n            </template>\n          </el-table-column>\n        </template>\n        <el-table-column\n          v-if=\"tableColumns.actions.length > 0\"\n          label=\"操作\"\n          v-bind=\"customTableObj.operateOptions\"\n          fixed=\"right\"\n          :min-width=\"customTableObj.tableActionsWidth\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              v-for=\"(item, index) in tableColumns.actions\"\n              :key=\"index\"\n              v-bind=\"item.otherOptions\"\n              size=\"mini\"\n              @click=\"item.onclick(scope.$index, scope.row)\"\n            >{{ item.actionLabel }}</el-button>\n            <slot :slot-scope=\"scope.row\" name=\"customBtn\" />\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <div v-if=\"!customTableObj.disablidPagination\" class=\"pagination\">\n      <el-pagination\n        :total=\"customTableObj.total\"\n        :page-sizes=\"customTableObj.pageSizeOptions\"\n        :current-page=\"customTableObj.currentPage\"\n        :page-size=\"customTableObj.pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n      />\n    </div>\n  </div>\n</template>\n<script>\nimport CustomButton from '@/businessComponents/CustomButton/index.vue'\nexport default {\n  components: {\n    CustomButton,\n    renderDom: {\n      functional: true,\n      props: {\n        render: Function\n      },\n      render(createElement, renDom) {\n        return <div>{renDom.props.render()}</div>\n      }\n    }\n  },\n  props: {\n    customTableConfig: {\n      type: Object,\n      default: () => { }\n    }\n  },\n  data() {\n    return {\n      tableHeight: '100%', // 页面高度\n      showIndex: false,\n      showSelection: false\n    }\n  },\n\n  computed: {\n    customTableObj() {\n      return this.customTableConfig\n    },\n    tableColumns() {\n      const jsonArray = this.customTableObj.tableColumns\n      this.showIndex = jsonArray.some(\n        (obj) =>\n          obj.otherOptions &&\n          obj.otherOptions.type &&\n          Object.keys(obj.otherOptions).length > 0 &&\n          obj.otherOptions.type === 'index'\n      )\n      this.showSelection = jsonArray.some(\n        (obj) =>\n          obj.otherOptions &&\n          obj.otherOptions.type &&\n          Object.keys(obj.otherOptions).length > 0 &&\n          obj.otherOptions.type === 'selection'\n      )\n      const newsColumns = jsonArray\n        .filter((obj) => {\n          // 检查 otherOptions 是否存在，并且 type 是否等于 'index'\n          return !obj.otherOptions || obj.otherOptions.type !== 'index'\n        })\n        .filter((obj) => {\n          // 检查 otherOptions 是否存在，并且 type 是否等于 'index'\n          return !obj.otherOptions || obj.otherOptions.type !== 'selection'\n        })\n\n      // .filter(\n      //   (obj) =>\n      //     // obj.otherOptions &&\n      //     // obj.otherOptions.type &&\n      //     // Object.keys(obj.otherOptions).length > 0 &&\n      //     obj.otherOptions.type != \"index\"\n      // )\n      // .filter(\n      //   (obj) =>\n      //     // obj.otherOptions &&\n      //     // obj.otherOptions.type &&\n      //     // Object.keys(obj.otherOptions).length > 0 &&\n      //     obj.otherOptions.type != \"selection\"\n      // );\n      newsColumns.forEach((element) => {\n        element.width = this.flexColumnWidth(element.label, element.key)\n      })\n      const tableWidth = this.getTableWidth()\n      const realWidth = newsColumns.reduce((cur, next) => {\n        return cur + next.width\n      }, 0)\n      console.log(tableWidth, realWidth, 'realWidth')\n\n      newsColumns.forEach((element) => {\n        const width =\n          (tableWidth - this.customTableObj.tableActionsWidth - realWidth) /\n          newsColumns.length\n        if (!element.render) {\n          element.width =\n            tableWidth - this.customTableObj.tableActionsWidth > realWidth\n              ? this.flexColumnWidth(element.label, element.key) + width\n              : this.flexColumnWidth(element.label, element.key)\n        } else {\n          element.width = 140\n        }\n      })\n      return {\n        columns: newsColumns,\n        actions: this.customTableObj.tableActions\n      }\n    },\n    tableData() {\n      const data = []\n      return [].concat(data, this.customTableObj.tableData)\n    }\n  },\n  watch: {\n    // 监视name属性的变化\n    tableColumns(newValue, oldValue) {\n      if (newValue.length > 0) {\n        setTimeout(() => {\n          this.getTableHeight()\n          window.addEventListener('resize', this.getTableHeight) // 监听窗口大小变化，重新计算高度 }\n        }, 0)\n      }\n    }\n  },\n  mounted() { },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.getTableHeight) // 移除事件监听器\n  },\n  methods: {\n    getTableWidth() {\n      // 页面表格宽度\n      const table = document.querySelector('.app-main')\n      if (table) {\n        const tableWidth = table.clientWidth\n        console.log(tableWidth, 'tableWidth')\n        return tableWidth\n      }\n    },\n    getTextWidth(str) {\n      let width = 0\n      const html = document.createElement('span')\n      html.innerText = str\n      html.className = 'getTextWidth'\n      document.querySelector('body').appendChild(html)\n      width = document.querySelector('.getTextWidth').offsetWidth\n      document.querySelector('.getTextWidth').remove()\n      console.log()\n      return width\n    },\n    getMaxLength(arr) {\n      return arr.reduce((acc, item) => {\n        if (item) {\n          const calcLen = this.getTextWidth(item)\n          if (acc < calcLen) {\n            acc = calcLen\n          }\n        }\n        return acc\n      }, 0)\n    },\n    flexColumnWidth(label, prop) {\n      // 1.获取该列的所有数据\n      const arr = this.tableData.map((x) => x[prop])\n      arr.push(label) // 把每列的表头也加进去算\n      // console.log(arr)\n      // 2.计算每列内容最大的宽度 + 表格的内间距（依据实际情况而定）\n      return this.getMaxLength(arr) + 30\n    },\n    getTableHeight() {\n      // 计算页面高度，并减去其他元素的高度（如页眉、页脚等）\n      const pageHeight = document.documentElement.clientHeight\n      const otherElementHeight = 340 // 其他元素的高度，根据实际情况设置\n      this.tableHeight = pageHeight - otherElementHeight\n    },\n    handleSizeChange(val) {\n      this.$emit('handleSizeChange', val)\n    },\n    handleCurrentChange(val) {\n      this.$emit('handleCurrentChange', val)\n    },\n    selectionChange(selection) {\n      this.$emit('handleSelectionChange', selection)\n    },\n    select(selection, row) {\n      this.$emit('select', selection, row)\n    },\n    selectAll(selection) {\n      console.log('ffffffffffffffffff ', selection)\n      this.$emit('selectall', selection, this.tableData)\n    },\n    setSelection(rowList, selected) {\n      rowList.map((tmp) => {\n        const row = this.tableData.find((item) => item.Id === tmp.Id)\n        this.$nextTick(() => {\n          this.$refs.table.toggleRowSelection(row, selected)\n        })\n      })\n    },\n    clearSelection()\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.CustomTable {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  .button {\n    display: flex;\n    flex-direction: row;\n    justify-content: flex-end;\n  }\n  .table {\n    flex: 1;\n    overflow: hidden;\n    margin-top: 10px;\n  }\n  .pagination {\n    margin-top: 10px;\n    display: flex;\n    flex-direction: row;\n    justify-content: flex-end;\n  }\n  .no-wrap-cell {\n    white-space: nowrap;\n  }\n  // display: flex;\n  // flex-direction: column;\n  // background-color: white;\n  // // padding: 10px 15px;\n  // .table{\n  //   padding: 2px 5px;\n  // }\n  // .el-pagination{\n  //   display: flex;\n  //   justify-content: flex-end;\n\n  // }\n}\n</style>\n"]}]}