{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\taskCenter\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\taskCenter\\index.vue", "mtime": 1755674552423}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "getGridByCode", "DialogForm", "downloadFile", "dayjs", "GetTaskPageList", "UpdateTask", "GetTypesByModule", "name", "components", "mixins", "data", "_this", "currentComponent", "componentsConfig", "Data", "componentsFuns", "open", "dialogVisible", "close", "onFresh", "dialogTitle", "tableSelection", "ruleForm", "TaskType", "TaskName", "Status", "TaskBeg", "TaskEnd", "customForm", "formItems", "key", "label", "type", "options", "otherOptions", "clearable", "change", "e", "console", "log", "value", "disabled", "placeholder", "length", "format", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "align", "fixed", "width", "tableData", "operateOptions", "tableActions", "actionLabel", "onclick", "index", "row", "platform", "code", "id", "url", "<PERSON><PERSON><PERSON>", "$qiankun", "switchMicroAppFn", "handleEdit", "computed", "created", "init", "methods", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "result", "typeList", "wrap", "_callee$", "_context", "prev", "next", "Type", "sent", "IsSucceed", "map", "item", "find", "stop", "searchForm", "resetForm", "Date", "getTaskPageList", "_this3", "_callee2", "_callee2$", "_context2", "_objectSpread", "Page", "PageSize", "TotalCount", "$message", "error", "Message", "handleExport", "_this4", "_callee3", "_callee3$", "_context3", "ExportEntranceWarning", "Id", "toString", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "selection"], "sources": ["src/views/business/eventManagement/taskCenter/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport getGridByCode from '../../safetyManagement/mixins/index'\r\nimport DialogForm from './dialogForm.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport dayjs from 'dayjs'\r\n\r\nimport {\r\n  GetTaskPageList,\r\n  UpdateTask,\r\n  GetTypesByModule\r\n} from '@/api/business/eventManagement'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  mixins: [getGridByCode],\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        Data: {}\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '编辑',\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        TaskType: '',\r\n        TaskName: '',\r\n        Status: '0',\r\n        TaskBeg: null,\r\n        TaskEnd: null\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'TaskType',\r\n            label: '任务类型',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'TaskName',\r\n            label: '任务名称',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Status',\r\n            label: '任务状态',\r\n            type: 'select',\r\n            options: [\r\n              {\r\n                label: '全部',\r\n                value: '0'\r\n              },\r\n              {\r\n                label: '未完成',\r\n                value: '1'\r\n              },\r\n              {\r\n                label: '已完成 ',\r\n                value: '2'\r\n              },\r\n              {\r\n                label: '已超期',\r\n                value: '3'\r\n              },\r\n              {\r\n                label: '超期完成',\r\n                value: '4'\r\n              }\r\n            ],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Date', // 字段ID\r\n            label: '任务开始时间', // Form的label\r\n            type: 'datePicker', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              type: 'daterange',\r\n              disabled: false,\r\n              placeholder: '请输入...'\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.TaskBeg = dayjs(e[0]).format('YYYY-MM-DD')\r\n                this.ruleForm.TaskEnd = dayjs(e[1]).format('YYYY-MM-DD')\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: []\r\n          //   {\r\n          //     text: \"导出\",\r\n          //     onclick: (item) => {\r\n          //       console.log(item);\r\n          //       this.handleExport();\r\n          //     },\r\n          //   },\r\n          // ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center',\r\n              fixed: 'left'\r\n            }\r\n          },\r\n          {\r\n            label: '任务开始时间',\r\n            key: 'BegTime',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '计划完成时间',\r\n            key: 'EndTime',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '实际完成时间',\r\n            key: 'DoneTime',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '任务状态',\r\n            key: 'StatusDisplay',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '负责人',\r\n            key: 'ActualReceiveUsersNames',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '任务名称',\r\n            key: 'Name',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n\r\n          {\r\n            label: '通知方式',\r\n            key: 'MessageNoticeModeDisplay',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '任务类型',\r\n            key: 'TaskType',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '来源',\r\n            key: 'SourceName',\r\n            width: 180,\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '业务模块',\r\n            key: 'Module',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '操作人',\r\n            key: 'ModifyUserName',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          },\r\n          {\r\n            label: '备注',\r\n            key: 'Remark',\r\n            otherOptions: {\r\n              align: 'center'\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center',\r\n          width: '180'\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              // this.handleEdit(row);\r\n              const platform = 'digitalfactory'\r\n              const code = 'szgc'\r\n              const id = '97b119f9-e634-4d95-87b0-df2433dc7893'\r\n              let url = ''\r\n              // if (row.Module == \"能耗管理\") {\r\n              //   url = \"/business/energy/alarmDetail\";\r\n              // } else if (row.Module == \"车辆道闸\") {\r\n              //   url = \"/bussiness/vehicle/alarm-info\";\r\n              // } else if (row.Module == \"门禁管理\") {\r\n              //   url = \"/business/AccessControlAlarmDetails\";\r\n              // } else if (row.Module == \"安防管理\") {\r\n              //   url = \"/business/equipmentAlarm\";\r\n              // } else if (row.Module == \"危化品管理\") {\r\n              //   url = \"/business/hazchem/alarmInformation\";\r\n              // } else\r\n              if (row.Module == '环境管理') {\r\n                url = '/business/environment/alarmInformation'\r\n              } else if (row.Module == '访客管理') {\r\n                url = '/business/visitorList'\r\n                console.log('访客管理')\r\n              }\r\n              this.$qiankun.switchMicroAppFn(platform, code, id, url)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(row)\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init()\r\n    this.GetTypesByModule()\r\n  },\r\n  methods: {\r\n    async GetTypesByModule() {\r\n      const res = await GetTypesByModule({\r\n        Type: '3',\r\n        Module: ''\r\n      })\r\n      console.log(res, 'res')\r\n      if (res.IsSucceed) {\r\n        const result = res.Data || []\r\n        const typeList = result.map((item) => ({\r\n          value: item,\r\n          label: item\r\n        }))\r\n        this.customForm.formItems.find(\r\n          (item) => item.key == 'TaskType'\r\n        ).options = typeList\r\n      }\r\n    },\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.TaskBeg = null\r\n      this.ruleForm.TaskEnd = null\r\n      this.ruleForm.Date = null\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getTaskPageList()\r\n    },\r\n\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.getTaskPageList()\r\n    },\r\n    async getTaskPageList() {\r\n      const res = await GetTaskPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n        // if (this.customTableConfig.tableData.length > 0) {\r\n        //   this.customTableConfig.tableData.map((v) => {\r\n        //     v.Warning_First_Time =\r\n        //       (v.Warning_First_Time ?? \"\") != \"\"\r\n        //         ? dayjs(v.Warning_First_Time).format(\"YYYY-MM-DD HH:mm:ss\")\r\n        //         : \"\";\r\n        //     v.Warning_Last_Time =\r\n        //       (v.Warning_Last_Time ?? \"\") != \"\"\r\n        //         ? dayjs(v.Warning_Last_Time).format(\"YYYY-MM-DD HH:mm:ss\")\r\n        //         : \"\";\r\n        //   });\r\n        // }\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    async handleExport() {\r\n      const res = await ExportEntranceWarning({\r\n        id: this.tableSelection.map((item) => item.Id).toString(),\r\n        code: 'AccessControlAlarmDetails1'\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '告警明细数据')\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.getTaskPageList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.getTaskPageList()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    },\r\n    handleEdit(row) {\r\n      this.dialogVisible = true\r\n      this.componentsConfig.Data = row\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  width: 100%;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,OAAAC,aAAA;AACA,OAAAC,UAAA;AAEA,SAAAC,YAAA;AACA,OAAAC,KAAA;AAEA,SACAC,eAAA,EACAC,UAAA,EACAC,gBAAA,IAAAA,iBAAA,QACA;AACA;EACAC,IAAA;EACAC,UAAA;IACAV,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAY,MAAA,GAAAT,aAAA;EACAU,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,gBAAA,EAAAX,UAAA;MACAY,gBAAA;QACAC,IAAA;MACA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAL,KAAA,CAAAM,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAP,KAAA,CAAAM,aAAA;UACAN,KAAA,CAAAQ,OAAA;QACA;MACA;MACAF,aAAA;MACAG,WAAA;MACAC,cAAA;MACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,MAAA;QACAC,OAAA;QACAC,OAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,OAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAE,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,OAAA,GACA;YACAF,KAAA;YACAS,KAAA;UACA,GACA;YACAT,KAAA;YACAS,KAAA;UACA,GACA;YACAT,KAAA;YACAS,KAAA;UACA,GACA;YACAT,KAAA;YACAS,KAAA;UACA,GACA;YACAT,KAAA;YACAS,KAAA;UACA,EACA;UACAN,YAAA;YACAC,SAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAP,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAAA;UACAE,YAAA;YACA;YACAC,SAAA;YACAH,IAAA;YACAS,QAAA;YACAC,WAAA;UACA;UACAN,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;YACA,IAAAA,CAAA,IAAAA,CAAA,CAAAM,MAAA;cACAhC,KAAA,CAAAW,QAAA,CAAAI,OAAA,GAAAvB,KAAA,CAAAkC,CAAA,KAAAO,MAAA;cACAjC,KAAA,CAAAW,QAAA,CAAAK,OAAA,GAAAxB,KAAA,CAAAkC,CAAA,KAAAO,MAAA;YACA;UACA;QACA,EACA;QACAC,KAAA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA,GACA;UACAtB,YAAA;YACAF,IAAA;YACAyB,KAAA;YACAC,KAAA;UACA;QACA,GACA;UACA3B,KAAA;UACAD,GAAA;UACAI,YAAA;YACAuB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAI,YAAA;YACAuB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAI,YAAA;YACAuB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAI,YAAA;YACAuB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAI,YAAA;YACAuB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAI,YAAA;YACAuB,KAAA;UACA;QACA,GAEA;UACA1B,KAAA;UACAD,GAAA;UACAI,YAAA;YACAuB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAI,YAAA;YACAuB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACA6B,KAAA;UACAzB,YAAA;YACAuB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAI,YAAA;YACAuB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAI,YAAA;YACAuB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAI,YAAA;YACAuB,KAAA;UACA;QACA,EACA;QACAG,SAAA;QACAC,cAAA;UACAJ,KAAA;UACAE,KAAA;QACA;QACAG,YAAA,GACA;UACAC,WAAA;UACA7B,YAAA;YACAF,IAAA;UACA;UACAgC,OAAA,WAAAA,QAAAC,KAAA,EAAAC,GAAA;YACA;YACA,IAAAC,QAAA;YACA,IAAAC,IAAA;YACA,IAAAC,EAAA;YACA,IAAAC,GAAA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAAJ,GAAA,CAAAK,MAAA;cACAD,GAAA;YACA,WAAAJ,GAAA,CAAAK,MAAA;cACAD,GAAA;cACAhC,OAAA,CAAAC,GAAA;YACA;YACA5B,KAAA,CAAA6D,QAAA,CAAAC,gBAAA,CAAAN,QAAA,EAAAC,IAAA,EAAAC,EAAA,EAAAC,GAAA;UACA;QACA,GACA;UACAP,WAAA;UACA7B,YAAA;YACAF,IAAA;UACA;UACAgC,OAAA,WAAAA,QAAAC,KAAA,EAAAC,GAAA;YACAvD,KAAA,CAAA+D,UAAA,CAAAR,GAAA;UACA;QACA;MAEA;IACA;EACA;EACAS,QAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;IACA,KAAAvE,gBAAA;EACA;EACAwE,OAAA;IACAxE,gBAAA,WAAAA,iBAAA;MAAA,IAAAyE,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA,EAAAC,MAAA,EAAAC,QAAA;QAAA,OAAAL,mBAAA,GAAAM,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACArF,iBAAA;gBACAsF,IAAA;gBACArB,MAAA;cACA;YAAA;cAHAa,GAAA,GAAAK,QAAA,CAAAI,IAAA;cAIAvD,OAAA,CAAAC,GAAA,CAAA6C,GAAA;cACA,IAAAA,GAAA,CAAAU,SAAA;gBACAT,MAAA,GAAAD,GAAA,CAAAtE,IAAA;gBACAwE,QAAA,GAAAD,MAAA,CAAAU,GAAA,WAAAC,IAAA;kBAAA;oBACAxD,KAAA,EAAAwD,IAAA;oBACAjE,KAAA,EAAAiE;kBACA;gBAAA;gBACAjB,MAAA,CAAAnD,UAAA,CAAAC,SAAA,CAAAoE,IAAA,CACA,UAAAD,IAAA;kBAAA,OAAAA,IAAA,CAAAlE,GAAA;gBAAA,CACA,EAAAG,OAAA,GAAAqD,QAAA;cACA;YAAA;YAAA;cAAA,OAAAG,QAAA,CAAAS,IAAA;UAAA;QAAA,GAAAf,OAAA;MAAA;IACA;IACAgB,UAAA,WAAAA,WAAAzF,IAAA;MACA4B,OAAA,CAAAC,GAAA,CAAA7B,IAAA;MACA,KAAAuC,iBAAA,CAAAI,WAAA;MACA,KAAAlC,OAAA;IACA;IACAiF,SAAA,WAAAA,UAAA;MACA,KAAA9E,QAAA,CAAAI,OAAA;MACA,KAAAJ,QAAA,CAAAK,OAAA;MACA,KAAAL,QAAA,CAAA+E,IAAA;MACA,KAAAlF,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAAmF,eAAA;IACA;IAEAzB,IAAA,WAAAA,KAAA;MACA;MACA,KAAAyB,eAAA;IACA;IACAA,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MAAA,OAAAvB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAsB,SAAA;QAAA,IAAApB,GAAA;QAAA,OAAAH,mBAAA,GAAAM,IAAA,UAAAkB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhB,IAAA,GAAAgB,SAAA,CAAAf,IAAA;YAAA;cAAAe,SAAA,CAAAf,IAAA;cAAA,OACAvF,eAAA,CAAAuG,aAAA;gBACAC,IAAA,EAAAL,MAAA,CAAAtD,iBAAA,CAAAI,WAAA;gBACAwD,QAAA,EAAAN,MAAA,CAAAtD,iBAAA,CAAAK;cAAA,GACAiD,MAAA,CAAAjF,QAAA,CACA;YAAA;cAJA8D,GAAA,GAAAsB,SAAA,CAAAb,IAAA;cAKA,IAAAT,GAAA,CAAAU,SAAA;gBACAS,MAAA,CAAAtD,iBAAA,CAAAW,SAAA,GAAAwB,GAAA,CAAAtE,IAAA,CAAAA,IAAA;gBACAyF,MAAA,CAAAtD,iBAAA,CAAAM,KAAA,GAAA6B,GAAA,CAAAtE,IAAA,CAAAgG,UAAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cACA;gBACAP,MAAA,CAAAQ,QAAA,CAAAC,KAAA,CAAA5B,GAAA,CAAA6B,OAAA;cACA;YAAA;YAAA;cAAA,OAAAP,SAAA,CAAAR,IAAA;UAAA;QAAA,GAAAM,QAAA;MAAA;IACA;IACAU,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,OAAAnC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAkC,SAAA;QAAA,IAAAhC,GAAA;QAAA,OAAAH,mBAAA,GAAAM,IAAA,UAAA8B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5B,IAAA,GAAA4B,SAAA,CAAA3B,IAAA;YAAA;cAAA2B,SAAA,CAAA3B,IAAA;cAAA,OACA4B,qBAAA;gBACAlD,EAAA,EAAA8C,MAAA,CAAA9F,cAAA,CAAA0E,GAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAwB,EAAA;gBAAA,GAAAC,QAAA;gBACArD,IAAA;cACA;YAAA;cAHAgB,GAAA,GAAAkC,SAAA,CAAAzB,IAAA;cAIA,IAAAT,GAAA,CAAAU,SAAA;gBACAxD,OAAA,CAAAC,GAAA,CAAA6C,GAAA;gBACAlF,YAAA,CAAAkF,GAAA,CAAAtE,IAAA;cACA;YAAA;YAAA;cAAA,OAAAwG,SAAA,CAAApB,IAAA;UAAA;QAAA,GAAAkB,QAAA;MAAA;IACA;IACAM,gBAAA,WAAAA,iBAAAC,GAAA;MACArF,OAAA,CAAAC,GAAA,iBAAAqF,MAAA,CAAAD,GAAA;MACA,KAAA1E,iBAAA,CAAAK,QAAA,GAAAqE,GAAA;MACA,KAAArB,eAAA;IACA;IACAuB,mBAAA,WAAAA,oBAAAF,GAAA;MACArF,OAAA,CAAAC,GAAA,wBAAAqF,MAAA,CAAAD,GAAA;MACA,KAAA1E,iBAAA,CAAAI,WAAA,GAAAsE,GAAA;MACA,KAAArB,eAAA;IACA;IACAwB,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA1G,cAAA,GAAA0G,SAAA;IACA;IACArD,UAAA,WAAAA,WAAAR,GAAA;MACA,KAAAjD,aAAA;MACA,KAAAJ,gBAAA,CAAAC,IAAA,GAAAoD,GAAA;IACA;EACA;AACA", "ignoreList": []}]}