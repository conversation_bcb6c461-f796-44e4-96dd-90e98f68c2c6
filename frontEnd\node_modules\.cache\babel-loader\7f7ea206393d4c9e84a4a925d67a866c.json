{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\water\\components\\bar.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\water\\components\\bar.vue", "mtime": 1755743644481}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "use", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "GridComponent", "LegendComponent", "TooltipComponent", "TitleComponent", "DataZoomComponent", "GetWaterAnalyseStatistic", "components", "data", "barOptionRef", "tooltip", "trigger", "xAxis", "type", "axisTick", "show", "axisLine", "grid", "left", "right", "bottom", "containLabel", "yAxis", "nameTextStyle", "color", "series", "name", "symbol", "valueFormatter", "value", "<PERSON><PERSON><PERSON><PERSON>", "itemStyle", "Total", "computed", "parentData", "DateType", "StartTime", "EndTime", "randomInteger", "watch", "handler", "nv", "ov", "console", "log", "fetchData", "created", "inject", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "_res$Data$List", "_res$Data$List2", "xAxisData", "seriesData", "wrap", "_callee$", "_context", "prev", "next", "sent", "IsSucceed", "Data", "List", "map", "item", "Key", "Value", "stop"], "sources": ["src/views/business/energyManagement/pJEnergyAnalysis/water/components/bar.vue"], "sourcesContent": ["<template>\n  <div class=\"chart-container\">\n    <div class=\"title\">\n      <div class=\"count\">\n        共计用水<span style=\"color: #00d3a7\">{{ Total }}</span>吨\n      </div>\n    </div>\n    <div class=\"cs-chart\">\n      <v-chart\n        ref=\"barChartRef\"\n        class=\"barChartDom\"\n        :option=\"barOptionRef\"\n        :autoresize=\"true\"\n      />\n    </div>\n  </div>\n</template>\n\n<script>\nimport VChart from 'vue-echarts'\nimport { use } from 'echarts/core'\nimport { CanvasRenderer } from 'echarts/renderers'\nimport { BarChart } from 'echarts/charts'\nimport {\n  GridComponent,\n  LegendComponent,\n  TooltipComponent,\n  TitleComponent,\n  DataZoomComponent\n} from 'echarts/components'\nimport { GetWaterAnalyseStatistic } from '@/api/business/pJEnergyAnalysis'\nuse([\n  CanvasRenderer,\n  BarChart,\n  DataZoomComponent,\n  GridComponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent\n])\nexport default {\n  components: {\n    VChart\n  },\n  data() {\n    return {\n      barOptionRef: {\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: [],\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: false\n          }\n        },\n        grid: {\n          left: '2%',\n          right: '0%',\n          bottom: '10%',\n          containLabel: true\n        },\n        yAxis: {\n          type: 'value',\n          nameTextStyle: {\n            color: '#888888'\n          }\n        },\n        series: [\n          {\n            name: '共计用水',\n            data: [],\n            type: 'bar',\n            symbol: 'emptyCircle',\n            tooltip: {\n              valueFormatter: function(value) {\n                return value + ' 吨'\n              }\n            },\n            barWidth: 10,\n            itemStyle: {\n              color: '#00D3A7'\n            }\n          }\n        ]\n      },\n      Total: 0\n    }\n  },\n  computed: {\n    parentData() {\n      return {\n        DateType: this.DateType(),\n        StartTime: this.StartTime(),\n        EndTime: this.EndTime(),\n        randomInteger: this.randomInteger()\n      }\n    }\n  },\n  watch: {\n    parentData: {\n      handler(nv, ov) {\n        console.log(12)\n        this.fetchData()\n      }\n    }\n  },\n  created() {\n    this.fetchData()\n  },\n  inject: ['DateType', 'StartTime', 'EndTime', 'randomInteger'],\n  methods: {\n    async fetchData() {\n      const res = await GetWaterAnalyseStatistic(this.parentData)\n      if (res.IsSucceed) {\n        this.Total = res.Data.Total\n        const xAxisData = (res.Data.List ?? []).map(item => item.Key)\n        const seriesData = (res.Data.List ?? []).map(item => item.Value)\n        this.barOptionRef.xAxis.data = xAxisData\n        this.barOptionRef.series[0].data = seriesData\n      }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.chart-container {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  padding: 16px 24px;\n  box-sizing: border-box;\n  background: #fff;\n  .title {\n    height: 40px;\n    display: flex;\n    align-items: center;\n    // justify-content: space-between;\n    padding-top: 16px;\n    .count {\n      font-weight: bold;\n      font-size: 16px;\n      color: #666666;\n      margin-right: 8px;\n      > span {\n        margin: 0 5px;\n      }\n    }\n    .lengend {\n      > span {\n        display: inline-block;\n        width: 10px;\n        height: 4px;\n        margin-right: 8px;\n      }\n      font-size: 12px;\n      color: #999999;\n      display: flex;\n      align-items: center;\n    }\n  }\n\n  .cs-chart {\n    flex: 1;\n\n    .no-data {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 26px;\n      width: 100%;\n      height: 100%;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAmBA,OAAAA,MAAA;AACA,SAAAC,GAAA;AACA,SAAAC,cAAA;AACA,SAAAC,QAAA;AACA,SACAC,aAAA,EACAC,eAAA,EACAC,gBAAA,EACAC,cAAA,EACAC,iBAAA,QACA;AACA,SAAAC,wBAAA;AACAR,GAAA,EACAC,cAAA,EACAC,QAAA,EACAK,iBAAA,EACAJ,aAAA,EACAC,eAAA,EACAE,cAAA,EACAD,gBAAA,CACA;AACA;EACAI,UAAA;IACAV,MAAA,EAAAA;EACA;EACAW,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;QACAC,OAAA;UACAC,OAAA;QACA;QACAC,KAAA;UACAC,IAAA;UACAL,IAAA;UACAM,QAAA;YACAC,IAAA;UACA;UACAC,QAAA;YACAD,IAAA;UACA;QACA;QACAE,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAC,YAAA;QACA;QACAC,KAAA;UACAT,IAAA;UACAU,aAAA;YACAC,KAAA;UACA;QACA;QACAC,MAAA,GACA;UACAC,IAAA;UACAlB,IAAA;UACAK,IAAA;UACAc,MAAA;UACAjB,OAAA;YACAkB,cAAA,WAAAA,eAAAC,KAAA;cACA,OAAAA,KAAA;YACA;UACA;UACAC,QAAA;UACAC,SAAA;YACAP,KAAA;UACA;QACA;MAEA;MACAQ,KAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA;QACAC,QAAA,OAAAA,QAAA;QACAC,SAAA,OAAAA,SAAA;QACAC,OAAA,OAAAA,OAAA;QACAC,aAAA,OAAAA,aAAA;MACA;IACA;EACA;EACAC,KAAA;IACAL,UAAA;MACAM,OAAA,WAAAA,QAAAC,EAAA,EAAAC,EAAA;QACAC,OAAA,CAAAC,GAAA;QACA,KAAAC,SAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,SAAA;EACA;EACAE,MAAA;EACAC,OAAA;IACAH,SAAA,WAAAA,UAAA;MAAA,IAAAI,KAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,SAAA,EAAAC,UAAA;QAAA,OAAAP,mBAAA,GAAAQ,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAzD,wBAAA,CAAA2C,KAAA,CAAAf,UAAA;YAAA;cAAAoB,GAAA,GAAAO,QAAA,CAAAG,IAAA;cACA,IAAAV,GAAA,CAAAW,SAAA;gBACAhB,KAAA,CAAAjB,KAAA,GAAAsB,GAAA,CAAAY,IAAA,CAAAlC,KAAA;gBACAyB,SAAA,KAAAF,cAAA,GAAAD,GAAA,CAAAY,IAAA,CAAAC,IAAA,cAAAZ,cAAA,cAAAA,cAAA,OAAAa,GAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAC,GAAA;gBAAA;gBACAZ,UAAA,KAAAF,eAAA,GAAAF,GAAA,CAAAY,IAAA,CAAAC,IAAA,cAAAX,eAAA,cAAAA,eAAA,OAAAY,GAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAE,KAAA;gBAAA;gBACAtB,KAAA,CAAAxC,YAAA,CAAAG,KAAA,CAAAJ,IAAA,GAAAiD,SAAA;gBACAR,KAAA,CAAAxC,YAAA,CAAAgB,MAAA,IAAAjB,IAAA,GAAAkD,UAAA;cACA;YAAA;YAAA;cAAA,OAAAG,QAAA,CAAAW,IAAA;UAAA;QAAA,GAAAnB,OAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}