<template>
  <div class="app-container abs100 analysisBox">
    <top class="top" :params-obj="warningTotal" />
    <bottom class="bottom" :params-arr="paramsArr" />
  </div>
</template>

<script>
import Top from './components/top'
import Bottom from './components/bottom'
import { GetJumpUrl } from '@/api/business/PJbehaviorAnalysis'
export default {
  components: {
    Top,
    Bottom
  },
  data() {
    return {
      warningTotal: {},
      paramsArr: []
    }
  },
  created() {
    this.getJumpUrl()
  },
  mounted() {

  },
  methods: {
    async getJumpUrl() {
      const res = await GetJumpUrl()
      if (res.IsSucceed) {
        res.Data.map(i => {
          if (i.ViewMore == '行为告警总数') {
            this.warningTotal = i
          } else {
            this.paramsArr.push(i)
          }
        })
      }
    }
  }
}
</script>
<style scoped lang='scss'>
.analysisBox {
  // padding: 10px 15px;
  // height: calc(100vh - 90px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  .top,
  .bottom {
    display: flex;
    flex: 1;
  }
}
</style>
