{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\hazardousChemicals\\alarmInformation\\index.vue?vue&type=style&index=0&id=253ac59c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\hazardousChemicals\\alarmInformation\\index.vue", "mtime": 1755506574331}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoubGF5b3V0IHsKICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA5MHB4KTsKICBvdmVyZmxvdzogYXV0bzsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8hBA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/hazardousChemicals/alarmInformation", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <CustomLayout>\n      <template v-slot:searchForm>\n        <CustomForm\n          :custom-form-items=\"customForm.formItems\"\n          :custom-form-buttons=\"customForm.customFormButtons\"\n          :value=\"ruleForm\"\n          :inline=\"true\"\n          :rules=\"customForm.rules\"\n          @submitForm=\"searchForm\"\n          @resetForm=\"resetForm\"\n        />\n      </template>\n      <template v-slot:layoutTable>\n        <CustomTable\n          :custom-table-config=\"customTableConfig\"\n          @handleSizeChange=\"handleSizeChange\"\n          @handleCurrentChange=\"handleCurrentChange\"\n          @handleSelectionChange=\"handleSelectionChange\"\n          ><template #customBtn=\"{ slotScope }\"\n            ><el-button\n              v-if=\"slotScope.Handle_Status == 1\"\n              type=\"text\"\n              @click=\"handelClose(slotScope)\"\n              >关闭</el-button\n            ></template\n          ></CustomTable\n        >\n      </template>\n    </CustomLayout>\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <component\n        :is=\"currentComponent\"\n        v-if=\"dialogVisible\"\n        :components-config=\"componentsConfig\"\n        :components-funs=\"componentsFuns\"\n    /></el-dialog>\n  </div>\n</template>\n\n<script>\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\n\nimport DialogForm from \"./dialogForm.vue\";\nimport DialogFormLook from \"./dialogFormLook.vue\";\n\nimport { downloadFile } from \"@/utils/downloadFile\";\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\nimport { deviceTypeMixins } from \"../../mixins/deviceType.js\";\nimport {\n  GetWarningList,\n  GetWarningType,\n  ExportWarning,\n  UpdateWarningStatus,\n} from \"@/api/business/hazardousChemicals\";\n// import * as moment from 'moment'\nimport dayjs from \"dayjs\";\n\nexport default {\n  name: \"\",\n  components: {\n    CustomTable,\n    // CustomButton,\n    // CustomTitle,\n    CustomForm,\n    CustomLayout,\n  },\n  mixins: [deviceTypeMixins],\n  data() {\n    return {\n      currentComponent: DialogForm,\n      componentsConfig: {},\n      componentsFuns: {\n        open: () => {\n          this.dialogVisible = true;\n        },\n        close: () => {\n          this.dialogVisible = false;\n          this.onFresh();\n        },\n      },\n      dialogVisible: false,\n      dialogTitle: \"\",\n      tableSelection: [],\n\n      ruleForm: {\n        Content: \"\",\n        EqtType: \"\",\n        Position: \"\",\n      },\n      customForm: {\n        formItems: [\n          {\n            key: \"Content\", // 字段ID\n            label: \"\", // Form的label\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"输入设备编号或名称进行搜�?,\n            },\n            width: \"240px\",\n            change: (e) => {\n              // change事件\n              console.log(e);\n            },\n          },\n          {\n            key: \"EqtType\",\n            label: \"设备类型\",\n            type: \"select\",\n\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"请选择设备类型\",\n            },\n            options: [],\n            change: (e) => {\n              console.log(e);\n            },\n          },\n          {\n            key: \"WarningType\",\n            label: \"告警类型\",\n            type: \"select\",\n\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"请选择告警类型\",\n            },\n            options: [],\n            change: (e) => {\n              console.log(e);\n            },\n          },\n          {\n            key: \"Handle_Status\",\n            label: \"告警状�?,\n            type: \"select\",\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"请选择告警状�?,\n            },\n            options: [\n              {\n                label: \"告警�?,\n                value: 1,\n              },\n              {\n                label: \"已关�?,\n                value: 2,\n              },\n              // {\n              //   label: '已处�?,\n              //   value: 3\n              // }\n            ],\n            change: (e) => {\n              console.log(e);\n            },\n          },\n          {\n            key: \"Position\", // 字段ID\n            label: \"安装位置\", // Form的label\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\n              // 除了model以外的其他的参数,具体请参考element文档\n              clearable: true,\n              placeholder: \"请输入安装位�?,\n            },\n            change: (e) => {\n              // change事件\n              console.log(e);\n            },\n          },\n        ],\n        rules: {\n          // 请参照elementForm rules\n        },\n        customFormButtons: {\n          submitName: \"查询\",\n          resetName: \"重置\",\n        },\n      },\n      customTableConfig: {\n        buttonConfig: {\n          buttonList: [\n            // {\n            //   text: '新增',\n            //   round: false, // 是否圆角\n            //   plain: false, // 是否朴素\n            //   circle: false, // 是否圆形\n            //   loading: false, // 是否加载�?            //   disabled: false, // 是否禁用\n            //   icon: '', //  图标\n            //   autofocus: false, // 是否聚焦\n            //   type: 'primary', // primary / success / warning / danger / info / text\n            //   size: 'small', // medium / small / mini\n            //   onclick: (item) => {\n            //     console.log(item)\n            //     this.handleCreate()\n            //   }\n            // },\n            // {\n            //   text: '导出',\n            //   onclick: (item) => {\n            //     console.log(item)\n            //     this.handleExport()\n            //   }\n            // },\n            {\n              text: \"批量导出\",\n              onclick: (item) => {\n                console.log(item);\n                this.handleAllExport();\n              },\n            },\n          ],\n        },\n        // 表格\n        loading: false,\n        pageSizeOptions: [10, 20, 50, 80],\n        currentPage: 1,\n        pageSize: 20,\n        total: 0,\n        tableColumns: [\n          // {\n          //   width: 50,\n          //   otherOptions: {\n          //     type: 'selection',\n          //     align: 'center'\n          //   }\n          // },\n          {\n            width: 60,\n            label: \"序号\",\n            otherOptions: {\n              type: \"index\",\n              align: \"center\",\n            }, // key\n            // otherOptions: {\n            //   width: 180, // 宽度\n            //   fixed: 'left', // left, right\n            //   align: 'center' //\tleft/center/right\n            // }\n          },\n          {\n            label: \"告警时间\",\n            key: \"Time\",\n            otherOptions: {\n              fixed: \"left\",\n            },\n          },\n          // {\n          //   label: \"告警事件名称\",\n          //   key: \"EqtNameType\",\n          //   otherOptions: {\n          //     fixed: \"left\",\n          //   },\n          // },\n          {\n            label: \"告警编号\",\n            key: \"WId\",\n            width: 160,\n          },\n          {\n            label: \"告警设备编号\",\n            key: \"EId\",\n          },\n          {\n            label: \"告警事件名称\",\n            key: \"EnvEventName\",\n          },\n          {\n            label: \"告警类型\",\n            key: \"Type\",\n            width: 90,\n          },\n          {\n            label: \"触发�?,\n            key: \"TriggerItem\",\n            width: 90,\n          },\n          {\n            label: \"触发�?,\n            key: \"WarningValue\",\n            width: 90,\n          },\n          {\n            label: \"安装位置\",\n            key: \"Position\",\n          },\n          {\n            label: \"告警状�?,\n            key: \"HandleStatusStr\",\n          },\n          {\n            label: \"操作�?,\n            key: \"Handler_UserName\",\n          },\n          {\n            label: \"操作时间\",\n            key: \"Handle_Time\",\n          },\n        ],\n        tableData: [],\n        operateOptions: {\n          width: 200,\n        },\n        tableActions: [\n          // {\n          //   actionLabel: '关闭',\n          //   otherOptions: {\n          //     type: 'text'\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handelClose(row)\n          //   }\n          // },\n          {\n            actionLabel: \"查看\",\n            otherOptions: {\n              type: \"text\",\n            },\n            onclick: (index, row) => {\n              this.handleEdit(index, row, \"view\");\n            },\n          },\n          // {\n          //   actionLabel: '编辑',\n          //   otherOptions: {\n          //     type: 'text'\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handleEdit(index, row, 'edit')\n          //   }\n          // },\n          // {\n          //   actionLabel: '删除',\n          //   otherOptions: {\n          //     type: 'text'\n          //   },\n          //   onclick: (index, row) => {\n          //     this.handleDelete(index, row)\n          //   }\n          // }\n        ],\n      },\n    };\n  },\n  computed: {},\n  mounted() {\n    this.init();\n    this.initDeviceType(\"EqtType\", \"HazchemEqtType\");\n  },\n  methods: {\n    searchForm(data) {\n      console.log(data);\n      this.customTableConfig.currentPage = 1;\n      this.onFresh();\n    },\n    resetForm() {\n      this.onFresh();\n    },\n    onFresh() {\n      this.GetWarningList();\n    },\n    init() {\n      this.GetWarningList();\n      this.GetWarningType();\n    },\n    async GetWarningList() {\n      this.customTableConfig.loading = true;\n      const res = await GetWarningList({\n        ParameterJson: [\n          {\n            Key: \"\",\n            Value: [null],\n            Type: \"\",\n            Filter_Type: \"\",\n          },\n        ],\n        Page: this.customTableConfig.currentPage,\n        PageSize: this.customTableConfig.pageSize,\n\n        SortName: \"\",\n        SortOrder: \"\",\n        Search: \"\",\n        Content: \"\",\n        EqtType: \"\",\n        Position: \"\",\n        IsAll: true,\n        ...this.ruleForm,\n      });\n      this.customTableConfig.loading = false;\n      if (res.IsSucceed) {\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\n          ...item,\n          Time: dayjs(item.Time).format(\"YYYY-MM-DD HH:mm:ss\"),\n        }));\n        console.log(res);\n        this.customTableConfig.total = res.Data.TotalCount;\n      } else {\n        this.$message.error(res.Message);\n      }\n    },\n    async GetWarningType() {\n      const res = await GetWarningType({});\n      if (res.IsSucceed) {\n        console.log(res, \"res\");\n        this.customForm.formItems.find(\n          (item, index) => item.key === \"WarningType\"\n        ).options = res.Data.map((item) => ({\n          label: item.Type,\n          value: item.Type,\n        }));\n        // console.log(res)\n      } else {\n        this.$message.error(res.Message);\n      }\n    },\n    handleCreate() {\n      this.dialogTitle = \"新增\";\n      this.componentsConfig = {\n        disabled: false,\n        title: \"新增\",\n      };\n      this.dialogVisible = true;\n    },\n    // handleDelete(index, row) {\n    //   console.log(index, row)\n    //   console.log(this)\n    //   this.$confirm('该操作将在监测设备档案中删除该设备信�?请确认是否删�?', '删除', {\n    //     type: 'error'\n    //   })\n    //     .then(async(_) => {\n    //       const res = await DeleteEquipment({\n    //         IDs: [row.ID]\n    //       })\n    //       if (res.IsSucceed) {\n    //         this.init()\n    //       } else {\n    //         this.$message.error(res.Message)\n    //       }\n    //     })\n    //     .catch((_) => {})\n    // },\n    handleEdit(index, row, type) {\n      console.log(index, row, type);\n      this.dialogVisible = true;\n      if (type === \"view\") {\n        this.dialogTitle = \"查看\";\n        this.currentComponent = DialogForm;\n        this.componentsConfig = {\n          ID: row.ID,\n          disabled: true,\n          title: \"查看\",\n          row: row,\n        };\n      }\n      // else if (type === 'edit') {\n      //   this.dialogTitle = '编辑'\n      //   this.componentsConfig = {\n      //     ID: row.ID,\n      //     disabled: false,\n      //     title: '编辑'\n      //   }\n      // }\n    },\n    // async handleExport() {\n    //   console.log(this.ruleForm)\n    //   const res = await ExportWarning({\n    //     Content: '',\n    //     EqtType: '',\n    //     Position: '',\n    //     IsAll: false,\n    //     Ids: this.tableSelection.map((item) => item.ID),\n    //     ...this.ruleForm\n    //   })\n    //   if (res.IsSucceed) {\n    //     console.log(res)\n    //     downloadFile(res.Data, '21')\n    //   } else {\n    //     this.$message.error(res.Message)\n    //   }\n    // },\n    async handleAllExport() {\n      const res = await ExportWarning({\n        Content: \"\",\n        EqtType: \"\",\n        Position: \"\",\n        IsAll: true,\n        Ids: [],\n        ...this.ruleForm,\n      });\n      if (res.IsSucceed) {\n        console.log(res);\n        downloadFile(res.Data, \"21\");\n      } else {\n        this.$message.error(res.Message);\n      }\n    },\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`);\n      this.customTableConfig.pageSize = val;\n      this.init();\n    },\n    handleCurrentChange(val) {\n      console.log(`当前�? ${val}`);\n      this.customTableConfig.currentPage = val;\n      this.init();\n    },\n    handleSelectionChange(selection) {\n      this.tableSelection = selection;\n    },\n    handelClose(row) {\n      if (row.HandleStatusStr == \"关闭\") {\n        this.$message.warning(\"请勿重复操作\");\n      } else {\n        UpdateWarningStatus({ id: row.Id, wid: row.WId, StatusEnum: 2 }).then(\n          (res) => {\n            if (res.IsSucceed) {\n              this.$message.success(\"操作成功\");\n              this.init();\n            } else {\n              this.$message.error(res.Message);\n            }\n          }\n        );\n      }\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.layout {\n  height: calc(100vh - 90px);\n  overflow: auto;\n}\n</style>\n"]}]}