{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\behavioralAnalysisDashboard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\behaviorAnalysis\\behavioralAnalysisDashboard\\index.vue", "mtime": 1755674552414}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVG9wIGZyb20gJy4vY29tcG9uZW50cy90b3AnDQppbXBvcnQgQm90dG9tIGZyb20gJy4vY29tcG9uZW50cy9ib3R0b20nDQppbXBvcnQgeyBHZXRKdW1wVXJsIH0gZnJvbSAnQC9hcGkvYnVzaW5lc3MvUEpiZWhhdmlvckFuYWx5c2lzJw0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7DQogICAgVG9wLA0KICAgIEJvdHRvbQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB3YXJuaW5nVG90YWw6IHt9LA0KICAgICAgcGFyYW1zQXJyOiBbXQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldEp1bXBVcmwoKQ0KICB9LA0KICBtb3VudGVkKCkgew0KDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBhc3luYyBnZXRKdW1wVXJsKCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0SnVtcFVybCgpDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICByZXMuRGF0YS5tYXAoaSA9PiB7DQogICAgICAgICAgaWYgKGkuVmlld01vcmUgPT0gJ+ihjOS4uuWRiuitpuaAu+aVsCcpIHsNCiAgICAgICAgICAgIHRoaXMud2FybmluZ1RvdGFsID0gaQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLnBhcmFtc0Fyci5wdXNoKGkpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/behaviorAnalysis/behavioralAnalysisDashboard", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 analysisBox\">\r\n    <top class=\"top\" :params-obj=\"warningTotal\" />\r\n    <bottom class=\"bottom\" :params-arr=\"paramsArr\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Top from './components/top'\r\nimport Bottom from './components/bottom'\r\nimport { GetJumpUrl } from '@/api/business/PJbehaviorAnalysis'\r\nexport default {\r\n  components: {\r\n    Top,\r\n    Bottom\r\n  },\r\n  data() {\r\n    return {\r\n      warningTotal: {},\r\n      paramsArr: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getJumpUrl()\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n    async getJumpUrl() {\r\n      const res = await GetJumpUrl()\r\n      if (res.IsSucceed) {\r\n        res.Data.map(i => {\r\n          if (i.ViewMore == '行为告警总数') {\r\n            this.warningTotal = i\r\n          } else {\r\n            this.paramsArr.push(i)\r\n          }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang='scss'>\r\n.analysisBox {\r\n  // padding: 10px 15px;\r\n  // height: calc(100vh - 90px);\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .top,\r\n  .bottom {\r\n    display: flex;\r\n    flex: 1;\r\n  }\r\n}\r\n</style>\r\n"]}]}