
<template>
  <div class="CustomTable">
    <div
      v-if="customTableObj.buttonConfig.buttonList.length > 0"
      class="button"
    >
      <CustomButton :custom-button-config="customTableObj.buttonConfig" />
    </div>
    <div
      class="table"
      :style="{
        marginTop:
          customTableObj.buttonConfig.buttonList.length == 0 ? 0 : '10px',
      }"
    >
      <el-table
        ref="table"
        v-loading="customTableObj.loading"
        :data="tableData"
        :border="customTableObj.border == false ? false : true"
        :fit="true"
        stripe
        :height="customTableObj.height ? customTableObj.height : tableHeight"
        :max-height="
          customTableObj.height ? customTableObj.height : tableHeight
        "
        style="width: 100%"
        :row-key="showSelection && customTableObj.rowKey ? customTableObj.rowKey : ''"
        @selection-change="selectionChange"
        @select="select"
        @select-all="selectAll"
      >
        <el-table-column
          v-if="showSelection"
          type="selection"
          label="selection"
          :reserve-selection="!!customTableObj.rowKey"
        />
        <el-table-column v-if="showIndex" type="index" label="序号" fixed="left" />
        <!-- 循环动态列 -->
        <template v-for="(column, index) in tableColumns.columns">
          <el-table-column
            v-if="!column.hide"
            :key="index"
            v-bind="column.otherOptions"
            :prop="column.key"
            :label="column.label"
            :min-width="column.width"
            :resizable="true"
          >
            <!-- show-overflow-tooltip -->
            <!-- :width="column.width" -->
            <!-- :width="flexColumnWidth(column.label, column.key)" -->
            <template v-slot="scope">
              <render-dom
                v-if="column.render"
                :render="() => column.render(scope.row)"
              />
              <span v-else style="white-space: nowrap">
                {{
                  scope.row[column.key] === 0
                    ? "0"
                    : scope.row[column.key]
                      ? scope.row[column.key]
                      : "-"
                }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column
          v-if="tableColumns.actions.length > 0"
          label="操作"
          v-bind="customTableObj.operateOptions"
          fixed="right"
          :min-width="customTableObj.tableActionsWidth"
        >
          <template slot-scope="scope">
            <el-button
              v-for="(item, index) in tableColumns.actions"
              :key="index"
              v-bind="item.otherOptions"
              size="mini"
              @click="item.onclick(scope.$index, scope.row)"
            >{{ item.actionLabel }}</el-button>
            <slot :slot-scope="scope.row" name="customBtn" />
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div v-if="!customTableObj.disablidPagination" class="pagination">
      <el-pagination
        :total="customTableObj.total"
        :page-sizes="customTableObj.pageSizeOptions"
        :current-page="customTableObj.currentPage"
        :page-size="customTableObj.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import CustomButton from '@/businessComponents/CustomButton/index.vue'
export default {
  components: {
    CustomButton,
    renderDom: {
      functional: true,
      props: {
        render: Function
      },
      render(createElement, renDom) {
        return <div>{renDom.props.render()}</div>
      }
    }
  },
  props: {
    customTableConfig: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      tableHeight: '100%', // 页面高度
      showIndex: false,
      showSelection: false
    }
  },

  computed: {
    customTableObj() {
      return this.customTableConfig
    },
    tableColumns() {
      const jsonArray = this.customTableObj.tableColumns
      this.showIndex = jsonArray.some(
        (obj) =>
          obj.otherOptions &&
          obj.otherOptions.type &&
          Object.keys(obj.otherOptions).length > 0 &&
          obj.otherOptions.type === 'index'
      )
      this.showSelection = jsonArray.some(
        (obj) =>
          obj.otherOptions &&
          obj.otherOptions.type &&
          Object.keys(obj.otherOptions).length > 0 &&
          obj.otherOptions.type === 'selection'
      )
      const newsColumns = jsonArray
        .filter((obj) => {
          // 检查 otherOptions 是否存在，并且 type 是否等于 'index'
          return !obj.otherOptions || obj.otherOptions.type !== 'index'
        })
        .filter((obj) => {
          // 检查 otherOptions 是否存在，并且 type 是否等于 'index'
          return !obj.otherOptions || obj.otherOptions.type !== 'selection'
        })

      // .filter(
      //   (obj) =>
      //     // obj.otherOptions &&
      //     // obj.otherOptions.type &&
      //     // Object.keys(obj.otherOptions).length > 0 &&
      //     obj.otherOptions.type != "index"
      // )
      // .filter(
      //   (obj) =>
      //     // obj.otherOptions &&
      //     // obj.otherOptions.type &&
      //     // Object.keys(obj.otherOptions).length > 0 &&
      //     obj.otherOptions.type != "selection"
      // );
      newsColumns.forEach((element) => {
        element.width = this.flexColumnWidth(element.label, element.key)
      })
      const tableWidth = this.getTableWidth()
      const realWidth = newsColumns.reduce((cur, next) => {
        return cur + next.width
      }, 0)
      console.log(tableWidth, realWidth, 'realWidth')

      newsColumns.forEach((element) => {
        const width =
          (tableWidth - this.customTableObj.tableActionsWidth - realWidth) /
          newsColumns.length
        if (!element.render) {
          element.width =
            tableWidth - this.customTableObj.tableActionsWidth > realWidth
              ? this.flexColumnWidth(element.label, element.key) + width
              : this.flexColumnWidth(element.label, element.key)
        } else {
          element.width = 140
        }
      })
      return {
        columns: newsColumns,
        actions: this.customTableObj.tableActions
      }
    },
    tableData() {
      const data = []
      return [].concat(data, this.customTableObj.tableData)
    }
  },
  watch: {
    // 监视name属性的变化
    tableColumns(newValue, oldValue) {
      if (newValue.length > 0) {
        setTimeout(() => {
          this.getTableHeight()
          window.addEventListener('resize', this.getTableHeight) // 监听窗口大小变化，重新计算高度 }
        }, 0)
      }
    }
  },
  mounted() { },
  beforeDestroy() {
    window.removeEventListener('resize', this.getTableHeight) // 移除事件监听器
  },
  methods: {
    getTableWidth() {
      // 页面表格宽度
      const table = document.querySelector('.app-main')
      if (table) {
        const tableWidth = table.clientWidth
        console.log(tableWidth, 'tableWidth')
        return tableWidth
      }
    },
    getTextWidth(str) {
      let width = 0
      const html = document.createElement('span')
      html.innerText = str
      html.className = 'getTextWidth'
      document.querySelector('body').appendChild(html)
      width = document.querySelector('.getTextWidth').offsetWidth
      document.querySelector('.getTextWidth').remove()
      console.log()
      return width
    },
    getMaxLength(arr) {
      return arr.reduce((acc, item) => {
        if (item) {
          const calcLen = this.getTextWidth(item)
          if (acc < calcLen) {
            acc = calcLen
          }
        }
        return acc
      }, 0)
    },
    flexColumnWidth(label, prop) {
      // 1.获取该列的所有数据
      const arr = this.tableData.map((x) => x[prop])
      arr.push(label) // 把每列的表头也加进去算
      // console.log(arr)
      // 2.计算每列内容最大的宽度 + 表格的内间距（依据实际情况而定）
      return this.getMaxLength(arr) + 30
    },
    getTableHeight() {
      // 计算页面高度，并减去其他元素的高度（如页眉、页脚等）
      const pageHeight = document.documentElement.clientHeight
      const otherElementHeight = 340 // 其他元素的高度，根据实际情况设置
      this.tableHeight = pageHeight - otherElementHeight
    },
    handleSizeChange(val) {
      this.$emit('handleSizeChange', val)
    },
    handleCurrentChange(val) {
      this.$emit('handleCurrentChange', val)
    },
    selectionChange(selection) {
      this.$emit('handleSelectionChange', selection)
    },
    select(selection, row) {
      this.$emit('select', selection, row)
    },
    selectAll(selection) {
      console.log('ffffffffffffffffff ', selection)
      this.$emit('selectall', selection, this.tableData)
    },
    setSelection(rowList, selected) {
      rowList.map((tmp) => {
        const row = this.tableData.find((item) => item.Id === tmp.Id)
        this.$nextTick(() => {
          this.$refs.table.toggleRowSelection(row, selected)
        })
      })
    },
    setClearSelection() {
      this.$refs.table.clearSelection()
    }
  }
}
</script>

<style lang="scss" scoped>
.CustomTable {
  height: 100%;
  display: flex;
  flex-direction: column;
  .button {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }
  .table {
    flex: 1;
    overflow: hidden;
    margin-top: 10px;
  }
  .pagination {
    margin-top: 10px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }
  .no-wrap-cell {
    white-space: nowrap;
  }
  // display: flex;
  // flex-direction: column;
  // background-color: white;
  // // padding: 10px 15px;
  // .table{
  //   padding: 2px 5px;
  // }
  // .el-pagination{
  //   display: flex;
  //   justify-content: flex-end;

  // }
}
</style>
