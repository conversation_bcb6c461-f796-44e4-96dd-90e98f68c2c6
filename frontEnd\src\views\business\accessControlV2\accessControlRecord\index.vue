<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        >
          <template #formSlot="{ slotScope }">
            <el-tree-select
              ref="treeSelectArea"
              v-model="ruleForm.Dept"
              :select-params="{
                clearable: true,
              }"
              @searchFun="searchFun"
              class="customTreeSelect"
              :tree-params="categoryOptions"
            />
          </template>
        </CustomForm>
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </CustomLayout>
  </div>
</template>

<script>
import CustomLayout from "@/businessComponents/CustomLayout/index.vue";
import CustomTable from "@/businessComponents/CustomTable/index.vue";
import CustomForm from "@/businessComponents/CustomForm/index.vue";
import { downloadFile } from "@/utils/downloadFile";
import dayjs from "dayjs";
import {
  ExportEntranceEquipmentList,
  GetDictionaryDetailListByCode,
  GetTrafficRecordPageList,
  GetCompanyList,
} from "@/api/business/accessControl";
import addRouterPage from "@/mixins/add-router-page";
import { GetDepartmentTree } from "@/api/sys";

export default {
  name: "",
  components: {
    CustomTable,
    CustomForm,
    CustomLayout,
  },
  data() {
    return {
      componentsConfig: {},
      componentsFuns: {
        open: () => {
          this.dialogVisible = true;
        },
        close: () => {
          this.dialogVisible = false;
          this.onFresh();
        },
      },
      ruleForm: {
        PName: "",
        daterangeArr: "",
        BeginTime: "",
        EndTime: "",
        EquipmentType: "",
        Position: "",
        TrafficType: "",
        TrafficWay: "",
        PType: "",
        EquipmentName: "",
        Phone: "",
        Dept: "",
        Company: "",
      },
      categoryOptions: {
        "default-expand-all": true,
        filterable: true,
        clickParent: false,
        data: [],
        props: {
          disabled: "disabled",
          children: "Children",
          label: "Label",
          value: "Id",
        },
      },
      customForm: {
        formItems: [
          {
            key: "PName", // 字段ID
            label: "姓名", // Form的label
            type: "input", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            otherOptions: {
              clearable: true,
            },
            // width: '240px',
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "daterangeArr",
            label: "通行时间",
            type: "datePicker",
            otherOptions: {
              rangeSeparator: "至",
              startPlaceholder: "开始日期",
              endPlaceholder: "结束日期",
              type: "daterange",
              clearable: true,
            },
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "EquipmentType", // 字段ID
            label: "门禁类型", // Form的label
            type: "select",
            options: [],
            otherOptions: {
              placeholder: "",
              clearable: true,
            },
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "Position", // 字段ID
            label: "安装位置", // Form的label
            type: "input",
            otherOptions: {
              placeholder: "",
              clearable: true,
            },
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "TrafficWay", // 字段ID
            label: "通行方式", // Form的label
            type: "input",
            otherOptions: {
              clearable: true,
            },
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "TrafficType", // 字段ID
            label: "通行类型", // Form的label
            type: "select",
            otherOptions: {
              placeholder: "",
              clearable: true,
            },
            options: [
              { label: "进", value: "进" },
              { label: "出", value: "出" },
            ],
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "PType", // 字段ID
            label: "人员类型", // Form的label
            type: "select",
            otherOptions: {
              clearable: true,
            },
            options: [],
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "EquipmentName", // 字段ID
            label: "门禁名称", // Form的label
            type: "input",
            otherOptions: {
              placeholder: "",
              clearable: true,
            },
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "Phone", // 字段ID
            label: "联系方式", // Form的label
            type: "input",
            otherOptions: {
              placeholder: "",
              clearable: true,
            },
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "Dept", // 字段ID
            label: "人员部门", // Form的label
            type: "slot",
            change: (e) => {
              console.log(e);
            },
          },
          {
            key: "Company", // 字段ID
            label: "所属公司", // Form的label
            type: "select",
            otherOptions: {
              clearable: true,
            },
            options: [],
            change: (e) => {
              console.log(e);
            },
          },
        ],
        rules: {},
        customFormButtons: {
          submitName: "查询",
          resetName: "重置",
        },
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: "批量导出",
              onclick: (item) => {
                console.log(item);
                this.handleExport();
              },
            },
          ],
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            label: "通行时间",
            key: "TrafficTime",
            otherOptions: {
              fixed: "left",
            },
          },
          {
            label: "姓名",
            key: "PName",
            otherOptions: {
              fixed: "left",
            },
          },
          {
            label: "联系方式",
            key: "ContactWay",
          },
          {
            label: "人员类型",
            key: "PType",
          },
          {
            label: "所属部门",
            key: "Department",
          },
          {
            label: "所属公司",
            key: "CompanyName",
          },
          {
            label: "通行类型",
            key: "TrafficType",
          },
          {
            label: "设备类型",
            key: "EquipmentType",
          },
          {
            label: "设备名称",
            key: "EquipmentName",
          },
          {
            label: "设备位置",
            key: "Position",
          },
          {
            label: "通行方式",
            key: "TrafficWay",
          },
        ],
        tableData: [],
        tableActions: [
          {
            actionLabel: "查看详情",
            otherOptions: {
              type: "text",
            },
            onclick: (index, row) => {
              this.handleEdit(index, row);
            },
          },
        ],
      },
      addPageArray: [
        {
          path: this.$route.path + "/detail",
          hidden: true,
          component: () => import("./detail.vue"),
          meta: { title: `门禁通行详情` },
          name: "accessControlRecordDetail",
        },
      ],
    };
  },
  computed: {},
  async created() {
    // 门禁类型
    this.customForm.formItems.find(
      (item) => item.key === "EquipmentType"
    ).options = await this.initDeviceType("Entrance_Type");
    // 人员类型
    this.customForm.formItems.find((item) => item.key === "PType").options =
      await this.initDeviceType("P_Type");
    // 所属公司
    this.customForm.formItems.find((item) => item.key === "Company").options =
      await this.initGetCompanyList("P_Type");
    //
    this.getDeptTreeData();
    this.init();
  },
  mixins: [addRouterPage],
  methods: {
    async initDeviceType(code) {
      const res = await GetDictionaryDetailListByCode({
        dictionaryCode: code,
      });
      const options = res.Data.map((item, index) => ({
        label: item.Display_Name,
        value: item.Value,
      }));
      return options;
    },
    async initGetCompanyList(code) {
      const res = await GetCompanyList({});
      const options = res.Data.map((item, index) => ({
        label: item.Name,
        value: item.Id,
      }));
      return options;
    },
    searchForm(data) {
      console.log(data);
      this.customTableConfig.currentPage = 1;
      this.onFresh();
    },
    resetForm() {
      this.ruleForm = {
        PName: "",
        daterangeArr: "",
        BeginTime: "",
        EndTime: "",
        EquipmentType: "",
        Position: "",
        TrafficType: "",
        TrafficWay: "",
        PType: "",
        EquipmentName: "",
        Phone: "",
        Dept: "",
        Company: "",
      };
      this.onFresh();
    },
    onFresh() {
      this.getTrafficRecordPageList();
    },
    init() {
      this.getTrafficRecordPageList();
    },
    async getTrafficRecordPageList() {
      let BeginTime = "";
      let EndTime = "";
      const data = { ...this.ruleForm };
      if ((this.ruleForm.daterangeArr ?? []).length > 0) {
        BeginTime = dayjs(this.ruleForm.daterangeArr[0]).format("YYYY-MM-DD");
        EndTime = dayjs(this.ruleForm.daterangeArr[1]).format("YYYY-MM-DD");
      }
      delete data.daterangeArr;
      console.log({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...data,
        BeginTime,
        EndTime,
      });
      const res = await GetTrafficRecordPageList({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...data,
        BeginTime,
        EndTime,
      });
      if (res.IsSucceed) {
        this.customTableConfig.tableData = res.Data.Data.map((item) => ({
          ...item,
          Traffic_Time: dayjs(item.Traffic_Time).format("YYYY-MM-DD HH:mm:ss"),
        }));
        console.log(res);
        this.customTableConfig.total = res.Data.TotalCount;
      } else {
        this.$message.error(res.Message);
      }
    },
    handleEdit(index, row) {
      this.$router.push({
        name: "accessControlRecordDetail",
        query: { pg_redirect: this.$route.name, row: JSON.stringify(row) },
      });
    },
    async handleExport() {
      let BeginTime = "";
      let EndTime = "";
      if ((this.ruleForm.daterangeArr ?? []).length > 0) {
        BeginTime = dayjs(this.ruleForm.daterangeArr[0]).format("YYYY-MM-DD");
        EndTime = dayjs(this.ruleForm.daterangeArr[1]).format("YYYY-MM-DD");
      }
      const res = await ExportEntranceEquipmentList({
        // id: this.tableSelection.map((item) => item.Id).join(','),
        ...this.ruleForm,
        BeginTime,
        EndTime,
      });
      if (res.IsSucceed) {
        console.log(res);
        downloadFile(res.Data, "21");
      } else {
        this.$message.error(res.Message);
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.customTableConfig.pageSize = val;
      this.init();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.customTableConfig.currentPage = val;
      this.init();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
    async getDeptTreeData() {
      await GetDepartmentTree({ isAll: false }).then((res) => {
        if (res.IsSucceed) {
          this.categoryOptions.data = res.Data;
          this.$nextTick((_) => {
            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data);
          });
        }
      });
    },
    searchFun(value) {
      this.$refs.treeSelectArea.filterFun(value);
    },
  },
};
</script>

<style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
.layout {
  height: calc(100vh - 90px);
  overflow: auto;
}
</style>
