{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\hazardousChemicals\\monitorData\\index.vue?vue&type=style&index=0&id=05dfa303&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\hazardousChemicals\\monitorData\\index.vue", "mtime": 1755506574336}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmxheW91dCB7DQogIGhlaWdodDogY2FsYygxMDB2aCAtIDkwcHgpOw0KICBvdmVyZmxvdzogYXV0bzsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkeA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/hazardousChemicals/monitorData", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport DialogFormLook from './dialogFormLook.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport { GetDataList, ExportDataList, GetHazchemDTCList } from '@/api/business/hazardousChemicals'\r\nimport { GetDictionaryDetailListByCode } from '@/api/sys'\r\n\r\nimport { deviceTypeMixins } from '../../mixins/deviceType.js'\r\nimport otherMixin from '../../mixins/index.js'\r\n// import moment from 'moment'\r\nimport dayjs from 'dayjs'\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  mixins: [deviceTypeMixins, otherMixin],\r\n  data() {\r\n    return {\r\n      currentComponent: DialogFormLook,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        DataType: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Content', // 字段ID\r\n            label: '', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '输入设备编号或名称进行搜�?\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EqtType',\r\n            label: '设备类型',\r\n            type: 'select',\r\n\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择设备类型'\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              this.ruleForm.DataType = ''\r\n              const Id = this.deceiveTypeList.find((item) => item.value === e).Id\r\n              this.getDTCList(GetHazchemDTCList, Id, 'DataType', 'DataId')\r\n            }\r\n          },\r\n          {\r\n            key: 'DataType',\r\n            label: '数据类型',\r\n            type: 'select',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择设备类型'\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position', // 字段ID\r\n            label: '安装位置', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请输入安装位�?\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            // {\r\n            //   text: '新增',\r\n            //   round: false, // 是否圆角\r\n            //   plain: false, // 是否朴素\r\n            //   circle: false, // 是否圆形\r\n            //   loading: false, // 是否加载�?\n            //   disabled: false, // 是否禁用\r\n            //   icon: '', //  图标\r\n            //   autofocus: false, // 是否聚焦\r\n            //   type: 'primary', // primary / success / warning / danger / info / text\r\n            //   size: 'small', // medium / small / mini\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleCreate()\r\n            //   }\r\n            // },\r\n            // {\r\n            //   text: '导出',\r\n            //   key: 'batch',\r\n            //   disabled: true,\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleExport()\r\n            //   }\r\n            // },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleAllExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            otherOptions: {\r\n              width: 20,\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          }\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 200\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          }\r\n          // {\r\n          //   actionLabel: '编辑',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleEdit(index, row, 'edit')\r\n          //   }\r\n          // },\r\n          // {\r\n          //   actionLabel: '删除',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleDelete(index, row)\r\n          //   }\r\n          // }\r\n        ],\r\n        deceiveTypeList: []\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  async mounted() {\r\n    this.init()\r\n    this.deceiveTypeList = this.customForm.formItems.find((item) => item.key === 'EqtType').options = await this.getDictionaryDetailListByCode('HazchemEqtType', 'Value')\r\n    // this.initDeviceType(\"EqtType\", \"EnvironmentEqtType\");\r\n    this.getDTCList(GetHazchemDTCList, '', 'DataType', 'DataId')\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.getDTCList(GetHazchemDTCList, '', 'DataType', 'DataId')\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.GetDataList()\r\n    },\r\n    init() {\r\n      this.GetDataList()\r\n    },\r\n    async GetDataList() {\r\n      this.customTableConfig.loading = true\r\n      const res = await GetDataList({\r\n        ParameterJson: [\r\n          {\r\n            Key: '',\r\n            Value: [null],\r\n            Type: '',\r\n            Filter_Type: ''\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        SortName: '',\r\n        SortOrder: '',\r\n        Search: '',\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        ...this.ruleForm\r\n      })\r\n      this.customTableConfig.loading = false\r\n      if (res.IsSucceed) {\r\n        if (res.Data.Data && res.Data.Data.length > 0) {\r\n          this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n            ...item,\r\n            Time: dayjs(item.Time).format('YYYY-MM-DD HH:mm:ss')\r\n          }))\r\n        } else {\r\n          this.customTableConfig.tableData = []\r\n        }\r\n        /* this.customTableConfig.tableColumns = [].concat(\r\n          [\r\n            {\r\n              width: 50,\r\n              otherOptions: {\r\n                type: 'selection',\r\n                align: 'center'\r\n              }\r\n            },\r\n            {\r\n              width: 60,\r\n              label: '序号',\r\n              otherOptions: {\r\n                type: 'index',\r\n                align: 'center'\r\n              }\r\n            }\r\n          ],\r\n          res.Data.Data.Headers\r\n        )\r\n        console.log(res.Data.Data.Headers, 'tableColumns')\r\n        console.log(this.customTableConfig.tableColumns, 'tableColumns')\r\n        */\r\n        /** 2023-09-18 erwin modify */\r\n        this.customTableConfig.tableColumns = [\r\n          // {\r\n          //   width: 60,\r\n          //   label: \"序号\",\r\n          //   otherOptions: {\r\n          //     type: \"index\",\r\n          //     align: \"center\",\r\n          //   },\r\n          // },\r\n          {\r\n            label: '设备编号',\r\n            key: 'EId',\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: '设备名称',\r\n            key: 'Name',\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: '设备类型',\r\n            key: 'EqtType',\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: '安装位置',\r\n            key: 'Position'\r\n          },\r\n          {\r\n            label: '数据更新时间',\r\n            key: 'Time'\r\n          },\r\n          {\r\n            label: '数据类型',\r\n            key: 'TypeDes'\r\n            // render: (row) => {\r\n            //   return `${row.TypeDes}(${row.Unit})`\r\n            // }\r\n          },\r\n          {\r\n            label: '数据参数',\r\n            key: 'Value'\r\n          },\r\n          {\r\n            label: '单位',\r\n            key: 'Unit'\r\n          }\r\n        ]\r\n        /** end  */\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    async getDictionaryDetailListByCode(dictionaryCode = 'deviceType', Value) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode\r\n      })\r\n      if (res.IsSucceed) {\r\n        const options = [{ label: '全部', value: '' }]\r\n        res.Data.map((item) => {\r\n          options.push({\r\n            label: item.Display_Name,\r\n            value: item[Value],\r\n            ...item\r\n          })\r\n        })\r\n        return options\r\n      }\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      this.dialogVisible = true\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.currentComponent = DialogFormLook\r\n        this.componentsConfig = {\r\n          ID: row.Id,\r\n          disabled: true,\r\n          title: '查看',\r\n          ...row\r\n        }\r\n      }\r\n      // else if (type === 'edit') {\r\n      //   this.dialogTitle = '编辑'\r\n      //   this.componentsConfig = {\r\n      //     ID: row.ID,\r\n      //     disabled: false,\r\n      //     title: '编辑'\r\n      //   }\r\n      // }\r\n    },\r\n    async handleExport() {\r\n      console.log(this.ruleForm)\r\n      const res = await ExportDataList({\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: false,\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    async handleAllExport() {\r\n      const res = await ExportDataList({\r\n        Content: '',\r\n        EqtType: '',\r\n        Position: '',\r\n        IsAll: true,\r\n        Ids: [],\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n      if (this.tableSelection.length > 0) {\r\n        this.customTableConfig.buttonConfig.buttonList.find(\r\n          (v) => v.key == 'batch'\r\n        ).disabled = false\r\n      } else {\r\n        this.customTableConfig.buttonConfig.buttonList.find(\r\n          (v) => v.key == 'batch'\r\n        ).disabled = true\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}