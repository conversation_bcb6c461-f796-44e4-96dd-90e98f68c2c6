{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJenergyManagement\\alarmDetail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJenergyManagement\\alarmDetail\\index.vue", "mtime": 1755674552403}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["parseTime", "CustomLayout", "CustomTable", "CustomForm", "DialogForm", "downloadFile", "GetGridByCode", "GetWarningListSZCJ", "GetWarningTypeList", "ExportWarningListSZCJ", "UpdateWarningStatus", "name", "components", "data", "_this", "currentComponent", "componentsConfig", "componentsFuns", "open", "dialogVisible", "close", "onFresh", "dialogTitle", "tableSelection", "ruleForm", "Content", "EnergyType", "WarningType", "Position", "customForm", "formItems", "key", "label", "type", "placeholder", "otherOptions", "clearable", "width", "change", "e", "console", "log", "options", "value", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "buttonList", "text", "onclick", "item", "handleAllExport", "pageSizeOptions", "currentPage", "pageSize", "total", "operateOptions", "tableColumns", "align", "tableData", "tableActionsWidth", "tableActions", "actionLabel", "index", "row", "handleEdit", "computed", "created", "getBaseData", "init", "methods", "_this2", "then", "res", "IsSucceed", "Data", "map", "Type", "$message", "Message", "code", "_this2$customTableCon", "ColumnList", "Display_Name", "Code", "push", "apply", "_toConsumableArray", "message", "searchForm", "resetForm", "getWarningList", "_this3", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "_objectSpread", "Page", "PageSize", "sent", "Time", "Date", "TotalCount", "stop", "_this4", "$nextTick", "$refs", "_this5", "_callee2", "_callee2$", "_context2", "IsAll", "Ids", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "selection", "disabled", "length", "handelClose", "_this6", "HandleStatusStr", "warning", "id", "Id", "wid", "WId", "StatusEnum", "success", "error"], "sources": ["src/views/business/SZCJenergyManagement/alarmDetail/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        ><template\r\n          #customBtn=\"{ slotScope }\"\r\n        ><el-button\r\n          v-if=\"slotScope.Handle_Status == 1\"\r\n          type=\"text\"\r\n          @click=\"handelClose(slotScope)\"\r\n        >关闭</el-button></template></CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      top=\"6vh\"\r\n      :destroy-on-close=\"true\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { parseTime } from '@/utils'\r\n// import { baseUrl } from '@/utils/baseurl'\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport DialogForm from './dialogForm.vue'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport {\r\n  // GetDictionaryDetailListByCode,\r\n  // ExportData,\r\n  GetWarningListSZCJ,\r\n  GetWarningTypeList,\r\n  ExportWarningListSZCJ,\r\n  UpdateWarningStatus\r\n} from '@/api/business/energyManagement'\r\nexport default {\r\n  name: 'AlarmDetail',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Content: '',\r\n        EnergyType: '',\r\n        WarningType: '',\r\n        Position: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Content', // 字段ID\r\n            label: '点表编号或名称', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            placeholder: '输入点表编号或名称进行搜索',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EnergyType',\r\n            label: '能耗类型',\r\n            type: 'select',\r\n            placeholder: '请选择能耗类型',\r\n            options: [\r\n              { label: '用电量', value: 'electric' },\r\n              { label: '用水量', value: 'warter' },\r\n              { label: '用氧气量', value: 'gas' }\r\n            ], // 类型数据列表\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'WarningType',\r\n            label: '告警类型',\r\n            type: 'select',\r\n            placeholder: '请选择告警类型',\r\n            options: [], // 类型数据列表\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Handle_Status',\r\n            label: '告警状态',\r\n            type: 'select',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择告警状态'\r\n            },\r\n            options: [\r\n              {\r\n                label: '告警中',\r\n                value: 1\r\n              },\r\n              {\r\n                label: '已关闭',\r\n                value: 2\r\n              },\r\n              {\r\n                label: '已处理',\r\n                value: 3\r\n              }\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position', // 字段ID\r\n            label: '安装位置', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            placeholder: '请输入安装位置',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleAllExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        operateOptions:{\r\n          width:140\r\n        },\r\n        tableColumns: [\r\n          // {\r\n          //   width: 50,\r\n          //   otherOptions: {\r\n          //     type: 'selection',\r\n          //     align: 'center'\r\n          //   }\r\n          // },\r\n          {\r\n            width: 60,\r\n            label: '序号',\r\n            otherOptions: {\r\n              type: 'index',\r\n              align: 'center'\r\n            } // key\r\n            // otherOptions: {\r\n            //   width: 180, // 宽度\r\n            //   fixed: 'left', // left, right\r\n            //   align: 'center' //\tleft/center/right\r\n            // }\r\n          }\r\n          //   {\r\n          //     label: '设备编号',\r\n          //     key: 'HId'\r\n          //   }\r\n        ],\r\n        tableData: [],\r\n        tableActionsWidth: 120,\r\n        tableActions: [\r\n          // {\r\n          //   actionLabel: '关闭',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handelClose(row)\r\n          //   }\r\n          // },\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          }\r\n\r\n          // {\r\n          //   actionLabel: '删除',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleDelete(index, row)\r\n          //   }\r\n          // }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.getBaseData()\r\n    this.init()\r\n  },\r\n  methods: {\r\n    getBaseData() {\r\n      // 获取点表类型\r\n      // GetDictionaryDetailListByCode({ dictionaryCode: 'PointTableType' }).then(res => {\r\n      //   if (res.IsSucceed) {\r\n      //     const data = res.Data.map(item => {\r\n      //       return {\r\n      //         label: item.Display_Name,\r\n      //         value: item.Value\r\n      //       }\r\n      //     })\r\n      //     this.customForm.formItems[1].options = data\r\n      //   } else {\r\n      //     this.$message({\r\n      //       type: 'error',\r\n      //       data: res.Message\r\n      //     })\r\n      //   }\r\n      // })\r\n      // 获取告警类型\r\n      GetWarningTypeList().then(res => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data.map(item => {\r\n            return {\r\n              label: item.Type,\r\n              value: item.Type\r\n            }\r\n          })\r\n          this.customForm.formItems[2].options = data\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            data: res.Message\r\n          })\r\n        }\r\n      })\r\n      // 获取表格配置\r\n      GetGridByCode({ code: 'alarm_detail_list' }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data.ColumnList.map(item => {\r\n            return {\r\n              label: item.Display_Name,\r\n              key: item.Code\r\n            }\r\n          })\r\n          this.customTableConfig.tableColumns.push(...data)\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getWarningList()\r\n    },\r\n    init() {\r\n      this.getWarningList()\r\n    },\r\n    async getWarningList() {\r\n      const res = await GetWarningListSZCJ({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.customTableConfig.tableData = res.Data.Data.map(item => {\r\n          item.Time = item.Time ? parseTime(new Date(item.Time), '{y}-{m}-{d} {h}:{i}:{s}') : ''\r\n          return item\r\n        })\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.componentsConfig = { ...row }\r\n        this.$nextTick(() => {\r\n          this.$refs.currentComponent.init(type)\r\n        })\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        this.componentsConfig = { ...row }\r\n        this.$nextTick(() => {\r\n          this.$refs.currentComponent.init(type)\r\n        })\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    // async handleExport() {\r\n    //   console.log(this.ruleForm)\r\n    //   console.log(this.tableSelection, 'this.tableSelection')\r\n    //   const res = await ExportData({\r\n    //     IsAll: false,\r\n    //     Ids: this.tableSelection.map((item) => item.Id),\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //     // const url = new URL(res.Data, baseUrl())\r\n    //     // window.open(url.href, '_blank')\r\n    //     // this.$message({\r\n    //     //   type: 'success',\r\n    //     //   message: '导出成功!'\r\n    //     // })\r\n    //   }\r\n    // },\r\n    async handleAllExport() {\r\n      const res = await ExportWarningListSZCJ({\r\n        IsAll: true,\r\n        Ids: [],\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n        // const url = new URL(res.Data, baseUrl())\r\n        // window.open(url.href, '_blank')\r\n        this.$message({\r\n          type: 'success',\r\n          message: '导出成功!'\r\n        })\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n      this.customTableConfig.buttonConfig.buttonList[1].disabled = selection.length === 0\r\n    },\r\n    handelClose(row) {\r\n      if (row.HandleStatusStr == '关闭') {\r\n        this.$message.warning('请勿重复操作')\r\n      } else {\r\n        UpdateWarningStatus({ id: row.Id, wid: row.WId, StatusEnum: 2 }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('操作成功')\r\n            this.init()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n  <style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,SAAAA,SAAA;AACA;AACA,OAAAC,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,SAAAC,YAAA;AACA,SAAAC,aAAA;AACA;AACA;AACA;AACAC,kBAAA,EACAC,kBAAA,EACAC,qBAAA,EACAC,mBAAA,QACA;AACA;EACAC,IAAA;EACAC,UAAA;IACAV,WAAA,EAAAA,WAAA;IACA;IACA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAY,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,gBAAA,EAAAX,UAAA;MACAY,gBAAA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAJ,KAAA,CAAAK,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAN,KAAA,CAAAK,aAAA;UACAL,KAAA,CAAAO,OAAA;QACA;MACA;MACAF,aAAA;MACAG,WAAA;MACAC,cAAA;MAEAC,QAAA;QACAC,OAAA;QACAC,UAAA;QACAC,WAAA;QACAC,QAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAAA;UACAC,WAAA;UACAC,YAAA;YACA;YACAC,SAAA;UACA;UACAC,KAAA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAR,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,WAAA;UACAQ,OAAA,GACA;YAAAV,KAAA;YAAAW,KAAA;UAAA,GACA;YAAAX,KAAA;YAAAW,KAAA;UAAA,GACA;YAAAX,KAAA;YAAAW,KAAA;UAAA,EACA;UAAA;UACAR,YAAA;YACA;YACAC,SAAA;UACA;UACAE,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAR,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,WAAA;UACAQ,OAAA;UAAA;UACAP,YAAA;YACA;YACAC,SAAA;UACA;UACAE,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAR,GAAA;UACAC,KAAA;UACAC,IAAA;UACAE,YAAA;YACA;YACAC,SAAA;YACAF,WAAA;UACA;UACAQ,OAAA,GACA;YACAV,KAAA;YACAW,KAAA;UACA,GACA;YACAX,KAAA;YACAW,KAAA;UACA,GACA;YACAX,KAAA;YACAW,KAAA;UACA,EACA;UACAL,MAAA,WAAAA,OAAAC,CAAA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAR,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAAA;UACAC,WAAA;UACAC,YAAA;YACA;YACAC,SAAA;UACA;UACAE,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,EACA;QACAK,KAAA;UACA;QAAA,CACA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACAC,UAAA,GACA;YACAC,IAAA;YACAC,OAAA,WAAAA,QAAAC,IAAA;cACAb,OAAA,CAAAC,GAAA,CAAAY,IAAA;cACAvC,KAAA,CAAAwC,eAAA;YACA;UACA;QAEA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,cAAA;UACAtB,KAAA;QACA;QACAuB,YAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACAvB,KAAA;UACAL,KAAA;UACAG,YAAA;YACAF,IAAA;YACA4B,KAAA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACA;QACA;QACA;QACA;QAAA,CACA;QACAC,SAAA;QACAC,iBAAA;QACAC,YAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACAC,WAAA;UACA9B,YAAA;YACAF,IAAA;UACA;UACAmB,OAAA,WAAAA,QAAAc,KAAA,EAAAC,GAAA;YACArD,KAAA,CAAAsD,UAAA,CAAAF,KAAA,EAAAC,GAAA;UACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAAA;MAEA;IACA;EACA;EACAE,QAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAF,WAAA,WAAAA,YAAA;MAAA,IAAAG,MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAlE,kBAAA,GAAAmE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAhE,IAAA,GAAA+D,GAAA,CAAAE,IAAA,CAAAC,GAAA,WAAA1B,IAAA;YACA;cACArB,KAAA,EAAAqB,IAAA,CAAA2B,IAAA;cACArC,KAAA,EAAAU,IAAA,CAAA2B;YACA;UACA;UACAN,MAAA,CAAA7C,UAAA,CAAAC,SAAA,IAAAY,OAAA,GAAA7B,IAAA;QACA;UACA6D,MAAA,CAAAO,QAAA;YACAhD,IAAA;YACApB,IAAA,EAAA+D,GAAA,CAAAM;UACA;QACA;MACA;MACA;MACA5E,aAAA;QAAA6E,IAAA;MAAA,GAAAR,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UAAA,IAAAO,qBAAA;UACA,IAAAvE,IAAA,GAAA+D,GAAA,CAAAE,IAAA,CAAAO,UAAA,CAAAN,GAAA,WAAA1B,IAAA;YACA;cACArB,KAAA,EAAAqB,IAAA,CAAAiC,YAAA;cACAvD,GAAA,EAAAsB,IAAA,CAAAkC;YACA;UACA;UACA,CAAAH,qBAAA,GAAAV,MAAA,CAAA1B,iBAAA,CAAAY,YAAA,EAAA4B,IAAA,CAAAC,KAAA,CAAAL,qBAAA,EAAAM,kBAAA,CAAA7E,IAAA;QACA;UACA6D,MAAA,CAAAO,QAAA;YACAhD,IAAA;YACA0D,OAAA,EAAAf,GAAA,CAAAM;UACA;QACA;MACA;IACA;IACAU,UAAA,WAAAA,WAAA/E,IAAA;MACA,KAAAmC,iBAAA,CAAAQ,WAAA;MACAhB,OAAA,CAAAC,GAAA,CAAA5B,IAAA;MACA,KAAAQ,OAAA;IACA;IACAwE,SAAA,WAAAA,UAAA;MACA,KAAAxE,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAAyE,cAAA;IACA;IACAtB,IAAA,WAAAA,KAAA;MACA,KAAAsB,cAAA;IACA;IACAA,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAvB,GAAA;QAAA,OAAAqB,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAjG,kBAAA,CAAAkG,aAAA;gBACAC,IAAA,EAAAX,MAAA,CAAA/C,iBAAA,CAAAQ,WAAA;gBACAmD,QAAA,EAAAZ,MAAA,CAAA/C,iBAAA,CAAAS;cAAA,GACAsC,MAAA,CAAAvE,QAAA,CACA;YAAA;cAJAoD,GAAA,GAAA0B,QAAA,CAAAM,IAAA;cAKA,IAAAhC,GAAA,CAAAC,SAAA;gBACArC,OAAA,CAAAC,GAAA,CAAAmC,GAAA;gBACAmB,MAAA,CAAA/C,iBAAA,CAAAc,SAAA,GAAAc,GAAA,CAAAE,IAAA,CAAAA,IAAA,CAAAC,GAAA,WAAA1B,IAAA;kBACAA,IAAA,CAAAwD,IAAA,GAAAxD,IAAA,CAAAwD,IAAA,GAAA7G,SAAA,KAAA8G,IAAA,CAAAzD,IAAA,CAAAwD,IAAA;kBACA,OAAAxD,IAAA;gBACA;gBACA0C,MAAA,CAAA/C,iBAAA,CAAAU,KAAA,GAAAkB,GAAA,CAAAE,IAAA,CAAAiC,UAAA;cACA;gBACAhB,MAAA,CAAAd,QAAA;kBACAhD,IAAA;kBACA0D,OAAA,EAAAf,GAAA,CAAAM;gBACA;cACA;YAAA;YAAA;cAAA,OAAAoB,QAAA,CAAAU,IAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IACA;IACA/B,UAAA,WAAAA,WAAAF,KAAA,EAAAC,GAAA,EAAAlC,IAAA;MAAA,IAAAgF,MAAA;MACAzE,OAAA,CAAAC,GAAA,CAAAyB,KAAA,EAAAC,GAAA,EAAAlC,IAAA;MACA,IAAAA,IAAA;QACA,KAAAX,WAAA;QACA,KAAAN,gBAAA,GAAAyF,aAAA,KAAAtC,GAAA;QACA,KAAA+C,SAAA;UACAD,MAAA,CAAAE,KAAA,CAAApG,gBAAA,CAAAyD,IAAA,CAAAvC,IAAA;QACA;MACA,WAAAA,IAAA;QACA,KAAAX,WAAA;QACA,KAAAN,gBAAA,GAAAyF,aAAA,KAAAtC,GAAA;QACA,KAAA+C,SAAA;UACAD,MAAA,CAAAE,KAAA,CAAApG,gBAAA,CAAAyD,IAAA,CAAAvC,IAAA;QACA;MACA;MACA,KAAAd,aAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAmC,eAAA,WAAAA,gBAAA;MAAA,IAAA8D,MAAA;MAAA,OAAApB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAmB,SAAA;QAAA,IAAAzC,GAAA;QAAA,OAAAqB,mBAAA,GAAAG,IAAA,UAAAkB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhB,IAAA,GAAAgB,SAAA,CAAAf,IAAA;YAAA;cAAAe,SAAA,CAAAf,IAAA;cAAA,OACA/F,qBAAA,CAAAgG,aAAA;gBACAe,KAAA;gBACAC,GAAA;cAAA,GACAL,MAAA,CAAA5F,QAAA,CACA;YAAA;cAJAoD,GAAA,GAAA2C,SAAA,CAAAX,IAAA;cAKA,IAAAhC,GAAA,CAAAC,SAAA;gBACArC,OAAA,CAAAC,GAAA,CAAAmC,GAAA;gBACAvE,YAAA,CAAAuE,GAAA,CAAAE,IAAA;gBACA;gBACA;gBACAsC,MAAA,CAAAnC,QAAA;kBACAhD,IAAA;kBACA0D,OAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA4B,SAAA,CAAAP,IAAA;UAAA;QAAA,GAAAK,QAAA;MAAA;IACA;IACAK,gBAAA,WAAAA,iBAAAC,GAAA;MACAnF,OAAA,CAAAC,GAAA,iBAAAmF,MAAA,CAAAD,GAAA;MACA,KAAA3E,iBAAA,CAAAS,QAAA,GAAAkE,GAAA;MACA,KAAAnD,IAAA;IACA;IACAqD,mBAAA,WAAAA,oBAAAF,GAAA;MACAnF,OAAA,CAAAC,GAAA,wBAAAmF,MAAA,CAAAD,GAAA;MACA,KAAA3E,iBAAA,CAAAQ,WAAA,GAAAmE,GAAA;MACA,KAAAnD,IAAA;IACA;IACAsD,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAxG,cAAA,GAAAwG,SAAA;MACA,KAAA/E,iBAAA,CAAAC,YAAA,CAAAC,UAAA,IAAA8E,QAAA,GAAAD,SAAA,CAAAE,MAAA;IACA;IACAC,WAAA,WAAAA,YAAA/D,GAAA;MAAA,IAAAgE,MAAA;MACA,IAAAhE,GAAA,CAAAiE,eAAA;QACA,KAAAnD,QAAA,CAAAoD,OAAA;MACA;QACA3H,mBAAA;UAAA4H,EAAA,EAAAnE,GAAA,CAAAoE,EAAA;UAAAC,GAAA,EAAArE,GAAA,CAAAsE,GAAA;UAAAC,UAAA;QAAA,GAAA/D,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAsD,MAAA,CAAAlD,QAAA,CAAA0D,OAAA;YACAR,MAAA,CAAA3D,IAAA;UACA;YACA2D,MAAA,CAAAlD,QAAA,CAAA2D,KAAA,CAAAhE,GAAA,CAAAM,OAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}