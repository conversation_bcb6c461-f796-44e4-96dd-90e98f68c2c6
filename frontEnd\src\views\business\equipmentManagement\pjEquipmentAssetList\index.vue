<template>
  <div class="app-container abs100 pjEquipmentAssetList">
    <custom-layout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="submitForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          ref="table1"
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        />
      </template>
    </custom-layout>

    <el-dialog
      v-dialogDrag
      width="30%"
      :title="dialogTitle"
      :visible.sync="dialogVisible"
    >
      <component
        :is="currentComponent"
        ref="content"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      />
    </el-dialog>
  </div>
</template>

<script>
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import addRouterPage from '@/mixins/add-router-page'
import {
  DeleteEquipmentAssetEntity,
  ExportEquipmentAssetsList,
  GetEquipmentAssetPageList,
  AssetImportTemplatePJ,
  AssetEquipmentImportPJ,
  ExportEquipmentListPJ,
  GetEquipmentAssetPageListPJ,
  GetDictionaryDetailListByParentId,
  ExportEquipCardInfo
} from '@/api/business/eqptAsset'
import { GetGridByCode } from '@/api/sys'
import { timeFormat } from '@/filters'
import { getDictionary } from '@/utils/common'
import { downloadFile, downloadFileOnNewTag } from '@/utils/downloadFile'
import Print from './components/print.vue'
import importDialog from './components/import.vue'
import exportInfo from '@/views/business/energyManagement/mixins/export.js'
export default {
  name: 'EquipmentAssetList',
  components: { CustomTable, CustomLayout, CustomForm, Print, importDialog },
  mixins: [addRouterPage, exportInfo],
  data() {
    return {
      componentsConfig: {
        interfaceName: AssetEquipmentImportPJ
      },
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.fetchData()
        }
      },
      addPageArray: [
        {
          path: this.$route.path + '/add',
          hidden: true,
          component: () => import('./add.vue'),
          name: 'EquipmentAssetListAdd',
          meta: { title: `新增` }
        },
        {
          path: this.$route.path + '/edit',
          hidden: true,
          component: () => import('./add.vue'),
          name: 'EquipmentAssetListEdit',
          meta: { title: `编辑` }
        },
        {
          path: this.$route.path + '/view',
          hidden: true,
          component: () => import('./add.vue'),
          name: 'EquipmentAssetListView',
          meta: { title: `查看` }
        },
        {
          path: this.$route.path + '/dataAcquisition',
          hidden: true,
          component: () => import('./dataAcquisition.vue'),
          name: 'DataAcquisition',
          meta: { title: `查看数据` }
        },
        {
          path: this.$route.path + '/equipmentData',
          hidden: true,
          component: () => import('./equipmentData.vue'),
          name: 'PJEquipmentData',
          meta: { title: `设备数采` }
        }
      ],
      ruleForm: {
        EquipmentName: '',
        departName: '',
        EquipmentType: '',
        EquipmentItemType: ''
      },
      customForm: {
        formItems: [
          {
            key: 'EquipmentName',
            label: '设备名称',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'EquipmentType',
            label: '设备类型',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              this.customForm.formItems.find(
                (v) => v.key === 'EquipmentItemType'
              ).options = []
              this.ruleForm.EquipmentItemType = ''
              GetDictionaryDetailListByParentId(e).then((res) => {
                this.customForm.formItems.find(
                  (v) => v.key === 'EquipmentItemType'
                ).options = res.Data.map((v) => {
                  return {
                    label: v.Display_Name,
                    value: v.Id
                  }
                })
              })
            }
          },
          {
            key: 'EquipmentItemType',
            label: '设备子类',
            type: 'select',
            options: [],
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'departName',
            label: '所属部门',
            type: 'input',
            otherOptions: {
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          }
        ],
        rules: {},
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        // 表格
        // rowKey: 'Id', // 唯一标识 用于保持选中状态
        pageSizeOptions: [20, 40, 60, 80, 100],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        tableColumns: [
          {
            width: '55',
            otherOptions: {
              type: 'selection',
              align: 'center',
              fixed: 'left'
            }
          },
          {
            label: '设备名称',
            key: 'Display_Name',
            otherOptions: {
              align: 'center',
              fixed: 'left'
            }
          },
          {
            label: '设备SN',
            key: 'Serial_Number',
            otherOptions: {
              align: 'center',
              fixed: 'left'
            }
          },
          {
            label: '设备编号',
            key: 'Device_Number',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '品牌',
            key: 'Brand',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '规格型号',
            key: 'Spec',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '设备类型',
            key: 'Type_Name',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '设备子类',
            key: 'Type_Detail_Name',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '厂家名称',
            key: 'Manufacturer',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '厂家联系方式',
            key: 'Manufacturer_Contact_Info',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '经销商',
            key: 'Dealer',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '经销商联系方式',
            key: 'Dealer_Contact_Info',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '工程师',
            key: 'Engineer',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '工程师联系方式',
            key: 'Engineer_Contact_Info',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '所属部门',
            key: 'Department',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '安装位置',
            key: 'Position',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '安装时间',
            key: 'Install_Date',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '设备管理员',
            key: 'Administrator',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '设备管理员联系方式',
            key: 'Administrator_Contact_Info',
            otherOptions: {
              align: 'center'
            }
          },
          {
            label: '用途',
            key: 'Usage',
            otherOptions: {
              align: 'center'
            }
          }
        ],
        tableData: [],
        operateOptions: {
          align: 'center',
          width: '200px'
        },
        buttonConfig: {
          buttonList: [
            {
              text: '新增',
              round: false, // 是否圆角
              plain: false, // 是否朴素
              circle: false, // 是否圆形
              loading: false, // 是否加载中
              disabled: false, // 是否禁用
              icon: '', //  图标
              autofocus: false, // 是否聚焦
              type: 'primary', // primary / success / warning / danger / info / text
              size: 'small', // medium / small / mini
              onclick: (item) => {
                console.log(item)
                this.handleCreate()
              }
            },
            {
              text: '下载模板',
              disabled: false, // 是否禁用
              onclick: (item) => {
                console.log(item)
                this.handleDownTemplate()
              }
            },
            {
              text: '批量导入',
              disabled: false, // 是否禁用
              onclick: (item) => {
                console.log(item)
                this.currentComponent = 'importDialog'
                this.dialogVisible = true
                this.dialogTitle = '批量导入'
              }
            },
            {
              text: '批量导出',
              disabled: false,
              onclick: () => {
                this.handleExport()
              }
            },
            {
              text: '批量打印',
              disabled: false,
              loading: false,
              onclick: () => {
                this.handlePrint()
              }
            },
            {
              text: '确定',
              type: 'primary',
              disabled: false,
              onclick: () => {
                this.handleAssocia()
              }
            }
          ]
        },
        tableActionsWidth: 180,
        tableActions: [
          {
            actionLabel: '编辑',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(row.Id)
            }
          },
          {
            actionLabel: '删除',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleDelete(row.Id)
            }
          },
          // {
          //   actionLabel: "打印二维码",
          //   otherOptions: {
          //     type: "text",
          //   },
          //   onclick: (index, row) => {
          //     this.handlePrintQr(row.Id);
          //   },
          // },
          {
            actionLabel: '查看详情',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleInfo(row.Id)
            }
          }
          // {
          //   actionLabel: "查看数据",
          //   otherOptions: {
          //     type: "text",
          //   },
          //   onclick: (index, row) => {
          //     this.viewData(row);
          //   },
          // },
        ]
      },
      multipleSelection: [],
      currentComponent: 'Print',
      dialogVisible: false,
      associa: false // 是否关联
    }
  },
  watch: {
    'multipleSelection.length': {
      handler(newValue) {
        this.customTableConfig.buttonConfig.buttonList.find(
          (item) => item.text === '确定'
        ).disabled = !newValue
      },
      immediate: true
    }
  },
  created() {
    this.associa = this.$route.query.associa === 'true'
    if (this.associa) {
      this.customTableConfig.buttonConfig.buttonList = this.customTableConfig.buttonConfig.buttonList.filter(v => v.text === '确定')
    } else {
      this.customTableConfig.buttonConfig.buttonList = this.customTableConfig.buttonConfig.buttonList.filter(v => v.text !== '确定')
    }
  },
  mounted() {
    // this.getGridByCode("EquipmentAssetList");
    this.fetchData()
    getDictionary('deviceType').then((res) => {
      const item = this.customForm.formItems.find(
        (v) => v.key === 'EquipmentType'
      )
      console.log('res', res, item)
      item.options = res.map((v) => {
        return {
          label: v.Display_Name,
          value: v.Id
        }
      })
    })
  },
  activated() {
    this.fetchData()
  },
  methods: {
    resetForm() {
      this.ruleForm = {}
      this.customForm.formItems.find(
        (v) => v.key === 'EquipmentItemType'
      ).options = []
      this.fetchData()
      this.$refs.table1.setClearSelection()
    },
    submitForm() {
      this.customTableConfig.currentPage = 1
      this.fetchData()
      this.$refs.table1.setClearSelection()
    },
    fetchData() {
      GetEquipmentAssetPageListPJ({
        Display_Name: this.ruleForm.EquipmentName,
        Device_Type_Id: this.ruleForm.EquipmentType,
        Device_Type_Detail_Id: this.ruleForm.EquipmentItemType,
        Department: this.ruleForm.departName,
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize
      }).then((res) => {
        if (res.IsSucceed) {
          this.customTableConfig.tableData = res.Data.Data.map((v) => {
            v.Install_Date = timeFormat(v.Install_Date)
            return v
          })
          this.customTableConfig.total = res.Data.TotalCount
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    handleCreate() {
      // this.dialogTitle = '新增'
      // this.dialogVisible = true
      this.$router.push({
        name: 'EquipmentAssetListAdd',
        query: { pg_redirect: this.$route.name, type: 1 }
      })
    },
    handleSizeChange(val) {
      this.customTableConfig.pageSize = val
      this.fetchData({
        Page: this.customTableConfig.currentPage,
        PageSize: val
      })
    },
    handleCurrentChange(val) {
      this.customTableConfig.currentPage = val
      this.fetchData({ Page: val, PageSize: this.customTableConfig.pageSize })
    },
    handleSelectionChange(data) {
      console.log(data)
      this.multipleSelection = data
    },
    // handleExport() {
    //   console.log('handleExport')
    //   ExportEquipmentAssetsList({
    //     ids: this.multipleSelection.map(v => v.Id)
    //   }).then(res => {
    //     if (res.IsSucceed) {
    //       downloadFile(res.Data)
    //     } else {
    //       this.$message({
    //         message: res.Message,
    //         type: 'error'
    //       })
    //     }
    //   })
    // },
    // v2 导出设备资产列表
    handleExport() {
      console.log('handleExport')
      ExportEquipmentListPJ({
        Id: this.multipleSelection.map((v) => v.Id).toString(),
        Display_Name: this.ruleForm.EquipmentName,
        Device_Type_Id: this.ruleForm.EquipmentType,
        Department: this.ruleForm.departName,
        Device_Type_Detail_Id: this.ruleForm.EquipmentItemType
      }).then((res) => {
        if (res.IsSucceed) {
          downloadFile(res.Data)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    async handlePrint() {
      this.customTableConfig.buttonConfig.buttonList[4].loading = true
      const res = await ExportEquipCardInfo({
        Display_Name: this.ruleForm.EquipmentName,
        Device_Type_Id: this.ruleForm.EquipmentType,
        Device_Type_Detail_Id: this.ruleForm.EquipmentItemType,
        Department: this.ruleForm.departName,
        Ids: this.multipleSelection.map((v) => v.Id)
      })
      if (res.IsSucceed) {
        downloadFileOnNewTag(res.Data)
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
      this.customTableConfig.buttonConfig.buttonList[4].loading = false
    },
    handleDelete(id) {
      this.$confirm('是否删除该设备, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          DeleteEquipmentAssetEntity({ ids: [id] }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.fetchData()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleEdit(id) {
      this.$router.push({
        name: 'EquipmentAssetListEdit',
        query: { pg_redirect: this.$route.name, id, type: 2 }
      })
    },
    handlePrintQr(v) {
      this.dialogVisible = true
      this.dialogTitle = '设备二维码'
      this.$nextTick((_) => {
        this.$refs['content'].setCode(v)
      })
    },
    handleInfo(id) {
      this.$router.push({
        name: 'EquipmentAssetListView',
        query: { pg_redirect: this.$route.name, id, type: 3 }
      })
    },
    // getGridByCode(code) {
    //   GetGridByCode({ code }).then((res) => {
    //     console.log(res.Data);
    //     if (res.IsSucceed) {
    //       const Grid = res.Data.Grid;
    //       this.customTableConfig.tableColumns = res.Data?.ColumnList.map(
    //         (item) => {
    //           return Object.assign(
    //             {},
    //             {
    //               key: item.Code,
    //               label: item.Display_Name,
    //               width: item.Width,
    //               otherOptions: {
    //                 align: item.Align ? item.Align : "center",
    //                 sortable: item.Is_Sort,
    //                 fixed: item.Is_Frozen === false ? false : "left",
    //                 Digit_Number: item.Digit_Number,
    //               },
    //             }
    //           );
    //         }
    //       );
    //       if (Grid.Is_Select) {
    //         this.customTableConfig.tableColumns.unshift({
    //           otherOptions: {
    //             type: "selection",
    //             align: "center",
    //           },
    //         });
    //       }
    //       this.customTableConfig.pageSize = Number(Grid.Row_Number);
    //     }
    //   });
    // },
    // 查看数据
    viewData(data) {
      data.num = 1
      data.historyRouter = this.$route.name
      this.$router.push({
        name: 'PJEquipmentData',
        query: { pg_redirect: this.$route.name, data }
      })

      this.$store.dispatch('eqpt/changeEqptData', data)
    },
    // 下载模板
    handleDownTemplate() {
      AssetImportTemplatePJ({}).then((res) => {
        if (res.IsSucceed) {
          downloadFile(res.Data, '设备资产导入模板')
        } else {
          this.$message.error(res.Message)
        }
      })
    },
    // 处理关联设备数据 待开发
    handleAssocia() {
      console.log('multipleSelection', this.multipleSelection)
    }
  }
}
</script>

<style scoped>
.pjEquipmentAssetList {
  /* height: calc(100vh - 90px); */
  overflow: hidden;
}
</style>
