<template>
  <div class="app-container abs100">
    <CustomLayout>
      <template v-slot:searchForm>
        <CustomForm
          :custom-form-items="customForm.formItems"
          :custom-form-buttons="customForm.customFormButtons"
          :value="ruleForm"
          :inline="true"
          :rules="customForm.rules"
          @submitForm="searchForm"
          @resetForm="resetForm"
        />
      </template>
      <template v-slot:layoutTable>
        <CustomTable
          :custom-table-config="customTableConfig"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange"
        ><template
          #customBtn="{ slotScope }"
        ><el-button
          v-if="slotScope.Handle_Status == 1"
          type="text"
          @click="handelClose(slotScope)"
        >关闭</el-button></template></CustomTable>
      </template>
    </CustomLayout>
    <el-dialog
      v-dialogDrag
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      top="6vh"
      :destroy-on-close="true"
    >
      <component
        :is="currentComponent"
        ref="currentComponent"
        :components-config="componentsConfig"
        :components-funs="componentsFuns"
      /></el-dialog>
  </div>
</template>

<script>
import { parseTime } from '@/utils'
// import { baseUrl } from '@/utils/baseurl'
import CustomLayout from '@/businessComponents/CustomLayout/index.vue'
import CustomTable from '@/businessComponents/CustomTable/index.vue'
import CustomForm from '@/businessComponents/CustomForm/index.vue'
import DialogForm from './dialogForm.vue'
import { downloadFile } from '@/utils/downloadFile'
import { GetGridByCode } from '@/api/sys'
import {
  // GetDictionaryDetailListByCode,
  // ExportData,
  GetWarningListSZCJ,
  GetWarningTypeList,
  ExportWarningListSZCJ,
  UpdateWarningStatus
} from '@/api/business/energyManagement'
export default {
  name: 'AlarmDetail',
  components: {
    CustomTable,
    // CustomButton,
    // CustomTitle,
    CustomForm,
    CustomLayout
  },
  data() {
    return {
      currentComponent: DialogForm,
      componentsConfig: {},
      componentsFuns: {
        open: () => {
          this.dialogVisible = true
        },
        close: () => {
          this.dialogVisible = false
          this.onFresh()
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      tableSelection: [],

      ruleForm: {
        Content: '',
        EnergyType: '',
        WarningType: '',
        Position: ''
      },
      customForm: {
        formItems: [
          {
            key: 'Content', // 字段ID
            label: '点表编号或名称', // Form的label
            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            placeholder: '输入点表编号或名称进行搜索',
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true
            },
            width: '240px',
            change: (e) => {
              // change事件
              console.log(e)
            }
          },
          {
            key: 'EnergyType',
            label: '能耗类型',
            type: 'select',
            placeholder: '请选择能耗类型',
            options: [
              { label: '用电量', value: 'electric' },
              { label: '用水量', value: 'warter' },
              { label: '用氧气量', value: 'gas' }
            ], // 类型数据列表
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'WarningType',
            label: '告警类型',
            type: 'select',
            placeholder: '请选择告警类型',
            options: [], // 类型数据列表
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Handle_Status',
            label: '告警状态',
            type: 'select',
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true,
              placeholder: '请选择告警状态'
            },
            options: [
              {
                label: '告警中',
                value: 1
              },
              {
                label: '已关闭',
                value: 2
              },
              {
                label: '已处理',
                value: 3
              }
            ],
            change: (e) => {
              console.log(e)
            }
          },
          {
            key: 'Position', // 字段ID
            label: '安装位置', // Form的label
            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
            placeholder: '请输入安装位置',
            otherOptions: {
              // 除了model以外的其他的参数,具体请参考element文档
              clearable: true
            },
            change: (e) => {
              // change事件
              console.log(e)
            }
          }
        ],
        rules: {
          // 请参照elementForm rules
        },
        customFormButtons: {
          submitName: '查询',
          resetName: '重置'
        }
      },
      customTableConfig: {
        buttonConfig: {
          buttonList: [
            {
              text: '批量导出',
              onclick: (item) => {
                console.log(item)
                this.handleAllExport()
              }
            }
          ]
        },
        // 表格
        pageSizeOptions: [10, 20, 50, 80],
        currentPage: 1,
        pageSize: 20,
        total: 0,
        operateOptions:{
          width:140
        },
        tableColumns: [
          // {
          //   width: 50,
          //   otherOptions: {
          //     type: 'selection',
          //     align: 'center'
          //   }
          // },
          {
            width: 60,
            label: '序号',
            otherOptions: {
              type: 'index',
              align: 'center'
            } // key
            // otherOptions: {
            //   width: 180, // 宽度
            //   fixed: 'left', // left, right
            //   align: 'center' //	left/center/right
            // }
          }
          //   {
          //     label: '设备编号',
          //     key: 'HId'
          //   }
        ],
        tableData: [],
        tableActionsWidth: 120,
        tableActions: [
          // {
          //   actionLabel: '关闭',
          //   otherOptions: {
          //     type: 'text'
          //   },
          //   onclick: (index, row) => {
          //     this.handelClose(row)
          //   }
          // },
          {
            actionLabel: '查看',
            otherOptions: {
              type: 'text'
            },
            onclick: (index, row) => {
              this.handleEdit(index, row, 'view')
            }
          }

          // {
          //   actionLabel: '删除',
          //   otherOptions: {
          //     type: 'text'
          //   },
          //   onclick: (index, row) => {
          //     this.handleDelete(index, row)
          //   }
          // }
        ]
      }
    }
  },
  computed: {},
  created() {
    this.getBaseData()
    this.init()
  },
  methods: {
    getBaseData() {
      // 获取点表类型
      // GetDictionaryDetailListByCode({ dictionaryCode: 'PointTableType' }).then(res => {
      //   if (res.IsSucceed) {
      //     const data = res.Data.map(item => {
      //       return {
      //         label: item.Display_Name,
      //         value: item.Value
      //       }
      //     })
      //     this.customForm.formItems[1].options = data
      //   } else {
      //     this.$message({
      //       type: 'error',
      //       data: res.Message
      //     })
      //   }
      // })
      // 获取告警类型
      GetWarningTypeList().then(res => {
        if (res.IsSucceed) {
          const data = res.Data.map(item => {
            return {
              label: item.Type,
              value: item.Type
            }
          })
          this.customForm.formItems[2].options = data
        } else {
          this.$message({
            type: 'error',
            data: res.Message
          })
        }
      })
      // 获取表格配置
      GetGridByCode({ code: 'alarm_detail_list' }).then(res => {
        if (res.IsSucceed) {
          const data = res.Data.ColumnList.map(item => {
            return {
              label: item.Display_Name,
              key: item.Code
            }
          })
          this.customTableConfig.tableColumns.push(...data)
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    searchForm(data) {
      this.customTableConfig.currentPage = 1
      console.log(data)
      this.onFresh()
    },
    resetForm() {
      this.onFresh()
    },
    onFresh() {
      this.getWarningList()
    },
    init() {
      this.getWarningList()
    },
    async getWarningList() {
      const res = await GetWarningListSZCJ({
        Page: this.customTableConfig.currentPage,
        PageSize: this.customTableConfig.pageSize,
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        console.log(res)
        this.customTableConfig.tableData = res.Data.Data.map(item => {
          item.Time = item.Time ? parseTime(new Date(item.Time), '{y}-{m}-{d} {h}:{i}:{s}') : ''
          return item
        })
        this.customTableConfig.total = res.Data.TotalCount
      } else {
        this.$message({
          type: 'error',
          message: res.Message
        })
      }
    },
    handleEdit(index, row, type) {
      console.log(index, row, type)
      if (type === 'view') {
        this.dialogTitle = '查看'
        this.componentsConfig = { ...row }
        this.$nextTick(() => {
          this.$refs.currentComponent.init(type)
        })
      } else if (type === 'edit') {
        this.dialogTitle = '编辑'
        this.componentsConfig = { ...row }
        this.$nextTick(() => {
          this.$refs.currentComponent.init(type)
        })
      }
      this.dialogVisible = true
    },
    // async handleExport() {
    //   console.log(this.ruleForm)
    //   console.log(this.tableSelection, 'this.tableSelection')
    //   const res = await ExportData({
    //     IsAll: false,
    //     Ids: this.tableSelection.map((item) => item.Id),
    //     ...this.ruleForm
    //   })
    //   if (res.IsSucceed) {
    //     console.log(res)
    //     downloadFile(res.Data, '21')
    //     // const url = new URL(res.Data, baseUrl())
    //     // window.open(url.href, '_blank')
    //     // this.$message({
    //     //   type: 'success',
    //     //   message: '导出成功!'
    //     // })
    //   }
    // },
    async handleAllExport() {
      const res = await ExportWarningListSZCJ({
        IsAll: true,
        Ids: [],
        ...this.ruleForm
      })
      if (res.IsSucceed) {
        console.log(res)
        downloadFile(res.Data, '21')
        // const url = new URL(res.Data, baseUrl())
        // window.open(url.href, '_blank')
        this.$message({
          type: 'success',
          message: '导出成功!'
        })
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.customTableConfig.pageSize = val
      this.init()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.customTableConfig.currentPage = val
      this.init()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
      this.customTableConfig.buttonConfig.buttonList[1].disabled = selection.length === 0
    },
    handelClose(row) {
      if (row.HandleStatusStr == '关闭') {
        this.$message.warning('请勿重复操作')
      } else {
        UpdateWarningStatus({ id: row.Id, wid: row.WId, StatusEnum: 2 }).then(res => {
          if (res.IsSucceed) {
            this.$message.success('操作成功')
            this.init()
          } else {
            this.$message.error(res.Message)
          }
        })
      }
    }
  }
}
</script>

  <style lang="scss" scoped>
.mt20 {
  margin-top: 10px;
}
</style>

