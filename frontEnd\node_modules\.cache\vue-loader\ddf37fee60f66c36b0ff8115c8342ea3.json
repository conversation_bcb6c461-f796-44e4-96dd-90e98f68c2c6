{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlRecord\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\accessControl\\accessControlRecord\\index.vue", "mtime": 1755674552409}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk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file": "index.vue", "sourceRoot": "src/views/business/accessControl/accessControlRecord", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\n\r\nimport dialogFormLook from './dialogFormLook.vue'\r\n\r\nimport { downloadFile } from '@/utils/downloadFile'\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n// import * as moment from 'moment'\r\nimport dayjs from 'dayjs'\r\n\r\nimport {\r\n  GetRole,\r\n  GetTrafficRecordList,\r\n  ExportEntranceTrafficRecord,\r\n  GetDictionaryDetailListByCode\r\n} from '@/api/business/accessControl'\r\n\r\nexport default {\r\n  name: '',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: dialogFormLook,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        P_Name: '',\r\n        BeginTime: '',\r\n        EndTime: '',\r\n        EntranceType: '',\r\n        EquipmentName: '',\r\n        Position: '',\r\n        Position_Name: '',\r\n        Traffic_Way: '',\r\n        P_Type: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'P_Name', // 字段ID\r\n            label: '姓名', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            // width: '240px',\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'BeginTime',\r\n            label: '通行开始时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EndTime',\r\n            label: '通行结束时间',\r\n            type: 'datePicker',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EntranceType', // 字段ID\r\n            label: '门禁类型', // Form的label\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EquipmentName', // 字段ID\r\n            label: '门禁设备', // Form的label\r\n            type: 'input',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position', // 字段ID\r\n            label: '安装位置', // Form的label\r\n            type: 'input',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position_Name', // 字段ID\r\n            label: '岗位类型', // Form的label\r\n            type: 'select',\r\n            otherOptions: {\r\n              placeholder: ''\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Traffic_Way', // 字段ID\r\n            label: '通行方式', // Form的label\r\n            type: 'select',\r\n\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'P_Type', // 字段ID\r\n            label: '人员类型', // Form的label\r\n            type: 'select',\r\n\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            // {\r\n            //   text: '新增',\r\n            //   round: false, // 是否圆角\r\n            //   plain: false, // 是否朴素\r\n            //   circle: false, // 是否圆形\r\n            //   loading: false, // 是否加载中\r\n            //   disabled: false, // 是否禁用\r\n            //   icon: '', //  图标\r\n            //   autofocus: false, // 是否聚焦\r\n            //   type: 'primary', // primary / success / warning / danger / info / text\r\n            //   size: 'small', // medium / small / mini\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleCreate()\r\n            //   }\r\n            // },\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleExport()\r\n              }\r\n            }\r\n            // {\r\n            //   text: '批量导出',\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleAllExport()\r\n            //   }\r\n            // }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: 'selection',\r\n              align: 'center'\r\n            }\r\n          },\r\n          // {\r\n          //   width: 60,\r\n          //   label: '序号',\r\n          //   otherOptions: {\r\n          //     type: 'index',\r\n          //     align: 'center'\r\n          //   } // key\r\n          // },\r\n          {\r\n            label: '姓名',\r\n            key: 'P_Name'\r\n          },\r\n          // {\r\n          //   label: '图片',\r\n          //   key: 'Name'\r\n          // },\r\n          {\r\n            label: '性别',\r\n            key: 'P_Sex'\r\n          },\r\n          {\r\n            label: '联系方式',\r\n            key: 'Contact_Way'\r\n          },\r\n          {\r\n            label: '人员类型',\r\n            key: 'P_Type'\r\n          },\r\n          {\r\n            label: '岗位类型',\r\n            key: 'Position_Name'\r\n          },\r\n          {\r\n            label: '门禁类型',\r\n            key: 'Entrance_Equipment_Type'\r\n          },\r\n          {\r\n            label: '门禁名称',\r\n            key: 'Entrance_Equipment_Name'\r\n          },\r\n          {\r\n            label: '门禁位置',\r\n            key: 'Position'\r\n          },\r\n          {\r\n            label: '通行时间',\r\n            key: 'Traffic_Time'\r\n          },\r\n          {\r\n            label: '通行方式',\r\n            key: 'Traffic_Way'\r\n          }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看详情',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          }\r\n          // {\r\n          //   actionLabel: '编辑',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleEdit(index, row, 'edit')\r\n          //   }\r\n          // },\r\n          // {\r\n          //   actionLabel: '删除',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleDelete(index, row)\r\n          //   }\r\n          // }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  async created() {\r\n    // 门禁类型\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'EntranceType'\r\n    ).options = await this.initDeviceType('Entrance_Type')\r\n    // 岗位类型\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'Position_Name'\r\n    ).options = await this.initGetRole('Entrance_Type')\r\n\r\n    console.log(await this.initDeviceType('Position_Type'), '1221212222222')\r\n    // 通行方式\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'Traffic_Way'\r\n    ).options = await this.initDeviceType('Traffic_Way')\r\n    // 人员类型\r\n    this.customForm.formItems.find((item) => item.key === 'P_Type').options =\r\n      await this.initDeviceType('P_Type')\r\n    this.init()\r\n  },\r\n  methods: {\r\n    async initDeviceType(code) {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode: code\r\n      })\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    // 岗位类型\r\n    async initGetRole(code) {\r\n      const res = await GetRole({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n\r\n    searchForm(data) {\r\n      console.log(data)\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.GetTrafficRecordList()\r\n    },\r\n    init() {\r\n      this.GetTrafficRecordList()\r\n    },\r\n    async GetTrafficRecordList() {\r\n      const res = await GetTrafficRecordList({\r\n        ParameterJson: [\r\n          {\r\n            Key: 'string',\r\n            Value: [null],\r\n            Type: 'string',\r\n            Filter_Type: 'string'\r\n          }\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n\r\n        Search: '',\r\n        SortName: '',\r\n        SortOrder: '',\r\n        TotalCount: 0,\r\n        P_Name: '',\r\n\r\n        EntranceType: '',\r\n        EquipmentName: '',\r\n        Position: '',\r\n        Position_Name: '',\r\n        Traffic_Way: '',\r\n        P_Type: '',\r\n        ...this.ruleForm,\r\n        BeginTime: this.ruleForm.EndTime\r\n          ? dayjs(this.ruleForm.BeginTime).format('YYYY-MM-DD')\r\n          : '',\r\n        EndTime: this.ruleForm.EndTime\r\n          ? dayjs(this.ruleForm.EndTime).format('YYYY-MM-DD')\r\n          : ''\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n          ...item,\r\n          Traffic_Time: dayjs(item.Traffic_Time).format('YYYY-MM-DD HH:mm:ss')\r\n        }))\r\n        console.log(res)\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = '新增'\r\n      this.dialogVisible = true\r\n    },\r\n    // handleDelete(index, row) {\r\n    //   console.log(index, row)\r\n    //   console.log(this)\r\n    //   this.$confirm('确认删除？', {\r\n    //     type: 'warning'\r\n    //   })\r\n    //     .then(async(_) => {\r\n    //       const res = await DeleteEquipment({\r\n    //         IDs: [row.ID]\r\n    //       })\r\n    //       if (res.IsSucceed) {\r\n    //         this.init()\r\n    //       }\r\n    //     })\r\n    //     .catch((_) => {})\r\n    // },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.componentsConfig = {\r\n          ID: row.Id,\r\n          title: '查看',\r\n          row: row\r\n        }\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    async handleExport() {\r\n      if (this.tableSelection.length == 0) {\r\n        this.$message.warning('请选择数据在导出')\r\n        return\r\n      }\r\n      const res = await ExportEntranceTrafficRecord({\r\n        id: this.tableSelection.map((item) => item.Id).join(','),\r\n        code: 'accessControlRecord'\r\n        // ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    // async handleAllExport() {\r\n    //   const res = await ExportEntranceTrafficRecord({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: true,\r\n    //     Ids: [],\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   }\r\n    // },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}