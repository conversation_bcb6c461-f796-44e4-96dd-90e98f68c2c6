{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\noticeAnnouncement\\index.vue?vue&type=style&index=0&id=2539a799&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\noticeAnnouncement\\index.vue", "mtime": 1755506574322}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoubXQyMCB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQp9DQoubGF5b3V0ew0KICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA5MHB4KTsNCiAgb3ZlcmZsb3c6IGF1dG87DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyfA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/eventManagement/noticeAnnouncement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        >\r\n          <template #customBtn=\"{ slotScope }\">\r\n            <el-button\r\n              v-if=\"slotScope.Status == 1\"\r\n              type=\"text\"\r\n              @click=\"handleEdit(2, slotScope, 'edit')\"\r\n              >编辑</el-button\r\n            ></template\r\n          >\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      width=\"900px\"\r\n      :visible.sync=\"dialogVisible\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport getGridByCode from \"../../safetyManagement/mixins/index\";\r\nimport DialogForm from \"./dialogForm.vue\";\r\nimport DialogFormLook from \"./dialogFormLook.vue\";\r\n\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\nimport dayjs from \"dayjs\";\r\nimport {\r\n  GetUsers,\r\n  GetPageList,\r\n  SaveNotice,\r\n  BatchCloseNotice,\r\n  GetNoticeInfo,\r\n  GetNoticeDropDownOption,\r\n  GetPublishUnitList,\r\n  DeleteNotice,\r\n} from \"@/api/business/eventManagement\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        Data: {},\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"查看\",\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        Title: \"\",\r\n        Type: \"\",\r\n        Source: \"\",\r\n        Module: \"事件管理\",\r\n        NotifyUser: \"\",\r\n        Date: [],\r\n        StartTime: \"\",\r\n        EndTime: \"\",\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Title\",\r\n            label: \"通知标题\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          // {\r\n          //   key: \"Type\",\r\n          //   label: \"通知类型\",\r\n          //   type: \"select\",\r\n          //   options: [],\r\n          //   otherOptions: {\r\n          //     clearable: true,\r\n          //   },\r\n          //   change: (e) => {\r\n          //     // change事件\r\n          //     console.log(e);\r\n          //   },\r\n          // },\r\n          // {\r\n          //   key: \"Source\",\r\n          //   label: \"来源\",\r\n          //   type: \"input\",\r\n          //   options: [],\r\n          //   otherOptions: {\r\n          //     clearable: true,\r\n          //   },\r\n          //   change: (e) => {\r\n          //     console.log(e);\r\n          //   },\r\n          // },\r\n          {\r\n            key: \"Module\",\r\n            label: \"业务模块\",\r\n            type: \"select\",\r\n            options: [\r\n              {\r\n                label: \"全部\",\r\n                value: \"\",\r\n              },\r\n              {\r\n                label: \"事件管理\",\r\n                value: \"事件管理\",\r\n              },\r\n            ],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"NotifyUser\",\r\n            label: \"通知人员\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Date\", // 字段ID\r\n            label: \"发布时间\", // Form的label\r\n            type: \"datePicker\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              type: \"datetimerange\",\r\n              disabled: false,\r\n              placeholder: \"请输�?..\",\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n              this.ruleForm.StartTime = dayjs(e[0]).format(\r\n                \"YYYY-MM-DD HH:mm:ss\"\r\n              );\r\n              this.ruleForm.EndTime = dayjs(e[1]).format(\"YYYY-MM-DD HH:mm:ss\");\r\n            },\r\n          },\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"发布公告\",\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载�?\n              disabled: false, // 是否禁用\r\n              icon: \"\", //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n            {\r\n              text: \"关闭\",\r\n              type: \"danger\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleClose();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            otherOptions: {\r\n              type: \"selection\",\r\n              align: \"center\",\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"通知标题\",\r\n            key: \"Title\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          // {\r\n          //   label: \"通知类型\",\r\n          //   key: \"TypeName\",\r\n          //   otherOptions: {\r\n          //     align: \"center\",\r\n          //   },\r\n          // },\r\n          // {\r\n          //   label: \"来源\",\r\n          //   key: \"Source\",\r\n          //   otherOptions: {\r\n          //     align: \"center\",\r\n          //   },\r\n          // },\r\n          {\r\n            label: \"业务模块\",\r\n            key: \"Module\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"通知人员\",\r\n            key: \"NotifyUser\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"公告状�?,\r\n            key: \"StatusName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"发布时间\",\r\n            key: \"PublishTime\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"创建�?,\r\n            key: \"Create_UserName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: \"center\",\r\n        },\r\n        tableActionsWidth: 160,\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看详情\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, \"view\");\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n          // {\r\n          //   actionLabel: \"编辑\",\r\n          //   otherOptions: {\r\n          //     type: \"text\",\r\n          //     disabled: true,\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     console.log(row, \"row\");\r\n          //     this.handleEdit(index, row, \"edit\");\r\n          //   },\r\n          // },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init();\r\n  },\r\n  mixins: [getGridByCode],\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1;\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.StartTime = null;\r\n      this.ruleForm.EndTime = null;\r\n      this.ruleForm.Date = null;\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.GetPageList();\r\n    },\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.GetPageList();\r\n      this.getNoticeDropDownOption();\r\n    },\r\n    async getNoticeDropDownOption() {\r\n      const res = await GetNoticeDropDownOption({});\r\n      if (res.IsSucceed) {\r\n        let result = res.Data || [];\r\n        let noticeType = [];\r\n        let noticeLevel = [];\r\n        result.forEach((element) => {\r\n          if (element.TypeName == \"通知类型\") {\r\n            noticeType = element.Data.map((item) => ({\r\n              value: item.Value,\r\n              label: item.Name,\r\n            }));\r\n          } else if (element.TypeName == \"发布层级\") {\r\n            noticeLevel = element.Data.map((item) => ({\r\n              value: item.Value,\r\n              label: item.Name,\r\n            }));\r\n          }\r\n        });\r\n        this.customForm.formItems.find((item) => item.key == \"Type\").options =\r\n          noticeType;\r\n      }\r\n    },\r\n    async GetPageList() {\r\n      const res = await GetPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n        if (this.customTableConfig.tableData.length > 0) {\r\n        }\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    // 通知公告删除\r\n    async handleDelete(index, row) {\r\n      console.log(row, \"---\");\r\n      const res = await DeleteNotice({\r\n        IDs: [row.Id],\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.onFresh();\r\n        this.$message.success(\"删除成功\");\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = \"发布公告\";\r\n      this.dialogVisible = true;\r\n      this.currentComponent = DialogForm;\r\n      this.componentsConfig = {\r\n        type: \"add\",\r\n      };\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type);\r\n\r\n      let isShowBtn = true;\r\n      if (type === \"view\") {\r\n        this.currentComponent = DialogFormLook;\r\n        this.dialogTitle = \"查看\";\r\n        this.componentsConfig = {\r\n          ID: row.Id,\r\n          disabled: true,\r\n          type,\r\n          title: \"查看\",\r\n        };\r\n        isShowBtn = false;\r\n      } else if (type === \"edit\") {\r\n        this.currentComponent = DialogForm;\r\n        this.dialogTitle = \"编辑\";\r\n        this.componentsConfig = {\r\n          ID: row.Id,\r\n          disabled: true,\r\n          type,\r\n          title: \"编辑\",\r\n        };\r\n        isShowBtn = true;\r\n      }\r\n      this.dialogVisible = true;\r\n    },\r\n    async handleClose() {\r\n      if (this.tableSelection.length == 0) {\r\n        this.$message.warning('请选择数据后再关闭!')\r\n      } else {\r\n        const flag = this.tableSelection.some(item => item.Status == 0 || item.Status == 2)\r\n        if (flag) {\r\n          this.$message.warning('此功能只能关闭待发布的公�?')\r\n          return\r\n        } else {\r\n          const res = await BatchCloseNotice({\r\n            id: this.tableSelection.map((item) => item.Id).toString(),\r\n            // code: \"AccessControlAlarmDetails1\",\r\n          });\r\n          if (res.IsSucceed) {\r\n            console.log(res);\r\n            this.$message.success(\"操作成功\");\r\n            this.init();\r\n            // downloadFile(res.Data, \"告警明细数据\");\r\n          }\r\n        }\r\n      }\r\n    },\r\n    // async handleExport() {\r\n    //   const res = await ExportEntranceWarning({\r\n    //     id: this.tableSelection.map((item) => item.Id).toString(),\r\n    //     code: \"AccessControlAlarmDetails1\",\r\n    //   });\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res);\r\n    //     downloadFile(res.Data, \"告警明细数据\");\r\n    //   }\r\n    // },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.GetPageList();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.GetPageList();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    // handleEdit(row) {\r\n    //   this.dialogVisible = true;\r\n    //   this.componentsConfig.Data = row;\r\n    // },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}