{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\noticeAnnouncement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\eventManagement\\noticeAnnouncement\\index.vue", "mtime": 1755506574322}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAg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file": "index.vue", "sourceRoot": "src/views/business/eventManagement/noticeAnnouncement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        >\r\n          <template #customBtn=\"{ slotScope }\">\r\n            <el-button\r\n              v-if=\"slotScope.Status == 1\"\r\n              type=\"text\"\r\n              @click=\"handleEdit(2, slotScope, 'edit')\"\r\n              >编辑</el-button\r\n            ></template\r\n          >\r\n        </CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      width=\"900px\"\r\n      :visible.sync=\"dialogVisible\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport getGridByCode from \"../../safetyManagement/mixins/index\";\r\nimport DialogForm from \"./dialogForm.vue\";\r\nimport DialogFormLook from \"./dialogFormLook.vue\";\r\n\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\nimport dayjs from \"dayjs\";\r\nimport {\r\n  GetUsers,\r\n  GetPageList,\r\n  SaveNotice,\r\n  BatchCloseNotice,\r\n  GetNoticeInfo,\r\n  GetNoticeDropDownOption,\r\n  GetPublishUnitList,\r\n  DeleteNotice,\r\n} from \"@/api/business/eventManagement\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        Data: {},\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"查看\",\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        Title: \"\",\r\n        Type: \"\",\r\n        Source: \"\",\r\n        Module: \"事件管理\",\r\n        NotifyUser: \"\",\r\n        Date: [],\r\n        StartTime: \"\",\r\n        EndTime: \"\",\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Title\",\r\n            label: \"通知标题\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          // {\r\n          //   key: \"Type\",\r\n          //   label: \"通知类型\",\r\n          //   type: \"select\",\r\n          //   options: [],\r\n          //   otherOptions: {\r\n          //     clearable: true,\r\n          //   },\r\n          //   change: (e) => {\r\n          //     // change事件\r\n          //     console.log(e);\r\n          //   },\r\n          // },\r\n          // {\r\n          //   key: \"Source\",\r\n          //   label: \"来源\",\r\n          //   type: \"input\",\r\n          //   options: [],\r\n          //   otherOptions: {\r\n          //     clearable: true,\r\n          //   },\r\n          //   change: (e) => {\r\n          //     console.log(e);\r\n          //   },\r\n          // },\r\n          {\r\n            key: \"Module\",\r\n            label: \"业务模块\",\r\n            type: \"select\",\r\n            options: [\r\n              {\r\n                label: \"全部\",\r\n                value: \"\",\r\n              },\r\n              {\r\n                label: \"事件管理\",\r\n                value: \"事件管理\",\r\n              },\r\n            ],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"NotifyUser\",\r\n            label: \"通知人员\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Date\", // 字段ID\r\n            label: \"发布时间\", // Form的label\r\n            type: \"datePicker\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              type: \"datetimerange\",\r\n              disabled: false,\r\n              placeholder: \"请输�?..\",\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n              this.ruleForm.StartTime = dayjs(e[0]).format(\r\n                \"YYYY-MM-DD HH:mm:ss\"\r\n              );\r\n              this.ruleForm.EndTime = dayjs(e[1]).format(\"YYYY-MM-DD HH:mm:ss\");\r\n            },\r\n          },\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"发布公告\",\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载�?\n              disabled: false, // 是否禁用\r\n              icon: \"\", //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n            {\r\n              text: \"关闭\",\r\n              type: \"danger\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleClose();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            otherOptions: {\r\n              type: \"selection\",\r\n              align: \"center\",\r\n              fixed: \"left\",\r\n            },\r\n          },\r\n          {\r\n            label: \"通知标题\",\r\n            key: \"Title\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          // {\r\n          //   label: \"通知类型\",\r\n          //   key: \"TypeName\",\r\n          //   otherOptions: {\r\n          //     align: \"center\",\r\n          //   },\r\n          // },\r\n          // {\r\n          //   label: \"来源\",\r\n          //   key: \"Source\",\r\n          //   otherOptions: {\r\n          //     align: \"center\",\r\n          //   },\r\n          // },\r\n          {\r\n            label: \"业务模块\",\r\n            key: \"Module\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"通知人员\",\r\n            key: \"NotifyUser\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"公告状�?,\r\n            key: \"StatusName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"发布时间\",\r\n            key: \"PublishTime\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"创建�?,\r\n            key: \"Create_UserName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: \"center\",\r\n        },\r\n        tableActionsWidth: 160,\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看详情\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, \"view\");\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n          // {\r\n          //   actionLabel: \"编辑\",\r\n          //   otherOptions: {\r\n          //     type: \"text\",\r\n          //     disabled: true,\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     console.log(row, \"row\");\r\n          //     this.handleEdit(index, row, \"edit\");\r\n          //   },\r\n          // },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init();\r\n  },\r\n  mixins: [getGridByCode],\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1;\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.StartTime = null;\r\n      this.ruleForm.EndTime = null;\r\n      this.ruleForm.Date = null;\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.GetPageList();\r\n    },\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.GetPageList();\r\n      this.getNoticeDropDownOption();\r\n    },\r\n    async getNoticeDropDownOption() {\r\n      const res = await GetNoticeDropDownOption({});\r\n      if (res.IsSucceed) {\r\n        let result = res.Data || [];\r\n        let noticeType = [];\r\n        let noticeLevel = [];\r\n        result.forEach((element) => {\r\n          if (element.TypeName == \"通知类型\") {\r\n            noticeType = element.Data.map((item) => ({\r\n              value: item.Value,\r\n              label: item.Name,\r\n            }));\r\n          } else if (element.TypeName == \"发布层级\") {\r\n            noticeLevel = element.Data.map((item) => ({\r\n              value: item.Value,\r\n              label: item.Name,\r\n            }));\r\n          }\r\n        });\r\n        this.customForm.formItems.find((item) => item.key == \"Type\").options =\r\n          noticeType;\r\n      }\r\n    },\r\n    async GetPageList() {\r\n      const res = await GetPageList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n        if (this.customTableConfig.tableData.length > 0) {\r\n        }\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    // 通知公告删除\r\n    async handleDelete(index, row) {\r\n      console.log(row, \"---\");\r\n      const res = await DeleteNotice({\r\n        IDs: [row.Id],\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.onFresh();\r\n        this.$message.success(\"删除成功\");\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    handleCreate() {\r\n      this.dialogTitle = \"发布公告\";\r\n      this.dialogVisible = true;\r\n      this.currentComponent = DialogForm;\r\n      this.componentsConfig = {\r\n        type: \"add\",\r\n      };\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type);\r\n\r\n      let isShowBtn = true;\r\n      if (type === \"view\") {\r\n        this.currentComponent = DialogFormLook;\r\n        this.dialogTitle = \"查看\";\r\n        this.componentsConfig = {\r\n          ID: row.Id,\r\n          disabled: true,\r\n          type,\r\n          title: \"查看\",\r\n        };\r\n        isShowBtn = false;\r\n      } else if (type === \"edit\") {\r\n        this.currentComponent = DialogForm;\r\n        this.dialogTitle = \"编辑\";\r\n        this.componentsConfig = {\r\n          ID: row.Id,\r\n          disabled: true,\r\n          type,\r\n          title: \"编辑\",\r\n        };\r\n        isShowBtn = true;\r\n      }\r\n      this.dialogVisible = true;\r\n    },\r\n    async handleClose() {\r\n      if (this.tableSelection.length == 0) {\r\n        this.$message.warning('请选择数据后再关闭!')\r\n      } else {\r\n        const flag = this.tableSelection.some(item => item.Status == 0 || item.Status == 2)\r\n        if (flag) {\r\n          this.$message.warning('此功能只能关闭待发布的公�?')\r\n          return\r\n        } else {\r\n          const res = await BatchCloseNotice({\r\n            id: this.tableSelection.map((item) => item.Id).toString(),\r\n            // code: \"AccessControlAlarmDetails1\",\r\n          });\r\n          if (res.IsSucceed) {\r\n            console.log(res);\r\n            this.$message.success(\"操作成功\");\r\n            this.init();\r\n            // downloadFile(res.Data, \"告警明细数据\");\r\n          }\r\n        }\r\n      }\r\n    },\r\n    // async handleExport() {\r\n    //   const res = await ExportEntranceWarning({\r\n    //     id: this.tableSelection.map((item) => item.Id).toString(),\r\n    //     code: \"AccessControlAlarmDetails1\",\r\n    //   });\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res);\r\n    //     downloadFile(res.Data, \"告警明细数据\");\r\n    //   }\r\n    // },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.GetPageList();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.GetPageList();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    // handleEdit(row) {\r\n    //   this.dialogVisible = true;\r\n    //   this.componentsConfig.Data = row;\r\n    // },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}