{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\personnelManagement\\index.vue?vue&type=style&index=0&id=e7d1465e&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\personnelManagement\\index.vue", "mtime": 1755674552430}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoucGVyc29ubmVsTWFuYWdlbWVudHsKICAvLyBoZWlnaHQ6IGNhbGMoMTAwdmggLSA5MHB4KTsKICAvLyBvdmVyZmxvdzogaGlkZGVuOwp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6ZA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/personnelManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 personnelManagement\">\r\n    <custom-layout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"submitForm\"\r\n          @resetForm=\"fetchData\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </custom-layout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport getGridByCode from '../safetyManagement/mixins/index'\r\nimport { querypersonnel, DeletePersonnel, DownloadPersonnelsTemplate, DownloadPersonnelsToExcel, ExportPersonnelList } from '@/api/business/personnelManagement.js'\r\nimport {\r\n  GetDepartment,\r\n  GetCompany\r\n} from '@/api/business/accessControl'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport addDialog from './components/addDialog'\r\nimport DialogFormImport from './components/importFile'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nexport default {\r\n  components: {\r\n    CustomLayout,\r\n    CustomTable,\r\n    CustomForm\r\n  },\r\n  mixins: [getGridByCode, addRouterPage],\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        name: '',\r\n        mobile: '',\r\n        personnelType: null,\r\n        companyId: '',\r\n        departmentId: '',\r\n        personnelStatus: null\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'name',\r\n            label: '姓名',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'mobile',\r\n            label: '联系方式',\r\n            type: 'input',\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'personnelType',\r\n            label: '人员类型',\r\n            type: 'select',\r\n            options: [\r\n              {\r\n                label: '系统人员',\r\n                value: '1'\r\n              },\r\n              {\r\n                label: '普通人员',\r\n                value: '2'\r\n              }\r\n            ],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'companyId',\r\n            label: '所属公司',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'departmentId',\r\n            label: '所属部门',\r\n            type: 'select',\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'personnelStatus',\r\n            label: '状态',\r\n            type: 'select',\r\n            options: [\r\n              {\r\n                label: '在职',\r\n                value: '1'\r\n              },\r\n              {\r\n                label: '离职',\r\n                value: '2'\r\n              }\r\n            ],\r\n            otherOptions: {\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '导入模板下载',\r\n              icon: 'el-icon-download',\r\n              onclick: () => {\r\n                this.handleDownTemplate()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导入',\r\n              icon: 'el-icon-download',\r\n              onclick: () => {\r\n                this.handleImport()\r\n              }\r\n            },\r\n            {\r\n              text: '批量导出',\r\n              icon: 'el-icon-upload2',\r\n              onclick: () => {\r\n                this.handleExport()\r\n              }\r\n            },\r\n            {\r\n              text: '新增',\r\n              icon: 'el-icon-plus',\r\n              type: 'primary',\r\n              onclick: () => {\r\n                this.handleClick('add')\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        pageSizeOptions: [20, 40, 60, 80, 100],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: 'center'\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleWatch(row.Id)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '编辑',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleClick('edit', row)\r\n            }\r\n          },\r\n          {\r\n            actionLabel: '删除',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDele(row.Id)\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      multipleSelection: [],\r\n      dialogVisible: false,\r\n      currentComponent: null,\r\n      dialogTitle: '新增',\r\n      componentsFuns: {\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.fetchData()\r\n        }\r\n      },\r\n      componentsConfig: {},\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/info',\r\n          hidden: true,\r\n          component: () => import('./info.vue'),\r\n          meta: { title: '人员详情' },\r\n          name: 'PersonnelManagementInfo'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  async created() {\r\n    this.fetchData()\r\n    this.customForm.formItems.find(\r\n      (item) => item.key === 'departmentId'\r\n    ).options = await this.initGetDepartment()\r\n    // 所属单位\r\n    this.customForm.formItems.find((item) => item.key === 'companyId').options =\r\n      await this.initGetCompany()\r\n    this.getGridByCodeRender('PersonnelManagement', [{ key: 'Gender', Tag: 'span', condition: 1, val1: '男', val2: '女' }, { key: 'PersonnelStatus', Tag: 'span', condition: 1, val1: '在职', val2: '离职' }, { key: 'PersonnelType', Tag: 'span', condition: 1, val1: '系统人员', val2: '普通人员' }])\r\n  },\r\n  async mounted() {\r\n    this.customForm.formItems[1].options = await this.getDictionaryDetailListByCode('PatrolResult')\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      querypersonnel({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.customTableConfig.tableData = res.Data.Data\r\n          this.customTableConfig.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    submitForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      this.fetchData()\r\n    },\r\n    async initGetDepartment() {\r\n      const res = await GetDepartment({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    async initGetCompany() {\r\n      const res = await GetCompany({})\r\n      const options = res.Data.map((item, index) => ({\r\n        label: item.Display_Name,\r\n        value: item.Value\r\n      }))\r\n      return options\r\n    },\r\n    handleSizeChange(val) {\r\n      this.customTableConfig.pageSize = val\r\n      this.fetchData()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.customTableConfig.currentPage = val\r\n      this.fetchData()\r\n    },\r\n    handleSelectionChange(data) {\r\n      this.multipleSelection = data\r\n    },\r\n    // async handleExport() {\r\n    //   const res = await DownloadPersonnelsToExcel({\r\n    //     id: this.multipleSelection.map(v => v.Id)\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     downloadFile(res.Data, '人员管理')\r\n    //   } else {\r\n    //     this.$message({\r\n    //       type: 'error',\r\n    //       message: res.Message\r\n    //     })\r\n    //   }\r\n    // },\r\n    // v2 版本导出\r\n    async handleExport() {\r\n      const res = await ExportPersonnelList({\r\n        Id: this.multipleSelection.map(v => v.Id).toString(),\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        downloadFile(res.Data, '人员管理')\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    handleClick(type, data) {\r\n      this.dialogVisible = true\r\n      this.currentComponent = addDialog\r\n      if (type == 'add') {\r\n        this.dialogTitle = '新增'\r\n        this.componentsConfig = {\r\n          name: '',\r\n          mobile: '',\r\n          personnelType: null,\r\n          companyId: '',\r\n          departmentId: '',\r\n          personnelStatus: null\r\n        }\r\n      } else {\r\n        this.dialogTitle = '编辑'\r\n        this.componentsConfig = data\r\n      }\r\n    },\r\n    handleWatch(Id) {\r\n      this.$router.push({\r\n        name: 'PersonnelManagementInfo',\r\n        query: { pg_redirect: this.$route.name, Id }\r\n      })\r\n    },\r\n    handleDele(id) {\r\n      this.$confirm('是否确定删除该数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeletePersonnel({ id }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    handleDownTemplate() {\r\n      DownloadPersonnelsTemplate({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data, '授权名单导入模板')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    async handleImport() {\r\n      this.dialogTitle = '批量导入'\r\n      this.currentComponent = DialogFormImport\r\n      this.componentsConfig = {\r\n        disabled: true,\r\n        title: '批量导入'\r\n      }\r\n      this.dialogVisible = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n  <style scoped lang='scss'>\r\n  .personnelManagement{\r\n    // height: calc(100vh - 90px);\r\n    // overflow: hidden;\r\n  }\r\n</style>\r\n"]}]}