{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\vehicleManagement\\index.vue?vue&type=style&index=0&id=562c409a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\vehicleBarrier\\vehicleManagement\\index.vue", "mtime": 1755506574545}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KQGltcG9ydCAiQC92aWV3cy9idXNpbmVzcy92ZWhpY2xlQmFycmllci9pbmRleC5zY3NzIjsNCi5tdDIwIHsNCiAgbWFyZ2luLXRvcDogMTBweDsNCn0NCjo6di1kZWVwIHsNCiAgLmVsLWRpYWxvZ19fYm9keSB7DQogICAgcGFkZGluZzogMHB4IDIwcHggNXB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/vehicleBarrier/vehicleManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n          ><template #customBtn=\"{ slotScope }\"\r\n            ><el-button type=\"text\" @click=\"setVehicleStatus(slotScope)\"\r\n              >{{ slotScope.Status == 0 ? \"移出\" : \"列入\" }}黑名�?/el-button\r\n            ></template\r\n          ></CustomTable\r\n        >\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"600px\"\r\n      @closed=\"closedDialog\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"dialogRef\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\n\r\nimport baseInfo from \"./dialog/baseInfo.vue\";\r\n\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\nimport { parseTime } from \"@/utils/index.js\";\r\nimport { GetOssUrl } from \"@/api/sys/index\";\r\n\r\nimport importDialog from \"@/views/business/vehicleBarrier/components/import.vue\";\r\nimport exportInfo from \"@/views/business/vehicleBarrier/mixins/export.js\";\r\nimport {\r\n  GetVehiclePageList,\r\n  DelVehicle,\r\n  SetVehicleStatus,\r\n  ExportVehicleData,\r\n  ImportVehicleData,\r\n} from \"@/api/business/vehicleBarrier.js\";\r\nexport default {\r\n  Name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n    baseInfo,\r\n    importDialog,\r\n  },\r\n  mixins: [exportInfo],\r\n  data() {\r\n    return {\r\n      currentComponent: baseInfo,\r\n      componentsConfig: {\r\n        interfaceName: ImportVehicleData,\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"\",\r\n      tableSelection: [],\r\n      selectIds: [],\r\n      ruleForm: {\r\n        VehicleOwnerName: \"\",\r\n        VehicleOwnerPhone: \"\",\r\n        Number: \"\",\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"VehicleOwnerName\", // 字段ID\r\n            label: \"车主姓名\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n            },\r\n          },\r\n          {\r\n            key: \"VehicleOwnerPhone\",\r\n            label: \"车主联系方式\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n          },\r\n          {\r\n            key: \"Number\",\r\n            label: \"车牌\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n          },\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"新增\",\r\n              round: false, // 是否圆角\r\n              plain: false, // 是否朴素\r\n              circle: false, // 是否圆形\r\n              loading: false, // 是否加载�?\n              disabled: true, // 是否禁用\r\n              icon: \"\", //  图标\r\n              autofocus: false, // 是否聚焦\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n            {\r\n              text: \"下载模板\",\r\n              disabled: true, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData([], \"车辆管理模板\", ExportVehicleData);\r\n              },\r\n            },\r\n            {\r\n              text: \"批量导入\",\r\n              disabled: true, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.currentComponent = \"importDialog\";\r\n                this.dialogVisible = true;\r\n                this.dialogTitle = \"批量导入\";\r\n              },\r\n            },\r\n            {\r\n              key: \"batch\",\r\n              disabled: false, // 是否禁用\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.ExportData(\r\n                  {\r\n                    ...this.ruleForm,\r\n                    Ids: this.selectIds.toString(),\r\n                  },\r\n                  \"车辆管理\",\r\n                  ExportVehicleData\r\n                );\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        height: \"100%\",\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: \"selection\",\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"车牌号码\",\r\n            key: \"Number\",\r\n          },\r\n          {\r\n            label: \"车辆品牌\",\r\n            key: \"Brand\",\r\n          },\r\n          {\r\n            label: \"车辆型号\",\r\n            key: \"VehicleModel\",\r\n          },\r\n          {\r\n            label: \"车辆颜色\",\r\n            key: \"VehicleColorImg\",\r\n            render: (row) => {\r\n              if (row.VehicleColorImgUrl) {\r\n                return this.$createElement(\"el-image\", {\r\n                  attrs: {\r\n                    src: row.VehicleColorImgUrl,\r\n                    previewSrcList: [\r\n                      row.VehicleColorImgUrl,\r\n                      row.VehicleImgUrl,\r\n                      row.VehicleNumberImgUrl,\r\n                    ],\r\n                  },\r\n                });\r\n              }\r\n            },\r\n          },\r\n          {\r\n            label: \"车辆照片\",\r\n            key: \"VehicleImg\",\r\n            render: (row) => {\r\n              if (row.VehicleImgUrl) {\r\n                return this.$createElement(\"el-image\", {\r\n                  attrs: {\r\n                    src: row.VehicleImgUrl,\r\n                    previewSrcList: [\r\n                      row.VehicleColorImgUrl,\r\n                      row.VehicleImgUrl,\r\n                      row.VehicleNumberImgUrl,\r\n                    ],\r\n                  },\r\n                });\r\n              }\r\n            },\r\n          },\r\n          {\r\n            label: \"车牌照片\",\r\n            key: \"VehicleNumberImg\",\r\n            render: (row) => {\r\n              if (row.VehicleNumberImgUrl) {\r\n                return this.$createElement(\"el-image\", {\r\n                  attrs: {\r\n                    src: row.VehicleNumberImgUrl,\r\n                    previewSrcList: [\r\n                      row.VehicleColorImgUrl,\r\n                      row.VehicleImgUrl,\r\n                      row.VehicleNumberImgUrl,\r\n                    ],\r\n                  },\r\n                });\r\n              }\r\n            },\r\n          },\r\n          {\r\n            label: \"车辆类型\",\r\n            key: \"TypeName\",\r\n          },\r\n          {\r\n            label: \"车主姓名\",\r\n            key: \"VehicleOwnerName\",\r\n          },\r\n          {\r\n            label: \"车主联系方式\",\r\n            key: \"VehicleOwnerPhoneCollect\",\r\n            otherOptions: {\r\n              showOverflowTooltip: true,\r\n            },\r\n          },\r\n          {\r\n            label: \"停车�?,\r\n            key: \"PackingName\",\r\n          },\r\n          {\r\n            // 车位编码\r\n            label: \"固定停车�?,\r\n            key: \"FixedPackingSpace\",\r\n          },\r\n          {\r\n            label: \"新增时间\",\r\n            key: \"Create_Date\",\r\n          },\r\n          {\r\n            label: \"新增操作�?,\r\n            key: \"Create_UserName\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        tableActionsWidth: 220,\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: true, // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, \"edit\");\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n              disabled: true, // 是否禁用\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"查看详情\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, \"view\");\r\n            },\r\n          },\r\n          // {\r\n          //   actionLabel: '列入黑名�?,\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.setVehicleStatus(row)\r\n          //   }\r\n          // }\r\n        ],\r\n        operateOptions: {\r\n          width: 300, // 操作栏宽�?\n        },\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1;\r\n      console.log(data);\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.fetchData();\r\n    },\r\n    async init() {\r\n      await this.fetchData();\r\n    },\r\n    async fetchData() {\r\n      const res = await GetVehiclePageList({\r\n        ParameterJson: [\r\n          {\r\n            Key: \"\",\r\n            Value: [null],\r\n            Type: \"\",\r\n            Filter_Type: \"\",\r\n          },\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.Total;\r\n        this.handelImage(res.Data.Data);\r\n      }\r\n    },\r\n    handelImage(data) {\r\n      const promises = data.map(async (v) => {\r\n        let VehicleColorUrl = \"\";\r\n        let VehicleUrl = \"\";\r\n        let VehicleNumberUrl = \"\";\r\n        const arr = [];\r\n\r\n        if (v.VehicleColorImg) {\r\n          const vehicleColorRes = await GetOssUrl({ url: v.VehicleColorImg });\r\n          VehicleColorUrl = vehicleColorRes.Data;\r\n        }\r\n\r\n        if (v.VehicleImg) {\r\n          const vehicleRes = await GetOssUrl({ url: v.VehicleImg });\r\n          VehicleUrl = vehicleRes.Data;\r\n        }\r\n\r\n        if (v.VehicleNumberImg) {\r\n          const vehicleNumberRes = await GetOssUrl({ url: v.VehicleNumberImg });\r\n          VehicleNumberUrl = vehicleNumberRes.Data;\r\n        }\r\n\r\n        return (function () {\r\n          // 使用闭包来捕获变量的当前�?\n          const capturedVehicleColorUrl = VehicleColorUrl;\r\n          const capturedVehicleUrl = VehicleUrl;\r\n          const capturedVehicleNumberUrl = VehicleNumberUrl;\r\n\r\n          v.VehicleColorImgUrl = v.VehicleColorImg\r\n            ? capturedVehicleColorUrl\r\n            : \"\";\r\n          v.VehicleImgUrl = v.VehicleImg ? capturedVehicleUrl : \"\";\r\n          v.VehicleNumberImgUrl = v.VehicleNumberImg\r\n            ? capturedVehicleNumberUrl\r\n            : \"\";\r\n          v.Create_Date = parseTime(\r\n            new Date(v.Create_Date),\r\n            \"{y}-{m}-{d} {h}:{i}:{s}\"\r\n          );\r\n          arr.push(\r\n            v.VehicleOwnerPhone,\r\n            v.VehicleOwnerPhone1,\r\n            v.VehicleOwnerPhone2\r\n          );\r\n          v.VehicleOwnerPhoneCollect = arr.filter(Boolean).join(\",\");\r\n\r\n          return v;\r\n        })();\r\n      });\r\n\r\n      Promise.all(promises)\r\n        .then((data) => {\r\n          this.customTableConfig.tableData = data;\r\n          console.log(this.tableData);\r\n        })\r\n        .catch((error) => {\r\n          console.error(error);\r\n        });\r\n    },\r\n    handleCreate() {\r\n      this.currentComponent = \"baseInfo\";\r\n      this.dialogTitle = \"新增\";\r\n      this.dialogVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.init(0, {}, \"add\");\r\n      });\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row);\r\n      console.log(this);\r\n      this.$confirm(\"确认删除�?, {\r\n        type: \"warning\",\r\n      })\r\n        .then(async (_) => {\r\n          const res = await DelVehicle({\r\n            Id: row.Id,\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: \"删除成功\",\r\n              type: \"success\",\r\n            });\r\n            this.init();\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: \"error\",\r\n            });\r\n          }\r\n        })\r\n        .catch((_) => {\r\n          this.$message({\r\n            type: \"info\",\r\n            message: \"已取消删�?,\r\n          });\r\n        });\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type);\r\n      this.currentComponent = \"baseInfo\";\r\n      if (type === \"view\") {\r\n        this.dialogTitle = \"查看\";\r\n      } else if (type === \"edit\") {\r\n        this.dialogTitle = \"编辑\";\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.dialogRef.init(index, row, type);\r\n      });\r\n\r\n      this.dialogVisible = true;\r\n    },\r\n    // 列入黑白名单\r\n    setVehicleStatus(row) {\r\n      this.$confirm(\r\n        `确认${row.Status == 0 ? \"移出\" : \"列入\"}黑名�?`,\r\n        `确认${row.Status == 0 ? \"移出\" : \"列入\"}黑名�?`,\r\n        {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }\r\n      )\r\n        .then(() => {\r\n          SetVehicleStatus({ id: row.Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"保存成功!\",\r\n              });\r\n              this.init();\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: \"error\",\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"info\",\r\n            message: \"已取消删�?,\r\n          });\r\n        });\r\n    },\r\n    // 关闭弹窗\r\n    closedDialog() {\r\n      this.$refs.dialogRef.closeClearForm();\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.onFresh();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.onFresh();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      const Ids = [];\r\n      this.tableSelection = selection;\r\n      this.tableSelection.forEach((item) => {\r\n        Ids.push(item.Id);\r\n      });\r\n      console.log(Ids);\r\n      this.selectIds = Ids;\r\n      console.log(this.tableSelection);\r\n      // if (this.tableSelection.length > 0) {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = false\r\n      // } else {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = true\r\n      // }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"@/views/business/vehicleBarrier/index.scss\";\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n::v-deep {\r\n  .el-dialog__body {\r\n    padding: 0px 20px 5px;\r\n  }\r\n}\r\n</style>\r\n"]}]}