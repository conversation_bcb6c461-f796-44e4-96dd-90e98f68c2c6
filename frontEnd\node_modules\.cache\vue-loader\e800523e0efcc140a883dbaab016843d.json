{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomTable\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\businessComponents\\CustomTable\\index.vue", "mtime": 1755682470696}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/businessComponents/CustomTable", "sourcesContent": ["\n<template>\n  <div class=\"CustomTable\">\n    <div\n      v-if=\"customTableObj.buttonConfig.buttonList.length > 0\"\n      class=\"button\"\n    >\n      <CustomButton :custom-button-config=\"customTableObj.buttonConfig\" />\n    </div>\n    <div\n      class=\"table\"\n      :style=\"{\n        marginTop:\n          customTableObj.buttonConfig.buttonList.length == 0 ? 0 : '10px',\n      }\"\n    >\n      <el-table\n        ref=\"table\"\n        v-loading=\"customTableObj.loading\"\n        :data=\"tableData\"\n        :border=\"customTableObj.border == false ? false : true\"\n        :fit=\"true\"\n        stripe\n        :height=\"customTableObj.height ? customTableObj.height : tableHeight\"\n        :max-height=\"\n          customTableObj.height ? customTableObj.height : tableHeight\n        \"\n        style=\"width: 100%\"\n        :row-key=\"showSelection && customTableObj.rowKey ? customTableObj.rowKey : ''\"\n        @selection-change=\"selectionChange\"\n        @select=\"select\"\n        @select-all=\"selectAll\"\n      >\n        <el-table-column\n          v-if=\"showSelection\"\n          type=\"selection\"\n          label=\"selection\"\n          :reserve-selection=\"!!customTableObj.rowKey\"\n        />\n        <el-table-column v-if=\"showIndex\" type=\"index\" label=\"序号\" fixed=\"left\" />\n        <!-- 循环动态列 -->\n        <template v-for=\"(column, index) in tableColumns.columns\">\n          <el-table-column\n            v-if=\"!column.hide\"\n            :key=\"index\"\n            v-bind=\"column.otherOptions\"\n            :prop=\"column.key\"\n            :label=\"column.label\"\n            :min-width=\"column.width\"\n            :resizable=\"true\"\n          >\n            <!-- show-overflow-tooltip -->\n            <!-- :width=\"column.width\" -->\n            <!-- :width=\"flexColumnWidth(column.label, column.key)\" -->\n            <template v-slot=\"scope\">\n              <render-dom\n                v-if=\"column.render\"\n                :render=\"() => column.render(scope.row)\"\n              />\n              <span v-else style=\"white-space: nowrap\">\n                {{\n                  scope.row[column.key] === 0\n                    ? \"0\"\n                    : scope.row[column.key]\n                      ? scope.row[column.key]\n                      : \"-\"\n                }}</span>\n            </template>\n          </el-table-column>\n        </template>\n        <el-table-column\n          v-if=\"tableColumns.actions.length > 0\"\n          label=\"操作\"\n          v-bind=\"customTableObj.operateOptions\"\n          fixed=\"right\"\n          :min-width=\"customTableObj.tableActionsWidth\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              v-for=\"(item, index) in tableColumns.actions\"\n              :key=\"index\"\n              v-bind=\"item.otherOptions\"\n              size=\"mini\"\n              @click=\"item.onclick(scope.$index, scope.row)\"\n            >{{ item.actionLabel }}</el-button>\n            <slot :slot-scope=\"scope.row\" name=\"customBtn\" />\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <div v-if=\"!customTableObj.disablidPagination\" class=\"pagination\">\n      <el-pagination\n        :total=\"customTableObj.total\"\n        :page-sizes=\"customTableObj.pageSizeOptions\"\n        :current-page=\"customTableObj.currentPage\"\n        :page-size=\"customTableObj.pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n      />\n    </div>\n  </div>\n</template>\n<script>\nimport CustomButton from '@/businessComponents/CustomButton/index.vue'\nexport default {\n  components: {\n    CustomButton,\n    renderDom: {\n      functional: true,\n      props: {\n        render: Function\n      },\n      render(createElement, renDom) {\n        return <div>{renDom.props.render()}</div>\n      }\n    }\n  },\n  props: {\n    customTableConfig: {\n      type: Object,\n      default: () => { }\n    }\n  },\n  data() {\n    return {\n      tableHeight: '100%', // 页面高度\n      showIndex: false,\n      showSelection: false\n    }\n  },\n\n  computed: {\n    customTableObj() {\n      return this.customTableConfig\n    },\n    tableColumns() {\n      const jsonArray = this.customTableObj.tableColumns\n      this.showIndex = jsonArray.some(\n        (obj) =>\n          obj.otherOptions &&\n          obj.otherOptions.type &&\n          Object.keys(obj.otherOptions).length > 0 &&\n          obj.otherOptions.type === 'index'\n      )\n      this.showSelection = jsonArray.some(\n        (obj) =>\n          obj.otherOptions &&\n          obj.otherOptions.type &&\n          Object.keys(obj.otherOptions).length > 0 &&\n          obj.otherOptions.type === 'selection'\n      )\n      const newsColumns = jsonArray\n        .filter((obj) => {\n          // 检查 otherOptions 是否存在，并且 type 是否等于 'index'\n          return !obj.otherOptions || obj.otherOptions.type !== 'index'\n        })\n        .filter((obj) => {\n          // 检查 otherOptions 是否存在，并且 type 是否等于 'index'\n          return !obj.otherOptions || obj.otherOptions.type !== 'selection'\n        })\n\n      // .filter(\n      //   (obj) =>\n      //     // obj.otherOptions &&\n      //     // obj.otherOptions.type &&\n      //     // Object.keys(obj.otherOptions).length > 0 &&\n      //     obj.otherOptions.type != \"index\"\n      // )\n      // .filter(\n      //   (obj) =>\n      //     // obj.otherOptions &&\n      //     // obj.otherOptions.type &&\n      //     // Object.keys(obj.otherOptions).length > 0 &&\n      //     obj.otherOptions.type != \"selection\"\n      // );\n      newsColumns.forEach((element) => {\n        element.width = this.flexColumnWidth(element.label, element.key)\n      })\n      const tableWidth = this.getTableWidth()\n      const realWidth = newsColumns.reduce((cur, next) => {\n        return cur + next.width\n      }, 0)\n      console.log(tableWidth, realWidth, 'realWidth')\n\n      newsColumns.forEach((element) => {\n        const width =\n          (tableWidth - this.customTableObj.tableActionsWidth - realWidth) /\n          newsColumns.length\n        if (!element.render) {\n          element.width =\n            tableWidth - this.customTableObj.tableActionsWidth > realWidth\n              ? this.flexColumnWidth(element.label, element.key) + width\n              : this.flexColumnWidth(element.label, element.key)\n        } else {\n          element.width = 140\n        }\n      })\n      return {\n        columns: newsColumns,\n        actions: this.customTableObj.tableActions\n      }\n    },\n    tableData() {\n      const data = []\n      return [].concat(data, this.customTableObj.tableData)\n    }\n  },\n  watch: {\n    // 监视name属性的变化\n    tableColumns(newValue, oldValue) {\n      if (newValue.length > 0) {\n        setTimeout(() => {\n          this.getTableHeight()\n          window.addEventListener('resize', this.getTableHeight) // 监听窗口大小变化，重新计算高度 }\n        }, 0)\n      }\n    }\n  },\n  mounted() { },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.getTableHeight) // 移除事件监听器\n  },\n  methods: {\n    getTableWidth() {\n      // 页面表格宽度\n      const table = document.querySelector('.app-main')\n      if (table) {\n        const tableWidth = table.clientWidth\n        console.log(tableWidth, 'tableWidth')\n        return tableWidth\n      }\n    },\n    getTextWidth(str) {\n      let width = 0\n      const html = document.createElement('span')\n      html.innerText = str\n      html.className = 'getTextWidth'\n      document.querySelector('body').appendChild(html)\n      width = document.querySelector('.getTextWidth').offsetWidth\n      document.querySelector('.getTextWidth').remove()\n      console.log()\n      return width\n    },\n    getMaxLength(arr) {\n      return arr.reduce((acc, item) => {\n        if (item) {\n          const calcLen = this.getTextWidth(item)\n          if (acc < calcLen) {\n            acc = calcLen\n          }\n        }\n        return acc\n      }, 0)\n    },\n    flexColumnWidth(label, prop) {\n      // 1.获取该列的所有数据\n      const arr = this.tableData.map((x) => x[prop])\n      arr.push(label) // 把每列的表头也加进去算\n      // console.log(arr)\n      // 2.计算每列内容最大的宽度 + 表格的内间距（依据实际情况而定）\n      return this.getMaxLength(arr) + 30\n    },\n    getTableHeight() {\n      // 计算页面高度，并减去其他元素的高度（如页眉、页脚等）\n      const pageHeight = document.documentElement.clientHeight\n      const otherElementHeight = 340 // 其他元素的高度，根据实际情况设置\n      this.tableHeight = pageHeight - otherElementHeight\n    },\n    handleSizeChange(val) {\n      this.$emit('handleSizeChange', val)\n    },\n    handleCurrentChange(val) {\n      this.$emit('handleCurrentChange', val)\n    },\n    selectionChange(selection) {\n      this.$emit('handleSelectionChange', selection)\n    },\n    select(selection, row) {\n      this.$emit('select', selection, row)\n    },\n    selectAll(selection) {\n      console.log('ffffffffffffffffff ', selection)\n      this.$emit('selectall', selection, this.tableData)\n    },\n    setSelection(rowList, selected) {\n      rowList.map((tmp) => {\n        const row = this.tableData.find((item) => item.Id === tmp.Id)\n        this.$nextTick(() => {\n          this.$refs.table.toggleRowSelection(row, selected)\n        })\n      })\n    },\n    clearSelection()\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.CustomTable {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  .button {\n    display: flex;\n    flex-direction: row;\n    justify-content: flex-end;\n  }\n  .table {\n    flex: 1;\n    overflow: hidden;\n    margin-top: 10px;\n  }\n  .pagination {\n    margin-top: 10px;\n    display: flex;\n    flex-direction: row;\n    justify-content: flex-end;\n  }\n  .no-wrap-cell {\n    white-space: nowrap;\n  }\n  // display: flex;\n  // flex-direction: column;\n  // background-color: white;\n  // // padding: 10px 15px;\n  // .table{\n  //   padding: 2px 5px;\n  // }\n  // .el-pagination{\n  //   display: flex;\n  //   justify-content: flex-end;\n\n  // }\n}\n</style>\n"]}]}