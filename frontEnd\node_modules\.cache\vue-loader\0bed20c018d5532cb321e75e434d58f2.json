{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJenergyManagement\\alarmDetail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJenergyManagement\\alarmDetail\\index.vue", "mtime": 1755506574452}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/business/SZCJenergyManagement/alarmDetail", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        ><template\r\n          #customBtn=\"{ slotScope }\"\r\n        ><el-button\r\n          v-if=\"slotScope.Handle_Status == 1\"\r\n          type=\"text\"\r\n          @click=\"handelClose(slotScope)\"\r\n        >关闭</el-button></template></CustomTable>\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      top=\"6vh\"\r\n      :destroy-on-close=\"true\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n      /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { parseTime } from '@/utils'\r\n// import { baseUrl } from '@/utils/baseurl'\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport DialogForm from './dialogForm.vue'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport {\r\n  // GetDictionaryDetailListByCode,\r\n  // ExportData,\r\n  GetWarningListSZCJ,\r\n  GetWarningTypeList,\r\n  ExportWarningListSZCJ,\r\n  UpdateWarningStatus\r\n} from '@/api/business/energyManagement'\r\nexport default {\r\n  name: 'AlarmDetail',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Content: '',\r\n        EnergyType: '',\r\n        WarningType: '',\r\n        Position: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Content', // 字段ID\r\n            label: '点表编号或名�?, // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            placeholder: '输入点表编号或名称进行搜�?,\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EnergyType',\r\n            label: '能耗类�?,\r\n            type: 'select',\r\n            placeholder: '请选择能耗类�?,\r\n            options: [\r\n              { label: '用电�?, value: 'electric' },\r\n              { label: '用水�?, value: 'warter' },\r\n              { label: '用氧气量', value: 'gas' }\r\n            ], // 类型数据列表\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'WarningType',\r\n            label: '告警类型',\r\n            type: 'select',\r\n            placeholder: '请选择告警类型',\r\n            options: [], // 类型数据列表\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Handle_Status',\r\n            label: '告警状�?,\r\n            type: 'select',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择告警状�?\r\n            },\r\n            options: [\r\n              {\r\n                label: '告警�?,\r\n                value: 1\r\n              },\r\n              {\r\n                label: '已关�?,\r\n                value: 2\r\n              },\r\n              {\r\n                label: '已处�?,\r\n                value: 3\r\n              }\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position', // 字段ID\r\n            label: '安装位置', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            placeholder: '请输入安装位�?,\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleAllExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        operateOptions:{\r\n          width:140\r\n        },\r\n        tableColumns: [\r\n          // {\r\n          //   width: 50,\r\n          //   otherOptions: {\r\n          //     type: 'selection',\r\n          //     align: 'center'\r\n          //   }\r\n          // },\r\n          {\r\n            width: 60,\r\n            label: '序号',\r\n            otherOptions: {\r\n              type: 'index',\r\n              align: 'center'\r\n            } // key\r\n            // otherOptions: {\r\n            //   width: 180, // 宽度\r\n            //   fixed: 'left', // left, right\r\n            //   align: 'center' //\tleft/center/right\r\n            // }\r\n          }\r\n          //   {\r\n          //     label: '设备编号',\r\n          //     key: 'HId'\r\n          //   }\r\n        ],\r\n        tableData: [],\r\n        tableActionsWidth: 120,\r\n        tableActions: [\r\n          // {\r\n          //   actionLabel: '关闭',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handelClose(row)\r\n          //   }\r\n          // },\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          }\r\n\r\n          // {\r\n          //   actionLabel: '删除',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleDelete(index, row)\r\n          //   }\r\n          // }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.getBaseData()\r\n    this.init()\r\n  },\r\n  methods: {\r\n    getBaseData() {\r\n      // 获取点表类型\r\n      // GetDictionaryDetailListByCode({ dictionaryCode: 'PointTableType' }).then(res => {\r\n      //   if (res.IsSucceed) {\r\n      //     const data = res.Data.map(item => {\r\n      //       return {\r\n      //         label: item.Display_Name,\r\n      //         value: item.Value\r\n      //       }\r\n      //     })\r\n      //     this.customForm.formItems[1].options = data\r\n      //   } else {\r\n      //     this.$message({\r\n      //       type: 'error',\r\n      //       data: res.Message\r\n      //     })\r\n      //   }\r\n      // })\r\n      // 获取告警类型\r\n      GetWarningTypeList().then(res => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data.map(item => {\r\n            return {\r\n              label: item.Type,\r\n              value: item.Type\r\n            }\r\n          })\r\n          this.customForm.formItems[2].options = data\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            data: res.Message\r\n          })\r\n        }\r\n      })\r\n      // 获取表格配置\r\n      GetGridByCode({ code: 'alarm_detail_list' }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data.ColumnList.map(item => {\r\n            return {\r\n              label: item.Display_Name,\r\n              key: item.Code\r\n            }\r\n          })\r\n          this.customTableConfig.tableColumns.push(...data)\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getWarningList()\r\n    },\r\n    init() {\r\n      this.getWarningList()\r\n    },\r\n    async getWarningList() {\r\n      const res = await GetWarningListSZCJ({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.customTableConfig.tableData = res.Data.Data.map(item => {\r\n          item.Time = item.Time ? parseTime(new Date(item.Time), '{y}-{m}-{d} {h}:{i}:{s}') : ''\r\n          return item\r\n        })\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.componentsConfig = { ...row }\r\n        this.$nextTick(() => {\r\n          this.$refs.currentComponent.init(type)\r\n        })\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        this.componentsConfig = { ...row }\r\n        this.$nextTick(() => {\r\n          this.$refs.currentComponent.init(type)\r\n        })\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    // async handleExport() {\r\n    //   console.log(this.ruleForm)\r\n    //   console.log(this.tableSelection, 'this.tableSelection')\r\n    //   const res = await ExportData({\r\n    //     IsAll: false,\r\n    //     Ids: this.tableSelection.map((item) => item.Id),\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //     // const url = new URL(res.Data, baseUrl())\r\n    //     // window.open(url.href, '_blank')\r\n    //     // this.$message({\r\n    //     //   type: 'success',\r\n    //     //   message: '导出成功!'\r\n    //     // })\r\n    //   }\r\n    // },\r\n    async handleAllExport() {\r\n      const res = await ExportWarningListSZCJ({\r\n        IsAll: true,\r\n        Ids: [],\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n        // const url = new URL(res.Data, baseUrl())\r\n        // window.open(url.href, '_blank')\r\n        this.$message({\r\n          type: 'success',\r\n          message: '导出成功!'\r\n        })\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n      this.customTableConfig.buttonConfig.buttonList[1].disabled = selection.length === 0\r\n    },\r\n    handelClose(row) {\r\n      if (row.HandleStatusStr == '关闭') {\r\n        this.$message.warning('请勿重复操作')\r\n      } else {\r\n        UpdateWarningStatus({ id: row.Id, wid: row.WId, StatusEnum: 2 }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('操作成功')\r\n            this.init()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n  <style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n\r\n"]}]}