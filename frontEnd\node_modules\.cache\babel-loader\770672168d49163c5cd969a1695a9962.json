{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJbehaviorAnalysis\\behaviorAnalysisAlarm\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\SZCJbehaviorAnalysis\\behaviorAnalysisAlarm\\index.vue", "mtime": 1755674552403}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CustomLayout", "CustomTable", "CustomForm", "dialogForm", "dayjs", "GetBehaviorWarningListSZCJ", "GetBehaviorWarningEntity", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GetDictionaryDetailListByCode", "name", "components", "data", "_this", "currentComponent", "componentsConfig", "Data", "componentsFuns", "open", "dialogVisible", "close", "onFresh", "dialogTitle", "tableSelection", "ruleForm", "DeviceName", "WarningType", "HandleStatus", "Date", "BeginWarningTime", "EndWarningTime", "customForm", "formItems", "key", "label", "type", "otherOptions", "clearable", "disabled", "placeholder", "change", "e", "console", "log", "length", "format", "options", "GetTypesByModule", "value", "rules", "customFormButtons", "submitName", "resetName", "customTableConfig", "buttonConfig", "pageSizeOptions", "currentPage", "pageSize", "total", "tableColumns", "align", "width", "render", "row", "$createElement", "style", "color", "tableData", "operateOptions", "tableActions", "actionLabel", "onclick", "index", "handleEdit", "handleRebroadcast", "computed", "created", "init", "getDictionaryDetailListByCode", "methods", "handleClose", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "SetWarningStatus", "Status", "Ids", "map", "item", "Id", "sent", "IsSucceed", "$message", "success", "stop", "_this3", "_callee2", "result", "warningType", "_callee2$", "_context2", "dictionaryCode", "Value", "Display_Name", "find", "searchForm", "resetForm", "GetBehaviorWarningList", "_this4", "_callee3", "_callee3$", "_context3", "_objectSpread", "Page", "PageSize", "TotalCount", "error", "Message", "_this5", "_callee4", "_callee4$", "_context4", "ID", "handleSizeChange", "val", "concat", "handleCurrentChange", "handleSelectionChange", "selection"], "sources": ["src/views/business/SZCJbehaviorAnalysis/behaviorAnalysisAlarm/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\nimport dialogForm from \"./dialogForm.vue\";\r\nimport dayjs from \"dayjs\";\r\n\r\nimport {\r\n  GetBehaviorWarningListSZCJ,\r\n  GetBehaviorWarningEntity,\r\n  TriggerBehaviorWarning,\r\n} from \"@/api/business/behaviorAnalysis\";\r\nimport { GetDictionaryDetailListByCode } from \"@/api/sys\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    CustomForm,\r\n    CustomLayout,\r\n  },\r\n  data() {\r\n    return {\r\n      currentComponent: dialogForm,\r\n      componentsConfig: {\r\n        Data: {},\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"告警详情\",\r\n      tableSelection: [],\r\n      ruleForm: {\r\n        DeviceName: \"\",\r\n        WarningType: \"\",\r\n        HandleStatus: \"\",\r\n        Date: [],\r\n        BeginWarningTime: null,\r\n        EndWarningTime: null,\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Date\", // 字段ID\r\n            label: \"告警时间\", // Form的label\r\n            type: \"datePicker\", // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              type: \"daterange\",\r\n              disabled: false,\r\n              placeholder: \"请输入...\",\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n              if (e && e.length > 0) {\r\n                this.ruleForm.BeginWarningTime = dayjs(e[0]).format(\r\n                  \"YYYY-MM-DD\"\r\n                );\r\n                this.ruleForm.EndWarningTime = dayjs(e[1]).format(\"YYYY-MM-DD\");\r\n              }\r\n            },\r\n          },\r\n          {\r\n            key: \"WarningType\",\r\n            label: \"告警类型\",\r\n            type: \"select\",\r\n            options: [],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n              this.GetTypesByModule();\r\n            },\r\n          },\r\n          {\r\n            key: \"DeviceName\",\r\n            label: \"告警设备\",\r\n            type: \"input\",\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"HandleStatus\",\r\n            label: \"状态\",\r\n            type: \"select\",\r\n            options: [\r\n              {\r\n                label: \"待广播\",\r\n                value: \"1\",\r\n              },\r\n              {\r\n                label: \"已提交\",\r\n                value: \"2\",\r\n              },\r\n              {\r\n                label: \"提交成功\",\r\n                value: \"3\",\r\n              },\r\n              {\r\n                label: \"提交失败\",\r\n                value: \"4\",\r\n              },\r\n            ],\r\n            otherOptions: {\r\n              clearable: true,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n        ],\r\n        rules: {},\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          // buttonList: [\r\n          //   {\r\n          //     text: \"批量关闭\",\r\n          //     onclick: (item) => {\r\n          //       console.log(item);\r\n          //       this.handleClose();\r\n          //     },\r\n          //   },\r\n          // ],\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          // {\r\n          //   otherOptions: {\r\n          //     type: \"selection\",\r\n          //     align: \"center\",\r\n          //     fixed: \"left\",\r\n          //   },\r\n          // },\r\n          {\r\n            label: \"告警时间\",\r\n            key: \"WarningTime\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"告警类型\",\r\n            key: \"WarningTypeDes\",\r\n            width: 140,\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"告警设备\",\r\n            key: \"DeviceName\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"告警设备编码\",\r\n            key: \"DeviceCode\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"设备地址\",\r\n            key: \"Position\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"联动广播\",\r\n            key: \"BroadcastEquipmentCount\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"广播时间\",\r\n            key: \"BroadcastTime\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            label: \"状态\",\r\n            key: \"HandleStatus\",\r\n            otherOptions: {\r\n              align: \"center\",\r\n            },\r\n            render: (row) => {\r\n              if (row.HandleStatus == 1) {\r\n                return this.$createElement(\"span\", {}, \"待广播\");\r\n              } else if (row.HandleStatus == 2) {\r\n                return this.$createElement(\"span\", {}, \"已提交\");\r\n              } else if (row.HandleStatus == 3) {\r\n                return this.$createElement(\r\n                  \"span\",\r\n                  {\r\n                    style: {\r\n                      color: \"green\",\r\n                    },\r\n                  },\r\n                  \"提交成功\"\r\n                );\r\n              } else if (row.HandleStatus == 4) {\r\n                return this.$createElement(\r\n                  \"span\",\r\n                  {\r\n                    style: {\r\n                      color: \"red\",\r\n                    },\r\n                  },\r\n                  \"提交失败\"\r\n                );\r\n              }\r\n              return this.$createElement(\"span\", {}, \"\");\r\n            },\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          align: \"center\",\r\n          width: \"180\",\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看详情\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(row);\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"重新广播\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleRebroadcast(row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init();\r\n\r\n    this.getDictionaryDetailListByCode();\r\n  },\r\n  // mixins: [getGridByCode],\r\n  methods: {\r\n    async handleClose() {\r\n      const res = await SetWarningStatus({\r\n        Status: \"2\",\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.$message.success(\"操作成功\");\r\n        this.onFresh();\r\n      }\r\n    },\r\n    async getDictionaryDetailListByCode() {\r\n      const res = await GetDictionaryDetailListByCode({\r\n        dictionaryCode: \"BehaviorWarningType\",\r\n      });\r\n      if (res.IsSucceed) {\r\n        let result = res.Data || [];\r\n        let warningType = result.map((item) => ({\r\n          value: item.Value,\r\n          label: item.Display_Name,\r\n        }));\r\n        this.customForm.formItems.find(\r\n          (item) => item.key == \"WarningType\"\r\n        ).options = warningType;\r\n      }\r\n    },\r\n\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.ruleForm.BeginWarningTime = null;\r\n      this.ruleForm.EndWarningTime = null;\r\n      this.ruleForm.Date = null;\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.GetBehaviorWarningList();\r\n    },\r\n\r\n    init() {\r\n      // this.getGridByCode(\"AccessControlAlarmDetails1\");\r\n      this.GetBehaviorWarningList();\r\n    },\r\n    async GetBehaviorWarningList() {\r\n      const res = await GetBehaviorWarningListSZCJ({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data;\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n\r\n    async handleRebroadcast(row) {\r\n      const res = await TriggerBehaviorWarning({\r\n        ID: row.Id,\r\n      });\r\n      if (res.IsSucceed) {\r\n        this.$message.success(\"操作成功\");\r\n        this.init();\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.GetBehaviorWarningList();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.GetBehaviorWarningList();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleEdit(row) {\r\n      this.dialogVisible = true;\r\n      this.componentsConfig.Data = row;\r\n      this.componentsConfig = {\r\n        type: \"edit\",\r\n        data: row,\r\n      };\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,OAAAA,YAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,KAAA;AAEA,SACAC,0BAAA,EACAC,wBAAA,EACAC,sBAAA,QACA;AACA,SAAAC,6BAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAT,WAAA,EAAAA,WAAA;IACAC,UAAA,EAAAA,UAAA;IACAF,YAAA,EAAAA;EACA;EACAW,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,gBAAA,EAAAV,UAAA;MACAW,gBAAA;QACAC,IAAA;MACA;MACAC,cAAA;QACAC,IAAA,WAAAA,KAAA;UACAL,KAAA,CAAAM,aAAA;QACA;QACAC,KAAA,WAAAA,MAAA;UACAP,KAAA,CAAAM,aAAA;UACAN,KAAA,CAAAQ,OAAA;QACA;MACA;MACAF,aAAA;MACAG,WAAA;MACAC,cAAA;MACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,YAAA;QACAC,IAAA;QACAC,gBAAA;QACAC,cAAA;MACA;MACAC,UAAA;QACAC,SAAA,GACA;UACAC,GAAA;UAAA;UACAC,KAAA;UAAA;UACAC,IAAA;UAAA;UACAC,YAAA;YACA;YACAC,SAAA;YACAF,IAAA;YACAG,QAAA;YACAC,WAAA;UACA;UACAC,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;YACA,IAAAA,CAAA,IAAAA,CAAA,CAAAG,MAAA;cACA/B,KAAA,CAAAW,QAAA,CAAAK,gBAAA,GAAAxB,KAAA,CAAAoC,CAAA,KAAAI,MAAA,CACA,YACA;cACAhC,KAAA,CAAAW,QAAA,CAAAM,cAAA,GAAAzB,KAAA,CAAAoC,CAAA,KAAAI,MAAA;YACA;UACA;QACA,GACA;UACAZ,GAAA;UACAC,KAAA;UACAC,IAAA;UACAW,OAAA;UACAV,YAAA;YACAC,SAAA;UACA;UACAG,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;YACA5B,KAAA,CAAAkC,gBAAA;UACA;QACA,GACA;UACAd,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,YAAA;YACAC,SAAA;UACA;UACAG,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,GACA;UACAR,GAAA;UACAC,KAAA;UACAC,IAAA;UACAW,OAAA,GACA;YACAZ,KAAA;YACAc,KAAA;UACA,GACA;YACAd,KAAA;YACAc,KAAA;UACA,GACA;YACAd,KAAA;YACAc,KAAA;UACA,GACA;YACAd,KAAA;YACAc,KAAA;UACA,EACA;UACAZ,YAAA;YACAC,SAAA;UACA;UACAG,MAAA,WAAAA,OAAAC,CAAA;YACA;YACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACA;QACA,EACA;QACAQ,KAAA;QACAC,iBAAA;UACAC,UAAA;UACAC,SAAA;QACA;MACA;MACAC,iBAAA;QACAC,YAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QAAA,CACA;QACA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACAzB,KAAA;UACAD,GAAA;UACAG,YAAA;YACAwB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACA4B,KAAA;UACAzB,YAAA;YACAwB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAG,YAAA;YACAwB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAG,YAAA;YACAwB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAG,YAAA;YACAwB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAG,YAAA;YACAwB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAG,YAAA;YACAwB,KAAA;UACA;QACA,GACA;UACA1B,KAAA;UACAD,GAAA;UACAG,YAAA;YACAwB,KAAA;UACA;UACAE,MAAA,WAAAA,OAAAC,GAAA;YACA,IAAAA,GAAA,CAAApC,YAAA;cACA,OAAAd,KAAA,CAAAmD,cAAA;YACA,WAAAD,GAAA,CAAApC,YAAA;cACA,OAAAd,KAAA,CAAAmD,cAAA;YACA,WAAAD,GAAA,CAAApC,YAAA;cACA,OAAAd,KAAA,CAAAmD,cAAA,CACA,QACA;gBACAC,KAAA;kBACAC,KAAA;gBACA;cACA,GACA,MACA;YACA,WAAAH,GAAA,CAAApC,YAAA;cACA,OAAAd,KAAA,CAAAmD,cAAA,CACA,QACA;gBACAC,KAAA;kBACAC,KAAA;gBACA;cACA,GACA,MACA;YACA;YACA,OAAArD,KAAA,CAAAmD,cAAA;UACA;QACA,EACA;QACAG,SAAA;QACAC,cAAA;UACAR,KAAA;UACAC,KAAA;QACA;QACAQ,YAAA,GACA;UACAC,WAAA;UACAlC,YAAA;YACAD,IAAA;UACA;UACAoC,OAAA,WAAAA,QAAAC,KAAA,EAAAT,GAAA;YACAlD,KAAA,CAAA4D,UAAA,CAAAV,GAAA;UACA;QACA,GACA;UACAO,WAAA;UACAlC,YAAA;YACAD,IAAA;UACA;UACAoC,OAAA,WAAAA,QAAAC,KAAA,EAAAT,GAAA;YACAlD,KAAA,CAAA6D,iBAAA,CAAAX,GAAA;UACA;QACA;MAEA;IACA;EACA;EACAY,QAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;IAEA,KAAAC,6BAAA;EACA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAC,gBAAA;gBACAC,MAAA;gBACAC,GAAA,EAAAb,MAAA,CAAA1D,cAAA,CAAAwE,GAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAC,EAAA;gBAAA;cACA;YAAA;cAHAX,GAAA,GAAAG,QAAA,CAAAS,IAAA;cAIA,IAAAZ,GAAA,CAAAa,SAAA;gBACAlB,MAAA,CAAAmB,QAAA,CAAAC,OAAA;gBACApB,MAAA,CAAA5D,OAAA;cACA;YAAA;YAAA;cAAA,OAAAoE,QAAA,CAAAa,IAAA;UAAA;QAAA,GAAAjB,OAAA;MAAA;IACA;IACAP,6BAAA,WAAAA,8BAAA;MAAA,IAAAyB,MAAA;MAAA,OAAArB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAoB,SAAA;QAAA,IAAAlB,GAAA,EAAAmB,MAAA,EAAAC,WAAA;QAAA,OAAAvB,mBAAA,GAAAI,IAAA,UAAAoB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlB,IAAA,GAAAkB,SAAA,CAAAjB,IAAA;YAAA;cAAAiB,SAAA,CAAAjB,IAAA;cAAA,OACAlF,6BAAA;gBACAoG,cAAA;cACA;YAAA;cAFAvB,GAAA,GAAAsB,SAAA,CAAAV,IAAA;cAGA,IAAAZ,GAAA,CAAAa,SAAA;gBACAM,MAAA,GAAAnB,GAAA,CAAAtE,IAAA;gBACA0F,WAAA,GAAAD,MAAA,CAAAV,GAAA,WAAAC,IAAA;kBAAA;oBACAhD,KAAA,EAAAgD,IAAA,CAAAc,KAAA;oBACA5E,KAAA,EAAA8D,IAAA,CAAAe;kBACA;gBAAA;gBACAR,MAAA,CAAAxE,UAAA,CAAAC,SAAA,CAAAgF,IAAA,CACA,UAAAhB,IAAA;kBAAA,OAAAA,IAAA,CAAA/D,GAAA;gBAAA,CACA,EAAAa,OAAA,GAAA4D,WAAA;cACA;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IAEAS,UAAA,WAAAA,WAAArG,IAAA;MACA8B,OAAA,CAAAC,GAAA,CAAA/B,IAAA;MACA,KAAAyC,iBAAA,CAAAG,WAAA;MACA,KAAAnC,OAAA;IACA;IACA6F,SAAA,WAAAA,UAAA;MACA,KAAA1F,QAAA,CAAAK,gBAAA;MACA,KAAAL,QAAA,CAAAM,cAAA;MACA,KAAAN,QAAA,CAAAI,IAAA;MACA,KAAAP,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MACA,KAAA8F,sBAAA;IACA;IAEAtC,IAAA,WAAAA,KAAA;MACA;MACA,KAAAsC,sBAAA;IACA;IACAA,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MAAA,OAAAlC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAiC,SAAA;QAAA,IAAA/B,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA+B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7B,IAAA,GAAA6B,SAAA,CAAA5B,IAAA;YAAA;cAAA4B,SAAA,CAAA5B,IAAA;cAAA,OACArF,0BAAA,CAAAkH,aAAA;gBACAC,IAAA,EAAAL,MAAA,CAAA/D,iBAAA,CAAAG,WAAA;gBACAkE,QAAA,EAAAN,MAAA,CAAA/D,iBAAA,CAAAI;cAAA,GACA2D,MAAA,CAAA5F,QAAA,CACA;YAAA;cAJA8D,GAAA,GAAAiC,SAAA,CAAArB,IAAA;cAKA,IAAAZ,GAAA,CAAAa,SAAA;gBACAiB,MAAA,CAAA/D,iBAAA,CAAAc,SAAA,GAAAmB,GAAA,CAAAtE,IAAA,CAAAA,IAAA;gBACAoG,MAAA,CAAA/D,iBAAA,CAAAK,KAAA,GAAA4B,GAAA,CAAAtE,IAAA,CAAA2G,UAAA;cACA;gBACAP,MAAA,CAAAhB,QAAA,CAAAwB,KAAA,CAAAtC,GAAA,CAAAuC,OAAA;cACA;YAAA;YAAA;cAAA,OAAAN,SAAA,CAAAjB,IAAA;UAAA;QAAA,GAAAe,QAAA;MAAA;IACA;IAEA3C,iBAAA,WAAAA,kBAAAX,GAAA;MAAA,IAAA+D,MAAA;MAAA,OAAA5C,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA2C,SAAA;QAAA,IAAAzC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAyC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvC,IAAA,GAAAuC,SAAA,CAAAtC,IAAA;YAAA;cAAAsC,SAAA,CAAAtC,IAAA;cAAA,OACAnF,sBAAA;gBACA0H,EAAA,EAAAnE,GAAA,CAAAkC;cACA;YAAA;cAFAX,GAAA,GAAA2C,SAAA,CAAA/B,IAAA;cAGA,IAAAZ,GAAA,CAAAa,SAAA;gBACA2B,MAAA,CAAA1B,QAAA,CAAAC,OAAA;gBACAyB,MAAA,CAAAjD,IAAA;cACA;YAAA;YAAA;cAAA,OAAAoD,SAAA,CAAA3B,IAAA;UAAA;QAAA,GAAAyB,QAAA;MAAA;IACA;IACAI,gBAAA,WAAAA,iBAAAC,GAAA;MACA1F,OAAA,CAAAC,GAAA,iBAAA0F,MAAA,CAAAD,GAAA;MACA,KAAA/E,iBAAA,CAAAI,QAAA,GAAA2E,GAAA;MACA,KAAAjB,sBAAA;IACA;IACAmB,mBAAA,WAAAA,oBAAAF,GAAA;MACA1F,OAAA,CAAAC,GAAA,wBAAA0F,MAAA,CAAAD,GAAA;MACA,KAAA/E,iBAAA,CAAAG,WAAA,GAAA4E,GAAA;MACA,KAAAjB,sBAAA;IACA;IACAoB,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAjH,cAAA,GAAAiH,SAAA;IACA;IACA/D,UAAA,WAAAA,WAAAV,GAAA;MACA,KAAA5C,aAAA;MACA,KAAAJ,gBAAA,CAAAC,IAAA,GAAA+C,GAAA;MACA,KAAAhD,gBAAA;QACAoB,IAAA;QACAvB,IAAA,EAAAmD;MACA;IACA;EACA;AACA", "ignoreList": []}]}