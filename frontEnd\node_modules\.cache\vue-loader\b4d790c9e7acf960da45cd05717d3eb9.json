{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\hazardousChemicals\\monitoringArchives\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\hazardousChemicals\\monitoringArchives\\index.vue", "mtime": 1755506574341}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQ3VzdG9tTGF5b3V0IGZyb20gIkAvYnVzaW5lc3NDb21wb25lbnRzL0N1c3RvbUxheW91dC9pbmRleC52dWUiOw0KaW1wb3J0IEN1c3RvbVRhYmxlIGZyb20gIkAvYnVzaW5lc3NDb21wb25lbnRzL0N1c3RvbVRhYmxlL2luZGV4LnZ1ZSI7DQppbXBvcnQgQ3VzdG9tRm9ybSBmcm9tICJAL2J1c2luZXNzQ29tcG9uZW50cy9DdXN0b21Gb3JtL2luZGV4LnZ1ZSI7DQoNCmltcG9ydCBEaWFsb2dGb3JtIGZyb20gIi4vZGlhbG9nRm9ybS52dWUiOw0KaW1wb3J0IERpYWxvZ1R5cGUgZnJvbSAiLi9kaWFsb2dUeXBlLnZ1ZSI7DQppbXBvcnQgRGlhbG9nRm9ybUxvb2sgZnJvbSAiLi9kaWFsb2dGb3JtTG9vay52dWUiOw0KDQppbXBvcnQgeyBkb3dubG9hZEZpbGUgfSBmcm9tICJAL3V0aWxzL2Rvd25sb2FkRmlsZSI7DQovLyBpbXBvcnQgQ3VzdG9tVGl0bGUgZnJvbSAnQC9idXNpbmVzc0NvbXBvbmVudHMvQ3VzdG9tVGl0bGUvaW5kZXgudnVlJw0KLy8gaW1wb3J0IEN1c3RvbUJ1dHRvbiBmcm9tICdAL2J1c2luZXNzQ29tcG9uZW50cy9DdXN0b21CdXR0b24vaW5kZXgudnVlJw0KDQppbXBvcnQgew0KICBHZXRFcXVpcG1lbnRMaXN0LA0KICBEZWxldGVFcXVpcG1lbnQsDQogIEV4cG9ydEhhemNoZW1FcXVpcG1lbnQsDQogIEV4cG9ydEVxdWlwbWVudExpc3QsDQogIEhhemNoZW1JbXBvcnRUZW1wbGF0ZSwNCiAgSGF6Y2hlbUVxdWlwbWVudEltcG9ydCwNCn0gZnJvbSAiQC9hcGkvYnVzaW5lc3MvaGF6YXJkb3VzQ2hlbWljYWxzIjsNCmltcG9ydCBpbXBvcnREaWFsb2cgZnJvbSAiQC92aWV3cy9idXNpbmVzcy9lbmVyZ3lNYW5hZ2VtZW50L2NvbXBvbmVudHMvaW1wb3J0LnZ1ZSI7DQppbXBvcnQgeyBkZXZpY2VUeXBlTWl4aW5zIH0gZnJvbSAiLi4vLi4vbWl4aW5zL2RldmljZVR5cGUuanMiOw0KLy8gaW1wb3J0ICogYXMgbW9tZW50IGZyb20gJ21vbWVudCcNCmltcG9ydCBkYXlqcyBmcm9tICJkYXlqcyI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICIiLA0KICBjb21wb25lbnRzOiB7DQogICAgQ3VzdG9tVGFibGUsDQogICAgLy8gQ3VzdG9tQnV0dG9uLA0KICAgIC8vIEN1c3RvbVRpdGxlLA0KICAgIEN1c3RvbUZvcm0sDQogICAgQ3VzdG9tTGF5b3V0LA0KICAgIGltcG9ydERpYWxvZywNCiAgfSwNCiAgbWl4aW5zOiBbZGV2aWNlVHlwZU1peGluc10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGN1cnJlbnRDb21wb25lbnQ6IERpYWxvZ0Zvcm0sDQogICAgICBjb21wb25lbnRzQ29uZmlnOiB7DQogICAgICAgIGludGVyZmFjZU5hbWU6IEhhemNoZW1FcXVpcG1lbnRJbXBvcnQsDQogICAgICB9LA0KICAgICAgY29tcG9uZW50c0Z1bnM6IHsNCiAgICAgICAgb3BlbjogKCkgPT4gew0KICAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICAgIH0sDQogICAgICAgIGNsb3NlOiAoKSA9PiB7DQogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5vbkZyZXNoKCk7DQogICAgICAgIH0sDQogICAgICB9LA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBkaWFsb2dUaXRsZTogIiIsDQogICAgICB0YWJsZVNlbGVjdGlvbjogW10sDQoNCiAgICAgIHJ1bGVGb3JtOiB7DQogICAgICAgIENvbnRlbnQ6ICIiLA0KICAgICAgICBFcXRUeXBlOiAiIiwNCiAgICAgICAgUG9zaXRpb246ICIiLA0KICAgICAgfSwNCiAgICAgIGN1c3RvbUZvcm06IHsNCiAgICAgICAgZm9ybUl0ZW1zOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiAiQ29udGVudCIsIC8vIOWtl+autUlEDQogICAgICAgICAgICBsYWJlbDogIiIsIC8vIEZvcm3nmoRsYWJlbA0KICAgICAgICAgICAgdHlwZTogImlucHV0IiwgLy8gaW5wdXQ65pmu6YCa6L6T5YWl5qGGLHRleHRhcmVhOuaWh+acrO+/vT9zZWxlY3Q65LiL5ouJ6YCJ5oup77+9P2RhdGVwaWNrZXI65pel5pyf6YCJ5oup77+9PwogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgLy8g6Zmk5LqGbW9kZWzku6XlpJbnmoTlhbbku5bnmoTlj4LmlbAs5YW35L2T6K+35Y+C6ICDZWxlbWVudOaWh+ahow0KICAgICAgICAgICAgICBjbGVhcmFibGU6IHRydWUsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6L6T5YWl6K6+5aSH57yW5Y+35oiW5ZCN56ew6L+b6KGM5pCc77+9PywNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB3aWR0aDogIjI0MHB4IiwNCiAgICAgICAgICAgIGNoYW5nZTogKGUpID0+IHsNCiAgICAgICAgICAgICAgLy8gY2hhbmdl5LqL5Lu2DQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKGUpOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGtleTogIkVxdFR5cGUiLA0KICAgICAgICAgICAgbGFiZWw6ICLorr7lpIfnsbvlnosiLA0KICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgLy8g6Zmk5LqGbW9kZWzku6XlpJbnmoTlhbbku5bnmoTlj4LmlbAs5YW35L2T6K+35Y+C6ICDZWxlbWVudOaWh+ahow0KICAgICAgICAgICAgICBjbGVhcmFibGU6IHRydWUsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6K+36YCJ5oup6K6+5aSH57G75Z6LIiwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBvcHRpb25zOiBbXSwNCiAgICAgICAgICAgIGNoYW5nZTogKGUpID0+IHsNCiAgICAgICAgICAgICAgY29uc29sZS5sb2coZSk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiAiUG9zaXRpb24iLCAvLyDlrZfmrrVJRA0KICAgICAgICAgICAgbGFiZWw6ICLlronoo4XkvY3nva4iLCAvLyBGb3Jt55qEbGFiZWwNCiAgICAgICAgICAgIHR5cGU6ICJpbnB1dCIsIC8vIGlucHV0OuaZrumAmui+k+WFpeahhix0ZXh0YXJlYTrmlofmnKzvv70/c2VsZWN0OuS4i+aLiemAieaLqe+/vT9kYXRlcGlja2VyOuaXpeacn+mAieaLqe+/vT8KICAgICAgICAgICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgICAgIC8vIOmZpOS6hm1vZGVs5Lul5aSW55qE5YW25LuW55qE5Y+C5pWwLOWFt+S9k+ivt+WPguiAg2VsZW1lbnTmlofmoaMNCiAgICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlLA0KICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogIuivt+i+k+WFpeWuieijheS9je+/vT8sDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgY2hhbmdlOiAoZSkgPT4gew0KICAgICAgICAgICAgICAvLyBjaGFuZ2Xkuovku7YNCiAgICAgICAgICAgICAgY29uc29sZS5sb2coZSk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIHJ1bGVzOiB7DQogICAgICAgICAgLy8g6K+35Y+C54WnZWxlbWVudEZvcm0gcnVsZXMNCiAgICAgICAgfSwNCiAgICAgICAgY3VzdG9tRm9ybUJ1dHRvbnM6IHsNCiAgICAgICAgICBzdWJtaXROYW1lOiAi5p+l6K+iIiwNCiAgICAgICAgICByZXNldE5hbWU6ICLph43nva4iLA0KICAgICAgICB9LA0KICAgICAgfSwNCiAgICAgIGN1c3RvbVRhYmxlQ29uZmlnOiB7DQogICAgICAgIGJ1dHRvbkNvbmZpZzogew0KICAgICAgICAgIGJ1dHRvbkxpc3Q6IFsNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgdGV4dDogIuiuvuWkh+exu+Wei+mFjee9riIsDQogICAgICAgICAgICAgIHR5cGU6ICJwcmltYXJ5IiwgLy8gcHJpbWFyeSAvIHN1Y2Nlc3MgLyB3YXJuaW5nIC8gZGFuZ2VyIC8gaW5mbyAvIHRleHQNCiAgICAgICAgICAgICAgc2l6ZTogInNtYWxsIiwgLy8gbWVkaXVtIC8gc21hbGwgLyBtaW5pDQogICAgICAgICAgICAgIG9uY2xpY2s6IChpdGVtKSA9PiB7DQogICAgICAgICAgICAgICAgY29uc29sZS5sb2coaXRlbSk7DQogICAgICAgICAgICAgICAgdGhpcy5oYW5kbGVDbGljaygpOw0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgdGV4dDogIuaWsOWiniIsDQogICAgICAgICAgICAgIHR5cGU6ICJwcmltYXJ5IiwgLy8gcHJpbWFyeSAvIHN1Y2Nlc3MgLyB3YXJuaW5nIC8gZGFuZ2VyIC8gaW5mbyAvIHRleHQNCiAgICAgICAgICAgICAgc2l6ZTogInNtYWxsIiwgLy8gbWVkaXVtIC8gc21hbGwgLyBtaW5pDQogICAgICAgICAgICAgIG9uY2xpY2s6IChpdGVtKSA9PiB7DQogICAgICAgICAgICAgICAgY29uc29sZS5sb2coaXRlbSk7DQogICAgICAgICAgICAgICAgdGhpcy5oYW5kbGVDcmVhdGUoKTsNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIHRleHQ6ICLkuIvovb3mqKHmnb8iLA0KICAgICAgICAgICAgICBkaXNhYmxlZDogZmFsc2UsIC8vIOaYr+WQpuemgeeUqA0KICAgICAgICAgICAgICBvbmNsaWNrOiAoaXRlbSkgPT4gew0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGl0ZW0pOw0KICAgICAgICAgICAgICAgIHRoaXMuaGFuZGxlRG93blRlbXBsYXRlKCk7DQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICB0ZXh0OiAi5om56YeP5a+85YWlIiwNCiAgICAgICAgICAgICAgZGlzYWJsZWQ6IGZhbHNlLCAvLyDmmK/lkKbnpoHnlKgNCiAgICAgICAgICAgICAgb25jbGljazogKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhpdGVtKTsNCiAgICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAiaW1wb3J0RGlhbG9nIjsNCiAgICAgICAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgICAgICAgICAgIHRoaXMuZGlhbG9nVGl0bGUgPSAi5om56YeP5a+85YWlIjsNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvLyB7DQogICAgICAgICAgICAvLyAgIHRleHQ6ICflr7zlh7onLA0KICAgICAgICAgICAgLy8gICBrZXk6ICdiYXRjaCcsDQogICAgICAgICAgICAvLyAgIGRpc2FibGVkOiB0cnVlLA0KICAgICAgICAgICAgLy8gICBvbmNsaWNrOiAoaXRlbSkgPT4gew0KICAgICAgICAgICAgLy8gICAgIGNvbnNvbGUubG9nKGl0ZW0pDQogICAgICAgICAgICAvLyAgICAgdGhpcy5oYW5kbGVFeHBvcnQoKQ0KICAgICAgICAgICAgLy8gICB9DQogICAgICAgICAgICAvLyB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICB0ZXh0OiAi5om56YeP5a+85Ye6IiwNCiAgICAgICAgICAgICAgb25jbGljazogKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhpdGVtKTsNCiAgICAgICAgICAgICAgICB0aGlzLmhhbmRsZUFsbEV4cG9ydCgpOw0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICBdLA0KICAgICAgICB9LA0KICAgICAgICAvLyDooajmoLwNCiAgICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICAgIHBhZ2VTaXplT3B0aW9uczogWzEwLCAyMCwgNTAsIDgwXSwNCiAgICAgICAgY3VycmVudFBhZ2U6IDEsDQogICAgICAgIHBhZ2VTaXplOiAyMCwNCiAgICAgICAgdG90YWw6IDAsDQogICAgICAgIHRhYmxlQ29sdW1uczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHdpZHRoOiA1MCwNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICB0eXBlOiAic2VsZWN0aW9uIiwNCiAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHdpZHRoOiA2MCwNCiAgICAgICAgICAgIGxhYmVsOiAi5bqP5Y+3IiwNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICB0eXBlOiAiaW5kZXgiLA0KICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICB9LCAvLyBrZXkNCiAgICAgICAgICAgIC8vIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgLy8gICB3aWR0aDogMTgwLCAvLyDlrr3luqYNCiAgICAgICAgICAgIC8vICAgZml4ZWQ6ICdsZWZ0JywgLy8gbGVmdCwgcmlnaHQNCiAgICAgICAgICAgIC8vICAgYWxpZ246ICdjZW50ZXInIC8vCWxlZnQvY2VudGVyL3JpZ2h0DQogICAgICAgICAgICAvLyB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogIuiuvuWkh+e8luWPtyIsDQogICAgICAgICAgICBrZXk6ICJFSWQiLA0KICAgICAgICAgICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgICAgIGZpeGVkOiAnbGVmdCcNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvLyByZW5kZXI6IHJvdyA9PiB7DQogICAgICAgICAgICAvLyAgIHJldHVybiAoDQogICAgICAgICAgICAvLyAgICAgPHNwYW4+DQogICAgICAgICAgICAvLyAgICAgICB7cm93LkVJZH0NCiAgICAgICAgICAgIC8vICAgICA8L3NwYW4+DQogICAgICAgICAgICAvLyAgICkNCiAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGxhYmVsOiAi6K6+5aSH5ZCN56ewIiwNCiAgICAgICAgICAgIGtleTogIk5hbWUiLA0KICAgICAgICAgICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgICAgIGZpeGVkOiAnbGVmdCcNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogIuWTgeeJjCIsDQogICAgICAgICAgICBrZXk6ICJCcmFuZCIsDQogICAgICAgICAgfSwNCiAgICAgICAgICAvLyB7DQogICAgICAgICAgLy8gICBsYWJlbDogJ+Wei+WPtycsDQogICAgICAgICAgLy8gICBrZXk6ICdUeXBlJw0KICAgICAgICAgIC8vIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbGFiZWw6ICLorr7lpIfnsbvlnosiLA0KICAgICAgICAgICAga2V5OiAiRXF0VHlwZSIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogIuWuieijheS9jee9riIsDQogICAgICAgICAgICBrZXk6ICJQb3NpdGlvbiIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBsYWJlbDogIuWuieijheaXpeacnyIsDQogICAgICAgICAgICBrZXk6ICJEYXRlIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICB0YWJsZURhdGE6IFtdLA0KICAgICAgICBvcGVyYXRlT3B0aW9uczogew0KICAgICAgICAgIHdpZHRoOiAyMDAsDQogICAgICAgIH0sDQogICAgICAgIHRhYmxlQWN0aW9uczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGFjdGlvbkxhYmVsOiAi5p+l55yLIiwNCiAgICAgICAgICAgIG90aGVyT3B0aW9uczogew0KICAgICAgICAgICAgICB0eXBlOiAidGV4dCIsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgb25jbGljazogKGluZGV4LCByb3cpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy5oYW5kbGVFZGl0KGluZGV4LCByb3csICJ2aWV3Iik7DQogICAgICAgICAgICB9LA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgYWN0aW9uTGFiZWw6ICLnvJbovpEiLA0KICAgICAgICAgICAgb3RoZXJPcHRpb25zOiB7DQogICAgICAgICAgICAgIHR5cGU6ICJ0ZXh0IiwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBvbmNsaWNrOiAoaW5kZXgsIHJvdykgPT4gew0KICAgICAgICAgICAgICB0aGlzLmhhbmRsZUVkaXQoaW5kZXgsIHJvdywgImVkaXQiKTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBhY3Rpb25MYWJlbDogIuWIoOmZpCIsDQogICAgICAgICAgICBvdGhlck9wdGlvbnM6IHsNCiAgICAgICAgICAgICAgdHlwZTogInRleHQiLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIG9uY2xpY2s6IChpbmRleCwgcm93KSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuaGFuZGxlRGVsZXRlKGluZGV4LCByb3cpOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICB9Ow0KICB9LA0KICBjb21wdXRlZDoge30sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5pbml0KCk7DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5pbml0RGV2aWNlVHlwZSgiRXF0VHlwZSIsICJIYXpjaGVtRXF0VHlwZSIpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgc2VhcmNoRm9ybShkYXRhKSB7DQogICAgICBjb25zb2xlLmxvZyhkYXRhKTsNCiAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcuY3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy5vbkZyZXNoKCk7DQogICAgfSwNCiAgICByZXNldEZvcm0oKSB7DQogICAgICB0aGlzLm9uRnJlc2goKTsNCiAgICB9LA0KICAgIG9uRnJlc2goKSB7DQogICAgICB0aGlzLmdldEVxdWlwbWVudExpc3QoKTsNCiAgICB9LA0KICAgIGluaXQoKSB7DQogICAgICB0aGlzLmdldEVxdWlwbWVudExpc3QoKTsNCiAgICB9LA0KICAgIGFzeW5jIGdldEVxdWlwbWVudExpc3QoKSB7DQogICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0RXF1aXBtZW50TGlzdCh7DQogICAgICAgIFBhcmFtZXRlckpzb246IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBLZXk6ICIiLA0KICAgICAgICAgICAgVmFsdWU6IFtudWxsXSwNCiAgICAgICAgICAgIFR5cGU6ICIiLA0KICAgICAgICAgICAgRmlsdGVyX1R5cGU6ICIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIFBhZ2U6IHRoaXMuY3VzdG9tVGFibGVDb25maWcuY3VycmVudFBhZ2UsDQogICAgICAgIFBhZ2VTaXplOiB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLnBhZ2VTaXplLA0KDQogICAgICAgIFNvcnROYW1lOiAiIiwNCiAgICAgICAgU29ydE9yZGVyOiAiIiwNCiAgICAgICAgU2VhcmNoOiAiIiwNCiAgICAgICAgQ29udGVudDogIiIsDQogICAgICAgIEVxdFR5cGU6ICIiLA0KICAgICAgICBQb3NpdGlvbjogIiIsDQogICAgICAgIElzQWxsOiB0cnVlLA0KICAgICAgICAuLi50aGlzLnJ1bGVGb3JtLA0KICAgICAgfSk7DQogICAgICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcudGFibGVEYXRhID0gcmVzLkRhdGEuRGF0YS5tYXAoKGl0ZW0pID0+ICh7DQogICAgICAgICAgLi4uaXRlbSwNCiAgICAgICAgICBEYXRlOiBkYXlqcyhpdGVtLkRhdGUpLmZvcm1hdCgiWVlZWS1NTS1ERCIpLA0KICAgICAgICB9KSk7DQogICAgICAgIGNvbnNvbGUubG9nKHJlcyk7DQogICAgICAgIHRoaXMuY3VzdG9tVGFibGVDb25maWcudG90YWwgPSByZXMuRGF0YS5Ub3RhbENvdW50Ow0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuTWVzc2FnZSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIGhhbmRsZUNyZWF0ZSgpIHsNCiAgICAgIHRoaXMuZGlhbG9nVGl0bGUgPSAi5paw5aKeIjsNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9IERpYWxvZ0Zvcm07DQogICAgICB0aGlzLmNvbXBvbmVudHNDb25maWcgPSB7DQogICAgICAgIGRpc2FibGVkOiBmYWxzZSwNCiAgICAgICAgdGl0bGU6ICLmlrDlop4iLA0KICAgICAgfTsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCiAgICBoYW5kbGVEZWxldGUoaW5kZXgsIHJvdykgew0KICAgICAgY29uc29sZS5sb2coaW5kZXgsIHJvdyk7DQogICAgICBjb25zb2xlLmxvZyh0aGlzKTsNCiAgICAgIHRoaXMuJGNvbmZpcm0oDQogICAgICAgICLor6Xmk43kvZzlsIblnKjnm5HmtYvorr7lpIfmoaPmoYjkuK3liKDpmaTor6Xorr7lpIfkv6Hmga/vvIzor7fnoa7orqTmmK/lkKbliKDpmaTvvJ8iLA0KICAgICAgICAi5Yig6ZmkIiwNCiAgICAgICAgew0KICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgIH0NCiAgICAgICkNCiAgICAgICAgLnRoZW4oYXN5bmMgKF8pID0+IHsNCiAgICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBEZWxldGVFcXVpcG1lbnQoew0KICAgICAgICAgICAgSURzOiBbcm93LklEXSwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy5pbml0KCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChfKSA9PiB7fSk7DQogICAgfSwNCiAgICBoYW5kbGVFZGl0KGluZGV4LCByb3csIHR5cGUpIHsNCiAgICAgIGNvbnNvbGUubG9nKGluZGV4LCByb3csIHR5cGUpOw0KICAgICAgaWYgKHR5cGUgPT09ICJ2aWV3Iikgew0KICAgICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSBEaWFsb2dGb3JtTG9vazsNCiAgICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9ICLmn6XnnIsiOw0KICAgICAgICB0aGlzLmNvbXBvbmVudHNDb25maWcgPSB7DQogICAgICAgICAgSUQ6IHJvdy5JRCwNCiAgICAgICAgICBkaXNhYmxlZDogdHJ1ZSwNCiAgICAgICAgICB0aXRsZTogIuafpeeciyIsDQogICAgICAgIH07DQogICAgICB9IGVsc2UgaWYgKHR5cGUgPT09ICJlZGl0Iikgew0KICAgICAgICB0aGlzLmRpYWxvZ1RpdGxlID0gIue8lui+kSI7DQogICAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9IERpYWxvZ0Zvcm07DQogICAgICAgIHRoaXMuY29tcG9uZW50c0NvbmZpZyA9IHsNCiAgICAgICAgICBJRDogcm93LklELA0KICAgICAgICAgIGRpc2FibGVkOiBmYWxzZSwNCiAgICAgICAgICB0aXRsZTogIue8lui+kSIsDQogICAgICAgIH07DQogICAgICB9DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgLy8gYXN5bmMgaGFuZGxlRXhwb3J0KCkgew0KICAgIC8vICAgY29uc29sZS5sb2codGhpcy5ydWxlRm9ybSkNCiAgICAvLyAgIGNvbnN0IHJlcyA9IGF3YWl0IEV4cG9ydEhhemNoZW1FcXVpcG1lbnQoew0KICAgIC8vICAgICBDb250ZW50OiAnJywNCiAgICAvLyAgICAgRXF0VHlwZTogJycsDQogICAgLy8gICAgIFBvc2l0aW9uOiAnJywNCiAgICAvLyAgICAgSXNBbGw6IGZhbHNlLA0KICAgIC8vICAgICBJZHM6IHRoaXMudGFibGVTZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLklEKSwNCiAgICAvLyAgICAgLi4udGhpcy5ydWxlRm9ybQ0KICAgIC8vICAgfSkNCiAgICAvLyAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgLy8gICAgIGNvbnNvbGUubG9nKHJlcykNCiAgICAvLyAgICAgZG93bmxvYWRGaWxlKHJlcy5EYXRhLCAnMjEnKQ0KICAgIC8vICAgfSBlbHNlIHsNCiAgICAvLyAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuTWVzc2FnZSkNCiAgICAvLyAgIH0NCiAgICAvLyB9LA0KICAgIC8vIGFzeW5jIGhhbmRsZUFsbEV4cG9ydCgpIHsNCiAgICAvLyAgIGNvbnN0IHJlcyA9IGF3YWl0IEV4cG9ydEhhemNoZW1FcXVpcG1lbnQoew0KICAgIC8vICAgICBDb250ZW50OiAiIiwNCiAgICAvLyAgICAgRXF0VHlwZTogIiIsDQogICAgLy8gICAgIFBvc2l0aW9uOiAiIiwNCiAgICAvLyAgICAgSXNBbGw6IHRydWUsDQogICAgLy8gICAgIElkczogW10sDQogICAgLy8gICAgIC4uLnRoaXMucnVsZUZvcm0sDQogICAgLy8gICB9KTsNCiAgICAvLyAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgLy8gICAgIGNvbnNvbGUubG9nKHJlcyk7DQogICAgLy8gICAgIGRvd25sb2FkRmlsZShyZXMuRGF0YSwgIjIxIik7DQogICAgLy8gICB9IGVsc2Ugew0KICAgIC8vICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKTsNCiAgICAvLyAgIH0NCiAgICAvLyB9LA0KICAgIC8vIHYyIOeJiOacrCDljYfnuqflr7zlh7oNCiAgICBhc3luYyBoYW5kbGVBbGxFeHBvcnQoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCBFeHBvcnRFcXVpcG1lbnRMaXN0KHsNCiAgICAgICAgQ29udGVudDogIiIsDQogICAgICAgIEVxdFR5cGU6ICIiLA0KICAgICAgICBQb3NpdGlvbjogIiIsDQogICAgICAgIElzQWxsOiB0cnVlLA0KICAgICAgICBJZDogdGhpcy50YWJsZVNlbGVjdGlvbi5tYXAoKGl0ZW0pID0+IGl0ZW0uSUQpLnRvU3RyaW5nKCksDQogICAgICAgIC4uLnRoaXMucnVsZUZvcm0sDQogICAgICB9KTsNCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIGNvbnNvbGUubG9nKHJlcyk7DQogICAgICAgIGRvd25sb2FkRmlsZShyZXMuRGF0YSwgIjIxIik7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOS4i+i9veaooeadvw0KICAgIGhhbmRsZURvd25UZW1wbGF0ZSgpIHsNCiAgICAgIEhhemNoZW1JbXBvcnRUZW1wbGF0ZSh7fSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgZG93bmxvYWRGaWxlKHJlcy5EYXRhLCAi5Y2x5YyW5ZOB55uR5rWL6K6+5aSH5qGj5qGI5a+877+9Pyk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuTWVzc2FnZSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgaGFuZGxlU2l6ZUNoYW5nZSh2YWwpIHsNCiAgICAgIGNvbnNvbGUubG9nKGDmr4/pobUgJHt2YWx9IOadoWApOw0KICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5wYWdlU2l6ZSA9IHZhbDsNCiAgICAgIHRoaXMuaW5pdCgpOw0KICAgIH0sDQogICAgaGFuZGxlQ3VycmVudENoYW5nZSh2YWwpIHsNCiAgICAgIGNvbnNvbGUubG9nKGDlvZPliY3vv70/ICR7dmFsfWApOw0KICAgICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5jdXJyZW50UGFnZSA9IHZhbDsNCiAgICAgIHRoaXMuaW5pdCgpOw0KICAgIH0sDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy50YWJsZVNlbGVjdGlvbiA9IHNlbGVjdGlvbjsNCiAgICAgIC8vIGlmICh0aGlzLnRhYmxlU2VsZWN0aW9uLmxlbmd0aCA+IDApIHsNCiAgICAgIC8vICAgdGhpcy5jdXN0b21UYWJsZUNvbmZpZy5idXR0b25Db25maWcuYnV0dG9uTGlzdC5maW5kKCh2KSA9PiB2LmtleSA9PSAnYmF0Y2gnKS5kaXNhYmxlZCA9IGZhbHNlDQogICAgICAvLyB9IGVsc2Ugew0KICAgICAgLy8gICB0aGlzLmN1c3RvbVRhYmxlQ29uZmlnLmJ1dHRvbkNvbmZpZy5idXR0b25MaXN0LmZpbmQoKHYpID0+IHYua2V5ID09ICdiYXRjaCcpLmRpc2FibGVkID0gdHJ1ZQ0KICAgICAgLy8gfQ0KICAgIH0sDQogICAgaGFuZGxlQ2xpY2soKSB7DQogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSBEaWFsb2dUeXBlOw0KICAgICAgdGhpcy5jb21wb25lbnRzQ29uZmlnID0gew0KICAgICAgICBkaXNhYmxlZDogZmFsc2UsDQogICAgICAgIHRpdGxlOiAi5paw5aKeIiwNCiAgICAgIH07DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/hazardousChemicals/monitoringArchives", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n        />\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog v-dialogDrag :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CustomLayout from \"@/businessComponents/CustomLayout/index.vue\";\r\nimport CustomTable from \"@/businessComponents/CustomTable/index.vue\";\r\nimport CustomForm from \"@/businessComponents/CustomForm/index.vue\";\r\n\r\nimport DialogForm from \"./dialogForm.vue\";\r\nimport DialogType from \"./dialogType.vue\";\r\nimport DialogFormLook from \"./dialogFormLook.vue\";\r\n\r\nimport { downloadFile } from \"@/utils/downloadFile\";\r\n// import CustomTitle from '@/businessComponents/CustomTitle/index.vue'\r\n// import CustomButton from '@/businessComponents/CustomButton/index.vue'\r\n\r\nimport {\r\n  GetEquipmentList,\r\n  DeleteEquipment,\r\n  ExportHazchemEquipment,\r\n  ExportEquipmentList,\r\n  HazchemImportTemplate,\r\n  HazchemEquipmentImport,\r\n} from \"@/api/business/hazardousChemicals\";\r\nimport importDialog from \"@/views/business/energyManagement/components/import.vue\";\r\nimport { deviceTypeMixins } from \"../../mixins/deviceType.js\";\r\n// import * as moment from 'moment'\r\nimport dayjs from \"dayjs\";\r\nexport default {\r\n  name: \"\",\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout,\r\n    importDialog,\r\n  },\r\n  mixins: [deviceTypeMixins],\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {\r\n        interfaceName: HazchemEquipmentImport,\r\n      },\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true;\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false;\r\n          this.onFresh();\r\n        },\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: \"\",\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Content: \"\",\r\n        EqtType: \"\",\r\n        Position: \"\",\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: \"Content\", // 字段ID\r\n            label: \"\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: \"输入设备编号或名称进行搜�?,\r\n            },\r\n            width: \"240px\",\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"EqtType\",\r\n            label: \"设备类型\",\r\n            type: \"select\",\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: \"请选择设备类型\",\r\n            },\r\n            options: [],\r\n            change: (e) => {\r\n              console.log(e);\r\n            },\r\n          },\r\n          {\r\n            key: \"Position\", // 字段ID\r\n            label: \"安装位置\", // Form的label\r\n            type: \"input\", // input:普通输入框,textarea:文本�?select:下拉选择�?datepicker:日期选择�?\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: \"请输入安装位�?,\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e);\r\n            },\r\n          },\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: \"查询\",\r\n          resetName: \"重置\",\r\n        },\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: \"设备类型配置\",\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleClick();\r\n              },\r\n            },\r\n            {\r\n              text: \"新增\",\r\n              type: \"primary\", // primary / success / warning / danger / info / text\r\n              size: \"small\", // medium / small / mini\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleCreate();\r\n              },\r\n            },\r\n            {\r\n              text: \"下载模板\",\r\n              disabled: false, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleDownTemplate();\r\n              },\r\n            },\r\n            {\r\n              text: \"批量导入\",\r\n              disabled: false, // 是否禁用\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.currentComponent = \"importDialog\";\r\n                this.dialogVisible = true;\r\n                this.dialogTitle = \"批量导入\";\r\n              },\r\n            },\r\n            // {\r\n            //   text: '导出',\r\n            //   key: 'batch',\r\n            //   disabled: true,\r\n            //   onclick: (item) => {\r\n            //     console.log(item)\r\n            //     this.handleExport()\r\n            //   }\r\n            // },\r\n            {\r\n              text: \"批量导出\",\r\n              onclick: (item) => {\r\n                console.log(item);\r\n                this.handleAllExport();\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // 表格\r\n        loading: false,\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        tableColumns: [\r\n          {\r\n            width: 50,\r\n            otherOptions: {\r\n              type: \"selection\",\r\n              align: \"center\",\r\n            },\r\n          },\r\n          {\r\n            width: 60,\r\n            label: \"序号\",\r\n            otherOptions: {\r\n              type: \"index\",\r\n              align: \"center\",\r\n            }, // key\r\n            // otherOptions: {\r\n            //   width: 180, // 宽度\r\n            //   fixed: 'left', // left, right\r\n            //   align: 'center' //\tleft/center/right\r\n            // }\r\n          },\r\n          {\r\n            label: \"设备编号\",\r\n            key: \"EId\",\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n            // render: row => {\r\n            //   return (\r\n            //     <span>\r\n            //       {row.EId}\r\n            //     </span>\r\n            //   )\r\n            // }\r\n          },\r\n          {\r\n            label: \"设备名称\",\r\n            key: \"Name\",\r\n            otherOptions: {\r\n              fixed: 'left'\r\n            },\r\n          },\r\n          {\r\n            label: \"品牌\",\r\n            key: \"Brand\",\r\n          },\r\n          // {\r\n          //   label: '型号',\r\n          //   key: 'Type'\r\n          // },\r\n          {\r\n            label: \"设备类型\",\r\n            key: \"EqtType\",\r\n          },\r\n          {\r\n            label: \"安装位置\",\r\n            key: \"Position\",\r\n          },\r\n          {\r\n            label: \"安装日期\",\r\n            key: \"Date\",\r\n          },\r\n        ],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 200,\r\n        },\r\n        tableActions: [\r\n          {\r\n            actionLabel: \"查看\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, \"view\");\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"编辑\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, \"edit\");\r\n            },\r\n          },\r\n          {\r\n            actionLabel: \"删除\",\r\n            otherOptions: {\r\n              type: \"text\",\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleDelete(index, row);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.init();\r\n  },\r\n  mounted() {\r\n    this.initDeviceType(\"EqtType\", \"HazchemEqtType\");\r\n  },\r\n  methods: {\r\n    searchForm(data) {\r\n      console.log(data);\r\n      this.customTableConfig.currentPage = 1;\r\n      this.onFresh();\r\n    },\r\n    resetForm() {\r\n      this.onFresh();\r\n    },\r\n    onFresh() {\r\n      this.getEquipmentList();\r\n    },\r\n    init() {\r\n      this.getEquipmentList();\r\n    },\r\n    async getEquipmentList() {\r\n      this.customTableConfig.loading = true;\r\n      const res = await GetEquipmentList({\r\n        ParameterJson: [\r\n          {\r\n            Key: \"\",\r\n            Value: [null],\r\n            Type: \"\",\r\n            Filter_Type: \"\",\r\n          },\r\n        ],\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n\r\n        SortName: \"\",\r\n        SortOrder: \"\",\r\n        Search: \"\",\r\n        Content: \"\",\r\n        EqtType: \"\",\r\n        Position: \"\",\r\n        IsAll: true,\r\n        ...this.ruleForm,\r\n      });\r\n      this.customTableConfig.loading = false;\r\n      if (res.IsSucceed) {\r\n        this.customTableConfig.tableData = res.Data.Data.map((item) => ({\r\n          ...item,\r\n          Date: dayjs(item.Date).format(\"YYYY-MM-DD\"),\r\n        }));\r\n        console.log(res);\r\n        this.customTableConfig.total = res.Data.TotalCount;\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n\r\n    handleCreate() {\r\n      this.dialogTitle = \"新增\";\r\n      this.currentComponent = DialogForm;\r\n      this.componentsConfig = {\r\n        disabled: false,\r\n        title: \"新增\",\r\n      };\r\n      this.dialogVisible = true;\r\n    },\r\n    handleDelete(index, row) {\r\n      console.log(index, row);\r\n      console.log(this);\r\n      this.$confirm(\r\n        \"该操作将在监测设备档案中删除该设备信息，请确认是否删除？\",\r\n        \"删除\",\r\n        {\r\n          type: \"error\",\r\n        }\r\n      )\r\n        .then(async (_) => {\r\n          const res = await DeleteEquipment({\r\n            IDs: [row.ID],\r\n          });\r\n          if (res.IsSucceed) {\r\n            this.init();\r\n          } else {\r\n            this.$message.error(res.Message);\r\n          }\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type);\r\n      if (type === \"view\") {\r\n        this.currentComponent = DialogFormLook;\r\n        this.dialogTitle = \"查看\";\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: true,\r\n          title: \"查看\",\r\n        };\r\n      } else if (type === \"edit\") {\r\n        this.dialogTitle = \"编辑\";\r\n        this.currentComponent = DialogForm;\r\n        this.componentsConfig = {\r\n          ID: row.ID,\r\n          disabled: false,\r\n          title: \"编辑\",\r\n        };\r\n      }\r\n      this.dialogVisible = true;\r\n    },\r\n    // async handleExport() {\r\n    //   console.log(this.ruleForm)\r\n    //   const res = await ExportHazchemEquipment({\r\n    //     Content: '',\r\n    //     EqtType: '',\r\n    //     Position: '',\r\n    //     IsAll: false,\r\n    //     Ids: this.tableSelection.map((item) => item.ID),\r\n    //     ...this.ruleForm\r\n    //   })\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res)\r\n    //     downloadFile(res.Data, '21')\r\n    //   } else {\r\n    //     this.$message.error(res.Message)\r\n    //   }\r\n    // },\r\n    // async handleAllExport() {\r\n    //   const res = await ExportHazchemEquipment({\r\n    //     Content: \"\",\r\n    //     EqtType: \"\",\r\n    //     Position: \"\",\r\n    //     IsAll: true,\r\n    //     Ids: [],\r\n    //     ...this.ruleForm,\r\n    //   });\r\n    //   if (res.IsSucceed) {\r\n    //     console.log(res);\r\n    //     downloadFile(res.Data, \"21\");\r\n    //   } else {\r\n    //     this.$message.error(res.Message);\r\n    //   }\r\n    // },\r\n    // v2 版本 升级导出\r\n    async handleAllExport() {\r\n      const res = await ExportEquipmentList({\r\n        Content: \"\",\r\n        EqtType: \"\",\r\n        Position: \"\",\r\n        IsAll: true,\r\n        Id: this.tableSelection.map((item) => item.ID).toString(),\r\n        ...this.ruleForm,\r\n      });\r\n      if (res.IsSucceed) {\r\n        console.log(res);\r\n        downloadFile(res.Data, \"21\");\r\n      } else {\r\n        this.$message.error(res.Message);\r\n      }\r\n    },\r\n    // 下载模板\r\n    handleDownTemplate() {\r\n      HazchemImportTemplate({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          downloadFile(res.Data, \"危化品监测设备档案导�?);\r\n        } else {\r\n          this.$message.error(res.Message);\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.customTableConfig.pageSize = val;\r\n      this.init();\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前�? ${val}`);\r\n      this.customTableConfig.currentPage = val;\r\n      this.init();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection;\r\n      // if (this.tableSelection.length > 0) {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = false\r\n      // } else {\r\n      //   this.customTableConfig.buttonConfig.buttonList.find((v) => v.key == 'batch').disabled = true\r\n      // }\r\n    },\r\n    handleClick() {\r\n      this.currentComponent = DialogType;\r\n      this.componentsConfig = {\r\n        disabled: false,\r\n        title: \"新增\",\r\n      };\r\n      this.dialogVisible = true;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout{\r\n  height: calc(100vh - 90px);\r\n  overflow: auto;\r\n}\r\n</style>\r\n"]}]}