{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\alarmDetail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\alarmDetail\\index.vue", "mtime": 1755674552414}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/energyManagement/alarmDetail", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <CustomLayout>\r\n      <template v-slot:searchForm>\r\n        <CustomForm\r\n          :custom-form-items=\"customForm.formItems\"\r\n          :custom-form-buttons=\"customForm.customFormButtons\"\r\n          :value=\"ruleForm\"\r\n          :inline=\"true\"\r\n          :rules=\"customForm.rules\"\r\n          @submitForm=\"searchForm\"\r\n          @resetForm=\"resetForm\"\r\n        />\r\n      </template>\r\n      <template v-slot:layoutTable>\r\n        <CustomTable\r\n          :custom-table-config=\"customTableConfig\"\r\n          @handleSizeChange=\"handleSizeChange\"\r\n          @handleCurrentChange=\"handleCurrentChange\"\r\n          @handleSelectionChange=\"handleSelectionChange\"\r\n          ><template #customBtn=\"{ slotScope }\"\r\n            ><el-button\r\n              v-if=\"slotScope.Handle_Status == 1\"\r\n              type=\"text\"\r\n              @click=\"handelClose(slotScope)\"\r\n              >关闭</el-button\r\n            ></template\r\n          ></CustomTable\r\n        >\r\n      </template>\r\n    </CustomLayout>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      top=\"6vh\"\r\n      :destroy-on-close=\"true\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"currentComponent\"\r\n        :components-config=\"componentsConfig\"\r\n        :components-funs=\"componentsFuns\"\r\n    /></el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { parseTime } from '@/utils'\r\n// import { baseUrl } from '@/utils/baseurl'\r\nimport CustomLayout from '@/businessComponents/CustomLayout/index.vue'\r\nimport CustomTable from '@/businessComponents/CustomTable/index.vue'\r\nimport CustomForm from '@/businessComponents/CustomForm/index.vue'\r\nimport DialogForm from './dialogForm.vue'\r\nimport { downloadFile } from '@/utils/downloadFile'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport {\r\n  // GetDictionaryDetailListByCode,\r\n  // ExportData,\r\n  GetWarningList,\r\n  GetWarningTypeList,\r\n  ExportWarningList,\r\n  UpdateWarningStatus\r\n} from '@/api/business/energyManagement'\r\n\r\nimport { deviceTypeMixins } from '../../mixins/deviceType.js'\r\nexport default {\r\n  name: 'AlarmDetail',\r\n  components: {\r\n    CustomTable,\r\n    // CustomButton,\r\n    // CustomTitle,\r\n    CustomForm,\r\n    CustomLayout\r\n  },\r\n  mixins: [deviceTypeMixins],\r\n  data() {\r\n    return {\r\n      currentComponent: DialogForm,\r\n      componentsConfig: {},\r\n      componentsFuns: {\r\n        open: () => {\r\n          this.dialogVisible = true\r\n        },\r\n        close: () => {\r\n          this.dialogVisible = false\r\n          this.onFresh()\r\n        }\r\n      },\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      tableSelection: [],\r\n\r\n      ruleForm: {\r\n        Content: '',\r\n        EnergyType: '',\r\n        WarningType: '',\r\n        Position: ''\r\n      },\r\n      customForm: {\r\n        formItems: [\r\n          {\r\n            key: 'Content', // 字段ID\r\n            label: '点表编号或名称', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            placeholder: '输入点表编号或名称进行搜索',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            width: '240px',\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'EnergyType',\r\n            label: '能耗类型',\r\n            type: 'select',\r\n            placeholder: '请选择能耗类型',\r\n            options: [\r\n              // { label: '用电量', value: 'electric' },\r\n              // { label: '用水量', value: 'warter' },\r\n              // { label: '用氧气量', value: 'gas' }\r\n            ], // 类型数据列表\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'WarningType',\r\n            label: '告警类型',\r\n            type: 'select',\r\n            placeholder: '请选择告警类型',\r\n            options: [], // 类型数据列表\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Handle_Status',\r\n            label: '告警状态',\r\n            type: 'select',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true,\r\n              placeholder: '请选择告警状态'\r\n            },\r\n            options: [\r\n              {\r\n                label: '告警中',\r\n                value: 1\r\n              },\r\n              {\r\n                label: '已关闭',\r\n                value: 2\r\n              },\r\n              // {\r\n              //   label: '已处理',\r\n              //   value: 3\r\n              // }\r\n            ],\r\n            change: (e) => {\r\n              console.log(e)\r\n            }\r\n          },\r\n          {\r\n            key: 'Position', // 字段ID\r\n            label: '安装位置', // Form的label\r\n            type: 'input', // input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器\r\n            placeholder: '请输入安装位置',\r\n            otherOptions: {\r\n              // 除了model以外的其他的参数,具体请参考element文档\r\n              clearable: true\r\n            },\r\n            change: (e) => {\r\n              // change事件\r\n              console.log(e)\r\n            }\r\n          }\r\n        ],\r\n        rules: {\r\n          // 请参照elementForm rules\r\n        },\r\n        customFormButtons: {\r\n          submitName: '查询',\r\n          resetName: '重置'\r\n        }\r\n      },\r\n      customTableConfig: {\r\n        buttonConfig: {\r\n          buttonList: [\r\n            {\r\n              text: '批量导出',\r\n              onclick: (item) => {\r\n                console.log(item)\r\n                this.handleAllExport()\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        // 表格\r\n        pageSizeOptions: [10, 20, 50, 80],\r\n        currentPage: 1,\r\n        pageSize: 20,\r\n        total: 0,\r\n        operateOptions: {\r\n          width: 140\r\n        },\r\n        tableActionsWidth: 120,\r\n        tableColumns: [\r\n          // {\r\n          //   width: 50,\r\n          //   otherOptions: {\r\n          //     type: 'selection',\r\n          //     align: 'center'\r\n          //   }\r\n          // },\r\n          {\r\n            width: 60,\r\n            label: '序号',\r\n            otherOptions: {\r\n              type: 'index',\r\n              align: 'center'\r\n            } // key\r\n            // otherOptions: {\r\n            //   width: 180, // 宽度\r\n            //   fixed: 'left', // left, right\r\n            //   align: 'center' //\tleft/center/right\r\n            // }\r\n          }\r\n          //   {\r\n          //     label: '设备编号',\r\n          //     key: 'HId'\r\n          //   }\r\n        ],\r\n        tableData: [],\r\n        tableActions: [\r\n          // {\r\n          //   actionLabel: '关闭',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handelClose(row)\r\n          //   }\r\n          // },\r\n          {\r\n            actionLabel: '查看',\r\n            otherOptions: {\r\n              type: 'text'\r\n            },\r\n            onclick: (index, row) => {\r\n              this.handleEdit(index, row, 'view')\r\n            }\r\n          }\r\n\r\n          // {\r\n          //   actionLabel: '删除',\r\n          //   otherOptions: {\r\n          //     type: 'text'\r\n          //   },\r\n          //   onclick: (index, row) => {\r\n          //     this.handleDelete(index, row)\r\n          //   }\r\n          // }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  created() {\r\n    this.getBaseData()\r\n    this.init()\r\n    this.initDeviceType('EnergyType', 'EnergyEqtType')\r\n  },\r\n  methods: {\r\n    getBaseData() {\r\n      // 获取点表类型\r\n      // GetDictionaryDetailListByCode({ dictionaryCode: 'PointTableType' }).then(res => {\r\n      //   if (res.IsSucceed) {\r\n      //     const data = res.Data.map(item => {\r\n      //       return {\r\n      //         label: item.Display_Name,\r\n      //         value: item.Value\r\n      //       }\r\n      //     })\r\n      //     this.customForm.formItems[1].options = data\r\n      //   } else {\r\n      //     this.$message({\r\n      //       type: 'error',\r\n      //       data: res.Message\r\n      //     })\r\n      //   }\r\n      // })\r\n      // 获取告警类型\r\n      GetWarningTypeList().then(res => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data.map(item => {\r\n            return {\r\n              label: item.Type,\r\n              value: item.Type\r\n            }\r\n          })\r\n          this.customForm.formItems[2].options = data\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            data: res.Message\r\n          })\r\n        }\r\n      })\r\n      // 获取表格配置\r\n      GetGridByCode({ code: 'alarm_detail_list' }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const data = res.Data.ColumnList.map(item => {\r\n            return {\r\n              label: item.Display_Name,\r\n              key: item.Code,\r\n              width: item.Width,\r\n              otherOptions: {\r\n                fixed: item.Is_Frozen === false ? false : \"left\",\r\n              },\r\n            }\r\n          })\r\n          this.customTableConfig.tableColumns.push(...data)\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    searchForm(data) {\r\n      this.customTableConfig.currentPage = 1\r\n      console.log(data)\r\n      this.onFresh()\r\n    },\r\n    resetForm() {\r\n      this.onFresh()\r\n    },\r\n    onFresh() {\r\n      this.getWarningList()\r\n    },\r\n    init() {\r\n      this.getWarningList()\r\n    },\r\n    async getWarningList() {\r\n      const res = await GetWarningList({\r\n        Page: this.customTableConfig.currentPage,\r\n        PageSize: this.customTableConfig.pageSize,\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        this.customTableConfig.tableData = res.Data.Data.map(item => {\r\n          item.Time = item.Time ? parseTime(new Date(item.Time), '{y}-{m}-{d} {h}:{i}:{s}') : ''\r\n          return item\r\n        })\r\n        this.customTableConfig.total = res.Data.TotalCount\r\n      } else {\r\n        this.$message({\r\n          type: 'error',\r\n          message: res.Message\r\n        })\r\n      }\r\n    },\r\n    handleEdit(index, row, type) {\r\n      console.log(index, row, type)\r\n      if (type === 'view') {\r\n        this.dialogTitle = '查看'\r\n        this.componentsConfig = { ...row }\r\n        this.$nextTick(() => {\r\n          this.$refs.currentComponent.init(type)\r\n        })\r\n      } else if (type === 'edit') {\r\n        this.dialogTitle = '编辑'\r\n        this.componentsConfig = { ...row }\r\n        this.$nextTick(() => {\r\n          this.$refs.currentComponent.init(type)\r\n        })\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    async handleAllExport() {\r\n      const res = await ExportWarningList({\r\n        IsAll: true,\r\n        Ids: this.tableSelection.map((item) => item.Id),\r\n        ...this.ruleForm\r\n      })\r\n      if (res.IsSucceed) {\r\n        console.log(res)\r\n        downloadFile(res.Data, '21')\r\n        // const url = new URL(res.Data, baseUrl())\r\n        // window.open(url.href, '_blank')\r\n        this.$message({\r\n          type: 'success',\r\n          message: '导出成功!'\r\n        })\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`)\r\n      this.customTableConfig.pageSize = val\r\n      this.init()\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`)\r\n      this.customTableConfig.currentPage = val\r\n      this.init()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.tableSelection = selection\r\n      this.customTableConfig.buttonConfig.buttonList[1].disabled = selection.length === 0\r\n    },\r\n    handelClose(row) {\r\n      if (row.HandleStatusStr == '关闭') {\r\n        this.$message.warning('请勿重复操作')\r\n      } else {\r\n        UpdateWarningStatus({ id: row.Id, wid: row.WId, StatusEnum: 2 }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('操作成功')\r\n            this.init()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n  <style lang=\"scss\" scoped>\r\n.mt20 {\r\n  margin-top: 10px;\r\n}\r\n.layout {\r\n  height: calc(100vh - 90px);\r\n  overflow: hidden;\r\n}\r\n</style>\r\n\r\n"]}]}